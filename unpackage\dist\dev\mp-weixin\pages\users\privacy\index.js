"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_user = require("../../../api/user.js");
if (!Math) {
  mpHtml();
}
const mpHtml = () => "../../../uni_modules/mp-html/components/mp-html/mp-html.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const app = getApp();
    const content = common_vendor.ref("");
    const navH = common_vendor.ref(0);
    const tagStyle = common_vendor.ref({
      img: "width:100%;display:block;",
      table: "width:100%",
      video: "width:100%"
    });
    const initNavHeight = () => {
      navH.value = app.globalData.navHeight || 88;
    };
    const loadUserAgreement = async (type) => {
      try {
        common_vendor.index.__f__("log", "at pages/users/privacy/index.vue:44", "开始加载用户协议，类型:", type);
        const res = await api_user.getUserAgreement(type);
        common_vendor.index.__f__("log", "at pages/users/privacy/index.vue:46", "用户协议响应:", res);
        if (res && res.data && res.data.content) {
          content.value = res.data.content;
          common_vendor.index.__f__("log", "at pages/users/privacy/index.vue:50", "内容已设置，长度:", content.value.length);
          common_vendor.index.setNavigationBarTitle({
            title: res.data.title || "用户协议"
          });
        } else {
          common_vendor.index.__f__("error", "at pages/users/privacy/index.vue:57", "响应数据格式错误:", res);
          common_vendor.index.showToast({
            title: "内容加载失败",
            icon: "none",
            duration: 2e3
          });
        }
      } catch (err) {
        common_vendor.index.__f__("error", "at pages/users/privacy/index.vue:65", "加载用户协议失败:", err);
        common_vendor.index.showToast({
          title: err.message || err || "加载失败",
          icon: "none",
          duration: 2e3
        });
      }
    };
    common_vendor.onLoad((options) => {
      common_vendor.index.__f__("log", "at pages/users/privacy/index.vue:77", "页面参数:", options);
      initNavHeight();
      if (options && options.type) {
        loadUserAgreement(options.type);
      } else {
        common_vendor.index.__f__("warn", "at pages/users/privacy/index.vue:86", "缺少type参数");
        common_vendor.index.showToast({
          title: "缺少必要参数",
          icon: "none",
          duration: 2e3
        });
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          content: content.value,
          ["tag-style"]: tagStyle.value
        })
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/users/privacy/index.js.map
