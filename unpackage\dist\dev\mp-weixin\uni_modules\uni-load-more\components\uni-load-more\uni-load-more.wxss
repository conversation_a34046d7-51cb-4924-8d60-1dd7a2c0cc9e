
.uni-load-more {
  display: flex;
  flex-direction: row;
  height: 60rpx;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text {
  font-size: 24rpx;
  margin-left: 16rpx;
}
.uni-load-more__img {
  width: 48rpx;
  height: 48rpx;
}
.uni-load-more__img--nvue {
  color: #999;
}
.uni-load-more__img--android,
.uni-load-more__img--ios {
  width: 48rpx;
  height: 48rpx;
  transform: rotate(0);
}
.uni-load-more__img--android {
  animation: loading-ios 1s 0s linear infinite;
}
.uni-load-more__img--ios-H5 {
  position: relative;
  animation: loading-ios-H5 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 image {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5 {
0% {
    transform: rotate(0);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
to {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP {
  position: relative;
  width: 48rpx;
  height: 48rpx;
  transform: rotate(0);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 4rpx transparent;
  border-top: solid 4rpx #999;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon:nth-child(1) {
  animation: loading-android-MP-1 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon:nth-child(2) {
  animation: loading-android-MP-2 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon:nth-child(3) {
  animation: loading-android-MP-3 1s 0s linear infinite;
}
@keyframes loading-android {
0% {
    transform: rotate(0);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1 {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(90deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2 {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(180deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3 {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(270deg);
}
to {
    transform: rotate(360deg);
}
}
