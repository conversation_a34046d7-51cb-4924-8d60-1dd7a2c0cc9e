<view class="container"><view class="nav-bar bfw" style="{{'padding-top:' + e}}"><view class="bar-box df" style="{{'height:' + c + ';' + ('width:' + '100%')}}"><view class="bar-back df" bindtap="{{b}}"><image src="{{a}}" style="width:34rpx;height:34rpx"></image></view><view class="bar-title ohto">圈子</view></view><view class="nav-box df"><view wx:for="{{d}}" wx:for-item="item" wx:key="e" class="nav-item df" bindtap="{{item.f}}" data-idx="{{item.g}}"><text style="{{'color:' + item.b + ';' + ('font-size:' + item.c)}}">{{item.a}}</text><view style="{{'opacity:' + item.d}}" class="nav-line"></view></view></view></view><view class="content-box" style="{{'margin-top:' + l}}"><view wx:if="{{f}}" class="loading-container"><view class="loading-indicator"></view></view><view wx:if="{{g}}" class="empty-box df"><image src="{{h}}"/><view class="e1">{{i}}</view><view class="e2">空空如也，等待探索</view></view><block wx:else><view wx:for="{{j}}" wx:for-item="item" wx:key="p" class="list-box df" data-id="{{item.q}}" bindtap="{{item.r}}"><view class="list-avatar"><lazy-image wx:if="{{item.b}}" u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}"></lazy-image><view wx:if="{{item.c}}" class="tag" style="background:url(/static/img/gf.png) 0% 0% / 100% 100%"></view><view wx:elif="{{item.d}}" class="tag" style="background:url(/static/img/tj.png) 0% 0% / 100% 100%"></view></view><view class="list-item"><view class="name ohto2">{{item.e}}</view><view class="intro ohto2">{{item.f}}</view><view wx:if="{{item.g}}" class="cu-img-group"><block wx:if="{{item.h}}"><view wx:for="{{item.i}}" wx:for-item="member" wx:key="b" class="cu-img"><image src="{{member.a}}" mode="aspectFill"></image></view></block><view class="cu-txt">{{item.j}}人加入 · {{item.k}}篇笔记</view><view wx:if="{{item.l}}" class="view-count">· 访问 {{item.m}}</view></view><view wx:if="{{item.n}}" class="recent-topics"><view class="recent-title">最近话题：</view><view wx:for="{{item.o}}" wx:for-item="topic" wx:key="b" class="topic-item">{{topic.a}}</view></view></view></view></block><view wx:if="{{k}}" style="text-align:center;padding:20rpx 0;color:#999;font-size:24rpx"> 没有更多数据了 </view></view><uni-popup wx:if="{{o}}" class="r" u-s="{{['d']}}" u-r="tipsPopup" u-i="49178bb0-1" bind:__l="__l" u-p="{{o}}"><view class="tips-box df"><view class="tips-item bfh">{{m}}</view></view></uni-popup></view>