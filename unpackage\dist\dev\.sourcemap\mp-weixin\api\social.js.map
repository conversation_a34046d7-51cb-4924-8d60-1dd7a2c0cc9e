{"version": 3, "file": "social.js", "sources": ["api/social.js"], "sourcesContent": ["import request from \"@/utils/request.js\";\n\n// 获取用户社交信息\nexport function getUserSocialInfo() {\n\treturn request.get('v3/user/social/info');\n}\n\n// 更新用户社交信息\nexport function updateUserSocialInfo(data) {\n\treturn request.post('v3/user/social/update', data);\n}\n\n// 获取关注列表\nexport function getSocialFollowList(data) {\n\treturn request.get('v3/user/social/follow/list', data);\n}\n\n// 获取粉丝列表\nexport function getSocialFansList(data) {\n\treturn request.get('v3/user/social/fans/list', data);\n}\n\n// 关注/取消关注用户\nexport function followUser(data) {\n\treturn request.post('v3/user/social/follow', data);\n}\n\n/**\n * 检查是否已关注用户\n * @param {Number} to_uid 被检查的用户ID\n * @returns {Promise}\n */\nexport function checkIsFollowing(to_uid) {\n\treturn request.get('v3/user/social/is_follow', { to_uid });\n}\n\n// 获取用户主页信息（包含访客记录）\nexport function getUserHomepage(data) {\n\treturn request.get('v3/user/social/homepage', data, {\n\t\tnoAuth: true, // 不需要登录验证，但登录用户会获得更多功能\n\t});\n}\n\n/**\n * 获取访客详细信息\n * @param {Object} data - 请求参数\n * @param {Number} data.page - 页码，默认1\n * @param {Number} data.limit - 每页数量，默认20\n * @param {Number} data.type - 类型，0-谁看过我，1-我看过谁，默认0\n * @returns {Promise} 返回访客列表数据\n */\nexport function getVisitorDetails(data = {}) {\n\treturn request.get('v3/user/social/visitors/details', data);\n}\n\n/**\n * 获取兴趣标签分类和标签列表（合并接口）\n * @returns {Promise} 返回包含分类和标签的数据\n */\nexport function getTagsWithCategories() {\n\treturn request.get('v3/user/social/tags/all');\n}\n\n/**\n * 获取我的兴趣标签\n * @returns {Promise}\n */\nexport function getMyTags() {\n\treturn request.get('v3/user/social/tags/my');\n}\n\n/**\n * 更新用户兴趣标签\n * @param {Array} tag_ids 标签ID数组\n * @returns {Promise}\n */\nexport function updateUserTags(tag_ids) {\n\treturn request.post('v3/user/social/tags/update', { label_ids: tag_ids });\n}\n\n// 获取我的动态列表\nexport function getMyDynamicList(data) {\n\treturn request.get('v3/dynamic/my_list', data);\n}\n\n// 获取指定用户的动态列表  \nexport function getUserDynamicList(userId, data) {\n\treturn request.get(`v3/dynamic/user_list/${userId}`, data);\n}\n\n// 获取其他用户动态列表（保留向后兼容）\nexport function getOtherUserDynamicList(userId, data) {\n\treturn request.get(`v3/dynamic/user_list/${userId}`, data, {\n\t\tnoAuth: true, // 支持未登录用户访问\n\t});\n}\n\n// 发布动态\nexport function publishDynamic(data) {\n\treturn request.post('v3/dynamic/publish', data);\n}\n\n// 更新动态\nexport function updateDynamic(id, data) {\n\treturn request.post(`v3/dynamic/update/${id}`, data);\n}\n\n// 删除动态\nexport function deleteDynamic(id) {\n\treturn request.post(`v3/dynamic/delete/${id}`);\n}\n\n// 点赞/取消点赞\nexport function likeDynamic(data) {\n\treturn request.post('v3/dynamic/like', data);\n}\n\n// 取消点赞动态\nexport function unlikeDynamic(data) {\n\treturn request.post('v3/dynamic/unlike', data);\n}\n\n/**\n * 动态投票\n * @param {Object} data { vote_id, option_id }\n * @returns {Promise}\n */\nexport function vote(data) {\n  return request.post('v3/dynamic/vote', data);\n}\n\n// 获取动态详情\nexport function getDynamicDetail(id) {\n  return request.get(`v3/dynamic/detail/${id}`, {}, {\n    noAuth: true\n  });\n}\n\n/**\n * 举报动态\n * @param {String} reason 举报原因\n * @param {Number} dynamic_id 动态ID\n * @param {Number} uid 动态作者ID\n * @param {String} content 动态内容\n * @param {String} image 动态图片\n * @returns {Promise}\n */\nexport function reportDynamic(reason, dynamic_id, uid, content, image) {\n\treturn request.post('v3/dynamic/report', { \n\t\treason,\n\t\tdynamic_id,\n\t\tuid,\n\t\tcontent,\n\t\timage\n\t});\n}\n\n// 获取话题列表\nexport function getTopicList(data) {\n\treturn request.get('v3/dynamic/topic/list', data);\n}\n\n// 获取推荐话题\nexport function getRecommendTopics() {\n\treturn request.get('v3/dynamic/topic/recommend');\n}\n\n// 获取话题详情\nexport function getTopicDetail(id) {\n\treturn request.get(`v3/dynamic/topic/detail/${id}`, {}, {\n\t\tnoAuth: true\n\t});\n}\n\n// 获取话题下的动态列表\nexport function getTopicDynamicList(data) {\n\treturn request.get(`v3/dynamic/topic/dynamic/${data.topic_id}`, {\n\t\tpage: data.page || 1,\n\t\tlimit: data.limit || 10,\n\t\ttype: data.type || 'latest',\n\t\tkeyword: data.keyword || ''\n\t}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取用户点赞的动态列表\n * @param {Number} userId 用户ID\n * @param {Object} data 分页参数\n */\nexport function getLikeDynamicList(userId, data) {\n\treturn request.get(`v3/dynamic/like_list/${userId}`, data, {\n\t\tnoAuth: true, // 支持未登录用户访问\n\t});\n}\n\n\n/**\n * 获取用户访客列表\n * @param {Object} data 分页参数\n * @returns {Promise}\n */\nexport function getVisitorList(data) {\n\treturn request.get('user/visitor/list', data);\n}\n\n// 获取评论列表\nexport function getCommentsList(target_id, data = {}) {\n  return request.get(`v3/comment/list/${parseInt(target_id) || 0}`, {\n    ...data,\n    page: parseInt(data.page) || 1,\n    limit: parseInt(data.limit) || 10,\n    sort_type: parseInt(data.sort_type) || 0\n  }, {\n    noAuth: true\n  });\n}\n\n// 获取评论回复列表\nexport function getCommentReplies(data) {\n  // 确保关键参数为数字类型\n  const params = {\n    ...data,\n    parent_id: parseInt(data.parent_id) || 0,  // 一级评论ID\n    page: parseInt(data.page) || 1,\n    limit: parseInt(data.limit) || 3,  // 每次只加载少量数据，让用户可以分批查看\n    sort_type: parseInt(data.sort_type) || 1  // 默认按时间排序\n  };\n  return request.get('v3/comment/replies', params, {\n    noAuth: true\n  });\n}\n\n/**\n * 获取用户实名认证信息\n * @returns {Promise}\n */\nexport function getRealAuthInfo() {\n  return request.get('v3/user/real_auth/info');\n}\n\n/**\n * 提交实名认证\n * @param {Object} data 包含实名认证信息\n * @param {String} data.real_name 真实姓名\n * @param {String} data.id_card_number 身份证号码\n * @returns {Promise}\n */\nexport function submitRealAuth(data) {\n  return request.post('v3/user/real_auth/submit', data);\n}\n\n/**\n * 撤销实名认证申请\n * @returns {Promise}\n */\nexport function cancelRealAuth() {\n  return request.post('v3/user/real_auth/cancel');\n}\n\n// 添加评论\nexport function addComment(data) {\n  const params = {\n    ...data,\n    type: parseInt(data.type) || 0,\n    target_id: parseInt(data.target_id) || 0,\n    reply_id: parseInt(data.reply_id) || 0\n  };\n  return request.post('v3/comment/add', params);\n}\n\n// 删除评论\nexport function deleteComment(id) {\n  return request.post(`v3/comment/delete/${parseInt(id) || 0}`);\n}\n\n// 点赞评论\nexport function likeComment(id) {\n  return request.post(`v3/comment/like/${parseInt(id) || 0}`);\n}\n\n// 取消点赞评论\nexport function unlikeComment(id) {\n  return request.post(`v3/comment/unlike/${parseInt(id) || 0}`);\n}\n\n/**\n * 获取动态列表\n * @param {Object} data 包含:\n *   - page: 页码\n *   - limit: 每页数量\n *   - type: 类型(1=热门,2=关注,3=附近)\n *   - uid: 用户ID(前端传递，用于后端判断点赞状态)\n *   - waterfall: 瀑布流模式(可选,1=瀑布流)\n * @returns {Promise}\n */\nexport function getDynamicList(data) {\n\n\t\n\treturn request.get('v3/dynamic/list', data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 更新隐私设置\n * @param {Object} data 隐私设置参数\n * @returns {Promise}\n * \n * 参数示例：\n * {\n *   privacy_settings: {\n *     showFans: true,    // 是否显示粉丝列表\n *     showFollows: true, // 是否显示关注列表\n *     showVisitors: true, // 是否显示访客\n *     allowSearch: true,  // 是否允许被搜索\n *   }\n * }\n */\nexport function updatePrivacySettings(data) {\n\treturn request.post('v3/user/social/privacy', data);\n}\n\n/**\n * 更新瀑布流设置\n * @param {Object} data 瀑布流设置参数\n * @returns {Promise}\n * \n * 参数示例：\n * {\n *   flow_settings: {\n *     dynamicFlow: true, // 是否开启动态瀑布流\n *     circleFlow: true   // 是否开启圈子瀑布流\n *   }\n * }\n */\nexport function updateFlowSettings(data) {\n\treturn request.post('v3/user/social/flow', data);\n}\n\n// ==================== 圈子相关接口 ====================\n\n/**\n * 获取圈子列表\n * @param {Object} data 查询参数\n * @param {Number} data.page 页码，默认1\n * @param {Number} data.limit 每页数量，默认10\n * @param {String} data.keyword 搜索关键词（可选）\n * @param {Number} data.is_hot 是否热门：0=否,1=是（可选）\n * @param {Number} data.is_recommend 是否推荐：0=否,1=是（可选）\n * @returns {Promise}\n */\nexport function getCircleList(data) {\n\treturn request.get('v3/circle/list', data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取圈子详情\n * @param {Number} id 圈子ID\n * @returns {Promise}\n */\nexport function getCircleDetail(id) {\n\treturn request.get(`v3/circle/detail/${id}`, {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取热门圈子列表\n * @returns {Promise}\n */\nexport function getHotCircles() {\n  return request.get('v3/circle/hot', {}, {\n    noAuth: true\n  });\n}\n\n/**\n * 获取推荐圈子列表\n * @returns {Promise}\n */\nexport function getRecommendCircles() {\n  return request.get('v3/circle/recommend', {}, {\n    noAuth: true\n  });\n}\n\n/**\n * 创建圈子\n * @param {Object} data 圈子信息\n * @param {String} data.circle_name 圈子名称（必填）\n * @param {String} data.circle_avatar 圈子头像（可选）\n * @param {String} data.circle_background 圈子背景图（可选）\n * @param {String} data.circle_description 圈子描述（可选）\n * @param {String} data.circle_notice 圈子公告（可选）\n * @returns {Promise}\n */\nexport function createCircle(data) {\n\treturn request.post('v3/circle/create', data);\n}\n\n/**\n * 更新圈子信息\n * @param {Number} id 圈子ID\n * @param {Object} data 更新的圈子信息\n * @returns {Promise}\n */\nexport function updateCircle(id, data) {\n\treturn request.post(`v3/circle/update/${id}`, data);\n}\n\n/**\n * 删除圈子\n * @param {Number} id 圈子ID\n * @returns {Promise}\n */\nexport function deleteCircle(id) {\n\treturn request.post(`v3/circle/delete/${id}`);\n}\n\n/**\n * 加入圈子\n * @param {Object} data 加入信息\n * @param {Number} data.circle_id 圈子ID（必填）\n * @param {Number} data.inviter_uid 邀请人用户ID（可选）\n * @returns {Promise}\n */\nexport function joinCircle(data) {\n\treturn request.post('v3/circle/join', data);\n}\n\n/**\n * 退出圈子\n * @param {Object} data 退出信息\n * @param {Number} data.circle_id 圈子ID（必填）\n * @returns {Promise}\n */\nexport function exitCircle(data) {\n\treturn request.post('v3/circle/exit', data);\n}\n\n/**\n * 获取我创建的圈子\n * @param {Object} data 分页参数\n * @param {Number} data.page 页码，默认1\n * @param {Number} data.limit 每页数量，默认10\n * @returns {Promise}\n */\nexport function getMyCircles(data) {\n\treturn request.get('v3/circle/my', data);\n}\n\n/**\n * 获取我加入的圈子\n * @param {Object} data 分页参数\n * @param {Number} data.page 页码，默认1\n * @param {Number} data.limit 每页数量，默认10\n * @returns {Promise}\n */\nexport function getJoinedCircles(data) {\n\treturn request.get('v3/circle/joined', data);\n}\n\n// ==================== 圈子成员管理接口 ====================\n\n/**\n * 获取圈子成员列表\n * @param {Object} data 查询参数\n * @param {Number} data.circle_id 圈子ID（必填）\n * @param {Number} data.page 页码，默认1\n * @param {Number} data.limit 每页数量，默认20\n * @param {Number} data.role_type 角色类型筛选：1=普通成员,2=管理员,3=圈主（可选）\n * @returns {Promise}\n */\nexport function getCircleMemberList(data) {\n\treturn request.get('v3/circle/member/list', data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 踢出圈子成员\n * @param {Object} data 踢出信息\n * @param {Number} data.circle_id 圈子ID（必填）\n * @param {Number} data.target_uid 目标用户ID（必填）\n * @param {String} data.reason 踢出原因（可选）\n * @returns {Promise}\n */\nexport function kickCircleMember(data) {\n\treturn request.post('v3/circle/member/kick', data);\n}\n\n/**\n * 设置圈子成员角色\n * @param {Object} data 角色设置信息\n * @param {Number} data.circle_id 圈子ID（必填）\n * @param {Number} data.target_uid 目标用户ID（必填）\n * @param {Number} data.role_type 角色类型：1=普通成员,2=管理员（必填）\n * @returns {Promise}\n */\nexport function setCircleMemberRole(data) {\n\treturn request.post('v3/circle/member/set_role', data);\n}\n\n/**\n * 禁言圈子成员\n * @param {Object} data 禁言信息\n * @param {Number} data.circle_id 圈子ID（必填）\n * @param {Number} data.target_uid 目标用户ID（必填）\n * @param {Number} data.mute_days 禁言天数，默认1天（可选）\n * @param {String} data.reason 禁言原因（可选）\n * @returns {Promise}\n */\nexport function muteCircleMember(data) {\n\treturn request.post('v3/circle/member/mute', data);\n}\n\n/**\n * 解除圈子成员禁言\n * @param {Object} data 解禁信息\n * @param {Number} data.circle_id 圈子ID（必填）\n * @param {Number} data.target_uid 目标用户ID（必填）\n * @returns {Promise}\n */\nexport function unmuteCircleMember(data) {\n\treturn request.post('v3/circle/member/unmute', data);\n}\n\n/**\n * 获取圈子动态列表\n * @param {Object} data 查询参数\n * @param {Number} data.page 页码，默认1\n * @param {Number} data.limit 每页数量，默认10\n * @param {Number} data.circle_id 圈子ID（必填）\n * @param {String} data.type 类型：recommend=推荐，latest=最新，默认recommend\n * @param {String} data.keyword 搜索关键词（可选）\n * @param {Number} data.status 状态：0=待审核，1=已通过，2=未通过，3=草稿箱，默认1\n * @returns {Promise}\n */\nexport function getCircleDynamicList(data) {\n\treturn request.get('v3/circle/dynamic/list', data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 搜索用户（用于@功能）\n * @param {Object} data 搜索参数\n * @param {String} data.keyword 搜索关键词（必填）\n * @param {Number} data.page 页码，默认1\n * @param {Number} data.limit 每页数量，默认20\n * @returns {Promise}\n */\nexport function searchUsers(data) {\n\treturn request.get('v3/user/social/search_users', data);\n}\n\n// ==================== 树洞盲盒相关接口 ====================\n\n/**\n * 发布树洞盲盒\n * @param {Object} data 盲盒信息\n * @param {Number} data.type 盲盒类型：1-问题 2-秘密 3-心愿 4-语音\n * @param {String} data.content 盲盒内容\n * @param {String} data.voice_url 语音文件URL（type=4时使用）\n * @param {Number} data.voice_duration 语音时长（秒）\n * @param {Number} data.is_anonymous 是否匿名：0-否 1-是\n * @returns {Promise}\n */\nexport function publishTreeHoleBox(data) {\n\treturn request.post('v3/treehole/publish', data);\n}\n\n/**\n * 抽取树洞盲盒\n * @param {Object} data 抽取参数\n * @param {Number} data.type 盲盒类型筛选（可选）：0-随机 1-问题 2-秘密 3-心愿 4-语音\n * @returns {Promise}\n */\nexport function drawTreeHoleBox(data = {}) {\n\treturn request.post('v3/treehole/draw', data);\n}\n\n/**\n * 获取我的树洞盲盒列表\n * @param {Object} data 查询参数\n * @param {Number} data.page 页码，默认1\n * @param {Number} data.limit 每页数量，默认10\n * @param {Number} data.type 类型筛选（可选）：1-问题 2-秘密 3-心愿 4-语音\n * @param {Number} data.status 状态筛选（可选）：1-正常 2-已下架\n * @returns {Promise}\n */\nexport function getMyTreeHoleBoxList(data) {\n\treturn request.get('v3/treehole/my_list', data);\n}\n\n/**\n * 获取我抽到的树洞盲盒列表\n * @param {Object} data 查询参数\n * @param {Number} data.page 页码，默认1\n * @param {Number} data.limit 每页数量，默认10\n * @param {Number} data.is_responded 是否已回应筛选（可选）：0-未回应 1-已回应\n * @returns {Promise}\n */\nexport function getMyDrawnBoxList(data) {\n\treturn request.get('v3/treehole/drawn_list', data);\n}\n\n/**\n * 获取树洞盲盒详情\n * @param {Number} id 盲盒ID\n * @returns {Promise}\n */\nexport function getTreeHoleBoxDetail(id) {\n\treturn request.get(`v3/treehole/detail/${id}`);\n}\n\n/**\n * 回应树洞盲盒\n * @param {Object} data 回应信息\n * @param {Number} data.box_id 盲盒ID\n * @param {Number} data.draw_id 抽取记录ID（可选，一级回应时需要）\n * @param {Number} data.parent_id 父级回应ID（可选，回复时需要）\n * @param {Number} data.reply_to_uid 回复的用户ID（可选，回复时需要）\n * @param {String} data.content 回应内容\n * @param {String} data.voice_url 语音文件URL（可选）\n * @param {Number} data.voice_duration 语音时长（秒，可选）\n * @returns {Promise}\n */\nexport function responseTreeHoleBox(data) {\n\treturn request.post('v3/treehole/response', data);\n}\n\n/**\n * 获取树洞盲盒回应列表\n * @param {Object} data 查询参数\n * @param {Number} data.box_id 盲盒ID\n * @param {Number} data.page 页码，默认1\n * @param {Number} data.limit 每页数量，默认10\n * @param {Number} data.parent_id 父级回应ID（可选，获取子回复时使用）\n * @returns {Promise}\n */\nexport function getTreeHoleResponseList(data) {\n\treturn request.get('v3/treehole/response_list', data);\n}\n\n/**\n * 删除树洞盲盒\n * @param {Number} id 盲盒ID\n * @returns {Promise}\n */\nexport function deleteTreeHoleBox(id) {\n\treturn request.post(`v3/treehole/delete/${id}`);\n}\n\n/**\n * 删除树洞回应\n * @param {Number} id 回应ID\n * @returns {Promise}\n */\nexport function deleteTreeHoleResponse(id) {\n\treturn request.post(`v3/treehole/response/delete/${id}`);\n}\n\n/**\n * 放回树洞盲盒（不再显示在我抽到的列表中）\n * @param {Number} draw_id 抽取记录ID\n * @returns {Promise}\n */\nexport function returnTreeHoleBox(draw_id) {\n\treturn request.post(`v3/treehole/return/${draw_id}`);\n}\n\n/**\n * 获取树洞盲盒统计信息\n * @returns {Promise}\n */\nexport function getTreeHoleStats() {\n\treturn request.get('v3/treehole/stats');\n}\n\n\n"], "names": ["request"], "mappings": ";;AAGO,SAAS,oBAAoB;AACnC,SAAOA,cAAO,QAAC,IAAI,qBAAqB;AACzC;AAGO,SAAS,qBAAqB,MAAM;AAC1C,SAAOA,sBAAQ,KAAK,yBAAyB,IAAI;AAClD;AAGO,SAAS,oBAAoB,MAAM;AACzC,SAAOA,sBAAQ,IAAI,8BAA8B,IAAI;AACtD;AAGO,SAAS,kBAAkB,MAAM;AACvC,SAAOA,sBAAQ,IAAI,4BAA4B,IAAI;AACpD;AAGO,SAAS,WAAW,MAAM;AAChC,SAAOA,sBAAQ,KAAK,yBAAyB,IAAI;AAClD;AAYO,SAAS,gBAAgB,MAAM;AACrC,SAAOA,sBAAQ,IAAI,2BAA2B,MAAM;AAAA,IACnD,QAAQ;AAAA;AAAA,EACV,CAAE;AACF;AAUO,SAAS,kBAAkB,OAAO,IAAI;AAC5C,SAAOA,sBAAQ,IAAI,mCAAmC,IAAI;AAC3D;AAMO,SAAS,wBAAwB;AACvC,SAAOA,cAAO,QAAC,IAAI,yBAAyB;AAC7C;AAMO,SAAS,YAAY;AAC3B,SAAOA,cAAO,QAAC,IAAI,wBAAwB;AAC5C;AAOO,SAAS,eAAe,SAAS;AACvC,SAAOA,cAAAA,QAAQ,KAAK,8BAA8B,EAAE,WAAW,QAAO,CAAE;AACzE;AAGO,SAAS,iBAAiB,MAAM;AACtC,SAAOA,sBAAQ,IAAI,sBAAsB,IAAI;AAC9C;AAQO,SAAS,wBAAwB,QAAQ,MAAM;AACrD,SAAOA,cAAAA,QAAQ,IAAI,wBAAwB,MAAM,IAAI,MAAM;AAAA,IAC1D,QAAQ;AAAA;AAAA,EACV,CAAE;AACF;AAGO,SAAS,eAAe,MAAM;AACpC,SAAOA,sBAAQ,KAAK,sBAAsB,IAAI;AAC/C;AAQO,SAAS,cAAc,IAAI;AACjC,SAAOA,cAAAA,QAAQ,KAAK,qBAAqB,EAAE,EAAE;AAC9C;AAGO,SAAS,YAAY,MAAM;AACjC,SAAOA,sBAAQ,KAAK,mBAAmB,IAAI;AAC5C;AAYO,SAAS,KAAK,MAAM;AACzB,SAAOA,sBAAQ,KAAK,mBAAmB,IAAI;AAC7C;AAGO,SAAS,iBAAiB,IAAI;AACnC,SAAOA,cAAAA,QAAQ,IAAI,qBAAqB,EAAE,IAAI,IAAI;AAAA,IAChD,QAAQ;AAAA,EACZ,CAAG;AACH;AAWO,SAAS,cAAc,QAAQ,YAAY,KAAK,SAAS,OAAO;AACtE,SAAOA,cAAO,QAAC,KAAK,qBAAqB;AAAA,IACxC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAE;AACF;AAGO,SAAS,aAAa,MAAM;AAClC,SAAOA,sBAAQ,IAAI,yBAAyB,IAAI;AACjD;AAQO,SAAS,eAAe,IAAI;AAClC,SAAOA,cAAAA,QAAQ,IAAI,2BAA2B,EAAE,IAAI,IAAI;AAAA,IACvD,QAAQ;AAAA,EACV,CAAE;AACF;AAGO,SAAS,oBAAoB,MAAM;AACzC,SAAOA,cAAAA,QAAQ,IAAI,4BAA4B,KAAK,QAAQ,IAAI;AAAA,IAC/D,MAAM,KAAK,QAAQ;AAAA,IACnB,OAAO,KAAK,SAAS;AAAA,IACrB,MAAM,KAAK,QAAQ;AAAA,IACnB,SAAS,KAAK,WAAW;AAAA,EAC3B,GAAI;AAAA,IACF,QAAQ;AAAA,EACV,CAAE;AACF;AAOO,SAAS,mBAAmB,QAAQ,MAAM;AAChD,SAAOA,cAAAA,QAAQ,IAAI,wBAAwB,MAAM,IAAI,MAAM;AAAA,IAC1D,QAAQ;AAAA;AAAA,EACV,CAAE;AACF;AAaO,SAAS,gBAAgB,WAAW,OAAO,IAAI;AACpD,SAAOA,cAAO,QAAC,IAAI,mBAAmB,SAAS,SAAS,KAAK,CAAC,IAAI;AAAA,IAChE,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,IAAI,KAAK;AAAA,IAC7B,OAAO,SAAS,KAAK,KAAK,KAAK;AAAA,IAC/B,WAAW,SAAS,KAAK,SAAS,KAAK;AAAA,EAC3C,GAAK;AAAA,IACD,QAAQ;AAAA,EACZ,CAAG;AACH;AAGO,SAAS,kBAAkB,MAAM;AAEtC,QAAM,SAAS;AAAA,IACb,GAAG;AAAA,IACH,WAAW,SAAS,KAAK,SAAS,KAAK;AAAA;AAAA,IACvC,MAAM,SAAS,KAAK,IAAI,KAAK;AAAA,IAC7B,OAAO,SAAS,KAAK,KAAK,KAAK;AAAA;AAAA,IAC/B,WAAW,SAAS,KAAK,SAAS,KAAK;AAAA;AAAA,EAC3C;AACE,SAAOA,sBAAQ,IAAI,sBAAsB,QAAQ;AAAA,IAC/C,QAAQ;AAAA,EACZ,CAAG;AACH;AAMO,SAAS,kBAAkB;AAChC,SAAOA,cAAO,QAAC,IAAI,wBAAwB;AAC7C;AASO,SAAS,eAAe,MAAM;AACnC,SAAOA,sBAAQ,KAAK,4BAA4B,IAAI;AACtD;AAMO,SAAS,iBAAiB;AAC/B,SAAOA,cAAO,QAAC,KAAK,0BAA0B;AAChD;AAGO,SAAS,WAAW,MAAM;AAC/B,QAAM,SAAS;AAAA,IACb,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,IAAI,KAAK;AAAA,IAC7B,WAAW,SAAS,KAAK,SAAS,KAAK;AAAA,IACvC,UAAU,SAAS,KAAK,QAAQ,KAAK;AAAA,EACzC;AACE,SAAOA,sBAAQ,KAAK,kBAAkB,MAAM;AAC9C;AAGO,SAAS,cAAc,IAAI;AAChC,SAAOA,cAAO,QAAC,KAAK,qBAAqB,SAAS,EAAE,KAAK,CAAC,EAAE;AAC9D;AAGO,SAAS,YAAY,IAAI;AAC9B,SAAOA,cAAO,QAAC,KAAK,mBAAmB,SAAS,EAAE,KAAK,CAAC,EAAE;AAC5D;AAGO,SAAS,cAAc,IAAI;AAChC,SAAOA,cAAO,QAAC,KAAK,qBAAqB,SAAS,EAAE,KAAK,CAAC,EAAE;AAC9D;AAYO,SAAS,eAAe,MAAM;AAGpC,SAAOA,sBAAQ,IAAI,mBAAmB,MAAM;AAAA,IAC3C,QAAQ;AAAA,EACV,CAAE;AACF;AAiBO,SAAS,sBAAsB,MAAM;AAC3C,SAAOA,sBAAQ,KAAK,0BAA0B,IAAI;AACnD;AAeO,SAAS,mBAAmB,MAAM;AACxC,SAAOA,sBAAQ,KAAK,uBAAuB,IAAI;AAChD;AAcO,SAAS,cAAc,MAAM;AACnC,SAAOA,sBAAQ,IAAI,kBAAkB,MAAM;AAAA,IAC1C,QAAQ;AAAA,EACV,CAAE;AACF;AAOO,SAAS,gBAAgB,IAAI;AACnC,SAAOA,cAAAA,QAAQ,IAAI,oBAAoB,EAAE,IAAI,IAAI;AAAA,IAChD,QAAQ;AAAA,EACV,CAAE;AACF;AAMO,SAAS,gBAAgB;AAC9B,SAAOA,sBAAQ,IAAI,iBAAiB,IAAI;AAAA,IACtC,QAAQ;AAAA,EACZ,CAAG;AACH;AAsBO,SAAS,aAAa,MAAM;AAClC,SAAOA,sBAAQ,KAAK,oBAAoB,IAAI;AAC7C;AA4BO,SAAS,WAAW,MAAM;AAChC,SAAOA,sBAAQ,KAAK,kBAAkB,IAAI;AAC3C;AAQO,SAAS,WAAW,MAAM;AAChC,SAAOA,sBAAQ,KAAK,kBAAkB,IAAI;AAC3C;AAoBO,SAAS,iBAAiB,MAAM;AACtC,SAAOA,sBAAQ,IAAI,oBAAoB,IAAI;AAC5C;AAaO,SAAS,oBAAoB,MAAM;AACzC,SAAOA,sBAAQ,IAAI,yBAAyB,MAAM;AAAA,IACjD,QAAQ;AAAA,EACV,CAAE;AACF;AAUO,SAAS,iBAAiB,MAAM;AACtC,SAAOA,sBAAQ,KAAK,yBAAyB,IAAI;AAClD;AAUO,SAAS,oBAAoB,MAAM;AACzC,SAAOA,sBAAQ,KAAK,6BAA6B,IAAI;AACtD;AAWO,SAAS,iBAAiB,MAAM;AACtC,SAAOA,sBAAQ,KAAK,yBAAyB,IAAI;AAClD;AASO,SAAS,mBAAmB,MAAM;AACxC,SAAOA,sBAAQ,KAAK,2BAA2B,IAAI;AACpD;AAaO,SAAS,qBAAqB,MAAM;AAC1C,SAAOA,sBAAQ,IAAI,0BAA0B,MAAM;AAAA,IAClD,QAAQ;AAAA,EACV,CAAE;AACF;AAUO,SAAS,YAAY,MAAM;AACjC,SAAOA,sBAAQ,IAAI,+BAA+B,IAAI;AACvD;AAcO,SAAS,mBAAmB,MAAM;AACxC,SAAOA,sBAAQ,KAAK,uBAAuB,IAAI;AAChD;AAQO,SAAS,gBAAgB,OAAO,IAAI;AAC1C,SAAOA,sBAAQ,KAAK,oBAAoB,IAAI;AAC7C;AAWO,SAAS,qBAAqB,MAAM;AAC1C,SAAOA,sBAAQ,IAAI,uBAAuB,IAAI;AAC/C;AAUO,SAAS,kBAAkB,MAAM;AACvC,SAAOA,sBAAQ,IAAI,0BAA0B,IAAI;AAClD;AAOO,SAAS,qBAAqB,IAAI;AACxC,SAAOA,cAAAA,QAAQ,IAAI,sBAAsB,EAAE,EAAE;AAC9C;AAcO,SAAS,oBAAoB,MAAM;AACzC,SAAOA,sBAAQ,KAAK,wBAAwB,IAAI;AACjD;AAWO,SAAS,wBAAwB,MAAM;AAC7C,SAAOA,sBAAQ,IAAI,6BAA6B,IAAI;AACrD;AAyBO,SAAS,kBAAkB,SAAS;AAC1C,SAAOA,cAAAA,QAAQ,KAAK,sBAAsB,OAAO,EAAE;AACpD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}