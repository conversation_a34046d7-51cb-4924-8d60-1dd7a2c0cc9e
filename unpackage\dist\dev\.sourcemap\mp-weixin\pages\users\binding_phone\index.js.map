{"version": 3, "file": "index.js", "sources": ["pages/users/binding_phone/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlcnMvYmluZGluZ19waG9uZS9pbmRleC52dWU"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<navbar></navbar>\n\t\t<view class=\"content-box\" :style=\"{'margin-top': statusBarHeight + 'px'}\">\n\t\t\t<view class=\"login-box\">\n\t\t\t\t<view class=\"title\">\n\t\t\t\t\t{{pageType == 1 ? $t('绑定手机号') : $t('手机号登录')}}\n\t\t\t\t</view>\n\t\t\t\t<view class=\"sub-title\">\n\t\t\t\t\t{{pageType == 1 ? $t('登录注册需绑定手机号') : $t('首次登录会自动注册')}}\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"input-item df\">\n\t\t\t\t\t<view class=\"icon-mobile\">\n\t\t\t\t\t\t<image src=\"/static/img/icon-mobile.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<input type='number' :placeholder='$t(`填写手机号码`)' v-model=\"phone\" :maxlength=\"11\"/>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"input-item df\">\n\t\t\t\t\t<view class=\"icon-code\">\n\t\t\t\t\t\t<image src=\"/static/img/icon-code.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<input type='number' :placeholder='$t(`填写验证码`)' :maxlength=\"6\" v-model=\"captcha\"/>\n\t\t\t\t\t<view :class=\"['send-code', disabled ? 'active' : '']\" @tap=\"code\">{{text}}</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"tips-text\" v-if=\"pageType == 0\">* {{$t(`首次登录将自动创建账号`)}}</view>\n\t\t\t\t\n\t\t\t\t<view class=\"btn-submit df\" @tap=\"submitData\">\n\t\t\t\t\t{{$t(`${pageType == 1 ? '绑定手机号' : '立即登录'}`)}}\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"agreement\" v-if=\"pageType == 0 && !canGetPrivacySetting\">\n\t\t\t\t\t<checkbox-group @tap.stop='ChangeIsDefault'>\n\t\t\t\t\t\t<label class=\"df\">\n\t\t\t\t\t\t\t<checkbox :class=\"inAnimation ? 'trembling' : ''\" @animationend='inAnimation=false' :checked=\"protocol ? true : false\" style=\"transform:scale(0.7)\"/>\n\t\t\t\t\t\t\t<view class=\"agreement-text\">\n\t\t\t\t\t\t\t\t{{$t(`已阅读并同意`)}}\n\t\t\t\t\t\t\t\t<text @tap.stop=\"privacy(4)\">{{$t(`《用户协议》`)}}</text>\n\t\t\t\t\t\t\t\t{{$t(`与`)}}\n\t\t\t\t\t\t\t\t<text @tap.stop=\"privacy(3)\">{{$t(`《隐私协议》`)}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</label>\n\t\t\t\t\t</checkbox-group>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<Verify @success=\"success\" :captchaType=\"'clickWord'\" :imgSize=\"{ width: '330px', height: '155px' }\" ref=\"verify\"></Verify>\n\t\t<editUserModal :isShow=\"isShow\" @closeEdit=\"closeEdit\" @editSuccess=\"editSuccess\">\n\t\t</editUserModal>\n\t\t<!-- #ifdef MP -->\n\t\t<privacyAgreementPopup v-if=\"canGetPrivacySetting\" @onReject=\"onReject\" @onAgree=\"onAgree\">\n\t\t</privacyAgreementPopup>\n\t\t<!-- #endif -->\n\t</view>\n</template>\n\n<script>\n\tconst app = getApp();\n\tlet statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 'px';\n\timport sendVerifyCode from \"@/mixins/SendVerifyCode\";\n\timport colors from '@/mixins/color.js';\n\timport editUserModal from '@/components/eidtUserModal/index.vue'\n\timport privacyAgreementPopup from '@/components/privacyAgreementPopup/index.vue'\n\timport {\n\t\tbindingUserPhone,\n\t\tverifyCode,\n\t\tregisterVerify,\n\t\tupdatePhone\n\t} from '@/api/api.js';\n\timport {\n\tloginMobile,\n\tgetCodeApi,\n\tgetUserInfo,\n\tphoneSilenceAuth\n} from \"@/api/user.js\";\nimport {\n\tgetUserSocialInfo\n} from '@/api/social.js';\n\timport {\n\t\tphoneLogin,\n\t\twechatBindingPhone\n\t} from '@/api/public.js'\n\timport Routine from '@/libs/routine';\n\timport Verify from '../components/verify/index.vue';\n\timport Cache from '@/utils/cache';\n\texport default {\n\t\tmixins: [sendVerifyCode, colors],\n\t\tcomponents: {\n\t\t\tVerify,\n\t\t\teditUserModal,\n\t\t\tprivacyAgreementPopup\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tstatusBarHeight: statusBarHeight,\n\t\t\t\tpageType: 1, // 0 登录 1 绑定手机\n\t\t\t\tphone: '',\n\t\t\t\tcaptcha: '',\n\t\t\t\ttext: '获取验证码',\n\t\t\t\tisShow: false,\n\t\t\t\tprotocol: false,\n\t\t\t\tinAnimation: false,\n\t\t\t\tauthKey: \"\",\n\t\t\t\tbackUrl: \"\",\n\t\t\t\tpageTitle: '绑定手机号',\n\t\t\t\tconfigData: Cache.get('BASIC_CONFIG'),\n\t\t\t\tcanGetPrivacySetting: false,\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\tif (options.authKey) {\n\t\t\t\tthis.authKey = options.authKey\n\t\t\t}\n\t\t\t// #ifdef MP\n\t\t\tif (wx.getPrivacySetting) {\n\t\t\t\tthis.canGetPrivacySetting = true\n\t\t\t}\n\t\t\t// #endif\n\n\t\t\tthis.backUrl = options.backUrl || ''\n\t\t\tif (options.pageType) {\n\t\t\t\tthis.pageType = options.pageType || 1\n\t\t\t\tthis.pageTitle = options.pageType == 1 ? '绑定手机号' : '手机号登录'\n\t\t\t}\n\t\t\tlet pages = getCurrentPages();\n\t\t\tlet prePage = pages[pages.length - 2];\n\t\t\tif (prePage && prePage.route == 'pages/order_addcart/order_addcart') {\n\t\t\t\tthis.isHome = true;\n\t\t\t} else {\n\t\t\t\tthis.isHome = false;\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tonAgree() {\n\t\t\t\tthis.protocol = true\n\t\t\t},\n\t\t\tsubmitData() {\n\t\t\t\tlet that = this;\n\t\t\t\tif (this.pageType == 0) {\n\t\t\t\t\tthis.isLogin()\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tif (!this.rules()) return\n\t\t\t\tif (!this.authKey) {\n\t\t\t\t\tlet key = this.$Cache.get('snsapiKey');\n\t\t\t\t\tthis.phoneAuth(key)\n\t\t\t\t} else {\n\t\t\t\t\tthis.phoneAuth(this.authKey)\n\t\t\t\t}\n\t\t\t},\n\t\t\trules() {\n\t\t\t\tlet that = this;\n\t\t\t\tif (!this.protocol && this.pageType == 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: this.$t('请先阅读并同意协议'),\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\tif (!that.phone) {\n\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\ttitle: that.$t(`请填写手机号码`)\n\t\t\t\t\t});\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\tif (!(/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.phone))) {\n\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\ttitle: that.$t(`请输入正确的手机号码`)\n\t\t\t\t\t});\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\tif (!that.captcha) {\n\t\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\t\ttitle: that.$t(`请填写验证码`)\n\t\t\t\t\t});\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\treturn true\n\t\t\t},\n\t\t\tisLogin() {\n\t\t\t\tif (!this.rules()) return\n\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: this.$t(`正在登录中`)\n\t\t\t\t});\n\t\t\t\tRoutine.getCode()\n\t\t\t\t\t.then(code => {\n\t\t\t\t\t\tphoneLogin({\n\t\t\t\t\t\t\t\tcode,\n\t\t\t\t\t\t\t\tspread_spid: app.globalData.spid,\n\t\t\t\t\t\t\t\tspread_code: app.globalData.code,\n\t\t\t\t\t\t\t\tphone: this.phone,\n\t\t\t\t\t\t\t\tcaptcha: this.captcha,\n\t\t\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tlet time = res.data.expires_time - this.$Cache.time();\n\t\t\t\t\t\t\t\tthis.$store.commit('LOGIN', {\n\t\t\t\t\t\t\t\t\ttoken: res.data.token,\n\t\t\t\t\t\t\t\t\ttime: time\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tthis.getUserInfo(res.data.bindName);\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: err,\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t});\n\t\t\t\t\t})\n\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\tconsole.log(err)\n\t\t\t\t\t});\n\t\t\t},\n\t\t\tphoneAuth(key) {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: this.$t(`正在登录中`)\n\t\t\t\t});\n\t\t\t\tlet met\n\t\t\t\t// #ifdef MP\n\t\t\t\tmet = phoneLogin\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef MP\n\t\t\t\tmet = wechatBindingPhone\n\t\t\t\t// #endif\n\t\t\t\tmet({\n\t\t\t\t\tphone: this.phone,\n\t\t\t\t\tcaptcha: this.captcha,\n\t\t\t\t\tkey\n\t\t\t\t}).then(res => {\n\t\t\t\t\tlet time = res.data.expires_time - this.$Cache.time();\n\t\t\t\t\tthis.$store.commit('LOGIN', {\n\t\t\t\t\t\ttoken: res.data.token,\n\t\t\t\t\t\ttime: time\n\t\t\t\t\t});\n\t\t\t\t\tthis.getUserInfo(res.data.bindName);\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\tthis.$util.Tips({\n\t\t\t\t\t\ttitle: error\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t},\n\t\t\t/**\n\t\t\t * 获取个人用户信息\n\t\t\t */\n\t\t\tgetUserInfo(new_user) {\n\t\t\t\tlet that = this;\n\t\t\t\t// 先获取基础用户信息\n\t\t\t\tgetUserInfo().then(res => {\n\t\t\t\t\tthat.userInfo = res.data;\n\t\t\t\t\tthat.$store.commit('SETUID', res.data.uid);\n\t\t\t\t\t\n\t\t\t\t\t// 再获取用户社交信息（拓展信息）\n\t\t\t\t\tgetUserSocialInfo()\n\t\t\t\t\t\t.then((socialRes) => {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t// 更新用户社交信息到Vuex\n\t\t\t\t\t\t\tif (socialRes.data) {\n\t\t\t\t\t\t\t\tthat.$store.commit('UPDATE_USERINFO', socialRes.data);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tif (new_user) {\n\t\t\t\t\t\t\t\tthis.isShow = true\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// #ifdef MP\n\t\t\t\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\t\t\t\ttitle: that.$t(`登录成功`),\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t}, {\n\t\t\t\t\t\t\t\t\ttab: 3,\n\t\t\t\t\t\t\t\t\turl: this.configData.wechat_auth_switch ? 2 : 1\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\t// #ifndef MP\n\t\t\t\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\t\t\t\ttitle: that.$t(`登录成功`),\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t}, {\n\t\t\t\t\t\t\t\t\ttab: 4,\n\t\t\t\t\t\t\t\t\turl: this.backUrl || 'pages/user/index'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(() => {\n\t\t\t\t\t\t\t// 即使社交信息获取失败，仍然继续\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tif (new_user) {\n\t\t\t\t\t\t\t\tthis.isShow = true\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// #ifdef MP\n\t\t\t\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\t\t\t\ttitle: that.$t(`登录成功`),\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t}, {\n\t\t\t\t\t\t\t\t\ttab: 3,\n\t\t\t\t\t\t\t\t\turl: this.configData.wechat_auth_switch ? 2 : 1\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t\t// #ifndef MP\n\t\t\t\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\t\t\t\ttitle: that.$t(`登录成功`),\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t}, {\n\t\t\t\t\t\t\t\t\ttab: 4,\n\t\t\t\t\t\t\t\t\turl: this.backUrl || 'pages/user/index'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\tsuccess(data) {\n\t\t\t\tthis.$refs.verify.hide()\n\t\t\t\tlet that = this;\n\t\t\t\tverifyCode().then(res => {\n\t\t\t\t\tregisterVerify(that.phone, 'reset', res.data.key, this.captchaType, data.captchaVerification)\n\t\t\t\t\t\t.then(res => {\n\t\t\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tthat.sendCode();\n\t\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\t\t\t\ttitle: err\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\t/**\n\t\t\t * 发送验证码\n\t\t\t *\n\t\t\t */\n\t\t\tasync code() {\n\t\t\t\tlet that = this;\n\t\t\t\tif (!that.phone) return that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请填写手机号码`)\n\t\t\t\t});\n\t\t\t\tif (!(/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.phone))) return that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请输入正确的手机号码`)\n\t\t\t\t});\n\t\t\t\tthis.$refs.verify.show();\n\t\t\t\treturn;\n\t\t\t},\n\t\t\tChangeIsDefault() {\n\t\t\t\tthis.$set(this, 'protocol', !this.protocol);\n\t\t\t},\n\t\t\tcloseEdit() {\n\t\t\t\tthis.isShow = false\n\t\t\t\tthis.$util.Tips({\n\t\t\t\t\ttitle: this.$t(`登录成功`),\n\t\t\t\t\ticon: 'success'\n\t\t\t\t}, {\n\t\t\t\t\ttab: 3,\n\t\t\t\t\turl: 2\n\t\t\t\t});\n\t\t\t},\n\t\t\teditSuccess() {\n\t\t\t\tthis.isShow = false\n\t\t\t},\n\t\t\tback() {\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta: this.configData.wechat_auth_switch ? 2 : 1\n\t\t\t\t})\n\t\t\t},\n\t\t\tprivacy(type) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: \"/pages/users/privacy/index?type=\" + type\n\t\t\t\t})\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n.container {\n\twidth: 100%;\n\tbackground: #fff;\n\theight: 100vh;\n\toverflow: hidden;\n}\n\n.content-box {\n\twidth: 100%;\n}\n\n.login-box {\n\twidth: calc(100% - 60rpx);\n\tmargin: 30rpx;\n}\n\n.login-box .title {\n\tfont-size: 40rpx;\n\tfont-weight: 700;\n\tcolor: #000;\n}\n\n.login-box .sub-title {\n\tmargin-top: 20rpx;\n\tfont-size: 26rpx;\n\tcolor: #999;\n}\n\n.login-box .input-item {\n\tmargin-top: 60rpx;\n\tpadding: 0 30rpx;\n\twidth: calc(100% - 60rpx);\n\theight: 100rpx;\n\tborder-radius: 100rpx;\n\tbackground: #f8f8f8;\n\tposition: relative;\n}\n\n.input-item .icon-mobile,\n.input-item .icon-code {\n\tmargin-right: 20rpx;\n\twidth: 36rpx;\n\theight: 36rpx;\n}\n\n.input-item .icon-mobile image,\n.input-item .icon-code image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.input-item input {\n\twidth: calc(100% - 56rpx);\n\theight: 100%;\n\tfont-size: 28rpx;\n}\n\n.input-item .send-code {\n\tposition: absolute;\n\tright: 30rpx;\n\twidth: 180rpx;\n\theight: 60rpx;\n\tline-height: 60rpx;\n\ttext-align: center;\n\tfont-size: 24rpx;\n\tfont-weight: 700;\n\tbackground: #000;\n\tcolor: #fff;\n\tborder-radius: 30rpx;\n}\n\n.input-item .send-code.active {\n\tbackground: #f5f5f5;\n\tcolor: #999;\n}\n\n.login-box .tips-text {\n\tmargin-top: 30rpx;\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.login-box .btn-submit {\n\tmargin-top: 60rpx;\n\twidth: 100%;\n\theight: 100rpx;\n\tline-height: 100rpx;\n\ttext-align: center;\n\tfont-size: 28rpx;\n\tfont-weight: 700;\n\tbackground: #000;\n\tcolor: #fff;\n\tborder-radius: 100rpx;\n\tjustify-content: center;\n}\n\n.login-box .agreement {\n\tmargin-top: 30rpx;\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.agreement .agreement-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.agreement-text text {\n\tcolor: #576b95;\n}\n\n.df {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.trembling {\n\tanimation: shake 0.6s;\n}\n\n@keyframes shake {\n\t0%, 100% { transform: translateX(0); }\n\t10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }\n\t20%, 40%, 60%, 80% { transform: translateX(5px); }\n}\n</style>\n", "import MiniProgramPage from 'D:/uniapp/vue3/pages/users/binding_phone/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "sendVerifyCode", "colors", "<PERSON><PERSON>", "wx", "Routine", "phoneLogin", "getUserInfo", "getUserSocialInfo", "verifyCode", "registerVerify", "res"], "mappings": ";;;;;;;;;;;AA4DC,MAAM,MAAM,OAAM;AAClB,IAAI,kBAAkBA,cAAG,MAAC,kBAAiB,EAAG,kBAAkB;AAGhE,MAAK,gBAAiB,MAAW;AACjC,8BAA8B,MAAW;AAqBzC,eAAe,MAAW;AAE1B,MAAK,YAAU;AAAA,EACd,QAAQ,CAACC,sBAAc,gBAAEC,mBAAM;AAAA,EAC/B,YAAY;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN;AAAA,MACA,UAAU;AAAA;AAAA,MACV,OAAO;AAAA,MACP,SAAS;AAAA,MACT,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,WAAW;AAAA,MACX,YAAYC,YAAAA,MAAM,IAAI,cAAc;AAAA,MACpC,sBAAsB;AAAA,IACvB;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,QAAI,QAAQ,SAAS;AACpB,WAAK,UAAU,QAAQ;AAAA,IACxB;AAEA,QAAIC,cAAAA,KAAG,mBAAmB;AACzB,WAAK,uBAAuB;AAAA,IAC7B;AAGA,SAAK,UAAU,QAAQ,WAAW;AAClC,QAAI,QAAQ,UAAU;AACrB,WAAK,WAAW,QAAQ,YAAY;AACpC,WAAK,YAAY,QAAQ,YAAY,IAAI,UAAU;AAAA,IACpD;AACA,QAAI,QAAQ;AACZ,QAAI,UAAU,MAAM,MAAM,SAAS,CAAC;AACpC,QAAI,WAAW,QAAQ,SAAS,qCAAqC;AACpE,WAAK,SAAS;AAAA,WACR;AACN,WAAK,SAAS;AAAA,IACf;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,UAAU;AACT,WAAK,WAAW;AAAA,IAChB;AAAA,IACD,aAAa;AAEZ,UAAI,KAAK,YAAY,GAAG;AACvB,aAAK,QAAQ;AACb;AAAA,MACD;AACA,UAAI,CAAC,KAAK,MAAK;AAAI;AACnB,UAAI,CAAC,KAAK,SAAS;AAClB,YAAI,MAAM,KAAK,OAAO,IAAI,WAAW;AACrC,aAAK,UAAU,GAAG;AAAA,aACZ;AACN,aAAK,UAAU,KAAK,OAAO;AAAA,MAC5B;AAAA,IACA;AAAA,IACD,QAAQ;AACP,UAAI,OAAO;AACX,UAAI,CAAC,KAAK,YAAY,KAAK,YAAY,GAAG;AACzCJ,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,KAAK,GAAG,WAAW;AAAA,UAC1B,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AACD,eAAO;AAAA,MACR;AACA,UAAI,CAAC,KAAK,OAAO;AAChB,aAAK,MAAM,KAAK;AAAA,UACf,OAAO,KAAK,GAAG,SAAS;AAAA,QACzB,CAAC;AACD,eAAO;AAAA,MACR;AACA,UAAI,CAAE,2BAA2B,KAAK,KAAK,KAAK,GAAI;AACnD,aAAK,MAAM,KAAK;AAAA,UACf,OAAO,KAAK,GAAG,YAAY;AAAA,QAC5B,CAAC;AACD,eAAO;AAAA,MACR;AACA,UAAI,CAAC,KAAK,SAAS;AAClB,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,QAAQ;AAAA,QACxB,CAAC;AAAA,MAEF;AACA,aAAO;AAAA,IACP;AAAA,IACD,UAAU;AACT,UAAI,CAAC,KAAK,MAAK;AAAI;AAEnBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,KAAK,GAAG,OAAO;AAAA,MACvB,CAAC;AACDK,mBAAAA,QAAQ,QAAQ,EACd,KAAK,UAAQ;AACbC,8BAAW;AAAA,UACT;AAAA,UACA,aAAa,IAAI,WAAW;AAAA,UAC5B,aAAa,IAAI,WAAW;AAAA,UAC5B,OAAO,KAAK;AAAA,UACZ,SAAS,KAAK;AAAA,SACd,EAAE,KAAK,SAAO;AACdN,wBAAG,MAAC,YAAW;AACf,cAAI,OAAO,IAAI,KAAK,eAAe,KAAK,OAAO;AAC/C,eAAK,OAAO,OAAO,SAAS;AAAA,YAC3B,OAAO,IAAI,KAAK;AAAA,YAChB;AAAA,UACD,CAAC;AACD,eAAK,YAAY,IAAI,KAAK,QAAQ;AAAA,SAClC,EACA,MAAM,SAAO;AACbA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AAAA,QACF,CAAC;AAAA,OACF,EACA,MAAM,SAAO;AACbA,sBAAAA,MAAY,MAAA,OAAA,8CAAA,GAAG;AAAA,MAChB,CAAC;AAAA,IACF;AAAA,IACD,UAAU,KAAK;AACdA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,KAAK,GAAG,OAAO;AAAA,MACvB,CAAC;AACD,UAAI;AAEJ,YAAMM,WAAS;AAKf,UAAI;AAAA,QACH,OAAO,KAAK;AAAA,QACZ,SAAS,KAAK;AAAA,QACd;AAAA,OACA,EAAE,KAAK,SAAO;AACd,YAAI,OAAO,IAAI,KAAK,eAAe,KAAK,OAAO;AAC/C,aAAK,OAAO,OAAO,SAAS;AAAA,UAC3B,OAAO,IAAI,KAAK;AAAA,UAChB;AAAA,QACD,CAAC;AACD,aAAK,YAAY,IAAI,KAAK,QAAQ;AAAA,MACnC,CAAC,EAAE,MAAM,WAAS;AACjBN,sBAAAA,MAAI,YAAY;AAChB,aAAK,MAAM,KAAK;AAAA,UACf,OAAO;AAAA,SACP;AAAA,OACD;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAID,YAAY,UAAU;AACrB,UAAI,OAAO;AAEXO,2BAAa,EAAC,KAAK,SAAO;AACzB,aAAK,WAAW,IAAI;AACpB,aAAK,OAAO,OAAO,UAAU,IAAI,KAAK,GAAG;AAGzCC,qCAAkB,EAChB,KAAK,CAAC,cAAc;AACpBR,wBAAG,MAAC,YAAW;AAEf,cAAI,UAAU,MAAM;AACnB,iBAAK,OAAO,OAAO,mBAAmB,UAAU,IAAI;AAAA,UACrD;AAEA,cAAI,UAAU;AACb,iBAAK,SAAS;AAAA,iBACR;AAEN,iBAAK,MAAM,KAAK;AAAA,cACf,OAAO,KAAK,GAAG,MAAM;AAAA,cACrB,MAAM;AAAA,YACP,GAAG;AAAA,cACF,KAAK;AAAA,cACL,KAAK,KAAK,WAAW,qBAAqB,IAAI;AAAA,YAC/C,CAAC;AAAA,UAWF;AAAA,SACA,EACA,MAAM,MAAM;AAEZA,wBAAG,MAAC,YAAW;AACf,cAAI,UAAU;AACb,iBAAK,SAAS;AAAA,iBACR;AAEN,iBAAK,MAAM,KAAK;AAAA,cACf,OAAO,KAAK,GAAG,MAAM;AAAA,cACrB,MAAM;AAAA,YACP,GAAG;AAAA,cACF,KAAK;AAAA,cACL,KAAK,KAAK,WAAW,qBAAqB,IAAI;AAAA,YAC/C,CAAC;AAAA,UAWF;AAAA,QACD,CAAC;AAAA,MACH,CAAC;AAAA,IACD;AAAA,IACD,QAAQ,MAAM;AACb,WAAK,MAAM,OAAO,KAAK;AACvB,UAAI,OAAO;AACXS,yBAAY,EAAC,KAAK,SAAO;AACxBC,gBAAAA,eAAe,KAAK,OAAO,SAAS,IAAI,KAAK,KAAK,KAAK,aAAa,KAAK,mBAAmB,EAC1F,KAAK,CAAAC,SAAO;AACZ,eAAK,MAAM,KAAK;AAAA,YACf,OAAOA,KAAI;AAAA,UACZ,CAAC;AACD,eAAK,SAAQ;AAAA,QACd,CAAC,EAAE,MAAM,SAAO;AACf,iBAAO,KAAK,MAAM,KAAK;AAAA,YACtB,OAAO;AAAA,UACR,CAAC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,OAAO;AACZ,UAAI,OAAO;AACX,UAAI,CAAC,KAAK;AAAO,eAAO,KAAK,MAAM,KAAK;AAAA,UACvC,OAAO,KAAK,GAAG,SAAS;AAAA,QACzB,CAAC;AACD,UAAI,CAAE,2BAA2B,KAAK,KAAK,KAAK;AAAI,eAAO,KAAK,MAAM,KAAK;AAAA,UAC1E,OAAO,KAAK,GAAG,YAAY;AAAA,QAC5B,CAAC;AACD,WAAK,MAAM,OAAO;AAClB;AAAA,IACA;AAAA,IACD,kBAAkB;AACjB,WAAK,KAAK,MAAM,YAAY,CAAC,KAAK,QAAQ;AAAA,IAC1C;AAAA,IACD,YAAY;AACX,WAAK,SAAS;AACd,WAAK,MAAM,KAAK;AAAA,QACf,OAAO,KAAK,GAAG,MAAM;AAAA,QACrB,MAAM;AAAA,MACP,GAAG;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA,IACD,cAAc;AACb,WAAK,SAAS;AAAA,IACd;AAAA,IACD,OAAO;AACNX,oBAAAA,MAAI,aAAa;AAAA,QAChB,OAAO,KAAK,WAAW,qBAAqB,IAAI;AAAA,OAChD;AAAA,IACD;AAAA,IACD,QAAQ,MAAM;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,qCAAqC;AAAA,OAC1C;AAAA,IACD;AAAA,EACF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxXD,GAAG,WAAW,eAAe;"}