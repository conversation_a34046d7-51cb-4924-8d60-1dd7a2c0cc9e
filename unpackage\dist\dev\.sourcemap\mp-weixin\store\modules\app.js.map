{"version": 3, "file": "app.js", "sources": ["store/modules/app.js"], "sourcesContent": ["// +----------------------------------------------------------------------\r\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\r\n// +----------------------------------------------------------------------\r\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\r\n// +----------------------------------------------------------------------\r\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\r\n// +----------------------------------------------------------------------\r\n// | Author: CRMEB Team <<EMAIL>>\r\n// +----------------------------------------------------------------------\r\n\r\nimport {\r\n\tgetUserInfo\r\n} from \"../../api/user.js\";\r\nimport cacheConfig from '../../config/cache.js';\r\nconst {\r\n\tLOGIN_STATUS,\r\n\tUID,\r\n\tUSER_INFO\r\n} = cacheConfig;\r\nimport Cache from '../../utils/cache';\r\n\r\nconst state = {\r\n\ttoken: Cache.get(LOGIN_STATUS) || false,\r\n\tbackgroundColor: \"#fff\",\r\n\tuserInfo: {},\r\n\tuid: Cache.get(UID) || 0,\r\n\thomeActive: false,\r\n\tphoneStatus: true,\r\n\tpageFooter: uni.getStorageSync('pageFoot') || {},\r\n\tactivityTab: '',\r\n\tisCurrentMsg: false,\r\n\tunreadMessageCount: 0,\r\n\tpreventScroll: false,\r\n\tflowSettings: {\r\n\t\tdynamicFlow: false,\r\n\t\tcircleFlow: false\r\n\t}\r\n};\r\n\r\nconst mutations = {\r\n\tSETPHONESTATUS(state, val) {\r\n\t\tstate.phoneStatus = val;\r\n\t},\r\n\tLOGIN(state, opt) {\r\n\t\tstate.token = opt.token;\r\n\t\tCache.set(LOGIN_STATUS, opt.token, opt.time);\r\n\t},\r\n\tSETUID(state, val) {\r\n\t\tstate.uid = val;\r\n\t\tCache.set(UID, val);\r\n\t},\r\n\tUPDATE_LOGIN(state, token) {\r\n\t\tstate.token = token;\r\n\t},\r\n\tACTIVITYTAB(state, tab) {\r\n\t\tstate.activityTab = tab;\r\n\t},\r\n\tLOGOUT(state) {\r\n\t\tstate.token = false;\r\n\t\tstate.uid = 0\r\n\t\tCache.clear(LOGIN_STATUS);\r\n\t\tCache.clear(USER_INFO);\r\n\t\tCache.clear(UID);\r\n\t\tCache.clear('snsapiCode');\r\n\t},\r\n\tBACKGROUND_COLOR(state, color) {\r\n\t\tstate.backgroundColor = color;\r\n\t},\r\n\tSET_CURRENT_MSG(state, val) {\r\n\t\tstate.isCurrentMsg = val;\r\n\t},\r\n\tSET_UNREAD_MESSAGE_COUNT(state, count) {\r\n\t\tstate.unreadMessageCount = count;\r\n\t},\r\n\tUPDATE_USERINFO(state, userInfo) {\r\n\t\tstate.userInfo = userInfo;\r\n\t\tCache.set(USER_INFO, userInfo);\r\n\t},\r\n\tOPEN_HOME(state) {\r\n\t\tstate.homeActive = true;\r\n\t},\r\n\tCLOSE_HOME(state) {\r\n\t\tstate.homeActive = false;\r\n\t},\r\n\tFOOT_UPLOAD(state, data) {\r\n\t\tstate.pageFooter = data\r\n\t},\r\n\tSET_PREVENT_SCROLL(state, value) {\r\n\t\tstate.preventScroll = value;\r\n\t},\r\n\tUPDATE_FLOW_SETTINGS(state, flowSettings) {\r\n\t\tstate.flowSettings = { ...state.flowSettings, ...flowSettings };\r\n\t}\r\n};\r\n\r\nconst actions = {\r\n\r\n\tUSERINFO({\r\n\t\tstate,\r\n\t\tcommit\r\n\t}, force) {\r\n\t\tif (state.userInfo !== null && !force)\r\n\t\t\treturn Promise.resolve(state.userInfo);\r\n\t\telse\r\n\t\t\treturn new Promise(reslove => {\r\n\t\t\t\tgetUserInfo().then(res => {\r\n\t\t\t\t\tcommit(\"UPDATE_USERINFO\", res.data);\r\n\t\t\t\t\tCache.set(USER_INFO, res.data);\r\n\t\t\t\t\treslove(res.data);\r\n\t\t\t\t});\r\n\t\t\t}).catch(() => {\r\n\r\n\t\t\t});\r\n\t}\r\n};\r\n\r\nexport default {\r\n\tstate,\r\n\tmutations,\r\n\tactions\r\n};\r\n"], "names": ["cacheConfig", "<PERSON><PERSON>", "uni", "state", "getUserInfo"], "mappings": ";;;;;AAcA,MAAM;AAAA,EACL;AAAA,EACA;AAAA,EACA;AACD,IAAIA;AAGJ,MAAM,QAAQ;AAAA,EACb,OAAOC,YAAK,MAAC,IAAI,YAAY,KAAK;AAAA,EAClC,iBAAiB;AAAA,EACjB,UAAU,CAAE;AAAA,EACZ,KAAKA,YAAK,MAAC,IAAI,GAAG,KAAK;AAAA,EACvB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAYC,cAAG,MAAC,eAAe,UAAU,KAAK,CAAE;AAAA,EAChD,aAAa;AAAA,EACb,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,cAAc;AAAA,IACb,aAAa;AAAA,IACb,YAAY;AAAA,EACZ;AACF;AAEA,MAAM,YAAY;AAAA,EACjB,eAAeC,QAAO,KAAK;AAC1B,IAAAA,OAAM,cAAc;AAAA,EACpB;AAAA,EACD,MAAMA,QAAO,KAAK;AACjB,IAAAA,OAAM,QAAQ,IAAI;AAClBF,gBAAK,MAAC,IAAI,cAAc,IAAI,OAAO,IAAI,IAAI;AAAA,EAC3C;AAAA,EACD,OAAOE,QAAO,KAAK;AAClB,IAAAA,OAAM,MAAM;AACZF,gBAAAA,MAAM,IAAI,KAAK,GAAG;AAAA,EAClB;AAAA,EACD,aAAaE,QAAO,OAAO;AAC1B,IAAAA,OAAM,QAAQ;AAAA,EACd;AAAA,EACD,YAAYA,QAAO,KAAK;AACvB,IAAAA,OAAM,cAAc;AAAA,EACpB;AAAA,EACD,OAAOA,QAAO;AACb,IAAAA,OAAM,QAAQ;AACd,IAAAA,OAAM,MAAM;AACZF,sBAAM,MAAM,YAAY;AACxBA,sBAAM,MAAM,SAAS;AACrBA,sBAAM,MAAM,GAAG;AACfA,sBAAM,MAAM,YAAY;AAAA,EACxB;AAAA,EACD,iBAAiBE,QAAO,OAAO;AAC9B,IAAAA,OAAM,kBAAkB;AAAA,EACxB;AAAA,EACD,gBAAgBA,QAAO,KAAK;AAC3B,IAAAA,OAAM,eAAe;AAAA,EACrB;AAAA,EACD,yBAAyBA,QAAO,OAAO;AACtC,IAAAA,OAAM,qBAAqB;AAAA,EAC3B;AAAA,EACD,gBAAgBA,QAAO,UAAU;AAChC,IAAAA,OAAM,WAAW;AACjBF,gBAAAA,MAAM,IAAI,WAAW,QAAQ;AAAA,EAC7B;AAAA,EACD,UAAUE,QAAO;AAChB,IAAAA,OAAM,aAAa;AAAA,EACnB;AAAA,EACD,WAAWA,QAAO;AACjB,IAAAA,OAAM,aAAa;AAAA,EACnB;AAAA,EACD,YAAYA,QAAO,MAAM;AACxB,IAAAA,OAAM,aAAa;AAAA,EACnB;AAAA,EACD,mBAAmBA,QAAO,OAAO;AAChC,IAAAA,OAAM,gBAAgB;AAAA,EACtB;AAAA,EACD,qBAAqBA,QAAO,cAAc;AACzC,IAAAA,OAAM,eAAe,EAAE,GAAGA,OAAM,cAAc,GAAG;EACjD;AACF;AAEA,MAAM,UAAU;AAAA,EAEf,SAAS;AAAA,IACR,OAAAA;AAAA,IACA;AAAA,EACA,GAAE,OAAO;AACT,QAAIA,OAAM,aAAa,QAAQ,CAAC;AAC/B,aAAO,QAAQ,QAAQA,OAAM,QAAQ;AAAA;AAErC,aAAO,IAAI,QAAQ,aAAW;AAC7BC,6BAAa,EAAC,KAAK,SAAO;AACzB,iBAAO,mBAAmB,IAAI,IAAI;AAClCH,sBAAAA,MAAM,IAAI,WAAW,IAAI,IAAI;AAC7B,kBAAQ,IAAI,IAAI;AAAA,QACrB,CAAK;AAAA,MACL,CAAI,EAAE,MAAM,MAAM;AAAA,MAElB,CAAI;AAAA,EACF;AACF;AAEA,MAAe,MAAA;AAAA,EACd;AAAA,EACA;AAAA,EACA;AACD;;"}