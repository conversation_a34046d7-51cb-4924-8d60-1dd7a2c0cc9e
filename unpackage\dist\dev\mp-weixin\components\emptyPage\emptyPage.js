"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "EmptyPage",
  props: {
    // 标题
    title: {
      type: String,
      default: "暂无数据"
    },
    // 描述文字
    description: {
      type: String,
      default: ""
    },
    // 图片地址
    image: {
      type: String,
      default: "/static/img/empty.png"
    },
    // 图片尺寸
    imageSize: {
      type: String,
      default: "300rpx"
    },
    // 按钮文字
    buttonText: {
      type: String,
      default: ""
    }
  },
  methods: {
    handleButtonClick() {
      this.$emit("buttonClick");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.image,
    b: $props.imageSize,
    c: $props.imageSize,
    d: common_vendor.t($props.title),
    e: $props.description
  }, $props.description ? {
    f: common_vendor.t($props.description)
  } : {}, {
    g: $props.buttonText
  }, $props.buttonText ? {
    h: common_vendor.t($props.buttonText),
    i: common_vendor.o((...args) => $options.handleButtonClick && $options.handleButtonClick(...args))
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-5cea664a"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/emptyPage/emptyPage.js.map
