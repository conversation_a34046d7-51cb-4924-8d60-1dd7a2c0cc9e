{"version": 3, "file": "index.js", "sources": ["components/share/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy9zaGFyZS9pbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"share-container\" v-if=\"show\">\n    <view class=\"share-mask\" @tap.stop=\"close\"></view>\n    <view class=\"share-content\">\n      <!-- 标题栏 -->\n      <view class=\"share-header\">\n        <text class=\"share-title\">分享至</text>\n        <view class=\"popup-close df\" @tap.stop=\"close\">\n          <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\n        </view>\n      </view>\n      \n      <!-- 人员列表 - 可左右滑动 -->\n      <scroll-view scroll-x=\"true\" class=\"person-scroll\" show-scrollbar=\"false\">\n        <view class=\"person-list\">\n          <view class=\"person-item\" v-for=\"(person, index) in personList\" :key=\"index\" @tap=\"handleShareToPerson(person)\">\n            <view class=\"person-avatar\">\n              <image :src=\"person.avatar\" mode=\"aspectFill\"></image>\n              <view v-if=\"person.online\" class=\"person-online-dot\" :style=\"{'background': '#4CD964'}\"></view>\n            </view>\n            <view class=\"person-name ohto\">{{person.name}}</view>\n            <view class=\"person-tips\">\n              <text>好友</text>\n            </view>\n          </view>\n        </view>\n      </scroll-view>\n      \n      <!-- 第一条分割线 -->\n      <view class=\"divider-line\"></view>\n      \n      <!-- 分享选项 -->\n      <scroll-view scroll-x=\"true\" class=\"share-scroll\" show-scrollbar=\"false\">\n        <view class=\"share-list\">\n          <!-- 第一行分享选项 - 社交平台分享 -->\n          <!-- #ifndef H5 -->\n          <view class=\"share-item\" v-if=\"showWeixinButton\" @tap=\"shareToWeixin\">\n            <view class=\"share-icon-bg\" style=\"background-color: #50c332\">\n              <image :src=\"getIconSrc('wechat')\" class=\"share-icon\" mode=\"aspectFit\"></image>\n            </view>\n            <text class=\"share-text\">微信好友</text>\n          </view>\n          <!-- #endif -->\n          \n          <!-- #ifdef MP -->\n          <button class=\"share-item share-button\" open-type=\"share\" hover-class=\"none\" @tap=\"shareToMPFriend\">\n            <view class=\"share-icon-bg\" style=\"background-color: #50c332\">\n              <image :src=\"getIconSrc('wechat')\" class=\"share-icon\" mode=\"aspectFit\"></image>\n            </view>\n            <text class=\"share-text\">发送给朋友</text>\n          </button>\n          <!-- #endif -->\n          \n          <!-- #ifdef APP-PLUS -->\n          <view class=\"share-item\" @tap=\"appShare('WXSceneSession')\">\n            <view class=\"share-icon-bg\" style=\"background-color: #50c332\">\n              <image :src=\"getIconSrc('wechat')\" class=\"share-icon\" mode=\"aspectFit\"></image>\n            </view>\n            <text class=\"share-text\">微信好友</text>\n          </view>\n          <view class=\"share-item\" @tap=\"appShare('WXSenceTimeline')\">\n            <view class=\"share-icon-bg\" style=\"background-color: #87d068\">\n              <image :src=\"getIconSrc('moments')\" class=\"share-icon\" mode=\"aspectFit\"></image>\n            </view>\n            <text class=\"share-text\">微信朋友圈</text>\n          </view>\n          <!-- #endif -->\n          \n          <!-- 复制口令 - 支持所有平台 -->\n          <view class=\"share-item copy-data\" v-if=\"showCopyCommand\" :data-clipboard-text=\"shareData.command_word || generateCommandWord()\" @tap=\"copyCommand\">\n            <view class=\"share-icon-bg\" style=\"background-color: #f8f8f8\">\n              <image :src=\"getIconSrc('copy')\" class=\"share-icon\" mode=\"aspectFit\"></image>\n            </view>\n            <text class=\"share-text\">复制口令</text>\n          </view>\n          \n          <view class=\"share-item\" @tap=\"generatePoster\">\n            <view class=\"share-icon-bg\" style=\"background-color: #f8f8f8\">\n              <image :src=\"getIconSrc('poster')\" class=\"share-icon\" mode=\"aspectFit\"></image>\n            </view>\n            <text class=\"share-text\">生成海报</text>\n          </view>\n          \n          <view class=\"share-item\" @tap=\"copyLink\">\n            <view class=\"share-icon-bg\" style=\"background-color: #f8f8f8\">\n              <image :src=\"getIconSrc('link')\" class=\"share-icon\" mode=\"aspectFit\"></image>\n            </view>\n            <text class=\"share-text\">复制链接</text>\n          </view>\n        </view>\n      </scroll-view>\n      \n      <!-- 第二条分割线 -->\n      <view class=\"divider-line\"></view>\n      \n      <!-- 第二行分享选项 - 功能操作 -->\n      <scroll-view scroll-x=\"true\" class=\"share-scroll\" show-scrollbar=\"false\">\n        <view class=\"share-list\">\n          <view class=\"share-item\" v-for=\"(item, index) in computedBottomItems\" :key=\"index\" @tap=\"handleAction(item)\">\n            <view class=\"share-icon-bg bottom-icon\" :class=\"{'delete-item': item.type === 'delete'}\">\n              <image :src=\"getIconSrc(item.icon)\" class=\"share-icon\" mode=\"aspectFit\"></image>\n            </view>\n            <text class=\"share-text\" :class=\"{'delete-text': item.type === 'delete'}\">{{item.name}}</text>\n          </view>\n        </view>\n      </scroll-view>\n      \n      <!-- 底部安全距离 -->\n      <view class=\"safe-bottom\"></view>\n    </view>\n    \n    <!-- 海报展示弹窗 -->\n    <view class=\"poster-popup\" v-if=\"posterImageStatus\">\n      <image src=\"/static/img/poster-close.png\" class=\"close\" @tap=\"posterImageClose\"></image>\n      <image class=\"poster-img\" :src=\"posterImage\"></image>\n      <!-- #ifndef H5 -->\n      <view class=\"save-poster\" @tap=\"savePosterPath\">保存到手机</view>\n      <!-- #endif -->\n      <!-- #ifdef H5 -->\n      <view class=\"keep\">长按图片可以保存到手机</view>\n      <!-- #endif -->\n    </view>\n    <view class=\"mask\" v-if=\"posterImageStatus\" @tap=\"posterImageClose\"></view>\n    <canvas class=\"canvas\" canvas-id=\"myCanvas\" v-if=\"canvasStatus\"></canvas>\n    \n    <!-- #ifdef H5 -->\n    <!-- 发送给朋友图片 -->\n    <view class=\"share-box\" v-if=\"H5ShareBox\">\n      <image :src=\"imgHost + '/statics/images/share-info.png'\" @tap=\"H5ShareBox = false\"></image>\n    </view>\n    <!-- #endif -->\n  </view>\n</template>\n\n<script>\n// #ifdef H5\n// 不再使用clipboard.js，直接使用原生clipboard API和备用方案\n// #endif\n\n// 统一导入appConfig，避免条件编译作用域问题\nimport appConfig from '@/config/app.js';\nconst { HTTP_REQUEST_URL } = appConfig;\n\n// #ifdef APP-PLUS\nconst { TOKENNAME } = appConfig;\n// #endif\n\nexport default {\n  props: {\n    show: {\n      type: Boolean,\n      default: false\n    },\n    // 分享数据信息\n    shareData: {\n      type: Object,\n      default: () => ({\n        title: '默认标题',\n        description: '默认描述',\n        image: '/static/img/avatar.png',\n        link: '',\n        command_word: ''\n      })\n    },\n    // 笔记信息\n    noteInfo: {\n      type: Object,\n      default: () => ({})\n    },\n    // 当前用户ID\n    userId: {\n      type: [Number, String],\n      default: 0\n    },\n    // 分享类型 goods: 商品分享, note: 笔记分享\n    shareType: {\n      type: String,\n      default: 'note'\n    }\n  },\n  data() {\n    return {\n      imgHost: HTTP_REQUEST_URL,\n      // 人员列表\n      personList: [\n        { id: 1, name: '好友1', avatar: '/static/img/avatar.png', online: true },\n        { id: 2, name: '好友2', avatar: '/static/img/avatar.png', online: false },\n        { id: 3, name: '好友3', avatar: '/static/img/avatar.png', online: true },\n        { id: 4, name: '好友4', avatar: '/static/img/avatar.png', online: false },\n        { id: 5, name: '好友5', avatar: '/static/img/avatar.png', online: true },\n        { id: 6, name: '好友6', avatar: '/static/img/avatar.png', online: true },\n        { id: 7, name: '好友7', avatar: '/static/img/avatar.png', online: false },\n        { id: 8, name: '好友8', avatar: '/static/img/avatar.png', online: true },\n        { id: 9, name: '好友9', avatar: '/static/img/avatar.png', online: false },\n        { id: 10, name: '好友10', avatar: '/static/img/avatar.png', online: true }\n      ],\n      // 第二行分享选项 - 功能操作\n      bottomItems: [],\n      // 默认图标映射\n      defaultIcons: {\n        forward: '/static/img/fx.png',\n        wechat: '/static/img/wz.png',\n        moments: '/static/img/pl.png',\n        qq: '/static/img/dz.png',\n        qzone: '/static/img/dz1.png',\n        poster: '/static/img/gd.png',\n        link: '/static/img/z.png',\n        copy: '/static/img/bj.png',\n        dislike: '/static/img/sc.png',\n        money: '/static/img/bj.png',\n        report: '/static/img/setting/5.png',\n        miniapp: '/static/img/xcx.png',\n        edit: '/static/img/bj.png',\n        delete: '/static/img/sc.png'\n      },\n      // 举报原因列表\n      reportReasons: [\"违法违规\", \"色情低俗\", \"账号诈骗\", \"侵犯权益\", \"骚扰谩骂\", \"内容抄袭\", \"其他\"],\n      \n      // 海报相关数据\n      posterImageStatus: false,\n      posterImage: '',\n      canvasStatus: false,\n      H5ShareBox: false,\n      \n      // 微信状态\n      weixinStatus: false,\n      showWeixinButton: false\n    };\n  },\n  computed: {\n    // 动态计算底部操作项\n    computedBottomItems() {\n      if (this.shareType === 'note') {\n      const isCircle = this.noteInfo.type === 'circle';\n      const isOwner = this.noteInfo.uid && this.noteInfo.uid == this.userId;\n      \n      const items = [\n        { name: '不喜欢', icon: 'dislike', bgColor: '#f8f8f8', type: 'dislike' },\n        { name: '举报', icon: 'report', bgColor: '#f8f8f8', type: 'report' }\n      ];\n      \n      // 如果是作者本人或圈主，添加编辑和删除功能\n      if (isOwner) {\n        const editText = isCircle ? '编辑圈子' : '编辑';\n        const deleteText = isCircle ? '删除圈子' : '删除';\n        \n        items.splice(-1, 0, \n          { name: editText, icon: 'edit', bgColor: '#f8f8f8', type: 'edit' },\n          { name: deleteText, icon: 'delete', bgColor: '#f8f8f8', type: 'delete' }\n        );\n      }\n      \n      return items;\n      } else {\n        // 商品分享时的底部操作项可以为空或添加其他操作\n        return [];\n      }\n    },\n    \n    // 是否显示复制口令按钮\n    showCopyCommand() {\n      // 如果有现成的口令，直接显示\n      if (this.shareData.command_word) {\n        return true;\n      }\n      \n      // 如果有分享数据，可以生成口令\n      if (this.shareData.title || this.shareData.link || this.noteInfo.id) {\n        return true;\n      }\n      \n      return false;\n    }\n  },\n  mounted() {\n    this.initClipboard();\n    this.checkWeixinStatus();\n  },\n  methods: {\n    close() {\n      this.$emit('close');\n    },\n    \n    // 初始化剪贴板功能\n    initClipboard() {\n      // #ifdef H5\n      // 不再使用clipboard.js，直接在copyCommand方法中处理复制\n      // 这个方法现在主要用于初始化其他功能\n      // #endif\n    },\n    \n    // 检查微信状态\n    checkWeixinStatus() {\n      // #ifdef H5\n      if (this.$wechat && this.$wechat.isWeixin()) {\n        this.weixinStatus = true;\n        this.showWeixinButton = true;\n      }\n      // #endif\n    },\n    \n    // 获取图标源，确保始终有图标显示\n    getIconSrc(icon) {\n      if (icon.startsWith('/')) {\n        // 如果是完整路径，尝试直接使用\n        return icon;\n      } else {\n        // 否则使用默认图标映射\n        return this.defaultIcons[icon] || '/static/img/avatar.png';\n      }\n    },\n    \n    // 处理分享给特定人员\n    handleShareToPerson(person) {\n      console.log('分享给:', person.name);\n      uni.showToast({\n        title: `分享给${person.name}`,\n        icon: 'none'\n      });\n      this.$emit('share-to-person', person);\n    },\n    \n    // 分享到微信好友 (H5)\n    shareToWeixin() {\n      // #ifdef H5\n      if (this.weixinStatus) {\n        this.H5ShareBox = true;\n        this.shareToWeixinSDK();\n      } else {\n        uni.showToast({\n          title: '请在微信中打开',\n          icon: 'none'\n        });\n      }\n      // #endif\n    },\n    \n    // 小程序分享好友\n    shareToMPFriend() {\n      console.log('小程序分享好友');\n      this.close();\n    },\n    \n    // APP分享\n    appShare(scene) {\n      // #ifdef APP-PLUS\n      const shareObj = {\n        provider: \"weixin\",\n        scene: scene, // WXSceneSession: 分享到聊天界面, WXSenceTimeline: 分享到朋友圈\n        type: 0,\n        href: this.shareData.link || location.href,\n        title: this.shareData.title || '默认标题',\n        summary: this.shareData.description || '默认描述',\n        imageUrl: this.shareData.image || '/static/img/avatar.png',\n        success: (res) => {\n          console.log(\"success:\" + JSON.stringify(res));\n          uni.showToast({\n            title: '分享成功',\n            icon: 'success'\n          });\n          this.close();\n        },\n        fail: (err) => {\n          console.log(\"fail:\" + JSON.stringify(err));\n      uni.showToast({\n            title: '分享失败',\n        icon: 'none'\n      });\n        }\n      };\n      uni.share(shareObj);\n      // #endif\n    },\n    \n    // 微信SDK分享\n    shareToWeixinSDK() {\n      // #ifdef H5\n      if (this.$wechat && this.$wechat.isWeixin()) {\n        const configAppMessage = {\n          desc: this.shareData.description || '默认描述',\n          title: this.shareData.title || '默认标题',\n          link: this.shareData.link || location.href,\n          imgUrl: this.shareData.image || '/static/img/avatar.png'\n        };\n        this.$wechat\n          .wechatEvevt(['updateAppMessageShareData', 'updateTimelineShareData', 'onMenuShareAppMessage', 'onMenuShareTimeline'], configAppMessage)\n          .then((res) => {\n            console.log('微信分享配置成功', res);\n          })\n          .catch((err) => {\n            console.log('微信分享配置失败', err);\n          });\n      }\n      // #endif\n    },\n    \n    // 生成口令\n    generateCommandWord() {\n      if (this.shareData.command_word) {\n        return this.shareData.command_word;\n      }\n      \n      // 根据分享类型生成不同的口令\n      let commandWord = '';\n      if (this.shareType === 'note') {\n        const title = this.noteInfo.title || this.shareData.title || '精彩内容';\n        const id = this.noteInfo.id || '';\n        commandWord = `#${title}# 快来看看这个精彩内容！复制这条消息，打开APP查看详情。ID:${id}`;\n      } else if (this.shareType === 'goods') {\n        const title = this.shareData.title || '商品分享';\n        const link = this.shareData.link || '';\n        commandWord = `#${title}# 发现好物推荐！复制这条消息，打开APP查看详情。${link}`;\n      } else {\n        const title = this.shareData.title || '精彩内容';\n        commandWord = `#${title}# 快来看看这个精彩内容！`;\n      }\n      \n      return commandWord;\n    },\n    \n    // 复制口令\n    copyCommand() {\n      const commandWord = this.shareData.command_word || this.generateCommandWord();\n      \n      // #ifdef H5\n      // H5环境下如果支持 clipboard.js，会自动处理\n      // 这里作为备用方案\n      if (navigator.clipboard) {\n        navigator.clipboard.writeText(commandWord).then(() => {\n          uni.showToast({\n            title: '口令复制成功',\n            icon: 'success'\n          });\n          this.close();\n        }).catch(() => {\n          // 使用传统方法\n          this.fallbackCopy(commandWord);\n        });\n      } else {\n        this.fallbackCopy(commandWord);\n      }\n      // #endif\n      \n      // #ifndef H5\n      uni.setClipboardData({\n        data: commandWord,\n        success: () => {\n          uni.showToast({\n            title: '口令复制成功',\n            icon: 'success'\n          });\n          this.close();\n        },\n        fail: () => {\n          uni.showToast({\n            title: '复制失败',\n            icon: 'none'\n          });\n        }\n      });\n      // #endif\n    },\n    \n    // H5备用复制方法\n    fallbackCopy(text) {\n      // #ifdef H5\n      const textArea = document.createElement('textarea');\n      textArea.value = text;\n      document.body.appendChild(textArea);\n      textArea.select();\n      try {\n        const successful = document.execCommand('copy');\n        if (successful) {\n          uni.showToast({\n            title: '口令复制成功',\n            icon: 'success'\n          });\n          this.close();\n        } else {\n          uni.showToast({\n            title: '复制失败',\n            icon: 'none'\n          });\n        }\n      } catch (err) {\n        uni.showToast({\n          title: '复制失败',\n          icon: 'none'\n        });\n      }\n      document.body.removeChild(textArea);\n      // #endif\n    },\n    \n    // 生成海报\n    generatePoster() {\n      console.log('生成海报');\n      this.canvasStatus = true;\n      this.close();\n      \n      // 模拟海报生成过程\n      setTimeout(() => {\n        this.posterImage = this.shareData.image || '/static/img/avatar.png';\n        this.posterImageStatus = true;\n        this.canvasStatus = false;\n      }, 2000);\n      \n      this.$emit('generate-poster', this.shareData);\n    },\n    \n    // 关闭海报\n    posterImageClose() {\n      this.posterImageStatus = false;\n    },\n    \n    // 保存海报到手机\n    savePosterPath() {\n      // #ifdef MP || APP-PLUS\n      uni.getSetting({\n        success: (res) => {\n          if (!res.authSetting['scope.writePhotosAlbum']) {\n            uni.authorize({\n              scope: 'scope.writePhotosAlbum',\n              success: () => {\n                this.saveImage();\n              },\n              fail: () => {\n                uni.showToast({\n                  title: '需要授权保存相册',\n                  icon: 'none'\n                });\n              }\n            });\n          } else {\n            this.saveImage();\n          }\n        }\n      });\n      // #endif\n    },\n    \n    // 保存图片\n    saveImage() {\n      uni.saveImageToPhotosAlbum({\n        filePath: this.posterImage,\n        success: () => {\n          this.posterImageClose();\n          uni.showToast({\n            title: '保存成功',\n            icon: 'success'\n          });\n        },\n        fail: () => {\n          uni.showToast({\n            title: '保存失败',\n            icon: 'none'\n          });\n        }\n      });\n    },\n    \n    // 复制链接\n    copyLink() {\n      let shareUrl;\n      if (this.shareType === 'note') {\n      if (this.noteInfo.type === 'circle') {\n        shareUrl = this.noteInfo.share_url || `/pages/note/circle?id=${this.noteInfo.id}`;\n      } else {\n        shareUrl = `/pages/note/details?id=${this.noteInfo.id}`;\n        }\n      } else {\n        shareUrl = this.shareData.link || location.href;\n      }\n      \n      uni.setClipboardData({\n        data: shareUrl,\n        success: () => {\n          uni.showToast({ title: '链接已复制', icon: 'success' });\n          this.close();\n        },\n        fail: () => {\n          uni.showToast({ title: '复制失败', icon: 'none' });\n        }\n      });\n    },\n    \n    handleAction(item) {\n      // 处理功能操作逻辑\n      console.log('执行操作:', item.name, item.type);\n      \n      switch(item.type) {\n        case 'dislike':\n          this.markDislike();\n          break;\n        case 'report':\n          this.showReportModal();\n          break;\n        case 'edit':\n          this.editNote();\n          break;\n        case 'delete':\n          this.deleteNote();\n          break;\n      }\n    },\n    \n    // 标记不感兴趣\n    markDislike() {\n      const isCircle = this.noteInfo.type === 'circle';\n      const content = isCircle ? '确定标记该圈子为不感兴趣吗？' : '确定标记为不感兴趣吗？';\n      const successText = isCircle ? '已标记该圈子为不感兴趣' : '已标记为不感兴趣';\n      \n      uni.showModal({\n        title: '确认操作',\n        content: content,\n        success: (res) => {\n          if (res.confirm) {\n            uni.showToast({ title: successText, icon: 'success' });\n            this.close();\n            this.$emit('dislike', this.noteInfo.id);\n          }\n        }\n      });\n    },\n    \n    // 显示举报弹窗\n    showReportModal() {\n      uni.showActionSheet({\n        itemList: this.reportReasons,\n        success: (res) => {\n          const reason = this.reportReasons[res.tapIndex];\n          this.reportNote(reason);\n        }\n      });\n    },\n    \n    // 举报笔记\n    reportNote(reason) {\n      console.log('举报笔记:', reason);\n      uni.showLoading({ title: '举报中...', mask: true });\n      \n      // 发送举报事件给父组件处理\n      this.$emit('report', {\n        noteId: this.noteInfo.id,\n        reason: reason,\n        noteInfo: this.noteInfo\n      });\n      \n      setTimeout(() => {\n        uni.hideLoading();\n        uni.showToast({ title: '举报成功', icon: 'success' });\n        this.close();\n      }, 1000);\n    },\n    \n    // 编辑笔记\n    editNote() {\n      this.close();\n      this.$emit('edit', this.noteInfo.id);\n    },\n    \n    // 删除笔记或圈子\n    deleteNote() {\n      const isCircle = this.noteInfo.type === 'circle';\n      const content = isCircle ? '确认要永久删除这个圈子吗？删除后圈子内的所有内容都将清空。' : '确认要永久删除这篇笔记吗？';\n      \n      uni.showModal({\n        title: '确认删除',\n        content: content,\n        confirmColor: '#FA5150',\n        success: (res) => {\n          if (res.confirm) {\n            this.close();\n            this.$emit('delete', this.noteInfo.id);\n          }\n        }\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.share-container {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 999;\n}\n\n.share-mask {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  background-color: rgba(0, 0, 0, 0.6);\n}\n\n.share-content {\n  position: absolute;\n  left: 0;\n  bottom: 0;\n  width: 100%;\n  background-color: #ffffff;\n  border-top-left-radius: 20rpx;\n  border-top-right-radius: 20rpx;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);\n}\n\n.share-header {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n  height: 90rpx;\n  border-bottom: 1rpx solid #f2f2f2;\n}\n\n.share-title {\n  font-size: 32rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.popup-close {\n  position: absolute;\n  right: 30rpx;\n  top: 50%;\n  transform: translateY(-50%) rotate(45deg);\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 50%;\n  background: #f8f8f8;\n  justify-content: center;\n}\n\n/* 分割线样式 */\n.divider-line {\n  height: 1px;\n  background-color: #eeeeee;\n  width: 100%;\n}\n\n/* 人员列表样式 */\n.person-scroll {\n  width: 100%;\n  white-space: nowrap;\n  padding: 30rpx 0;\n  background-color: #ffffff;\n}\n\n.person-list {\n  width: 100%;\n  display: flex;\n  padding: 0 10rpx;\n}\n\n.person-item {\n  flex-shrink: 0;\n}\n\n.person-avatar {\n  margin: 0 20rpx;\n  width: 116rpx;\n  height: 116rpx;\n  border-radius: 50%;\n  background: #f8f8f8;\n  border: 2rpx solid #f5f5f5;\n  position: relative;\n}\n\n.person-avatar image {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n}\n\n.person-online-dot {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  width: 24rpx;\n  height: 24rpx;\n  border-radius: 50%;\n  border: 6rpx solid #fff;\n  z-index: 10;\n}\n\n.person-name {\n  margin: 20rpx 0 10rpx;\n  width: 160rpx;\n  color: #000;\n  font-weight: 500;\n  font-size: 24rpx;\n  line-height: 24rpx;\n  text-align: center;\n}\n\n.person-tips {\n  width: 160rpx;\n  color: #999;\n  font-size: 18rpx;\n  line-height: 18rpx;\n  font-weight: 300;\n  text-align: center;\n}\n\n.ohto {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n/* 分享选项滚动样式 */\n.share-scroll {\n  width: 100%;\n  white-space: nowrap;\n  padding: 30rpx 0;\n  background-color: #ffffff;\n}\n\n.share-list {\n  display: flex;\n  padding: 0 10rpx;\n}\n\n.share-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin: 0 20rpx;\n  background: none;\n  border: none;\n  padding: 0;\n  font-size: inherit;\n  color: inherit;\n}\n\n.share-button {\n  background: none !important;\n  border: none !important;\n  padding: 0 !important;\n  margin: 0 20rpx !important;\n  display: flex !important;\n  flex-direction: column !important;\n  align-items: center !important;\n}\n\n.share-button::after {\n  border: none !important;\n}\n\n.share-icon-bg {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10rpx;\n}\n\n.bottom-icon {\n  background-color: #f8f8f8 !important;\n}\n\n.delete-item {\n  background-color: #fff2f2 !important;\n}\n\n.share-icon {\n  width: 50rpx;\n  height: 50rpx;\n}\n\n.share-text {\n  font-size: 24rpx;\n  color: #333;\n  text-align: center;\n  width: 120rpx;\n  margin-top: 8rpx;\n}\n\n.delete-text {\n  color: #ff4757 !important;\n}\n\n.safe-bottom {\n  height: 34rpx;\n  height: calc(34rpx + env(safe-area-inset-bottom));\n}\n\n/* 海报相关样式 */\n.poster-popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(0, 0, 0, 0.8);\n}\n\n.poster-popup .close {\n  width: 60rpx;\n  height: 60rpx;\n  margin-bottom: 20rpx;\n}\n\n.poster-img {\n  max-width: 80%;\n  max-height: 70%;\n  border-radius: 10rpx;\n}\n\n.save-poster {\n  margin-top: 40rpx;\n  padding: 20rpx 40rpx;\n  background-color: #007aff;\n  color: white;\n  border-radius: 50rpx;\n  font-size: 28rpx;\n}\n\n.keep {\n  margin-top: 40rpx;\n  color: white;\n  font-size: 28rpx;\n  text-align: center;\n}\n\n.canvas {\n  position: fixed;\n  top: -9999rpx;\n  left: -9999rpx;\n  width: 750rpx;\n  height: 1334rpx;\n}\n\n.mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 299;\n  background-color: rgba(0, 0, 0, 0.6);\n}\n\n/* H5分享图片 */\n.share-box {\n  z-index: 1000;\n  position: fixed;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n}\n\n.share-box image {\n  width: 100%;\n  height: 100%;\n}\n</style>", "import Component from 'D:/uniapp/vue3/components/share/index.vue'\nwx.createComponent(Component)"], "names": ["appConfig", "uni"], "mappings": ";;;;AA6IA,MAAM,EAAE,qBAAqBA;AAM7B,MAAK,YAAU;AAAA,EACb,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS,OAAO;AAAA,QACd,OAAO;AAAA,QACP,aAAa;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,cAAc;AAAA;IAEjB;AAAA;AAAA,IAED,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS,OAAO,CAAA;AAAA,IACjB;AAAA;AAAA,IAED,QAAQ;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACV;AAAA;AAAA,IAED,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,SAAS;AAAA;AAAA,MAET,YAAY;AAAA,QACV,EAAE,IAAI,GAAG,MAAM,OAAO,QAAQ,0BAA0B,QAAQ,KAAM;AAAA,QACtE,EAAE,IAAI,GAAG,MAAM,OAAO,QAAQ,0BAA0B,QAAQ,MAAO;AAAA,QACvE,EAAE,IAAI,GAAG,MAAM,OAAO,QAAQ,0BAA0B,QAAQ,KAAM;AAAA,QACtE,EAAE,IAAI,GAAG,MAAM,OAAO,QAAQ,0BAA0B,QAAQ,MAAO;AAAA,QACvE,EAAE,IAAI,GAAG,MAAM,OAAO,QAAQ,0BAA0B,QAAQ,KAAM;AAAA,QACtE,EAAE,IAAI,GAAG,MAAM,OAAO,QAAQ,0BAA0B,QAAQ,KAAM;AAAA,QACtE,EAAE,IAAI,GAAG,MAAM,OAAO,QAAQ,0BAA0B,QAAQ,MAAO;AAAA,QACvE,EAAE,IAAI,GAAG,MAAM,OAAO,QAAQ,0BAA0B,QAAQ,KAAM;AAAA,QACtE,EAAE,IAAI,GAAG,MAAM,OAAO,QAAQ,0BAA0B,QAAQ,MAAO;AAAA,QACvE,EAAE,IAAI,IAAI,MAAM,QAAQ,QAAQ,0BAA0B,QAAQ,KAAK;AAAA,MACxE;AAAA;AAAA,MAED,aAAa,CAAE;AAAA;AAAA,MAEf,cAAc;AAAA,QACZ,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA;AAAA,MAED,eAAe,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,IAAI;AAAA;AAAA,MAGpE,mBAAmB;AAAA,MACnB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,YAAY;AAAA;AAAA,MAGZ,cAAc;AAAA,MACd,kBAAkB;AAAA;EAErB;AAAA,EACD,UAAU;AAAA;AAAA,IAER,sBAAsB;AACpB,UAAI,KAAK,cAAc,QAAQ;AAC/B,cAAM,WAAW,KAAK,SAAS,SAAS;AACxC,cAAM,UAAU,KAAK,SAAS,OAAO,KAAK,SAAS,OAAO,KAAK;AAE/D,cAAM,QAAQ;AAAA,UACZ,EAAE,MAAM,OAAO,MAAM,WAAW,SAAS,WAAW,MAAM,UAAW;AAAA,UACrE,EAAE,MAAM,MAAM,MAAM,UAAU,SAAS,WAAW,MAAM,SAAS;AAAA;AAInE,YAAI,SAAS;AACX,gBAAM,WAAW,WAAW,SAAS;AACrC,gBAAM,aAAa,WAAW,SAAS;AAEvC,gBAAM;AAAA,YAAO;AAAA,YAAI;AAAA,YACf,EAAE,MAAM,UAAU,MAAM,QAAQ,SAAS,WAAW,MAAM,OAAQ;AAAA,YAClE,EAAE,MAAM,YAAY,MAAM,UAAU,SAAS,WAAW,MAAM,SAAS;AAAA;QAE3E;AAEA,eAAO;AAAA,aACA;AAEL,eAAO;MACT;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AAEhB,UAAI,KAAK,UAAU,cAAc;AAC/B,eAAO;AAAA,MACT;AAGA,UAAI,KAAK,UAAU,SAAS,KAAK,UAAU,QAAQ,KAAK,SAAS,IAAI;AACnE,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAAA,EACD;AAAA,EACD,UAAU;AACR,SAAK,cAAa;AAClB,SAAK,kBAAiB;AAAA,EACvB;AAAA,EACD,SAAS;AAAA,IACP,QAAQ;AACN,WAAK,MAAM,OAAO;AAAA,IACnB;AAAA;AAAA,IAGD,gBAAgB;AAAA,IAKf;AAAA;AAAA,IAGD,oBAAoB;AAAA,IAOnB;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,UAAI,KAAK,WAAW,GAAG,GAAG;AAExB,eAAO;AAAA,aACF;AAEL,eAAO,KAAK,aAAa,IAAI,KAAK;AAAA,MACpC;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB,QAAQ;AAC1BC,oBAAY,MAAA,MAAA,OAAA,qCAAA,QAAQ,OAAO,IAAI;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,MAAM,OAAO,IAAI;AAAA,QACxB,MAAM;AAAA,MACR,CAAC;AACD,WAAK,MAAM,mBAAmB,MAAM;AAAA,IACrC;AAAA;AAAA,IAGD,gBAAgB;AAAA,IAYf;AAAA;AAAA,IAGD,kBAAkB;AAChBA,oBAAAA,wDAAY,SAAS;AACrB,WAAK,MAAK;AAAA,IACX;AAAA;AAAA,IAGD,SAAS,OAAO;AAAA,IA4Bf;AAAA;AAAA,IAGD,mBAAmB;AAAA,IAmBlB;AAAA;AAAA,IAGD,sBAAsB;AACpB,UAAI,KAAK,UAAU,cAAc;AAC/B,eAAO,KAAK,UAAU;AAAA,MACxB;AAGA,UAAI,cAAc;AAClB,UAAI,KAAK,cAAc,QAAQ;AAC7B,cAAM,QAAQ,KAAK,SAAS,SAAS,KAAK,UAAU,SAAS;AAC7D,cAAM,KAAK,KAAK,SAAS,MAAM;AAC/B,sBAAc,IAAI,KAAK,oCAAoC,EAAE;AAAA,MAC/D,WAAW,KAAK,cAAc,SAAS;AACrC,cAAM,QAAQ,KAAK,UAAU,SAAS;AACtC,cAAM,OAAO,KAAK,UAAU,QAAQ;AACpC,sBAAc,IAAI,KAAK,6BAA6B,IAAI;AAAA,aACnD;AACL,cAAM,QAAQ,KAAK,UAAU,SAAS;AACtC,sBAAc,IAAI,KAAK;AAAA,MACzB;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,cAAc;AACZ,YAAM,cAAc,KAAK,UAAU,gBAAgB,KAAK;AAsBxDA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD,eAAK,MAAK;AAAA,QACX;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IAEF;AAAA;AAAA,IAGD,aAAa,MAAM;AAAA,IA4BlB;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAA,MAAA,OAAA,qCAAY,MAAM;AAClB,WAAK,eAAe;AACpB,WAAK,MAAK;AAGV,iBAAW,MAAM;AACf,aAAK,cAAc,KAAK,UAAU,SAAS;AAC3C,aAAK,oBAAoB;AACzB,aAAK,eAAe;AAAA,MACrB,GAAE,GAAI;AAEP,WAAK,MAAM,mBAAmB,KAAK,SAAS;AAAA,IAC7C;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,oBAAoB;AAAA,IAC1B;AAAA;AAAA,IAGD,iBAAiB;AAEfA,oBAAAA,MAAI,WAAW;AAAA,QACb,SAAS,CAAC,QAAQ;AAChB,cAAI,CAAC,IAAI,YAAY,wBAAwB,GAAG;AAC9CA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,SAAS,MAAM;AACb,qBAAK,UAAS;AAAA,cACf;AAAA,cACD,MAAM,MAAM;AACVA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AAAA,iBACI;AACL,iBAAK,UAAS;AAAA,UAChB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IAEF;AAAA;AAAA,IAGD,YAAY;AACVA,oBAAAA,MAAI,uBAAuB;AAAA,QACzB,UAAU,KAAK;AAAA,QACf,SAAS,MAAM;AACb,eAAK,iBAAgB;AACrBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACF;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,WAAW;AACT,UAAI;AACJ,UAAI,KAAK,cAAc,QAAQ;AAC/B,YAAI,KAAK,SAAS,SAAS,UAAU;AACnC,qBAAW,KAAK,SAAS,aAAa,yBAAyB,KAAK,SAAS,EAAE;AAAA,eAC1E;AACL,qBAAW,0BAA0B,KAAK,SAAS,EAAE;AAAA,QACrD;AAAA,aACK;AACL,mBAAW,KAAK,UAAU,QAAQ,SAAS;AAAA,MAC7C;AAEAA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM;AAAA,QACN,SAAS,MAAM;AACbA,wBAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,UAAQ,CAAG;AACjD,eAAK,MAAK;AAAA,QACX;AAAA,QACD,MAAM,MAAM;AACVA,wBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAK,CAAG;AAAA,QAC/C;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAED,aAAa,MAAM;AAEjBA,4EAAY,SAAS,KAAK,MAAM,KAAK,IAAI;AAEzC,cAAO,KAAK,MAAI;AAAA,QACd,KAAK;AACH,eAAK,YAAW;AAChB;AAAA,QACF,KAAK;AACH,eAAK,gBAAe;AACpB;AAAA,QACF,KAAK;AACH,eAAK,SAAQ;AACb;AAAA,QACF,KAAK;AACH,eAAK,WAAU;AACf;AAAA,MACJ;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACZ,YAAM,WAAW,KAAK,SAAS,SAAS;AACxC,YAAM,UAAU,WAAW,mBAAmB;AAC9C,YAAM,cAAc,WAAW,gBAAgB;AAE/CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP;AAAA,QACA,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfA,0BAAG,MAAC,UAAU,EAAE,OAAO,aAAa,MAAM,UAAU,CAAC;AACrD,iBAAK,MAAK;AACV,iBAAK,MAAM,WAAW,KAAK,SAAS,EAAE;AAAA,UACxC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB;AAChBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,KAAK;AAAA,QACf,SAAS,CAAC,QAAQ;AAChB,gBAAM,SAAS,KAAK,cAAc,IAAI,QAAQ;AAC9C,eAAK,WAAW,MAAM;AAAA,QACxB;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,QAAQ;AACjBA,oBAAY,MAAA,MAAA,OAAA,qCAAA,SAAS,MAAM;AAC3BA,oBAAG,MAAC,YAAY,EAAE,OAAO,UAAU,MAAM,KAAG,CAAG;AAG/C,WAAK,MAAM,UAAU;AAAA,QACnB,QAAQ,KAAK,SAAS;AAAA,QACtB;AAAA,QACA,UAAU,KAAK;AAAA,MACjB,CAAC;AAED,iBAAW,MAAM;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,UAAQ,CAAG;AAChD,aAAK,MAAK;AAAA,MACX,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,WAAW;AACT,WAAK,MAAK;AACV,WAAK,MAAM,QAAQ,KAAK,SAAS,EAAE;AAAA,IACpC;AAAA;AAAA,IAGD,aAAa;AACX,YAAM,WAAW,KAAK,SAAS,SAAS;AACxC,YAAM,UAAU,WAAW,kCAAkC;AAE7DA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP;AAAA,QACA,cAAc;AAAA,QACd,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,iBAAK,MAAK;AACV,iBAAK,MAAM,UAAU,KAAK,SAAS,EAAE;AAAA,UACvC;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtqBA,GAAG,gBAAgB,SAAS;"}