
.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


/* 小程序兼容：将SCSS嵌套语法改为普通CSS */
.jsfun-record .mask[data-v-2d68ff8f] {
	position: fixed;
	z-index: 1000;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
}
.jsfun-record .record-container[data-v-2d68ff8f] {
	position: fixed;
	z-index: 1001;
	right: 0;
	left: 0;
	bottom: 0;
	background: #fff;
	text-align: center;
	transition: transform 0.3s ease;
	transform: translateY(100%);
}
.jsfun-record .record-container.show[data-v-2d68ff8f] {
	transform: translateY(0);
}
.jsfun-record .time-display[data-v-2d68ff8f] {
	font-size: 1.875rem;
	color: #000;
	line-height: 3.125rem;
	margin-top: 1.5625rem;
	font-weight: 500;
}
.jsfun-record .time-hint[data-v-2d68ff8f] {
	color: #999;
	font-size: 0.875rem;
	margin-bottom: 0.9375rem;
}
.jsfun-record .record-box[data-v-2d68ff8f] {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0.625rem 0;
}
.jsfun-record .record-canvas[data-v-2d68ff8f] {
	position: relative;
	width: 6.25rem;
	height: 6.25rem;
	margin: 0 1.875rem;
	z-index: 10;
}
.jsfun-record .record-canvas .record-indicator[data-v-2d68ff8f] {
	position: absolute;
	top: 0.625rem;
	left: 0.625rem;
	width: 5rem;
	height: 5rem;
	border: 1px dashed #fe3b54;
	border-radius: 50%;
	background: #fe3b54 url(../../static/jc-record/recording.png) no-repeat center;
	background-size: 50%;
	z-index: 100;
	transition: all 0.2s ease;
}
.jsfun-record .record-canvas .record-indicator.recording[data-v-2d68ff8f] {
	transform: scale(1.05);
	box-shadow: 0 0 0.625rem rgba(254, 59, 84, 0.3);
}
.jsfun-record .control-btn[data-v-2d68ff8f] {
	width: 2.5rem;
	height: 2.5rem;
	border-radius: 50%;
	background-size: 100%;
	cursor: pointer;
	transition: transform 0.2s ease;
}
.jsfun-record .control-btn[data-v-2d68ff8f]:active {
	transform: scale(0.95);
}
.jsfun-record .stop-btn[data-v-2d68ff8f] {
	background-image: url(../../static/jc-record/stop.png);
}
.jsfun-record .play-btn[data-v-2d68ff8f] {
	background-image: url(../../static/jc-record/play.png);
}
.jsfun-record .confirm-btn[data-v-2d68ff8f] {
	background-image: url(../../static/jc-record/confirm.png);
}
.jsfun-record .instruction-text[data-v-2d68ff8f] {
	color: #666;
	font-size: 1rem;
	margin-bottom: 1.5625rem;
	padding-bottom: env(safe-area-inset-bottom);
}

.emoji-panel[data-v-71424074] {
  width: 100%;
  height: 12.5rem;
  background-color: #fff;
  border-top: 0.03125rem solid #f1f1f1;
  z-index: 999;
  position: relative;
}
.emoji-scroll[data-v-71424074] {
  height: 100%;
}
.emoji-container[data-v-71424074] {
  padding: 0.625rem;
}
.recent-section[data-v-71424074] {
  margin-bottom: 0.625rem;
}
.section-title[data-v-71424074] {
  font-size: 0.75rem;
  color: #999;
  margin-bottom: 0.46875rem;
  padding-left: 0.3125rem;
}
.recent-emojis[data-v-71424074] {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3125rem;
}
.section-divider[data-v-71424074] {
  height: 0.03125rem;
  background-color: #f1f1f1;
  margin: 0.625rem 0;
}
.all-emojis[data-v-71424074] {
  display: flex;
  flex-wrap: wrap;
  gap: 0.15625rem;
}
.emoji-item[data-v-71424074] {
  width: 2.1875rem;
  height: 2.1875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}
.emoji-item[data-v-71424074]:active {
  background-color: #f5f5f5;
}
.emoji-image[data-v-71424074] {
  width: 1.5625rem;
  height: 1.5625rem;
  object-fit: contain;
}

/* 预览弹窗样式 */
.emoji-preview[data-v-71424074] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn-71424074 0.3s ease;
}
.preview-content[data-v-71424074] {
  background-color: #fff;
  border-radius: 0.625rem;
  padding: 1.875rem 1.25rem 1.25rem;
  margin: 1.25rem;
  max-width: 15.625rem;
  text-align: center;
  animation: slideUp-71424074 0.3s ease;
}
.preview-image[data-v-71424074] {
  width: 3.75rem;
  height: 3.75rem;
  margin-bottom: 0.625rem;
}
.preview-name[data-v-71424074] {
  font-size: 1rem;
  color: #333;
  margin-bottom: 1.25rem;
}
.preview-actions[data-v-71424074] {
  display: flex;
  gap: 0.625rem;
  justify-content: center;
}

/* 小程序兼容：将SCSS嵌套语法改为普通CSS */
.preview-btn[data-v-71424074] {
  padding: 0.625rem 1.25rem;
  border-radius: 1.5625rem;
  font-size: 0.875rem;
  text-align: center;
  transition: all 0.2s ease;
}
.preview-btn[data-v-71424074]:not(.secondary) {
  background-color: #ff4d6a;
  color: #fff;
}
.preview-btn[data-v-71424074]:not(.secondary):active {
  background-color: #e6445e;
}
.preview-btn.secondary[data-v-71424074] {
  background-color: #f5f5f5;
  color: #666;
}
.preview-btn.secondary[data-v-71424074]:active {
  background-color: #e8e8e8;
}

/* 动画 */
@keyframes fadeIn-71424074 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes slideUp-71424074 {
from {
    opacity: 0;
    transform: translateY(1.5625rem);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
@media (prefers-color-scheme: dark) {
.emoji-panel[data-v-71424074] {
    background-color: #1a1a1a;
    border-top-color: #333;
}
.section-title[data-v-71424074] {
    color: #666;
}
.section-divider[data-v-71424074] {
    background-color: #333;
}
.emoji-item[data-v-71424074]:active {
    background-color: #333;
}
.preview-content[data-v-71424074] {
    background-color: #333;
}
.preview-name[data-v-71424074] {
    color: #fff;
}
}

/* 基础样式 - 参考 details.vue 的 empty-box 样式 */
.empty-page[data-v-5cea664a] {
  width: 100%;
  padding: 3.125rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.empty-content[data-v-5cea664a] {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

/* 图片样式 - 参考 details.vue */
.empty-image[data-v-5cea664a] {
  width: 9.375rem;
  height: 9.375rem;
  margin-bottom: 0.9375rem;
  opacity: 0.8;
}

/* 标题样式 - 参考 details.vue 的 .e1 */
.empty-title[data-v-5cea664a] {
  font-size: 0.9375rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.3125rem;
  line-height: 1.4;
}

/* 描述样式 - 参考 details.vue 的 .e2 */
.empty-description[data-v-5cea664a] {
  margin-top: 0.3125rem;
  color: #999;
  font-size: 0.8125rem;
  line-height: 1.5;
  margin-bottom: 1.25rem;
  max-width: 15.625rem;
  text-align: center;
}

/* 按钮样式 - 参考 details.vue 的 retry-btn */
.empty-button[data-v-5cea664a] {
  margin-top: 1.25rem;
  width: 6.25rem;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 700;
  color: #fff;
  background: #007aff;
  border-radius: 1.25rem;
  transition: all 0.3s ease;
}
.empty-button[data-v-5cea664a]:active {
  background: #0056cc;
  transform: scale(0.95);
}

/* 工具类 */
.df[data-v-5cea664a] {
  display: flex;
  align-items: center;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
.empty-title[data-v-5cea664a] {
    color: #fff;
}
.empty-description[data-v-5cea664a] {
    color: #ccc;
}
.empty-page[data-v-5cea664a] {
    background-color: #1a1a1a;
}
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.empty-image[data-v-5cea664a] {
    width: 7.8125rem;
    height: 7.8125rem;
}
.empty-title[data-v-5cea664a] {
    font-size: 0.875rem;
}
.empty-description[data-v-5cea664a] {
    font-size: 0.75rem;
    padding: 0 1.25rem;
}
}

/* 小程序兼容性 */






/* H5 兼容性 */








/* 投票展示样式 */
.vote-box[data-v-eb14d672] {
  width: 100%;
  margin-top: 0.5rem;
}
.vote-container[data-v-eb14d672] {
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 0.5rem;
  position: relative;
}
.vote-header[data-v-eb14d672] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 0.625rem;
}
.vote-title-container[data-v-eb14d672] {
  display: flex;
  align-items: center;
  flex: 1;
}
.vote-icon[data-v-eb14d672] {
  width: 1rem;
  height: 1rem;
  margin-right: 0.3125rem;
}
.vote-title[data-v-eb14d672] {
  font-size: 0.875rem;
  font-weight: 500;
  color: #333;
  text-align: left;
  white-space: normal;
  word-break: break-word;
}
.vote-delete[data-v-eb14d672] {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.3125rem;
  position: relative;
  z-index: 10;
  cursor: pointer;
  transition: background 0.2s;
}
.vote-delete[data-v-eb14d672]:hover {
  background: #e8e8e8;
}
.vote-delete[data-v-eb14d672]:active {
  transform: scale(0.95);
}
.vote-options[data-v-eb14d672] {
  display: flex;
  flex-direction: column;
  padding: 0 0;
}
.vote-option-unvoted[data-v-eb14d672] {
  background: #fff;
  border-radius: 1rem;
  font-size: 0.8125rem;
  color: #333;
  margin-bottom: 0.5625rem;
  border: none;
  box-shadow: none;
  text-align: center;
  transition: background 0.2s, border 0.2s;
  margin-left: 0.625rem;
  margin-right: 0.625rem;
  position: relative;
  min-height: 2.0625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.625rem;
  box-sizing: border-box;
}
.vote-row[data-v-eb14d672] {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.625rem;
}
.vote-left[data-v-eb14d672] {
  display: flex;
  align-items: center;
}
.vote-bar-bg[data-v-eb14d672] {
  border-radius: 18px;
  font-size: 17px;
  color: #333;
  margin-bottom: 10px;
  border: none;
  box-shadow: none;
  text-align: center;
  transition: background 0.2s, border 0.2s;
  margin-left: 11px;
  margin-right: 11px;
  min-height: 27px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 11px;
  box-sizing: border-box;
}
.vote-bar[data-v-eb14d672] {
  height: 100%;
  border-radius: 1rem;
  transition: width 0.3s;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}
.vote-checked-icon[data-v-eb14d672] {
  width: 0.875rem;
  height: 0.875rem;
  background: #ffd600;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.3125rem;
}
.vote-checked-icon uni-image[data-v-eb14d672] {
  width: 0.625rem;
  height: 0.625rem;
  display: block;
}
.vote-content[data-v-eb14d672] {
  font-size: 0.8125rem;
  color: #333;
  text-align: left;
  white-space: normal;
  word-break: break-word;
  font-weight: 500;
}
.vote-percent[data-v-eb14d672] {
  font-size: 0.8125rem;
  color: #000000;
  text-align: right;
  margin-left: 0.375rem;
  min-width: 1.5rem;
  flex-shrink: 0;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: 500;
  margin-right: 1.25rem;
  position: relative;
}
.vote-people[data-v-eb14d672] {
  margin-top: 0.5rem;
  color: #999;
  font-size: 0.75rem;
  text-align: left;
  padding-left: 0.625rem;
  padding-bottom: 0.625rem;
}


.container {
  padding-bottom: 4.375rem; /* 为固定工具栏留出空间，减少空白 */
}

/* 导航栏样式 - 参考details.vue */
.nav-box{
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  width: 100%;
  background: #fff;
  box-sizing: border-box;
}
.nav-item{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.nav-item .nav-back{
  padding: 0 0.9375rem;
  width: 1.0625rem;
  height: 100%;
  border-radius: 50%;
}
.nav-title {
  flex: 1;
  font-size: 1rem;
  font-weight: 700;
  text-align: center;
  max-width: 12.5rem;
}
.nav-publish {
  padding: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 非小程序环境的默认内边距 */
.nav-publish {
  padding-right: 0.9375rem;
}
.publish-btn {
  position: relative;
  padding: 0 1rem;
  height: 2rem;
  min-width: 3rem;
  border-radius: 1rem;
  background: #000;
  color: #fff;
  font-size: 0.875rem;
  font-weight: 700;
  letter-spacing: 0.03125rem;
  text-align: center;
  line-height: 2rem;
  border: none;
  outline: none;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 0.125rem 0.5rem rgba(0, 0, 0, 0.15),
    0 0.0625rem 0.25rem rgba(0, 0, 0, 0.1);
}

/* 发光效果 */
.publish-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 内部高光 */
.publish-btn::after {
  content: '';
  position: absolute;
  top: 0.0625rem;
  left: 0.0625rem;
  right: 0.0625rem;
  height: 0.9375rem;
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 0.9375rem 0.9375rem 1.875rem 1.875rem;
  opacity: 0.6;
}

/* 点击效果 */
.publish-btn:active {
  transform: translateY(0.0625rem) scale(0.98);
  box-shadow:
    0 0.0625rem 0.25rem rgba(0, 0, 0, 0.2),
    0 0.03125rem 0.125rem rgba(0, 0, 0, 0.15);
}

/* 禁用状态 */
.publish-btn-disabled {
  background: #666;
  color: #999;
  cursor: not-allowed;
  box-shadow:
    0 0.0625rem 0.25rem rgba(0, 0, 0, 0.1),
    0 0.03125rem 0.125rem rgba(0, 0, 0, 0.05);
}
.publish-btn-disabled::before {
  display: none;
}
.publish-btn-disabled::after {
  opacity: 0.3;
}
.publish-btn-disabled:active {
  transform: none;
}

/* 小程序环境适配 */














/* H5环境优化 */























/* APP环境优化 */
.publish-btn {
  background: #000;
  border: 0.03125rem solid rgba(255, 255, 255, 0.08);
}
.publish-btn::before {
  background: linear-gradient(45deg,
    rgba(255, 107, 107, 0.1) 0%,
    rgba(238, 90, 82, 0.05) 50%,
    rgba(255, 107, 107, 0.1) 100%);
}


/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
.publish-btn {
    background: #1a1a1a;
    border: 0.03125rem solid rgba(255, 255, 255, 0.1);
    box-shadow:
      0 0.125rem 0.5rem rgba(0, 0, 0, 0.3),
      0 0.0625rem 0.25rem rgba(0, 0, 0, 0.2);
}
.publish-btn-disabled {
    background: #333;
    color: #666;
}
}
.content-box {
  margin: 0.9375rem;
  width: calc(100% - 1.875rem);
  border-radius: 0.9375rem;
  position: relative;
}
.textarea-container {
  position: relative;
  width: 100%;
}

/* 透明的textarea用于输入 */
.textarea-input {
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  font-size: 0.875rem;
  min-height: 8.75rem;
  line-height: 1.6;
  color: transparent; /* 文字透明，只显示光标 */
  background: transparent;
  position: relative;
  z-index: 2;
}

/* 文本覆盖层 */
.text-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  font-size: 0.875rem;
  min-height: 8.75rem;
  line-height: 1.6;
  pointer-events: none; /* 不阻挡textarea的点击 */
  z-index: 1;
}
.formatted-content {
  width: 100%;
  font-size: 0.875rem;
  line-height: 1.6;
  color: #333;
  word-wrap: break-word;
  word-break: break-all;
}

/* 占位符文本样式 */
.placeholder-text {
  color: #999;
  font-size: 0.875rem;
}

/* 普通文本样式 */
.normal-text {
  color: #333;
  font-size: 0.875rem;
}

/* @用户文本样式 - 蓝色链接样式 */
.mention-text {
  color: #1890ff;
  font-size: 0.875rem;
  font-weight: 500;
}

/* 兼容原有的textarea-item样式 */
.content-box .textarea-item {
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  font-size: 0.875rem;
  min-height: 8.75rem;
}

/* @用户标签样式 */
.mention-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.375rem;
  padding: 0 0.625rem 0.3125rem;
  margin-top: 0.3125rem;
}
.mention-tag {
  display: flex;
  align-items: center;
  background: #f0f9ff;
  border: 1px solid #07c160;
  border-radius: 0.625rem;
  padding: 0.25rem 0.375rem;
}
.mention-tag:active {
  background: #e6f7ff;
}
.mention-avatar {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  margin-right: 0.25rem;
  flex-shrink: 0;
}
.mention-nickname {
  font-size: 0.75rem;
  color: #07c160;
  font-weight: 500;
  margin-right: 0.25rem;
}
.mention-delete {
  font-size: 0.875rem;
  color: #999;
  font-weight: bold;
  width: 0.75rem;
  height: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
}
.content-box .scroll-box {
  margin: 0.625rem 0;
  white-space: nowrap;
  width: 100%;
}
.scroll-box .scroll-item {
  display: flex;
  padding: 0 0.625rem;
}
.scroll-item .del {
  position: absolute;
  z-index: 9;
  top: 0.25rem;
  right: 0.25rem;
  width: 1.1875rem;
  height: 1.1875rem;
  border-radius: 50%;
  justify-content: center;
  transform: rotate(45deg);
  border: 1px solid #fff;
  background: rgba(0, 0, 0, 0.3);
}
.scroll-item .del uni-image {
  width: 0.5625rem;
  height: 0.5625rem;
}
.scroll-item .img-box {
  flex-shrink: 0;
  margin-right: 0.25rem;
  width: 6.125rem;
  height: 6.125rem;
  border-radius: 0.25rem;
  border: 1px solid #f8f8f8;
  justify-content: center;
  position: relative;
  overflow: hidden;
}
.scroll-item .icon {
  color: #999;
  font-size: 1.125rem;
  font-weight: 700;
}
.scroll-item uni-image,
.scroll-item uni-video {
  width: 100%;
  height: 100%;
}
.scroll-item .sort {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 6.125rem;
  height: 1.40625rem;
  background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.376));
  border-radius: 0 0 0.25rem 0.25rem;
}
.scroll-item .sort .sort-item {
  width: 50%;
  justify-content: center;
  font-size: 0.8125rem;
  font-weight: 700;
}
.scroll-item .video-box {
  width: 100%;
  justify-content: space-between;
}
.video-box .video-item {
  width: calc(50% - 0.25rem);
  height: 7.5rem;
  border-radius: 0.25rem;
  position: relative;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.video-box .video-item uni-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 0.25rem;
}
.video-box .video-item .del {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  width: 1.25rem;
  height: 1.25rem;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  z-index: 10;
}
.video-box .video-item .del uni-image {
  width: 0.625rem;
  height: 0.625rem;
}
.scroll-item .audio-box,
.scroll-item .file-audio {
  width: 100%;
  height: 4rem;
  border-radius: 0.25rem;
}
.scroll-item .file-audio {
  color: #fff;
  position: relative;
  overflow: hidden;
}
.file-audio .audio-left {
  margin-right: 0.9375rem;
  width: 4rem;
  height: 4rem;
  position: relative;
}
.file-audio .audio-left .icon {
  position: absolute;
  top: 1.25rem;
  right: 1.25rem;
  bottom: 1.25rem;
  left: 1.25rem;
  width: 1.5rem;
  height: 1.5rem;
}
.file-audio .audio-bg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -2;
  width: 100%;
  height: 100%;
}
.file-audio .audio-mb {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  -webkit-backdrop-filter: saturate(150%) blur(25px);
  backdrop-filter: saturate(150%) blur(25px);
  background: rgba(0, 0, 0, 0.5);
}
.file-audio .audio-t1 {
  font-size: 0.8125rem;
  font-weight: 700;
}
.file-audio .audio-t2 {
  margin-top: 0.3125rem;
  opacity: 0.8;
  font-size: 0.625rem;
}
.file-audio .aph {
  color: #fff;
}
.scroll-item .file-add {
  flex-direction: column;
  justify-content: center;
  background: #f8f8f8;
  border: 1px solid #f8f8f8;
  font-size: 0.5625rem;
  color: #000;
}
.scroll-item .file-add uni-image {
  width: 0.625rem;
  height: 0.625rem;
  margin-bottom: 0.25rem;
}
.content-box .content-item {
  width: calc(100% - 1.25rem);
  margin: 0.625rem;
  padding-top: 0.625rem;
  border-top: 0.03125rem solid #f0f0f0;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 0.5rem;
}

/* 内容区域的标签项样式调整 */
.content-item .tag-item {
  margin: 0; /* 重置margin，使用gap来控制间距 */
  flex-shrink: 0; /* 防止标签被压缩 */
}

/* 标签区域的标签项保持原有边距 */
.tags-box .tag-item {
  margin: 0 0.3125rem 0.3125rem;
}
.tags-box {
  width: calc(100% - 1.25rem);
  padding: 0 0.625rem;
  display: flex;
  flex-wrap: wrap;
}
.tag-item {
  margin: 0 0.3125rem 0.3125rem;
  height: 2rem;
  border-radius: 0.5rem;
  border: 0.125rem solid #f5f5f5;
  transition: all 0.2s ease;
}
.tag-item:active {
  transform: scale(0.95);
  background-color: #f8f8f8;
}

/* 已选择状态 */
.tag-item.selected {
  border: 0.125rem solid #f5f5f5;
  background-color: #fff;
}

/* 未选择状态 */
.tag-item.unselected {
  border: 0.125rem dashed #e0e0e0;
  background-color: #fafafa;
}
.tag-item.unselected:active {
  border-color: #d0d0d0;
  background-color: #f0f0f0;
  transform: scale(0.95);
}
.tag-item .icon {
  margin: 0.25rem;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 0.25rem;
  transition: opacity 0.2s ease;
}
.tag-item uni-text {
  font-size: 0.75rem;
  font-weight: 700;
  margin: 0 0.5rem 0 0.25rem;
  transition: color 0.2s ease;
}
.tag-delete {
  width: 0.875rem;
  height: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.25rem;
}
.tag-delete uni-text {
  font-size: 0.75rem;
  color: #999;
  margin: 0;
}
.location-icon {
  padding: 0.375rem;
  transition: opacity 0.2s ease;
}
.location-text {
  max-width: 6.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.content-hk .hk-item .del {
  color: #fa5150;
  font-size: 0.875rem;
  font-weight: 700;
  transform: rotate(45deg);
}
.popup-box {
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  background: #fff;
  border-radius: 0.9375rem 0.9375rem 0 0;
  position: relative;
  overflow: hidden;
}
.popup-box .popup-top {
  width: calc(100% - 0.625rem);
  padding: 0.3125rem;
  justify-content: space-between;
}
.popup-top .popup-title .t1 {
  font-size: 1.1875rem;
  font-weight: 700;
}
.popup-top .popup-title .t2 {
  color: #999;
  font-size: 0.625rem;
  font-weight: 300;
}
.popup-top .popup-close {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: #f8f8f8;
  justify-content: center;
  transform: rotate(45deg);
}
.popup-box .popup-btn {
  margin: 1.25rem 0.3125rem;
  width: calc(100% - 0.625rem);
  height: 3.125rem;
  line-height: 3.125rem;
  text-align: center;
  font-size: 0.75rem;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 3.125rem;
}
.popup-box .popup-search {
  margin: 0.9375rem 0.3125rem;
  width: calc(100% - 1.25rem);
  padding: 0 0.3125rem;
  height: 2.5rem;
  background: #f8f8f8;
  border-radius: 0.375rem;
}
.popup-box .popup-search uni-input {
  width: calc(100% - 4.0625rem);
  margin: 0 0.625rem;
  font-size: 0.75rem;
  font-weight: 700;
}
.popup-search .search-ph {
  color: #999;
}
.popup-search .search-btn {
  width: 2.8125rem;
  height: 1.875rem;
  line-height: 1.875rem;
  text-align: center;
  font-size: 0.625rem;
  font-weight: 700;
  color: #000;
  background: #fff;
  border-radius: 0.25rem;
}
.popup-box .popup-scroll {
  width: 100%;
  max-height: 50vh;
  overflow-y: scroll;
}
.popup-box .circle-box {
  display: flex;
  flex-wrap: wrap;
}
.circle-box .circle-item {
  margin: 0.3125rem;
  padding: 0.25rem;
  color: #000;
  border-color: #f8f8f8;
  border-width: 0.125rem;
  border-style: solid;
  border-radius: 1.5625rem;
}
.circle-box .active {
  border-color: #000 !important;
  background: #f8f8f8 !important;
}
.circle-box .circle-item uni-image {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
}
.circle-box .circle-item uni-text {
  margin: 0 0.5rem;
  font-size: 0.8125rem;
  font-weight: 700;
}
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-box uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.9375rem;
}
.empty-box .e1 {
  font-size: 0.875rem;
  font-weight: bold;
  margin-bottom: 0.3125rem;
}
.empty-box .e2 {
  font-size: 0.75rem;
  color: #999;
}
.tips-box {
  padding: 0.625rem 0.9375rem;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 0.375rem;
  justify-content: center;
}
.tips-box .tips-item {
  color: #fff;
  font-size: 0.875rem;
  font-weight: 700;
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.media-toolbar-fixed {
  position: fixed;
  left: 0;
  width: 100%;
  background-color: #f8f8f8;
  border-top: 1px solid #e5e5e5;
  z-index: 99;
  box-shadow: 0 -0.0625rem 0.1875rem rgba(0, 0, 0, 0.05);
  transition: bottom 0.3s ease;
}

/* 键盘弹起时的样式调整 */
.media-toolbar-fixed.keyboard-active {
  padding-bottom: 0; /* 键盘弹起时移除底部安全区域 */
}

/* 非键盘状态时保持底部安全区域 */
.media-toolbar-fixed:not(.keyboard-active) {
  padding-bottom: env(safe-area-inset-bottom);
}
.toolbar-box {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  width: 100%;
  min-height: 2.75rem;
  position: relative;
}
.toolbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.375rem 0.1875rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
  flex: 1;
  max-width: 3.125rem;
  min-height: 2.1875rem;
}
.toolbar-item:active {
  background-color: #eaeaea;
  transform: scale(0.95);
}
.toolbar-item.active {
  background-color: #f0f0f0;
  border-radius: 0.375rem;
}
.toolbar-item uni-image {
  width: 1.375rem;
  height: 1.375rem;
}
.toolbar-item .vote {
  color: #ff9500;
  text-shadow: 0 0.03125rem 0.0625rem rgba(255, 149, 0, 0.3);
  font-size: 1.25rem;
  font-weight: bold;
  background-color: rgba(255, 149, 0, 0.1);
  border-radius: 50%;
  width: 1.375rem;
  height: 1.375rem;
  line-height: 1.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.more-options-panel {
  width: 100%;
  background: #f8f8f8;
  border-radius: 0;
  max-height: 0;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
}
.more-options-panel.expanded {
  max-height: 6.25rem;
  padding: 0.625rem 0;
}
.more-options-row {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  margin-bottom: 0;
}
.more-options-row .toolbar-item {
  background-color: #ffffff;
  border-radius: 0.375rem;
  padding: 0.375rem 0.1875rem;
  margin: 0 0.1875rem;
}
.more-options-row .toolbar-item uni-image {
  width: 1.375rem;
  height: 1.375rem;
}
.toolbar-item.invisible {
  opacity: 0;
  pointer-events: none;
}

/* 话题列表样式 */
.topic-box {
  width: 100%;
  padding: 0.3125rem;
}
.topic-item {
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  border-bottom: 1px solid #f5f5f5;
}
.topic-tag {
  width: 1.875rem;
  height: 1.875rem;
  line-height: 1.875rem;
  text-align: center;
  background: #f8f8f8;
  border-radius: 50%;
  font-size: 1.125rem;
  font-weight: bold;
  margin-right: 0.625rem;
}
.topic-content {
  flex: 1;
}
.topic-name {
  font-size: 0.875rem;
  font-weight: 700;
  margin-bottom: 0.1875rem;
}
.topic-desc {
  font-size: 0.75rem;
  color: #999;
}

/* 录音组件样式 */
.record-popup-box {
  padding-bottom: 1.25rem;
}
.record-container {
  width: 100%;
  padding: 0.9375rem 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.record-text {
  font-size: 0.875rem;
  color: #333;
  text-align: center;
  padding: 0.625rem;
}

/* 投票弹窗样式 */
.vote-popup {
  padding-bottom: 0.9375rem;
}
.vote-title-input {
  margin: 0.625rem 0.3125rem;
  width: calc(100% - 0.625rem);
  border-bottom: 1px solid #f0f0f0;
}
.vote-input {
  width: 100%;
  height: 2.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0 0.3125rem;
}
.vote-options {
  margin: 0.625rem 0.3125rem;
  width: calc(100% - 0.625rem);
}
.vote-option-item {
  position: relative;
  margin-bottom: 0.625rem;
  background-color: #f5f6fa;
  border-radius: 0.5rem;
  padding: 0 0.625rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
}
.vote-option-input {
  flex: 1;
  height: 2.5rem;
  font-size: 0.8125rem;
  color: #333;
}
.vote-option-delete {
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.delete-circle {
  width: 1.125rem;
  height: 1.125rem;
  border-radius: 50%;
  background-color: #ff5252;
  display: flex;
  align-items: center;
  justify-content: center;
}
.delete-line {
  width: 0.625rem;
  height: 0.125rem;
  background-color: #fff;
}
.vote-add-option {
  margin: 0.9375rem 0.3125rem;
  width: calc(100% - 0.625rem);
  height: 2.5rem;
  border: 1px dashed #ddd;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}
.vote-add-icon {
  font-size: 1rem;
  margin-right: 0.3125rem;
}
.vote-add-text {
  font-size: 0.8125rem;
}
.vote-bottom-btns {
  margin-top: 1.25rem;
  display: flex;
  justify-content: space-between;
  padding: 0 0.9375rem;
}
.vote-cancel-btn, .vote-confirm-btn {
  width: 45%;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 1.25rem;
}
.vote-cancel-btn {
  color: #666;
  background-color: #f5f5f5;
}
.vote-confirm-btn {
  color: #fff;
  background-color: #000;
}

/* 投票显示样式 */
.vote-display {
  margin: 0.625rem 0;
  padding: 0.625rem;
  background-color: #f8f8f8;
  border-radius: 0.5rem;
  width: 100%;
}
.vote-display-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}
.vote-icon {
  margin-right: 0.3125rem;
  font-size: 1rem;
}
.vote-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #333;
}
.vote-display-options {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}
.vote-display-option {
  padding: 0.625rem;
  background-color: #fff;
  border-radius: 0.375rem;
  font-size: 0.8125rem;
  color: #333;
  text-align: center;
}

/* 投票显示样式 */
.vote-box {
  width: 100%;
}
.vote-container {
  width: calc(100% - 1.25rem);
  margin: 0 0.625rem;
  background-color: #f5f5f5;
  border-radius: 0.5rem;
  padding: 0.625rem;
  position: relative;
}
.vote-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  position: relative;
}
.vote-title-container {
  display: flex;
  align-items: center;
}
.vote-icon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.3125rem;
}
.vote-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #333;
  text-align: left;
}
.vote-delete {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
}
.vote-options {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}
.vote-option {
  padding: 0.625rem;
  background-color: #ffffff;
  border-radius: 0.375rem;
  font-size: 0.8125rem;
  color: #333;
  text-align: center;
}
.toolbar-label {
  margin-top: 0.1875rem;
  font-size: 0.6875rem;
  color: #666;
  text-align: center;
  line-height: 1;
}
.empty-users, .loading-users {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.875rem 0.625rem;
  color: #999;
  font-size: 0.8125rem;
}



/* @用户面板样式 - 横向滑动卡片式 */
.mention-user-panel {
  position: fixed;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 0.03125rem solid #e5e5e5;
  z-index: 999;
  padding: 0.625rem 0;
  /* 在工具栏上方，工具栏最小高度88rpx + 安全区域 */
  margin-bottom: calc(2.75rem + env(safe-area-inset-bottom));
}

/* @用户面板中的关闭按钮定位 */
.mention-user-panel .popup-close {
  position: absolute;
  top: 0.3125rem;
  right: 0.625rem;
  z-index: 1000;
}
.empty-mention-users,
.loading-mention-users {
  text-align: center;
  padding: 1.875rem 0;
  color: #999;
  font-size: 0.875rem;
  height: 3.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 横向滚动容器 */
.mention-user-scroll {
  width: 100%;
  height: 3.75rem;
  white-space: nowrap;
}

/* 横向用户列表容器 */
.mention-user-list-horizontal {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 3.75rem;
  padding: 0 0.625rem;
  gap: 0.625rem;
}

/* 用户卡片样式 */
.mention-user-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 3.125rem;
  height: 3.75rem;
  flex-shrink: 0;
  position: relative;
  border-radius: 0.375rem;
  padding: 0.25rem;
}
.mention-user-card:active {
  background: #f5f5f5;
}

/* 用户头像 */
.mention-user-avatar {
  width: 1.875rem;
  height: 1.875rem;
  border-radius: 50%;
  margin-bottom: 0.25rem;
  flex-shrink: 0;
  border: 0.0625rem solid #fff;
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.1);
}

/* 用户昵称 */
.mention-user-nickname {
  font-size: 0.6875rem;
  color: #333;
  font-weight: 500;
  text-align: center;
  max-width: 2.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
}


