{"version": 3, "file": "getters.js", "sources": ["store/getters.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nexport default {\n  token: state => state.app.token,\n  isLogin: state => !!state.app.token,\n  backgroundColor: state => state.app.backgroundColor,\n  userInfo: state => state.app.userInfo || {},\n\tuid:state => state.app.uid,\n\thomeActive: state => state.app.homeActive,\n\thome: state => state.app.home,\n\tcartNum: state => state.indexData.cartNum,\n\tactivityTab: state => state.app.activityTab,\n\tflowSettings: state => state.app.flowSettings || { dynamicFlow: false, circleFlow: false },\n};\n// export default {\n//   token: state => 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************.U-i1pbdRjyXI1gr79Uq2XBPZ89T8f5Ai9jwrR8woTwE',\n//   isLogin: state => true,\n//   backgroundColor: state => state.app.backgroundColor,\n//   userInfo: state => state.app.userInfo || {}\n// };\n"], "names": [], "mappings": ";AAUA,MAAe,UAAA;AAAA,EACb,OAAO,WAAS,MAAM,IAAI;AAAA,EAC1B,SAAS,WAAS,CAAC,CAAC,MAAM,IAAI;AAAA,EAC9B,iBAAiB,WAAS,MAAM,IAAI;AAAA,EACpC,UAAU,WAAS,MAAM,IAAI,YAAY,CAAE;AAAA,EAC5C,KAAI,WAAS,MAAM,IAAI;AAAA,EACvB,YAAY,WAAS,MAAM,IAAI;AAAA,EAC/B,MAAM,WAAS,MAAM,IAAI;AAAA,EACzB,SAAS,WAAS,MAAM,UAAU;AAAA,EAClC,aAAa,WAAS,MAAM,IAAI;AAAA,EAChC,cAAc,WAAS,MAAM,IAAI,gBAAgB,EAAE,aAAa,OAAO,YAAY,MAAO;AAC3F;;"}