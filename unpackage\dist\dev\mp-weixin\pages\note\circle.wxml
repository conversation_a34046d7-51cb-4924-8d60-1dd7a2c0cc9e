<view class="container"><view class="nav-box df" style="{{'padding-top:' + f + ';' + ('background:' + g)}}"><view class="nav-back df" style="{{'height:' + b}}" bindtap="{{c}}"><image src="{{a}}" style="width:34rpx;height:34rpx"></image></view><view wx:if="{{d}}" class="nav-title ohto">{{e}}</view></view><view class="circle-header"><view class="images-box df"><view class="circle-image-container"><lazy-image wx:if="{{h}}" class="circle-bg-image" bindtap="{{i}}" u-i="854b211a-0" bind:__l="__l" u-p="{{j}}"></lazy-image><view wx:else class="default-bg"></view><view class="bg-overlay"></view></view></view><view class="circle-info-overlay" style="{{'padding-top:' + M}}"><view class="circle-main-info df"><view class="circle-avatar"><image wx:if="{{k}}" src="{{l}}" mode="aspectFill" class="avatar-img"></image><view wx:else class="skeleton-avatar"></view></view><view class="circle-details"><view class="circle-name"><text wx:if="{{m}}">{{n}}</text><view wx:else class="skeleton-text"></view></view><view class="circle-stats df"><text wx:if="{{o}}" class="stat-item">{{p}}篇笔记</text><text wx:if="{{q}}" class="stat-divider">·</text><text wx:if="{{r}}" class="stat-item">{{s}}人加入</text><text wx:if="{{t}}" class="stat-divider">·</text><text wx:if="{{v}}" class="stat-item">访问 {{w}}</text><view wx:if="{{x}}" class="skeleton-stats"><view class="skeleton-item"></view><view class="skeleton-item"></view><view class="skeleton-item"></view></view></view></view></view><view wx:if="{{y}}" class="circle-description"><text>{{z}}</text></view><view wx:if="{{A}}" class="circle-notice"><text>【公告】{{B}}</text></view><view class="btn-box df"><view wx:if="{{C}}" class="btn df bg1 loading"><view class="loading-dots"><view class="dot"></view><view class="dot"></view><view class="dot"></view></view></view><block wx:else><view bindtap="{{G}}" class="{{['btn', 'df', 'bg1', H]}}"><view wx:if="{{D}}" class="cu-img-group"><view wx:for="{{E}}" wx:for-item="member" wx:key="b" class="cu-img"><image src="{{member.a}}" mode="aspectFill"></image></view></view><text>{{F}}</text></view><view bindtap="{{K}}" class="{{['btn', 'df', 'bg2', L]}}"><image src="{{I}}" class="icon"></image><text hidden="{{!J}}">分享</text></view></block></view></view></view><view class="bar-box df"><view wx:for="{{N}}" wx:for-item="item" wx:key="e" class="bar-item df" bindtap="{{item.f}}" data-idx="{{item.g}}"><text style="{{'color:' + item.b + ';' + ('font-size:' + item.c)}}">{{item.a}}</text><view style="{{'opacity:' + item.d}}" class="bar-line"></view></view></view><view wx:if="{{O}}" class="rule-box"><view class="rule-title">小圈规则</view><view class="rule-list"><view class="rule-item"><view class="rule-tag tag-orange">适合人群</view><text class="rule-text">性格属性有K8倾向，以及喜欢和K8做朋友的圈友。性格属性有K8倾向，以及喜欢和K8做朋友的圈友。</text></view><view class="rule-item"><view class="rule-tag tag-orange">提倡内容</view><text class="rule-text">分享记录猫咪生活，如猫咪的想法、装扮、吐槽等。</text></view><view class="rule-item"><view class="rule-tag tag-orange">禁止内容</view><text class="rule-text">广告/引流贴，或与圈子主题毫不相关的内容，看到直接删除。</text></view></view><view class="rule-team-title df"><text>管理团队</text><view class="rule-team-all" bindtap="{{P}}">全部成员</view></view><view class="rule-team-list"><view wx:for="{{Q}}" wx:for-item="member" wx:key="h" class="member-item df" bindtap="{{member.i}}"><image class="avatar" src="{{member.a}}"></image><view class="info"><view class="name df"><text>{{member.b}}</text><view wx:if="{{member.c}}" class="role-tag owner">圈主</view><view wx:elif="{{member.d}}" class="role-tag admin">管理员</view></view><view class="meta df"><text class="{{['gender', member.f]}}">{{member.e}}</text><text class="contrib">加入时间：{{member.g}}</text></view></view></view></view></view><view wx:else class="content-area"><view wx:if="{{R}}" class="loading-container"><view class="loading-indicator"></view></view><view wx:if="{{S}}" class="empty-box df"><image src="{{T}}"/><view class="e1">{{U}} 暂无笔记</view><view class="e2">{{V}}</view></view><view wx:else class="{{[Z]}}"><waterfall wx:if="{{W}}" u-i="854b211a-1" bind:__l="__l" u-p="{{X}}"></waterfall><block wx:else><card-gg wx:for="{{Y}}" wx:for-item="item" wx:key="a" bindlikeback="{{item.b}}" bindupdate="{{item.c}}" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"></card-gg></block></view><view wx:if="{{aa}}" style="text-align:center;padding:20rpx 0;color:#999;font-size:24rpx"> 没有更多数据了 </view></view><share-component wx:if="{{ah}}" bindclose="{{ab}}" bindshare="{{ac}}" binddislike="{{ad}}" bindreport="{{ae}}" bindedit="{{af}}" binddelete="{{ag}}" u-i="854b211a-3" bind:__l="__l" u-p="{{ah}}"></share-component><uni-popup wx:if="{{ak}}" class="r" u-s="{{['d']}}" u-r="tipsPopup" u-i="854b211a-4" bind:__l="__l" u-p="{{ak}}"><view class="tips-box df"><view class="tips-item">{{ai}}</view></view></uni-popup></view>