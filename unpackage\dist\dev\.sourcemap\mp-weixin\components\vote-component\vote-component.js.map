{"version": 3, "file": "vote-component.js", "sources": ["components/vote-component/vote-component.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy92b3RlLWNvbXBvbmVudC92b3RlLWNvbXBvbmVudC52dWU"], "sourcesContent": ["<template>\n  <view v-if=\"voteInfo\" class=\"vote-box\">\n    <view class=\"vote-container\">\n      <view class=\"vote-header\">\n        <view class=\"vote-title-container\">\n          <image class=\"vote-icon\" src=\"/static/img/toupiao.png\" mode=\"aspectFit\"></image>\n          <text class=\"vote-title\">{{ voteInfo.vote.title }}</text>\n        </view>\n        <!-- 删除按钮（仅在编辑模式下显示） -->\n        <view v-if=\"showDelete\" class=\"vote-delete\" @tap=\"handleDelete\">\n          <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx;transform:rotate(45deg)\"></image>\n        </view>\n      </view>\n      <view class=\"vote-options\">\n        <!-- 未投票时 -->\n        <template v-if=\"!voteInfo.user_selected\">\n          <view \n            v-for=\"(option, idx) in voteInfo.options\" \n            :key=\"option.id || idx\" \n            class=\"vote-option vote-option-unvoted\"\n            @tap=\"handleVote(option.id)\">\n            <text class=\"vote-content\">{{ option.option_text }}</text>\n          </view>\n        </template>\n        <!-- 已投票时 -->\n        <template v-else>\n          <view \n            v-for=\"(option, idx) in voteInfo.options\" \n            :key=\"option.id || idx\" \n            class=\"vote-option vote-option-unvoted\">\n            <view class=\"vote-row\">\n              <view class=\"vote-left\">\n                <view v-if=\"voteInfo.user_selected === option.id\" class=\"vote-checked-icon\">\n                  <image src=\"/static/img/c1.png\" style=\"width:30rpx;height:30rpx;\"></image>\n                </view>\n                <text class=\"vote-content\">{{ option.option_text }}</text>\n              </view>\n              <text class=\"vote-percent\">{{ option.percent }}%</text>\n            </view>\n            <view class=\"vote-bar-bg\">\n              <view class=\"vote-bar\" :style=\"{\n                width: option.percent + '%',\n                background: voteInfo.user_selected === option.id ? '#ffd600' : '#eaeaea'\n              }\"></view>\n            </view>\n          </view>\n        </template>\n      </view>\n      <view class=\"vote-people\">{{ votePeopleText }}</view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { vote } from '@/api/social.js'\n\nexport default {\n  name: 'VoteComponent',\n  props: {\n    // 投票信息对象\n    voteInfo: {\n      type: Object,\n      default: null\n    },\n    // 是否显示删除按钮（编辑模式）\n    showDelete: {\n      type: Boolean,\n      default: false\n    },\n    // 是否禁用投票（已投票或其他原因）\n    disabled: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      voting: false // 投票进行中状态\n    }\n  },\n  computed: {\n    // 参与投票人数文本\n    votePeopleText() {\n      if (!this.voteInfo) return '';\n      const total = this.voteInfo.total || 0;\n      return this.formatNumber(total, 10000, '万') + '人参与了投票';\n    }\n  },\n  methods: {\n    // 数字格式化\n    formatNumber(num, threshold = 10000, suffix = 'w') {\n      if (!num || num < threshold) return num.toString() || '';\n      return (num / threshold).toFixed(1) + suffix;\n    },\n\n    // 处理投票点击\n    async handleVote(optionId) {\n      // 检查是否已投票或禁用状态\n      if (this.voting || this.voteInfo.user_selected || this.disabled) {\n        return;\n      }\n\n      // 检查登录状态\n      if (!this.$store.getters.isLogin) {\n        uni.navigateTo({\n          url: '/pages/login/index'\n        });\n        return;\n      }\n\n      this.voting = true;\n      \n      try {\n        const voteId = this.voteInfo.vote.id;\n        const res = await vote({ \n          vote_id: voteId, \n          option_id: optionId \n        });\n        \n        if (res.status === 200 && res.data && res.data.vote_info) {\n          // 发送投票成功事件给父组件\n          this.$emit('vote-success', {\n            voteInfo: res.data.vote_info,\n            optionId: optionId\n          });\n          \n          uni.showToast({ \n            title: '投票成功', \n            icon: 'none' \n          });\n        } else {\n          uni.showToast({ \n            title: res.msg || '投票失败', \n            icon: 'none' \n          });\n        }\n      } catch (error) {\n        console.error('投票失败:', error);\n        uni.showToast({ \n          title: error.message || '投票失败', \n          icon: 'none' \n        });\n      } finally {\n        this.voting = false;\n      }\n    },\n\n    // 处理删除投票\n    handleDelete() {\n      console.log('VoteComponent: handleDelete被调用');\n      // 直接触发删除事件，让父组件处理确认逻辑\n      this.$emit('vote-delete');\n      console.log('VoteComponent: vote-delete事件已发送');\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 投票展示样式 */\n.vote-box {\n  width: 100%;\n  margin-top: 16rpx;\n}\n\n.vote-container {\n  width: 100%;\n  background-color: #f5f5f5;\n  border-radius: 16rpx;\n  position: relative;\n}\n\n.vote-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: relative;\n  padding: 20rpx;\n}\n\n.vote-title-container {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.vote-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 10rpx;\n}\n\n.vote-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  text-align: left;\n  white-space: normal;\n  word-break: break-word;\n}\n\n.vote-delete {\n  width: 40rpx;\n  height: 40rpx;\n  border-radius: 50%;\n  background: #f8f8f8;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 10rpx;\n  position: relative;\n  z-index: 10;\n  cursor: pointer;\n  transition: background 0.2s;\n}\n\n.vote-delete:hover {\n  background: #e8e8e8;\n}\n\n.vote-delete:active {\n  transform: scale(0.95);\n}\n\n.vote-options {\n  display: flex;\n  flex-direction: column;\n  padding: 0 0;\n}\n\n.vote-option-unvoted {\n  background: #fff;\n  border-radius: 32rpx;\n  font-size: 26rpx;\n  color: #333;\n  margin-bottom: 18rpx;\n  border: none;\n  box-shadow: none;\n  text-align: center;\n  transition: background 0.2s, border 0.2s;\n  margin-left: 20rpx;\n  margin-right: 20rpx;\n  position: relative;\n  min-height: 66rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 20rpx;\n  box-sizing: border-box;\n}\n\n.vote-row {\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 2;\n  pointer-events: none;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20rpx;\n}\n\n.vote-left {\n  display: flex;\n  align-items: center;\n}\n\n.vote-bar-bg {\n  border-radius: 18px;\n  font-size: 17px;\n  color: #333;\n  margin-bottom: 10px;\n  border: none;\n  box-shadow: none;\n  text-align: center;\n  transition: background 0.2s, border 0.2s;\n  margin-left: 11px;\n  margin-right: 11px;\n  min-height: 27px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 11px;\n  box-sizing: border-box;\n}\n\n.vote-bar {\n  height: 100%;\n  border-radius: 32rpx;\n  transition: width 0.3s;\n  position: absolute;\n  left: 0;\n  top: 0;\n  z-index: 1;\n}\n\n.vote-checked-icon {\n  width: 28rpx;\n  height: 28rpx;\n  background: #ffd600;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 10rpx;\n}\n\n.vote-checked-icon image {\n  width: 20rpx;\n  height: 20rpx;\n  display: block;\n}\n\n.vote-content {\n  font-size: 26rpx;\n  color: #333;\n  text-align: left;\n  white-space: normal;\n  word-break: break-word;\n  font-weight: 500;\n}\n\n.vote-percent {\n  font-size: 26rpx;\n  color: #000000;\n  text-align: right;\n  margin-left: 12rpx;\n  min-width: 48rpx;\n  flex-shrink: 0;\n  white-space: nowrap;\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  font-weight: 500;\n  margin-right: 40rpx;\n  position: relative;\n}\n\n.vote-people {\n  margin-top: 16rpx;\n  color: #999;\n  font-size: 24rpx;\n  text-align: left;\n  padding-left: 20rpx;\n  padding-bottom: 20rpx;\n}\n</style>\n", "import Component from 'D:/uniapp/vue3/components/vote-component/vote-component.vue'\nwx.createComponent(Component)"], "names": ["uni", "vote"], "mappings": ";;;;AAwDA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEL,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA;AAAA,IAED,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,QAAQ;AAAA;AAAA,IACV;AAAA,EACD;AAAA,EACD,UAAU;AAAA;AAAA,IAER,iBAAiB;AACf,UAAI,CAAC,KAAK;AAAU,eAAO;AAC3B,YAAM,QAAQ,KAAK,SAAS,SAAS;AACrC,aAAO,KAAK,aAAa,OAAO,KAAO,GAAG,IAAI;AAAA,IAChD;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,aAAa,KAAK,YAAY,KAAO,SAAS,KAAK;AACjD,UAAI,CAAC,OAAO,MAAM;AAAW,eAAO,IAAI,SAAW,KAAG;AACtD,cAAQ,MAAM,WAAW,QAAQ,CAAC,IAAI;AAAA,IACvC;AAAA;AAAA,IAGD,MAAM,WAAW,UAAU;AAEzB,UAAI,KAAK,UAAU,KAAK,SAAS,iBAAiB,KAAK,UAAU;AAC/D;AAAA,MACF;AAGA,UAAI,CAAC,KAAK,OAAO,QAAQ,SAAS;AAChCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACP,CAAC;AACD;AAAA,MACF;AAEA,WAAK,SAAS;AAEd,UAAI;AACF,cAAM,SAAS,KAAK,SAAS,KAAK;AAClC,cAAM,MAAM,MAAMC,gBAAK;AAAA,UACrB,SAAS;AAAA,UACT,WAAW;AAAA,QACb,CAAC;AAED,YAAI,IAAI,WAAW,OAAO,IAAI,QAAQ,IAAI,KAAK,WAAW;AAExD,eAAK,MAAM,gBAAgB;AAAA,YACzB,UAAU,IAAI,KAAK;AAAA,YACnB;AAAA,UACF,CAAC;AAEDD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,eACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,uDAAA,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QACR,CAAC;AAAA,MACH,UAAU;AACR,aAAK,SAAS;AAAA,MAChB;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,0EAAY,gCAAgC;AAE5C,WAAK,MAAM,aAAa;AACxBA,oBAAAA,MAAA,MAAA,OAAA,uDAAY,iCAAiC;AAAA,IAC/C;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1JA,GAAG,gBAAgB,SAAS;"}