"use strict";
const common_vendor = require("../common/vendor.js");
require("../store/index.js");
const utils_index = require("../utils/index.js");
require("../utils/request.js");
const Socket = function() {
};
Socket.prototype = {
  // close() {
  //   clearInterval(this.timer);
  //   this.ws.close();
  // },
  onSocketOpen: function(my) {
    common_vendor.index.$emit("socketOpen", my);
  },
  init: function() {
    var that = this;
    this.timer = setInterval(function() {
      that.send({
        type: "ping"
      });
    }, 1e4);
  },
  send: function(data) {
    let datas = JSON.stringify(data);
    return common_vendor.index.sendSocketMessage({
      data: datas
    });
  },
  onMessage: function(res) {
    const {
      type,
      data = {}
    } = JSON.parse(res.data);
    common_vendor.index.$emit(type, data);
  },
  onClose: function() {
    common_vendor.index.closeSocket();
    clearInterval(this.timer);
    common_vendor.index.$emit("socket_close");
  },
  onError: function(e) {
    common_vendor.index.$emit("socket_error", e);
  },
  close: function() {
    common_vendor.index.closeSocket();
  },
  onStart: function(token, form_type) {
    let wssUrl = `${utils_index.VUE_APP_WS_URL}`;
    this.ws = common_vendor.index.connectSocket({
      url: wssUrl + "?type=user&token=" + token + "&form_type=" + form_type,
      header: {
        "content-type": "application/json"
      },
      method: "GET",
      success: (res) => {
      }
    });
    this.ws.onOpen(this.onSocketOpen.bind(this));
    this.ws.onError(this.onError.bind(this));
    this.ws.onMessage(this.onMessage.bind(this));
    this.ws.onClose(this.onClose.bind(this));
  }
};
Socket.prototype.constructor = Socket;
exports.Socket = Socket;
//# sourceMappingURL=../../.sourcemap/mp-weixin/libs/new_chat.js.map
