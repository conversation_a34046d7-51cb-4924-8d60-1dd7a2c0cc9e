
.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.nav-bar[data-v-eaf4c2e5] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.nav-bar .navbar-item[data-v-eaf4c2e5] {
  width: 100%;
  display: flex;
  align-items: center;
}
.navbar-item .back-box[data-v-eaf4c2e5] {
  padding: 0 0.9375rem;
  display: flex;
  align-items: center;
}
.navbar-item .back-box uni-image[data-v-eaf4c2e5] {
  width: 1.0625rem;
  height: 1.0625rem;
}
.bfw[data-v-eaf4c2e5] {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,.8);
}
.bf8[data-v-eaf4c2e5] {
  background: #f8f8f8;
}


.money-box[data-v-433ff6d7] {
  display: flex;
  align-items: flex-end;
  font-weight: bolder;
}
.money-box .sale[data-v-433ff6d7] {
  margin: 0 0.0625rem;
}
.money-box .unit[data-v-433ff6d7] {
  margin-bottom: 0.0625rem;
}


body { background: #f8f8f8;
}
.container { padding-bottom: 7.5rem;
}
.w100p30 {
  z-index: 1;
  margin: 0.625rem 0.9375rem;
  width: calc(100% - 3.125rem);
  padding: 0.9375rem 0.625rem;
  border-radius: 0.9375rem;
  background: #fff;
  overflow: hidden;
  position: relative;
}
.title {
  width: 100%;
  color: #000;
  font-size: 0.8125rem;
  font-weight: 700;
}
.w100p30 .map-bg {
  position: absolute;
  z-index: -2;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}
.w100p30 .mask-bg {
  position: absolute;
  z-index: -1;
  right: -0.0625rem;
  bottom: -0.0625rem;
  width: calc(100% - 0.75rem);
  height: calc(100% - 0.75rem);
  border: 0.4375rem solid #fff;
  border-radius: 0.9375rem;
  background-image: linear-gradient(to right, #fff, rgba(255, 255, 255, .9), rgba(255, 255, 255, .6));
}
.w100p30 .adds-box {
  z-index: 1;
  width: 100%;
  height: 100%;
  justify-content: space-between;
}
.adds-box .adds-item {
  color: #000;
  font-size: 0.75rem;
  font-weight: 700;
}
.adds-box .adds-item .txt {
  padding: 0.3125rem 0;
  font-size: 1rem;
}
.adds-box .adds-add uni-image {
  width: 0.875rem;
  height: 0.875rem;
  transform: rotate(-90deg);
}
.goods-item {
  padding-top: 0.9375rem;
  justify-content: space-between;
  animation: fadeIn .45s ease;
}
.goods-item uni-image {
  width: 4.375rem;
  height: 4.375rem;
  border-radius: 0.25rem;
}
.goods-item .goods-info {
  width: calc(100% - 5rem);
  height: 4.375rem;
  font-weight: 700;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.goods-item .goods-info .t1 {
  color: #000;
  font-size: 0.8125rem;
}
.goods-item .goods-info .t2 {
  color: #999;
  font-size: 0.75rem;
}
.goods-item .goods-info .goods-info-bom {
  margin-top: 0.78125rem;
  width: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}
.goods-info .goods-info-bom .sum {
  margin-right: 0.625rem;
  color: #999;
  font-size: 0.6875rem;
}
.list-item {
  padding: 0.46875rem 0;
  justify-content: space-between;
  color: #000;
  font-size: 0.75rem;
}
.list-item .list-right .zs {
  color: #999;
  margin-right: 0.3125rem;
}
.list-item .list-right .icon {
  width: 0.6875rem;
  height: 0.6875rem;
  transform: rotate(-90deg);
}
.list-item uni-textarea {
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  border-radius: 0.25rem;
  background: #f8f8f8;
  font-size: 0.75rem;
  font-weight: 700;
  min-height: 3.75rem;
}
.list-item .list-item-xz {
  margin-top: 0.46875rem;
  color: #999;
  font-weight: 300;
  font-size: 0.75rem;
}
.list-item .list-item-xz uni-text {
  margin-left: 0.1875rem;
  color: #000;
  font-weight: 500;
  text-decoration: underline;
}
.footer {
  position: fixed;
  z-index: 99;
  bottom: 0;
  left: 0;
  width: calc(100% - 2.5rem);
  padding: 0.9375rem 1.25rem;
  flex-direction: column;
  background-color: #fff;
  padding-bottom: max(env(safe-area-inset-bottom), 0.625rem);
  border-top: 0.0625rem solid #f5f5f5;
}
.footer .btn {
  width: 100%;
  height: 3.125rem;
  justify-content: center;
  background: #000;
  border-radius: 1.5625rem;
}
.footer .btn uni-image {
  width: 1rem;
  height: 1rem;
}
.footer .btn uni-text {
  margin-left: 0.375rem;
  color: #fff;
  font-size: 0.8125rem;
  font-weight: bolder;
}
.popup-box {
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  background: #f8f8f8;
  border-radius: 0.9375rem 0.9375rem 0 0;
  position: relative;
  overflow: hidden;
}
.popup-box .popup-top {
  width: calc(100% - 0.625rem);
  padding: 0.3125rem;
  justify-content: space-between;
}
.popup-top .popup-title .t1 {
  font-size: 1.1875rem;
  font-weight: 700;
}
.popup-top .popup-title .t2 {
  color: #999;
  font-size: 0.625rem;
  font-weight: 300;
}
.popup-top .popup-close {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: #eee;
  justify-content: center;
  transform: rotate(45deg);
}
.popup-box .popup-scroll {
  padding-top: 0.625rem;
  width: 100%;
  max-height: 50vh;
  overflow-y: scroll;
}
.popup-scroll .coupon {
  margin: 0 0.1875rem 0.9375rem;
  width: calc(100% - 0.625rem);
  background: #fff;
  border-width: 0.125rem;
  border-style: solid;
  border-radius: 0.25rem;
  position: relative;
  overflow: hidden;
}
.coupon .coupon-bg {
  position: absolute;
  z-index: 1;
  right: -2.8125rem;
  bottom: -3.75rem;
  width: 11.875rem;
  height: 11.875rem;
}
.coupon .coupon-sub {
  position: absolute;
  z-index: 9;
  top: 0;
  left: 0;
  border-radius: 0.25rem 0;
  padding: 0 0.375rem;
  height: 1.125rem;
  line-height: 1.125rem;
  text-align: center;
  font-size: 0.625rem;
  color: #fa5150;
  background: rgba(250, 81, 80, .125);
}
.coupon .coupon-item {
  z-index: 2;
  width: calc(100% - 2.5rem);
  padding: 1.5625rem 1.25rem 0.625rem;
  border-bottom: 0.0625rem dashed #f8f8f8;
  position: relative;
}
.coupon-item .coupon-price {
  width: calc(100% - 6.25rem);
  color: #000;
  font-size: 1.5rem;
  font-weight: 700;
}
.coupon-item .coupon-intro {
  width: calc(100% - 6.25rem);
  margin: 0.25rem 0;
  color: #444;
  font-size: 0.75rem;
}
.coupon .coupon-validity {
  width: calc(100% - 2.5rem);
  padding: 0.625rem 1.25rem;
  color: #999;
  font-size: 0.625rem;
}
.coupon .coupon-err {
  position: absolute;
  z-index: 10;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 0.875rem;
  font-style: italic;
  font-weight: 700;
  justify-content: center;
  color: #ccc;
  background: rgba(255, 255, 255, .85);
}
.popup-box .popup-btn {
  margin: 1.25rem 0.3125rem;
  width: calc(100% - 0.625rem);
  height: 3.125rem;
  line-height: 3.125rem;
  text-align: center;
  font-size: 0.75rem;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 3.125rem;
}
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.875rem 0;
}
.empty-box uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.9375rem;
}
.empty-box .e1 {
  font-size: 0.875rem;
  font-weight: bold;
  margin-bottom: 0.3125rem;
}
.empty-box .e2 {
  font-size: 0.75rem;
  color: #999;
}
.tips-box {
  padding: 0.625rem 0.9375rem;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 0.375rem;
  justify-content: center;
}
.tips-box .tips-item {
  color: #fff;
  font-size: 0.875rem;
  font-weight: 700;
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
