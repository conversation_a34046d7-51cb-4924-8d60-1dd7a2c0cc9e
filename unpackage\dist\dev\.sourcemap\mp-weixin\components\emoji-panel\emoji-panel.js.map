{"version": 3, "file": "emoji-panel.js", "sources": ["components/emoji-panel/emoji-panel.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy9lbW9qaS1wYW5lbC9lbW9qaS1wYW5lbC52dWU"], "sourcesContent": ["<template>\n  <view v-if=\"show\" class=\"emoji-panel\">\n    <!-- 表情内容区域 -->\n    <scroll-view class=\"emoji-scroll\" scroll-y enhanced :show-scrollbar=\"false\">\n      <view class=\"emoji-container\">\n        <!-- 最近使用的表情 -->\n        <view v-if=\"recentEmojis.length > 0\" class=\"recent-section\">\n          <view class=\"section-title\">最近使用</view>\n          <view class=\"recent-emojis\">\n            <view \n              v-for=\"(emoji, index) in recentEmojis\" \n              :key=\"`recent-${index}`\"\n              class=\"emoji-item\" \n              @tap.stop=\"selectEmoji(emoji)\"\n              @longpress=\"previewEmoji(emoji)\"\n            >\n              <image \n                class=\"emoji-image\" \n                :src=\"emoji.url || emoji.icon\"\n                :lazy-load=\"true\"\n                mode=\"aspectFit\"\n                @error=\"onImageError\"\n              ></image>\n            </view>\n          </view>\n        </view>\n\n        <!-- 分隔线 -->\n        <view v-if=\"recentEmojis.length > 0\" class=\"section-divider\"></view>\n\n        <!-- 所有表情 -->\n        <view class=\"all-emojis\">\n        <view \n            v-for=\"(emoji, index) in emojiList\" \n            :key=\"`emoji-${index}`\"\n            class=\"emoji-item\" \n            @tap.stop=\"selectEmoji(emoji)\"\n            @longpress=\"previewEmoji(emoji)\"\n        >\n            <image \n              class=\"emoji-image\" \n              :src=\"emoji.url || emoji.icon\"\n              :lazy-load=\"true\"\n              mode=\"aspectFit\"\n              @error=\"onImageError\"\n            ></image>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n    \n    <!-- 表情预览弹窗 -->\n    <view v-if=\"previewEmojiData\" class=\"emoji-preview\" @tap=\"closePreview\">\n      <view class=\"preview-content\" @tap.stop>\n        <image class=\"preview-image\" :src=\"previewEmojiData.url || previewEmojiData.icon\" mode=\"aspectFit\"></image>\n        <text class=\"preview-name\">{{previewEmojiData.phrase}}</text>\n        <view class=\"preview-actions\">\n          <view class=\"preview-btn\" @tap=\"selectEmoji(previewEmojiData)\">发送</view>\n          <view class=\"preview-btn secondary\" @tap=\"closePreview\">取消</view>\n          </view>\n              </view>\n          </view>\n  </view>\n</template>\n\n<script>\n// 导入表情列表\nimport emojiList from './sina.js'\n\n// 缓存键名\nconst RECENT_EMOJIS_KEY = 'recent_emojis_v2'\n\nexport default {\n  name: 'EmojiPanel',\n  props: {\n    show: {\n      type: Boolean,\n      default: false\n    },\n    maxRecentCount: {\n      type: Number,\n      default: 9,\n      validator: (value) => value > 0 && value <= 20\n    },\n    enablePreview: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      emojiList: emojiList || [],\n      recentEmojis: [],\n      previewEmojiData: null\n    }\n  },\n  watch: {\n    show(val) {\n      if (val) {\n        this.loadRecentEmojis();\n      } else {\n        this.closePreview();\n      }\n    }\n  },\n  methods: {\n    // 加载最近使用的表情\n    loadRecentEmojis() {\n      try {\n        const recentEmojiStr = uni.getStorageSync(RECENT_EMOJIS_KEY);\n        if (recentEmojiStr) {\n          const recentData = JSON.parse(recentEmojiStr);\n          if (Array.isArray(recentData)) {\n            this.recentEmojis = recentData.slice(0, this.maxRecentCount);\n          }\n        }\n      } catch (e) {\n        this.recentEmojis = [];\n      }\n    },\n    \n    // 保存最近使用的表情\n    saveRecentEmojis() {\n      try {\n        uni.setStorageSync(RECENT_EMOJIS_KEY, JSON.stringify(this.recentEmojis));\n      } catch (e) {\n        // 静默失败\n      }\n    },\n    \n    // 选择表情\n    selectEmoji(emoji) {\n      if (!emoji) return;\n      \n      // 隐藏键盘\n      uni.hideKeyboard && uni.hideKeyboard();\n      \n      // 关闭预览\n      this.closePreview();\n      \n      // 添加到最近使用的表情\n      this.addToRecentEmojis(emoji);\n      \n      // 触发选择事件\n      this.$emit('select', emoji);\n    },\n\n    // 添加到最近使用\n    addToRecentEmojis(emoji) {\n      if (!emoji) return;\n\n      // 检查是否已存在\n      const existingIndex = this.recentEmojis.findIndex(item => {\n        const itemId = item.url || item.icon;\n        const emojiId = emoji.url || emoji.icon;\n        return itemId === emojiId;\n      });\n      \n      // 如果已存在，移除旧的\n      if (existingIndex !== -1) {\n        this.recentEmojis.splice(existingIndex, 1);\n      }\n      \n      // 添加到开头\n      this.recentEmojis.unshift(emoji);\n      \n      // 限制数量\n      if (this.recentEmojis.length > this.maxRecentCount) {\n        this.recentEmojis = this.recentEmojis.slice(0, this.maxRecentCount);\n      }\n      \n      // 保存到本地存储\n      this.saveRecentEmojis();\n    },\n\n    // 预览表情\n    previewEmoji(emoji) {\n      if (!this.enablePreview || !emoji) return;\n      this.previewEmojiData = emoji;\n    },\n\n    // 关闭预览\n    closePreview() {\n      this.previewEmojiData = null;\n    },\n\n    // 图片加载失败\n    onImageError() {\n      // 静默处理图片加载失败\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.emoji-panel {\n  width: 100%;\n  height: 400rpx;\n  background-color: #fff;\n  border-top: 1rpx solid #f1f1f1;\n  z-index: 999;\n  position: relative;\n}\n\n.emoji-scroll {\n  height: 100%;\n}\n\n.emoji-container {\n  padding: 20rpx;\n}\n\n// 最近使用区域\n.recent-section {\n  margin-bottom: 20rpx;\n}\n\n.section-title {\n  font-size: 24rpx;\n  color: #999;\n  margin-bottom: 15rpx;\n  padding-left: 10rpx;\n}\n\n.recent-emojis {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10rpx;\n}\n\n.section-divider {\n  height: 1rpx;\n  background-color: #f1f1f1;\n  margin: 20rpx 0;\n}\n\n// 所有表情区域\n.all-emojis {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 5rpx;\n}\n\n.emoji-item {\n  width: 70rpx;\n  height: 70rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 12rpx;\n  transition: background-color 0.2s ease;\n}\n\n.emoji-item:active {\n  background-color: #f5f5f5;\n}\n\n.emoji-image {\n  width: 50rpx;\n  height: 50rpx;\n  object-fit: contain;\n}\n\n/* 预览弹窗样式 */\n.emoji-preview {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.6);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n  animation: fadeIn 0.3s ease;\n}\n\n.preview-content {\n  background-color: #fff;\n  border-radius: 20rpx;\n  padding: 60rpx 40rpx 40rpx;\n  margin: 40rpx;\n  max-width: 500rpx;\n  text-align: center;\n  animation: slideUp 0.3s ease;\n}\n\n.preview-image {\n  width: 120rpx;\n  height: 120rpx;\n  margin-bottom: 20rpx;\n}\n\n.preview-name {\n  font-size: 32rpx;\n  color: #333;\n  margin-bottom: 40rpx;\n}\n\n.preview-actions {\n  display: flex;\n  gap: 20rpx;\n  justify-content: center;\n}\n\n/* 小程序兼容：将SCSS嵌套语法改为普通CSS */\n.preview-btn {\n  padding: 20rpx 40rpx;\n  border-radius: 50rpx;\n  font-size: 28rpx;\n  text-align: center;\n  transition: all 0.2s ease;\n}\n\n.preview-btn:not(.secondary) {\n  background-color: #ff4d6a;\n  color: #fff;\n}\n\n.preview-btn:not(.secondary):active {\n  background-color: #e6445e;\n}\n\n.preview-btn.secondary {\n  background-color: #f5f5f5;\n  color: #666;\n}\n\n.preview-btn.secondary:active {\n  background-color: #e8e8e8;\n}\n\n/* 动画 */\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(50rpx);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n// 暗色主题支持\n@media (prefers-color-scheme: dark) {\n  .emoji-panel {\n    background-color: #1a1a1a;\n    border-top-color: #333;\n  }\n  \n  .section-title {\n    color: #666;\n  }\n  \n  .section-divider {\n    background-color: #333;\n  }\n  \n  .emoji-item:active {\n    background-color: #333;\n  }\n  \n  .preview-content {\n    background-color: #333;\n  }\n  \n  .preview-name {\n    color: #fff;\n  }\n}\n</style>", "import Component from 'D:/uniapp/vue3/components/emoji-panel/emoji-panel.vue'\nwx.createComponent(Component)"], "names": ["emojiList", "uni"], "mappings": ";;;AAsEA,MAAM,oBAAoB;AAE1B,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW,CAAC,UAAU,QAAQ,KAAK,SAAS;AAAA,IAC7C;AAAA,IACD,eAAe;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,WAAWA,2BAAU,aAAG,CAAE;AAAA,MAC1B,cAAc,CAAE;AAAA,MAChB,kBAAkB;AAAA,IACpB;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACL,KAAK,KAAK;AACR,UAAI,KAAK;AACP,aAAK,iBAAgB;AAAA,aAChB;AACL,aAAK,aAAY;AAAA,MACnB;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,mBAAmB;AACjB,UAAI;AACF,cAAM,iBAAiBC,cAAAA,MAAI,eAAe,iBAAiB;AAC3D,YAAI,gBAAgB;AAClB,gBAAM,aAAa,KAAK,MAAM,cAAc;AAC5C,cAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,iBAAK,eAAe,WAAW,MAAM,GAAG,KAAK,cAAc;AAAA,UAC7D;AAAA,QACF;AAAA,MACF,SAAS,GAAG;AACV,aAAK,eAAe;MACtB;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB;AACjB,UAAI;AACFA,sBAAG,MAAC,eAAe,mBAAmB,KAAK,UAAU,KAAK,YAAY,CAAC;AAAA,MACzE,SAAS,GAAG;AAAA,MAEZ;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,UAAI,CAAC;AAAO;AAGZA,oBAAAA,MAAI,gBAAgBA,oBAAI;AAGxB,WAAK,aAAY;AAGjB,WAAK,kBAAkB,KAAK;AAG5B,WAAK,MAAM,UAAU,KAAK;AAAA,IAC3B;AAAA;AAAA,IAGD,kBAAkB,OAAO;AACvB,UAAI,CAAC;AAAO;AAGZ,YAAM,gBAAgB,KAAK,aAAa,UAAU,UAAQ;AACxD,cAAM,SAAS,KAAK,OAAO,KAAK;AAChC,cAAM,UAAU,MAAM,OAAO,MAAM;AACnC,eAAO,WAAW;AAAA,MACpB,CAAC;AAGD,UAAI,kBAAkB,IAAI;AACxB,aAAK,aAAa,OAAO,eAAe,CAAC;AAAA,MAC3C;AAGA,WAAK,aAAa,QAAQ,KAAK;AAG/B,UAAI,KAAK,aAAa,SAAS,KAAK,gBAAgB;AAClD,aAAK,eAAe,KAAK,aAAa,MAAM,GAAG,KAAK,cAAc;AAAA,MACpE;AAGA,WAAK,iBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,aAAa,OAAO;AAClB,UAAI,CAAC,KAAK,iBAAiB,CAAC;AAAO;AACnC,WAAK,mBAAmB;AAAA,IACzB;AAAA;AAAA,IAGD,eAAe;AACb,WAAK,mBAAmB;AAAA,IACzB;AAAA;AAAA,IAGD,eAAe;AAAA,IAEf;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9LA,GAAG,gBAAgB,SAAS;"}