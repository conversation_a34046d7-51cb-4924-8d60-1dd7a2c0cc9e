"use strict";
const utils_request = require("../utils/request.js");
function getUserSocialInfo() {
  return utils_request.request.get("v3/user/social/info");
}
function updateUserSocialInfo(data) {
  return utils_request.request.post("v3/user/social/update", data);
}
function getSocialFollowList(data) {
  return utils_request.request.get("v3/user/social/follow/list", data);
}
function getSocialFansList(data) {
  return utils_request.request.get("v3/user/social/fans/list", data);
}
function followUser(data) {
  return utils_request.request.post("v3/user/social/follow", data);
}
function getUserHomepage(data) {
  return utils_request.request.get("v3/user/social/homepage", data, {
    noAuth: true
    // 不需要登录验证，但登录用户会获得更多功能
  });
}
function getVisitorDetails(data = {}) {
  return utils_request.request.get("v3/user/social/visitors/details", data);
}
function getTagsWithCategories() {
  return utils_request.request.get("v3/user/social/tags/all");
}
function getMyTags() {
  return utils_request.request.get("v3/user/social/tags/my");
}
function updateUserTags(tag_ids) {
  return utils_request.request.post("v3/user/social/tags/update", { label_ids: tag_ids });
}
function getMyDynamicList(data) {
  return utils_request.request.get("v3/dynamic/my_list", data);
}
function getOtherUserDynamicList(userId, data) {
  return utils_request.request.get(`v3/dynamic/user_list/${userId}`, data, {
    noAuth: true
    // 支持未登录用户访问
  });
}
function publishDynamic(data) {
  return utils_request.request.post("v3/dynamic/publish", data);
}
function deleteDynamic(id) {
  return utils_request.request.post(`v3/dynamic/delete/${id}`);
}
function likeDynamic(data) {
  return utils_request.request.post("v3/dynamic/like", data);
}
function vote(data) {
  return utils_request.request.post("v3/dynamic/vote", data);
}
function getDynamicDetail(id) {
  return utils_request.request.get(`v3/dynamic/detail/${id}`, {}, {
    noAuth: true
  });
}
function reportDynamic(reason, dynamic_id, uid, content, image) {
  return utils_request.request.post("v3/dynamic/report", {
    reason,
    dynamic_id,
    uid,
    content,
    image
  });
}
function getTopicList(data) {
  return utils_request.request.get("v3/dynamic/topic/list", data);
}
function getTopicDetail(id) {
  return utils_request.request.get(`v3/dynamic/topic/detail/${id}`, {}, {
    noAuth: true
  });
}
function getTopicDynamicList(data) {
  return utils_request.request.get(`v3/dynamic/topic/dynamic/${data.topic_id}`, {
    page: data.page || 1,
    limit: data.limit || 10,
    type: data.type || "latest",
    keyword: data.keyword || ""
  }, {
    noAuth: true
  });
}
function getLikeDynamicList(userId, data) {
  return utils_request.request.get(`v3/dynamic/like_list/${userId}`, data, {
    noAuth: true
    // 支持未登录用户访问
  });
}
function getCommentsList(target_id, data = {}) {
  return utils_request.request.get(`v3/comment/list/${parseInt(target_id) || 0}`, {
    ...data,
    page: parseInt(data.page) || 1,
    limit: parseInt(data.limit) || 10,
    sort_type: parseInt(data.sort_type) || 0
  }, {
    noAuth: true
  });
}
function getCommentReplies(data) {
  const params = {
    ...data,
    parent_id: parseInt(data.parent_id) || 0,
    // 一级评论ID
    page: parseInt(data.page) || 1,
    limit: parseInt(data.limit) || 3,
    // 每次只加载少量数据，让用户可以分批查看
    sort_type: parseInt(data.sort_type) || 1
    // 默认按时间排序
  };
  return utils_request.request.get("v3/comment/replies", params, {
    noAuth: true
  });
}
function getRealAuthInfo() {
  return utils_request.request.get("v3/user/real_auth/info");
}
function submitRealAuth(data) {
  return utils_request.request.post("v3/user/real_auth/submit", data);
}
function cancelRealAuth() {
  return utils_request.request.post("v3/user/real_auth/cancel");
}
function addComment(data) {
  const params = {
    ...data,
    type: parseInt(data.type) || 0,
    target_id: parseInt(data.target_id) || 0,
    reply_id: parseInt(data.reply_id) || 0
  };
  return utils_request.request.post("v3/comment/add", params);
}
function deleteComment(id) {
  return utils_request.request.post(`v3/comment/delete/${parseInt(id) || 0}`);
}
function likeComment(id) {
  return utils_request.request.post(`v3/comment/like/${parseInt(id) || 0}`);
}
function unlikeComment(id) {
  return utils_request.request.post(`v3/comment/unlike/${parseInt(id) || 0}`);
}
function getDynamicList(data) {
  return utils_request.request.get("v3/dynamic/list", data, {
    noAuth: true
  });
}
function updatePrivacySettings(data) {
  return utils_request.request.post("v3/user/social/privacy", data);
}
function updateFlowSettings(data) {
  return utils_request.request.post("v3/user/social/flow", data);
}
function getCircleList(data) {
  return utils_request.request.get("v3/circle/list", data, {
    noAuth: true
  });
}
function getCircleDetail(id) {
  return utils_request.request.get(`v3/circle/detail/${id}`, {}, {
    noAuth: true
  });
}
function getHotCircles() {
  return utils_request.request.get("v3/circle/hot", {}, {
    noAuth: true
  });
}
function createCircle(data) {
  return utils_request.request.post("v3/circle/create", data);
}
function joinCircle(data) {
  return utils_request.request.post("v3/circle/join", data);
}
function exitCircle(data) {
  return utils_request.request.post("v3/circle/exit", data);
}
function getJoinedCircles(data) {
  return utils_request.request.get("v3/circle/joined", data);
}
function getCircleMemberList(data) {
  return utils_request.request.get("v3/circle/member/list", data, {
    noAuth: true
  });
}
function kickCircleMember(data) {
  return utils_request.request.post("v3/circle/member/kick", data);
}
function setCircleMemberRole(data) {
  return utils_request.request.post("v3/circle/member/set_role", data);
}
function muteCircleMember(data) {
  return utils_request.request.post("v3/circle/member/mute", data);
}
function unmuteCircleMember(data) {
  return utils_request.request.post("v3/circle/member/unmute", data);
}
function getCircleDynamicList(data) {
  return utils_request.request.get("v3/circle/dynamic/list", data, {
    noAuth: true
  });
}
function searchUsers(data) {
  return utils_request.request.get("v3/user/social/search_users", data);
}
function publishTreeHoleBox(data) {
  return utils_request.request.post("v3/treehole/publish", data);
}
function drawTreeHoleBox(data = {}) {
  return utils_request.request.post("v3/treehole/draw", data);
}
function getMyTreeHoleBoxList(data) {
  return utils_request.request.get("v3/treehole/my_list", data);
}
function getMyDrawnBoxList(data) {
  return utils_request.request.get("v3/treehole/drawn_list", data);
}
function getTreeHoleBoxDetail(id) {
  return utils_request.request.get(`v3/treehole/detail/${id}`);
}
function responseTreeHoleBox(data) {
  return utils_request.request.post("v3/treehole/response", data);
}
function getTreeHoleResponseList(data) {
  return utils_request.request.get("v3/treehole/response_list", data);
}
function returnTreeHoleBox(draw_id) {
  return utils_request.request.post(`v3/treehole/return/${draw_id}`);
}
exports.addComment = addComment;
exports.cancelRealAuth = cancelRealAuth;
exports.createCircle = createCircle;
exports.deleteComment = deleteComment;
exports.deleteDynamic = deleteDynamic;
exports.drawTreeHoleBox = drawTreeHoleBox;
exports.exitCircle = exitCircle;
exports.followUser = followUser;
exports.getCircleDetail = getCircleDetail;
exports.getCircleDynamicList = getCircleDynamicList;
exports.getCircleList = getCircleList;
exports.getCircleMemberList = getCircleMemberList;
exports.getCommentReplies = getCommentReplies;
exports.getCommentsList = getCommentsList;
exports.getDynamicDetail = getDynamicDetail;
exports.getDynamicList = getDynamicList;
exports.getHotCircles = getHotCircles;
exports.getJoinedCircles = getJoinedCircles;
exports.getLikeDynamicList = getLikeDynamicList;
exports.getMyDrawnBoxList = getMyDrawnBoxList;
exports.getMyDynamicList = getMyDynamicList;
exports.getMyTags = getMyTags;
exports.getMyTreeHoleBoxList = getMyTreeHoleBoxList;
exports.getOtherUserDynamicList = getOtherUserDynamicList;
exports.getRealAuthInfo = getRealAuthInfo;
exports.getSocialFansList = getSocialFansList;
exports.getSocialFollowList = getSocialFollowList;
exports.getTagsWithCategories = getTagsWithCategories;
exports.getTopicDetail = getTopicDetail;
exports.getTopicDynamicList = getTopicDynamicList;
exports.getTopicList = getTopicList;
exports.getTreeHoleBoxDetail = getTreeHoleBoxDetail;
exports.getTreeHoleResponseList = getTreeHoleResponseList;
exports.getUserHomepage = getUserHomepage;
exports.getUserSocialInfo = getUserSocialInfo;
exports.getVisitorDetails = getVisitorDetails;
exports.joinCircle = joinCircle;
exports.kickCircleMember = kickCircleMember;
exports.likeComment = likeComment;
exports.likeDynamic = likeDynamic;
exports.muteCircleMember = muteCircleMember;
exports.publishDynamic = publishDynamic;
exports.publishTreeHoleBox = publishTreeHoleBox;
exports.reportDynamic = reportDynamic;
exports.responseTreeHoleBox = responseTreeHoleBox;
exports.returnTreeHoleBox = returnTreeHoleBox;
exports.searchUsers = searchUsers;
exports.setCircleMemberRole = setCircleMemberRole;
exports.submitRealAuth = submitRealAuth;
exports.unlikeComment = unlikeComment;
exports.unmuteCircleMember = unmuteCircleMember;
exports.updateFlowSettings = updateFlowSettings;
exports.updatePrivacySettings = updatePrivacySettings;
exports.updateUserSocialInfo = updateUserSocialInfo;
exports.updateUserTags = updateUserTags;
exports.vote = vote;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/social.js.map
