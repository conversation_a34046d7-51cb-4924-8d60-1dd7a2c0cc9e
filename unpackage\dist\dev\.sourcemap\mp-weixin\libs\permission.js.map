{"version": 3, "file": "permission.js", "sources": ["libs/permission.js"], "sourcesContent": ["import Cache from '@/utils/cache';\n\n/**\n * 判断传入的 key 是否在数组 arr 中存在\n * @param {string} key - 待判断的字符串\n * @returns {boolean} - 返回布尔值，表示是否有权限\n */\nfunction ActivePermission(key) {\n\t// seckill 秒杀 bargain 砍价 combination 拼团\n\tconst config = Cache.get('BASIC_CONFIG') || {};\n\tlet arr = (config && config.site_func) || ['seckill', 'bargain', 'combination']; // 定义一个数组，包含三种类型\n\tlet index = arr.indexOf(key); // 获取 key 在数组中的索引\n\tif (index > -1) {\n\t\t// 如果索引大于 -1，说明 key 存在于数组中\n\t\treturn true; // 有权限\n\t} else {\n\t\treturn false; // 无权限\n\t}\n}\n\n\nexport default ActivePermission;"], "names": ["<PERSON><PERSON>"], "mappings": ";;AAOA,SAAS,iBAAiB,KAAK;AAE9B,QAAM,SAASA,YAAK,MAAC,IAAI,cAAc,KAAK,CAAA;AAC5C,MAAI,MAAO,UAAU,OAAO,aAAc,CAAC,WAAW,WAAW,aAAa;AAC9E,MAAI,QAAQ,IAAI,QAAQ,GAAG;AAC3B,MAAI,QAAQ,IAAI;AAEf,WAAO;AAAA,EACT,OAAQ;AACN,WAAO;AAAA,EACP;AACF;;"}