<view class="tabbar df"><view wx:if="{{a}}" class="{{['tabbar-add', 'df', 'bfh', z && 'fade-in', A && 'fade-out']}}" catchtouchmove="{{B}}" bindtap="{{C}}"><view class="content-wrapper"><view class="add-header"><view class="header-content"><view class="add-title">发布内容 <text class="add-plus">+</text></view><view class="add-subtitle">{{b}}</view></view><view class="header-image"><image src="{{c}}" mode="aspectFit"></image></view></view><view class="card-container" catchtap="{{x}}" catchtouchmove="{{y}}"><view class="{{['card', 'cream-card', f]}}" catchtap="{{g}}" bindtouchstart="{{h}}" bindtouchend="{{i}}"><view class="card-left"><image src="{{d}}"></image></view><view class="card-content"><view class="card-title">发布图文</view><view class="card-subtitle">分享精彩瞬间</view></view><view class="card-right"><image src="{{e}}"></image></view></view><view class="two-column-container"><view class="{{['two-column-card', 'video-card', k]}}" catchtap="{{l}}" bindtouchstart="{{m}}" bindtouchend="{{n}}"><view class="card-content-left"><view class="two-column-card-title">发视频</view><view class="two-column-card-desc">记录精彩瞬间</view></view><view class="card-content-right"><image src="{{j}}"></image></view></view><view class="{{['two-column-card', 'audio-card', p]}}" catchtap="{{q}}" bindtouchstart="{{r}}" bindtouchend="{{s}}"><view class="card-content-left"><view class="two-column-card-title">发音频</view><view class="two-column-card-desc">分享好声音</view></view><view class="card-content-right"><image src="{{o}}"></image></view></view></view><view class="card mint-card" catchtap="{{w}}"><view class="card-left"><image src="{{t}}"></image></view><view class="card-content"><view class="card-title">创建圈子</view><view class="card-subtitle">一起交流讨论</view></view><view class="card-right"><image src="{{v}}"></image></view></view></view></view></view><view class="{{['tabbar-box', 'df', ah && 'bfw tb-bs']}}"><view wx:if="{{D}}" class="{{['tabbar-item', 'df', F]}}" bindtap="{{G}}" bindtouchstart="{{H}}" bindtouchend="{{I}}"><image class="icon" src="{{E}}"></image></view><view wx:if="{{J}}" class="{{['tabbar-item', 'df', O]}}" bindtap="{{P}}" bindtouchstart="{{Q}}" bindtouchend="{{R}}"><image class="icon" src="{{K}}"></image><view wx:if="{{L}}" class="msg" style="{{'padding:' + N}}">{{M}}</view></view><view class="{{['tabbar-item', 'df', V]}}" bindtap="{{W}}" bindtouchstart="{{X}}" bindtouchend="{{Y}}"><view class="add df" style="{{'background:' + T + ';' + ('transform:' + U)}}"><image src="{{S}}"></image></view></view><view wx:if="{{Z}}" class="tabbar-item df" bindtap="{{ad}}"><image class="icon" src="{{aa}}"></image><view wx:if="{{ab}}" class="msg">{{ac}}</view></view><view wx:if="{{ae}}" class="tabbar-item df" bindtap="{{ag}}"><image class="icon" src="{{af}}"></image></view></view></view>