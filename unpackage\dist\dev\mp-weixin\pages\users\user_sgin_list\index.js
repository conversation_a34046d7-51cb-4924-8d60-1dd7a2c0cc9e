"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_user = require("../../../api/user.js");
const libs_login = require("../../../libs/login.js");
const common_assets = require("../../../common/assets.js");
const emptyPage = () => "../../../components/emptyPage/emptyPage.js";
const authorize = () => "../../../components/Authorize.js";
const _sfc_main = {
  components: {
    emptyPage,
    authorize
  },
  data() {
    return {
      tipsTitle: "",
      loading: false,
      loadend: false,
      loadtitle: this.$t(`加载更多`),
      page: 1,
      limit: 8,
      signList: [],
      isAuto: false,
      //没有授权的不会自动授权
      isShowAuth: false
      //是否隐藏授权
    };
  },
  computed: common_vendor.mapGetters(["isLogin"]),
  watch: {
    isLogin: {
      handler: function(newV, oldV) {
        if (newV) {
          this.getSignMoneList();
        }
      },
      deep: true
    }
  },
  onLoad() {
    if (this.isLogin) {
      this.getSignMoneList();
    } else {
      libs_login.toLogin();
    }
  },
  onPullDownRefresh() {
    this.page = 1;
    this.loadend = false;
    this.signList = [];
    this.getSignMoneList();
    setTimeout(() => {
      common_vendor.index.stopPullDownRefresh();
    }, 1e3);
  },
  onReachBottom: function() {
    if (!this.loadend && !this.loading) {
      this.getSignMoneList();
    }
  },
  methods: {
    // 显示提示信息
    opTipsPopup(msg) {
      this.tipsTitle = msg;
      this.$refs.tipsPopup.open();
      setTimeout(() => {
        this.$refs.tipsPopup.close();
      }, 2e3);
    },
    /**
     *
     * 授权回调
    */
    onLoadFun: function() {
      this.getSignMoneList();
    },
    // 授权关闭
    authColse: function(e) {
      this.isShowAuth = e;
    },
    /**
       * 获取签到记录列表
      */
    getSignMoneList: function() {
      let that = this;
      if (that.loading)
        return;
      if (that.loadend)
        return;
      that.loading = true;
      that.loadtitle = "";
      api_user.getSignMonthList({ page: that.page, limit: that.limit }).then((res) => {
        common_vendor.index.__f__("log", "at pages/users/user_sgin_list/index.vue:160", "签到记录API响应:", res);
        let list = res.data || [];
        let loadend = list.length < that.limit;
        if (that.page === 1) {
          that.signList = list;
        } else {
          that.signList = that.signList.concat(list);
        }
        that.$set(that, "signList", that.signList);
        that.page++;
        that.loadend = loadend;
        that.loading = false;
        that.loadtitle = loadend ? that.$t(`我也是有底线的`) : that.$t(`加载更多`);
        common_vendor.index.__f__("log", "at pages/users/user_sgin_list/index.vue:177", "处理后的签到列表:", that.signList);
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/users/user_sgin_list/index.vue:179", "获取签到记录失败:", err);
        that.loading = false;
        that.loadtitle = that.$t(`加载更多`);
      });
    },
    // 去签到
    goToSign() {
      common_vendor.index.navigateTo({
        url: "/pages/users/user_sgin/index"
      });
    }
  }
};
if (!Array) {
  const _component_emptyPage = common_vendor.resolveComponent("emptyPage");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_emptyPage + _easycom_uni_popup2)();
}
const _easycom_uni_popup = () => "../../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({}, {
    e: $data.loading && $data.signList.length === 0
  }, $data.loading && $data.signList.length === 0 ? {} : $data.signList.length > 0 ? common_vendor.e({
    g: common_vendor.f($data.signList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.month),
        b: common_vendor.f(item.list, (record, recordIndex, i1) => {
          return {
            a: common_vendor.t(_ctx.$t(record.title)),
            b: common_vendor.t(record.add_time),
            c: common_vendor.t(record.number),
            d: recordIndex
          };
        }),
        c: index
      };
    }),
    h: common_assets._imports_0$11,
    i: $data.loading
  }, $data.loading ? {} : $data.loadend ? {} : {
    k: common_vendor.o((...args) => $options.getSignMoneList && $options.getSignMoneList(...args))
  }, {
    j: $data.loadend
  }) : {
    l: common_vendor.o($options.goToSign),
    m: common_vendor.p({
      title: "暂无签到记录",
      description: "快去签到获得奖励吧",
      buttonText: "去签到"
    })
  }, {
    f: $data.signList.length > 0,
    n: common_vendor.t($data.tipsTitle),
    o: common_vendor.sr("tipsPopup", "72f8cce4-1"),
    p: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/users/user_sgin_list/index.js.map
