{"version": 3, "file": "routine_phone.js", "sources": ["pages/users/components/login_mobile/routine_phone.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvcGFnZXMvdXNlcnMvY29tcG9uZW50cy9sb2dpbl9tb2JpbGUvcm91dGluZV9waG9uZS52dWU"], "sourcesContent": ["<template>\n\t<view v-if=\"isPhoneBox\">\n\t\t<view class=\"mobile-bg\"></view>\n\t\t<view class=\"mobile-mask animated\" :class=\"{slideInUp:isUp}\">\n\t\t\t<view class=\"info-box\">\n\t\t\t\t<image :src=\"logoUrl\"></image>\n\t\t\t\t<view class=\"title\">{{$t(`获取授权`)}}</view>\n\t\t\t\t<view class=\"sub-title\">{{$t(`获取手机号授权`)}}</view>\n\t\t\t</view>\n\t\t\t<view class=\"btn-submit df\" open-type=\"getPhoneNumber\" @getphonenumber=\"getphonenumber\">{{$t(`获取手机号`)}}</view>\n\t\t</view>\n\t</view>\n</template>\n<script>\n\tconst app = getApp();\n\timport Routine from '@/libs/routine';\n\timport {\n\tloginMobile,\n\tgetCodeApi,\n\tgetUserInfo\n} from \"@/api/user\";\nimport {\n\tgetUserSocialInfo\n} from '@/api/social.js';\n\timport {\n\t\tgetLogo,\n\t\tsilenceAuth,\n\t\troutineBindingPhone\n\t} from '@/api/public';\n\texport default {\n\t\tname: 'routine_phone',\n\t\tprops: {\n\t\t\tisPhoneBox: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false,\n\t\t\t},\n\t\t\tlogoUrl: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '',\n\t\t\t},\n\t\t\tauthKey: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '',\n\t\t\t}\n\t\t},\n\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tkeyCode: '',\n\t\t\t\taccount: '',\n\t\t\t\tcodeNum: '',\n\t\t\t\tisStatus: false\n\t\t\t}\n\t\t},\n\t\tmounted() {},\n\t\tmethods: {\n\t\t\t// #ifdef MP\n\t\t\t// 小程序获取手机号码\n\t\t\tgetphonenumber(e) {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: this.$t(`正在登录中`)\n\t\t\t\t});\n\t\t\t\tRoutine.getCode()\n\t\t\t\t\t.then(code => {\n\t\t\t\t\t\tthis.getUserPhoneNumber(e.detail.encryptedData, e.detail.iv, code);\n\t\t\t\t\t})\n\t\t\t\t\t.catch(error => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t});\n\t\t\t},\n\t\t\t// 小程序获取手机号码回调\n\t\t\tgetUserPhoneNumber(encryptedData, iv, code) {\n\t\t\t\troutineBindingPhone({\n\t\t\t\t\t\tencryptedData: encryptedData,\n\t\t\t\t\t\tiv: iv,\n\t\t\t\t\t\tcode: code,\n\t\t\t\t\t\tspread_spid: app.globalData.spid,\n\t\t\t\t\t\tspread_code: app.globalData.code,\n\t\t\t\t\t\tkey: this.authKey\n\t\t\t\t\t})\n\t\t\t\t\t.then(res => {\n\t\t\t\t\t\tlet time = res.data.expires_time - this.$Cache.time();\n\t\t\t\t\t\tthis.$store.commit('LOGIN', {\n\t\t\t\t\t\t\ttoken: res.data.token,\n\t\t\t\t\t\t\ttime: time\n\t\t\t\t\t\t});\n\t\t\t\t\t\t// this.getUserInfo();\n\t\t\t\t\t\tthis.$emit('loginSuccess', {\n\t\t\t\t\t\t\tisStatus: true,\n\t\t\t\t\t\t\tnew_user: res.data.userInfo.new_user\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t\t.catch(res => {\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t});\n\t\t\t},\n\t\t\t/**\n\t\t\t * 获取个人用户信息\n\t\t\t */\n\t\t\tgetUserInfo: function() {\n\t\t\t\tlet that = this;\n\t\t\t\t// 先获取基础用户信息\n\t\t\t\tgetUserInfo().then(res => {\n\t\t\t\t\tthat.userInfo = res.data\n\t\t\t\t\tthat.$store.commit(\"SETUID\", res.data.uid);\n\t\t\t\t\t\n\t\t\t\t\t// 再获取用户社交信息（拓展信息）\n\t\t\t\t\tgetUserSocialInfo()\n\t\t\t\t\t\t.then((socialRes) => {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t// 更新用户社交信息到Vuex\n\t\t\t\t\t\t\tif (socialRes.data) {\n\t\t\t\t\t\t\t\tthat.$store.commit(\"UPDATE_USERINFO\", socialRes.data);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tthat.isStatus = true\n\t\t\t\t\t\t\tthis.close(res.data.new_user || 0)\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(() => {\n\t\t\t\t\t\t\t// 即使社交信息获取失败，仍然继续\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tthat.isStatus = true\n\t\t\t\t\t\t\tthis.close(res.data.new_user || 0)\n\t\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\t// #endif\n\t\t\tclose(new_user) {\n\t\t\t\tthis.$emit('close', {\n\t\t\t\t\tisStatus: this.isStatus,\n\t\t\t\t\tnew_user\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.mobile-bg {\n\t\tposition: fixed;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\tz-index: 19;\n\t}\n\n\t.mobile-mask {\n\t\tz-index: 20;\n\t\tposition: fixed;\n\t\tleft: 0;\n\t\tbottom: 0;\n\t\twidth: 100%;\n\t\tpadding: 67rpx 30rpx;\n\t\tbackground: #fff;\n\t\tborder-radius: 40rpx 40rpx 0 0;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.info-box {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.info-box image {\n\t\twidth: 150rpx;\n\t\theight: 150rpx;\n\t\tborder-radius: 20rpx;\n\t}\n\n\t.info-box .title {\n\t\tmargin-top: 30rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tfont-size: 40rpx;\n\t\tfont-weight: 700;\n\t\tcolor: #000;\n\t}\n\n\t.info-box .sub-title {\n\t\tfont-size: 26rpx;\n\t\tcolor: #999;\n\t}\n\n\t.btn-submit {\n\t\tmargin-top: 60rpx;\n\t\twidth: 100%;\n\t\theight: 100rpx;\n\t\tline-height: 100rpx;\n\t\ttext-align: center;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 700;\n\t\tbackground: #000;\n\t\tcolor: #fff;\n\t\tborder-radius: 100rpx;\n\t\tjustify-content: center;\n\t}\n\n\t.df {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.animated {\n\t\tanimation-duration: .4s;\n\t}\n\n\t@keyframes slideInUp {\n\t\tfrom {\n\t\t\ttransform: translate3d(0, 100%, 0);\n\t\t}\n\t\tto {\n\t\t\ttransform: translate3d(0, 0, 0);\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/uniapp/vue3/pages/users/components/login_mobile/routine_phone.vue'\nwx.createComponent(Component)"], "names": ["uni", "Routine", "routineBindingPhone", "getUserInfo", "getUserSocialInfo"], "mappings": ";;;;;;AAcC,MAAM,MAAM,OAAM;AAelB,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA,IACN,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EAED,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,IACX;AAAA,EACA;AAAA,EACD,UAAU;AAAA,EAAE;AAAA,EACZ,SAAS;AAAA;AAAA,IAGR,eAAe,GAAG;AACjBA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,KAAK,GAAG,OAAO;AAAA,MACvB,CAAC;AACDC,mBAAAA,QAAQ,QAAQ,EACd,KAAK,UAAQ;AACb,aAAK,mBAAmB,EAAE,OAAO,eAAe,EAAE,OAAO,IAAI,IAAI;AAAA,OACjE,EACA,MAAM,WAAS;AACfD,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACF;AAAA;AAAA,IAED,mBAAmB,eAAe,IAAI,MAAM;AAC3CE,qCAAoB;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa,IAAI,WAAW;AAAA,QAC5B,aAAa,IAAI,WAAW;AAAA,QAC5B,KAAK,KAAK;AAAA,OACV,EACA,KAAK,SAAO;AACZ,YAAI,OAAO,IAAI,KAAK,eAAe,KAAK,OAAO;AAC/C,aAAK,OAAO,OAAO,SAAS;AAAA,UAC3B,OAAO,IAAI,KAAK;AAAA,UAChB;AAAA,QACD,CAAC;AAED,aAAK,MAAM,gBAAgB;AAAA,UAC1B,UAAU;AAAA,UACV,UAAU,IAAI,KAAK,SAAS;AAAA,SAC5B;AAAA,OACD,EACA,MAAM,SAAO;AACbF,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAID,aAAa,WAAW;AACvB,UAAI,OAAO;AAEXG,2BAAa,EAAC,KAAK,SAAO;AACzB,aAAK,WAAW,IAAI;AACpB,aAAK,OAAO,OAAO,UAAU,IAAI,KAAK,GAAG;AAGzCC,qCAAkB,EAChB,KAAK,CAAC,cAAc;AACpBJ,wBAAG,MAAC,YAAW;AAEf,cAAI,UAAU,MAAM;AACnB,iBAAK,OAAO,OAAO,mBAAmB,UAAU,IAAI;AAAA,UACrD;AAEA,eAAK,WAAW;AAChB,eAAK,MAAM,IAAI,KAAK,YAAY,CAAC;AAAA,SACjC,EACA,MAAM,MAAM;AAEZA,wBAAG,MAAC,YAAW;AACf,eAAK,WAAW;AAChB,eAAK,MAAM,IAAI,KAAK,YAAY,CAAC;AAAA,QAClC,CAAC;AAAA,MACH,CAAC;AAAA,IACD;AAAA,IAED,MAAM,UAAU;AACf,WAAK,MAAM,SAAS;AAAA,QACnB,UAAU,KAAK;AAAA,QACf;AAAA,OACA;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;ACrID,GAAG,gBAAgB,SAAS;"}