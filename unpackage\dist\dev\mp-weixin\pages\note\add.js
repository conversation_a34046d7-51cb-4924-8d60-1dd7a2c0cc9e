"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const common_assets = require("../../common/assets.js");
const jcRecord = () => "../../components/jc-record/jc-record.js";
const emojiPanel = () => "../../components/emoji-panel/emoji-panel.js";
const emptyPage = () => "../../components/emptyPage/emptyPage.js";
const VoteComponent = () => "../../components/vote-component/vote-component.js";
const _sfc_main = {
  components: {
    jcRecord,
    emojiPanel,
    emptyPage,
    VoteComponent
  },
  data() {
    return {
      navbarHeight: 0,
      // 导航栏总高度
      capsuleInfo: null,
      // 胶囊按钮信息
      isUser: false,
      id: 0,
      content: "",
      type: 2,
      // 默认显示为图片动态样式，鼓励用户上传图片 (1-纯文本,2-图片,3-视频,4-音频)
      order_id: "",
      order_type: 0,
      goods: [],
      imgs: [],
      video: {
        high: 0,
        wide: 0,
        url: "",
        cover: ""
      },
      audio: {
        cover: "",
        url: "",
        name: "",
        intro: "",
        size: 0
      },
      adds: {
        name: "",
        address: "",
        latitude: "",
        longitude: ""
      },
      circle: {
        id: "",
        name: "",
        avatar: ""
      },
      activity: {
        id: "",
        name: "",
        img: ""
      },
      circleList: [],
      goodsList: [],
      activityList: [],
      kw: "",
      tipsTitle: "",
      keyboardHeight: 0,
      userKeyword: "",
      topicKeyword: "",
      userList: [],
      topicList: [],
      showMoreOptions: false,
      isRecording: false,
      canvasStatus: false,
      canvasWidth: 0,
      canvasHeight: 0,
      // 表情面板相关
      showEmojiPanel: false,
      emojiMap: {},
      // 表情映射
      commentImage: null,
      // 评论图片
      // 草稿相关
      draftKey: "note_draft",
      saveTimer: null,
      // 用于防抖的定时器
      contentProcessTimer: null,
      // 内容处理防抖定时器
      searchThrottleTimer: null,
      // 搜索节流定时器
      isPublished: false,
      // 标识是否已成功发布
      autoFocus: true,
      // 自动聚焦，页面加载时自动调起输入法
      // 弹窗管理
      currentPopup: null,
      // 当前打开的弹窗
      // H5键盘监听方法
      handleWindowResize: null,
      // 已选话题
      selectedTopics: [],
      // 投票相关数据
      showVote: false,
      // 控制投票组件显示
      voteData: {
        title: "",
        options: ["", ""],
        // 默认两个选项
        expireTime: 7
        // 默认7天后过期
      },
      // @用户相关数据
      showUserSearch: false,
      // 显示用户搜索面板
      searchUserKeyword: "",
      // 搜索用户关键词
      searchUserList: [],
      // 搜索到的用户列表
      mentionUsers: [],
      // 已@的用户列表
      cursorPosition: 0,
      // 当前光标位置
      isSearchingUser: false,
      // 是否正在搜索用户
      isEditing: false,
      // 是否处于编辑模式
      mentionPanelClosed: false,
      // 用户是否手动关闭了@用户面板
      excludedMentions: [],
      // 被排除的@文本（用户关闭面板后的@文本）
      // 性能优化缓存
      _segmentCache: null,
      // contentSegments缓存
      _segmentCacheKey: ""
      // 缓存键
    };
  },
  computed: {
    // 将内容分段，区分普通文本和@用户文本（优化版本）
    contentSegments() {
      if (!this.content)
        return [];
      const cacheKey = `${this.content}_${JSON.stringify(this.mentionUsers)}_${JSON.stringify(this.excludedMentions)}`;
      if (this._segmentCache && this._segmentCacheKey === cacheKey) {
        return this._segmentCache;
      }
      const segments = [];
      const mentionPattern = /@([^\s@]+)/g;
      let lastIndex = 0;
      let match;
      const userNicknameSet = new Set(this.mentionUsers.map((user) => user.nickname));
      const excludedMap = /* @__PURE__ */ new Map();
      this.excludedMentions.forEach((excluded) => {
        excludedMap.set(`${excluded.position}_${excluded.text}`, true);
      });
      while ((match = mentionPattern.exec(this.content)) !== null) {
        const beforeText = this.content.substring(lastIndex, match.index);
        if (beforeText) {
          segments.push({
            text: beforeText,
            isMention: false
          });
        }
        const mentionText = match[0];
        const nickname = match[1];
        const isKnownUser = userNicknameSet.has(nickname);
        const isExcluded = excludedMap.has(`${match.index}_${mentionText}`);
        segments.push({
          text: mentionText,
          isMention: !isExcluded && isKnownUser,
          // 如果被排除，则不显示为@用户
          isKnownUser
        });
        lastIndex = match.index + match[0].length;
      }
      const remainingText = this.content.substring(lastIndex);
      if (remainingText) {
        segments.push({
          text: remainingText,
          isMention: false
        });
      }
      const result = segments.length > 0 ? segments : [{
        text: this.content,
        isMention: false
      }];
      this._segmentCache = result;
      this._segmentCacheKey = cacheKey;
      return result;
    },
    // 判断内容是否为空
    isContentEmpty() {
      const hasContent = this.content && this.content.trim();
      const hasImages = this.imgs && this.imgs.length > 0;
      const hasVideo = this.video && this.video.url;
      const hasAudio = this.audio && this.audio.url;
      const hasVote = this.showVote && this.voteData.title;
      return !hasContent && !hasImages && !hasVideo && !hasAudio && !hasVote;
    },
    // 发布按钮文本
    publishButtonText() {
      if (this.isContentEmpty) {
        return "发布";
      }
      if (this.video.url) {
        return "发布";
      } else if (this.audio.url) {
        return "发布";
      } else if (this.imgs.length > 0) {
        return "发布";
      } else if (this.showVote) {
        return "发布";
      } else {
        return "发布";
      }
    },
    // 发布按钮样式（根据胶囊按钮位置动态计算）
    publishButtonStyle() {
      const style = {};
      common_vendor.index.__f__("log", "at pages/note/add.vue:917", "开始计算发布按钮位置...");
      common_vendor.index.__f__("log", "at pages/note/add.vue:918", "胶囊按钮信息:", this.capsuleInfo);
      if (this.capsuleInfo && this.capsuleInfo.width > 0) {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        const capsule = this.capsuleInfo;
        const pixelRatio = systemInfo.pixelRatio || 2;
        const screenWidth = systemInfo.screenWidth;
        const windowWidth = systemInfo.windowWidth;
        const capsuleRightDistance = screenWidth - capsule.right;
        const capsuleRightDistanceRpx = capsuleRightDistance * 2;
        const safeDistanceRpx = 20;
        const finalMarginRpx = capsuleRightDistanceRpx + safeDistanceRpx;
        style.marginRight = finalMarginRpx + "rpx";
        style.paddingRight = "0";
        common_vendor.index.__f__("log", "at pages/note/add.vue:946", "胶囊按钮位置计算结果:", {
          systemInfo: {
            screenWidth,
            windowWidth,
            pixelRatio
          },
          capsule: {
            width: capsule.width,
            height: capsule.height,
            left: capsule.left,
            right: capsule.right,
            top: capsule.top,
            bottom: capsule.bottom
          },
          calculation: {
            capsuleRightDistance,
            capsuleRightDistanceRpx,
            safeDistanceRpx,
            finalMarginRpx
          },
          finalStyle: style
        });
      } else {
        style.marginRight = "40rpx";
        style.paddingRight = "0";
        common_vendor.index.__f__("log", "at pages/note/add.vue:972", "未获取到胶囊按钮信息，使用默认边距");
      }
      return style;
    },
    // 投票预览信息
    previewVoteInfo() {
      if (!this.showVote || !this.voteData.title) {
        return null;
      }
      const options = (this.voteData.options || []).map((opt) => (opt || "").trim()).filter((opt) => opt);
      if (options.length < 2) {
        return null;
      }
      return {
        vote: {
          id: "preview",
          title: this.voteData.title
        },
        options: options.map((text, index) => ({
          id: index + 1,
          option_text: text,
          percent: 0
        })),
        user_selected: null,
        total: 0
      };
    }
  },
  created() {
    if (this.$store.state.app.token) {
      this.isUser = true;
      const userInfo = common_vendor.index.getStorageSync("userInfo") || {};
      this.audio.cover = userInfo.avatar || "";
    }
    this.initNavbar();
    this.initEmojiMap();
  },
  onLoad(option) {
    let that = this;
    if (that.$store.state.app.token) {
      that.isUser = true;
    }
    if (option.id) {
      that.id = option.id;
    }
    if (option.type) {
      that.type = option.type;
    }
    if (option.order_id) {
      that.order_id = option.order_id;
    }
    if (option.order_type) {
      that.order_type = option.order_type;
    }
    that.loadTopics();
    if (typeof common_vendor.index.onKeyboardHeightChange === "function") {
      common_vendor.index.onKeyboardHeightChange((res) => {
        that.keyboardHeight = res.height;
        common_vendor.index.__f__("log", "at pages/note/add.vue:1062", "键盘高度变化:", res.height);
      });
    }
    that.loadDraft();
    setTimeout(() => {
      that.autoFocus = true;
    }, 300);
  },
  onShow() {
    if (this.$store.state.app.token) {
      this.isUser = true;
    } else {
      this.isUser = false;
    }
  },
  // 监听页面隐藏
  onHide() {
    if (!this.isPublished) {
      this.saveDraft();
    }
  },
  // 监听页面卸载
  onUnload() {
    if (!this.isPublished) {
      this.saveDraft();
    }
    if (this.saveTimer) {
      clearTimeout(this.saveTimer);
      this.saveTimer = null;
    }
    if (this.contentProcessTimer) {
      clearTimeout(this.contentProcessTimer);
      this.contentProcessTimer = null;
    }
    if (this.searchThrottleTimer) {
      clearTimeout(this.searchThrottleTimer);
      this.searchThrottleTimer = null;
    }
    this.clearSegmentCache();
  },
  methods: {
    // 初始化导航栏
    initNavbar() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight || 20;
      try {
        this.capsuleInfo = common_vendor.index.getMenuButtonBoundingClientRect();
        common_vendor.index.__f__("log", "at pages/note/add.vue:1154", "获取胶囊按钮信息:", this.capsuleInfo);
        if (this.capsuleInfo && this.capsuleInfo.width > 0) {
          const capsuleTop = this.capsuleInfo.top - this.statusBarHeight;
          this.titleBarHeight = this.capsuleInfo.height + capsuleTop * 2;
          common_vendor.index.__f__("log", "at pages/note/add.vue:1161", "胶囊按钮信息详细:", {
            width: this.capsuleInfo.width,
            height: this.capsuleInfo.height,
            left: this.capsuleInfo.left,
            right: this.capsuleInfo.right,
            top: this.capsuleInfo.top,
            bottom: this.capsuleInfo.bottom
          });
          common_vendor.index.__f__("log", "at pages/note/add.vue:1169", "计算的标题栏高度:", this.titleBarHeight);
          this.$nextTick(() => {
            common_vendor.index.__f__("log", "at pages/note/add.vue:1173", "胶囊按钮信息设置完成，触发视图更新");
            this.$forceUpdate();
          });
        } else {
          this.titleBarHeight = 44;
          common_vendor.index.__f__("log", "at pages/note/add.vue:1178", "未获取到有效的胶囊按钮信息，使用默认标题栏高度");
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/add.vue:1181", "获取胶囊按钮信息失败:", e);
        this.titleBarHeight = 44;
      }
      common_vendor.index.__f__("log", "at pages/note/add.vue:1202", "导航栏配置:", {
        statusBarHeight: this.statusBarHeight,
        titleBarHeight: this.titleBarHeight,
        platform: systemInfo.platform
      });
    },
    // 返回上一页
    goBack() {
      if (this.content.trim() || this.imgs.length > 0 || this.video.url || this.audio.url) {
        common_vendor.index.showModal({
          title: "提示",
          content: "是否保存草稿？",
          success: (res) => {
            if (res.confirm) {
              this.saveDraft();
            }
            common_vendor.index.navigateBack();
          }
        });
      } else {
        common_vendor.index.navigateBack();
      }
    },
    // 跳转到完善资料页面
    navigateToComplete() {
      common_vendor.index.navigateTo({
        url: "/pages/center/means"
      });
    },
    // 处理发布按钮点击
    handlePublish() {
      if (this.isContentEmpty) {
        this.opTipsPopup("请添加一些内容再发布哦～");
        return;
      }
      this.saveDynamic(1);
    },
    // 初始化表情映射
    initEmojiMap() {
      try {
        const emojiMapStr = common_vendor.index.getStorageSync("emoji_map");
        if (emojiMapStr) {
          this.emojiMap = JSON.parse(emojiMapStr);
          common_vendor.index.__f__("log", "at pages/note/add.vue:1254", "表情映射加载成功，总数:", Object.keys(this.emojiMap).length);
        } else {
          common_vendor.index.__f__("log", "at pages/note/add.vue:1258", "未找到表情映射，将使用默认映射");
          this.emojiMap = {};
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/add.vue:1262", "初始化表情映射失败", e);
        this.emojiMap = {};
      }
    },
    // 内容输入
    contentInput(e) {
      const value = e.detail.value;
      const cursor = e.detail.cursor || 0;
      this.content = value;
      this.cursorPosition = cursor;
      this.debouncedContentProcess(value, cursor);
    },
    // 防抖处理内容变化
    debouncedContentProcess(value, cursor) {
      if (this.contentProcessTimer) {
        clearTimeout(this.contentProcessTimer);
      }
      this.contentProcessTimer = setTimeout(() => {
        this.cleanupExcludedMentions();
        this.checkMentionInput(value, cursor);
        this.parseMentionUsersFromContent();
        this.autoSaveDraft();
      }, 300);
    },
    // textarea获取焦点
    onTextareaFocus() {
      common_vendor.index.__f__("log", "at pages/note/add.vue:1306", "textarea获取焦点");
      this.showEmojiPanel = false;
      this.isEditing = true;
      this.$nextTick(() => {
        setTimeout(() => {
          common_vendor.index.__f__("log", "at pages/note/add.vue:1312", "当前键盘高度:", this.keyboardHeight);
        }, 300);
      });
    },
    // textarea失去焦点
    onTextareaBlur() {
      common_vendor.index.__f__("log", "at pages/note/add.vue:1319", "textarea失去焦点");
      setTimeout(() => {
        if (!this.showUserSearch) {
          this.isEditing = false;
        }
      }, 200);
      setTimeout(() => {
        if (this.keyboardHeight > 0) {
          common_vendor.index.__f__("log", "at pages/note/add.vue:1329", "重置键盘高度");
          this.keyboardHeight = 0;
        }
      }, 100);
    },
    // 获取笔记详情
    dynamicInfo() {
      let that = this;
      common_vendor.index.request({
        url: this.$store.state.app.apiUrl + "/note/details",
        method: "GET",
        data: { id: that.id },
        header: {
          "Authorization": "Bearer " + this.$store.state.app.token
        },
        success: (res) => {
          if (res.data && res.data.status === 200) {
            const data = res.data.data;
            that.id = data.id;
            that.type = data.type;
            that.content = data.content;
            that.circle.id = data.circle_id;
            that.circle.name = data.circle_name;
            that.circle.avatar = data.circle_avatar;
            that.activity.id = data.activity_id;
            that.activity.name = data.activity_name;
            that.activity.img = data.activity_img;
            that.adds.name = data.adds_name;
            that.adds.address = data.adds;
            that.adds.latitude = data.lat;
            that.adds.longitude = data.lng;
            if (that.type == 1) {
              that.imgs = data.imgs;
            } else if (that.type == 2) {
              that.video.high = data.video.high;
              that.video.wide = data.video.wide;
              that.video.url = data.video.url;
              that.video.cover = data.video.cover;
            } else if (that.type == 3) {
              that.audio.cover = data.audio.cover;
              that.audio.url = data.audio.url;
              that.audio.name = data.audio.name;
              that.audio.intro = data.audio.intro;
              that.audio.size = data.audio.size;
            }
            that.order_id = data.order_id;
            that.order_type = data.order_type;
            if (that.order_id) {
              that.goods = data.goods;
            }
          }
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/note/add.vue:1388", "获取笔记详情失败:", err);
        }
      });
    },
    // 获取订单商品
    noteOrderGoods() {
      let that = this;
      common_vendor.index.request({
        url: this.$store.state.app.apiUrl + "/order/goods",
        method: "GET",
        data: { id: that.order_id },
        header: {
          "Authorization": "Bearer " + this.$store.state.app.token
        },
        success: (res) => {
          if (res.data && res.data.status === 200) {
            that.goods = res.data.data;
          }
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/note/add.vue:1411", "获取订单商品失败:", err);
        }
      });
    },
    // 获取圈子列表
    circleDynamic() {
      let that = this;
      common_vendor.index.request({
        url: this.$store.state.app.apiUrl + "/circle/list",
        method: "GET",
        header: {
          "Authorization": "Bearer " + this.$store.state.app.token
        },
        success: (res) => {
          if (res.data && res.data.status === 200) {
            that.circleList = res.data.data;
          }
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/note/add.vue:1433", "获取圈子列表失败:", err);
        }
      });
    },
    // 获取话题列表
    loadTopics() {
      let that = this;
      common_vendor.index.showLoading({
        title: "加载中",
        mask: true
      });
      const params = {
        keyword: that.topicKeyword,
        page: 1,
        limit: 20
      };
      api_social.getTopicList(params).then((res) => {
        common_vendor.index.hideLoading();
        if (res.status === 200) {
          that.topicList = res.data.list || [];
        } else {
          common_vendor.index.showToast({
            title: res.msg || "获取话题失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/note/add.vue:1472", "获取话题列表失败:", err);
        common_vendor.index.showToast({
          title: "获取话题失败",
          icon: "none"
        });
      });
    },
    // 搜索话题
    searchTopics() {
      this.loadTopics();
    },
    // 保存笔记
    saveDynamic(status) {
      let that = this;
      if (that.content.trim() === "") {
        return that.opTipsPopup("请输入内容");
      }
      let finalType;
      if (that.type == 3 && that.video.url) {
        finalType = 3;
      } else if (that.type == 4 && that.audio.url) {
        finalType = 4;
      } else if (that.imgs.length > 0) {
        finalType = 2;
      } else {
        finalType = 1;
      }
      if (that.type == 3) {
        if (!that.video.url) {
          return that.opTipsPopup("视频笔记需添加一个视频哦！");
        }
      }
      if (that.type == 4) {
        if (!that.audio.cover) {
          return that.opTipsPopup("音频封面不能为空哦！");
        }
        if (!that.audio.url) {
          return that.opTipsPopup("音频不能为空哦！");
        }
      }
      let vote = null;
      let vote_options = null;
      if (that.showVote) {
        if (!that.voteData.title || !that.voteData.title.trim()) {
          return that.opTipsPopup("请输入投票标题");
        }
        const options = (that.voteData.options || []).map((opt) => (opt || "").trim()).filter((opt) => opt);
        if (options.length < 2) {
          return that.opTipsPopup("至少需要2个投票选项");
        }
        vote = { title: that.voteData.title.trim() };
        vote_options = options;
      }
      common_vendor.index.showLoading({
        title: status ? "发布中..." : "保存中...",
        mask: true
      });
      const parsedContent = that.parseEmojiContent(that.content);
      let params = {
        id: that.id,
        content: parsedContent,
        // 使用解析后的内容
        type: finalType,
        // 使用实际判断的最终类型
        location_name: that.adds.name,
        latitude: that.adds.latitude,
        longitude: that.adds.longitude,
        circle_id: that.circle.id,
        status,
        // 0-草稿，1-发布
        order_id: that.order_id,
        order_type: that.order_type,
        goods: that.goods.map((item) => item.id)
      };
      if (vote && vote_options) {
        params.vote = vote;
        params.vote_options = vote_options;
      }
      if (that.commentImage) {
        if (finalType === 1 || finalType === 2) {
          finalType = 2;
          if (!that.imgs)
            that.imgs = [];
          that.imgs.push({
            url: that.commentImage,
            wide: 0,
            high: 0
          });
          params.type = finalType;
        }
      }
      if (finalType == 2) {
        params.images = that.imgs.map((img) => img.url);
      } else if (finalType == 3) {
        if (that.video.url) {
          params.video = that.video.url;
          params.video_cover = that.video.cover || "";
          params.video_width = that.video.wide || 1280;
          params.video_height = that.video.high || 720;
          common_vendor.index.__f__("log", "at pages/note/add.vue:1594", "视频数据:", {
            url: that.video.url,
            cover: that.video.cover,
            width: that.video.wide,
            height: that.video.high
          });
        }
      } else if (finalType == 4) {
        params.audio = that.audio.url;
        params.audio_cover = that.audio.cover;
        params.audio_title = that.audio.name;
      }
      common_vendor.index.__f__("log", "at pages/note/add.vue:1609", "发布动态参数:", params);
      common_vendor.index.__f__("log", "at pages/note/add.vue:1610", "当前类型:", that.type);
      common_vendor.index.__f__("log", "at pages/note/add.vue:1611", "最终类型:", finalType);
      common_vendor.index.__f__("log", "at pages/note/add.vue:1612", "视频数据:", that.video);
      api_social.publishDynamic(params).then((res) => {
        common_vendor.index.hideLoading();
        that.opTipsPopup(res.msg || (res.status === 200 ? status ? "发布成功" : "保存成功" : status ? "发布失败" : "保存失败"));
        if (res.status === 200) {
          common_vendor.index.__f__("log", "at pages/note/add.vue:1624", "发布成功，准备跳转到个人中心");
          if (status === 1) {
            that.isPublished = true;
          }
          that.clearDraft();
          setTimeout(function() {
            common_vendor.index.__f__("log", "at pages/note/add.vue:1635", "开始跳转到个人中心");
            common_vendor.index.reLaunch({
              url: "/pages/index/center",
              success: function() {
                common_vendor.index.__f__("log", "at pages/note/add.vue:1639", "跳转到个人中心成功");
              },
              fail: function(err) {
                common_vendor.index.__f__("error", "at pages/note/add.vue:1642", "跳转到个人中心失败:", err);
                common_vendor.index.navigateTo({
                  url: "/pages/index/center"
                });
              }
            });
          }, 1e3);
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/note/add.vue:1654", "发布笔记失败:", err);
        that.opTipsPopup("发布失败，请重试");
      });
    },
    // 选择视频（使用新的分片上传方法）
    chooseVideo() {
      let that = this;
      if (that.imgs.length > 0) {
        that.imgs = [];
      }
      if (that.audio.url) {
        that.audio = {
          cover: "",
          url: "",
          name: "",
          intro: "",
          size: 0
        };
      }
      that.type = 3;
      that.$util.uploadVideoSimple({
        count: 1,
        sourceType: ["album", "camera"],
        maxDuration: 60,
        camera: "back",
        url: "upload/video",
        name: "file"
      }, function(res) {
        common_vendor.index.__f__("log", "at pages/note/add.vue:1692", "视频上传成功:", res);
        if (res.status === 200 && res.data && (res.data.url || res.data.src)) {
          that.video.url = res.data.url || res.data.src;
          that.video.high = res.data.high || res.data.h || 720;
          that.video.wide = res.data.wide || res.data.w || 1280;
          that.$forceUpdate();
          that.handleVideoCover();
        } else {
          common_vendor.index.__f__("error", "at pages/note/add.vue:1704", "视频上传响应格式错误:", res);
          that.opTipsPopup("视频上传失败：响应格式错误");
        }
      }, function(err) {
        common_vendor.index.__f__("error", "at pages/note/add.vue:1709", "视频上传失败:", err);
        that.opTipsPopup("视频上传失败，请重试");
      });
    },
    // 处理视频封面
    handleVideoCover() {
      let that = this;
      common_vendor.index.__f__("log", "at pages/note/add.vue:1719", "视频数据:", that.video);
      common_vendor.index.__f__("log", "at pages/note/add.vue:1720", "视频URL:", that.video.url);
      if (that.video.cover) {
        that.opTipsPopup("视频上传成功");
        return;
      }
      common_vendor.index.showModal({
        title: "视频上传成功",
        content: "是否需要添加视频封面？",
        confirmText: "添加封面",
        cancelText: "稍后添加",
        success: (res) => {
          if (res.confirm) {
            that.addImgClick(2);
          } else {
            that.opTipsPopup("视频上传成功，请记得添加封面");
          }
        }
      });
    },
    // 视频加载成功
    onVideoLoad(e) {
      common_vendor.index.__f__("log", "at pages/note/add.vue:1749", "视频加载成功:", e);
      this.opTipsPopup("视频加载成功");
    },
    // 视频加载错误
    onVideoError(e) {
      common_vendor.index.__f__("error", "at pages/note/add.vue:1755", "视频加载失败:", e);
      this.opTipsPopup("视频加载失败，请检查视频格式");
    },
    // 添加图片（支持多选）
    addImgClick(type) {
      let that = this;
      if (type == 1) {
        that.canvasStatus = true;
        that.$util.uploadImageChange({
          url: "upload/image",
          count: 9
          // 支持最多9张图片
        }, function(res) {
          if (that.video.url) {
            that.video = {
              high: 0,
              wide: 0,
              url: "",
              cover: ""
            };
          }
          if (that.audio.url) {
            that.audio = {
              cover: "",
              url: "",
              name: "",
              intro: "",
              size: 0
            };
          }
          that.type = 2;
          if (res.data && Array.isArray(res.data)) {
            res.data.forEach((img) => {
              that.imgs.push({
                url: img.url || img.src,
                wide: img.wide || img.w || 0,
                high: img.high || img.h || 0
              });
            });
          } else if (res.data && (res.data.url || res.data.src)) {
            that.imgs.push({
              url: res.data.url || res.data.src,
              wide: res.data.wide || res.data.w || 0,
              high: res.data.high || res.data.h || 0
            });
          } else if (res.url || res.src) {
            that.imgs.push({
              url: res.url || res.src,
              wide: res.wide || res.w || 0,
              high: res.high || res.h || 0
            });
          } else {
            common_vendor.index.__f__("error", "at pages/note/add.vue:1824", "图片数据格式错误:", res);
            that.opTipsPopup("图片上传失败：数据格式错误");
          }
          that.$forceUpdate();
        }, function(err) {
          common_vendor.index.__f__("error", "at pages/note/add.vue:1831", "图片上传失败:", err);
          that.opTipsPopup("图片上传失败，请重试");
        });
      } else {
        this.canvasStatus = true;
        that.$util.uploadImageChange({
          url: "upload/image",
          count: 1
          // 封面只选择一张
        }, function(res) {
          common_vendor.index.__f__("log", "at pages/note/add.vue:1842", "封面上传响应:", res);
          let coverUrl = "";
          if (res.data && res.data.url) {
            coverUrl = res.data.url;
          } else if (res.data && res.data.src) {
            coverUrl = res.data.src;
          } else if (res.url) {
            coverUrl = res.url;
          } else if (res.src) {
            coverUrl = res.src;
          }
          if (coverUrl) {
            if (type == 2) {
              that.video.cover = coverUrl;
              that.opTipsPopup("视频封面上传成功");
            } else if (type == 3) {
              that.audio.cover = coverUrl;
              that.opTipsPopup("音频封面上传成功");
            }
          } else {
            common_vendor.index.__f__("error", "at pages/note/add.vue:1866", "无法获取封面URL:", res);
            that.opTipsPopup("封面上传失败：无法获取图片URL");
          }
        }, function(err) {
          common_vendor.index.__f__("error", "at pages/note/add.vue:1870", "封面上传失败:", err);
          that.opTipsPopup("封面上传失败，请重试");
        });
      }
    },
    // 添加音频文件
    addFileClick() {
      let that = this;
      common_vendor.index.showActionSheet({
        itemList: ["录制音频", "选择音频文件"],
        success: function(res) {
          if (res.tapIndex === 0) {
            that.recordPopupClick(true);
          } else if (res.tapIndex === 1) {
            that.chooseAudioFile();
          }
        }
      });
    },
    // 选择音频文件
    chooseAudioFile() {
      let that = this;
      that.$util.uploadAudio({
        url: "upload/audio",
        name: "file"
      }, function(res) {
        if (that.imgs.length > 0) {
          that.imgs = [];
        }
        if (that.video.url) {
          that.video = {
            high: 0,
            wide: 0,
            url: "",
            cover: ""
          };
        }
        that.type = 4;
        that.audio.url = res.data.url;
        that.audio.name = res.data.name || "音频文件";
        that.audio.size = res.data.size || 0;
        if (!that.audio.intro) {
          that.audio.intro = "上传于" + (/* @__PURE__ */ new Date()).toLocaleString();
        }
        if (!that.audio.cover) {
          const userInfo = common_vendor.index.getStorageSync("USER_INFO") || {};
          that.audio.cover = userInfo.avatar || "/static/img/audio_default_cover.png";
        }
        that.opTipsPopup("音频上传成功");
      }, function(err) {
        common_vendor.index.__f__("error", "at pages/note/add.vue:1940", "音频上传失败:", err);
        that.opTipsPopup("音频上传失败，请重试");
      });
    },
    // 处理媒体按钮点击
    handleMediaClick(type) {
      switch (type) {
        case 7:
          this.addFileClick();
          break;
        case 8:
          this.votePopupClick(true);
          break;
        default:
          common_vendor.index.__f__("log", "at pages/note/add.vue:1955", "未知的媒体类型:", type);
      }
    },
    // 录音弹窗控制
    recordPopupClick(isOpen) {
      if (isOpen) {
        this.openPopup("record", this.$refs.recordPopup);
      } else {
        this.$refs.recordPopup.close();
        this.currentPopup = null;
      }
    },
    // 录音弹窗变化事件
    onRecordPopupChange(e) {
      if (!e.show) {
        this.isRecording = false;
      }
    },
    // 录音完成事件
    onRecordFinish(audioPath) {
      common_vendor.index.__f__("log", "at pages/note/add.vue:1978", "录音完成:", audioPath);
      if (!audioPath) {
        this.opTipsPopup("录音失败，请重试");
        return;
      }
      this.handleAudioUpload(audioPath);
    },
    // 录音关闭事件
    onRecordClose() {
      common_vendor.index.__f__("log", "at pages/note/add.vue:1991", "录音组件关闭");
    },
    // 录音确认按钮点击事件
    handleRecordOk(audioPath) {
      common_vendor.index.__f__("log", "at pages/note/add.vue:1996", "录音确认:", audioPath);
      if (!audioPath) {
        this.opTipsPopup("录音无效，请重新录制");
        return;
      }
      this.handleAudioUpload(audioPath, true);
    },
    // 处理音频上传
    handleAudioUpload(audioPath, closePopup = false) {
      common_vendor.index.showLoading({
        title: "处理中...",
        mask: true
      });
      let that = this;
      that.$util.uploadAudio({
        url: "upload/audio",
        name: "file"
      }, function(res) {
        that.audio.url = res.data.url;
        if (!that.audio.name) {
          that.audio.name = res.data.name || "我的录音";
        }
        if (!that.audio.intro) {
          that.audio.intro = "录制于" + (/* @__PURE__ */ new Date()).toLocaleString();
        }
        if (!that.audio.cover) {
          const userInfo = common_vendor.index.getStorageSync("USER_INFO") || {};
          that.audio.cover = userInfo.avatar || "/static/img/audio_default_cover.png";
        }
        if (closePopup) {
          that.recordPopupClick(false);
        }
        common_vendor.index.hideLoading();
        that.opTipsPopup("录音上传成功");
      }, function() {
        common_vendor.index.hideLoading();
        that.opTipsPopup("录音上传失败，请重试");
      });
    },
    // 位置选择
    locationClick() {
      let that = this;
      common_vendor.index.showLoading({
        title: "正在打开位置选择...",
        mask: true
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.chooseLocation({
          success: function(res) {
            common_vendor.index.__f__("log", "at pages/note/add.vue:2068", "位置选择成功:", res);
            that.adds = {
              name: res.name || res.address || "未知位置",
              address: res.address || "",
              latitude: res.latitude || "",
              longitude: res.longitude || ""
            };
            that.opTipsPopup("位置添加成功");
          },
          fail: function(err) {
            common_vendor.index.__f__("error", "at pages/note/add.vue:2078", "位置选择失败:", err);
            if (err.errMsg) {
              if (err.errMsg.includes("auth") || err.errMsg.includes("permission")) {
                common_vendor.index.showModal({
                  title: "权限提示",
                  content: "需要获取位置权限才能选择位置，请在浏览器设置中允许位置访问",
                  confirmText: "我知道了",
                  showCancel: false
                });
              } else if (err.errMsg.includes("cancel")) {
                common_vendor.index.__f__("log", "at pages/note/add.vue:2091", "用户取消位置选择");
                return;
              } else if (err.errMsg.includes("system")) {
                common_vendor.index.showModal({
                  title: "系统提示",
                  content: "系统定位服务未开启，请在设置中开启定位服务",
                  confirmText: "我知道了",
                  showCancel: false
                });
              } else {
                that.opTipsPopup("位置选择失败，请重试");
              }
            } else {
              that.opTipsPopup("位置选择失败，请重试");
            }
          }
        });
      }, 300);
    },
    // 搜索商品
    searchClick(type = 0) {
      let that = this;
      if (type == 1 && that.goodsList.length) {
        return;
      }
      common_vendor.index.showLoading({
        title: "加载中",
        mask: true
      });
      const params = {
        keyword: that.kw,
        page: 1,
        limit: 20
      };
      common_vendor.index.request({
        url: that.$store.state.app.apiUrl + "/product/list",
        method: "GET",
        data: params,
        header: {
          "Authorization": "Bearer " + that.$store.state.app.token
        },
        success: (res) => {
          var _a;
          common_vendor.index.hideLoading();
          if (res.data && res.data.status === 200) {
            that.goodsList = res.data.data.list || [];
          } else {
            common_vendor.index.showToast({
              title: ((_a = res.data) == null ? void 0 : _a.msg) || "获取商品列表失败",
              icon: "none"
            });
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/note/add.vue:2156", "获取商品列表失败:", err);
          common_vendor.index.showToast({
            title: "获取商品列表失败",
            icon: "none"
          });
        }
      });
    },
    // 商品弹窗控制
    shopPopupClick(isOpen) {
      if (isOpen) {
        this.openPopup("shop", this.$refs.shopPopup);
        this.searchClick(1);
      } else {
        this.$refs.shopPopup.close();
        this.currentPopup = null;
      }
    },
    // 圈子弹窗控制
    upPopupClick(isOpen) {
      if (isOpen) {
        this.openPopup("circle", this.$refs.upPopup);
        this.loadCircles();
      } else {
        this.$refs.upPopup.close();
        this.currentPopup = null;
      }
    },
    // 商品选择
    goodsClick(e) {
      const index = e.currentTarget.dataset.idx;
      this.goodsList[index].selected = !this.goodsList[index].selected;
      let selectedGoods = [];
      for (let item of this.goodsList) {
        if (item.selected) {
          selectedGoods.push(item);
        }
      }
      this.goods = selectedGoods;
    },
    // 圈子选择
    circleClick(e) {
      const index = e.currentTarget.dataset.idx;
      if (this.circleList[index].id == this.circle.id) {
        this.circle.id = "";
        this.circle.name = "";
        this.circle.avatar = "";
      } else {
        this.circle = {
          id: this.circleList[index].id,
          name: this.circleList[index].name,
          avatar: this.circleList[index].avatar
        };
      }
    },
    // 图片预览
    previewClick(e) {
      const index = e.currentTarget.dataset.i;
      let urls = [];
      for (let img of this.imgs) {
        urls.push(img.url);
      }
      common_vendor.index.previewImage({
        current: index,
        urls
      });
    },
    // 显示操作提示
    opTipsPopup(msg) {
      common_vendor.index.showToast({
        title: msg,
        icon: "none",
        duration: 2e3
      });
    },
    // 处理多媒体点击
    handleMediaClick(type) {
      if (type > 5) {
        this.showMoreOptions = false;
      }
      switch (type) {
        case 1:
        case 2:
          this.chooseMedia();
          break;
        case 3:
          this.topicPopupClick(true);
          break;
        case 4:
          this.userPopupClick(true);
          break;
        case 7:
          this.type = 4;
          if (!this.audio.url) {
            this.recordPopupClick(true);
          }
          break;
        case 8:
          this.votePopupClick(true);
          break;
      }
    },
    // 处理多媒体长按 - 已删除长按设置封面功能
    handleMediaLongPress(type) {
    },
    // 切换更多选项菜单
    toggleMoreOptions() {
      if (this.showMoreOptions) {
        this.showMoreOptions = false;
        this.currentPopup = null;
      } else {
        this.closeAllPopups();
        this.showMoreOptions = true;
        this.currentPopup = "more";
      }
    },
    // 活动弹窗控制
    activityPopupClick(isOpen) {
      if (isOpen) {
        this.openPopup("activity", this.$refs.activityPopup);
        this.loadActivities();
      } else {
        this.$refs.activityPopup.close();
        this.currentPopup = null;
      }
    },
    // 加载活动列表
    loadActivities() {
      let that = this;
      common_vendor.index.showLoading({
        title: "加载中",
        mask: true
      });
      const params = {
        page: 1,
        limit: 20
      };
      common_vendor.index.request({
        url: that.$store.state.app.apiUrl + "/activity/list",
        method: "GET",
        data: params,
        header: {
          "Authorization": "Bearer " + that.$store.state.app.token
        },
        success: (res) => {
          var _a;
          common_vendor.index.hideLoading();
          if (res.data && res.data.status === 200) {
            that.activityList = res.data.data.list || [];
          } else {
            common_vendor.index.showToast({
              title: ((_a = res.data) == null ? void 0 : _a.msg) || "获取活动列表失败",
              icon: "none"
            });
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/note/add.vue:2346", "获取活动列表失败:", err);
          common_vendor.index.showToast({
            title: "获取活动列表失败",
            icon: "none"
          });
        }
      });
    },
    // 搜索用户
    searchUsers() {
      this.loadUsers();
    },
    // 选择用户
    selectUser(e) {
      const index = e.currentTarget.dataset.idx;
      const user = this.userList[index];
      this.content += ` @${user.nickname} `;
      this.$refs.userPopup.close();
      this.opTipsPopup(`已添加@${user.nickname}`);
    },
    // 选择话题
    selectTopic(e) {
      const index = e.currentTarget.dataset.idx;
      const topic = this.topicList[index];
      this.content += ` #${topic.name}# `;
      this.$refs.topicPopup.close();
      this.opTipsPopup(`已添加话题#${topic.name}#`);
    },
    // 删除已上传文件
    delClick(type, index) {
      let that = this;
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除吗？",
        success: (res) => {
          if (res.confirm) {
            switch (type) {
              case 0:
                that.imgs.splice(index, 1);
                if (that.imgs.length === 0) {
                  if (that.video.url) {
                    that.type = 3;
                  } else if (that.audio.url) {
                    that.type = 4;
                  } else {
                    that.type = 1;
                  }
                }
                break;
              case 1:
                that.video = {
                  high: 0,
                  wide: 0,
                  url: "",
                  cover: ""
                };
                if (that.imgs.length > 0) {
                  that.type = 2;
                } else if (that.audio.url) {
                  that.type = 4;
                } else {
                  that.type = 1;
                }
                break;
              case 2:
                that.video.cover = "";
                break;
              case 3:
                that.audio.cover = "";
                break;
              case 4:
                that.audio = {
                  cover: "",
                  url: "",
                  name: "",
                  intro: "",
                  size: 0
                };
                if (that.imgs.length > 0) {
                  that.type = 2;
                } else if (that.video.url) {
                  that.type = 3;
                } else {
                  that.type = 1;
                }
                break;
            }
          }
        }
      });
    },
    // 调整图片顺序
    onSort(index, direction) {
      let that = this;
      if (direction === 0 && index > 0) {
        const temp = that.imgs[index];
        that.imgs[index] = that.imgs[index - 1];
        that.imgs[index - 1] = temp;
      } else if (direction === 1 && index < that.imgs.length - 1) {
        const temp = that.imgs[index];
        that.imgs[index] = that.imgs[index + 1];
        that.imgs[index + 1] = temp;
      }
      that.imgs = [...that.imgs];
    },
    // 加载圈子列表
    loadCircles() {
      let that = this;
      common_vendor.index.showLoading({
        title: "加载中",
        mask: true
      });
      const params = {
        page: 1,
        limit: 50
        // 增加限制数量，获取更多已加入的圈子
      };
      api_social.getJoinedCircles(params).then((res) => {
        common_vendor.index.hideLoading();
        if (res.status === 200) {
          that.circleList = (res.data.list || []).map((item) => {
            return {
              id: item.circle_id || item.id,
              name: item.circle_name || item.name,
              avatar: item.circle_avatar || item.avatar,
              description: item.circle_description || item.description,
              member_count: item.member_count || 0
            };
          });
        } else {
          common_vendor.index.showToast({
            title: res.msg || "获取圈子列表失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/note/add.vue:2522", "获取圈子列表失败:", err);
        common_vendor.index.showToast({
          title: "获取圈子列表失败",
          icon: "none"
        });
      });
    },
    // 活动选择
    activityClick(e) {
      const index = e.currentTarget.dataset.idx;
      if (this.activityList[index].id == this.activity.id) {
        this.activity = {
          id: "",
          name: "",
          img: ""
        };
      } else {
        this.activity = {
          id: this.activityList[index].id,
          name: this.activityList[index].name,
          img: this.activityList[index].img
        };
      }
    },
    // 隐藏键盘
    hideKeyboard() {
      common_vendor.index.hideKeyboard();
      this.showEmojiPanel = false;
    },
    // 快速插入文本
    handleQuickInsert(text) {
      this.content += text;
    },
    // 用户弹窗控制
    userPopupClick(isOpen) {
      if (isOpen) {
        this.openPopup("user", this.$refs.userPopup);
        this.loadUsers();
      } else {
        this.$refs.userPopup.close();
        this.currentPopup = null;
      }
    },
    // 话题弹窗控制
    topicPopupClick(status) {
      if (status) {
        this.openPopup("topic", this.$refs.topicPopup);
        this.loadTopics();
      } else {
        this.$refs.topicPopup.close();
        this.currentPopup = null;
      }
    },
    // 话题点击
    topicClick(topic) {
      this.selectedTopics = [{
        id: topic.id,
        name: topic.title,
        icon: topic.icon || "/static/img/topic-icon.png"
      }];
      this.$refs.topicPopup.close();
    },
    // 移除已选话题
    removeSelectedTopic(index) {
      this.selectedTopics.splice(index, 1);
    },
    // 用户点击
    userClick(user) {
      const userTag = `@${user.nickname} `;
      this.content += userTag;
      this.$refs.userPopup.close();
    },
    // 加载用户列表
    loadUsers() {
      let that = this;
      common_vendor.index.showLoading({
        title: "加载中",
        mask: true
      });
      const params = {
        keyword: that.userKeyword,
        page: 1,
        limit: 20
      };
      api_social.getSocialFollowList(params).then((res) => {
        common_vendor.index.hideLoading();
        if (res.status === 200) {
          that.userList = (res.data.list || []).map((item) => {
            return {
              id: item.follow_uid,
              nickname: item.follow_nickname,
              avatar: item.follow_avatar
            };
          });
        } else {
          common_vendor.index.showToast({
            title: res.msg || "获取好友列表失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/note/add.vue:2656", "获取好友列表失败:", err);
        common_vendor.index.showToast({
          title: "获取好友列表失败",
          icon: "none"
        });
      });
    },
    // 删除图片
    deleteImage(index) {
      this.images.splice(index, 1);
    },
    // 上传图片
    uploadImage() {
      let that = this;
      that.$util.uploadImageChange("upload/image", function(res) {
        that.images.push(res.data.url);
      });
    },
    // 发布笔记
    publishNote() {
      if (!this.title.trim()) {
        return this.opTipsPopup("请输入笔记标题");
      }
    },
    // 切换表情面板
    toggleEmojiPanel() {
      if (this.showEmojiPanel) {
        this.showEmojiPanel = false;
        this.currentPopup = null;
      } else {
        this.closeAllPopups();
        this.showEmojiPanel = true;
        this.currentPopup = "emoji";
      }
    },
    // 选择表情
    onSelectEmoji(emoji) {
      const emojiText = emoji.phrase || emoji.alt || emoji.value || "";
      if (emojiText) {
        this.content += emojiText;
      }
    },
    // 选择GIF表情
    onSelectGif(gif) {
      if (gif && gif.url) {
        this.commentImage = gif.url;
        common_vendor.index.showToast({
          title: "GIF表情已添加",
          icon: "none"
        });
      }
    },
    // 删除表情
    onDeleteEmoji() {
      if (this.content.length > 0) {
        this.content = this.content.substring(0, this.content.length - 1);
      }
    },
    // 发送评论
    onSendComment() {
      this.saveDynamic(1);
    },
    // 解析表情内容
    parseEmojiContent(content) {
      if (!content)
        return "";
      let parsedContent = content;
      Object.keys(this.emojiMap).forEach((key) => {
        const decodedKey = decodeURIComponent(key);
        const url = this.emojiMap[key];
        const regex = new RegExp(decodedKey.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "g");
        parsedContent = parsedContent.replace(regex, `<img class="emoji" src="${url}" alt="${decodedKey}" />`);
      });
      return parsedContent;
    },
    // 保存草稿
    saveDraft() {
      if (this.isPublished) {
        return;
      }
      if (!this.content.trim() && this.imgs.length === 0 && !this.video.url && !this.audio.url) {
        return;
      }
      const draft = {
        content: this.content,
        type: this.type,
        imgs: this.imgs,
        video: this.video,
        audio: this.audio,
        adds: this.adds,
        circle: this.circle,
        activity: this.activity,
        timestamp: Date.now()
      };
      try {
        common_vendor.index.setStorageSync(this.draftKey, JSON.stringify(draft));
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/add.vue:2784", "保存草稿失败:", e);
      }
    },
    // 加载草稿
    loadDraft() {
      try {
        const draftStr = common_vendor.index.getStorageSync(this.draftKey);
        if (draftStr) {
          const draft = JSON.parse(draftStr);
          const now = Date.now();
          const draftTime = draft.timestamp || 0;
          const hours24 = 24 * 60 * 60 * 1e3;
          if (now - draftTime > hours24) {
            this.clearDraft();
            return;
          }
          common_vendor.index.showModal({
            title: "发现草稿",
            content: "是否恢复上次未完成的内容？",
            success: (res) => {
              if (res.confirm) {
                this.content = draft.content || "";
                this.type = draft.type || 2;
                this.imgs = draft.imgs || [];
                this.video = draft.video || {
                  high: 0,
                  wide: 0,
                  url: "",
                  cover: ""
                };
                this.audio = draft.audio || {
                  cover: "",
                  url: "",
                  name: "",
                  intro: "",
                  size: 0
                };
                this.adds = draft.adds || {
                  name: "",
                  address: "",
                  latitude: "",
                  longitude: ""
                };
                this.circle = draft.circle || {
                  id: "",
                  name: "",
                  avatar: ""
                };
                this.activity = draft.activity || {};
                common_vendor.index.showToast({
                  title: "草稿已恢复",
                  icon: "success"
                });
              } else {
                this.clearDraft();
              }
            }
          });
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/add.vue:2854", "加载草稿失败:", e);
      }
    },
    // 清除草稿
    clearDraft() {
      try {
        common_vendor.index.removeStorageSync(this.draftKey);
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/add.vue:2864", "清除草稿失败:", e);
      }
    },
    // 关闭所有弹窗
    closeAllPopups() {
      var _a, _b, _c, _d, _e, _f;
      if (this.currentPopup) {
        this.currentPopup = null;
      }
      (_a = this.$refs.shopPopup) == null ? void 0 : _a.close();
      (_b = this.$refs.upPopup) == null ? void 0 : _b.close();
      (_c = this.$refs.activityPopup) == null ? void 0 : _c.close();
      (_d = this.$refs.topicPopup) == null ? void 0 : _d.close();
      (_e = this.$refs.userPopup) == null ? void 0 : _e.close();
      (_f = this.$refs.recordPopup) == null ? void 0 : _f.close();
      this.showEmojiPanel = false;
      this.showMoreOptions = false;
    },
    // 打开指定弹窗
    openPopup(popupName, popupRef) {
      if (this.currentPopup && this.currentPopup !== popupName) {
        this.closeAllPopups();
      }
      this.currentPopup = popupName;
      setTimeout(() => {
        if (popupRef) {
          popupRef.open();
        }
      }, this.currentPopup !== popupName ? 200 : 0);
    },
    // 投票弹窗控制
    votePopupClick(isOpen) {
      if (isOpen) {
        this.openPopup("vote", this.$refs.votePopup);
      } else {
        this.$refs.votePopup.close();
        this.currentPopup = null;
      }
    },
    // 添加投票选项
    addVoteOption() {
      if (this.voteData.options.length < 10) {
        this.voteData.options.push("");
      } else {
        this.opTipsPopup("最多只能添加10个选项");
      }
    },
    // 删除投票选项
    deleteVoteOption(index) {
      if (this.voteData.options.length > 2) {
        this.voteData.options.splice(index, 1);
      } else {
        this.opTipsPopup("至少需要2个选项");
      }
    },
    // 确认投票
    confirmVote() {
      if (!this.voteData.title.trim()) {
        return this.opTipsPopup("请输入投票标题");
      }
      let hasEmptyOption = false;
      for (let option of this.voteData.options) {
        if (!option.trim()) {
          hasEmptyOption = true;
          break;
        }
      }
      if (hasEmptyOption) {
        return this.opTipsPopup("选项内容不能为空");
      }
      this.showVote = true;
      this.votePopupClick(false);
    },
    // 删除投票
    deleteVote() {
      common_vendor.index.__f__("log", "at pages/note/add.vue:2961", "deleteVote方法被调用");
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除投票吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.__f__("log", "at pages/note/add.vue:2967", "用户确认删除投票");
            this.showVote = false;
            this.voteData = {
              title: "",
              options: ["", ""],
              expireTime: 7
            };
            common_vendor.index.showToast({
              title: "投票已删除",
              icon: "success"
            });
          }
        }
      });
    },
    onRecordShow() {
    },
    // ==================== @用户相关方法 ====================
    // 检测@用户输入
    checkMentionInput(content, cursor) {
      const beforeCursor = content.substring(0, cursor);
      const atIndex = beforeCursor.lastIndexOf("@");
      if (atIndex !== -1) {
        const afterAt = beforeCursor.substring(atIndex + 1);
        if (!afterAt.includes(" ")) {
          if (afterAt.length === 0) {
            this.mentionPanelClosed = false;
            this.searchUserKeyword = "";
            this.searchUsersForMention();
          } else if (!this.mentionPanelClosed) {
            this.searchUserKeyword = afterAt;
            this.searchUsersForMention();
          }
        } else {
          this.hideUserSearch();
        }
      } else {
        this.hideUserSearch();
      }
    },
    // 搜索用户用于@功能（节流版本）
    searchUsersForMention() {
      if (this.searchThrottleTimer) {
        clearTimeout(this.searchThrottleTimer);
      }
      this.searchThrottleTimer = setTimeout(() => {
        this.performUserSearch();
      }, 200);
    },
    // 执行用户搜索
    async performUserSearch() {
      if (this.isSearchingUser)
        return;
      common_vendor.index.__f__("log", "at pages/note/add.vue:3042", "开始搜索用户，关键词:", this.searchUserKeyword);
      this.isSearchingUser = true;
      this.showUserSearch = true;
      try {
        if (this.searchUserKeyword.trim()) {
          const res = await api_social.searchUsers({
            keyword: this.searchUserKeyword.trim(),
            page: 1,
            limit: 20
          });
          common_vendor.index.__f__("log", "at pages/note/add.vue:3055", "搜索用户接口响应:", res);
          if (res.status === 200) {
            this.searchUserList = (res.data.list || []).map((item) => {
              let avatarUrl = item.avatar;
              if (avatarUrl && !avatarUrl.startsWith("http")) {
                avatarUrl = avatarUrl.startsWith("/") ? `${this.$baseUrl}${avatarUrl}` : `${this.$baseUrl}/${avatarUrl}`;
              }
              return {
                uid: item.uid,
                nickname: item.nickname,
                avatar: avatarUrl,
                is_follow: false
                // 搜索结果默认不是关注用户
              };
            });
            common_vendor.index.__f__("log", "at pages/note/add.vue:3073", "处理后的搜索用户列表:", this.searchUserList);
          } else {
            this.searchUserList = [];
            common_vendor.index.__f__("log", "at pages/note/add.vue:3076", "搜索用户接口响应异常:", res);
          }
        } else {
          const response = await api_social.getSocialFollowList({
            page: 1,
            limit: 50
          });
          common_vendor.index.__f__("log", "at pages/note/add.vue:3085", "关注用户接口响应:", response);
          if (response.status === 200 && response.data && response.data.list) {
            this.searchUserList = response.data.list.map((item) => {
              let avatarUrl = item.follow_avatar;
              if (avatarUrl && !avatarUrl.startsWith("http")) {
                avatarUrl = avatarUrl.startsWith("/") ? `${this.$baseUrl}${avatarUrl}` : `${this.$baseUrl}/${avatarUrl}`;
              }
              return {
                uid: item.follow_uid,
                nickname: item.follow_nickname,
                avatar: avatarUrl,
                is_follow: true
                // 标记为已关注
              };
            });
            common_vendor.index.__f__("log", "at pages/note/add.vue:3104", "处理后的关注用户列表:", this.searchUserList);
          } else {
            this.searchUserList = [];
            common_vendor.index.__f__("log", "at pages/note/add.vue:3107", "关注用户接口响应异常:", response);
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/add.vue:3111", "搜索用户失败:", error);
        this.searchUserList = [];
      } finally {
        this.isSearchingUser = false;
        common_vendor.index.__f__("log", "at pages/note/add.vue:3115", "搜索完成，面板状态:", {
          showUserSearch: this.showUserSearch,
          searchUserList: this.searchUserList,
          isSearchingUser: this.isSearchingUser
        });
      }
    },
    // 选择用户进行@
    selectUserForMention(user) {
      const beforeCursor = this.content.substring(0, this.cursorPosition);
      const afterCursor = this.content.substring(this.cursorPosition);
      const atIndex = beforeCursor.lastIndexOf("@");
      let newContent;
      let newCursorPosition;
      if (atIndex !== -1) {
        const afterAt = beforeCursor.substring(atIndex + 1);
        if (afterAt.includes(" ")) {
          const mentionText = `@${user.nickname} `;
          newContent = beforeCursor + mentionText + afterCursor;
          newCursorPosition = this.cursorPosition + mentionText.length;
        } else {
          const mentionText = `@${user.nickname} `;
          newContent = beforeCursor.substring(0, atIndex) + mentionText + afterCursor;
          newCursorPosition = atIndex + mentionText.length;
        }
      } else {
        const mentionText = `@${user.nickname} `;
        newContent = beforeCursor + mentionText + afterCursor;
        newCursorPosition = this.cursorPosition + mentionText.length;
      }
      this.content = newContent;
      this.cursorPosition = newCursorPosition;
      if (!this.mentionUsers.find((u) => u.uid === user.uid)) {
        this.mentionUsers.push({
          uid: user.uid,
          nickname: user.nickname,
          avatar: user.avatar
        });
      }
      if (atIndex !== -1) {
        this.excludedMentions = this.excludedMentions.filter(
          (excluded) => excluded.position !== atIndex
        );
      }
      this.mentionPanelClosed = false;
      this.hideUserSearch();
      this.autoSaveDraft();
    },
    // 隐藏用户搜索面板
    hideUserSearch() {
      this.showUserSearch = false;
      this.searchUserKeyword = "";
      this.searchUserList = [];
    },
    // 清理无效的排除记录
    cleanupExcludedMentions() {
      const originalLength = this.excludedMentions.length;
      this.excludedMentions = this.excludedMentions.filter((excluded) => {
        const actualText = this.content.substring(excluded.position, excluded.position + excluded.text.length);
        return actualText === excluded.text;
      });
      if (this.excludedMentions.length !== originalLength) {
        this.clearSegmentCache();
      }
    },
    // 清理分段缓存
    clearSegmentCache() {
      this._segmentCache = null;
      this._segmentCacheKey = "";
    },
    // 关闭@用户面板（处理未选择用户的情况）
    closeMentionPanel() {
      this.mentionPanelClosed = true;
      if (this.searchUserKeyword.trim()) {
        common_vendor.index.__f__("log", "at pages/note/add.vue:3220", "用户输入了@文字但未选择用户，保留为纯文本:", this.searchUserKeyword);
        const beforeCursor = this.content.substring(0, this.cursorPosition);
        const atIndex = beforeCursor.lastIndexOf("@");
        if (atIndex !== -1) {
          const mentionText = "@" + this.searchUserKeyword;
          this.excludedMentions.push({
            text: mentionText,
            position: atIndex
          });
        }
      }
      this.hideUserSearch();
    },
    // 显示关注的用户列表
    showFollowUsers() {
      this.mentionPanelClosed = false;
      this.searchUserKeyword = "";
      this.searchUsersForMention();
    },
    // 自动保存草稿（防抖）
    autoSaveDraft() {
      if (this.saveTimer) {
        clearTimeout(this.saveTimer);
      }
      this.saveTimer = setTimeout(() => {
        this.saveDraft();
      }, 1e3);
    },
    // 删除@用户
    removeMentionUser(uid) {
      const userToRemove = this.mentionUsers.find((user) => user.uid === uid);
      if (userToRemove) {
        const pattern = new RegExp(`@${this.escapeRegExp(userToRemove.nickname)}\\s*`, "g");
        this.content = this.content.replace(pattern, "");
      }
      this.mentionUsers = this.mentionUsers.filter((user) => user.uid !== uid);
      this.autoSaveDraft();
    },
    // 转义正则表达式特殊字符
    escapeRegExp(string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    },
    // 解析内容中的@用户并更新mentionUsers列表
    parseMentionUsersFromContent() {
      const pattern = /@([^\s@]+)/g;
      const mentionedNicknames = [];
      let match;
      while ((match = pattern.exec(this.content)) !== null) {
        mentionedNicknames.push(match[1]);
      }
      this.mentionUsers = this.mentionUsers.filter(
        (user) => mentionedNicknames.includes(user.nickname)
      );
    }
  }
};
if (!Array) {
  const _component_VoteComponent = common_vendor.resolveComponent("VoteComponent");
  const _component_emoji_panel = common_vendor.resolveComponent("emoji-panel");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  const _component_emptyPage = common_vendor.resolveComponent("emptyPage");
  const _component_jc_record = common_vendor.resolveComponent("jc-record");
  (_component_VoteComponent + _component_emoji_panel + _easycom_uni_popup2 + _component_emptyPage + _component_jc_record)();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.t($options.publishButtonText),
    d: $options.isContentEmpty ? 1 : "",
    e: common_vendor.o((...args) => $options.handlePublish && $options.handlePublish(...args)),
    f: common_vendor.s($options.publishButtonStyle),
    g: _ctx.titleBarHeight + "px",
    h: _ctx.statusBarHeight + "px",
    i: common_vendor.o([($event) => $data.content = $event.detail.value, (...args) => $options.contentInput && $options.contentInput(...args)]),
    j: common_vendor.o((...args) => $options.onTextareaFocus && $options.onTextareaFocus(...args)),
    k: common_vendor.o((...args) => $options.onTextareaBlur && $options.onTextareaBlur(...args)),
    l: $data.autoFocus,
    m: $data.content,
    n: common_vendor.f($options.contentSegments, (segment, index, i0) => {
      return {
        a: common_vendor.t(segment.text),
        b: index,
        c: common_vendor.n(segment.isMention ? "mention-text" : "normal-text")
      };
    }),
    o: !$data.content
  }, !$data.content ? {} : {}, {
    p: $data.isEditing ? 1 : "",
    q: $data.mentionUsers.length > 0
  }, $data.mentionUsers.length > 0 ? {
    r: common_vendor.f($data.mentionUsers, (user, index, i0) => {
      return {
        a: user.avatar,
        b: common_vendor.t(user.nickname),
        c: user.uid,
        d: common_vendor.o(($event) => $options.removeMentionUser(user.uid), user.uid)
      };
    })
  } : {}, {
    s: $data.type != 0
  }, $data.type != 0 ? common_vendor.e({
    t: $data.type == 2
  }, $data.type == 2 ? {
    v: common_vendor.f($data.imgs, (item, index, i0) => {
      return common_vendor.e({
        a: item.url,
        b: common_vendor.o((...args) => $options.previewClick && $options.previewClick(...args), index),
        c: index,
        d: common_vendor.o(($event) => $options.delClick(0, index), index)
      }, $data.imgs.length > 1 ? {
        e: index > 0 ? "#fff" : "#cecece",
        f: common_vendor.o(($event) => $options.onSort(index, 0), index),
        g: index < $data.imgs.length - 1 ? "#fff" : "#cecece",
        h: common_vendor.o(($event) => $options.onSort(index, 1), index)
      } : {}, {
        i: index
      });
    }),
    w: common_assets._imports_1$12,
    x: $data.imgs.length > 1,
    y: common_assets._imports_0$10,
    z: common_vendor.o(($event) => $options.addImgClick(1))
  } : {}, {
    A: $data.type == 3
  }, $data.type == 3 ? common_vendor.e({
    B: !$data.video.url
  }, !$data.video.url ? {
    C: common_assets._imports_0$4,
    D: common_vendor.o((...args) => $options.chooseVideo && $options.chooseVideo(...args))
  } : common_vendor.e({
    E: $data.video.url,
    F: common_vendor.o((...args) => $options.onVideoError && $options.onVideoError(...args)),
    G: common_vendor.o((...args) => $options.onVideoLoad && $options.onVideoLoad(...args)),
    H: common_assets._imports_1$12,
    I: common_vendor.o(($event) => $options.delClick(1)),
    J: $data.video.cover
  }, $data.video.cover ? {
    K: $data.video.cover,
    L: common_assets._imports_1$12,
    M: common_vendor.o(($event) => $options.delClick(2))
  } : {
    N: common_assets._imports_0$4,
    O: common_vendor.o(($event) => $options.addImgClick(2))
  })) : {}, {
    P: $data.type == 4
  }, $data.type == 4 ? common_vendor.e({
    Q: !$data.audio.url
  }, !$data.audio.url ? {
    R: common_assets._imports_0$4,
    S: common_vendor.o((...args) => $options.addFileClick && $options.addFileClick(...args))
  } : common_vendor.e({
    T: $data.audio.cover,
    U: $data.audio.cover
  }, $data.audio.cover ? {
    V: $data.audio.cover,
    W: common_assets._imports_4$1,
    X: common_assets._imports_1$12,
    Y: common_vendor.o(($event) => $options.delClick(3))
  } : {
    Z: common_assets._imports_0$4,
    aa: common_vendor.o(($event) => $options.addImgClick(3))
  }, {
    ab: $data.audio.name,
    ac: common_vendor.o(($event) => $data.audio.name = $event.detail.value),
    ad: $data.audio.intro,
    ae: common_vendor.o(($event) => $data.audio.intro = $event.detail.value),
    af: common_assets._imports_1$12,
    ag: common_vendor.o(($event) => $options.delClick(4))
  })) : {}) : {}, {
    ah: $data.showVote && $options.previewVoteInfo
  }, $data.showVote && $options.previewVoteInfo ? {
    ai: common_vendor.o($options.deleteVote),
    aj: common_vendor.p({
      voteInfo: $options.previewVoteInfo,
      showDelete: true,
      disabled: true
    })
  } : {}, {
    ak: common_assets._imports_3$6,
    al: $data.adds.name ? "1" : "0.6",
    am: common_vendor.t($data.adds.name || "添加位置"),
    an: $data.adds.name ? "#000" : "#999",
    ao: $data.adds.address,
    ap: common_vendor.n($data.adds.name ? "selected" : "unselected"),
    aq: common_vendor.o((...args) => $options.locationClick && $options.locationClick(...args)),
    ar: $data.circle.avatar || "/static/img/qz2.png",
    as: $data.circle.name ? "1" : "0.6",
    at: common_vendor.t($data.circle.name || "选择圈子"),
    av: $data.circle.name ? "#000" : "#999",
    aw: common_vendor.n($data.circle.name ? "selected" : "unselected"),
    ax: common_vendor.o(($event) => $options.upPopupClick(true)),
    ay: _ctx.statusBarHeight + _ctx.titleBarHeight + "px",
    az: $data.activity && $data.activity.id || $data.goods.length > 0 || $data.selectedTopics.length > 0
  }, $data.activity && $data.activity.id || $data.goods.length > 0 || $data.selectedTopics.length > 0 ? common_vendor.e({
    aA: $data.activity && $data.activity.id
  }, $data.activity && $data.activity.id ? {
    aB: $data.activity.img,
    aC: common_vendor.t($data.activity.name),
    aD: common_vendor.o(($event) => $options.activityPopupClick(true))
  } : {}, {
    aE: common_vendor.f($data.goods, (v, index, i0) => {
      return {
        a: v.product_img,
        b: common_vendor.t(v.goods_name),
        c: "goods-" + index
      };
    }),
    aF: common_vendor.f($data.selectedTopics, (topic, index, i0) => {
      return {
        a: topic.icon,
        b: common_vendor.t(topic.name),
        c: common_vendor.o(($event) => $options.removeSelectedTopic(index), "topic-" + index),
        d: "topic-" + index
      };
    })
  }) : {}, {
    aG: common_assets._imports_0$10,
    aH: common_vendor.o(($event) => $options.addImgClick(1)),
    aI: common_assets._imports_6$2,
    aJ: common_vendor.o((...args) => $options.chooseVideo && $options.chooseVideo(...args)),
    aK: common_assets._imports_7$1,
    aL: common_vendor.o(($event) => $options.topicPopupClick(true)),
    aM: common_assets._imports_1$11,
    aN: common_vendor.o((...args) => $options.showFollowUsers && $options.showFollowUsers(...args)),
    aO: common_assets._imports_2$9,
    aP: common_vendor.n($data.showEmojiPanel ? "active" : ""),
    aQ: common_vendor.o((...args) => $options.toggleEmojiPanel && $options.toggleEmojiPanel(...args)),
    aR: common_assets._imports_10,
    aS: common_vendor.o((...args) => $options.toggleMoreOptions && $options.toggleMoreOptions(...args)),
    aT: common_assets._imports_11$1,
    aU: common_vendor.o(($event) => $options.handleMediaClick(7)),
    aV: common_assets._imports_12,
    aW: common_vendor.o(($event) => $options.handleMediaClick(8)),
    aX: common_assets._imports_13,
    aY: common_vendor.o(($event) => $options.activityPopupClick(true)),
    aZ: _ctx.isNoteShop
  }, _ctx.isNoteShop ? {
    ba: common_assets._imports_14,
    bb: common_vendor.o(($event) => $options.shopPopupClick(true))
  } : {}, {
    bc: $data.showMoreOptions ? 1 : "",
    bd: $data.showEmojiPanel
  }, $data.showEmojiPanel ? {
    be: common_vendor.o($options.onSelectEmoji),
    bf: common_vendor.o($options.onSelectGif),
    bg: common_vendor.o($options.onDeleteEmoji),
    bh: common_vendor.o($options.onSendComment),
    bi: common_vendor.p({
      show: $data.showEmojiPanel,
      content: $data.content
    })
  } : {}, {
    bj: $data.showUserSearch
  }, $data.showUserSearch ? common_vendor.e({
    bk: common_vendor.o((...args) => $options.closeMentionPanel && $options.closeMentionPanel(...args)),
    bl: $data.searchUserList.length === 0 && !$data.isSearchingUser
  }, $data.searchUserList.length === 0 && !$data.isSearchingUser ? common_vendor.e({
    bm: $data.searchUserKeyword.trim()
  }, $data.searchUserKeyword.trim() ? {} : {}) : {}, {
    bn: $data.isSearchingUser
  }, $data.isSearchingUser ? {} : {}, {
    bo: $data.searchUserList.length > 0
  }, $data.searchUserList.length > 0 ? {
    bp: common_vendor.f($data.searchUserList, (user, index, i0) => {
      return {
        a: user.avatar,
        b: common_vendor.t(user.nickname),
        c: user.uid,
        d: common_vendor.o(($event) => $options.selectUserForMention(user), user.uid)
      };
    })
  } : {}, {
    bq: $data.keyboardHeight + "px"
  }) : {}, {
    br: $data.keyboardHeight + "px",
    bs: $data.keyboardHeight > 0 ? 1 : "",
    bt: common_assets._imports_0$4,
    bv: common_vendor.o(($event) => $options.shopPopupClick(false)),
    bw: common_vendor.o((...args) => $options.searchClick && $options.searchClick(...args)),
    bx: $data.kw,
    by: common_vendor.o(($event) => $data.kw = $event.detail.value),
    bz: common_vendor.o((...args) => $options.searchClick && $options.searchClick(...args)),
    bA: $data.goodsList.length <= 0
  }, $data.goodsList.length <= 0 ? {
    bB: common_assets._imports_3$1
  } : {}, {
    bC: common_vendor.f($data.goodsList, (item, index, i0) => {
      return {
        a: item.product_img,
        b: common_vendor.t(item.goods_name),
        c: index,
        d: common_vendor.n(item.selected ? "active" : ""),
        e: index,
        f: common_vendor.o((...args) => $options.goodsClick && $options.goodsClick(...args), index)
      };
    }),
    bD: common_vendor.o(($event) => $options.shopPopupClick(false)),
    bE: common_vendor.sr("shopPopup", "5e5002e4-2"),
    bF: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    bG: common_assets._imports_0$4,
    bH: common_vendor.o(($event) => $options.upPopupClick(false)),
    bI: $data.circleList.length <= 0
  }, $data.circleList.length <= 0 ? {
    bJ: common_assets._imports_3$1
  } : {}, {
    bK: common_vendor.f($data.circleList, (item, index, i0) => {
      return {
        a: item.avatar,
        b: common_vendor.t(item.name),
        c: index,
        d: common_vendor.n($data.circle.id == item.id ? "active" : ""),
        e: index,
        f: common_vendor.o((...args) => $options.circleClick && $options.circleClick(...args), index)
      };
    }),
    bL: common_vendor.o(($event) => $options.upPopupClick(false)),
    bM: common_vendor.sr("upPopup", "5e5002e4-3"),
    bN: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    bO: common_assets._imports_0$4,
    bP: common_vendor.o(($event) => $options.activityPopupClick(false)),
    bQ: $data.activityList.length <= 0
  }, $data.activityList.length <= 0 ? {
    bR: common_assets._imports_3$1
  } : {}, {
    bS: common_vendor.f($data.activityList, (item, index, i0) => {
      return {
        a: item.img,
        b: common_vendor.t(item.name),
        c: index,
        d: common_vendor.n($data.activity.id == item.id ? "active" : ""),
        e: index,
        f: common_vendor.o((...args) => $options.activityClick && $options.activityClick(...args), index)
      };
    }),
    bT: common_vendor.o(($event) => $options.activityPopupClick(false)),
    bU: common_vendor.sr("activityPopup", "5e5002e4-4"),
    bV: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    bW: common_assets._imports_0$4,
    bX: common_vendor.o(($event) => $options.topicPopupClick(false)),
    bY: common_vendor.o((...args) => $options.searchTopics && $options.searchTopics(...args)),
    bZ: $data.topicKeyword,
    ca: common_vendor.o(($event) => $data.topicKeyword = $event.detail.value),
    cb: common_vendor.o((...args) => $options.searchTopics && $options.searchTopics(...args)),
    cc: $data.topicList.length <= 0
  }, $data.topicList.length <= 0 ? {
    cd: common_vendor.p({
      title: "暂无话题",
      description: "暂时没有找到相关话题"
    })
  } : {}, {
    ce: common_vendor.f($data.topicList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.description || "暂无描述"),
        c: index,
        d: common_vendor.o(($event) => $options.topicClick(item), index)
      };
    }),
    cf: common_vendor.sr("topicPopup", "5e5002e4-5"),
    cg: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    ch: common_assets._imports_0$4,
    ci: common_vendor.o(($event) => $options.userPopupClick(false)),
    cj: common_vendor.o((...args) => $options.searchUsers && $options.searchUsers(...args)),
    ck: $data.userKeyword,
    cl: common_vendor.o(($event) => $data.userKeyword = $event.detail.value),
    cm: common_vendor.o((...args) => $options.searchUsers && $options.searchUsers(...args)),
    cn: $data.userList.length <= 0
  }, $data.userList.length <= 0 ? {
    co: common_assets._imports_3$1
  } : {}, {
    cp: common_vendor.f($data.userList, (item, index, i0) => {
      return {
        a: item.avatar,
        b: common_vendor.t(item.nickname),
        c: index,
        d: index,
        e: common_vendor.o(($event) => $options.userClick(item), index)
      };
    }),
    cq: common_vendor.sr("userPopup", "5e5002e4-7"),
    cr: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    cs: common_vendor.sr("jcRecord", "5e5002e4-9,5e5002e4-8"),
    ct: common_vendor.o($options.handleRecordOk),
    cv: common_vendor.o($options.onRecordShow),
    cw: common_vendor.o(($event) => $options.recordPopupClick(false)),
    cx: common_vendor.p({
      voicePath: $data.audio.url,
      maxTime: 60,
      minTime: 1
    }),
    cy: common_vendor.sr("recordPopup", "5e5002e4-8"),
    cz: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    cA: common_vendor.t($data.tipsTitle),
    cB: common_vendor.sr("tipsPopup", "5e5002e4-10"),
    cC: common_vendor.p({
      type: "center",
      ["mask-click"]: true
    }),
    cD: $data.canvasStatus
  }, $data.canvasStatus ? {
    cE: $data.canvasWidth + "px",
    cF: $data.canvasHeight + "px"
  } : {}, {
    cG: common_assets._imports_0$4,
    cH: common_vendor.o(($event) => $options.votePopupClick(false)),
    cI: $data.voteData.title,
    cJ: common_vendor.o(($event) => $data.voteData.title = $event.detail.value),
    cK: common_vendor.f($data.voteData.options, (option, index, i0) => {
      return {
        a: "选项" + (index + 1) + "（10个汉字以内）",
        b: $data.voteData.options[index],
        c: common_vendor.o(($event) => $data.voteData.options[index] = $event.detail.value, index),
        d: common_vendor.o(($event) => $options.deleteVoteOption(index), index),
        e: index
      };
    }),
    cL: common_vendor.o((...args) => $options.addVoteOption && $options.addVoteOption(...args)),
    cM: common_vendor.o(($event) => $options.votePopupClick(false)),
    cN: common_vendor.o((...args) => $options.confirmVote && $options.confirmVote(...args)),
    cO: common_vendor.sr("votePopup", "5e5002e4-11"),
    cP: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/note/add.js.map
