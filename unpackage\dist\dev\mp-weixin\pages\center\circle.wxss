
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.bar-box .bar-back {
  padding: 0 30rpx;
  width: 34rpx;
  height: 100%;
}
.bar-box .bar-title {
  max-width: 60%;
  font-size: 32rpx;
  font-weight: 700;
}
.nav-box {
  width: 100%;
  height: 80rpx;
}
.nav-box .nav-item {
  padding: 0 30rpx;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.nav-box .nav-item text {
  font-weight: 700;
  transition: all .3s ease-in-out;
}
.nav-box .nav-line {
  position: absolute;
  bottom: 12rpx;
  width: 18rpx;
  height: 6rpx;
  border-radius: 6rpx;
  background: #000;
  transition: opacity .3s ease-in-out;
}
.content-box {
  width: calc(100% - 60rpx);
  padding: 30rpx;
}

/* 加载中状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60rpx;
  margin-bottom: 20rpx;
}
.loading-indicator {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.list-box {
  width: 100%;
  padding-bottom: 30rpx;
  justify-content: space-between;
}
.list-box .list-avatar {
  width: 168rpx;
  height: 168rpx;
  border-radius: 50%;
  background: #f8f8f8;
  border: 1px solid #f8f8f8;
  position: relative;
}
.list-avatar .tag {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 6rpx solid #fff;
}
.list-box .list-item {
  width: calc(100% - 198rpx);
  margin-left: 30rpx;
}
.list-item .name {
  font-size: 32rpx;
  font-weight: 700;
}
.list-item .intro {
  margin: 10rpx 0 20rpx;
  color: #999;
  font-size: 24rpx;
}
.cu-img-group {
  direction: ltr;
  unicode-bidi: bidi-override;
  display: inline-block;
  margin-left: 16rpx;
}
.cu-img-group .cu-img {
  width: 32rpx;
  height: 32rpx;
  display: inline-flex;
  position: relative;
  margin-left: -16rpx;
  border: 4rpx solid #fff;
  background: #f8f8f8;
  vertical-align: middle;
  border-radius: 50%;
}
.cu-img-group .cu-img image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.cu-img-group .cu-txt {
  margin-left: 10rpx;
  display: inline-flex;
  color: #999;
  font-size: 20rpx;
  font-weight: 700;
}
.view-count {
  display: inline-flex;
  color: #999;
  font-size: 20rpx;
  font-weight: 500;
  margin-left: 4rpx;
}
.df {
  display: flex;
  align-items: center;
}
.bfw {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,.8);
}
.bfh {
  background: #000;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 700;
}
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-box image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-box .e1 {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.empty-box .e2 {
  font-size: 24rpx;
  color: #999;
}
.tips-box {
  justify-content: center;
  width: 100%;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  word-break: break-all;
}
.heio {
  width: 100%;
  overflow: hidden;
  transition: height 0.3s;
  justify-content: center;
}
.tags-box {
  display: flex;
  flex-wrap: wrap;
  margin: 6rpx 0;
}
.tag-item {
  font-size: 20rpx;
  color: #666;
  margin-right: 12rpx;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}
.recent-topics {
  margin-top: 8rpx;
}
.recent-title {
  font-size: 20rpx;
  color: #666;
  margin-bottom: 4rpx;
}
.topic-item {
  font-size: 20rpx;
  color: #666;
  background: #f8f8f8;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  margin-bottom: 4rpx;
  margin-right: 8rpx;
  display: inline-block;
}
