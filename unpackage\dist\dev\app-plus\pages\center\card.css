
.uni-load-more[data-v-9245e42c] {
  display: flex;
  flex-direction: row;
  height: 1.875rem;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-9245e42c] {
  font-size: 0.75rem;
  margin-left: 0.5rem;
}
.uni-load-more__img[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
}
.uni-load-more__img--nvue[data-v-9245e42c] {
  color: #999;
}
.uni-load-more__img--android[data-v-9245e42c],
.uni-load-more__img--ios[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
}
.uni-load-more__img--android[data-v-9245e42c] {
  animation: loading-ios 1s 0s linear infinite;
}
.uni-load-more__img--ios-H5[data-v-9245e42c] {
  position: relative;
  animation: loading-ios-H5-9245e42c 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-9245e42c] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-9245e42c {
0% {
    transform: rotate(0);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
to {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-9245e42c] {
  position: relative;
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 0.125rem transparent;
  border-top: solid 0.125rem #999;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(1) {
  animation: loading-android-MP-1-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(2) {
  animation: loading-android-MP-2-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(3) {
  animation: loading-android-MP-3-9245e42c 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(90deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(180deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(270deg);
}
to {
    transform: rotate(360deg);
}
}


.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


body {
	background: #f8f8f8;
	padding-bottom: 3.125rem;
}
.container {
	width: 100%;
}
.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 99;
	box-sizing: border-box;
}
.bar-box .bar-back {
	padding: 0 0.9375rem;
	width: 1.0625rem;
	height: 100%;
}
.bar-box .bar-title {
	max-width: 60%;
	font-size: 1rem;
	font-weight: 700;
}
.nav-box {
	width: 100%;
	height: 2.5rem;
}
.nav-box .nav-item {
	padding: 0 0.9375rem;
	height: 100%;
	flex-direction: column;
	justify-content: center;
	position: relative;
}
.nav-box .nav-item uni-text {
	font-weight: 700;
	transition: all .3s ease-in-out;
}
.nav-box .nav-line {
	position: absolute;
	bottom: 0.375rem;
	width: 0.5625rem;
	height: 0.1875rem;
	border-radius: 0.1875rem;
	background: #000;
	transition: opacity .3s ease-in-out;
}
.code-box {
	position: fixed;
	z-index: 99;
	left: 0.9375rem;
	bottom: max(env(safe-area-inset-bottom), 0.9375rem);
	width: calc(100% - 1.875rem);
	height: 3.125rem;
	color: #fff;
	font-size: 0.75rem;
	font-weight: 700;
	background: #000;
	border-radius: 3.125rem;
	justify-content: center;
}
.code-box uni-image {
	margin-right: 0.25rem;
	width: 1.0625rem;
	height: 1.0625rem;
}
.content {
	width: calc(100% - 1.875rem);
	padding: 0.9375rem;
}
.coupon {
	margin-bottom: 0.9375rem;
	width: 100%;
	border-radius: 0.25rem;
	background: #fff;
	position: relative;
	overflow: hidden;
}
.coupon .coupon-bg {
	position: absolute;
	z-index: 1;
	right: -2.8125rem;
	bottom: -3.75rem;
	width: 11.875rem;
	height: 11.875rem;
}
.coupon .coupon-item {
	z-index: 2;
	width: calc(100% - 2.5rem);
	padding: 1.5625rem 1.25rem 0.625rem;
	border-bottom: 0.0625rem dashed #f8f8f8;
	position: relative;
}
.coupon-item .corner-mark {
	position: absolute;
	z-index: 9;
	top: 0;
	left: 0;
	border-radius: 0.25rem 0;
	padding: 0 0.375rem;
	height: 1.125rem;
	line-height: 1.125rem;
	text-align: center;
	font-size: 0.625rem;
	color: #fa5150;
	background: rgba(250, 81, 80, .125);
}
.coupon-item .t1 {
	width: calc(100% - 6.25rem);
	color: #000;
	font-size: 1.5rem;
	font-weight: 700;
}
.coupon-item .t2 {
	width: calc(100% - 6.25rem);
	margin: 0.25rem 0;
	color: #444;
	font-size: 0.75rem;
}
.coupon .coupon-btn {
	position: absolute;
	z-index: 9;
	top: calc(50% - 0.9375rem);
	right: 0.9375rem;
	width: 4.375rem;
	height: 1.875rem;
	line-height: 1.875rem;
	text-align: center;
	font-size: 0.625rem;
	font-weight: 700;
	border-radius: 1.5625rem;
	color: #fff;
	background: #000;
}
.coupon .validity {
	width: calc(100% - 2.5rem);
	padding: 0.625rem 1.25rem;
	color: #999;
	font-size: 0.625rem;
}
.coupon .coupon-err {
	position: absolute;
	z-index: 10;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	font-size: 0.875rem;
	font-style: italic;
	font-weight: 700;
	justify-content: center;
	color: #ccc;
	background: rgba(255, 255, 255, .85);
}
.popup-box {
	width: calc(100% - 1.25rem);
	padding: 0.625rem;
	background: #fff;
	border-radius: 0.9375rem 0.9375rem 0 0;
	position: relative;
	overflow: hidden;
}
.popup-box .popup-top {
	width: calc(100% - 0.625rem);
	padding: 0.3125rem;
	justify-content: space-between;
}
.popup-top .popup-title .t1 {
	font-size: 1.1875rem;
	font-weight: 700;
}
.popup-top .popup-title .t2 {
	color: #999;
	font-size: 0.625rem;
	font-weight: 300;
}
.popup-top .popup-close {
	width: 1.5rem;
	height: 1.5rem;
	border-radius: 50%;
	background: #f8f8f8;
	justify-content: center;
	transform: rotate(45deg);
}
.exchange-input {
	margin: 0.9375rem 0.3125rem;
	padding: 0 0.9375rem;
	width: calc(100% - 2.5rem);
	height: 3.125rem;
	font-size: 0.875rem;
	font-weight: 700;
	text-align: center;
	border: 0.125rem solid #f5f5f5;
	border-radius: 0.9375rem;
}
.exchange-item uni-view {
	margin: 0.3125rem;
	width: calc(100% - 0.625rem);
	color: #999;
	font-size: 0.625rem;
}
.popup-box .popup-btn {
	margin: 1.25rem 0.3125rem;
	width: calc(100% - 0.625rem);
	height: 3.125rem;
	line-height: 3.125rem;
	text-align: center;
	font-size: 0.75rem;
	font-weight: 700;
	color: #fff;
	background: #000;
	border-radius: 3.125rem;
}
.empty-box {
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 3.125rem 0;
}
.empty-box uni-image {
	width: 6.25rem;
	height: 6.25rem;
	margin-bottom: 0.9375rem;
}
.empty-box .e1 {
	font-size: 0.875rem;
	font-weight: bold;
	margin-bottom: 0.3125rem;
}
.empty-box .e2 {
	font-size: 0.75rem;
	color: #999;
}
.tips-box {
	padding: 0.625rem 0.9375rem;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 0.375rem;
	justify-content: center;
}
.tips-box .tips-item {
	color: #fff;
	font-size: 0.875rem;
	font-weight: 700;
}
.df {
	display: flex;
	align-items: center;
}
.ohto {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.bf8 {
	background: #f8f8f8;
}
