.container {
  width: 100%;
  background: #fff;
  height: 100vh;
  overflow: hidden;
}
.content-box {
  width: 100%;
}
.logo-box {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 200rpx;
  margin-bottom: 60rpx;
}
.logo-box image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 20rpx;
}
.login-box {
  width: calc(100% - 60rpx);
  margin: 30rpx;
}
.login-box .title {
  font-size: 40rpx;
  font-weight: 700;
  color: #000;
  text-align: center;
}
.login-box .sub-title {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #999;
  text-align: center;
  margin-bottom: 80rpx;
}
.login-box .btn-submit {
  margin-top: 40rpx;
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 700;
  background: #000;
  color: #fff;
  border-radius: 100rpx;
  justify-content: center;
}
.login-box .phone-btn {
  background: #fff;
  color: #333;
  border: 1px solid #e4e4e4;
}
.login-box .agreement {
  margin-top: 60rpx;
  font-size: 24rpx;
  color: #999;
  text-align: center;
}
.agreement .agreement-text {
  font-size: 24rpx;
  color: #999;
}
.agreement-text text {
  color: #576b95;
}
.df {
  display: flex;
  align-items: center;
  justify-content: center;
}
.trembling {
  animation: shake 0.6s;
}
@keyframes shake {
0%, 100% {
    transform: translateX(0);
}
10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
}
20%, 40%, 60%, 80% {
    transform: translateX(5px);
}
}