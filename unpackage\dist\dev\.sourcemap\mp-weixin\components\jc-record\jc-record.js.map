{"version": 3, "file": "jc-record.js", "sources": ["components/jc-record/jc-record.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy9qYy1yZWNvcmQvamMtcmVjb3JkLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"jsfun-record\" @tap=\"showPicker\">\r\n\t\t<slot></slot>\r\n\t\t<!-- 遮罩层 -->\r\n\t\t<view class=\"mask\" @tap.stop=\"closePicker\" v-if=\"visible\" @touchmove.stop.prevent=\"preventMove\"></view>\r\n\t\t<!-- 录音控件 -->\r\n\t\t<view class=\"record-container\" :class=\"{'show': visible}\">\r\n\t\t\t<!-- 录音时长显示 -->\r\n\t\t\t<view class=\"time-display\">{{formattedTime}}</view>\r\n\t\t\t<view class=\"time-hint\">最短{{minTime}}秒，最长{{maxTime}}秒</view>\r\n\t\t\t\r\n\t\t\t<!-- 录音操作区域 -->\r\n\t\t\t<view class=\"record-box\">\r\n\t\t\t\t<!-- 停止播放按钮 -->\r\n\t\t\t\t<span class=\"control-btn stop-btn\" @tap.stop=\"stopPlayback\" v-if=\"voiceFile && isPlaying\"></span>\r\n\t\t\t\t<!-- 播放按钮 -->\r\n\t\t\t\t<span class=\"control-btn play-btn\" @tap.stop=\"startPlayback\" v-if=\"voiceFile && !isPlaying\"></span>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 录音按钮 -->\r\n\t\t\t\t<canvas \r\n\t\t\t\t\tclass=\"record-canvas\" \r\n\t\t\t\t\tcanvas-id=\"recordCanvas\" \r\n\t\t\t\t\t@touchstart=\"handleTouchStart\" \r\n\t\t\t\t\t@longpress=\"startRecording\" \r\n\t\t\t\t\t@touchend=\"stopRecording\" \r\n\t\t\t\t\************************=\"preventMove\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<span class=\"record-indicator\" :class=\"{'recording': isRecording}\"></span>\r\n\t\t\t\t</canvas>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 确认按钮 -->\r\n\t\t\t\t<span class=\"control-btn confirm-btn\" @tap.stop=\"confirmRecording\" v-if=\"voiceFile\"></span>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"instruction-text\">长按录音</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n// 录音管理器兼容性处理\r\nlet recorderManager = null;\r\nlet audioContext = null;\r\n\r\n// #ifdef APP-PLUS || MP-WEIXIN || MP-ALIPAY\r\nif (typeof uni.getRecorderManager === 'function') {\r\n\trecorderManager = uni.getRecorderManager();\r\n}\r\n// #endif\r\n\r\n// #ifdef H5\r\n// H5环境下的录音处理\r\nif (typeof navigator !== 'undefined' && navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\r\n\t// 支持录音\r\n} else {\r\n\tconsole.warn('当前环境不支持录音功能');\r\n}\r\n// #endif\r\n\r\nexport default {\r\n\tname: 'jc-record',\r\n\tprops: {\r\n\t\tvoicePath: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tmaxTime: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 15\r\n\t\t},\r\n\t\tminTime: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 5\r\n\t\t},\r\n\t\tautoShow: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tvisible: false,\r\n\t\t\tisRecording: false,\r\n\t\t\tisPlaying: false,\r\n\t\t\trecordTime: 0,\r\n\t\t\tplaybackTime: 0,\r\n\t\t\tvoiceFile: '',\r\n\t\t\t\r\n\t\t\t// 定时器\r\n\t\t\trecordTimer: null,\r\n\t\t\tplaybackTimer: null,\r\n\t\t\tanimationTimer: null,\r\n\t\t\t\r\n\t\t\t// Canvas相关\r\n\t\t\tcanvasContext: null,\r\n\t\t\tcanvasWidth: 0,\r\n\t\t\tcanvasHeight: 0,\r\n\t\t\tcenterX: 0,\r\n\t\t\tcenterY: 0,\r\n\t\t\tindicatorRadius: 0,\r\n\t\t\t\r\n\t\t\t// 动画参数\r\n\t\t\tanimationAngle: -0.5,\r\n\t\t\tframeInterval: 50\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tformattedTime() {\r\n\t\t\tconst time = this.isPlaying ? this.playbackTime : this.recordTime;\r\n\t\t\tconst minutes = Math.floor(time / 60);\r\n\t\t\tconst seconds = time % 60;\r\n\t\t\treturn `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.initializeComponent();\r\n\t},\r\n\tbeforeDestroy() {\r\n\t\tthis.cleanup();\r\n\t},\r\n\tmethods: {\r\n\t\t// 组件初始化\r\n\t\tasync initializeComponent() {\r\n\t\t\tawait this.requestPermissions();\r\n\t\t\tthis.setupAudioContext();\r\n\t\t\tthis.setupRecorderEvents();\r\n\t\t\tthis.setupCanvasDimensions();\r\n\t\t\t\r\n\t\t\tif (this.autoShow) {\r\n\t\t\t\tthis.$nextTick(() => this.showPicker());\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 请求录音权限\r\n\t\tasync requestPermissions() {\r\n  try {\r\n    await uni.authorize({ scope: 'scope.record' });\r\n    // 授权成功\r\n  } catch (error) {\r\n    // 授权失败，提示并引导用户去设置\r\n    uni.showModal({\r\n      title: '权限申请',\r\n      content: '需要录音权限才能使用此功能，请在设置中开启录音权限',\r\n      showCancel: true,\r\n      success: (res) => {\r\n        if (res.confirm) {\r\n          uni.openSetting();\r\n        }\r\n      }\r\n    });\r\n  }\r\n},\r\n\t\t\r\n\t\t// 设置音频上下文\r\n\t\tsetupAudioContext() {\r\n\t\t\taudioContext = uni.createInnerAudioContext();\r\n\t\t\taudioContext.onEnded(() => {\r\n\t\t\t\tthis.isPlaying = false;\r\n\t\t\t\tthis.playbackTime = 0;\r\n\t\t\t\tthis.clearTimer('playbackTimer');\r\n\t\t\t});\r\n\t\t\taudioContext.onError((error) => {\r\n\t\t\t\tthis.handleAudioError(error);\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 设置录音事件\r\n\t\tsetupRecorderEvents() {\r\n\t\t\tif (!recorderManager) {\r\n\t\t\t\tconsole.warn('录音管理器不可用');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\trecorderManager.onStop((result) => {\r\n\t\t\t\tthis.voiceFile = result.tempFilePath;\r\n\t\t\t\tthis.$emit('recorded', result.tempFilePath);\r\n\t\t\t});\r\n\r\n\t\t\trecorderManager.onError((error) => {\r\n\t\t\t\tthis.handleRecordError(error);\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 设置Canvas尺寸\r\n\t\tsetupCanvasDimensions() {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\t\r\n\t\t\t\t// 获取Canvas尺寸\r\n\t\t\t\tquery.select('.record-canvas').boundingClientRect((rect) => {\r\n\t\t\t\t\tif (rect) {\r\n\t\t\t\t\t\tthis.canvasWidth = rect.width;\r\n\t\t\t\t\t\tthis.canvasHeight = rect.height;\r\n\t\t\t\t\t\tthis.centerX = rect.width / 2;\r\n\t\t\t\t\t\tthis.centerY = rect.height / 2;\r\n\t\t\t\t\t\tthis.canvasContext = uni.createCanvasContext('recordCanvas', this);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 获取录音指示器尺寸\r\n\t\t\t\tquery.select('.record-indicator').boundingClientRect((rect) => {\r\n\t\t\t\t\tif (rect) {\r\n\t\t\t\t\t\tthis.indicatorRadius = rect.width / 2 + 4;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tquery.exec();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 显示录音面板\r\n\t\tshowPicker() {\r\n\t\t\tthis.visible = true;\r\n\t\t\tthis.$emit('show');\r\n\t\t},\r\n\t\t\r\n\t\t// 关闭录音面板\r\n\t\tclosePicker() {\r\n\t\t\tthis.visible = false;\r\n\t\t\tthis.resetState();\r\n\t\t\tthis.$emit('close');\r\n\t\t},\r\n\t\t\r\n\t\t// 重置状态\r\n\t\tresetState() {\r\n\t\t\tthis.stopPlayback();\r\n\t\t\tthis.voiceFile = '';\r\n\t\t\tthis.recordTime = 0;\r\n\t\t\tthis.playbackTime = 0;\r\n\t\t\tthis.clearAllTimers();\r\n\t\t\tthis.clearCanvas();\r\n\t\t},\r\n\t\t\r\n\t\t// 触摸开始\r\n\t\thandleTouchStart() {\r\n\t\t\tthis.stopPlayback();\r\n\t\t\tthis.voiceFile = '';\r\n\t\t\tthis.recordTime = 0;\r\n\t\t},\r\n\t\t\r\n\t\t// 开始录音\r\n\t\tstartRecording() {\r\n\t\t\tif (this.isRecording) return;\r\n\t\t\t\r\n\t\t\tthis.isRecording = true;\r\n\t\t\tthis.recordTime = 0;\r\n\t\t\t\r\n\t\t\t// 开始录音\r\n\t\t\tif (!recorderManager) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '当前环境不支持录音',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\trecorderManager.start({\r\n\t\t\t\tduration: this.maxTime * 1000,\r\n\t\t\t\tsampleRate: 16000,\r\n\t\t\t\tnumberOfChannels: 1,\r\n\t\t\t\tencodeBitRate: 96000,\r\n\t\t\t\tformat: 'mp3'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 开始计时\r\n\t\t\tthis.recordTimer = setInterval(() => {\r\n\t\t\t\tthis.recordTime++;\r\n\t\t\t\tif (this.recordTime >= this.maxTime) {\r\n\t\t\t\t\tthis.stopRecording();\r\n\t\t\t\t}\r\n\t\t\t}, 1000);\r\n\t\t\t\r\n\t\t\t// 开始动画\r\n\t\t\tthis.startRecordingAnimation();\r\n\t\t},\r\n\t\t\r\n\t\t// 停止录音\r\n\t\tstopRecording() {\r\n\t\t\tif (!this.isRecording) return;\r\n\t\t\t\r\n\t\t\tconst recordDuration = this.recordTime;\r\n\t\t\tthis.isRecording = false;\r\n\t\t\tthis.clearTimer('recordTimer');\r\n\t\t\tthis.clearTimer('animationTimer');\r\n\t\t\tthis.clearCanvas();\r\n\t\t\t\r\n\t\t\t// 检查录音时长\r\n\t\t\tif (recordDuration < this.minTime) {\r\n\t\t\t\tif (recordDuration <= 0) return;\r\n\t\t\t\t\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: `录音时长不能少于${this.minTime}秒`,\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\tthis.recordTime = 0;\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (recorderManager) {\r\n\t\t\t\trecorderManager.stop();\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 开始录音动画\r\n\t\tstartRecordingAnimation() {\r\n\t\t\tif (!this.canvasContext) return;\r\n\t\t\t\r\n\t\t\t// 绘制背景圆环\r\n\t\t\tthis.drawBackgroundCircle();\r\n\t\t\t\r\n\t\t\t// 重置动画角度\r\n\t\t\tthis.animationAngle = -0.5;\r\n\t\t\t\r\n\t\t\t// 开始进度动画\r\n\t\t\tthis.animationTimer = setInterval(() => {\r\n\t\t\t\tthis.drawProgressCircle();\r\n\t\t\t}, this.frameInterval);\r\n\t\t},\r\n\t\t\r\n\t\t// 绘制背景圆环\r\n\t\tdrawBackgroundCircle() {\r\n\t\t\tthis.canvasContext.beginPath();\r\n\t\t\tthis.canvasContext.setStrokeStyle('#fe3b54');\r\n\t\t\tthis.canvasContext.setGlobalAlpha(0.3);\r\n\t\t\tthis.canvasContext.setLineWidth(3);\r\n\t\t\tthis.canvasContext.arc(this.centerX, this.centerY, this.indicatorRadius, 0, 2 * Math.PI);\r\n\t\t\tthis.canvasContext.stroke();\r\n\t\t\tthis.canvasContext.draw();\r\n\t\t},\r\n\t\t\r\n\t\t// 绘制进度圆环\r\n\t\tdrawProgressCircle() {\r\n\t\t\tthis.canvasContext.beginPath();\r\n\t\t\tthis.canvasContext.setStrokeStyle('#fe3b54');\r\n\t\t\tthis.canvasContext.setGlobalAlpha(1);\r\n\t\t\tthis.canvasContext.setLineWidth(3);\r\n\t\t\t\r\n\t\t\tconst endAngle = this.animationAngle + (2 / (this.maxTime * 1000 / this.frameInterval));\r\n\t\t\tthis.canvasContext.arc(\r\n\t\t\t\tthis.centerX, \r\n\t\t\t\tthis.centerY, \r\n\t\t\t\tthis.indicatorRadius, \r\n\t\t\t\t-0.5 * Math.PI, \r\n\t\t\t\tendAngle * Math.PI, \r\n\t\t\t\tfalse\r\n\t\t\t);\r\n\t\t\t\r\n\t\t\tthis.canvasContext.stroke();\r\n\t\t\tthis.canvasContext.draw(true);\r\n\t\t\t\r\n\t\t\tthis.animationAngle = endAngle;\r\n\t\t},\r\n\t\t\r\n\t\t// 清除Canvas\r\n\t\tclearCanvas() {\r\n\t\t\tif (!this.canvasContext) return;\r\n\t\t\t\r\n\t\t\tthis.canvasContext.setFillStyle('#fff');\r\n\t\t\tthis.canvasContext.fillRect(0, 0, this.canvasWidth, this.canvasHeight);\r\n\t\t\tthis.canvasContext.draw();\r\n\t\t},\r\n\t\t\r\n\t\t// 开始播放\r\n\t\tstartPlayback() {\r\n\t\t\tif (!this.voiceFile || this.isPlaying) return;\r\n\t\t\t\r\n\t\t\taudioContext.src = this.voiceFile;\r\n\t\t\taudioContext.stop(); // 确保之前的播放已停止\r\n\t\t\taudioContext.play();\r\n\t\t\t\r\n\t\t\tthis.isPlaying = true;\r\n\t\t\tthis.playbackTime = this.recordTime;\r\n\t\t\t\r\n\t\t\t// 播放倒计时\r\n\t\t\tthis.playbackTimer = setInterval(() => {\r\n\t\t\t\tthis.playbackTime--;\r\n\t\t\t\tif (this.playbackTime <= 0) {\r\n\t\t\t\t\tthis.stopPlayback();\r\n\t\t\t\t}\r\n\t\t\t}, 1000);\r\n\t\t},\r\n\t\t\r\n\t\t// 停止播放\r\n\t\tstopPlayback() {\r\n\t\t\tif (audioContext) {\r\n\t\t\t\taudioContext.stop();\r\n\t\t\t}\r\n\t\t\tthis.isPlaying = false;\r\n\t\t\tthis.playbackTime = 0;\r\n\t\t\tthis.clearTimer('playbackTimer');\r\n\t\t},\r\n\t\t\r\n\t\t// 确认录音\r\n\t\tconfirmRecording() {\r\n\t\t\tif (!this.voiceFile) return;\r\n\t\t\t\r\n\t\t\tthis.$emit('confirm', this.voiceFile);\r\n\t\t\tthis.closePicker();\r\n\t\t},\r\n\t\t\r\n\t\t// 清除指定定时器\r\n\t\tclearTimer(timerName) {\r\n\t\t\tif (this[timerName]) {\r\n\t\t\t\tclearInterval(this[timerName]);\r\n\t\t\t\tthis[timerName] = null;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 清除所有定时器\r\n\t\tclearAllTimers() {\r\n\t\t\tthis.clearTimer('recordTimer');\r\n\t\t\tthis.clearTimer('playbackTimer');\r\n\t\t\tthis.clearTimer('animationTimer');\r\n\t\t},\r\n\t\t\r\n\t\t// 处理录音错误\r\n\t\thandleRecordError(error) {\r\n\t\t\tthis.isRecording = false;\r\n\t\t\tthis.clearAllTimers();\r\n\t\t\tthis.clearCanvas();\r\n\t\t\t\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '录音失败，请重试',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tthis.$emit('error', { type: 'record', error });\r\n\t\t},\r\n\t\t\r\n\t\t// 处理音频错误\r\n\t\thandleAudioError(error) {\r\n\t\t\tthis.isPlaying = false;\r\n\t\t\tthis.clearTimer('playbackTimer');\r\n\t\t\t\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '播放失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tthis.$emit('error', { type: 'audio', error });\r\n\t\t},\r\n\t\t\r\n\t\t// 阻止默认事件\r\n\t\tpreventMove() {\r\n\t\t\treturn false;\r\n\t\t},\r\n\t\t\r\n\t\t// 组件清理\r\n\t\tcleanup() {\r\n\t\t\tthis.clearAllTimers();\r\n\t\t\tif (audioContext) {\r\n\t\t\t\taudioContext.destroy();\r\n\t\t\t\taudioContext = null;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 小程序兼容：将SCSS嵌套语法改为普通CSS */\r\n.jsfun-record .mask {\r\n\tposition: fixed;\r\n\tz-index: 1000;\r\n\ttop: 0;\r\n\tright: 0;\r\n\tleft: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0, 0, 0, 0.6);\r\n}\r\n\r\n.jsfun-record .record-container {\r\n\tposition: fixed;\r\n\tz-index: 1001;\r\n\tright: 0;\r\n\tleft: 0;\r\n\tbottom: 0;\r\n\tbackground: #fff;\r\n\ttext-align: center;\r\n\ttransition: transform 0.3s ease;\r\n\ttransform: translateY(100%);\r\n}\r\n\r\n.jsfun-record .record-container.show {\r\n\ttransform: translateY(0);\r\n}\r\n\r\n.jsfun-record .time-display {\r\n\tfont-size: 60rpx;\r\n\tcolor: #000;\r\n\tline-height: 100rpx;\r\n\tmargin-top: 50rpx;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.jsfun-record .time-hint {\r\n\tcolor: #999;\r\n\tfont-size: 28rpx;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.jsfun-record .record-box {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 20rpx 0;\r\n}\r\n\r\n.jsfun-record .record-canvas {\r\n\tposition: relative;\r\n\twidth: 200rpx;\r\n\theight: 200rpx;\r\n\tmargin: 0 60rpx;\r\n\tz-index: 10;\r\n}\r\n\r\n.jsfun-record .record-canvas .record-indicator {\r\n\tposition: absolute;\r\n\ttop: 20rpx;\r\n\tleft: 20rpx;\r\n\twidth: 160rpx;\r\n\theight: 160rpx;\r\n\tborder: 1px dashed #fe3b54;\r\n\tborder-radius: 50%;\r\n\tbackground: #fe3b54 url(../../static/jc-record/recording.png) no-repeat center;\r\n\tbackground-size: 50%;\r\n\tz-index: 100;\r\n\ttransition: all 0.2s ease;\r\n}\r\n\r\n.jsfun-record .record-canvas .record-indicator.recording {\r\n\ttransform: scale(1.05);\r\n\tbox-shadow: 0 0 20rpx rgba(254, 59, 84, 0.3);\r\n}\r\n\r\n.jsfun-record .control-btn {\r\n\twidth: 80rpx;\r\n\theight: 80rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-size: 100%;\r\n\tcursor: pointer;\r\n\ttransition: transform 0.2s ease;\r\n}\r\n\r\n.jsfun-record .control-btn:active {\r\n\ttransform: scale(0.95);\r\n}\r\n\r\n.jsfun-record .stop-btn {\r\n\tbackground-image: url(../../static/jc-record/stop.png);\r\n}\r\n\r\n.jsfun-record .play-btn {\r\n\tbackground-image: url(../../static/jc-record/play.png);\r\n}\r\n\r\n.jsfun-record .confirm-btn {\r\n\tbackground-image: url(../../static/jc-record/confirm.png);\r\n}\r\n\r\n.jsfun-record .instruction-text {\r\n\tcolor: #666;\r\n\tfont-size: 32rpx;\r\n\tmargin-bottom: 50rpx;\r\n\tpadding-bottom: env(safe-area-inset-bottom);\r\n}\r\n</style>", "import Component from 'D:/uniapp/vue3/components/jc-record/jc-record.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AAyCA,IAAI,kBAAkB;AACtB,IAAI,eAAe;AAGnB,IAAI,OAAOA,cAAG,MAAC,uBAAuB,YAAY;AACjD,oBAAkBA,cAAAA,MAAI;AACvB;AAYA,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA,IACN,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,aAAa;AAAA,MACb,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,WAAW;AAAA;AAAA,MAGX,aAAa;AAAA,MACb,eAAe;AAAA,MACf,gBAAgB;AAAA;AAAA,MAGhB,eAAe;AAAA,MACf,aAAa;AAAA,MACb,cAAc;AAAA,MACd,SAAS;AAAA,MACT,SAAS;AAAA,MACT,iBAAiB;AAAA;AAAA,MAGjB,gBAAgB;AAAA,MAChB,eAAe;AAAA,IAChB;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,gBAAgB;AACf,YAAM,OAAO,KAAK,YAAY,KAAK,eAAe,KAAK;AACvD,YAAM,UAAU,KAAK,MAAM,OAAO,EAAE;AACpC,YAAM,UAAU,OAAO;AACvB,aAAO,GAAG,QAAQ,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,IAAI,QAAQ,SAAQ,EAAG,SAAS,GAAG,GAAG,CAAC;AAAA,IACrF;AAAA,EACA;AAAA,EACD,UAAU;AACT,SAAK,oBAAmB;AAAA,EACxB;AAAA,EACD,gBAAgB;AACf,SAAK,QAAO;AAAA,EACZ;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,sBAAsB;AAC3B,YAAM,KAAK;AACX,WAAK,kBAAiB;AACtB,WAAK,oBAAmB;AACxB,WAAK,sBAAqB;AAE1B,UAAI,KAAK,UAAU;AAClB,aAAK,UAAU,MAAM,KAAK,WAAY,CAAA;AAAA,MACvC;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,qBAAqB;AAC3B,UAAI;AACF,cAAMA,cAAG,MAAC,UAAU,EAAE,OAAO,eAAgB,CAAA;AAAA,MAE7C,SAAO,OAAO;AAEdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACfA,4BAAG,MAAC,YAAW;AAAA,YACjB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA,IAGE,oBAAoB;AACnB,qBAAeA,cAAAA,MAAI;AACnB,mBAAa,QAAQ,MAAM;AAC1B,aAAK,YAAY;AACjB,aAAK,eAAe;AACpB,aAAK,WAAW,eAAe;AAAA,MAChC,CAAC;AACD,mBAAa,QAAQ,CAAC,UAAU;AAC/B,aAAK,iBAAiB,KAAK;AAAA,MAC5B,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB;AACrB,UAAI,CAAC,iBAAiB;AACrBA,sBAAAA,MAAa,MAAA,QAAA,6CAAA,UAAU;AACvB;AAAA,MACD;AAEA,sBAAgB,OAAO,CAAC,WAAW;AAClC,aAAK,YAAY,OAAO;AACxB,aAAK,MAAM,YAAY,OAAO,YAAY;AAAA,MAC3C,CAAC;AAED,sBAAgB,QAAQ,CAAC,UAAU;AAClC,aAAK,kBAAkB,KAAK;AAAA,MAC7B,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,wBAAwB;AACvB,WAAK,UAAU,MAAM;AACpB,cAAM,QAAQA,cAAAA,MAAI,oBAAqB,EAAC,GAAG,IAAI;AAG/C,cAAM,OAAO,gBAAgB,EAAE,mBAAmB,CAAC,SAAS;AAC3D,cAAI,MAAM;AACT,iBAAK,cAAc,KAAK;AACxB,iBAAK,eAAe,KAAK;AACzB,iBAAK,UAAU,KAAK,QAAQ;AAC5B,iBAAK,UAAU,KAAK,SAAS;AAC7B,iBAAK,gBAAgBA,cAAG,MAAC,oBAAoB,gBAAgB,IAAI;AAAA,UAClE;AAAA,QACD,CAAC;AAGD,cAAM,OAAO,mBAAmB,EAAE,mBAAmB,CAAC,SAAS;AAC9D,cAAI,MAAM;AACT,iBAAK,kBAAkB,KAAK,QAAQ,IAAI;AAAA,UACzC;AAAA,QACD,CAAC;AAED,cAAM,KAAI;AAAA,MACX,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa;AACZ,WAAK,UAAU;AACf,WAAK,MAAM,MAAM;AAAA,IACjB;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,UAAU;AACf,WAAK,WAAU;AACf,WAAK,MAAM,OAAO;AAAA,IAClB;AAAA;AAAA,IAGD,aAAa;AACZ,WAAK,aAAY;AACjB,WAAK,YAAY;AACjB,WAAK,aAAa;AAClB,WAAK,eAAe;AACpB,WAAK,eAAc;AACnB,WAAK,YAAW;AAAA,IAChB;AAAA;AAAA,IAGD,mBAAmB;AAClB,WAAK,aAAY;AACjB,WAAK,YAAY;AACjB,WAAK,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,iBAAiB;AAChB,UAAI,KAAK;AAAa;AAEtB,WAAK,cAAc;AACnB,WAAK,aAAa;AAGlB,UAAI,CAAC,iBAAiB;AACrBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,sBAAgB,MAAM;AAAA,QACrB,UAAU,KAAK,UAAU;AAAA,QACzB,YAAY;AAAA,QACZ,kBAAkB;AAAA,QAClB,eAAe;AAAA,QACf,QAAQ;AAAA,MACT,CAAC;AAGD,WAAK,cAAc,YAAY,MAAM;AACpC,aAAK;AACL,YAAI,KAAK,cAAc,KAAK,SAAS;AACpC,eAAK,cAAa;AAAA,QACnB;AAAA,MACA,GAAE,GAAI;AAGP,WAAK,wBAAuB;AAAA,IAC5B;AAAA;AAAA,IAGD,gBAAgB;AACf,UAAI,CAAC,KAAK;AAAa;AAEvB,YAAM,iBAAiB,KAAK;AAC5B,WAAK,cAAc;AACnB,WAAK,WAAW,aAAa;AAC7B,WAAK,WAAW,gBAAgB;AAChC,WAAK,YAAW;AAGhB,UAAI,iBAAiB,KAAK,SAAS;AAClC,YAAI,kBAAkB;AAAG;AAEzBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,WAAW,KAAK,OAAO;AAAA,UAC9B,MAAM;AAAA,QACP,CAAC;AACD,aAAK,aAAa;AAClB;AAAA,MACD;AAEA,UAAI,iBAAiB;AACpB,wBAAgB,KAAI;AAAA,MACrB;AAAA,IACA;AAAA;AAAA,IAGD,0BAA0B;AACzB,UAAI,CAAC,KAAK;AAAe;AAGzB,WAAK,qBAAoB;AAGzB,WAAK,iBAAiB;AAGtB,WAAK,iBAAiB,YAAY,MAAM;AACvC,aAAK,mBAAkB;AAAA,MACxB,GAAG,KAAK,aAAa;AAAA,IACrB;AAAA;AAAA,IAGD,uBAAuB;AACtB,WAAK,cAAc;AACnB,WAAK,cAAc,eAAe,SAAS;AAC3C,WAAK,cAAc,eAAe,GAAG;AACrC,WAAK,cAAc,aAAa,CAAC;AACjC,WAAK,cAAc,IAAI,KAAK,SAAS,KAAK,SAAS,KAAK,iBAAiB,GAAG,IAAI,KAAK,EAAE;AACvF,WAAK,cAAc;AACnB,WAAK,cAAc;IACnB;AAAA;AAAA,IAGD,qBAAqB;AACpB,WAAK,cAAc;AACnB,WAAK,cAAc,eAAe,SAAS;AAC3C,WAAK,cAAc,eAAe,CAAC;AACnC,WAAK,cAAc,aAAa,CAAC;AAEjC,YAAM,WAAW,KAAK,iBAAkB,KAAK,KAAK,UAAU,MAAO,KAAK;AACxE,WAAK,cAAc;AAAA,QAClB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,OAAO,KAAK;AAAA,QACZ,WAAW,KAAK;AAAA,QAChB;AAAA;AAGD,WAAK,cAAc;AACnB,WAAK,cAAc,KAAK,IAAI;AAE5B,WAAK,iBAAiB;AAAA,IACtB;AAAA;AAAA,IAGD,cAAc;AACb,UAAI,CAAC,KAAK;AAAe;AAEzB,WAAK,cAAc,aAAa,MAAM;AACtC,WAAK,cAAc,SAAS,GAAG,GAAG,KAAK,aAAa,KAAK,YAAY;AACrE,WAAK,cAAc;IACnB;AAAA;AAAA,IAGD,gBAAgB;AACf,UAAI,CAAC,KAAK,aAAa,KAAK;AAAW;AAEvC,mBAAa,MAAM,KAAK;AACxB,mBAAa,KAAI;AACjB,mBAAa,KAAI;AAEjB,WAAK,YAAY;AACjB,WAAK,eAAe,KAAK;AAGzB,WAAK,gBAAgB,YAAY,MAAM;AACtC,aAAK;AACL,YAAI,KAAK,gBAAgB,GAAG;AAC3B,eAAK,aAAY;AAAA,QAClB;AAAA,MACA,GAAE,GAAI;AAAA,IACP;AAAA;AAAA,IAGD,eAAe;AACd,UAAI,cAAc;AACjB,qBAAa,KAAI;AAAA,MAClB;AACA,WAAK,YAAY;AACjB,WAAK,eAAe;AACpB,WAAK,WAAW,eAAe;AAAA,IAC/B;AAAA;AAAA,IAGD,mBAAmB;AAClB,UAAI,CAAC,KAAK;AAAW;AAErB,WAAK,MAAM,WAAW,KAAK,SAAS;AACpC,WAAK,YAAW;AAAA,IAChB;AAAA;AAAA,IAGD,WAAW,WAAW;AACrB,UAAI,KAAK,SAAS,GAAG;AACpB,sBAAc,KAAK,SAAS,CAAC;AAC7B,aAAK,SAAS,IAAI;AAAA,MACnB;AAAA,IACA;AAAA;AAAA,IAGD,iBAAiB;AAChB,WAAK,WAAW,aAAa;AAC7B,WAAK,WAAW,eAAe;AAC/B,WAAK,WAAW,gBAAgB;AAAA,IAChC;AAAA;AAAA,IAGD,kBAAkB,OAAO;AACxB,WAAK,cAAc;AACnB,WAAK,eAAc;AACnB,WAAK,YAAW;AAEhBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAED,WAAK,MAAM,SAAS,EAAE,MAAM,UAAU,MAAI,CAAG;AAAA,IAC7C;AAAA;AAAA,IAGD,iBAAiB,OAAO;AACvB,WAAK,YAAY;AACjB,WAAK,WAAW,eAAe;AAE/BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAED,WAAK,MAAM,SAAS,EAAE,MAAM,SAAS,MAAI,CAAG;AAAA,IAC5C;AAAA;AAAA,IAGD,cAAc;AACb,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,UAAU;AACT,WAAK,eAAc;AACnB,UAAI,cAAc;AACjB,qBAAa,QAAO;AACpB,uBAAe;AAAA,MAChB;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvcA,GAAG,gBAAgB,SAAS;"}