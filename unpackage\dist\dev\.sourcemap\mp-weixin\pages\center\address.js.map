{"version": 3, "file": "address.js", "sources": ["pages/center/address.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2VudGVyL2FkZHJlc3MudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <navbar></navbar>\r\n    <view class=\"content-box\" :style=\"{'margin-top': statusBarHeight + titleBarHeight + 'px'}\">\r\n      <emptyPage\r\n        v-if=\"isEmpty\"\r\n        title=\"暂无收货地址\"\r\n        description=\"可新建地址或使用微信地址\"\r\n        buttonText=\"新建地址\"\r\n        @buttonClick=\"addAddress\"\r\n      />\r\n      <block v-else>\r\n        <view v-for=\"(item, index) in addressList\" :key=\"index\" class=\"list\" :data-id=\"item.id\" @tap=\"currentPages($event)\">\r\n          <view class=\"list-item\">\r\n            <view class=\"name\">{{ item.real_name }}</view>\r\n            <view class=\"mobile\">{{ item.phone }}</view>\r\n            <view class=\"adds\">{{ item.province }} {{ item.city }} {{ item.district }}</view>\r\n            <view class=\"adds\">{{ item.detail }}</view>\r\n            <view class=\"df\" style=\"margin-top:30rpx\" :data-idx=\"index\" @tap.stop=\"defaultAddsClick($event)\">\r\n              <image :src=\"item.is_default == 1 ? '/static/img/c1.png' : '/static/img/c.png'\" style=\"width:28rpx;height:28rpx\"></image>\r\n              <text style=\"margin-left:12rpx;color:#000;font-size:24rpx\">设为默认</text>\r\n            </view>\r\n          </view>\r\n          <image class=\"list-editor\" src=\"/static/img/bj.png\" :data-idx=\"index\" @tap.stop=\"updateAddsClick($event)\"></image>\r\n        </view>\r\n      </block>\r\n      <uni-load-more :status=\"loadStatus\"></uni-load-more>\r\n    </view>\r\n    <view class=\"btn-box\">\r\n      <view @tap=\"popupClick(true)\" class=\"bg1\">新建地址</view>\r\n      <!-- #ifdef MP -->\r\n      <view @tap=\"weixinAddsClick\" class=\"bg2\">使用微信地址</view>\r\n      <!-- #endif -->\r\n      <!-- #ifdef H5 -->\r\n      <view v-if=\"this.$wechat && this.$wechat.isWeixin()\" @tap=\"weixinAddsClick\" class=\"bg2\">使用微信地址</view>\r\n      <!-- #endif -->\r\n    </view>\r\n    <uni-popup ref=\"addsPopup\" type=\"bottom\" :safe-area=\"false\" class=\"r\">\r\n      <view class=\"popup-box\">\r\n        <view class=\"popup-top df\">\r\n          <view class=\"popup-title\">\r\n            <view class=\"t1\">{{ id > 0 ? '编辑地址' : '新建地址' }}</view>\r\n            <view class=\"t2\">请仔细核对地址信息后保存</view>\r\n          </view>\r\n          <view class=\"popup-close df\" @tap=\"popupClick(false)\">\r\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\r\n          </view>\r\n        </view>\r\n        <view class=\"popup-adds\">\r\n          <view class=\"adds-tit\">收货人</view>\r\n          <input class=\"adds-item\" cursor-spacing=\"20\" type=\"text\" placeholder=\"点击输入名字\" placeholder-class=\"apc\" v-model=\"real_name\" />\r\n        </view>\r\n        <view class=\"popup-adds\">\r\n          <view class=\"adds-tit\">手机号码</view>\r\n          <input class=\"adds-item\" cursor-spacing=\"20\" maxlength=\"11\" type=\"number\" placeholder=\"点击输入手机号\" placeholder-class=\"apc\" v-model=\"phone\" />\r\n        </view>\r\n        <view class=\"popup-adds\">\r\n          <view class=\"adds-tit\">地区</view>\r\n          <picker @change=\"bindPickerChange\" :value=\"valueRegion\" mode=\"multiSelector\" @columnchange=\"bindMultiPickerColumnChange\" :range=\"multiArray\">\r\n            <view class=\"adds-item ohto\">\r\n              <text v-if=\"region[0] !== '省'\">{{ region[0] }} {{ region[1] }} {{ region[2] }}</text>\r\n              <text v-else>点击选择</text>\r\n            </view>\r\n          </picker>\r\n        </view>\r\n        <view class=\"popup-adds\">\r\n          <view class=\"adds-tit\">详细地址</view>\r\n          <input class=\"adds-item\" cursor-spacing=\"20\" type=\"text\" placeholder=\"点击输入详细地址\" placeholder-class=\"apc\" v-model=\"detail\" />\r\n        </view>\r\n        <view class=\"popup-default\" @tap=\"ChangeIsDefault\">\r\n          <checkbox-group>\r\n            <checkbox :checked=\"is_default ? true : false\" />\r\n            <text>设置为默认地址</text>\r\n          </checkbox-group>\r\n        </view>\r\n        <view class=\"popup-btn bg1\" @tap=\"confirmAdds\">确认保存</view>\r\n        <view v-if=\"id > 0\" @tap=\"delAddsClick\" class=\"popup-btn\" style=\"color:#FA5150;height:48rpx;line-height:48rpx\">删除</view>\r\n      </view>\r\n    </uni-popup>\r\n    <uni-popup ref=\"tipsPopup\" type=\"top\" mask-background-color=\"rgba(0, 0, 0, 0)\">\r\n      <view class=\"tips-box df\">\r\n        <view class=\"tips-item\">{{ tipsTitle }}</view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { \r\n  getAddressList, \r\n  setAddressDefault, \r\n  delAddress, \r\n  editAddress,\r\n  getAddressDetail \r\n} from '@/api/user.js'\r\nimport { getCity } from '@/api/api.js'\r\nimport { toLogin } from '@/libs/login.js'\r\nimport { mapGetters } from 'vuex'\r\nimport navbar from '@/components/navbar/navbar.vue'\r\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\r\nimport uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'\r\nimport emptyPage from '@/components/emptyPage/emptyPage.vue'\r\n\r\nexport default {\r\n  components: {\r\n    navbar,\r\n    uniLoadMore,\r\n    uniPopup,\r\n    emptyPage\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\r\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\r\n      od: 0,\r\n      id: 0,\r\n      idx: -1,\r\n      addressList: [],\r\n      real_name: '',\r\n      phone: '',\r\n      detail: '',\r\n      is_default: false,\r\n      isEmpty: false,\r\n      loadStatus: 'loading',\r\n      tipsTitle: '',\r\n      \r\n      // 地区选择相关\r\n      region: ['省', '市', '区'],\r\n      valueRegion: [0, 0, 0],\r\n      multiArray: [],\r\n      multiIndex: [0, 0, 0],\r\n      district: [],\r\n      cityId: 0,\r\n      \r\n      // 分页相关\r\n      page: 1,\r\n      limit: 20,\r\n      loading: false,\r\n      loadend: false,\r\n      loadTitle: '加载更多'\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters(['isLogin'])\r\n  },\r\n  onLoad(options) {\r\n    if (this.isLogin) {\r\n      if (options.od) {\r\n        this.od = options.od;\r\n      }\r\n      this.getAddressList(true);\r\n      this.getCityList();\r\n    } else {\r\n      toLogin();\r\n    }\r\n  },\r\n  onShow() {\r\n    if (this.isLogin) {\r\n      this.getAddressList(true);\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取地址列表 - 完全参考 user_address_list 页面\r\n    getAddressList(isPage) {\r\n      let that = this;\r\n      if (isPage) {\r\n        that.loadend = false;\r\n        that.page = 1;\r\n        that.$set(that, 'addressList', []);\r\n      }\r\n      if (that.loading) return;\r\n      if (that.loadend) return;\r\n      \r\n      that.loading = true;\r\n      that.loadTitle = '';\r\n      \r\n      getAddressList({\r\n        page: that.page,\r\n        limit: that.limit\r\n      }).then(res => {\r\n        let list = res.data;\r\n        let loadend = list.length < that.limit;\r\n        \r\n        // 使用SplitArray方法合并数组 - 参考页面的实现\r\n        that.addressList = that.SplitArray(list, that.addressList);\r\n        that.$set(that, 'addressList', that.addressList);\r\n        \r\n        that.isEmpty = that.addressList.length === 0;\r\n        that.loadend = loadend;\r\n        that.loadTitle = loadend ? '我也是有底线的' : '加载更多';\r\n        that.loadStatus = loadend ? 'noMore' : 'more';\r\n        that.page = that.page + 1;\r\n        that.loading = false;\r\n      }).catch(err => {\r\n        that.loading = false;\r\n        that.loadStatus = 'more';\r\n        that.loadTitle = '加载更多';\r\n        // 参考页面不显示错误提示，只是静默处理\r\n      });\r\n    },\r\n    \r\n    // 添加SplitArray工具方法 - 参考页面使用的数组合并方法\r\n    SplitArray(list, arr) {\r\n      if (!Array.isArray(list)) return arr || [];\r\n      if (!Array.isArray(arr)) return list;\r\n      return arr.concat(list);\r\n    },\r\n    \r\n    // 获取城市列表 - 参考 user_address 页面\r\n    getCityList() {\r\n      let that = this;\r\n      getCity().then(res => {\r\n        that.district = res.data;\r\n        that.initialize();\r\n      }).catch(err => {\r\n        console.error('获取城市列表失败:', err);\r\n      });\r\n    },\r\n    \r\n    // 初始化地区选择器 - 参考 user_address 页面\r\n    initialize() {\r\n      let that = this;\r\n      let province = [];\r\n      let city = [];\r\n      let area = [];\r\n      \r\n      if (!that.district || that.district.length === 0) return;\r\n      \r\n      let cityChildren = that.district[0].c || [];\r\n      let areaChildren = cityChildren.length ? (cityChildren[0].c || []) : [];\r\n      \r\n      that.district.forEach((item, i) => {\r\n        province.push(item.n);\r\n        if (item.n === that.region[0]) {\r\n          that.valueRegion[0] = i;\r\n          that.multiIndex[0] = i;\r\n        }\r\n      });\r\n      \r\n      if (that.district[that.valueRegion[0]] && that.district[that.valueRegion[0]].c) {\r\n        that.district[that.valueRegion[0]].c.forEach((item, i) => {\r\n          if (that.region[1] === item.n) {\r\n            that.valueRegion[1] = i;\r\n            that.multiIndex[1] = i;\r\n          }\r\n          city.push(item.n);\r\n        });\r\n      }\r\n      \r\n      if (that.district[that.valueRegion[0]] && \r\n          that.district[that.valueRegion[0]].c && \r\n          that.district[that.valueRegion[0]].c[that.valueRegion[1]] && \r\n          that.district[that.valueRegion[0]].c[that.valueRegion[1]].c) {\r\n        that.district[that.valueRegion[0]].c[that.valueRegion[1]].c.forEach((item, i) => {\r\n          if (that.region[2] === item.n) {\r\n            that.valueRegion[2] = i;\r\n            that.multiIndex[2] = i;\r\n          }\r\n          area.push(item.n);\r\n        });\r\n      }\r\n      \r\n      that.multiArray = [province, city, area];\r\n    },\r\n    \r\n    // 地区选择改变 - 参考 user_address 页面\r\n    bindPickerChange(e) {\r\n      let multiIndex = this.multiIndex;\r\n      let province = this.district[multiIndex[0]] || { c: [] };\r\n      let city = province.c[multiIndex[1]] || { v: 0 };\r\n      let multiArray = this.multiArray;\r\n      let value = e.detail.value;\r\n      \r\n      this.region = [multiArray[0][value[0]], multiArray[1][value[1]], multiArray[2][value[2]]];\r\n      this.cityId = city.v;\r\n      this.valueRegion = [0, 0, 0];\r\n      this.initialize();\r\n    },\r\n    \r\n    // 地区选择列改变 - 参考 user_address 页面\r\n    bindMultiPickerColumnChange(e) {\r\n      let that = this;\r\n      let column = e.detail.column;\r\n      let value = e.detail.value;\r\n      let currentCity = that.district[value] || { c: [] };\r\n      let multiArray = that.multiArray;\r\n      let multiIndex = that.multiIndex;\r\n      \r\n      multiIndex[column] = value;\r\n      \r\n      switch (column) {\r\n        case 0:\r\n          let areaList = currentCity.c[0] || { c: [] };\r\n          multiArray[1] = currentCity.c.map((item) => {\r\n            return item.n;\r\n          });\r\n          multiArray[2] = areaList.c.map((item) => {\r\n            return item.n;\r\n          });\r\n          break;\r\n        case 1:\r\n          let cityList = that.district[multiIndex[0]].c[multiIndex[1]].c || [];\r\n          multiArray[2] = cityList.map((item) => {\r\n            return item.n;\r\n          });\r\n          break;\r\n        case 2:\r\n          break;\r\n      }\r\n      \r\n      that.multiArray = multiArray;\r\n      that.multiIndex = multiIndex;\r\n    },\r\n    \r\n    // 确认添加/编辑地址 - 完全参考 user_address 页面的 formSubmit 方法\r\n    confirmAdds() {\r\n      let that = this;\r\n      \r\n      if (!that.real_name.trim()) return that.Tips({\r\n        title: '请填写收货人姓名'\r\n      });\r\n      if (!that.phone) return that.Tips({\r\n        title: '请填写联系电话'\r\n      });\r\n      if (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.phone)) return that.Tips({\r\n        title: '请输入正确的手机号码'\r\n      });\r\n      if (that.region[0] === '省') return that.Tips({\r\n        title: '请选择所在地区'\r\n      });\r\n      if (!that.detail.trim()) return that.Tips({\r\n        title: '请填写详细地址'\r\n      });\r\n      \r\n      // 完全按照参考页面的参数结构\r\n      let value = {\r\n        id: that.id,\r\n        real_name: that.real_name,\r\n        phone: that.phone,\r\n        address: {\r\n          province: that.region[0],\r\n          city: that.region[1],\r\n          district: that.region[2],\r\n          city_id: that.cityId\r\n        },\r\n        detail: that.detail,\r\n        is_default: that.is_default ? 1 : 0\r\n      };\r\n      \r\n      // 如果是第一个地址，自动设为默认\r\n      if (that.addressList.length === 0) {\r\n        value.is_default = 1;\r\n      }\r\n      \r\n      uni.showLoading({\r\n        title: '保存中',\r\n        mask: true\r\n      });\r\n      \r\n      editAddress(value).then(res => {\r\n        uni.hideLoading();\r\n        \r\n        // 完全参考页面的提示方式\r\n        if (that.id) {\r\n          that.Tips({\r\n            title: '修改成功',\r\n            icon: 'success'\r\n          });\r\n        } else {\r\n          that.Tips({\r\n            title: '添加成功',\r\n            icon: 'success'\r\n          });\r\n        }\r\n        \r\n        setTimeout(function() {\r\n          that.getAddressList(true);\r\n          that.$refs.addsPopup.close();\r\n        }, 1000);\r\n      }).catch(err => {\r\n        uni.hideLoading();\r\n        return that.Tips({\r\n          title: err\r\n        });\r\n      });\r\n    },\r\n    \r\n    // 编辑地址 - 参考 user_address_list 页面\r\n    updateAddsClick(e) {\r\n      let idx = e.currentTarget.dataset.idx;\r\n      let item = this.addressList[idx];\r\n      \r\n      this.idx = idx;\r\n      this.id = item.id;\r\n      this.real_name = item.real_name;\r\n      this.phone = item.phone;\r\n      this.region = [item.province, item.city, item.district];\r\n      this.detail = item.detail;\r\n      this.is_default = item.is_default === 1;\r\n      this.cityId = item.city_id || 0;\r\n      \r\n      this.initialize();\r\n      this.$refs.addsPopup.open();\r\n    },\r\n    \r\n    // 删除地址 - 参考 user_address_list 页面\r\n    delAddsClick() {\r\n      let that = this;\r\n      \r\n      uni.showModal({\r\n        content: '您确定要删除这个收货地址吗？',\r\n        success: function(res) {\r\n          if (res.confirm) {\r\n            delAddress(that.id).then(res => {\r\n              that.Tips({\r\n                title: '删除成功',\r\n                icon: 'success'\r\n              }, function() {\r\n                that.getAddressList(true);\r\n                that.$refs.addsPopup.close();\r\n              });\r\n            }).catch(err => {\r\n              return that.Tips({\r\n                title: err\r\n              });\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 设置默认地址 - 完全参考 user_address_list 页面的 radioChange 方法\r\n    defaultAddsClick(e) {\r\n      let that = this;\r\n      let idx = e.currentTarget.dataset.idx;\r\n      let address = that.addressList[idx];\r\n      \r\n      if (address == undefined) return that.Tips({\r\n        title: '您设置的默认地址不存在!'\r\n      });\r\n      \r\n      setAddressDefault(address.id).then(res => {\r\n        // 完全按照参考页面的方式更新本地数据\r\n        for (let i = 0, len = that.addressList.length; i < len; i++) {\r\n          if (i == idx) that.addressList[i].is_default = true;\r\n          else that.addressList[i].is_default = false;\r\n        }\r\n        that.Tips({\r\n          title: '设置成功',\r\n          icon: 'success'\r\n        }, function() {\r\n          that.$set(that, 'addressList', that.addressList);\r\n        });\r\n      }).catch(err => {\r\n        return that.Tips({\r\n          title: err\r\n        });\r\n      });\r\n    },\r\n    \r\n    // 使用微信地址 - 完全参考 user_address 和 user_address_list 页面\r\n    weixinAddsClick() {\r\n      let that = this;\r\n      \r\n      // #ifdef MP\r\n      // 完全参考 user_address_list 页面的 getWxAddress 方法\r\n      uni.authorize({\r\n        scope: 'scope.address',\r\n        success: function(res) {\r\n          uni.chooseAddress({\r\n            success: function(res) {\r\n              let addressP = {};\r\n              addressP.province = res.provinceName;\r\n              addressP.city = res.cityName;\r\n              addressP.district = res.countyName;\r\n              \r\n              // 完全按照参考页面的参数结构调用 editAddress\r\n              editAddress({\r\n                address: addressP,\r\n                is_default: 1,\r\n                real_name: res.userName,\r\n                post_code: res.postalCode,\r\n                phone: res.telNumber,\r\n                detail: res.detailInfo,\r\n                id: 0,\r\n                type: 1\r\n              }).then(res => {\r\n                that.Tips({\r\n                  title: '添加成功',\r\n                  icon: 'success'\r\n                }, function() {\r\n                  that.getAddressList(true);\r\n                });\r\n              }).catch(err => {\r\n                return that.Tips({\r\n                  title: err\r\n                });\r\n              });\r\n            },\r\n            fail: function(err) {\r\n              if (err.errMsg == 'chooseAddress:cancel') return that.Tips({\r\n                title: '取消选择'\r\n              });\r\n            }\r\n          });\r\n        },\r\n        fail: function(res) {\r\n          uni.showModal({\r\n            title: '您已拒绝导入微信地址权限',\r\n            content: '是否进入权限管理，调整授权？',\r\n            success(res) {\r\n              if (res.confirm) {\r\n                uni.openSetting({\r\n                  success: function(res) {}\r\n                });\r\n              } else if (res.cancel) {\r\n                return that.Tips({\r\n                  title: '已取消！'\r\n                });\r\n              }\r\n            }\r\n          });\r\n        }\r\n      });\r\n      // #endif\r\n      \r\n      // #ifdef H5\r\n      // 完全参考 user_address_list 页面的 getAddress 方法\r\n      if (this.$wechat && this.$wechat.isWeixin()) {\r\n        this.$wechat.openAddress().then(userInfo => {\r\n          editAddress({\r\n            real_name: userInfo.userName,\r\n            phone: userInfo.telNumber,\r\n            address: {\r\n              province: userInfo.provinceName,\r\n              city: userInfo.cityName,\r\n              district: userInfo.countryName\r\n            },\r\n            detail: userInfo.detailInfo,\r\n            post_code: userInfo.postalCode,\r\n            is_default: 1,\r\n            type: 1\r\n          }).then(() => {\r\n                      that.Tips({\r\n            title: '添加成功',\r\n            icon: 'success'\r\n          }, function() {\r\n            that.getAddressList(true);\r\n          });\r\n        }).catch(err => {\r\n          return that.Tips({\r\n            title: err || '添加失败'\r\n          });\r\n        });\r\n        });\r\n      } else {\r\n        that.Tips({\r\n          title: '请在微信中打开'\r\n        });\r\n      }\r\n      // #endif\r\n      \r\n      // #ifdef APP-PLUS\r\n      that.Tips({\r\n        title: 'APP暂不支持微信地址导入'\r\n      });\r\n      // #endif\r\n    },\r\n    \r\n    // 弹窗控制\r\n    popupClick(show) {\r\n      if (show) {\r\n        this.id = 0;\r\n        this.real_name = '';\r\n        this.phone = '';\r\n        this.region = ['省', '市', '区'];\r\n        this.detail = '';\r\n        this.is_default = false;\r\n        this.valueRegion = [0, 0, 0];\r\n        this.multiIndex = [0, 0, 0];\r\n        this.initialize();\r\n        this.$refs.addsPopup.open();\r\n      } else {\r\n        this.$refs.addsPopup.close();\r\n      }\r\n    },\r\n    \r\n    // 选择地址返回\r\n    currentPages(e) {\r\n      let id = e.currentTarget.dataset.id;\r\n      if (this.od && id) {\r\n        var pages = getCurrentPages();\r\n        if (pages.length > 1) {\r\n          pages[pages.length - 2].$vm.addressInfo(id);\r\n        }\r\n        uni.navigateBack();\r\n      }\r\n    },\r\n    \r\n    // 提示弹窗 - 兼容原有方式和参考页面的 $util.Tips 方式\r\n    opTipsPopup(title) {\r\n      let that = this;\r\n      that.tipsTitle = title;\r\n      that.$refs.tipsPopup.open();\r\n      setTimeout(function() {\r\n        that.$refs.tipsPopup.close();\r\n      }, 2000);\r\n    },\r\n    \r\n    // Tips 工具方法 - 替代 $util.Tips\r\n    Tips(options, callback) {\r\n      if (typeof options === 'string') {\r\n        options = { title: options };\r\n      }\r\n      \r\n      if (options.icon === 'success') {\r\n        uni.showToast({\r\n          title: options.title,\r\n          icon: 'success',\r\n          duration: 2000,\r\n          success: function() {\r\n            if (callback && typeof callback === 'function') {\r\n              setTimeout(callback, 2000);\r\n            }\r\n          }\r\n        });\r\n      } else {\r\n        uni.showToast({\r\n          title: options.title,\r\n          icon: 'none',\r\n          duration: 2000,\r\n          success: function() {\r\n            if (callback && typeof callback === 'function') {\r\n              setTimeout(callback, 2000);\r\n            }\r\n          }\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 修改默认地址状态\r\n    ChangeIsDefault() {\r\n      this.is_default = !this.is_default;\r\n    },\r\n    \r\n    // 获取单个地址详情 - 参考 user_address 页面\r\n    getUserAddress() {\r\n      if (!this.id) return false;\r\n      let that = this;\r\n      getAddressDetail(this.id).then(res => {\r\n        let region = [res.data.province, res.data.city, res.data.district];\r\n        that.real_name = res.data.real_name;\r\n        that.phone = res.data.phone;\r\n        that.detail = res.data.detail;\r\n        that.is_default = res.data.is_default;\r\n        that.$set(that, 'region', region);\r\n        that.cityId = res.data.city_id;\r\n      }).catch(err => {\r\n        that.Tips({\r\n          title: err || '获取地址详情失败'\r\n        });\r\n      });\r\n    },\r\n\r\n    // 新建地址\r\n    addAddress() {\r\n      this.addsPopupClick(true);\r\n    }\r\n  },\r\n  \r\n  // 触底加载更多\r\n  onReachBottom() {\r\n    this.getAddressList();\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n  width: 100%;\r\n  padding-bottom: 320rpx;\r\n}\r\n.list {\r\n  border-top: 1px solid #f8f8f8;\r\n  width: calc(100% - 60rpx);\r\n  padding: 30rpx;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n.list .list-editor {\r\n  width: 34rpx;\r\n  height: 34rpx;\r\n}\r\n.list .list-item {\r\n  font-size: 24rpx;\r\n}\r\n.list .list-item .name {\r\n  color: #000;\r\n  font-weight: 700;\r\n}\r\n.list .list-item .mobile {\r\n  margin-top: 20rpx;\r\n  color: #000;\r\n}\r\n.list .list-item .adds {\r\n  color: #999;\r\n}\r\n.btn-box {\r\n  position: fixed;\r\n  z-index: 99;\r\n  left: 30rpx;\r\n  right: 30rpx;\r\n  bottom: 60rpx;\r\n  width: calc(100% - 60rpx);\r\n}\r\n.btn-box view {\r\n  width: 100%;\r\n  height: 100rpx;\r\n  line-height: 100rpx;\r\n  text-align: center;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n  border-radius: 100rpx;\r\n  border: 1px solid #000;\r\n}\r\n.bg1 {\r\n  color: #fff;\r\n  background: #000;\r\n}\r\n.bg2 {\r\n  margin-top: 30rpx;\r\n  color: #000;\r\n  background: #fff;\r\n}\r\n.popup-box {\r\n  width: calc(100% - 40rpx);\r\n  padding: 20rpx;\r\n  background: #fff;\r\n  border-radius: 30rpx 30rpx 0 0;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n.popup-box .popup-top {\r\n  width: calc(100% - 20rpx);\r\n  padding: 10rpx;\r\n  justify-content: space-between;\r\n}\r\n.popup-top .popup-title .t1 {\r\n  font-size: 38rpx;\r\n  font-weight: 700;\r\n}\r\n.popup-top .popup-title .t2 {\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  font-weight: 300;\r\n}\r\n.popup-top .popup-close {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 50%;\r\n  background: #f8f8f8;\r\n  justify-content: center;\r\n  transform: rotate(45deg);\r\n}\r\n.popup-box .popup-btn {\r\n  margin: 40rpx 10rpx;\r\n  width: calc(100% - 20rpx);\r\n  height: 100rpx;\r\n  line-height: 100rpx;\r\n  text-align: center;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n  border-radius: 100rpx;\r\n}\r\n.popup-adds .adds-tit {\r\n  padding: 30rpx 10rpx 0;\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  font-weight: 500;\r\n}\r\n.popup-adds .adds-item {\r\n  width: calc(100% - 20rpx);\r\n  margin: 0 10rpx;\r\n  height: 70rpx;\r\n  line-height: 70rpx;\r\n  color: #000;\r\n  font-size: 28rpx;\r\n  font-weight: 700;\r\n}\r\n.popup-default {\r\n  padding: 30rpx 10rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.popup-default checkbox-group {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.popup-default checkbox {\r\n  margin-right: 15rpx;\r\n}\r\n.popup-default text {\r\n  font-size: 28rpx;\r\n  color: #000;\r\n}\r\n.apc {\r\n  color: #000;\r\n}\r\n.empty-box {\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n.empty-box image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n.empty-box .e1 {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 10rpx;\r\n}\r\n.empty-box .e2 {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n.ohto {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.tips-box {\r\n  padding: 20rpx 30rpx;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  border-radius: 12rpx;\r\n  justify-content: center;\r\n}\r\n.tips-box .tips-item {\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  font-weight: 700;\r\n}\r\n.df {\r\n  display: flex;\r\n  align-items: center;\r\n  }\r\n</style> ", "import MiniProgramPage from 'D:/uniapp/vue3/pages/center/address.vue'\nwx.createPage(MiniProgramPage)"], "names": ["mapGetters", "<PERSON><PERSON><PERSON><PERSON>", "getAddressList", "getCity", "uni", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "res", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getAddressDetail"], "mappings": ";;;;;;AAkGA,eAAe,MAAW;AAC1B,MAAO,cAAa,MAAW;AAC/B,iBAAiB,MAAW;AAC5B,MAAK,YAAa,MAAW;AAE7B,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB,KAAK,OAAO,MAAM,mBAAmB;AAAA,MACtD,gBAAgB,KAAK,OAAO,MAAM,kBAAkB;AAAA,MACpD,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,aAAa,CAAE;AAAA,MACf,WAAW;AAAA,MACX,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA;AAAA,MAGX,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,aAAa,CAAC,GAAG,GAAG,CAAC;AAAA,MACrB,YAAY,CAAE;AAAA,MACd,YAAY,CAAC,GAAG,GAAG,CAAC;AAAA,MACpB,UAAU,CAAE;AAAA,MACZ,QAAQ;AAAA;AAAA,MAGR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,WAAW;AAAA,IACb;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,GAAGA,cAAU,WAAC,CAAC,SAAS,CAAC;AAAA,EAC1B;AAAA,EACD,OAAO,SAAS;AACd,QAAI,KAAK,SAAS;AAChB,UAAI,QAAQ,IAAI;AACd,aAAK,KAAK,QAAQ;AAAA,MACpB;AACA,WAAK,eAAe,IAAI;AACxB,WAAK,YAAW;AAAA,WACX;AACLC,iBAAAA;IACF;AAAA,EACD;AAAA,EACD,SAAS;AACP,QAAI,KAAK,SAAS;AAChB,WAAK,eAAe,IAAI;AAAA,IAC1B;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,eAAe,QAAQ;AACrB,UAAI,OAAO;AACX,UAAI,QAAQ;AACV,aAAK,UAAU;AACf,aAAK,OAAO;AACZ,aAAK,KAAK,MAAM,eAAe,CAAE,CAAA;AAAA,MACnC;AACA,UAAI,KAAK;AAAS;AAClB,UAAI,KAAK;AAAS;AAElB,WAAK,UAAU;AACf,WAAK,YAAY;AAEjBC,8BAAe;AAAA,QACb,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,OACb,EAAE,KAAK,SAAO;AACb,YAAI,OAAO,IAAI;AACf,YAAI,UAAU,KAAK,SAAS,KAAK;AAGjC,aAAK,cAAc,KAAK,WAAW,MAAM,KAAK,WAAW;AACzD,aAAK,KAAK,MAAM,eAAe,KAAK,WAAW;AAE/C,aAAK,UAAU,KAAK,YAAY,WAAW;AAC3C,aAAK,UAAU;AACf,aAAK,YAAY,UAAU,YAAY;AACvC,aAAK,aAAa,UAAU,WAAW;AACvC,aAAK,OAAO,KAAK,OAAO;AACxB,aAAK,UAAU;AAAA,MACjB,CAAC,EAAE,MAAM,SAAO;AACd,aAAK,UAAU;AACf,aAAK,aAAa;AAClB,aAAK,YAAY;AAAA,MAEnB,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,MAAM,KAAK;AACpB,UAAI,CAAC,MAAM,QAAQ,IAAI;AAAG,eAAO,OAAO;AACxC,UAAI,CAAC,MAAM,QAAQ,GAAG;AAAG,eAAO;AAChC,aAAO,IAAI,OAAO,IAAI;AAAA,IACvB;AAAA;AAAA,IAGD,cAAc;AACZ,UAAI,OAAO;AACXC,sBAAS,EAAC,KAAK,SAAO;AACpB,aAAK,WAAW,IAAI;AACpB,aAAK,WAAU;AAAA,MACjB,CAAC,EAAE,MAAM,SAAO;AACdC,sBAAc,MAAA,MAAA,SAAA,mCAAA,aAAa,GAAG;AAAA,MAChC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,aAAa;AACX,UAAI,OAAO;AACX,UAAI,WAAW,CAAA;AACf,UAAI,OAAO,CAAA;AACX,UAAI,OAAO,CAAA;AAEX,UAAI,CAAC,KAAK,YAAY,KAAK,SAAS,WAAW;AAAG;AAElD,UAAI,eAAe,KAAK,SAAS,CAAC,EAAE,KAAK;AACtB,mBAAa,SAAU,aAAa,CAAC,EAAE,KAAK,CAAA,IAAM,CAAE;AAEvE,WAAK,SAAS,QAAQ,CAAC,MAAM,MAAM;AACjC,iBAAS,KAAK,KAAK,CAAC;AACpB,YAAI,KAAK,MAAM,KAAK,OAAO,CAAC,GAAG;AAC7B,eAAK,YAAY,CAAC,IAAI;AACtB,eAAK,WAAW,CAAC,IAAI;AAAA,QACvB;AAAA,MACF,CAAC;AAED,UAAI,KAAK,SAAS,KAAK,YAAY,CAAC,CAAC,KAAK,KAAK,SAAS,KAAK,YAAY,CAAC,CAAC,EAAE,GAAG;AAC9E,aAAK,SAAS,KAAK,YAAY,CAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,MAAM;AACxD,cAAI,KAAK,OAAO,CAAC,MAAM,KAAK,GAAG;AAC7B,iBAAK,YAAY,CAAC,IAAI;AACtB,iBAAK,WAAW,CAAC,IAAI;AAAA,UACvB;AACA,eAAK,KAAK,KAAK,CAAC;AAAA,QAClB,CAAC;AAAA,MACH;AAEA,UAAI,KAAK,SAAS,KAAK,YAAY,CAAC,CAAC,KACjC,KAAK,SAAS,KAAK,YAAY,CAAC,CAAC,EAAE,KACnC,KAAK,SAAS,KAAK,YAAY,CAAC,CAAC,EAAE,EAAE,KAAK,YAAY,CAAC,CAAC,KACxD,KAAK,SAAS,KAAK,YAAY,CAAC,CAAC,EAAE,EAAE,KAAK,YAAY,CAAC,CAAC,EAAE,GAAG;AAC/D,aAAK,SAAS,KAAK,YAAY,CAAC,CAAC,EAAE,EAAE,KAAK,YAAY,CAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,MAAM;AAC/E,cAAI,KAAK,OAAO,CAAC,MAAM,KAAK,GAAG;AAC7B,iBAAK,YAAY,CAAC,IAAI;AACtB,iBAAK,WAAW,CAAC,IAAI;AAAA,UACvB;AACA,eAAK,KAAK,KAAK,CAAC;AAAA,QAClB,CAAC;AAAA,MACH;AAEA,WAAK,aAAa,CAAC,UAAU,MAAM,IAAI;AAAA,IACxC;AAAA;AAAA,IAGD,iBAAiB,GAAG;AAClB,UAAI,aAAa,KAAK;AACtB,UAAI,WAAW,KAAK,SAAS,WAAW,CAAC,CAAC,KAAK,EAAE,GAAG,CAAA;AACpD,UAAI,OAAO,SAAS,EAAE,WAAW,CAAC,CAAC,KAAK,EAAE,GAAG;AAC7C,UAAI,aAAa,KAAK;AACtB,UAAI,QAAQ,EAAE,OAAO;AAErB,WAAK,SAAS,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AACxF,WAAK,SAAS,KAAK;AACnB,WAAK,cAAc,CAAC,GAAG,GAAG,CAAC;AAC3B,WAAK,WAAU;AAAA,IAChB;AAAA;AAAA,IAGD,4BAA4B,GAAG;AAC7B,UAAI,OAAO;AACX,UAAI,SAAS,EAAE,OAAO;AACtB,UAAI,QAAQ,EAAE,OAAO;AACrB,UAAI,cAAc,KAAK,SAAS,KAAK,KAAK,EAAE,GAAG,CAAA;AAC/C,UAAI,aAAa,KAAK;AACtB,UAAI,aAAa,KAAK;AAEtB,iBAAW,MAAM,IAAI;AAErB,cAAQ,QAAM;AAAA,QACZ,KAAK;AACH,cAAI,WAAW,YAAY,EAAE,CAAC,KAAK,EAAE,GAAG,CAAA;AACxC,qBAAW,CAAC,IAAI,YAAY,EAAE,IAAI,CAAC,SAAS;AAC1C,mBAAO,KAAK;AAAA,UACd,CAAC;AACD,qBAAW,CAAC,IAAI,SAAS,EAAE,IAAI,CAAC,SAAS;AACvC,mBAAO,KAAK;AAAA,UACd,CAAC;AACD;AAAA,QACF,KAAK;AACH,cAAI,WAAW,KAAK,SAAS,WAAW,CAAC,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC,EAAE,KAAK,CAAA;AAClE,qBAAW,CAAC,IAAI,SAAS,IAAI,CAAC,SAAS;AACrC,mBAAO,KAAK;AAAA,UACd,CAAC;AACD;AAAA,MAGJ;AAEA,WAAK,aAAa;AAClB,WAAK,aAAa;AAAA,IACnB;AAAA;AAAA,IAGD,cAAc;AACZ,UAAI,OAAO;AAEX,UAAI,CAAC,KAAK,UAAU,KAAI;AAAI,eAAO,KAAK,KAAK;AAAA,UAC3C,OAAO;AAAA,QACT,CAAC;AACD,UAAI,CAAC,KAAK;AAAO,eAAO,KAAK,KAAK;AAAA,UAChC,OAAO;AAAA,QACT,CAAC;AACD,UAAI,CAAC,2BAA2B,KAAK,KAAK,KAAK;AAAG,eAAO,KAAK,KAAK;AAAA,UACjE,OAAO;AAAA,QACT,CAAC;AACD,UAAI,KAAK,OAAO,CAAC,MAAM;AAAK,eAAO,KAAK,KAAK;AAAA,UAC3C,OAAO;AAAA,QACT,CAAC;AACD,UAAI,CAAC,KAAK,OAAO,KAAI;AAAI,eAAO,KAAK,KAAK;AAAA,UACxC,OAAO;AAAA,QACT,CAAC;AAGD,UAAI,QAAQ;AAAA,QACV,IAAI,KAAK;AAAA,QACT,WAAW,KAAK;AAAA,QAChB,OAAO,KAAK;AAAA,QACZ,SAAS;AAAA,UACP,UAAU,KAAK,OAAO,CAAC;AAAA,UACvB,MAAM,KAAK,OAAO,CAAC;AAAA,UACnB,UAAU,KAAK,OAAO,CAAC;AAAA,UACvB,SAAS,KAAK;AAAA,QACf;AAAA,QACD,QAAQ,KAAK;AAAA,QACb,YAAY,KAAK,aAAa,IAAI;AAAA;AAIpC,UAAI,KAAK,YAAY,WAAW,GAAG;AACjC,cAAM,aAAa;AAAA,MACrB;AAEAA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAEDC,eAAAA,YAAY,KAAK,EAAE,KAAK,SAAO;AAC7BD,sBAAG,MAAC,YAAW;AAGf,YAAI,KAAK,IAAI;AACX,eAAK,KAAK;AAAA,YACR,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,eACI;AACL,eAAK,KAAK;AAAA,YACR,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAEA,mBAAW,WAAW;AACpB,eAAK,eAAe,IAAI;AACxB,eAAK,MAAM,UAAU;QACtB,GAAE,GAAI;AAAA,MACT,CAAC,EAAE,MAAM,SAAO;AACdA,sBAAG,MAAC,YAAW;AACf,eAAO,KAAK,KAAK;AAAA,UACf,OAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,GAAG;AACjB,UAAI,MAAM,EAAE,cAAc,QAAQ;AAClC,UAAI,OAAO,KAAK,YAAY,GAAG;AAE/B,WAAK,MAAM;AACX,WAAK,KAAK,KAAK;AACf,WAAK,YAAY,KAAK;AACtB,WAAK,QAAQ,KAAK;AAClB,WAAK,SAAS,CAAC,KAAK,UAAU,KAAK,MAAM,KAAK,QAAQ;AACtD,WAAK,SAAS,KAAK;AACnB,WAAK,aAAa,KAAK,eAAe;AACtC,WAAK,SAAS,KAAK,WAAW;AAE9B,WAAK,WAAU;AACf,WAAK,MAAM,UAAU;IACtB;AAAA;AAAA,IAGD,eAAe;AACb,UAAI,OAAO;AAEXA,oBAAAA,MAAI,UAAU;AAAA,QACZ,SAAS;AAAA,QACT,SAAS,SAAS,KAAK;AACrB,cAAI,IAAI,SAAS;AACfE,qBAAAA,WAAW,KAAK,EAAE,EAAE,KAAK,CAAAC,SAAO;AAC9B,mBAAK,KAAK;AAAA,gBACR,OAAO;AAAA,gBACP,MAAM;AAAA,iBACL,WAAW;AACZ,qBAAK,eAAe,IAAI;AACxB,qBAAK,MAAM,UAAU;cACvB,CAAC;AAAA,YACH,CAAC,EAAE,MAAM,SAAO;AACd,qBAAO,KAAK,KAAK;AAAA,gBACf,OAAO;AAAA,cACT,CAAC;AAAA,YACH,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,GAAG;AAClB,UAAI,OAAO;AACX,UAAI,MAAM,EAAE,cAAc,QAAQ;AAClC,UAAI,UAAU,KAAK,YAAY,GAAG;AAElC,UAAI,WAAW;AAAW,eAAO,KAAK,KAAK;AAAA,UACzC,OAAO;AAAA,QACT,CAAC;AAEDC,eAAAA,kBAAkB,QAAQ,EAAE,EAAE,KAAK,SAAO;AAExC,iBAAS,IAAI,GAAG,MAAM,KAAK,YAAY,QAAQ,IAAI,KAAK,KAAK;AAC3D,cAAI,KAAK;AAAK,iBAAK,YAAY,CAAC,EAAE,aAAa;AAAA;AAC1C,iBAAK,YAAY,CAAC,EAAE,aAAa;AAAA,QACxC;AACA,aAAK,KAAK;AAAA,UACR,OAAO;AAAA,UACP,MAAM;AAAA,WACL,WAAW;AACZ,eAAK,KAAK,MAAM,eAAe,KAAK,WAAW;AAAA,QACjD,CAAC;AAAA,MACH,CAAC,EAAE,MAAM,SAAO;AACd,eAAO,KAAK,KAAK;AAAA,UACf,OAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB;AAChB,UAAI,OAAO;AAIXJ,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,SAAS,KAAK;AACrBA,wBAAAA,MAAI,cAAc;AAAA,YAChB,SAAS,SAASG,MAAK;AACrB,kBAAI,WAAW,CAAA;AACf,uBAAS,WAAWA,KAAI;AACxB,uBAAS,OAAOA,KAAI;AACpB,uBAAS,WAAWA,KAAI;AAGxBF,mCAAY;AAAA,gBACV,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,WAAWE,KAAI;AAAA,gBACf,WAAWA,KAAI;AAAA,gBACf,OAAOA,KAAI;AAAA,gBACX,QAAQA,KAAI;AAAA,gBACZ,IAAI;AAAA,gBACJ,MAAM;AAAA,eACP,EAAE,KAAK,CAAAA,SAAO;AACb,qBAAK,KAAK;AAAA,kBACR,OAAO;AAAA,kBACP,MAAM;AAAA,mBACL,WAAW;AACZ,uBAAK,eAAe,IAAI;AAAA,gBAC1B,CAAC;AAAA,cACH,CAAC,EAAE,MAAM,SAAO;AACd,uBAAO,KAAK,KAAK;AAAA,kBACf,OAAO;AAAA,gBACT,CAAC;AAAA,cACH,CAAC;AAAA,YACF;AAAA,YACD,MAAM,SAAS,KAAK;AAClB,kBAAI,IAAI,UAAU;AAAwB,uBAAO,KAAK,KAAK;AAAA,kBACzD,OAAO;AAAA,gBACT,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACF;AAAA,QACD,MAAM,SAAS,KAAK;AAClBH,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,QAAQG,MAAK;AACX,kBAAIA,KAAI,SAAS;AACfH,8BAAAA,MAAI,YAAY;AAAA,kBACd,SAAS,SAASG,MAAK;AAAA,kBAAC;AAAA,gBAC1B,CAAC;AAAA,cACH,WAAWA,KAAI,QAAQ;AACrB,uBAAO,KAAK,KAAK;AAAA,kBACf,OAAO;AAAA,gBACT,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IA4CF;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,UAAI,MAAM;AACR,aAAK,KAAK;AACV,aAAK,YAAY;AACjB,aAAK,QAAQ;AACb,aAAK,SAAS,CAAC,KAAK,KAAK,GAAG;AAC5B,aAAK,SAAS;AACd,aAAK,aAAa;AAClB,aAAK,cAAc,CAAC,GAAG,GAAG,CAAC;AAC3B,aAAK,aAAa,CAAC,GAAG,GAAG,CAAC;AAC1B,aAAK,WAAU;AACf,aAAK,MAAM,UAAU;aAChB;AACL,aAAK,MAAM,UAAU;MACvB;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,GAAG;AACd,UAAI,KAAK,EAAE,cAAc,QAAQ;AACjC,UAAI,KAAK,MAAM,IAAI;AACjB,YAAI,QAAQ;AACZ,YAAI,MAAM,SAAS,GAAG;AACpB,gBAAM,MAAM,SAAS,CAAC,EAAE,IAAI,YAAY,EAAE;AAAA,QAC5C;AACAH,sBAAG,MAAC,aAAY;AAAA,MAClB;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,UAAI,OAAO;AACX,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU;AACrB,iBAAW,WAAW;AACpB,aAAK,MAAM,UAAU;MACtB,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,KAAK,SAAS,UAAU;AACtB,UAAI,OAAO,YAAY,UAAU;AAC/B,kBAAU,EAAE,OAAO;MACrB;AAEA,UAAI,QAAQ,SAAS,WAAW;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,QAAQ;AAAA,UACf,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,WAAW;AAClB,gBAAI,YAAY,OAAO,aAAa,YAAY;AAC9C,yBAAW,UAAU,GAAI;AAAA,YAC3B;AAAA,UACF;AAAA,QACF,CAAC;AAAA,aACI;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,QAAQ;AAAA,UACf,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,WAAW;AAClB,gBAAI,YAAY,OAAO,aAAa,YAAY;AAC9C,yBAAW,UAAU,GAAI;AAAA,YAC3B;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AAChB,WAAK,aAAa,CAAC,KAAK;AAAA,IACzB;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI,CAAC,KAAK;AAAI,eAAO;AACrB,UAAI,OAAO;AACXK,eAAAA,iBAAiB,KAAK,EAAE,EAAE,KAAK,SAAO;AACpC,YAAI,SAAS,CAAC,IAAI,KAAK,UAAU,IAAI,KAAK,MAAM,IAAI,KAAK,QAAQ;AACjE,aAAK,YAAY,IAAI,KAAK;AAC1B,aAAK,QAAQ,IAAI,KAAK;AACtB,aAAK,SAAS,IAAI,KAAK;AACvB,aAAK,aAAa,IAAI,KAAK;AAC3B,aAAK,KAAK,MAAM,UAAU,MAAM;AAChC,aAAK,SAAS,IAAI,KAAK;AAAA,MACzB,CAAC,EAAE,MAAM,SAAO;AACd,aAAK,KAAK;AAAA,UACR,OAAO,OAAO;AAAA,QAChB,CAAC;AAAA,MACH,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,aAAa;AACX,WAAK,eAAe,IAAI;AAAA,IAC1B;AAAA,EACD;AAAA;AAAA,EAGD,gBAAgB;AACd,SAAK,eAAc;AAAA,EACrB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjqBA,GAAG,WAAW,eAAe;"}