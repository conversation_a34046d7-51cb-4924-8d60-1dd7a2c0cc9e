{"version": 3, "file": "routine.js", "sources": ["libs/routine.js"], "sourcesContent": ["// +----------------------------------------------------------------------\r\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\r\n// +----------------------------------------------------------------------\r\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\r\n// +----------------------------------------------------------------------\r\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\r\n// +----------------------------------------------------------------------\r\n// | Author: CRMEB Team <<EMAIL>>\r\n// +----------------------------------------------------------------------\r\n\r\nimport store from '../store';\r\nimport {\r\n\tcheckLogin\r\n} from './login';\r\nimport {\r\n\tlogin,\r\n\troutineLogin,\r\n\tsilenceAuth\r\n} from '../api/public';\r\nimport Cache from '../utils/cache';\r\nimport cacheConfig from './../config/cache.js';\r\nconst {\r\n\tSTATE_R_KEY,\r\n\tUSER_INFO,\r\n\tEXPIRES_TIME,\r\n\tLOGIN_STATUS\r\n} = cacheConfig;\r\nimport {\r\n\tmapGetters\r\n} from \"vuex\";\r\nclass Routine {\r\n\r\n\tconstructor() {\r\n\t\tthis.scopeUserInfo = 'scope.userInfo';\r\n\t}\r\n\r\n\tasync getUserCode() {\r\n\t\tlet isAuth = await this.isAuth(),\r\n\t\t\tcode = '';\r\n\t\tif (isAuth)\r\n\t\t\tcode = await this.getCode();\r\n\t\treturn code;\r\n\t}\r\n\t// 小程序静默授权\r\n\t// silenceAuth(code) {\r\n\t// \tconst app = getApp();\r\n\t// \tlet that = this;\r\n\t// \tlet spread = app.globalData.spid ? app.globalData.spid : '';\r\n\t// \treturn new Promise((resolve, reject) => {\r\n\t// \t\tsilenceAuth({\r\n\t// \t\t\t\tcode: code,\r\n\t// \t\t\t\tspread_spid: spread,\r\n\t// \t\t\t\tspread_code: app.globalData.code\r\n\t// \t\t\t})\r\n\t// \t\t\t.then(res => {\r\n\t// \t\t\t\tif (res.data && res.data.token !== undefined) {\r\n\t// \t\t\t\t\tuni.hideLoading();\r\n\t// \t\t\t\t\tlet time = res.data.expires_time - Math.round(new Date() / 1000);\r\n\t// \t\t\t\t\tstore.commit('LOGIN', {\r\n\t// \t\t\t\t\t\ttoken: res.data.token,\r\n\t// \t\t\t\t\t\ttime: time\r\n\t// \t\t\t\t\t});\r\n\t// \t\t\t\t\tstore.commit('SETUID', res.data.userInfo.uid);\r\n\t// \t\t\t\t\tstore.commit('UPDATE_USERINFO', res.data.userInfo);\r\n\t// \t\t\t\t\tresolve(res)\r\n\t// \t\t\t\t} else {\r\n\t// \t\t\t\t\treject()\r\n\t// \t\t\t\t\tuni.navigateTo({\r\n\t// \t\t\t\t\t\turl: '/pages/users/wechat_login/index'\r\n\t// \t\t\t\t\t})\r\n\t// \t\t\t\t}\r\n\t// \t\t\t})\r\n\t// \t\t\t.catch(err => {\r\n\t// \t\t\t\treject(err)\r\n\t// \t\t\t});\r\n\t// \t})\r\n\t// }\r\n\t/**\r\n\t * 获取用户信息\r\n\t */\r\n\tgetUserInfo() {\r\n\t\tlet that = this,\r\n\t\t\tcode = this.getUserCode();\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\tuni.getUserInfo({\r\n\t\t\t\tlang: 'zh_CN',\r\n\t\t\t\tsuccess(user) {\r\n\t\t\t\t\tif (code) user.code = code;\r\n\t\t\t\t\tresolve({\r\n\t\t\t\t\t\tuserInfo: user,\r\n\t\t\t\t\t\tislogin: false\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tfail(res) {\r\n\t\t\t\t\treject(res);\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t})\r\n\t}\r\n\r\n\t/**\r\n\t * 新版小程序获取用户信息 2021 4.13微信小程序开始正式启用\r\n\t */\r\n\tgetUserProfile(code) {\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\tuni.getUserProfile({\r\n\t\t\t\tlang: 'zh_CN',\r\n\t\t\t\tdesc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写\r\n\t\t\t\tsuccess(user) {\r\n\t\t\t\t\tif (code) user.code = code;\r\n\t\t\t\t\tresolve({\r\n\t\t\t\t\t\tuserInfo: user,\r\n\t\t\t\t\t\tislogin: false\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tfail(res) {\r\n\t\t\t\t\treject(res);\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t})\r\n\t}\r\n\r\n\t/**\r\n\t * 获取用户信息\r\n\t */\r\n\tauthorize() {\r\n\t\tlet that = this;\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\tif (checkLogin())\r\n\t\t\t\treturn resolve({\r\n\t\t\t\t\tuserInfo: Cache.get(USER_INFO, true),\r\n\t\t\t\t\tislogin: true,\r\n\t\t\t\t});\r\n\t\t\tuni.authorize({\r\n\t\t\t\tscope: that.scopeUserInfo,\r\n\t\t\t\tsuccess() {\r\n\t\t\t\t\tresolve({\r\n\t\t\t\t\t\tislogin: false\r\n\t\t\t\t\t});\r\n\t\t\t\t},\r\n\t\t\t\tfail(res) {\r\n\t\t\t\t\treject(res);\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t})\r\n\t}\r\n\r\n\tasync getCode() {\r\n\t\tlet provider = await this.getProvider();\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t// if(Cache.has(STATE_R_KEY)){\r\n\t\t\t// \treturn resolve(Cache.get(STATE_R_KEY));\r\n\t\t\t// }\r\n\t\t\tuni.login({\r\n\t\t\t\tprovider: provider,\r\n\t\t\t\tsuccess(res) {\r\n\t\t\t\t\tif (res.code) Cache.set(STATE_R_KEY, res.code, 10800);\r\n\t\t\t\t\treturn resolve(res.code);\r\n\t\t\t\t},\r\n\t\t\t\tfail() {\r\n\t\t\t\t\treturn reject(null);\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t})\r\n\t}\r\n\r\n\t/**\r\n\t * 获取服务供应商\r\n\t */\r\n\tgetProvider() {\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\tuni.getProvider({\r\n\t\t\t\tservice: 'oauth',\r\n\t\t\t\tsuccess(res) {\r\n\t\t\t\t\tresolve(res.provider);\r\n\t\t\t\t},\r\n\t\t\t\tfail() {\r\n\t\t\t\t\tresolve(false);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * 是否授权\r\n\t */\r\n\tisAuth() {\r\n\t\tlet that = this;\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\tuni.getSetting({\r\n\t\t\t\tsuccess(res) {\r\n\t\t\t\t\tif (!res.authSetting[that.scopeUserInfo]) {\r\n\t\t\t\t\t\tresolve(true)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tresolve(true);\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail() {\r\n\t\t\t\t\tresolve(false);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\r\n\t}\r\n\t/**\r\n\t * 小程序比较版本信息\r\n\t * @param v1 当前版本\r\n\t * @param v2 进行比较的版本 \r\n\t * @return boolen\r\n\t * \r\n\t */\r\n\tcompareVersion(v1, v2) {\r\n\t\tv1 = v1.split('.')\r\n\t\tv2 = v2.split('.')\r\n\t\tconst len = Math.max(v1.length, v2.length)\r\n\r\n\t\twhile (v1.length < len) {\r\n\t\t\tv1.push('0')\r\n\t\t}\r\n\t\twhile (v2.length < len) {\r\n\t\t\tv2.push('0')\r\n\t\t}\r\n\r\n\t\tfor (let i = 0; i < len; i++) {\r\n\t\t\tconst num1 = parseInt(v1[i])\r\n\t\t\tconst num2 = parseInt(v2[i])\r\n\r\n\t\t\tif (num1 > num2) {\r\n\t\t\t\treturn 1\r\n\t\t\t} else if (num1 < num2) {\r\n\t\t\t\treturn -1\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn 0\r\n\t}\r\n\tauthUserInfo(data) {\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\troutineLogin(data).then(res => {\r\n\t\t\t\tif (res.data.key !== undefined && res.data.key) {} else {\r\n\t\t\t\t\tstore.commit('UPDATE_USERINFO', res.data.userInfo);\r\n\t\t\t\t\tstore.commit('SETUID', res.data.userInfo.uid);\r\n\t\t\t\t\tCache.set(USER_INFO, res.data.userInfo);\r\n\t\t\t\t}\r\n\t\t\t\treturn resolve(res);\r\n\t\t\t}).catch(res => {\r\n\t\t\t\treturn reject(res);\r\n\t\t\t})\r\n\t\t})\r\n\t}\r\n}\r\n\r\nexport default new Routine();\r\n"], "names": ["cacheConfig", "uni", "checkLogin", "<PERSON><PERSON>", "routineLogin", "store"], "mappings": ";;;;;;;AAqBA,MAAM;AAAA,EACL;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,IAAIA;AAIJ,MAAM,QAAQ;AAAA,EAEb,cAAc;AACb,SAAK,gBAAgB;AAAA,EACrB;AAAA,EAED,MAAM,cAAc;AACnB,QAAI,SAAS,MAAM,KAAK,OAAQ,GAC/B,OAAO;AACR,QAAI;AACH,aAAO,MAAM,KAAK;AACnB,WAAO;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsCD,cAAc;AACV,QACF,OAAO,KAAK,YAAc;AAC3B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCC,oBAAAA,MAAI,YAAY;AAAA,QACf,MAAM;AAAA,QACN,QAAQ,MAAM;AACb,cAAI;AAAM,iBAAK,OAAO;AACtB,kBAAQ;AAAA,YACP,UAAU;AAAA,YACV,SAAS;AAAA,UACf,CAAM;AAAA,QACD;AAAA,QACD,KAAK,KAAK;AACT,iBAAO,GAAG;AAAA,QACV;AAAA,MACL,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe,MAAM;AACpB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,oBAAAA,MAAI,eAAe;AAAA,QAClB,MAAM;AAAA,QACN,MAAM;AAAA;AAAA,QACN,QAAQ,MAAM;AACb,cAAI;AAAM,iBAAK,OAAO;AACtB,kBAAQ;AAAA,YACP,UAAU;AAAA,YACV,SAAS;AAAA,UACf,CAAM;AAAA,QACD;AAAA,QACD,KAAK,KAAK;AACT,iBAAO,GAAG;AAAA,QACV;AAAA,MACL,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,YAAY;AACX,QAAI,OAAO;AACX,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,UAAIC,sBAAY;AACf,eAAO,QAAQ;AAAA,UACd,UAAUC,YAAK,MAAC,IAAI,WAAW,IAAI;AAAA,UACnC,SAAS;AAAA,QACd,CAAK;AACFF,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK;AAAA,QACZ,UAAU;AACT,kBAAQ;AAAA,YACP,SAAS;AAAA,UACf,CAAM;AAAA,QACD;AAAA,QACD,KAAK,KAAK;AACT,iBAAO,GAAG;AAAA,QACV;AAAA,MACL,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AAAA,EAED,MAAM,UAAU;AACf,QAAI,WAAW,MAAM,KAAK;AAC1B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAIvCA,oBAAAA,MAAI,MAAM;AAAA,QACT;AAAA,QACA,QAAQ,KAAK;AACZ,cAAI,IAAI;AAAME,8BAAM,IAAI,aAAa,IAAI,MAAM,KAAK;AACpD,iBAAO,QAAQ,IAAI,IAAI;AAAA,QACvB;AAAA,QACD,OAAO;AACN,iBAAO,OAAO,IAAI;AAAA,QAClB;AAAA,MACL,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc;AACb,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCF,oBAAAA,MAAI,YAAY;AAAA,QACf,SAAS;AAAA,QACT,QAAQ,KAAK;AACZ,kBAAQ,IAAI,QAAQ;AAAA,QACpB;AAAA,QACD,OAAO;AACN,kBAAQ,KAAK;AAAA,QACb;AAAA,MACL,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,SAAS;AACR,QAAI,OAAO;AACX,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,oBAAAA,MAAI,WAAW;AAAA,QACd,QAAQ,KAAK;AACZ,cAAI,CAAC,IAAI,YAAY,KAAK,aAAa,GAAG;AACzC,oBAAQ,IAAI;AAAA,UAClB,OAAY;AACN,oBAAQ,IAAI;AAAA,UACZ;AAAA,QACD;AAAA,QACD,OAAO;AACN,kBAAQ,KAAK;AAAA,QACb;AAAA,MACL,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,eAAe,IAAI,IAAI;AACtB,SAAK,GAAG,MAAM,GAAG;AACjB,SAAK,GAAG,MAAM,GAAG;AACjB,UAAM,MAAM,KAAK,IAAI,GAAG,QAAQ,GAAG,MAAM;AAEzC,WAAO,GAAG,SAAS,KAAK;AACvB,SAAG,KAAK,GAAG;AAAA,IACX;AACD,WAAO,GAAG,SAAS,KAAK;AACvB,SAAG,KAAK,GAAG;AAAA,IACX;AAED,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC7B,YAAM,OAAO,SAAS,GAAG,CAAC,CAAC;AAC3B,YAAM,OAAO,SAAS,GAAG,CAAC,CAAC;AAE3B,UAAI,OAAO,MAAM;AAChB,eAAO;AAAA,MACX,WAAc,OAAO,MAAM;AACvB,eAAO;AAAA,MACP;AAAA,IACD;AAED,WAAO;AAAA,EACP;AAAA,EACD,aAAa,MAAM;AAClB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCG,iBAAAA,aAAa,IAAI,EAAE,KAAK,SAAO;AAC9B,YAAI,IAAI,KAAK,QAAQ,UAAa,IAAI,KAAK;AAAK;AAAA,aAAQ;AACvDC,sBAAK,MAAC,OAAO,mBAAmB,IAAI,KAAK,QAAQ;AACjDA,sBAAK,MAAC,OAAO,UAAU,IAAI,KAAK,SAAS,GAAG;AAC5CF,sBAAK,MAAC,IAAI,WAAW,IAAI,KAAK,QAAQ;AAAA,QACtC;AACD,eAAO,QAAQ,GAAG;AAAA,MACtB,CAAI,EAAE,MAAM,SAAO;AACf,eAAO,OAAO,GAAG;AAAA,MACrB,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AACF;AAEA,MAAe,YAAA,IAAI,QAAS;;"}