"use strict";
const common_vendor = require("../../common/vendor.js");
const api_user = require("../../api/user.js");
const navbar = () => "../../components/navbar/navbar.js";
const uniPopup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
const app = getApp();
const _sfc_main = {
  components: {
    navbar,
    uniPopup
  },
  data() {
    return {
      statusBarHeight: app.globalData.statusBarHeight || 20,
      titleBarHeight: app.globalData.titleBarHeight || 44,
      tipsTitle: "",
      isLoading: true,
      agreementData: {
        avatar: "",
        name: "",
        content: ""
      },
      agreementContent: ""
      // 使用简化的内容格式
    };
  },
  onReady() {
    this.getAgreement();
  },
  methods: {
    getAgreement() {
      const timeout = setTimeout(() => {
        if (this.isLoading) {
          this.isLoading = false;
        }
      }, 1500);
      const cachedAgreement = common_vendor.index.getStorageSync("user_cancel_agreement");
      if (cachedAgreement) {
        try {
          const agreementData = JSON.parse(cachedAgreement);
          this.agreementData = agreementData;
          this.agreementContent = agreementData.content;
          this.isLoading = false;
          clearTimeout(timeout);
          return;
        } catch (e) {
          common_vendor.index.__f__("log", "at pages/setting/logout.vue:97", "缓存协议解析失败", e);
        }
      }
      api_user.getUserAgreement(5).then((res) => {
        clearTimeout(timeout);
        this.agreementData = res.data;
        this.agreementContent = res.data.content;
        common_vendor.index.setStorageSync("user_cancel_agreement", JSON.stringify(res.data));
        this.isLoading = false;
      }).catch((err) => {
        clearTimeout(timeout);
        this.isLoading = false;
        this.tipsTitle = "获取协议失败";
        this.$refs.tipsPopup.open();
        setTimeout(() => {
          this.$refs.tipsPopup.close();
        }, 1500);
      });
    },
    submitClick() {
      let self = this;
      common_vendor.index.showModal({
        content: "确定要注销账号吗？",
        confirmColor: "#FA5150",
        success: function(res) {
          if (res.confirm) {
            common_vendor.index.showLoading({
              mask: true
            });
            api_user.cancelUser().then(function(res2) {
              common_vendor.index.hideLoading();
              app.globalData.spid = "";
              app.globalData.pid = "";
              self.$store.commit("LOGOUT");
              self.tipsTitle = "账号注销申请已提交，将在7个工作日内处理";
              self.$refs.tipsPopup.open();
              common_vendor.index.clearStorage();
              setTimeout(function() {
                self.$refs.tipsPopup.close();
                common_vendor.index.reLaunch({
                  url: "/pages/index/index"
                });
              }, 1500);
            }).catch(function(err) {
              common_vendor.index.hideLoading();
              self.tipsTitle = err || "注销失败，请稍后重试";
              self.$refs.tipsPopup.open();
              setTimeout(function() {
                self.$refs.tipsPopup.close();
              }, 1500);
            });
          }
        }
      });
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isLoading
  }, $data.isLoading ? {} : {}, {
    b: !$data.isLoading && $data.agreementData.avatar
  }, !$data.isLoading && $data.agreementData.avatar ? {
    c: $data.agreementData.avatar,
    d: common_vendor.t($data.agreementData.name)
  } : {}, {
    e: !$data.isLoading
  }, !$data.isLoading ? {
    f: $data.agreementContent
  } : {}, {
    g: common_vendor.o((...args) => $options.submitClick && $options.submitClick(...args)),
    h: common_vendor.t($data.tipsTitle),
    i: common_vendor.sr("tipsPopup", "2783d7b3-0"),
    j: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/setting/logout.js.map
