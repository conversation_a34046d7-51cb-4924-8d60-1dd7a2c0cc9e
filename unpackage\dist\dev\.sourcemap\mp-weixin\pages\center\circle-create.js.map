{"version": 3, "file": "circle-create.js", "sources": ["pages/center/circle-create.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2VudGVyL2NpcmNsZS1jcmVhdGUudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n\r\n    <view class=\"form-container\">\r\n      <!-- 圈子头像和背景 -->\r\n      <view class=\"form-item\">\r\n        <view class=\"form-label\">圈子头像和背景</view>\r\n        <view class=\"upload-row\">\r\n          <!-- 圈子头像 -->\r\n          <view class=\"upload-item avatar-item\">\r\n            <view class=\"upload-box avatar-box\" @tap=\"chooseAvatar\">\r\n              <image v-if=\"circleAvatar\" :src=\"circleAvatar\" mode=\"aspectFill\" class=\"upload-image\"></image>\r\n              <view v-else class=\"upload-placeholder\">\r\n                <text class=\"upload-icon\">+</text>\r\n                <text class=\"upload-text\">圈子头像</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 圈子背景 -->\r\n          <view class=\"upload-item background-item\">\r\n            <view class=\"upload-box background-box\" @tap=\"chooseBackground\">\r\n              <image v-if=\"circleBackground\" :src=\"circleBackground\" mode=\"aspectFill\" class=\"upload-image\"></image>\r\n              <view v-else class=\"upload-placeholder\">\r\n                <text class=\"upload-icon\">+</text>\r\n                <text class=\"upload-text\">圈子背景</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 圈子名称 -->\r\n      <view class=\"form-item\">\r\n        <view class=\"form-label\">圈子名称</view>\r\n        <input class=\"form-input\" placeholder=\"请输入圈子名称\" v-model=\"circleName\" maxlength=\"20\" />\r\n      </view>\r\n      \r\n      <!-- 圈子简介 -->\r\n      <view class=\"form-item\">\r\n        <view class=\"form-label\">圈子简介</view>\r\n        <textarea class=\"form-textarea\" placeholder=\"请输入圈子简介，让更多人了解你的圈子\" maxlength=\"200\" v-model=\"circleDesc\" />\r\n        <view class=\"form-count\">{{circleDesc.length}}/200</view>\r\n      </view>\r\n      \r\n      <!-- 圈子公告 -->\r\n      <view class=\"form-item\">\r\n        <view class=\"form-label\">圈子公告</view>\r\n        <textarea class=\"form-textarea\" placeholder=\"请输入圈子公告（可选）\" maxlength=\"100\" v-model=\"circleNotice\" />\r\n        <view class=\"form-count\">{{circleNotice.length}}/100</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 提交按钮 -->\r\n    <view class=\"btn-container\">\r\n      <button class=\"submit-btn\" @tap=\"submitCircle\" :disabled=\"isSubmitting\">\r\n        {{isSubmitting ? '创建中...' : '创建圈子'}}\r\n      </button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { createCircle } from '@/api/social'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      circleName: '',\r\n      circleDesc: '',\r\n      circleNotice: '',\r\n      circleAvatar: '',\r\n      circleBackground: '',\r\n      isSubmitting: false\r\n    }\r\n  },\r\n  methods: {\r\n    // 返回上一页\r\n    navBack() {\r\n      uni.navigateBack();\r\n    },\r\n    \r\n    // 选择圈子头像\r\n    chooseAvatar() {\r\n      this.uploadImage('avatar');\r\n    },\r\n    \r\n    // 选择圈子背景\r\n    chooseBackground() {\r\n      this.uploadImage('background');\r\n    },\r\n    \r\n    // 上传图片\r\n    uploadImage(type) {\r\n      // 使用全局上传工具方法\r\n      if (this.$util && this.$util.uploadImageChange) {\r\n        this.$util.uploadImageChange('upload/image', \r\n          // 上传成功\r\n          (res) => {\r\n            uni.hideLoading();\r\n            if (res.data && res.data.url) {\r\n              if (type === 'avatar') {\r\n                this.circleAvatar = res.data.url;\r\n                uni.showToast({\r\n                  title: '头像上传成功',\r\n                  icon: 'success'\r\n                });\r\n              } else {\r\n                this.circleBackground = res.data.url;\r\n                uni.showToast({\r\n                  title: '背景上传成功',\r\n                  icon: 'success'\r\n                });\r\n              }\r\n            } else {\r\n              console.error('图片上传返回数据异常:', res);\r\n              uni.showToast({\r\n                title: '图片上传失败',\r\n                icon: 'none'\r\n              });\r\n            }\r\n          },\r\n          // 上传失败或取消\r\n          (err) => {\r\n            uni.hideLoading();\r\n            console.log('图片上传取消或失败:', err);\r\n            if (err && err.errMsg && !err.errMsg.includes('cancel')) {\r\n              uni.showToast({\r\n                title: '图片上传失败',\r\n                icon: 'none'\r\n              });\r\n            }\r\n          },\r\n          // 处理图片尺寸\r\n          (res) => {\r\n            if (res && res.w && res.h) {\r\n              console.log(`${type}图片尺寸:`, res.w, 'x', res.h);\r\n            }\r\n          }\r\n        );\r\n      } else {\r\n        // 备用方案：使用原始的chooseImage方法\r\n        uni.chooseImage({\r\n          count: 1,\r\n          sizeType: ['compressed'],\r\n          sourceType: ['album', 'camera'],\r\n          success: (res) => {\r\n            const tempFilePath = res.tempFilePaths[0];\r\n            \r\n            // 检查文件大小\r\n            uni.getFileInfo({\r\n              filePath: tempFilePath,\r\n              success: (fileInfo) => {\r\n                const fileSizeMB = fileInfo.size / (1024 * 1024);\r\n                if (fileSizeMB > 5) {\r\n                  uni.showToast({\r\n                    title: '图片大小不能超过5MB',\r\n                    icon: 'none'\r\n                  });\r\n                  return;\r\n                }\r\n                \r\n                // 显示上传进度\r\n                uni.showLoading({\r\n                  title: '上传中...',\r\n                  mask: true\r\n                });\r\n                \r\n                // 上传图片\r\n                uni.uploadFile({\r\n                  url: this.$api.uploadUrl || '/api/upload/image',\r\n                  filePath: tempFilePath,\r\n                  name: 'file',\r\n                  success: (uploadRes) => {\r\n                    uni.hideLoading();\r\n                    try {\r\n                      const data = JSON.parse(uploadRes.data);\r\n                      if (data.code === 200 && data.data && data.data.url) {\r\n                        if (type === 'avatar') {\r\n                          this.circleAvatar = data.data.url;\r\n                          uni.showToast({\r\n                            title: '头像上传成功',\r\n                            icon: 'success'\r\n                          });\r\n                        } else {\r\n                          this.circleBackground = data.data.url;\r\n                          uni.showToast({\r\n                            title: '背景上传成功',\r\n                            icon: 'success'\r\n                          });\r\n                        }\r\n                      } else {\r\n                        throw new Error('上传返回数据异常');\r\n                      }\r\n                    } catch (e) {\r\n                      console.error('图片上传解析失败:', e);\r\n                      uni.showToast({\r\n                        title: '图片上传失败',\r\n                        icon: 'none'\r\n                      });\r\n                    }\r\n                  },\r\n                  fail: (err) => {\r\n                    uni.hideLoading();\r\n                    console.error('图片上传失败:', err);\r\n                    uni.showToast({\r\n                      title: '图片上传失败',\r\n                      icon: 'none'\r\n                    });\r\n                  }\r\n                });\r\n              },\r\n              fail: (err) => {\r\n                console.error('获取文件信息失败:', err);\r\n                uni.showToast({\r\n                  title: '无法获取图片信息',\r\n                  icon: 'none'\r\n                });\r\n              }\r\n            });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 提交创建圈子\r\n    submitCircle() {\r\n      if (!this.circleName.trim()) {\r\n        uni.showToast({\r\n          title: '请输入圈子名称',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (!this.circleDesc.trim()) {\r\n        uni.showToast({\r\n          title: '请输入圈子简介',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (!this.circleAvatar) {\r\n        uni.showToast({\r\n          title: '请上传圈子头像',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      this.isSubmitting = true;\r\n      \r\n      const data = {\r\n        circle_name: this.circleName.trim(),\r\n        circle_description: this.circleDesc.trim(),\r\n        circle_notice: this.circleNotice.trim(),\r\n        circle_avatar: this.circleAvatar,\r\n        circle_background: this.circleBackground\r\n      };\r\n      \r\n      createCircle(data).then(res => {\r\n        this.isSubmitting = false;\r\n        \r\n        if (res.status === 200) {\r\n          uni.showToast({\r\n            title: '创建成功',\r\n            icon: 'success'\r\n          });\r\n          \r\n          setTimeout(() => {\r\n            uni.navigateBack();\r\n          }, 1500);\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '创建失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      }).catch(err => {\r\n        this.isSubmitting = false;\r\n        console.error('创建圈子失败:', err);\r\n        uni.showToast({\r\n          title: '网络错误，请稍后重试',\r\n          icon: 'none'\r\n        });\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n  min-height: 100vh;\r\n  background-color: #f8f8f8;\r\n}\r\n\r\n/* 导航栏 */\r\n.nav-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  height: 88rpx;\r\n  padding: 0 30rpx;\r\n  background-color: #fff;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 999;\r\n}\r\n\r\n.nav-back {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.back-icon {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n}\r\n\r\n.nav-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.nav-right {\r\n  width: 60rpx;\r\n}\r\n\r\n/* 表单容器 */\r\n.form-container {\r\n  background-color: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 40rpx 30rpx;\r\n}\r\n\r\n.form-item {\r\n  margin-bottom: 40rpx;\r\n  position: relative;\r\n}\r\n\r\n.form-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.form-label {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  margin-bottom: 20rpx;\r\n  color: #333;\r\n}\r\n\r\n/* 上传区域 */\r\n.upload-row {\r\n  display: flex;\r\n  gap: 30rpx;\r\n}\r\n\r\n.upload-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n/* 头像区域占1份 */\r\n.avatar-item {\r\n  flex: 1;\r\n}\r\n\r\n/* 背景区域占3份 */\r\n.background-item {\r\n  flex: 3;\r\n}\r\n\r\n.upload-box {\r\n  width: 100%;\r\n  height: 160rpx;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  background-color: #f8f8f8;\r\n  border: 2rpx dashed #ddd;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.upload-box:active {\r\n  transform: scale(0.98);\r\n}\r\n\r\n.avatar-box {\r\n  border-radius: 20rpx;\r\n}\r\n\r\n.background-box {\r\n  border-radius: 12rpx;\r\n}\r\n\r\n.upload-image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.upload-placeholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 48rpx;\r\n  font-weight: 300;\r\n  color: #999;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.upload-text {\r\n  font-size: 20rpx;\r\n  color: #999;\r\n  text-align: center;\r\n}\r\n\r\n.upload-label {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  margin-top: 16rpx;\r\n  text-align: center;\r\n}\r\n\r\n/* 输入框 */\r\n.form-input {\r\n  width: 100%;\r\n  height: 88rpx;\r\n  border-radius: 12rpx;\r\n  background-color: #f8f8f8;\r\n  padding: 0 24rpx;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  box-sizing: border-box;\r\n  border: 2rpx solid transparent;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.form-input:focus {\r\n  background-color: #fff;\r\n  border-color: #333;\r\n}\r\n\r\n.form-textarea {\r\n  width: 100%;\r\n  min-height: 160rpx;\r\n  border-radius: 12rpx;\r\n  background-color: #f8f8f8;\r\n  padding: 24rpx;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  box-sizing: border-box;\r\n  border: 2rpx solid transparent;\r\n  transition: all 0.3s ease;\r\n  line-height: 1.6;\r\n}\r\n\r\n.form-textarea:focus {\r\n  background-color: #fff;\r\n  border-color: #333;\r\n}\r\n\r\n.form-count {\r\n  position: absolute;\r\n  right: 24rpx;\r\n  bottom: 16rpx;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 提交按钮 */\r\n.btn-container {\r\n  margin: 40rpx 20rpx;\r\n}\r\n\r\n.submit-btn {\r\n  width: 100%;\r\n  height: 96rpx;\r\n  line-height: 96rpx;\r\n  background: linear-gradient(135deg, #333 0%, #555 100%);\r\n  color: #fff;\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  border-radius: 48rpx;\r\n  border: none;\r\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.submit-btn:active {\r\n  transform: translateY(2rpx);\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.submit-btn[disabled] {\r\n  background: #ccc;\r\n  color: #999;\r\n  box-shadow: none;\r\n  transform: none;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 750rpx) {\r\n  .upload-row {\r\n    gap: 20rpx;\r\n  }\r\n  \r\n  .upload-box {\r\n    height: 140rpx;\r\n  }\r\n  \r\n  .upload-icon {\r\n    font-size: 40rpx;\r\n  }\r\n  \r\n  .upload-text {\r\n    font-size: 18rpx;\r\n  }\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/uniapp/vue3/pages/center/circle-create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "createCircle"], "mappings": ";;;AAiEA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,cAAc;AAAA,IAChB;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,UAAU;AACRA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,eAAe;AACb,WAAK,YAAY,QAAQ;AAAA,IAC1B;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,YAAY,YAAY;AAAA,IAC9B;AAAA;AAAA,IAGD,YAAY,MAAM;AAEhB,UAAI,KAAK,SAAS,KAAK,MAAM,mBAAmB;AAC9C,aAAK,MAAM;AAAA,UAAkB;AAAA;AAAA,UAE3B,CAAC,QAAQ;AACPA,0BAAG,MAAC,YAAW;AACf,gBAAI,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC5B,kBAAI,SAAS,UAAU;AACrB,qBAAK,eAAe,IAAI,KAAK;AAC7BA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR,CAAC;AAAA,qBACI;AACL,qBAAK,mBAAmB,IAAI,KAAK;AACjCA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR,CAAC;AAAA,cACH;AAAA,mBACK;AACLA,4BAAA,MAAA,MAAA,SAAA,yCAAc,eAAe,GAAG;AAChCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACD;AAAA;AAAA,UAED,CAAC,QAAQ;AACPA,0BAAG,MAAC,YAAW;AACfA,0BAAA,MAAA,MAAA,OAAA,yCAAY,cAAc,GAAG;AAC7B,gBAAI,OAAO,IAAI,UAAU,CAAC,IAAI,OAAO,SAAS,QAAQ,GAAG;AACvDA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACD;AAAA;AAAA,UAED,CAAC,QAAQ;AACP,gBAAI,OAAO,IAAI,KAAK,IAAI,GAAG;AACzBA,4BAAAA,MAAA,MAAA,OAAA,yCAAY,GAAG,IAAI,SAAS,IAAI,GAAG,KAAK,IAAI,CAAC;AAAA,YAC/C;AAAA,UACF;AAAA;aAEG;AAELA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,UAAU,CAAC,YAAY;AAAA,UACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,UAC9B,SAAS,CAAC,QAAQ;AAChB,kBAAM,eAAe,IAAI,cAAc,CAAC;AAGxCA,0BAAAA,MAAI,YAAY;AAAA,cACd,UAAU;AAAA,cACV,SAAS,CAAC,aAAa;AACrB,sBAAM,aAAa,SAAS,QAAQ,OAAO;AAC3C,oBAAI,aAAa,GAAG;AAClBA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO;AAAA,oBACP,MAAM;AAAA,kBACR,CAAC;AACD;AAAA,gBACF;AAGAA,8BAAAA,MAAI,YAAY;AAAA,kBACd,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR,CAAC;AAGDA,8BAAAA,MAAI,WAAW;AAAA,kBACb,KAAK,KAAK,KAAK,aAAa;AAAA,kBAC5B,UAAU;AAAA,kBACV,MAAM;AAAA,kBACN,SAAS,CAAC,cAAc;AACtBA,kCAAG,MAAC,YAAW;AACf,wBAAI;AACF,4BAAM,OAAO,KAAK,MAAM,UAAU,IAAI;AACtC,0BAAI,KAAK,SAAS,OAAO,KAAK,QAAQ,KAAK,KAAK,KAAK;AACnD,4BAAI,SAAS,UAAU;AACrB,+BAAK,eAAe,KAAK,KAAK;AAC9BA,wCAAAA,MAAI,UAAU;AAAA,4BACZ,OAAO;AAAA,4BACP,MAAM;AAAA,0BACR,CAAC;AAAA,+BACI;AACL,+BAAK,mBAAmB,KAAK,KAAK;AAClCA,wCAAAA,MAAI,UAAU;AAAA,4BACZ,OAAO;AAAA,4BACP,MAAM;AAAA,0BACR,CAAC;AAAA,wBACH;AAAA,6BACK;AACL,8BAAM,IAAI,MAAM,UAAU;AAAA,sBAC5B;AAAA,oBACF,SAAS,GAAG;AACVA,kGAAc,aAAa,CAAC;AAC5BA,oCAAAA,MAAI,UAAU;AAAA,wBACZ,OAAO;AAAA,wBACP,MAAM;AAAA,sBACR,CAAC;AAAA,oBACH;AAAA,kBACD;AAAA,kBACD,MAAM,CAAC,QAAQ;AACbA,kCAAG,MAAC,YAAW;AACfA,kCAAA,MAAA,MAAA,SAAA,yCAAc,WAAW,GAAG;AAC5BA,kCAAAA,MAAI,UAAU;AAAA,sBACZ,OAAO;AAAA,sBACP,MAAM;AAAA,oBACR,CAAC;AAAA,kBACH;AAAA,gBACF,CAAC;AAAA,cACF;AAAA,cACD,MAAM,CAAC,QAAQ;AACbA,8BAAc,MAAA,MAAA,SAAA,yCAAA,aAAa,GAAG;AAC9BA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACR,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACb,UAAI,CAAC,KAAK,WAAW,QAAQ;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,WAAW,QAAQ;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,cAAc;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,WAAK,eAAe;AAEpB,YAAM,OAAO;AAAA,QACX,aAAa,KAAK,WAAW,KAAM;AAAA,QACnC,oBAAoB,KAAK,WAAW,KAAM;AAAA,QAC1C,eAAe,KAAK,aAAa,KAAM;AAAA,QACvC,eAAe,KAAK;AAAA,QACpB,mBAAmB,KAAK;AAAA;AAG1BC,iBAAAA,aAAa,IAAI,EAAE,KAAK,SAAO;AAC7B,aAAK,eAAe;AAEpB,YAAI,IAAI,WAAW,KAAK;AACtBD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAED,qBAAW,MAAM;AACfA,0BAAG,MAAC,aAAY;AAAA,UACjB,GAAE,IAAI;AAAA,eACF;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC,EAAE,MAAM,SAAO;AACd,aAAK,eAAe;AACpBA,sBAAA,MAAA,MAAA,SAAA,yCAAc,WAAW,GAAG;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;AChSA,GAAG,WAAW,eAAe;"}