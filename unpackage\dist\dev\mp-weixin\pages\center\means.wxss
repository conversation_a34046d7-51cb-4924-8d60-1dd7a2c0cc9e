
/* 保持原有的CSS类名，只添加性能优化 */
.container{
	width: calc(100% - 60rpx);
	padding: 0 30rpx 60rpx;
}
.title-box{
	padding: 20rpx 0;
	font-size: 40rpx;
	font-weight: 700;
}

/* 相册部分样式 */
.album-section {
	margin: 30rpx 0;
	width: 100%;
}
.album-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
	color: #333;
}
.album-desc {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 20rpx;
	line-height: 1.4;
}
.change-avatar {
	color: #FA5150;
	margin-left: 10rpx;
	font-weight: bold;
}
.photo-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-top: 20rpx;
}
.photo-item {
	position: relative;
	width: 220rpx;
	height: 220rpx;
	margin-bottom: 20rpx;
	border-radius: 12rpx;
	overflow: hidden;
	border: 2rpx dashed #ddd;
}
.photo-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.photo-tag {
	position: absolute;
	top: 10rpx;
	left: 10rpx;
	background-color: rgba(0, 0, 0, 0.6);
	color: #fff;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 10rpx;
	font-weight: normal;
}
.photo-boost {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: #FA5150;
	color: #fff;
	font-size: 22rpx;
	padding: 8rpx 0;
	text-align: center;
}
.photo-placeholder {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100%;
	background-color: #f5f5f5;
}
.photo-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
}
.photo-type {
	color: #666;
	font-size: 24rpx;
	margin-bottom: 10rpx;
	text-align: center;
	padding: 0 10rpx;
	font-weight: normal;
}
.avatar-box{
	margin: 10rpx 0 0;
	width: 180rpx;
	height: 180rpx;
	padding: 0;
	background: none;
	position: relative;
}
.avatar-box .avatar{
	width: 100%;
	height: 100%;
	background: #f8f8f8;
	border-radius: 50%;
}
.avatar-box .icon{
	position: absolute;
	right: 0;
	bottom: 0;
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	justify-content: center;
	background: #000;
	border: 4rpx solid #fff;
}
.title-label{
	width: calc(100% - 48rpx);
	padding: 30rpx 24rpx 12rpx;
	color: #999;
	font-size: 24rpx;
	font-weight: 700;
}
.subtitle {
	font-weight: normal;
	font-size: 20rpx;
	margin-left: 10rpx;
}
.w50{
	width: calc(50% - 80rpx) !important;
}
.w70{
	width: calc(70% - 15rpx) !important;
}
.w25{
	width: calc(25% - 15rpx) !important;
}
.sp{
	justify-content: space-between;
}
.age{
	width: calc(50% - 75rpx) !important;
}
.gender-box{
	padding: 20rpx 0;
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}
.gender-box .active-1{
	border-color: #fa5150 !important;
	background: rgba(250, 81, 80, 0.125);
}
.gender-box .active-2{
	border-color: #4cd964 !important;
	background: rgba(76, 217, 100, 0.125);
}
.gender-box .gender-item{
	height: 90rpx;
	width: 180rpx;
	border-radius: 24rpx;
	border-width: 4rpx;
	border-style: solid;
	border-color: #f5f5f5;
	justify-content: center;
	margin: 10rpx;
}
.gender-box .gender-item image{
	width: 44rpx;
	height: 44rpx;
	margin-right: 10rpx;
}
.input-box{
	width: calc(100% - 68rpx);
	padding: 0 30rpx;
	height: 90rpx;
	line-height: 90rpx;
	font-size: 28rpx;
	font-weight: 700;
	border: 4rpx solid #f5f5f5;
	border-radius: 24rpx;
	justify-content: space-between;
}
.textarea-box{
	width: calc(100% - 68rpx);
	padding: 20rpx 30rpx;
	min-height: 90rpx;
	line-height: 48rpx;
	color: #000;
	font-size: 28rpx;
	font-weight: 700;
	border: 4rpx solid #f5f5f5;
	border-radius: 24rpx;
}
.input-btn{
	width: 90rpx;
	height: 90rpx;
	font-size: 24rpx;
	justify-content: space-between;
	margin: 0;
	padding: 0;
	background: #fff;
}
.input-btn image{
	margin-right: 8rpx;
	width: 32rpx;
	height: 32rpx;
}
.input-tips{
	margin-top: 20rpx;
	color: #999;
	font-size: 18rpx;
}
.popup-box{
	width: calc(100% - 40rpx);
	padding: 20rpx;
	background: #fff;
	border-radius: 30rpx 30rpx 0 0;
	position: relative;
	overflow: hidden;
}
.popup-box .popup-top{
	width: calc(100% - 20rpx);
	padding: 10rpx;
	justify-content: space-between;
}
.popup-top .popup-title .t1{
	font-size: 38rpx;
	font-weight: 700;
}
.popup-top .popup-title .t2{
	color: #999;
	font-size: 20rpx;
	font-weight: 300;
}
.popup-top .popup-close{
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: #f8f8f8;
	justify-content: center;
	transform: rotate(45deg);
}
.popup-box .popup-btn{
	margin: 40rpx 10rpx;
	width: calc(100% - 20rpx);
	height: 90rpx;
	line-height: 90rpx;
	text-align: center;
	font-size: 24rpx;
	font-weight: 700;
	color: #fff;
	background: #000;
	border-radius: 90rpx;
}
.popup-box .age-box{
	padding: 20rpx 0;
	display: flex;
	flex-wrap: wrap;
}
.age-box .age-item{
	margin: 10rpx;
	padding: 30rpx 40rpx;
	color: #000;
	border-width: 4rpx;
	border-style: solid;
	border-color: #f5f5f5;
	font-size: 24rpx;
	font-weight: 700;
	border-radius: 30rpx;
}
.tips-box {
	padding: 20rpx 30rpx;
	border-radius: 12rpx;
	justify-content: center;
	margin-top: 40rpx;
}
.tips-box .tips-item {
	color: #fff;
	font-size: 28rpx;
	font-weight: 600;
	text-align: center;
}
.df{
	display: flex;
	align-items: center;
}

/* 底部按钮样式 */
.footer-box {
	position: fixed;
	z-index: 99;
	bottom: 0;
	left: 0;
	width: 100%;
	padding-bottom: env(safe-area-inset-bottom);
}
.footer-box .footer-item {
	width: calc(100% - 60rpx);
	padding: 20rpx 30rpx;
	justify-content: center;
}
.footer-item .btn {
	width: calc(100% - 30rpx);
	height: 90rpx;
	line-height: 90rpx;
	text-align: center;
	font-size: 28rpx;
	font-weight: 700;
	border-radius: 45rpx;
}
.bg2 {
	color: #fff;
	background: #000;
}
.bfw {
	background: #fff;
}
.bUp {
	box-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);
}

/* 增加一些缺失的样式类 */
button {
	padding: 0;
	background-color: transparent;
}
button::after {
	border: none;
}
image {
	max-width: 100%;
	max-height: 100%;
}

/* 生日选择器样式 */
.birthday-picker {
	width: 100%;
	height: 400rpx;
	margin-top: 20rpx;
}
.picker-item {
	height: 50px;
	line-height: 50px;
	text-align: center;
	font-size: 28rpx;
}

/* 年龄标签样式 */
.age-tags {
	display: flex;
	flex-wrap: wrap;
	margin: 10rpx 0 20rpx;
}
.age-tag {
	padding: 10rpx 30rpx;
	margin: 10rpx;
	background-color: #f5f5f5;
	border-radius: 40rpx;
	font-size: 26rpx;
	color: #666;
}
.age-tag-active {
	background-color: #000;
	color: #fff;
}

/* 标签相关样式 */
.tags-box {
	padding: 20rpx 10rpx;
	display: flex;
	flex-wrap: wrap;
	max-height: 500rpx;
	overflow-y: auto;
}
.tag-item {
	padding: 16rpx 30rpx;
	margin: 10rpx;
	background-color: #f5f5f5;
	border-radius: 40rpx;
	font-size: 26rpx;
	color: #666;
}
.tagactive {
	background-color: #000;
	color: #fff;
}
.tag-categories {
	white-space: nowrap;
	padding: 20rpx 0;
	border-bottom: 1px solid #f0f0f0;
}
.category-item {
	display: inline-block;
	padding: 16rpx 30rpx;
	margin: 0 10rpx;
	background-color: #f5f5f5;
	border-radius: 40rpx;
	font-size: 26rpx;
	color: #666;
}
.category-active {
	background-color: #000;
	color: #fff;
}

/* 标签内容区 */
.tags-swiper {
	height: 400rpx;
	margin-top: 20rpx;
}
.tags-scroll {
	height: 100%;
}
.no-tags {
	text-align: center;
	color: #999;
	font-size: 28rpx;
	padding: 30rpx 0;
	width: 100%;
}

/* 底部安全区域 */
.bottom-safe-area {
	height: 150rpx;
	width: 100%;
}

/* 新增照片按钮样式 */
.add-photo {
	border: 2rpx dashed #ccc;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f9f9f9;
}
.add-icon {
	font-size: 60rpx;
	color: #999;
	text-align: center;
	line-height: 1;
}
.add-text {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}
.photo-button {
	width: 100%;
	height: 100%;
	padding: 0;
	margin: 0;
	background: none;
	border: none;
	line-height: normal;
	position: relative;
	display: block;
}
.photo-button::after {
	border: none;
}
.category-item {
	display: inline-block;
	padding: 16rpx 30rpx;
	margin: 0 10rpx;
	background-color: #f5f5f5;
	border-radius: 40rpx;
	font-size: 26rpx;
	color: #666;
}
.category-active {
	background-color: #000;
	color: #fff;
}
.category-item {
	display: inline-block;
	padding: 16rpx 30rpx;
	margin: 0 10rpx;
	background-color: #f5f5f5;
	border-radius: 40rpx;
	font-size: 26rpx;
	color: #666;
}
.category-active {
	background-color: #000;
	color: #fff;
}
.category-count {
	font-size: 20rpx;
	margin-left: 6rpx;
	opacity: 0.8;
}
