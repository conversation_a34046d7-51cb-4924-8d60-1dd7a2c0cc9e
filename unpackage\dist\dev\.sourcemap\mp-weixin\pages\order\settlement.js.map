{"version": 3, "file": "settlement.js", "sources": ["pages/order/settlement.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvb3JkZXIvc2V0dGxlbWVudC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 导航栏 -->\r\n    <navbar :bg=\"1\"></navbar>\r\n    \r\n    <!-- 收货地址 -->\r\n    <view class=\"w100p30\" :style=\"{'margin-top': statusBarHeight + titleBarHeight + 'px'}\">\r\n      <image class=\"map-bg\" src=\"/static/img/inset/map.jpg\" mode=\"widthFix\"></image>\r\n      <view class=\"mask-bg\"></view>\r\n      <view class=\"adds-box df\" data-url=\"center/address?od=1\" @tap=\"navigateToFun\">\r\n        <view class=\"adds-item\">\r\n          <block v-if=\"addsInfo.id > 0\">\r\n            <view style=\"font-weight:normal\">{{addsInfo.province}} {{addsInfo.city}} {{addsInfo.county}}</view>\r\n            <view class=\"txt\">{{addsInfo.detailed}}</view>\r\n            <view>{{addsInfo.name}} {{addsInfo.mobile}}</view>\r\n          </block>\r\n          <block v-else>\r\n            <view class=\"txt\">请选择收货地址</view>\r\n          </block>\r\n        </view>\r\n        <view class=\"adds-add\" style=\"padding:20rpx\">\r\n          <image src=\"/static/img/x.png\"></image>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 商品清单 -->\r\n    <view v-if=\"goodsList.length\" class=\"w100p30\">\r\n      <view class=\"title\">商品清单</view>\r\n      <view \r\n        v-for=\"(item, index) in goodsList\" \r\n        :key=\"index\" \r\n        class=\"goods-item df\">\r\n        <image :src=\"item.product.img\" mode=\"aspectFill\"></image>\r\n        <view class=\"goods-info\">\r\n          <view class=\"t1 ohto\">{{item.goods_name}}</view>\r\n          <view class=\"t2 ohto\">{{item.product.name}}</view>\r\n          <view class=\"goods-info-bom\">\r\n            <view class=\"sum\">{{item.quantity}}×</view>\r\n            <money :price=\"item.product.price\"></money>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 价格明细 -->\r\n    <view class=\"w100p30\">\r\n      <view class=\"title\">价格明细</view>\r\n      <view class=\"list-item df\" style=\"margin-top:15rpx\">\r\n        <view>实际商品总额</view>\r\n        <view class=\"list-right df\">\r\n          <text class=\"zs\">共{{orderCount}}件</text>\r\n          <money :price=\"goodsAmount\" qs=\"28rpx\" ts=\"18rpx\"></money>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"list-item df\">\r\n        <view>优惠金额</view>\r\n        <view class=\"list-right df\">\r\n          <text class=\"zs\">立减</text>\r\n          <money :price=\"discountAmount\" cor=\"#999\" qs=\"28rpx\" ts=\"18rpx\"></money>\r\n        </view>\r\n      </view>\r\n      \r\n      <view v-if=\"appCard\" class=\"list-item df\">\r\n        <view>卡券</view>\r\n        <view class=\"list-right df\" @tap=\"cardPopupClick(true)\">\r\n          <text class=\"zs\" :style=\"{'color': cardAmount ? '#FA5150' : '#999'}\">优惠</text>\r\n          <money :price=\"cardAmount\" :cor=\"cardAmount ? '#FA5150' : '#999'\" qs=\"28rpx\" ts=\"18rpx\"></money>\r\n          <text class=\"zs\" style=\"margin-right:0\">（{{cardCount ? '共 ' + cardCount + ' 张可用' : '暂无可用'}}）</text>\r\n          <image class=\"icon\" src=\"/static/img/x.png\"></image>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"list-item df\">\r\n        <view>运费</view>\r\n        <view class=\"list-right df\">\r\n          <text class=\"zs\">包邮</text>\r\n          <money :price=\"0.00\" cor=\"#999\" qs=\"28rpx\" ts=\"18rpx\"></money>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"list-item df\">\r\n        <view>总计</view>\r\n        <view class=\"list-right df\">\r\n          <money :price=\"orderAmount\" qs=\"28rpx\" ts=\"18rpx\"></money>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 订单备注 -->\r\n    <view class=\"w100p30\">\r\n      <view class=\"title\">订单备注</view>\r\n      <view class=\"list-item df\" style=\"margin-top:15rpx\">\r\n        <textarea auto-height=\"true\" placeholder=\"选填，订单备注（最多200字）\" maxlength=\"200\" v-model=\"remark\"></textarea>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 买家须知 -->\r\n    <view class=\"w100p30\">\r\n      <view class=\"title\">买家须知</view>\r\n      <view class=\"list-item df\">\r\n        <view class=\"list-item-xz\" data-url=\"setting/xinxuan?id=4\" @tap=\"navigateToFun\">\r\n          提交订单即表示您同意{{appXx[0]}}的<text>退换货政策</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部支付按钮 -->\r\n    <view class=\"footer df\">\r\n      <view class=\"btn df\" @tap=\"settlementPay\">\r\n        <image src=\"/static/img/pay.png\"></image>\r\n        <text>¥ {{orderAmount}}</text>\r\n        <text>支付</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 卡券弹窗 -->\r\n    <uni-popup ref=\"cardPopup\" type=\"bottom\" :safe-area=\"false\">\r\n      <view class=\"popup-box\">\r\n        <view class=\"popup-top df\">\r\n          <view class=\"popup-title\">\r\n            <view class=\"t1\">选择卡券</view>\r\n            <view class=\"t2\">勾选一张卡券确认后即可使用</view>\r\n          </view>\r\n          <view class=\"popup-close df\" @tap=\"cardPopupClick(false)\">\r\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"popup-scroll\">\r\n          <view v-if=\"cardList.length <= 0\" class=\"empty-box df\">\r\n            <image src=\"/static/empty.png\"/>\r\n            <view class=\"e1\">暂无卡券</view>\r\n            <view class=\"e2\">去领券中心，领取或兑换卡券吧</view>\r\n          </view>\r\n          <block v-else>\r\n            <view \r\n              v-for=\"(item, index) in cardList\" \r\n              :key=\"index\" \r\n              class=\"coupon\" \r\n              :style=\"{'border-color': item.id == cardSelectId ? '#000' : '#f8f8f8'}\" \r\n              @tap=\"cardSelectId = item.id\">\r\n              <image class=\"coupon-bg\" src=\"/static/img/coupon-bg.png\"></image>\r\n              <view class=\"coupon-sub\">{{item.card.subscript}}</view>\r\n              <view class=\"coupon-item\">\r\n                <view class=\"coupon-price\">{{item.card.price}} 元</view>\r\n                <view class=\"coupon-intro\">{{item.card.intro}}</view>\r\n              </view>\r\n              <view class=\"coupon-validity\">\r\n                有效期：{{item.card.neck_create_time}} - {{item.card.expire_time}}\r\n              </view>\r\n              <view v-if=\"!item.available\" class=\"coupon-err df\">未满足使用条件，不可用</view>\r\n            </view>\r\n          </block>\r\n        </view>\r\n        \r\n        <view class=\"popup-btn\" @tap=\"cardPopupClick(false)\">确认</view>\r\n      </view>\r\n    </uni-popup>\r\n    \r\n    <!-- 提示弹窗 -->\r\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\r\n      <view class=\"tips-box df\">\r\n        <view class=\"tips-item\">{{tipsTitle}}</view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request.js'\r\nimport * as api from '@/config/api.js'\r\nimport navbar from '@/components/navbar/navbar.vue'\r\nimport money from '@/components/money/money.vue'\r\nimport uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'\r\n\r\nconst app = getApp()\r\n\r\nexport default {\r\n  components: {\r\n    navbar,\r\n    money,\r\n    uniPopup\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\r\n      appCard: app.globalData?.isCard || false,\r\n      appXx: app.globalData?.appXx || ['商城'],\r\n      type: 0,\r\n      productId: 0,\r\n      quantity: 1,\r\n      addsInfo: {\r\n        id: 0\r\n      },\r\n      goodsList: [],\r\n      orderCount: 0,\r\n      goodsAmount: \"0.00\",\r\n      discountAmount: \"0.00\",\r\n      orderAmount: \"0.00\",\r\n      reserveOrderAmount: \"0.00\",\r\n      cardAmount: 0,\r\n      cardId: 0,\r\n      cardCount: 0,\r\n      cardSelectId: 0,\r\n      cardList: [],\r\n      remark: \"\",\r\n      tipsTitle: \"\",\r\n      \r\n      // Mock数据\r\n      mockGoodsList: [\r\n        {\r\n          goods_id: 1001,\r\n          goods_name: \"2023夏季新款连衣裙\",\r\n          quantity: 1,\r\n          product: {\r\n            img: \"/static/img/avatar.png\",\r\n            name: \"白色 L码\",\r\n            price: \"299.00\",\r\n            line_price: \"399.00\"\r\n          }\r\n        },\r\n        {\r\n          goods_id: 1002,\r\n          goods_name: \"轻薄透气运动鞋\",\r\n          quantity: 1,\r\n          product: {\r\n            img: \"/static/img/avatar.png\",\r\n            name: \"黑色 40码\",\r\n            price: \"199.00\",\r\n            line_price: \"299.00\"\r\n          }\r\n        }\r\n      ],\r\n      \r\n      mockAddress: {\r\n        id: 1,\r\n        name: \"张三\",\r\n        mobile: \"13800138000\",\r\n        province: \"广东省\",\r\n        city: \"广州市\",\r\n        county: \"天河区\",\r\n        detailed: \"天河路123号时尚大厦A座2301\"\r\n      },\r\n      \r\n      mockCardList: [\r\n        {\r\n          id: 101,\r\n          available: true,\r\n          card: {\r\n            subscript: \"满300可用\",\r\n            price: \"50.00\",\r\n            intro: \"商城通用满300减50优惠券\",\r\n            neck_create_time: \"2023-05-01\",\r\n            expire_time: \"2023-12-31\"\r\n          }\r\n        },\r\n        {\r\n          id: 102,\r\n          available: true,\r\n          card: {\r\n            subscript: \"无门槛\",\r\n            price: \"10.00\",\r\n            intro: \"商城通用无门槛优惠券\",\r\n            neck_create_time: \"2023-05-01\",\r\n            expire_time: \"2023-12-31\"\r\n          }\r\n        },\r\n        {\r\n          id: 103,\r\n          available: false,\r\n          card: {\r\n            subscript: \"满500可用\",\r\n            price: \"100.00\",\r\n            intro: \"商城通用满500减100优惠券\",\r\n            neck_create_time: \"2023-05-01\",\r\n            expire_time: \"2023-12-31\"\r\n          }\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    if (options.type && options.type == 1) {\r\n      this.type = options.type\r\n      this.productId = options.pid\r\n      this.quantity = options.quantity\r\n    }\r\n    \r\n    this.addressInfo(0)\r\n    this.goodsSettlement()\r\n  },\r\n  methods: {\r\n    // 获取商品结算信息\r\n    goodsSettlement() {\r\n      let that = this\r\n      \r\n      // 检查API是否可用\r\n      if (api.api && api.api.goodsSettlementUrl) {\r\n        request(api.api.goodsSettlementUrl, {\r\n          type: that.type,\r\n          pid: that.productId,\r\n          quantity: that.quantity\r\n        }).then(function(res) {\r\n          if (res.code == 200) {\r\n            that.goodsList = res.data\r\n            that.calculate()\r\n          } else {\r\n            that.opTipsPopup(res.msg, true)\r\n          }\r\n        })\r\n      } else {\r\n        // 使用Mock数据\r\n        setTimeout(() => {\r\n          that.goodsList = that.mockGoodsList\r\n          that.calculate()\r\n        }, 500)\r\n      }\r\n    },\r\n    \r\n    // 结算支付\r\n    settlementPay() {\r\n      let that = this\r\n      \r\n      if (that.orderAmount <= 0) {\r\n        that.opTipsPopup(\"支付金额不能小于0.01！\")\r\n        return\r\n      }\r\n      \r\n      if (that.addsInfo.id <= 0) {\r\n        that.opTipsPopup(\"请选择一个收货地址！\")\r\n        return\r\n      }\r\n      \r\n      uni.showLoading({\r\n        title: \"生成订单中..\",\r\n        mask: true\r\n      })\r\n      \r\n      // 检查API是否可用\r\n      if (api.api && api.api.settlementPayUrl) {\r\n        request(api.api.settlementPayUrl, {\r\n          type: that.type,\r\n          pid: that.productId,\r\n          quantity: that.quantity,\r\n          aid: that.addsInfo.id,\r\n          cid: that.cardId,\r\n          remark: that.remark\r\n        }, \"POST\").then(function(res) {\r\n          uni.hideLoading()\r\n          app.globalData.isCenterPage = true\r\n          \r\n          if (res.code == 200) {\r\n            let payData = res.data\r\n            uni.requestPayment({\r\n              provider: \"weixin\",\r\n              timeStamp: payData.timestamp,\r\n              nonceStr: payData.nonceStr,\r\n              package: payData.package,\r\n              signType: payData.signType,\r\n              paySign: payData.paySign,\r\n              success: function() {\r\n                that.opTipsPopup(\"下单成功，我们会尽快为您发货 🎉\")\r\n                setTimeout(function() {\r\n                  uni.redirectTo({\r\n                    url: \"/pages/order/index?idx=2\"\r\n                  })\r\n                }, 1000)\r\n              },\r\n              fail: function() {\r\n                that.opTipsPopup(\"支付失败！\")\r\n                setTimeout(function() {\r\n                  uni.redirectTo({\r\n                    url: \"/pages/order/index?idx=1\"\r\n                  })\r\n                }, 1000)\r\n              }\r\n            })\r\n          } else {\r\n            that.opTipsPopup(res.msg)\r\n          }\r\n        })\r\n      } else {\r\n        // 模拟支付流程\r\n        setTimeout(() => {\r\n          uni.hideLoading()\r\n          app.globalData.isCenterPage = true\r\n          \r\n          uni.showModal({\r\n            title: '模拟支付',\r\n            content: '这是一个模拟的支付流程，点击确定将模拟支付成功',\r\n            success: function(res) {\r\n              if (res.confirm) {\r\n                that.opTipsPopup(\"下单成功，我们会尽快为您发货 🎉\")\r\n                setTimeout(function() {\r\n                  uni.redirectTo({\r\n                    url: \"/pages/order/index?idx=2\"\r\n                  })\r\n                }, 1000)\r\n              } else {\r\n                that.opTipsPopup(\"支付失败！\")\r\n                setTimeout(function() {\r\n                  uni.redirectTo({\r\n                    url: \"/pages/order/index?idx=1\"\r\n                  })\r\n                }, 1000)\r\n              }\r\n            }\r\n          })\r\n        }, 1000)\r\n      }\r\n    },\r\n    \r\n    // 获取地址信息\r\n    addressInfo(id) {\r\n      let that = this\r\n      \r\n      uni.showLoading({\r\n        mask: true\r\n      })\r\n      \r\n      // 检查API是否可用\r\n      if (api.api && api.api.userAddressInfoUrl) {\r\n        request(api.api.userAddressInfoUrl, {\r\n          id: id\r\n        }).then(function(res) {\r\n          uni.hideLoading()\r\n          \r\n          if (res.code == 200 && res.data) {\r\n            that.addsInfo = res.data\r\n          }\r\n        })\r\n      } else {\r\n        // 使用Mock数据\r\n        setTimeout(() => {\r\n          uni.hideLoading()\r\n          that.addsInfo = that.mockAddress\r\n        }, 500)\r\n      }\r\n    },\r\n    \r\n    // 计算价格\r\n    calculate() {\r\n      let that = this\r\n      let count = 0\r\n      let goodsAmount = 0\r\n      let discountAmount = 0\r\n      let orderAmount = 0\r\n      \r\n      for (let item of that.goodsList) {\r\n        goodsAmount += parseFloat(item.product.line_price * item.quantity)\r\n        discountAmount += parseFloat((item.product.line_price - item.product.price) * item.quantity)\r\n        orderAmount += parseFloat(item.product.price * item.quantity)\r\n        count += parseInt(item.quantity)\r\n      }\r\n      \r\n      that.goodsAmount = goodsAmount.toFixed(2)\r\n      that.discountAmount = discountAmount.toFixed(2)\r\n      that.orderAmount = orderAmount.toFixed(2)\r\n      that.orderCount = count\r\n      \r\n      if (that.appCard) {\r\n        that.useCard()\r\n      }\r\n    },\r\n    \r\n    // 使用卡券\r\n    useCard() {\r\n      let that = this\r\n      \r\n      // 检查API是否可用\r\n      if (api.api && api.api.useCardUrl) {\r\n        request(api.api.useCardUrl, {\r\n          product_id: that.productId,\r\n          amount: that.orderAmount\r\n        }, \"POST\").then(function(res) {\r\n          if (res.code == 200 && res.data[0]) {\r\n            that.cardList = res.data[2]\r\n            that.cardCount = res.data[1]\r\n            that.cardSelectId = res.data[0].id\r\n            that.cardId = res.data[0].id\r\n            that.cardAmount = res.data[0].card.price\r\n            that.reserveOrderAmount = that.orderAmount\r\n            \r\n            let newAmount = that.reserveOrderAmount - res.data[0].card.price\r\n            that.orderAmount = newAmount.toFixed(2)\r\n          }\r\n        })\r\n      } else {\r\n        // 使用Mock数据\r\n        setTimeout(() => {\r\n          that.cardList = that.mockCardList\r\n          that.cardCount = that.mockCardList.length\r\n          \r\n          // 默认选择第一张可用卡券\r\n          let availableCard = that.mockCardList.find(card => card.available === true)\r\n          if (availableCard) {\r\n            that.cardSelectId = availableCard.id\r\n            that.cardId = availableCard.id\r\n            that.cardAmount = availableCard.card.price\r\n            that.reserveOrderAmount = that.orderAmount\r\n            \r\n            let newAmount = parseFloat(that.reserveOrderAmount) - parseFloat(availableCard.card.price)\r\n            that.orderAmount = newAmount > 0 ? newAmount.toFixed(2) : \"0.01\"\r\n          }\r\n        }, 500)\r\n      }\r\n    },\r\n    \r\n    // 卡券弹窗点击\r\n    cardPopupClick(isOpen) {\r\n      if (isOpen) {\r\n        this.$refs.cardPopup.open()\r\n      } else {\r\n        if (this.cardId != this.cardSelectId) {\r\n          for (let item of this.cardList) {\r\n            if (item.id == this.cardSelectId) {\r\n              let newAmount = parseFloat(this.reserveOrderAmount) - parseFloat(item.card.price)\r\n              this.orderAmount = newAmount > 0 ? newAmount.toFixed(2) : \"0.01\"\r\n              this.cardId = item.id\r\n              this.cardAmount = item.card.price\r\n              break\r\n            }\r\n          }\r\n        }\r\n        this.$refs.cardPopup.close()\r\n      }\r\n    },\r\n    \r\n    // 页面跳转\r\n    navigateToFun(e) {\r\n      uni.navigateTo({\r\n        url: \"/pages/\" + e.currentTarget.dataset.url\r\n      })\r\n    },\r\n    \r\n    // 返回上一页\r\n    navBack() {\r\n      if (getCurrentPages().length > 1) {\r\n        uni.navigateBack()\r\n      } else {\r\n        uni.switchTab({\r\n          url: \"/pages/tabbar/shop\"\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 提示弹窗\r\n    opTipsPopup(msg, isBack = false) {\r\n      let that = this\r\n      that.tipsTitle = msg\r\n      that.$refs.tipsPopup.open()\r\n      \r\n      setTimeout(function() {\r\n        that.$refs.tipsPopup.close()\r\n        \r\n        if (isBack) {\r\n          that.navBack()\r\n        }\r\n      }, 2000)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\npage { background: #f8f8f8; }\r\n.container { padding-bottom: 240rpx; }\r\n.w100p30 {\r\n  z-index: 1;\r\n  margin: 20rpx 30rpx;\r\n  width: calc(100% - 100rpx);\r\n  padding: 30rpx 20rpx;\r\n  border-radius: 30rpx;\r\n  background: #fff;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n.title {\r\n  width: 100%;\r\n  color: #000;\r\n  font-size: 26rpx;\r\n  font-weight: 700;\r\n}\r\n.w100p30 .map-bg {\r\n  position: absolute;\r\n  z-index: -2;\r\n  right: 0;\r\n  bottom: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.w100p30 .mask-bg {\r\n  position: absolute;\r\n  z-index: -1;\r\n  right: -2rpx;\r\n  bottom: -2rpx;\r\n  width: calc(100% - 24rpx);\r\n  height: calc(100% - 24rpx);\r\n  border: 14rpx solid #fff;\r\n  border-radius: 30rpx;\r\n  background-image: linear-gradient(to right, #fff, rgba(255, 255, 255, .9), rgba(255, 255, 255, .6));\r\n}\r\n.w100p30 .adds-box {\r\n  z-index: 1;\r\n  width: 100%;\r\n  height: 100%;\r\n  justify-content: space-between;\r\n}\r\n.adds-box .adds-item {\r\n  color: #000;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n}\r\n.adds-box .adds-item .txt {\r\n  padding: 10rpx 0;\r\n  font-size: 32rpx;\r\n}\r\n.adds-box .adds-add image {\r\n  width: 28rpx;\r\n  height: 28rpx;\r\n  transform: rotate(-90deg);\r\n}\r\n.goods-item {\r\n  padding-top: 30rpx;\r\n  justify-content: space-between;\r\n  animation: fadeIn .45s ease;\r\n}\r\n.goods-item image {\r\n  width: 140rpx;\r\n  height: 140rpx;\r\n  border-radius: 8rpx;\r\n}\r\n.goods-item .goods-info {\r\n  width: calc(100% - 160rpx);\r\n  height: 140rpx;\r\n  font-weight: 700;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n.goods-item .goods-info .t1 {\r\n  color: #000;\r\n  font-size: 26rpx;\r\n}\r\n.goods-item .goods-info .t2 {\r\n  color: #999;\r\n  font-size: 24rpx;\r\n}\r\n.goods-item .goods-info .goods-info-bom {\r\n  margin-top: 25rpx;\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: flex-end;\r\n  justify-content: flex-end;\r\n}\r\n.goods-info .goods-info-bom .sum {\r\n  margin-right: 20rpx;\r\n  color: #999;\r\n  font-size: 22rpx;\r\n}\r\n.list-item {\r\n  padding: 15rpx 0;\r\n  justify-content: space-between;\r\n  color: #000;\r\n  font-size: 24rpx;\r\n}\r\n.list-item .list-right .zs {\r\n  color: #999;\r\n  margin-right: 10rpx;\r\n}\r\n.list-item .list-right .icon {\r\n  width: 22rpx;\r\n  height: 22rpx;\r\n  transform: rotate(-90deg);\r\n}\r\n.list-item textarea {\r\n  width: calc(100% - 40rpx);\r\n  padding: 20rpx;\r\n  border-radius: 8rpx;\r\n  background: #f8f8f8;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n  min-height: 120rpx;\r\n}\r\n.list-item .list-item-xz {\r\n  margin-top: 15rpx;\r\n  color: #999;\r\n  font-weight: 300;\r\n  font-size: 24rpx;\r\n}\r\n.list-item .list-item-xz text {\r\n  margin-left: 6rpx;\r\n  color: #000;\r\n  font-weight: 500;\r\n  text-decoration: underline;\r\n}\r\n.footer {\r\n  position: fixed;\r\n  z-index: 99;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: calc(100% - 80rpx);\r\n  padding: 30rpx 40rpx;\r\n  flex-direction: column;\r\n  background-color: #fff;\r\n  padding-bottom: max(env(safe-area-inset-bottom), 20rpx);\r\n  border-top: 2rpx solid #f5f5f5;\r\n}\r\n.footer .btn {\r\n  width: 100%;\r\n  height: 100rpx;\r\n  justify-content: center;\r\n  background: #000;\r\n  border-radius: 50rpx;\r\n}\r\n.footer .btn image {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n}\r\n.footer .btn text {\r\n  margin-left: 12rpx;\r\n  color: #fff;\r\n  font-size: 26rpx;\r\n  font-weight: bolder;\r\n}\r\n.popup-box {\r\n  width: calc(100% - 40rpx);\r\n  padding: 20rpx;\r\n  background: #f8f8f8;\r\n  border-radius: 30rpx 30rpx 0 0;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n.popup-box .popup-top {\r\n  width: calc(100% - 20rpx);\r\n  padding: 10rpx;\r\n  justify-content: space-between;\r\n}\r\n.popup-top .popup-title .t1 {\r\n  font-size: 38rpx;\r\n  font-weight: 700;\r\n}\r\n.popup-top .popup-title .t2 {\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  font-weight: 300;\r\n}\r\n.popup-top .popup-close {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 50%;\r\n  background: #eee;\r\n  justify-content: center;\r\n  transform: rotate(45deg);\r\n}\r\n.popup-box .popup-scroll {\r\n  padding-top: 20rpx;\r\n  width: 100%;\r\n  max-height: 50vh;\r\n  overflow-y: scroll;\r\n}\r\n.popup-scroll .coupon {\r\n  margin: 0 6rpx 30rpx;\r\n  width: calc(100% - 20rpx);\r\n  background: #fff;\r\n  border-width: 4rpx;\r\n  border-style: solid;\r\n  border-radius: 8rpx;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n.coupon .coupon-bg {\r\n  position: absolute;\r\n  z-index: 1;\r\n  right: -90rpx;\r\n  bottom: -120rpx;\r\n  width: 380rpx;\r\n  height: 380rpx;\r\n}\r\n.coupon .coupon-sub {\r\n  position: absolute;\r\n  z-index: 9;\r\n  top: 0;\r\n  left: 0;\r\n  border-radius: 8rpx 0;\r\n  padding: 0rpx 12rpx;\r\n  height: 36rpx;\r\n  line-height: 36rpx;\r\n  text-align: center;\r\n  font-size: 20rpx;\r\n  color: #fa5150;\r\n  background: rgba(250, 81, 80, .125);\r\n}\r\n.coupon .coupon-item {\r\n  z-index: 2;\r\n  width: calc(100% - 80rpx);\r\n  padding: 50rpx 40rpx 20rpx;\r\n  border-bottom: 2rpx dashed #f8f8f8;\r\n  position: relative;\r\n}\r\n.coupon-item .coupon-price {\r\n  width: calc(100% - 200rpx);\r\n  color: #000;\r\n  font-size: 48rpx;\r\n  font-weight: 700;\r\n}\r\n.coupon-item .coupon-intro {\r\n  width: calc(100% - 200rpx);\r\n  margin: 8rpx 0;\r\n  color: #444;\r\n  font-size: 24rpx;\r\n}\r\n.coupon .coupon-validity {\r\n  width: calc(100% - 80rpx);\r\n  padding: 20rpx 40rpx;\r\n  color: #999;\r\n  font-size: 20rpx;\r\n}\r\n.coupon .coupon-err {\r\n  position: absolute;\r\n  z-index: 10;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  font-size: 28rpx;\r\n  font-style: italic;\r\n  font-weight: 700;\r\n  justify-content: center;\r\n  color: #ccc;\r\n  background: rgba(255, 255, 255, .85);\r\n}\r\n.popup-box .popup-btn {\r\n  margin: 40rpx 10rpx;\r\n  width: calc(100% - 20rpx);\r\n  height: 100rpx;\r\n  line-height: 100rpx;\r\n  text-align: center;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n  color: #fff;\r\n  background: #000;\r\n  border-radius: 100rpx;\r\n}\r\n.empty-box {\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 60rpx 0;\r\n}\r\n.empty-box image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n.empty-box .e1 {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 10rpx;\r\n}\r\n.empty-box .e2 {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n.tips-box {\r\n  padding: 20rpx 30rpx;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  border-radius: 12rpx;\r\n  justify-content: center;\r\n}\r\n.tips-box .tips-item {\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  font-weight: 700;\r\n}\r\n.df {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.ohto {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/uniapp/vue3/pages/order/settlement.vue'\nwx.createPage(MiniProgramPage)"], "names": ["api.api", "request", "uni"], "mappings": ";;;;;AA6KA,eAAe,MAAW;AAC1B,MAAO,QAAO,MAAW;AACzB,iBAAiB,MAAW;AAE5B,MAAM,MAAM,OAAO;AAEnB,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;;AACL,WAAO;AAAA,MACL,iBAAiB,KAAK,OAAO,MAAM,mBAAmB;AAAA,MACtD,gBAAgB,KAAK,OAAO,MAAM,kBAAkB;AAAA,MACpD,WAAS,SAAI,eAAJ,mBAAgB,WAAU;AAAA,MACnC,SAAO,SAAI,eAAJ,mBAAgB,UAAS,CAAC,IAAI;AAAA,MACrC,MAAM;AAAA,MACN,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,QACR,IAAI;AAAA,MACL;AAAA,MACD,WAAW,CAAE;AAAA,MACb,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU,CAAE;AAAA,MACZ,QAAQ;AAAA,MACR,WAAW;AAAA;AAAA,MAGX,eAAe;AAAA,QACb;AAAA,UACE,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,SAAS;AAAA,YACP,KAAK;AAAA,YACL,MAAM;AAAA,YACN,OAAO;AAAA,YACP,YAAY;AAAA,UACd;AAAA,QACD;AAAA,QACD;AAAA,UACE,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,SAAS;AAAA,YACP,KAAK;AAAA,YACL,MAAM;AAAA,YACN,OAAO;AAAA,YACP,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACD;AAAA,MAED,aAAa;AAAA,QACX,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,MACX;AAAA,MAED,cAAc;AAAA,QACZ;AAAA,UACE,IAAI;AAAA,UACJ,WAAW;AAAA,UACX,MAAM;AAAA,YACJ,WAAW;AAAA,YACX,OAAO;AAAA,YACP,OAAO;AAAA,YACP,kBAAkB;AAAA,YAClB,aAAa;AAAA,UACf;AAAA,QACD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,WAAW;AAAA,UACX,MAAM;AAAA,YACJ,WAAW;AAAA,YACX,OAAO;AAAA,YACP,OAAO;AAAA,YACP,kBAAkB;AAAA,YAClB,aAAa;AAAA,UACf;AAAA,QACD;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,WAAW;AAAA,UACX,MAAM;AAAA,YACJ,WAAW;AAAA,YACX,OAAO;AAAA,YACP,OAAO;AAAA,YACP,kBAAkB;AAAA,YAClB,aAAa;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AACd,QAAI,QAAQ,QAAQ,QAAQ,QAAQ,GAAG;AACrC,WAAK,OAAO,QAAQ;AACpB,WAAK,YAAY,QAAQ;AACzB,WAAK,WAAW,QAAQ;AAAA,IAC1B;AAEA,SAAK,YAAY,CAAC;AAClB,SAAK,gBAAgB;AAAA,EACtB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,kBAAkB;AAChB,UAAI,OAAO;AAGX,UAAIA,WAAQ,OAAGA,WAAO,IAAC,oBAAoB;AACzCC,sBAAO,QAACD,WAAO,IAAC,oBAAoB;AAAA,UAClC,MAAM,KAAK;AAAA,UACX,KAAK,KAAK;AAAA,UACV,UAAU,KAAK;AAAA,QACjB,CAAC,EAAE,KAAK,SAAS,KAAK;AACpB,cAAI,IAAI,QAAQ,KAAK;AACnB,iBAAK,YAAY,IAAI;AACrB,iBAAK,UAAU;AAAA,iBACV;AACL,iBAAK,YAAY,IAAI,KAAK,IAAI;AAAA,UAChC;AAAA,SACD;AAAA,aACI;AAEL,mBAAW,MAAM;AACf,eAAK,YAAY,KAAK;AACtB,eAAK,UAAU;AAAA,QAChB,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,OAAO;AAEX,UAAI,KAAK,eAAe,GAAG;AACzB,aAAK,YAAY,eAAe;AAChC;AAAA,MACF;AAEA,UAAI,KAAK,SAAS,MAAM,GAAG;AACzB,aAAK,YAAY,YAAY;AAC7B;AAAA,MACF;AAEAE,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAGD,UAAIF,WAAM,OAAKA,WAAO,IAAC,kBAAkB;AACvCC,sBAAO,QAACD,WAAO,IAAC,kBAAkB;AAAA,UAChC,MAAM,KAAK;AAAA,UACX,KAAK,KAAK;AAAA,UACV,UAAU,KAAK;AAAA,UACf,KAAK,KAAK,SAAS;AAAA,UACnB,KAAK,KAAK;AAAA,UACV,QAAQ,KAAK;AAAA,QACd,GAAE,MAAM,EAAE,KAAK,SAAS,KAAK;AAC5BE,wBAAAA,MAAI,YAAY;AAChB,cAAI,WAAW,eAAe;AAE9B,cAAI,IAAI,QAAQ,KAAK;AACnB,gBAAI,UAAU,IAAI;AAClBA,0BAAAA,MAAI,eAAe;AAAA,cACjB,UAAU;AAAA,cACV,WAAW,QAAQ;AAAA,cACnB,UAAU,QAAQ;AAAA,cAClB,SAAS,QAAQ;AAAA,cACjB,UAAU,QAAQ;AAAA,cAClB,SAAS,QAAQ;AAAA,cACjB,SAAS,WAAW;AAClB,qBAAK,YAAY,mBAAmB;AACpC,2BAAW,WAAW;AACpBA,gCAAAA,MAAI,WAAW;AAAA,oBACb,KAAK;AAAA,mBACN;AAAA,gBACF,GAAE,GAAI;AAAA,cACR;AAAA,cACD,MAAM,WAAW;AACf,qBAAK,YAAY,OAAO;AACxB,2BAAW,WAAW;AACpBA,gCAAAA,MAAI,WAAW;AAAA,oBACb,KAAK;AAAA,mBACN;AAAA,gBACF,GAAE,GAAI;AAAA,cACT;AAAA,aACD;AAAA,iBACI;AACL,iBAAK,YAAY,IAAI,GAAG;AAAA,UAC1B;AAAA,SACD;AAAA,aACI;AAEL,mBAAW,MAAM;AACfA,wBAAAA,MAAI,YAAY;AAChB,cAAI,WAAW,eAAe;AAE9BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,SAAS,SAAS,KAAK;AACrB,kBAAI,IAAI,SAAS;AACf,qBAAK,YAAY,mBAAmB;AACpC,2BAAW,WAAW;AACpBA,gCAAAA,MAAI,WAAW;AAAA,oBACb,KAAK;AAAA,mBACN;AAAA,gBACF,GAAE,GAAI;AAAA,qBACF;AACL,qBAAK,YAAY,OAAO;AACxB,2BAAW,WAAW;AACpBA,gCAAAA,MAAI,WAAW;AAAA,oBACb,KAAK;AAAA,mBACN;AAAA,gBACF,GAAE,GAAI;AAAA,cACT;AAAA,YACF;AAAA,WACD;AAAA,QACF,GAAE,GAAI;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,IAAI;AACd,UAAI,OAAO;AAEXA,oBAAAA,MAAI,YAAY;AAAA,QACd,MAAM;AAAA,OACP;AAGD,UAAIF,WAAQ,OAAGA,WAAO,IAAC,oBAAoB;AACzCC,sBAAO,QAACD,WAAO,IAAC,oBAAoB;AAAA,UAClC;AAAA,QACF,CAAC,EAAE,KAAK,SAAS,KAAK;AACpBE,wBAAAA,MAAI,YAAY;AAEhB,cAAI,IAAI,QAAQ,OAAO,IAAI,MAAM;AAC/B,iBAAK,WAAW,IAAI;AAAA,UACtB;AAAA,SACD;AAAA,aACI;AAEL,mBAAW,MAAM;AACfA,wBAAAA,MAAI,YAAY;AAChB,eAAK,WAAW,KAAK;AAAA,QACtB,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACV,UAAI,OAAO;AACX,UAAI,QAAQ;AACZ,UAAI,cAAc;AAClB,UAAI,iBAAiB;AACrB,UAAI,cAAc;AAElB,eAAS,QAAQ,KAAK,WAAW;AAC/B,uBAAe,WAAW,KAAK,QAAQ,aAAa,KAAK,QAAQ;AACjE,0BAAkB,YAAY,KAAK,QAAQ,aAAa,KAAK,QAAQ,SAAS,KAAK,QAAQ;AAC3F,uBAAe,WAAW,KAAK,QAAQ,QAAQ,KAAK,QAAQ;AAC5D,iBAAS,SAAS,KAAK,QAAQ;AAAA,MACjC;AAEA,WAAK,cAAc,YAAY,QAAQ,CAAC;AACxC,WAAK,iBAAiB,eAAe,QAAQ,CAAC;AAC9C,WAAK,cAAc,YAAY,QAAQ,CAAC;AACxC,WAAK,aAAa;AAElB,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ;AAAA,MACf;AAAA,IACD;AAAA;AAAA,IAGD,UAAU;AACR,UAAI,OAAO;AAGX,UAAIF,WAAQ,OAAGA,WAAO,IAAC,YAAY;AACjCC,sBAAO,QAACD,WAAO,IAAC,YAAY;AAAA,UAC1B,YAAY,KAAK;AAAA,UACjB,QAAQ,KAAK;AAAA,QACd,GAAE,MAAM,EAAE,KAAK,SAAS,KAAK;AAC5B,cAAI,IAAI,QAAQ,OAAO,IAAI,KAAK,CAAC,GAAG;AAClC,iBAAK,WAAW,IAAI,KAAK,CAAC;AAC1B,iBAAK,YAAY,IAAI,KAAK,CAAC;AAC3B,iBAAK,eAAe,IAAI,KAAK,CAAC,EAAE;AAChC,iBAAK,SAAS,IAAI,KAAK,CAAC,EAAE;AAC1B,iBAAK,aAAa,IAAI,KAAK,CAAC,EAAE,KAAK;AACnC,iBAAK,qBAAqB,KAAK;AAE/B,gBAAI,YAAY,KAAK,qBAAqB,IAAI,KAAK,CAAC,EAAE,KAAK;AAC3D,iBAAK,cAAc,UAAU,QAAQ,CAAC;AAAA,UACxC;AAAA,SACD;AAAA,aACI;AAEL,mBAAW,MAAM;AACf,eAAK,WAAW,KAAK;AACrB,eAAK,YAAY,KAAK,aAAa;AAGnC,cAAI,gBAAgB,KAAK,aAAa,KAAK,UAAQ,KAAK,cAAc,IAAI;AAC1E,cAAI,eAAe;AACjB,iBAAK,eAAe,cAAc;AAClC,iBAAK,SAAS,cAAc;AAC5B,iBAAK,aAAa,cAAc,KAAK;AACrC,iBAAK,qBAAqB,KAAK;AAE/B,gBAAI,YAAY,WAAW,KAAK,kBAAkB,IAAI,WAAW,cAAc,KAAK,KAAK;AACzF,iBAAK,cAAc,YAAY,IAAI,UAAU,QAAQ,CAAC,IAAI;AAAA,UAC5D;AAAA,QACD,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,QAAQ;AACrB,UAAI,QAAQ;AACV,aAAK,MAAM,UAAU,KAAK;AAAA,aACrB;AACL,YAAI,KAAK,UAAU,KAAK,cAAc;AACpC,mBAAS,QAAQ,KAAK,UAAU;AAC9B,gBAAI,KAAK,MAAM,KAAK,cAAc;AAChC,kBAAI,YAAY,WAAW,KAAK,kBAAkB,IAAI,WAAW,KAAK,KAAK,KAAK;AAChF,mBAAK,cAAc,YAAY,IAAI,UAAU,QAAQ,CAAC,IAAI;AAC1D,mBAAK,SAAS,KAAK;AACnB,mBAAK,aAAa,KAAK,KAAK;AAC5B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,aAAK,MAAM,UAAU,MAAM;AAAA,MAC7B;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,GAAG;AACfE,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,YAAY,EAAE,cAAc,QAAQ;AAAA,OAC1C;AAAA,IACF;AAAA;AAAA,IAGD,UAAU;AACR,UAAI,gBAAe,EAAG,SAAS,GAAG;AAChCA,sBAAAA,MAAI,aAAa;AAAA,aACZ;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,SACN;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,KAAK,SAAS,OAAO;AAC/B,UAAI,OAAO;AACX,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU,KAAK;AAE1B,iBAAW,WAAW;AACpB,aAAK,MAAM,UAAU,MAAM;AAE3B,YAAI,QAAQ;AACV,eAAK,QAAQ;AAAA,QACf;AAAA,MACD,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnjBA,GAAG,WAAW,eAAe;"}