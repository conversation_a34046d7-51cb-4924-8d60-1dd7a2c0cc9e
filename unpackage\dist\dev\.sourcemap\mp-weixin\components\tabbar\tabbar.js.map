{"version": 3, "file": "tabbar.js", "sources": ["components/tabbar/tabbar.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy90YWJiYXIvdGFiYmFyLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"tabbar df\">\n    <!-- 添加内容弹出层 -->\n    <view v-if=\"isAdd\" class=\"tabbar-add df bfh\" :class=\"{'fade-in': !isAddBg, 'fade-out': isAddBg}\" @touchmove.stop.prevent @tap=\"handleAdd\">\n      <view class=\"content-wrapper\">\n        <!-- 头部标题区域 -->\n        <view class=\"add-header\">\n          <view class=\"header-content\">\n            <view class=\"add-title\">发布内容 <text class=\"add-plus\">+</text></view>\n            <view class=\"add-subtitle\">{{currentAppXx}}</view>\n          </view>\n          <view class=\"header-image\">\n            <image src=\"/static/img/fbt.png\" mode=\"aspectFit\"></image>\n          </view>\n        </view>\n        \n        <!-- 发布选项区域 - 1、2、1布局 -->\n        <view class=\"card-container\" @tap.stop @touchmove.stop.prevent>\n          <!-- 图文卡片 -->\n          <view\n            :class=\"['card', 'cream-card', { 'active-state': activeStates.card }]\"\n            @tap.stop=\"toAdd(1)\"\n            @touchstart=\"handleTouchStart('card')\"\n            @touchend=\"handleTouchEnd('card')\">\n            <view class=\"card-left\">\n              <image src=\"/static/img/tw.png\"></image>\n            </view>\n            <view class=\"card-content\">\n              <view class=\"card-title\">发布图文</view>\n              <view class=\"card-subtitle\">分享精彩瞬间</view>\n            </view>\n            <view class=\"card-right\">\n              <image src=\"/static/img/z1.png\"></image>\n            </view>\n          </view>\n          \n          <!-- 视频和音频选项 -->\n          <view class=\"two-column-container\">\n            <view\n              :class=\"['two-column-card', 'video-card', { 'active-state': activeStates.twoColumnCard }]\"\n              @tap.stop=\"toAdd(2)\"\n              @touchstart=\"handleTouchStart('twoColumnCard')\"\n              @touchend=\"handleTouchEnd('twoColumnCard')\">\n              <view class=\"card-content-left\">\n                <view class=\"two-column-card-title\">发视频</view>\n                <view class=\"two-column-card-desc\">记录精彩瞬间</view>\n              </view>\n              <view class=\"card-content-right\">\n                <image src=\"/static/img/sp.png\"></image>\n              </view>\n            </view>\n            <view\n              :class=\"['two-column-card', 'audio-card', { 'active-state': activeStates.twoColumnCard }]\"\n              @tap.stop=\"toAdd(3)\"\n              @touchstart=\"handleTouchStart('twoColumnCard')\"\n              @touchend=\"handleTouchEnd('twoColumnCard')\">\n              <view class=\"card-content-left\">\n                <view class=\"two-column-card-title\">发音频</view>\n                <view class=\"two-column-card-desc\">分享好声音</view>\n              </view>\n              <view class=\"card-content-right\">\n                <image src=\"/static/img/yw.png\"></image>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 圈子卡片 -->\n          <view class=\"card mint-card\" @tap.stop=\"toAdd(5)\">\n            <view class=\"card-left\">\n              <image src=\"/static/img/qz.png\"></image>\n            </view>\n            <view class=\"card-content\">\n              <view class=\"card-title\">创建圈子</view>\n              <view class=\"card-subtitle\">一起交流讨论</view>\n            </view>\n            <view class=\"card-right\">\n              <image src=\"/static/img/z1.png\"></image>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部导航栏 -->\n    <view class=\"tabbar-box df\" :class=\"{'bfw tb-bs': !isAddBtn}\">\n      <!-- 首页按钮 -->\n      <view\n        :class=\"['tabbar-item', 'df', { 'active-state': activeStates.tabbarItem }]\"\n        @tap=\"toTabbar(0)\"\n        @touchstart=\"handleTouchStart('tabbarItem')\"\n        @touchend=\"handleTouchEnd('tabbarItem')\"\n        v-if=\"!isAddBtn\">\n        <image class=\"icon\" :src=\"0 === currentPage ? tabBar[0].selectedIconPath : tabBar[0].iconPath\"></image>\n      </view>\n\n      <!-- 购物车按钮 -->\n      <view\n        :class=\"['tabbar-item', 'df', { 'active-state': activeStates.tabbarItem }]\"\n        @tap=\"toTabbar(1)\"\n        @touchstart=\"handleTouchStart('tabbarItem')\"\n        @touchend=\"handleTouchEnd('tabbarItem')\"\n        v-if=\"!isAddBtn\">\n        <image class=\"icon\" :src=\"1 === currentPage ? tabBar[1].selectedIconPath : tabBar[1].iconPath\"></image>\n        <view v-if=\"showCartBadge && cartBadgeCount > 0\" class=\"msg\" :style=\"{'padding': cartBadgeCount > 9 ? '0 10rpx' : '0'}\">\n          {{cartBadgeCount > 99 ? '99+' : cartBadgeCount}}\n        </view>\n      </view>\n\n      <!-- 中间添加按钮 -->\n      <view\n        :class=\"['tabbar-item', 'df', { 'active-state': activeStates.tabbarItem }]\"\n        @tap=\"toTabbar(2)\"\n        @touchstart=\"handleTouchStart('tabbarItem')\"\n        @touchend=\"handleTouchEnd('tabbarItem')\">\n        <view class=\"add df\" :style=\"{\n          'background': isAddBtn ? '#fff' : '#000',\n          'transform': isAddBtn ? 'rotate(225deg)' : 'rotate(0deg)'\n        }\">\n          <image :src=\"isAddBtn ? tabBar[2].selectedIconPath : tabBar[2].iconPath\"></image>\n        </view>\n      </view>\n\n      <!-- 消息按钮 -->\n      <view class=\"tabbar-item df\" @tap=\"toTabbar(3)\" v-if=\"!isAddBtn\">\n        <image class=\"icon\" :src=\"3 === currentPage ? tabBar[3].selectedIconPath : tabBar[3].iconPath\"></image>\n        <view v-if=\"currentMsg > 0\" class=\"msg\">\n          {{currentMsg > 99 ? '99+' : currentMsg}}\n        </view>\n      </view>\n\n      <!-- 个人中心按钮 -->\n      <view class=\"tabbar-item df\" @tap=\"toTabbar(4)\" v-if=\"!isAddBtn\">\n        <image class=\"icon\" :src=\"4 === currentPage ? tabBar[4].selectedIconPath : tabBar[4].iconPath\"></image>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted, onBeforeUnmount, defineProps, defineEmits } from 'vue'\n\n// 定义 props\nconst props = defineProps({\n  currentPage: {\n    type: Number,\n    default: 0\n  },\n  currentMsg: {\n    type: Number,\n    default: 0\n  }\n})\n\n// 定义 emits\nconst emit = defineEmits(['refresh'])\n\n// 响应式数据\nconst appXx = ref({3: \"下面选择您要发布的类型\"})\nconst isAdd = ref(false)\nconst isAddBtn = ref(false)\nconst isAddBg = ref(false)\nconst isLoggedIn = ref(false)\nconst loginCheckTimer = ref(null)\nconst cartBadgeCount = ref(0)\nconst showCartBadge = ref(false)\n\n// 防止重复点击\nconst lastClickTime = ref(0)\n\n// 点击状态管理（小程序兼容性）\nconst activeStates = reactive({\n  tabbarItem: false,\n  card: false,\n  twoColumnCard: false\n})\n\n// 静态配置数据\nconst tabBar = reactive([\n  {\n    selectedIconPath: \"/static/img/tabbar/1.png\",\n    iconPath: \"/static/img/tabbar/11.png\",\n    pagePath: \"/pages/index/index\"\n  },\n  {\n    selectedIconPath: \"/static/img/tabbar/2.png\",\n    iconPath: \"/static/img/tabbar/22.png\",\n    pagePath: \"/pages/index/dynamic\"\n  },\n  {\n    selectedIconPath: \"/static/img/tabbar/3.png\",\n    iconPath: \"/static/img/tabbar/33.png\"\n  },\n  {\n    selectedIconPath: \"/static/img/tabbar/4.png\",\n    iconPath: \"/static/img/tabbar/44.png\",\n    pagePath: \"/pages/index/message\"\n  },\n  {\n    selectedIconPath: \"/static/img/tabbar/5.png\",\n    iconPath: \"/static/img/tabbar/55.png\",\n    pagePath: \"/pages/index/center\"\n  }\n])\n\n// 计算属性\nconst currentAppXx = computed(() => {\n  return appXx.value[3] || \"下面选择您要发布的类型\"\n})\n\n// 安全获取全局数据的方法\nconst getGlobalData = (key, defaultValue) => {\n  try {\n    if (typeof getApp !== 'function') return defaultValue\n    const app = getApp()\n    if (!app || !app.globalData) return defaultValue\n    return app.globalData[key] !== undefined ? app.globalData[key] : defaultValue\n  } catch (e) {\n    console.warn('获取全局数据失败:', e)\n    return defaultValue\n  }\n}\n\n// 方法定义\nconst handleAdd = () => {\n  setBarColor(\"#000000\")\n  isAddBg.value = true\n  isAddBtn.value = false\n\n  setTimeout(() => {\n    isAdd.value = false\n    isAddBg.value = false\n  }, 300)\n}\n\n// 点击状态处理函数（小程序兼容性）\nconst handleTouchStart = (type) => {\n  activeStates[type] = true\n}\n\nconst handleTouchEnd = (type) => {\n  setTimeout(() => {\n    activeStates[type] = false\n  }, 150) // 150ms后恢复状态\n}\n\n// 导航处理函数\nconst navigateToPage = (targetUrl) => {\n  return new Promise((resolve, reject) => {\n    uni.reLaunch({\n      url: targetUrl,\n      success: resolve,\n      fail: (err) => {\n        // 降级处理\n        uni.redirectTo({\n          url: targetUrl,\n          success: resolve,\n          fail: (redirectErr) => {\n            uni.navigateTo({\n              url: targetUrl,\n              success: resolve,\n              fail: reject\n            })\n          }\n        })\n      }\n    })\n  })\n}\n\nconst toTabbar = async (index) => {\n  // 防止重复点击 - 减少延迟到100ms，提升响应速度\n  const now = Date.now()\n  if (now - lastClickTime.value < 100) return\n  lastClickTime.value = now\n\n  // 更新 appXx 配置\n  appXx.value = getGlobalData('appXx', {3: \"下面选择您要发布的类型\"})\n\n  if (index != props.currentPage && index != 2) {\n    // 自定义 tabbar 页面切换\n    const targetUrl = tabBar[index].pagePath\n\n    try {\n      await navigateToPage(targetUrl)\n      console.log('页面切换成功:', targetUrl)\n    } catch (err) {\n      console.error('页面跳转失败:', err)\n      uni.showToast({\n        title: '页面跳转失败，请重试',\n        icon: 'none',\n        duration: 2000\n      })\n    }\n  } else if (index == 2 && isAdd.value) {\n    // 如果点击中间按钮且已展开，则收起\n    handleAdd()\n  } else if (index == 2 && !isAdd.value) {\n    // 如果点击中间按钮且未展开，则展开\n    isAdd.value = true\n    isAddBtn.value = true\n    setBarColor(\"#ffffff\")\n  } else if (index == props.currentPage && (index == 0 || index == 1)) {\n    // 如果在首页或动态页点击当前页，则刷新\n    console.log('点击当前页面，触发刷新')\n    emit('refresh')\n  }\n}\n\nconst toAdd = (type) => {\n  console.log('toAdd 被调用，类型:', type)\n  setBarColor(\"#000000\")\n\n  if (type === 5) {\n    // 创建圈子\n    console.log('准备跳转到圈子创建页面')\n    uni.navigateTo({\n      url: \"/pages/center/circle-create\",\n      success: () => {\n        console.log('圈子创建页面跳转成功')\n        isAdd.value = false\n        isAddBtn.value = false\n      },\n      fail: (err) => {\n        console.error('navigateTo失败:', err)\n        // 如果页面不存在，给用户提示\n        uni.showToast({\n          title: '功能开发中',\n          icon: 'none'\n        })\n        isAdd.value = false\n        isAddBtn.value = false\n      }\n    })\n  } else {\n    // 发布动态\n    console.log('准备跳转到笔记发布页面，类型:', type)\n    uni.navigateTo({\n      url: \"/pages/note/add?type=\" + type,\n      success: () => {\n        console.log('笔记发布页面跳转成功')\n        isAdd.value = false\n        isAddBtn.value = false\n      },\n      fail: (err) => {\n        console.error('跳转到笔记发布页面失败:', err)\n        uni.showToast({\n          title: '页面跳转失败',\n          icon: 'none'\n        })\n        isAdd.value = false\n        isAddBtn.value = false\n      }\n    })\n  }\n}\n\nconst setBarColor = (color) => {\n  // #ifdef MP-WEIXIN\n  uni.setNavigationBarColor({\n    frontColor: color,\n    backgroundColor: \"#ffffff\",\n    animation: {\n      duration: 300,\n      timingFunc: \"easeIn\"\n    }\n  })\n  // #endif\n}\n\n// 检查全局登录状态\nconst checkGlobalLoginStatus = () => {\n  try {\n    // 检查token和用户信息\n    const token = uni.getStorageSync('token')\n    const userInfo = uni.getStorageSync('userInfo')\n\n    // 确定当前登录状态\n    const currentLoginStatus = !!(token && userInfo && userInfo.uid)\n\n    // 如果状态发生变化，广播事件\n    if (isLoggedIn.value !== currentLoginStatus) {\n      isLoggedIn.value = currentLoginStatus\n      // 广播登录状态变化事件\n      if (uni.$emit) {\n        uni.$emit('loginStateChanged', currentLoginStatus)\n      }\n    }\n  } catch (error) {\n    console.warn('检查登录状态失败:', error)\n  }\n}\n\n// 处理登录状态变化\nconst handleLoginStateChanged = (loginStatus) => {\n  isLoggedIn.value = !!loginStatus\n}\n\n// 处理购物车数量更新\nconst handleCartBadgeUpdate = (data) => {\n  if (data && typeof data === 'object') {\n    cartBadgeCount.value = data.count || 0\n    showCartBadge.value = data.show || false\n  }\n}\n\n// 生命周期钩子\nonMounted(() => {\n  // 检查全局登录状态\n  checkGlobalLoginStatus()\n\n  // 监听登录状态变化事件\n  if (uni.$on) {\n    uni.$on('loginStateChanged', handleLoginStateChanged)\n    uni.$on('updateCartBadge', handleCartBadgeUpdate)\n  }\n\n  // 定期检查登录状态 - 降低频率以提升性能\n  loginCheckTimer.value = setInterval(() => {\n    checkGlobalLoginStatus()\n  }, 60000) // 每60秒检查一次，减少性能消耗\n})\n\nonBeforeUnmount(() => {\n  // 清除定时器\n  if (loginCheckTimer.value) {\n    clearInterval(loginCheckTimer.value)\n    loginCheckTimer.value = null\n  }\n\n  // 移除事件监听\n  if (uni.$off) {\n    uni.$off('loginStateChanged', handleLoginStateChanged)\n    uni.$off('updateCartBadge', handleCartBadgeUpdate)\n  }\n})\n</script>\n\n<style>\n/* APP端兼容性优化 - 移除CSS变量，使用具体值 */\n\n.tabbar{\n  position: fixed;\n  z-index: 998;\n  width: 100%;\n  /* APP端兼容性：条件编译底部安全区域 */\n  /* #ifdef APP-PLUS */\n  bottom: 50rpx;\n  /* #endif */\n  /* #ifndef APP-PLUS */\n  bottom: max(env(safe-area-inset-bottom), 50rpx);\n  /* #endif */\n  box-sizing: border-box;\n  justify-content: center;\n  pointer-events: none; /* 优化性能，只有子元素可点击 */\n}\n\n.tabbar-box{\n  z-index: 998;\n  width: calc(100% - 120rpx);\n  height: 100rpx;\n  border-radius: 50rpx;\n  justify-content: space-around;\n  /* APP端兼容性：条件编译backdrop-filter */\n  /* #ifndef APP-PLUS */\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  /* #endif */\n  pointer-events: auto; /* 恢复点击事件 */\n  transition: all 0.3s ease; /* 添加过渡动画 */\n}\n\n.tb-bs{\n  box-shadow: 0 0 24rpx rgba(0, 0, 0, 0.06);\n  border: 1px solid #f8f8f8;\n}\n\n.tabbar-box .tabbar-item{\n  width: 20%;\n  height: 100rpx;\n  justify-content: center;\n  position: relative;\n  transition: transform 0.2s ease; /* 添加点击反馈动画 */\n  cursor: pointer;\n}\n\n/* 小程序兼容性：使用class替代:active伪类 */\n.tabbar-item.active-state {\n  transform: scale(0.95); /* 点击时缩放效果 */\n}\n\n.tabbar-item .icon{\n  width: 48rpx;\n  height: 48rpx;\n  transition: opacity 0.2s ease; /* 图标切换动画 */\n}\n\n.tabbar-item .msg{\n  position: absolute;\n  top: 18rpx;\n  left: calc(50% + 8rpx);\n  min-width: 34rpx;\n  height: 34rpx;\n  line-height: 34rpx;\n  text-align: center;\n  font-size: 18rpx;\n  font-weight: 700;\n  color: #fff;\n  background: #fa5150;\n  border-radius: 34rpx;\n  border: 2rpx solid #fff;\n}\n\n.tabbar-item .add{\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 50%;\n  justify-content: center;\n  transition: all 0.3s ease-in-out;\n}\n\n.tabbar-item .add image{\n  width: 16rpx;\n  height: 16rpx;\n}\n\n.tabbar .tabbar-add{\n  position: fixed;\n  z-index: 997;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  align-items: center;\n  background-image: linear-gradient(to bottom, rgb(173 173 173 / 95%), rgb(25 25 25 / 95%));\n  /* APP端兼容性：条件编译backdrop-filter */\n  /* #ifndef APP-PLUS */\n  backdrop-filter: blur(20rpx);\n  -webkit-backdrop-filter: blur(20rpx);\n  /* #endif */\n}\n\n.content-wrapper {\n  width: 100%;\n  padding-top: 180rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n/* 头部标题区域 */\n.add-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 80%;\n  padding-top: 90rpx;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.header-image {\n  width: 300rpx;\n  height: 300rpx;\n}\n\n.header-image image {\n  width: 100%;\n  height: 100%;\n  border-radius: 12rpx;\n}\n\n.add-title {\n  font-size: 40rpx;\n  font-weight: bold;\n  color: #fff;\n  display: flex;\n  align-items: center;\n}\n\n.add-plus {\n  margin-left: 8rpx;\n  font-weight: normal;\n}\n\n.add-subtitle {\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.8);\n  margin-top: 8rpx;\n}\n\n/* 新增卡片样式 */\n.card-container {\n  width: 80%;\n  display: flex;\n  flex-direction: column;\n  gap: 30rpx;\n  margin-top: 240rpx;\n  pointer-events: auto; /* 确保容器可以传递点击事件 */\n}\n\n/* 卡片通用样式 */\n.card {\n  display: flex;\n  align-items: center;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  background-color: rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n  pointer-events: auto; /* 确保卡片可以被点击 */\n}\n\n/* 小程序兼容性：使用class替代:active伪类 */\n.card.active-state {\n  transform: scale(0.98);\n  background-color: rgba(255, 255, 255, 0.15);\n}\n\n.cream-card {\n  background-color: rgba(255, 255, 255, 0.15);\n}\n\n.mint-card {\n  background-color: rgba(255, 255, 255, 0.15);\n}\n\n.card-left {\n  width: 60rpx;\n  height: 60rpx;\n  background-color: rgba(255, 255, 255, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 20rpx;\n}\n\n.card-left image {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.card-content {\n  flex: 1;\n}\n\n.card-title {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #fff;\n}\n\n.card-subtitle {\n  font-size: 22rpx;\n  color: rgba(255, 255, 255, 0.8);\n  margin-top: 6rpx;\n}\n\n.card-right {\n  width: 36rpx;\n  height: 36rpx;\n  transform: rotate(180deg);\n}\n\n.card-right image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 两列卡片布局 */\n.two-column-container {\n  display: flex;\n  width: 100%;\n  gap: 20rpx;\n}\n\n.two-column-card {\n  flex: 1;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  pointer-events: auto; /* 确保两列卡片可以被点击 */\n}\n\n/* 小程序兼容性：使用class替代:active伪类 */\n.two-column-card.active-state {\n  transform: scale(0.98);\n}\n\n.video-card {\n  background-color: rgba(76, 130, 219, 0.3);\n}\n\n.audio-card {\n  background-color: rgba(245, 166, 35, 0.3);\n}\n\n.card-content-left {\n  flex: 1;\n}\n\n.two-column-card-title {\n  font-size: 26rpx;\n  font-weight: bold;\n  color: #FFFFFF;\n}\n\n.two-column-card-desc {\n  font-size: 20rpx;\n  color: rgba(255, 255, 255, 0.8);\n  margin-top: 6rpx;\n}\n\n.card-content-right {\n  width: 50rpx;\n  height: 50rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.card-content-right image {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.close-btn image {\n  width: 24rpx;\n  height: 24rpx;\n}\n\n.df{\n  display: flex;\n  align-items: center;\n}\n\n.bfh{\n  background: rgba(0, 0, 0, 0.8);\n}\n\n.bfw{\n  background: #fff;\n}\n\n/* 动画优化 - APP端兼容性 */\n.fade-in{\n  animation: fadeIn 0.3s forwards;\n}\n\n.fade-out{\n  animation: fadeOut 0.3s forwards;\n}\n\n@keyframes fadeIn{\n  from{\n    opacity: 0;\n    transform: translateY(10rpx);\n  }\n  to{\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeOut{\n  from{\n    opacity: 1;\n    transform: translateY(0);\n  }\n  to{\n    opacity: 0;\n    transform: translateY(10rpx);\n  }\n}\n</style>", "import Component from 'D:/uniapp/vue3/components/tabbar/tabbar.vue'\nwx.createComponent(Component)"], "names": ["ref", "reactive", "computed", "uni", "onMounted", "onBeforeUnmount", "Component"], "mappings": ";;;;;;;;;;;;;;;;;AA8IA,UAAM,QAAQ;AAYd,UAAM,OAAO;AAGb,UAAM,QAAQA,cAAG,IAAC,EAAC,GAAG,cAAa,CAAC;AACpC,UAAM,QAAQA,cAAG,IAAC,KAAK;AACvB,UAAM,WAAWA,cAAG,IAAC,KAAK;AAC1B,UAAM,UAAUA,cAAG,IAAC,KAAK;AACzB,UAAM,aAAaA,cAAG,IAAC,KAAK;AAC5B,UAAM,kBAAkBA,cAAG,IAAC,IAAI;AAChC,UAAM,iBAAiBA,cAAG,IAAC,CAAC;AAC5B,UAAM,gBAAgBA,cAAG,IAAC,KAAK;AAG/B,UAAM,gBAAgBA,cAAG,IAAC,CAAC;AAG3B,UAAM,eAAeC,cAAAA,SAAS;AAAA,MAC5B,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,eAAe;AAAA,IACjB,CAAC;AAGD,UAAM,SAASA,cAAAA,SAAS;AAAA,MACtB;AAAA,QACE,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,kBAAkB;AAAA,QAClB,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,UAAU;AAAA,MACX;AAAA,MACD;AAAA,QACE,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,UAAU;AAAA,MACX;AAAA,IACH,CAAC;AAGD,UAAM,eAAeC,cAAQ,SAAC,MAAM;AAClC,aAAO,MAAM,MAAM,CAAC,KAAK;AAAA,IAC3B,CAAC;AAGD,UAAM,gBAAgB,CAAC,KAAK,iBAAiB;AAC3C,UAAI;AACF,YAAI,OAAO,WAAW;AAAY,iBAAO;AACzC,cAAM,MAAM,OAAQ;AACpB,YAAI,CAAC,OAAO,CAAC,IAAI;AAAY,iBAAO;AACpC,eAAO,IAAI,WAAW,GAAG,MAAM,SAAY,IAAI,WAAW,GAAG,IAAI;AAAA,MAClE,SAAQ,GAAG;AACVC,sBAAAA,2DAAa,aAAa,CAAC;AAC3B,eAAO;AAAA,MACR;AAAA,IACH;AAGA,UAAM,YAAY,MAAM;AACtB,kBAAY,SAAS;AACrB,cAAQ,QAAQ;AAChB,eAAS,QAAQ;AAEjB,iBAAW,MAAM;AACf,cAAM,QAAQ;AACd,gBAAQ,QAAQ;AAAA,MACjB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,mBAAmB,CAAC,SAAS;AACjC,mBAAa,IAAI,IAAI;AAAA,IACvB;AAEA,UAAM,iBAAiB,CAAC,SAAS;AAC/B,iBAAW,MAAM;AACf,qBAAa,IAAI,IAAI;AAAA,MACtB,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,iBAAiB,CAAC,cAAc;AACpC,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,sBAAAA,MAAI,SAAS;AAAA,UACX,KAAK;AAAA,UACL,SAAS;AAAA,UACT,MAAM,CAAC,QAAQ;AAEbA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK;AAAA,cACL,SAAS;AAAA,cACT,MAAM,CAAC,gBAAgB;AACrBA,8BAAAA,MAAI,WAAW;AAAA,kBACb,KAAK;AAAA,kBACL,SAAS;AAAA,kBACT,MAAM;AAAA,gBACpB,CAAa;AAAA,cACF;AAAA,YACX,CAAS;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,WAAW,OAAO,UAAU;AAEhC,YAAM,MAAM,KAAK,IAAK;AACtB,UAAI,MAAM,cAAc,QAAQ;AAAK;AACrC,oBAAc,QAAQ;AAGtB,YAAM,QAAQ,cAAc,SAAS,EAAC,GAAG,cAAa,CAAC;AAEvD,UAAI,SAAS,MAAM,eAAe,SAAS,GAAG;AAE5C,cAAM,YAAY,OAAO,KAAK,EAAE;AAEhC,YAAI;AACF,gBAAM,eAAe,SAAS;AAC9BA,wBAAAA,MAAY,MAAA,OAAA,uCAAA,WAAW,SAAS;AAAA,QACjC,SAAQ,KAAK;AACZA,wBAAAA,MAAc,MAAA,SAAA,uCAAA,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAAA,QACF;AAAA,MACF,WAAU,SAAS,KAAK,MAAM,OAAO;AAEpC,kBAAW;AAAA,MACZ,WAAU,SAAS,KAAK,CAAC,MAAM,OAAO;AAErC,cAAM,QAAQ;AACd,iBAAS,QAAQ;AACjB,oBAAY,SAAS;AAAA,MACzB,WAAa,SAAS,MAAM,gBAAgB,SAAS,KAAK,SAAS,IAAI;AAEnEA,sBAAAA,MAAY,MAAA,OAAA,uCAAA,aAAa;AACzB,aAAK,SAAS;AAAA,MACf;AAAA,IACH;AAEA,UAAM,QAAQ,CAAC,SAAS;AACtBA,oBAAAA,0DAAY,iBAAiB,IAAI;AACjC,kBAAY,SAAS;AAErB,UAAI,SAAS,GAAG;AAEdA,sBAAAA,MAAY,MAAA,OAAA,uCAAA,aAAa;AACzBA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,UACL,SAAS,MAAM;AACbA,0BAAAA,0DAAY,YAAY;AACxB,kBAAM,QAAQ;AACd,qBAAS,QAAQ;AAAA,UAClB;AAAA,UACD,MAAM,CAAC,QAAQ;AACbA,0BAAAA,MAAA,MAAA,SAAA,uCAAc,iBAAiB,GAAG;AAElCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AACD,kBAAM,QAAQ;AACd,qBAAS,QAAQ;AAAA,UAClB;AAAA,QACP,CAAK;AAAA,MACL,OAAS;AAELA,sBAAAA,MAAY,MAAA,OAAA,uCAAA,mBAAmB,IAAI;AACnCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,0BAA0B;AAAA,UAC/B,SAAS,MAAM;AACbA,0BAAAA,0DAAY,YAAY;AACxB,kBAAM,QAAQ;AACd,qBAAS,QAAQ;AAAA,UAClB;AAAA,UACD,MAAM,CAAC,QAAQ;AACbA,0BAAAA,MAAc,MAAA,SAAA,uCAAA,gBAAgB,GAAG;AACjCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AACD,kBAAM,QAAQ;AACd,qBAAS,QAAQ;AAAA,UAClB;AAAA,QACP,CAAK;AAAA,MACF;AAAA,IACH;AAEA,UAAM,cAAc,CAAC,UAAU;AAE7BA,oBAAAA,MAAI,sBAAsB;AAAA,QACxB,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,WAAW;AAAA,UACT,UAAU;AAAA,UACV,YAAY;AAAA,QACb;AAAA,MACL,CAAG;AAAA,IAEH;AAGA,UAAM,yBAAyB,MAAM;AACnC,UAAI;AAEF,cAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,cAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAG9C,cAAM,qBAAqB,CAAC,EAAE,SAAS,YAAY,SAAS;AAG5D,YAAI,WAAW,UAAU,oBAAoB;AAC3C,qBAAW,QAAQ;AAEnB,cAAIA,cAAAA,MAAI,OAAO;AACbA,gCAAI,MAAM,qBAAqB,kBAAkB;AAAA,UAClD;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,QAAA,uCAAa,aAAa,KAAK;AAAA,MAChC;AAAA,IACH;AAGA,UAAM,0BAA0B,CAAC,gBAAgB;AAC/C,iBAAW,QAAQ,CAAC,CAAC;AAAA,IACvB;AAGA,UAAM,wBAAwB,CAAC,SAAS;AACtC,UAAI,QAAQ,OAAO,SAAS,UAAU;AACpC,uBAAe,QAAQ,KAAK,SAAS;AACrC,sBAAc,QAAQ,KAAK,QAAQ;AAAA,MACpC;AAAA,IACH;AAGAC,kBAAAA,UAAU,MAAM;AAEd,6BAAwB;AAGxB,UAAID,cAAAA,MAAI,KAAK;AACXA,4BAAI,IAAI,qBAAqB,uBAAuB;AACpDA,4BAAI,IAAI,mBAAmB,qBAAqB;AAAA,MACjD;AAGD,sBAAgB,QAAQ,YAAY,MAAM;AACxC,+BAAwB;AAAA,MACzB,GAAE,GAAK;AAAA,IACV,CAAC;AAEDE,kBAAAA,gBAAgB,MAAM;AAEpB,UAAI,gBAAgB,OAAO;AACzB,sBAAc,gBAAgB,KAAK;AACnC,wBAAgB,QAAQ;AAAA,MACzB;AAGD,UAAIF,cAAAA,MAAI,MAAM;AACZA,4BAAI,KAAK,qBAAqB,uBAAuB;AACrDA,4BAAI,KAAK,mBAAmB,qBAAqB;AAAA,MAClD;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjbD,GAAG,gBAAgBG,SAAS;"}