"use strict";
const sendVerifyCode = {
  data() {
    return {
      disabled: false,
      text: this.$t("验证码"),
      runTime: void 0,
      captchaType: "clickWord"
    };
  },
  methods: {
    sendCode() {
      if (this.disabled)
        return;
      this.disabled = true;
      let n = 60;
      this.text = this.$t("剩余") + n + "s";
      this.runTime = setInterval(() => {
        n = n - 1;
        if (n < 0) {
          clearInterval(this.runTime);
          this.disabled = false;
          this.text = this.$t("重新获取");
          return;
        }
        this.text = this.$t("剩余") + n + "s";
      }, 1e3);
    }
  },
  onHide() {
    clearInterval(this.runTime);
  }
};
exports.sendVerifyCode = sendVerifyCode;
//# sourceMappingURL=../../.sourcemap/mp-weixin/mixins/SendVerifyCode.js.map
