<view class="container"><view class="search-box df"><image src="{{a}}" class="search-icon"></image><input class="search-input" placeholder="输入昵称搜索成员" bindinput="{{b}}" bindconfirm="{{c}}" value="{{d}}"/><view wx:if="{{e}}" class="clear-btn" bindtap="{{g}}"><image src="{{f}}" class="clear-icon"></image></view></view><view wx:if="{{h}}" class="loading-container"><view class="loading-spinner"></view><text class="loading-text">加载中...</text></view><view wx:if="{{i}}" class="content-area"><view wx:if="{{j}}" class="empty-state"><image src="{{k}}" class="empty-icon"></image><text class="empty-text">{{l}}</text><text wx:if="{{m}}" class="empty-tip">试试其他关键词</text></view><view wx:if="{{n}}" class="section"><view class="section-title"><text>管理团队</text><text class="member-count">({{o}})</text></view><view class="member-list"><view wx:for="{{p}}" wx:for-item="user" wx:key="n" class="member-item df" bindtap="{{user.o}}"><image src="{{user.a}}" class="avatar" mode="aspectFill"></image><view class="info"><view class="name df"><text>{{user.b}}</text><view wx:if="{{user.c}}" class="role-tag owner">圈主</view><view wx:elif="{{user.d}}" class="role-tag admin">管理员</view><view wx:if="{{user.e}}" class="status-tag muted">已禁言</view></view><view class="meta df"><text wx:if="{{user.f}}" class="{{['gender', user.h]}}">{{user.g}}</text><text wx:if="{{user.i}}" class="age">{{user.j}}岁</text><text class="contrib">加入时间：{{user.k}}</text></view></view><view wx:if="{{user.l}}" class="action-buttons" catchtap="{{user.m}}"><view class="action-btn"><text>管理</text></view></view></view></view></view><view wx:if="{{q}}" class="section"><view class="section-title"><text>普通成员</text><text class="member-count">({{r}})</text></view><view class="member-list"><view wx:for="{{s}}" wx:for-item="user" wx:key="l" class="member-item df" bindtap="{{user.m}}"><image src="{{user.a}}" class="avatar" mode="aspectFill"></image><view class="info"><view class="name df"><text>{{user.b}}</text><view wx:if="{{user.c}}" class="status-tag muted">已禁言</view></view><view class="meta df"><text wx:if="{{user.d}}" class="{{['gender', user.f]}}">{{user.e}}</text><text wx:if="{{user.g}}" class="age">{{user.h}}岁</text><text class="contrib">加入时间：{{user.i}}</text></view></view><view wx:if="{{user.j}}" class="action-buttons" catchtap="{{user.k}}"><view class="action-btn"><text>管理</text></view></view></view></view></view></view></view>