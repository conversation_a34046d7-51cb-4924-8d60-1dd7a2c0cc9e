{"version": 3, "file": "means.js", "sources": ["pages/center/means.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2VudGVyL21lYW5zLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view>\n\t\t<form @submit=\"formSubmit\">\n\t<view class=\"container\">\n\t\t<!-- <navbar></navbar> -->\n\t\t\t\t<!-- <view class=\"title-box\" :style=\"{'margin-top': statusBarHeight + titleBarHeight + 'px'}\">\n\t\t\t<view>完善你的资料📸</view>\n\t\t\t<view>让大家更好地了解你</view>\n\t\t</view>\n\t\t\t\t -->\n\t\t\t\t<!-- 相册模块 -->\n\t\t\t\t<view class=\"album-section\" >\n\t\t\t\t\t<view class=\"album-title\">我的照片📸 ({{userInfo.avatar ? backgroundImages.length + 1 : backgroundImages.length}}/6)</view>\n\t\t\t\t\t<view class=\"album-desc\">封面头像需上传你的清晰无遮挡照片，否则无法为你推荐优质的朋友！\n\t\t\t\t\t\t<text class=\"change-avatar\" @tap=\"changeProfilePhoto\">更换头像</text>\n\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"photo-grid\" id=\"photoGrid\">\n\t\t\t\t\t\t<!-- 封面头像 -->\n\t\t\t\t\t\t<view class=\"photo-item cover-photo\" \n\t\t\t\t\t\t\t@tap=\"userInfo.avatar ? showPhotoMenu({type: '头像', url: userInfo.avatar}, -1) : changeProfilePhoto()\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<block v-if=\"userInfo.avatar\">\n\t\t\t\t\t\t\t\t<image class=\"photo-image\" :src=\"userInfo.avatar\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t\t<view class=\"photo-tag\">头像</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t<view class=\"photo-placeholder\">\n\t\t\t\t\t\t\t\t\t<image class=\"photo-icon\" src=\"/static/img/avatar.png\"></image>\n\t\t\t\t\t\t\t\t\t<view class=\"photo-type\">我的头像</view>\n\t\t\t\t\t\t\t\t\t<view class=\"photo-boost\">+10%</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 生活照 -->\n\t\t\t\t\t\t<view class=\"photo-item\" \n\t\t\t\t\t\t\t@tap=\"hasPhotoOfType('生活照') ? showPhotoMenu(getPhotoObject('生活照'), getPhotoIndex('生活照')) : addPhotoByType('生活照')\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<block v-if=\"hasPhotoOfType('生活照')\">\n\t\t\t\t\t\t\t\t<image class=\"photo-image\" :src=\"getPhotoByType('生活照')\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t<view class=\"photo-placeholder\">\n\t\t\t\t\t\t\t\t\t<image class=\"photo-icon\" src=\"/static/img/photo-life.png\"></image>\n\t\t\t\t\t\t\t\t\t<view class=\"photo-type\">有趣的生活照</view>\n\t\t\t\t\t\t\t\t\t<view class=\"photo-boost\">+5%</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 旅行照 -->\n\t\t\t\t\t\t<view class=\"photo-item\" \n\t\t\t\t\t\t\t@tap=\"hasPhotoOfType('旅行照') ? showPhotoMenu(getPhotoObject('旅行照'), getPhotoIndex('旅行照')) : addPhotoByType('旅行照')\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<block v-if=\"hasPhotoOfType('旅行照')\">\n\t\t\t\t\t\t\t\t<image class=\"photo-image\" :src=\"getPhotoByType('旅行照')\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t<view class=\"photo-placeholder\">\n\t\t\t\t\t\t\t\t\t<image class=\"photo-icon\" src=\"/static/img/photo-travel.png\"></image>\n\t\t\t\t\t\t\t\t\t<view class=\"photo-type\">好看的旅行照</view>\n\t\t\t\t\t\t\t\t\t<view class=\"photo-boost\">+5%</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 才艺照 -->\n\t\t\t\t\t\t<view class=\"photo-item\" \n\t\t\t\t\t\t\t@tap=\"hasPhotoOfType('才艺照') ? showPhotoMenu(getPhotoObject('才艺照'), getPhotoIndex('才艺照')) : addPhotoByType('才艺照')\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<block v-if=\"hasPhotoOfType('才艺照')\">\n\t\t\t\t\t\t\t\t<image class=\"photo-image\" :src=\"getPhotoByType('才艺照')\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t<view class=\"photo-placeholder\">\n\t\t\t\t\t\t\t\t\t<image class=\"photo-icon\" src=\"/static/img/photo-talent.png\"></image>\n\t\t\t\t\t\t\t\t\t<view class=\"photo-type\">独一无二的才艺</view>\n\t\t\t\t\t\t\t\t\t<view class=\"photo-boost\">+5%</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 回忆照 -->\n\t\t\t\t\t\t<view class=\"photo-item\" \n\t\t\t\t\t\t\t@tap=\"hasPhotoOfType('回忆照') ? showPhotoMenu(getPhotoObject('回忆照'), getPhotoIndex('回忆照')) : addPhotoByType('回忆照')\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<block v-if=\"hasPhotoOfType('回忆照')\">\n\t\t\t\t\t\t\t\t<image class=\"photo-image\" :src=\"getPhotoByType('回忆照')\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t<view class=\"photo-placeholder\">\n\t\t\t\t\t\t\t\t\t<image class=\"photo-icon\" src=\"/static/img/photo-memory.png\"></image>\n\t\t\t\t\t\t\t\t\t<view class=\"photo-type\">美好的回忆瞬间</view>\n\t\t\t\t\t\t\t\t\t<view class=\"photo-boost\">+5%</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 美食/宠物照 -->\n\t\t\t\t\t\t<view class=\"photo-item\" \n\t\t\t\t\t\t\t@tap=\"hasPhotoOfType('美食宠物照') ? showPhotoMenu(getPhotoObject('美食宠物照'), getPhotoIndex('美食宠物照')) : addPhotoByType('美食宠物照')\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<block v-if=\"hasPhotoOfType('美食宠物照')\">\n\t\t\t\t\t\t\t\t<image class=\"photo-image\" :src=\"getPhotoByType('美食宠物照')\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t\t<block v-else>\n\t\t\t\t\t\t\t\t<view class=\"photo-placeholder\">\n\t\t\t\t\t\t\t\t\t<image class=\"photo-icon\" src=\"/static/img/photo-food.png\"></image>\n\t\t\t\t\t\t\t\t\t<view class=\"photo-type\">最爱的美食/宠物</view>\n\t\t\t\t\t\t\t\t\t<view class=\"photo-boost\">+5%</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 其他个人信息字段 -->\n\t\t\t\t<view class=\"df sp\">\n\t\t\t\t\t<view class=\"title-label w50\">昵称</view>\n\t\t\t\t\t<view class=\"title-label w50\">性别</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"df sp\">\n\t\t<input \n\t\t\ttype=\"nickname\" \n\t\t\t\t\t\tname=\"nickname\"\n\t\t\t\t\t\tclass=\"input-box w50\" \n\t\t\t@blur=\"nameBlur\" \n\t\t\tmaxlength=\"16\" \n\t\t\tplaceholder=\"怎么称呼你\" \n\t\t\tv-model=\"userInfo.nickname\"\n\t\t/>\n\t\t\t\t\t<view class=\"input-box w50 df\" @tap=\"genderPopupClick(true)\">\n\t\t\t\t\t\t<view :style=\"{'color': userInfo.sex !== undefined ? '#000' : '#999'}\">\n\t\t\t\t\t\t\t<block v-if=\"userInfo.sex === 1\">男</block>\n\t\t\t\t\t\t\t<block v-else-if=\"userInfo.sex === 2\">女</block>\n\t\t\t\t\t\t\t<block v-else>未知</block>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<image src=\"/static/img/x.png\" style=\"width:24rpx;height:24rpx\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"title-label\">关于我</view>\n\t\t<textarea \n\t\t\t@blur=\"introBlur\" \n\t\t\tclass=\"textarea-box\" \n\t\t\t:show-confirm-bar=\"false\" \n\t\t\tcursor-spacing=\"30\" \n\t\t\tmaxlength=\"100\" \n\t\t\tplaceholder=\"添加个人简介，让大家认识你...\" \n\t\t\tauto-height \n\t\t\tv-model=\"userInfo.about_me\"\n\t\t></textarea>\n\t\t\n\t\t\t\t<!-- 新增兴趣标签选择器 -->\n\t\t\t\t<view class=\"title-label\">我的标签</view>\n\t\t\t\t<view class=\"input-box df\" @tap=\"newTagsPopupClick(true)\">\n\t\t\t\t\t<view :style=\"{'color': userInfo.interest_tags && userInfo.interest_tags.length > 0 ? '#000' : '#999'}\">\n\t\t\t\t\t\t<block v-if=\"userInfo.interest_tags && userInfo.interest_tags.length > 0\">\n\t\t\t\t\t\t\t{{userInfo.interest_tags.join('、')}}\n\t\t\t\t\t\t</block>\n\t\t\t\t\t\t<block v-else>点击选择标签</block>\n\t\t\t\t\t</view>\n\t\t\t\t\t<image src=\"/static/img/x.png\" style=\"width:24rpx;height:24rpx\"></image>\n\t\t</view>\n\t\t\n\t\t\t\t<!-- 生日和星座放在一排 -->\n\t\t\t\t<view class=\"df sp\">\n\t\t\t\t\t<view class=\"title-label w50\">生日</view>\n\t\t\t\t\t<view class=\"title-label w50\">星座</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"df sp\">\n\t\t\t\t\t<view class=\"input-box w50 df\" @tap=\"birthdayPopupClick(true)\">\n\t\t\t\t\t<view :style=\"{'color': userInfo.birthday ? '#000' : '#999'}\">\n\t\t\t\t\t\t{{formattedBirthday || '点击选择'}}\n\t\t\t\t</view>\n\t\t\t\t\t<image src=\"/static/img/x.png\" style=\"width:24rpx;height:24rpx\"></image>\n\t\t\t\t</view>\n\t\t\t\t\t<view class=\"input-box w50 df\">\n\t\t\t\t\t<view :style=\"{'color': userInfo.constellation !== null ? '#000' : '#999'}\">\n\t\t\t\t\t\t{{userInfo.constellation !== null ? getConstellationName(userInfo.constellation) : '选择生日后自动生成'}}\n\t\t\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 学校和家乡放在一排 -->\n\t\t\t\t<view class=\"df sp\">\n\t\t\t\t\t<view class=\"title-label w50\">学校</view>\n\t\t\t\t\t<view class=\"title-label w50\">家乡</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"df sp\">\n\t\t\t\t<input \n\t\t\t\t\t\tclass=\"input-box w50\" \n\t\t\t\t\tmaxlength=\"30\" \n\t\t\t\t\tplaceholder=\"填写你的学校\" \n\t\t\t\t\tv-model=\"userInfo.school\"\n\t\t\t\t/>\n\t\t\t\t<input \n\t\t\t\t\t\tclass=\"input-box w50\" \n\t\t\t\t\tmaxlength=\"30\" \n\t\t\t\t\tplaceholder=\"填写你的家乡\" \n\t\t\t\t\tv-model=\"userInfo.hometown\"\n\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 新增字段：职业 -->\n\t\t\t\t<view class=\"title-label\">职业</view>\n\t\t\t\t<input \n\t\t\t\t\tclass=\"input-box\" \n\t\t\t\t\tmaxlength=\"30\" \n\t\t\t\t\tplaceholder=\"填写你的职业\" \n\t\t\t\t\tv-model=\"userInfo.occupation\"\n\t\t\t\t/>\n\t\t\t\t\n\t\t\t\t<!-- 新增字段：身高和体重 -->\n\t\t\t\t<view class=\"df sp\">\n\t\t\t\t\t<view class=\"title-label w50\">身高(cm)</view>\n\t\t\t\t\t<view class=\"title-label w50\">体重(kg)</view>\n\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"df sp\">\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"input-box w50\" \n\t\t\t\t\t\ttype=\"digit\" \n\t\t\t\t\t\tmaxlength=\"5\" \n\t\t\t\t\t\tplaceholder=\"填写身高\" \n\t\t\t\t\t\tv-model=\"userInfo.height\"\n\t\t\t\t\t/>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"input-box w50\" \n\t\t\t\t\t\ttype=\"digit\" \n\t\t\t\t\t\tmaxlength=\"5\" \n\t\t\t\t\t\tplaceholder=\"填写体重\" \n\t\t\t\t\t\tv-model=\"userInfo.weight\"\n\t\t\t\t\t/>\n\t\t</view>\n\t\t\n\t\t<view class=\"title-label\">手机号</view>\n\t\t<view class=\"input-box df\">\n\t\t\t<view>{{userInfo.phone || '未绑定'}}</view>\n\t\t\t<button class=\"input-btn df\" open-type=\"getPhoneNumber\" @getphonenumber=\"bindMobileClick\">\n\t\t\t\t<image src=\"/static/img/dh.png\"></image>\n\t\t\t\t<text>{{userInfo.phone ? '换绑' : '绑定'}}</text>\n\t\t\t</button>\n\t\t</view>\n\t\t\n\t\t<view class=\"title-label\">实名认证</view>\n\t\t<view class=\"input-box df\">\n\t\t\t<view>{{getAuthStatusText()}}</view>\n\t\t\t<view class=\"input-btn df\" @tap=\"goToRealAuth\">\n\t\t\t\t<image src=\"/static/img/verified.png\"></image>\n\t\t\t\t<text>{{userInfo.auth_status === 2 ? '完成' : '认证'}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"title-label\">IP属地</view>\n\t\t<view class=\"input-box df\">\n\t\t\t<view>{{userInfo.residence_name || userInfo.province || '未知'}}</view>\n\t\t\t<view class=\"input-btn df\" @tap=\"refreshIpClick\">\n\t\t\t\t<image src=\"/static/img/ip.png\"></image>\n\t\t\t\t<text>刷新</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"input-tips\">\n\t\t\tIP属地说明：为维护网络安全、保障良好生态和社区的真实性，根据网络运营商数据，展示用户IP属地信息。\n\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 底部操作按钮 -->\n\t\t\t\t<view class=\"footer-box bfw bUp\">\n\t\t\t\t\t<view class=\"footer-item df\">\n\t\t\t\t\t\t<view class=\"btn bg2\" @tap=\"saveAllChanges\">保存</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 生日选择弹窗 -->\n\t\t\t\t<uni-popup ref=\"birthdayPopup\" type=\"bottom\" :safe-area=\"false\">\n\t\t\t\t\t<view class=\"popup-box\">\n\t\t\t\t\t\t<view class=\"popup-top df\">\n\t\t\t\t\t\t\t<view class=\"popup-title\">\n\t\t\t\t\t\t\t\t<view class=\"t1\">选择生日</view>\n\t\t\t\t\t\t\t\t<view class=\"t2\">请选择您的出生日期</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"popup-close df\" @tap=\"birthdayPopupClick(false)\">\n\t\t\t\t\t\t\t\t<image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<picker-view\n\t\t\t\t\t\t\tclass=\"birthday-picker\"\n\t\t\t\t\t\t\t:indicator-style=\"'height: 50px;'\"\n\t\t\t\t\t\t\t:value=\"birthdayPickerValue\"\n\t\t\t\t\t\t\t@change=\"birthdayPickerChange\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<picker-view-column>\n\t\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"(item, index) in years\" :key=\"index\">{{item}}年</view>\n\t\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t\t\t<picker-view-column>\n\t\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"(item, index) in months\" :key=\"index\">{{item}}月</view>\n\t\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t\t\t<picker-view-column>\n\t\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"(item, index) in days\" :key=\"index\">{{item}}日</view>\n\t\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t\t</picker-view>\n\t\t\t\t\t\t<view class=\"popup-btn\" @tap=\"confirmBirthday\">确认保存</view>\n\t\t\t\t\t</view>\n\t\t\t\t</uni-popup>\n\t\t\n\t\t<!-- 年龄选择弹窗 -->\n\t\t<uni-popup ref=\"agePopup\" type=\"bottom\" :safe-area=\"false\">\n\t\t\t<view class=\"popup-box\">\n\t\t\t\t<view class=\"popup-top df\">\n\t\t\t\t\t<view class=\"popup-title\">\n\t\t\t\t\t\t\t\t<view class=\"t1\">选择兴趣爱好</view>\n\t\t\t\t\t\t\t\t<view class=\"t2\">可选择多个标签 ({{selectedTags.length}}/5)</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"popup-close df\" @tap=\"agePopupClick(false)\">\n\t\t\t\t\t\t<image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 标签分类导航 -->\n\t\t\t\t\t\t<scroll-view class=\"tag-categories\" scroll-x=\"true\" show-scrollbar=\"false\">\n\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tv-for=\"(category, index) in tagCategories\" \n\t\t\t\t\t\t:key=\"index\" \n\t\t\t\t\t\t\t\t:class=\"['category-item', currentCategoryIndex === index ? 'category-active' : '']\"\n\t\t\t\t\t\t\t\t@tap=\"switchCategory(index)\"\n\t\t\t\t\t\t\t\tstyle=\"position: relative;\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{{category.name}}\n\t\t\t\t\t\t\t\t<text v-if=\"category.tags && category.tags.length > 0\" \n\t\t\t\t\t\t\t\t\tstyle=\"font-size: 20rpx; margin-left: 6rpx; opacity: 0.8;\"\n\t\t\t\t\t\t\t\t>({{category.tags.length}})</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 标签内容区 -->\n\t\t\t\t\t\t<swiper class=\"tags-swiper\" :current=\"currentCategoryIndex\" @change=\"swiperChange\">\n\t\t\t\t\t\t\t<swiper-item v-for=\"(category, index) in tagCategories\" :key=\"index\">\n\t\t\t\t\t\t\t\t<scroll-view class=\"tags-scroll\" scroll-y=\"true\">\n\t\t\t\t\t\t\t\t\t<view class=\"tags-box\">\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"!category.tags || category.tags.length === 0\" class=\"no-tags\">\n\t\t\t\t\t\t\t\t\t\t\t该分类暂无标签\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\t\t\tv-for=\"(item, itemIndex) in category.tags\" \n\t\t\t\t\t\t\t\t\t\t\t:key=\"itemIndex\" \n\t\t\t\t\t\t\t\t\t\t\t:class=\"['tag-item', selectedTags.includes(item) ? 'tagactive' : '']\"\n\t\t\t\t\t\t\t\t\t\t\t@tap=\"toggleTag(item)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t{{item}}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t\t\t</swiper-item>\n\t\t\t\t\t\t</swiper>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"popup-btn\" @tap=\"confirmTags\">确认保存</view>\n\t\t\t\t\t</view>\n\t\t\t\t</uni-popup>\n\t\t\t\t\n\t\t\t\t<!-- 性别选择弹窗 -->\n\t\t\t\t<uni-popup ref=\"genderPopup\" type=\"bottom\" :safe-area=\"false\">\n\t\t\t\t\t<view class=\"popup-box\">\n\t\t\t\t\t\t<view class=\"popup-top df\">\n\t\t\t\t\t\t\t<view class=\"popup-title\">\n\t\t\t\t\t\t\t\t<view class=\"t1\">选择性别</view>\n\t\t\t\t\t\t\t\t<view class=\"t2\">请选择您的性别</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"popup-close df\" @tap=\"genderPopupClick(false)\">\n\t\t\t\t\t\t\t\t<image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"gender-box\">\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t@tap=\"genderItemClick(1)\" \n\t\t\t\t\t\t\t\t:class=\"['gender-item', 'df', tempGender == 1 ? 'active-1' : '']\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<image src=\"/static/img/nan.png\"></image>\n\t\t\t\t\t\t\t\t<text>男</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t@tap=\"genderItemClick(2)\" \n\t\t\t\t\t\t\t\t:class=\"['gender-item', 'df', tempGender == 2 ? 'active-2' : '']\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<image src=\"/static/img/nv.png\"></image>\n\t\t\t\t\t\t\t\t<text>女</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t@tap=\"genderItemClick(0)\" \n\t\t\t\t\t\t\t\t:class=\"['gender-item', 'df', tempGender == 0 ? 'active-1' : '']\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<text>未知</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"popup-btn\" @tap=\"confirmGender\">确认保存</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\n\t\t<!-- 提示弹窗 -->\n\t\t\t\t<uni-popup ref=\"tipsPopup\" type=\"center\" :mask-background-color=\"'rgba(0, 0, 0, 0.3)'\">\n\t\t\t<view class=\"tips-box df\">\n\t\t\t\t<view class=\"tips-item\">{{tipsTitle}}</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t\t\t\t\n\t\t\t\t<!-- 底部安全区域，防止内容被底部按钮遮挡 -->\n\t\t\t\t<view class=\"bottom-safe-area\"></view>\n\t\t\t\t\n\t\t\t\t<!-- canvas用于图片处理 -->\n\t\t\t\t<canvas canvas-id=\"canvas\" v-if=\"canvasStatus\"\n\t\t\t\t\t:style=\"{width: canvasWidth + 'px', height: canvasHeight + 'px', position: 'absolute', left:'-100000px', top:'-100000px'}\"></canvas>\n\t\t\t\t\n\t\t\t\t<!-- 新增标签选择弹窗 -->\n\t\t\t\t<uni-popup ref=\"newTagsPopup\" type=\"bottom\" :safe-area=\"false\">\n\t\t\t\t\t<view class=\"popup-box\">\n\t\t\t\t\t\t<view class=\"popup-top df\">\n\t\t\t\t\t\t\t<view class=\"popup-title\">\n\t\t\t\t\t\t\t\t<view class=\"t1\">选择我的标签</view>\n\t\t\t\t\t\t\t\t<view class=\"t2\">可选择多个标签 ({{selectedNewTags.length}}/5)</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"popup-close df\" @tap=\"newTagsPopupClick(false)\">\n\t\t\t\t\t\t\t\t<image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 标签分类导航 -->\n\t\t\t\t\t\t<scroll-view class=\"tag-categories\" scroll-x=\"true\" show-scrollbar=\"false\">\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tv-for=\"(category, index) in newTagCategories\" \n\t\t\t\t\t\t\t\t:key=\"index\" \n\t\t\t\t\t\t\t\t:class=\"['category-item', newCategoryIndex === index ? 'category-active' : '']\"\n\t\t\t\t\t\t\t\t@tap=\"switchNewCategory(index)\"\n\t\t\t\t\t\t\t\tstyle=\"position: relative;\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{{category.name}}\n\t\t\t\t\t\t\t\t<text v-if=\"category.tags && category.tags.length > 0\" \n\t\t\t\t\t\t\t\t\tstyle=\"font-size: 20rpx; margin-left: 6rpx; opacity: 0.8;\"\n\t\t\t\t\t\t\t\t>({{category.tags.length}})</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 标签内容区 -->\n\t\t\t\t\t\t<swiper class=\"tags-swiper\" :current=\"newCategoryIndex\" @change=\"newSwiperChange\">\n\t\t\t\t\t\t\t<swiper-item v-for=\"(category, index) in newTagCategories\" :key=\"index\">\n\t\t\t\t\t\t\t\t<scroll-view class=\"tags-scroll\" scroll-y=\"true\">\n\t\t\t\t\t\t\t\t\t<view class=\"tags-box\">\n\t\t\t\t\t\t\t\t\t\t<view v-if=\"!category.tags || category.tags.length === 0\" class=\"no-tags\">\n\t\t\t\t\t\t\t\t\t\t\t该分类暂无标签\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\t\t\t\tv-for=\"(item, itemIndex) in category.tags\" \n\t\t\t\t\t\t\t\t\t\t\t:key=\"itemIndex\" \n\t\t\t\t\t\t\t\t\t\t\t:class=\"['tag-item', selectedNewTags.includes(item) ? 'tagactive' : '']\"\n\t\t\t\t\t\t\t\t\t\t\t@tap=\"toggleNewTag(item)\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t{{item}}\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t\t\t</swiper-item>\n\t\t\t\t\t\t</swiper>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"popup-btn\" @tap=\"confirmNewTags\">确认保存</view>\n\t\t\t\t\t</view>\n\t\t\t\t</uni-popup>\n\t\t\t</view>\n\t\t</form>\n\t</view>\n</template>\n\n<script>\nimport { getMyTags, updateUserTags, getUserSocialInfo, updateUserSocialInfo, getTagsWithCategories } from '@/api/social.js'\nimport navbar from '@/components/navbar/navbar.vue'\nimport uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'\n\nconst app = getApp();\n\nexport default {\n\tcomponents: {\n\t\tnavbar,\n\t\tuniPopup\n\t},\n\tdata() {\n\t\t// 获取当前日期相关数据，用于生日选择器\n\t\tconst date = new Date();\n\t\tconst currentYear = date.getFullYear();\n\t\tconst years = Array.from({ length: 100 }, (_, i) => currentYear - 99 + i);\n\t\tconst months = Array.from({ length: 12 }, (_, i) => i + 1);\n\t\tconst days = Array.from({ length: 31 }, (_, i) => i + 1);\n\t\t\n\t\t// 初始化生日选择器的默认值（25岁，1月1日）\n\t\tconst defaultYearIndex = years.findIndex(year => year === currentYear - 25);\n\t\t\n\t\treturn {\n\t\t\tuserInfo: {\n\t\t\t\tavatar: \"\",\n\t\t\t\tnickname: \"\",\n\t\t\t\tabout_me: \"\",\n\t\t\t\tsex: 0,\n\t\t\t\tbirthday: \"\",\n\t\t\t\tconstellation: null,\n\t\t\t\tschool: \"\",\n\t\t\t\thometown: \"\",\n\t\t\t\toccupation: \"\",\n\t\t\t\tresidence_lat: 0,\n\t\t\t\tresidence_lng: 0,\n\t\t\t\tresidence_name: \"\",\n\t\t\t\tinterest_tags: [], // 标签数组\n\t\t\t},\n\t\t\t// 照片相关\n\t\t\tbackgroundImages: [],\n\t\t\tphotoTypes: ['生活照', '旅行照', '才艺照', '回忆照', '美食宠物照'],\n\t\t\t\n\t\t\t// 生日选择器\n\t\t\tyears,\n\t\t\tmonths,\n\t\t\tdays,\n\t\t\tbirthdayPickerValue: [defaultYearIndex, 0, 0],\n\t\t\t\n\t\t\t// 优化的标签状态管理\n\t\t\ttagState: {\n\t\t\t\tselectedTags: [],        // 当前选中的标签\n\t\t\t\tmyTags: [],             // 我的标签数据\n\t\t\t\tcategories: [],         // 标签分类数据\n\t\t\t\tcurrentCategoryIndex: 0, // 当前分类索引\n\t\t\t\tisLoading: false        // 加载状态\n\t\t\t},\n\n\t\t\t// 优化的UI状态管理\n\t\t\tuiState: {\n\t\t\t\tcanvasStatus: false,\n\t\t\t\tcanvasWidth: 0,\n\t\t\t\tcanvasHeight: 0,\n\t\t\t\ttempGender: undefined,   // 临时存储性别选择\n\t\t\t\ttipsTitle: ''           // 提示标题\n\t\t\t},\n\n\t\t\t// 防抖和性能优化\n\t\t\tdebounceTimers: {\n\t\t\t\tsave: null,\n\t\t\t\tupload: null\n\t\t\t}\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 格式化生日显示\n\t\tformattedBirthday() {\n\t\t\tif (!this.userInfo.birthday) return '';\n\t\t\t\n\t\t\t// 处理时间戳格式\n\t\t\tif (typeof this.userInfo.birthday === 'number' || !isNaN(this.userInfo.birthday)) {\n\t\t\t\t\tconst timestamp = parseInt(this.userInfo.birthday);\n\t\t\t\t\tconst date = new Date(timestamp * 1000);\n\t\t\t\treturn this.formatDate(date);\n\t\t\t}\n\t\t\t\n\t\t\treturn this.userInfo.birthday;\n\t\t},\n\t\t\n\t\t// 获取当前分类的标签\n\t\tcurrentCategoryTags() {\n\t\t\tconst currentCategory = this.tagState.categories[this.tagState.currentCategoryIndex];\n\t\t\treturn currentCategory?.tags || [];\n\t\t}\n\t},\n\tonLoad() {\n\t\t// 统一加载数据\n\t\tthis.initializeData();\n\t},\n\n\tonShow() {\n\t\t// 每次显示页面时检查用户是否切换\n\t\tthis.checkUserChange();\n\t},\n\tmethods: {\n\t\t// 检查用户是否切换\n\t\tasync checkUserChange() {\n\t\t\ttry {\n\t\t\t\t// 获取当前登录用户的ID和token\n\t\t\t\tconst currentUserId = this.$store.state.app.uid;\n\t\t\t\tconst currentToken = this.$store.state.app.token;\n\n\t\t\t\t// 检查是否有有效的登录状态\n\t\t\t\tif (!currentToken || !currentUserId) {\n\t\t\t\t\tconsole.log('用户未登录，跳转到登录页');\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/users/login/index'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 如果用户ID发生变化，或者页面数据为空，重新加载数据\n\t\t\t\tif (!this.userInfo.uid || currentUserId !== this.userInfo.uid) {\n\t\t\t\t\tconsole.log('检测到用户切换或数据为空，重新加载数据');\n\t\t\t\t\tconsole.log('当前用户ID:', currentUserId, '页面用户ID:', this.userInfo.uid);\n\t\t\t\t\tawait this.refreshUserData();\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('检查用户切换失败:', error);\n\t\t\t}\n\t\t},\n\n\t\t// 刷新用户数据\n\t\tasync refreshUserData() {\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '刷新数据...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\n\t\t\t\t// 重新获取用户信息和相关数据\n\t\t\t\tawait Promise.all([\n\t\t\t\t\tthis.getUserInfo(),\n\t\t\t\t\tthis.loadMyTags()\n\t\t\t\t]);\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('刷新用户数据失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '刷新失败，请重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\n\t\t// 统一初始化数据\n\t\tasync initializeData() {\n\t\t\ttry {\n\t\t\t\t// 显示加载中\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载中...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 并行加载数据\n\t\t\t\tawait Promise.all([\n\t\t\t\t\tthis.getUserInfo(),\n\t\t\t\t\tthis.loadTagCategories(),\n\t\t\t\t\tthis.loadMyTags(), // 添加加载我的标签\n\t\t\t\t\tthis.getUserLocation()\n\t\t\t\t]);\n\t\t\t\t\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('初始化数据失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载失败，请重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取用户信息\n\t\tasync getUserInfo() {\n\t\t\ttry {\n\t\t\t\tconst res = await getUserSocialInfo();\n\t\t\t\tif (res.code === 200 && res.data) {\n\t\t\t\t\tconsole.log('获取用户信息成功:', res.data);\n\n\t\t\t\t\t// 完全替换用户信息，避免保留上一个用户的数据\n\t\t\t\t\tthis.userInfo = res.data;\n\n\t\t\t\t\t// 初始化标签选择\n\t\t\t\t\tif (res.data.interest_tags) {\n\t\t\t\t\t\tthis.tagState.selectedTags = [...res.data.interest_tags];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.tagState.selectedTags = []; // 清空标签选择\n\t\t\t\t\t}\n\n\t\t\t\t\t// 清空背景图片数据，避免显示上一个用户的图片\n\t\t\t\t\tthis.backgroundImages = res.data.background_images || [];\n\n\t\t\t\t\t// 保存到本地\n\t\t\t\t\tuni.setStorageSync('USER_INFO', res.data);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取用户信息失败:', error);\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 优化的标签加载方法\n\t\tasync loadTagCategories() {\n\t\t\tif (this.tagState.isLoading) return;\n\t\t\tthis.tagState.isLoading = true;\n\n\t\t\ttry {\n\t\t\t\tconst res = await getTagsWithCategories();\n\t\t\t\tif (res.code === 200 && res.data) {\n\t\t\t\t\tthis.processNewTagsData(res.data);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载标签分类失败:', error);\n\t\t\t\tthrow error;\n\t\t\t} finally {\n\t\t\t\tthis.tagState.isLoading = false;\n\t\t\t}\n\t\t},\n\n\t\t// 防抖处理方法\n\t\tdebounce(func, delay = 300, timerKey = 'default') {\n\t\t\tif (this.debounceTimers[timerKey]) {\n\t\t\t\tclearTimeout(this.debounceTimers[timerKey]);\n\t\t\t}\n\n\t\t\tthis.debounceTimers[timerKey] = setTimeout(() => {\n\t\t\t\tfunc.call(this);\n\t\t\t\tthis.debounceTimers[timerKey] = null;\n\t\t\t}, delay);\n\t\t},\n\n\t\t// 统一的错误处理方法\n\t\thandleError(error, context = '操作') {\n\t\t\tconsole.error(`${context}失败:`, error);\n\n\t\t\tlet message = `${context}失败，请稍后重试`;\n\t\t\tif (error.message) {\n\t\t\t\tmessage = error.message;\n\t\t\t} else if (error.msg) {\n\t\t\t\tmessage = error.msg;\n\t\t\t}\n\n\t\t\tuni.showToast({\n\t\t\t\ttitle: message,\n\t\t\t\ticon: 'none',\n\t\t\t\tduration: 2000\n\t\t\t});\n\t\t},\n\n\t\t// 优化的照片处理方法\n\t\thasPhotoOfType(type) {\n\t\t\treturn this.backgroundImages.some(img => img.type === type);\n\t\t},\n\n\t\tgetPhotoByType(type) {\n\t\t\tconst photo = this.backgroundImages.find(img => img.type === type);\n\t\t\treturn photo ? photo.url : '';\n\t\t},\n\n\t\tgetPhotoObject(type) {\n\t\t\treturn this.backgroundImages.find(img => img.type === type) || null;\n\t\t},\n\n\t\tgetPhotoIndex(type) {\n\t\t\treturn this.backgroundImages.findIndex(img => img.type === type);\n\t\t},\n\t\t\n\t\t// 处理标签数据\n\t\tprocessNewTagsData(data) {\n\t\t\ttry {\n\t\t\t\tconst tagsArray = Array.isArray(data) ? data : (data.list || data.data || []);\n\t\t\t\t\n\t\t\t\tthis.newTagCategories = tagsArray.map(category => {\n\t\t\t\t\tconst tags = this.extractTags(category);\n\t\t\t\t\tconst originalTags = this.getOriginalTags(category);\n\t\t\t\t\t\n\t\t\t\t\treturn {\n\t\t\t\t\t\tid: category.id || 0,\n\t\t\t\t\t\tname: category.name || '未分类',\n\t\t\t\t\t\ttags: tags || [],\n\t\t\t\t\t\toriginalTags: originalTags || []\n\t\t\t\t\t};\n\t\t\t\t}).filter(category => category.tags && category.tags.length > 0);\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('处理标签数据失败:', error);\n\t\t\t\tthis.newTagCategories = [];\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 提取标签名称\n\t\textractTags(category) {\n\t\t\tif (Array.isArray(category.tags)) {\n\t\t\t\treturn category.tags.map(tag => typeof tag === 'string' ? tag : tag.name);\n\t\t\t}\n\t\t\tif (Array.isArray(category.tag_list)) {\n\t\t\t\treturn category.tag_list.map(tag => typeof tag === 'string' ? tag : tag.name);\n\t\t\t}\n\t\t\tif (Array.isArray(category.tagList)) {\n\t\t\t\treturn category.tagList.map(tag => typeof tag === 'string' ? tag : tag.name);\n\t\t\t}\n\t\t\treturn category.name ? [category.name] : [];\n\t\t},\n\t\t\n\t\t// 获取原始标签数据\n\t\tgetOriginalTags(category) {\n\t\t\tif (Array.isArray(category.tags)) return category.tags;\n\t\t\tif (Array.isArray(category.tag_list)) return category.tag_list;\n\t\t\tif (Array.isArray(category.tagList)) return category.tagList;\n\t\t\treturn category.name ? [category] : [];\n\t\t},\n\t\t\n\t\t// 格式化日期\n\t\tformatDate(date) {\n\t\t\tconst year = date.getFullYear();\n\t\t\tconst month = (date.getMonth() + 1).toString().padStart(2, '0');\n\t\t\tconst day = date.getDate().toString().padStart(2, '0');\n\t\t\treturn `${year}-${month}-${day}`;\n\t\t},\n\t\t\n\t\t// 获取用户位置信息\n\t\tgetUserLocation() {\n\t\t\t// 先尝试从缓存获取\n\t\t\tconst cachedLatitude = uni.getStorageSync('residence_lat');\n\t\t\tconst cachedLongitude = uni.getStorageSync('residence_lng');\n\t\t\t\n\t\t\tif (cachedLatitude && cachedLongitude) {\n\t\t\t\tthis.userInfo.residence_lat = parseFloat(cachedLatitude);\n\t\t\t\tthis.userInfo.residence_lng = parseFloat(cachedLongitude);\n\t\t\t\tconsole.log('个人资料页：从缓存获取到用户位置:', this.userInfo.residence_lat, this.userInfo.residence_lng);\n\t\t\t\t\n\t\t\t\t// 如果有缓存的地址信息，先使用缓存\n\t\t\t\tconst cachedAddress = uni.getStorageSync('residence_name');\n\t\t\t\tif (cachedAddress) {\n\t\t\t\t\tthis.userInfo.residence_name = cachedAddress;\n\t\t\t\t\tthis.userInfo.province = cachedAddress;\n\t\t\t\t\tconsole.log('从缓存获取到地址:', cachedAddress);\n\t\t\t\t} else {\n\t\t\t\t\t// 根据经纬度获取地址\n\t\t\t\t\tthis.getAddressFromLocation(this.userInfo.residence_lat, this.userInfo.residence_lng);\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 缓存中没有位置信息，尝试获取当前位置\n\t\t\tconsole.log('个人资料页：开始获取用户位置信息');\n\t\t\t\n\t\t\t// #ifdef H5\n\t\t\t// 检查是否在微信环境中\n\t\t\tconst ua = navigator.userAgent.toLowerCase();\n\t\t\tconst isWeixin = ua.indexOf('micromessenger') !== -1;\n\t\t\t\n\t\t\tif (isWeixin && this.$wechat && this.$wechat.isWeixin()) {\n\t\t\t\t// 微信环境下使用微信API获取位置\n\t\t\t\tthis.$wechat.location().then(res => {\n\t\t\t\t\tthis.userInfo.residence_lat = res.latitude;\n\t\t\t\t\tthis.userInfo.residence_lng = res.longitude;\n\t\t\t\t\t// 保存到缓存\n\t\t\t\t\tuni.setStorageSync('residence_lat', res.latitude);\n\t\t\t\t\tuni.setStorageSync('residence_lng', res.longitude);\n\t\t\t\t\tconsole.log('个人资料页：微信获取位置成功:', res.latitude, res.longitude);\n\t\t\t\t\t\n\t\t\t\t\t// 根据经纬度获取地址\n\t\t\t\t\tthis.getAddressFromLocation(res.latitude, res.longitude);\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.log('个人资料页：微信获取位置失败，使用默认位置:', err);\n\t\t\t\t\tthis.setDefaultLocation();\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t// #endif\n\t\t\t\t// 非微信环境或APP环境使用uni.getLocation\n\t\t\t\tuni.getLocation({\n\t\t\t\t\ttype: 'wgs84',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tthis.userInfo.residence_lat = res.latitude;\n\t\t\t\t\t\tthis.userInfo.residence_lng = res.longitude;\n\t\t\t\t\t\t// 保存到缓存\n\t\t\t\t\t\tuni.setStorageSync('residence_lat', res.latitude);\n\t\t\t\t\t\tuni.setStorageSync('residence_lng', res.longitude);\n\t\t\t\t\t\tconsole.log('个人资料页：uni.getLocation获取位置成功:', res.latitude, res.longitude);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 根据经纬度获取地址\n\t\t\t\t\t\tthis.getAddressFromLocation(res.latitude, res.longitude);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.log('个人资料页：获取位置失败，使用默认位置:', err);\n\t\t\t\t\t\tthis.setDefaultLocation();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t// #ifdef H5\n\t\t\t}\n\t\t\t// #endif\n\t\t},\n\t\t\n\t\t// 设置默认位置（广州）\n\t\tsetDefaultLocation() {\n\t\t\tthis.userInfo.residence_lat = 23.12908; // 广州纬度\n\t\t\tthis.userInfo.residence_lng = 113.26436; // 广州经度\n\t\t\tthis.userInfo.residence_name = \"广东省广州市\";\n\t\t\tthis.userInfo.province = \"广东省广州市\";\n\t\t\t// 保存到缓存\n\t\t\tuni.setStorageSync('residence_lat', this.userInfo.residence_lat);\n\t\t\tuni.setStorageSync('residence_lng', this.userInfo.residence_lng);\n\t\t\tuni.setStorageSync('residence_name', this.userInfo.residence_name);\n\t\t\tconsole.log('个人资料页：使用默认位置（广州）:', this.userInfo.residence_lat, this.userInfo.residence_lng);\n\t\t\tconsole.log('设置默认地址:', this.userInfo.residence_name);\n\t\t},\n\t\t\n\t\t// 强制刷新位置信息\n\t\trefreshLocation() {\n\t\t\tconsole.log('个人资料页：强制刷新位置信息');\n\t\t\t// 清除缓存的位置信息\n\t\t\tuni.removeStorageSync('residence_lat');\n\t\t\tuni.removeStorageSync('residence_lng');\n\t\t\tuni.removeStorageSync('residence_name');\n\t\t\t// 重新获取位置\n\t\t\tthis.getUserLocation();\n\t\t},\n\t\t\n\t\t// 根据经纬度获取地址信息（逆地理编码）\n\t\tgetAddressFromLocation(latitude, longitude) {\n\t\t\tconsole.log('开始逆地理编码，经纬度:', latitude, longitude);\n\t\t\t\n\t\t\t// 使用腾讯地图逆地理编码API\n\t\t\tuni.request({\n\t\t\t\turl: 'https://apis.map.qq.com/ws/geocoder/v1/',\n\t\t\t\tdata: {\n\t\t\t\t\tlocation: `${latitude},${longitude}`,\n\t\t\t\t\tkey: 'F7LBZ-NLU6D-6524Z-PK6ZQ-D47AJ-KRB2I', // 腾讯地图API key\n\t\t\t\t\tget_poi: 0\n\t\t\t\t},\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('逆地理编码响应:', res.data);\n\t\t\t\t\tif (res.data.status === 0 && res.data.result) {\n\t\t\t\t\t\tconst result = res.data.result;\n\t\t\t\t\t\tconst address = result.address;\n\t\t\t\t\t\tconst province = result.ad_info.province;\n\t\t\t\t\t\tconst city = result.ad_info.city;\n\t\t\t\t\t\tconst district = result.ad_info.district;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 组合地址信息\n\t\t\t\t\t\tlet fullAddress = '';\n\t\t\t\t\t\tif (province && city) {\n\t\t\t\t\t\t\tif (province === city) {\n\t\t\t\t\t\t\t\t// 直辖市情况\n\t\t\t\t\t\t\t\tfullAddress = province + (district || '');\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// 普通省市情况\n\t\t\t\t\t\t\t\tfullAddress = province + city + (district || '');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 备用方案\n\t\t\t\t\t\t\tfullAddress = address || '未知位置';\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 更新地址信息\n\t\t\t\t\t\tthis.userInfo.residence_name = fullAddress;\n\t\t\t\t\t\tthis.userInfo.province = fullAddress;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 保存到缓存\n\t\t\t\t\t\tuni.setStorageSync('residence_name', fullAddress);\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log('逆地理编码成功，地址:', fullAddress);\n\t\t\t\t\t\tthis.opTipsPopup('位置获取成功: ' + fullAddress);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('逆地理编码失败:', res.data);\n\t\t\t\t\t\tthis.setFallbackAddress();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('逆地理编码请求失败:', err);\n\t\t\t\t\tthis.setFallbackAddress();\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 设置备用地址\n\t\tsetFallbackAddress() {\n\t\t\t// 如果逆地理编码失败，使用默认地址\n\t\t\tconst defaultAddress = \"位置获取中...\";\n\t\t\tthis.userInfo.residence_name = defaultAddress;\n\t\t\tthis.userInfo.province = defaultAddress;\n\t\t\tconsole.log('设置备用地址:', defaultAddress);\n\t\t},\n\t\t\n\t\t// 同步位置信息到userInfo\n\t\tsyncLocationInfo() {\n\t\t\t// 从缓存获取位置信息\n\t\t\tconst cachedLatitude = uni.getStorageSync('residence_lat');\n\t\t\tconst cachedLongitude = uni.getStorageSync('residence_lng');\n\t\t\tconst cachedAddress = uni.getStorageSync('residence_name');\n\t\t\t\n\t\t\tif (cachedLatitude && cachedLongitude) {\n\t\t\t\t// 如果userInfo中没有位置信息，则更新\n\t\t\t\tif (!this.userInfo.residence_lat || !this.userInfo.residence_lng) {\n\t\t\t\t\tthis.userInfo.residence_lat = parseFloat(cachedLatitude);\n\t\t\t\t\tthis.userInfo.residence_lng = parseFloat(cachedLongitude);\n\t\t\t\t\tconsole.log('同步位置信息到userInfo:', this.userInfo.residence_lat, this.userInfo.residence_lng);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 同步地址信息\n\t\t\t\tif (cachedAddress && !this.userInfo.residence_name) {\n\t\t\t\t\tthis.userInfo.residence_name = cachedAddress;\n\t\t\t\t\tthis.userInfo.province = cachedAddress;\n\t\t\t\t\tconsole.log('同步地址信息到userInfo:', cachedAddress);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// 如果缓存中没有位置信息，获取位置\n\t\t\t\tthis.getUserLocation();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 统一的缓存解析方法\n\t\tgetUserInfoFromCache() {\n\t\t\tlet userInfo = uni.getStorageSync('USER_INFO') || {};\n\t\t\t\n\t\t\t// 如果缓存返回字符串，先解析它\n\t\t\tif (typeof userInfo === 'string') {\n\t\t\t\ttry {\n\t\t\t\t\tuserInfo = JSON.parse(userInfo);\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('解析USER_INFO缓存失败:', e);\n\t\t\t\t\tuserInfo = {};\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\treturn userInfo;\n\t\t},\n\t\t\n\t\t// 处理背景图片数据\n\t\thandleBackgroundImages() {\n\t\t\tif (this.userInfo.home_background) {\n\t\t\t\ttry {\n\t\t\t\t\t// 尝试解析JSON\n\t\t\t\t\tif (typeof this.userInfo.home_background === 'string') {\n\t\t\t\t\t\tthis.backgroundImages = JSON.parse(this.userInfo.home_background);\n\t\t\t\t\t} else if (Array.isArray(this.userInfo.home_background)) {\n\t\t\t\t\t\tthis.backgroundImages = this.userInfo.home_background;\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('解析背景图片数据失败', e);\n\t\t\t\t\tthis.backgroundImages = [];\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthis.backgroundImages = [];\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 检查是否有指定类型的照片\n\t\thasPhotoOfType(type) {\n\t\t\treturn this.backgroundImages.some(item => item.type === type);\n\t\t},\n\t\t\n\t\t// 获取指定类型的照片URL\n\t\tgetPhotoByType(type) {\n\t\t\tconst photo = this.backgroundImages.find(item => item.type === type);\n\t\t\treturn photo ? photo.url : '';\n\t\t},\n\t\t\n\t\t// 获取指定类型的照片对象\n\t\tgetPhotoObject(type) {\n\t\t\treturn this.backgroundImages.find(item => item.type === type) || null;\n\t\t},\n\t\t\n\t\t// 获取指定类型的照片索引\n\t\tgetPhotoIndex(type) {\n\t\t\treturn this.backgroundImages.findIndex(item => item.type === type);\n\t\t},\n\t\t\n\t\t// 根据类型添加照片\n\t\taddPhotoByType(type) {\n\t\t\tlet that = this;\n\t\t\t\n\t\t\t// 如果没有指定类型，弹出选择类型的菜单\n\t\t\tif (!type) {\n\t\t\t\tconst photoTypes = ['生活照', '旅行照', '才艺照', '回忆照', '美食宠物照'];\n\t\t\t\t// 过滤出已经有的类型\n\t\t\t\tconst existingTypes = this.backgroundImages.map(item => item.type);\n\t\t\t\tconst availableTypes = photoTypes.filter(t => !existingTypes.includes(t));\n\t\t\t\t\n\t\t\t\tif (availableTypes.length === 0) {\n\t\t\t\t\tthis.opTipsPopup('已经添加了所有类型的照片');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.showActionSheet({\n\t\t\t\t\titemList: availableTypes,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconst selectedType = availableTypes[res.tapIndex];\n\t\t\t\t\t\tthat.chooseAndUploadPhoto(selectedType);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 如果指定了类型，直接选择照片\n\t\t\tthat.chooseAndUploadPhoto(type);\n\t\t},\n\t\t\n\t\t// 选择并上传照片\n\t\tchooseAndUploadPhoto(type) {\n\t\t\tlet that = this;\n\t\t\tthat.canvasStatus = true;\n\t\t\tthat.$util.uploadImageChange('upload/image', (res) => {\n\t\t\t\t// 如果已经有这个类型的照片，先删除\n\t\t\t\tthat.backgroundImages = that.backgroundImages.filter(item => item.type !== type);\n\t\t\t\t// 添加新照片\n\t\t\t\tthat.backgroundImages.push({\n\t\t\t\t\ttype: type,\n\t\t\t\t\turl: res.data.url\n\t\t\t\t});\n\t\t\t\tthat.canvasStatus = false;\n\t\t\t\tthat.opTipsPopup('上传成功');\n\t\t\t}, (res) => {\n\t\t\t\tthat.canvasStatus = false;\n\t\t\t\tthat.opTipsPopup('上传失败');\n\t\t\t}, (res) => {\n\t\t\t\tthat.canvasWidth = res.w;\n\t\t\t\tthat.canvasHeight = res.h;\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 更换头像\n\t\tchangeProfilePhoto() {\n\t\t\tlet that = this;\n\t\t\tthis.canvasStatus = true;\n\t\t\t\n\t\t\tthat.$util.uploadImageChange('upload/image', (res) => {\n\t\t\t\tthat.canvasStatus = false;\n\t\t\t\t\n\t\t\t\t// 只在本地更新头像，不发送到服务器\n\t\t\t\tthat.userInfo.avatar = res.data.url;\n\t\t\t\t\n\t\t\t\t// 提示用户需要点击保存按钮\n\t\t\t\tthat.opTipsPopup('头像已选择，点击保存按钮生效');\n\t\t\t}, (res) => {\n\t\t\t\tthat.canvasStatus = false;\n\t\t\t\tthat.opTipsPopup('头像选择取消');\n\t\t\t}, (res) => {\n\t\t\t\tthat.canvasWidth = res.w;\n\t\t\t\tthat.canvasHeight = res.h;\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 微信头像获取（小程序专用）\n\t\tonChooseAvatar(e) {\n\t\t\tconst that = this;\n\t\t\tconst { avatarUrl } = e.detail;\n\t\t\t\n\t\t\tthis.$util.uploadImgs('upload/image', avatarUrl, (res) => {\n\t\t\t\t// 仅在本地更新头像，不发送到服务器\n\t\t\t\tthat.userInfo.avatar = res.data.url;\n\t\t\t\tthat.opTipsPopup('头像已选择，点击保存按钮生效');\n\t\t\t}, (err) => {\n\t\t\t\tconsole.log(err);\n\t\t\t\tthis.opTipsPopup('头像上传失败');\n\t\t\t});\n\t\t},\n\t\t\n\t\tnameBlur() {\n\t\t\t// 在有了保存按钮后，不需要在失去焦点时立即保存\n\t\t\t// 仅记录修改状态，由用户决定何时保存\n\t\t},\n\t\t\n\t\tintroBlur() {\n\t\t\t// 在有了保存按钮后，不需要在失去焦点时立即保存\n\t\t\t// 仅记录修改状态，由用户决定何时保存\n\t\t},\n\t\t\n\t\tuserUpInfo(type = 0) {\n\t\t\tlet that = this;\n\t\t\t\n\t\t\t// 先处理背景图片数据\n\t\t\tthat.userInfo.home_background = JSON.stringify(that.backgroundImages);\n\t\t\t\n\t\t\t// 尝试使用真实API\n\t\t\tif (api.default && api.default.api && api.default.api.editUserInfoUrl) {\n\t\t\t\trequest(api.default.api.editUserInfoUrl, {\n\t\t\t\t\ttype: type,\n\t\t\t\t\tname: that.userInfo.name,\n\t\t\t\t\tintro: that.userInfo.intro,\n\t\t\t\t\tavatar: that.userInfo.avatar,\n\t\t\t\t\tgender: that.userInfo.gender,\n\t\t\t\t\tage: that.userInfo.age,\n\t\t\t\t\t// 新增字段\n\t\t\t\t\tschool: that.userInfo.school,\n\t\t\t\t\thometown: that.userInfo.hometown,\n\t\t\t\t\toccupation: that.userInfo.occupation,\n\t\t\t\t\theight: that.userInfo.height,\n\t\t\t\t\tweight: that.userInfo.weight,\n\t\t\t\t\tbirthday: that.userInfo.birthday,\n\t\t\t\t\thome_background: that.userInfo.home_background\n\t\t\t\t}, \"POST\").then(function(res) {\n\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\tlet userInfo = that.getUserInfoFromCache();\n\t\t\t\t\t\tuserInfo = {...userInfo, ...that.userInfo};\n\t\t\t\t\t\tuni.setStorageSync('USER_INFO', userInfo);\n\t\t\t\t\t\tthat.originalUserInfo = JSON.parse(JSON.stringify(that.userInfo)); // 更新原始信息\n\t\t\t\t\t}\n\t\t\t\t\tthat.opTipsPopup(res.msg);\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\t// 使用Mock数据\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tlet userInfo = that.getUserInfoFromCache();\n\t\t\t\t\tuserInfo = {\n\t\t\t\t\t\t...userInfo,\n\t\t\t\t\t\tname: that.userInfo.name,\n\t\t\t\t\t\tintro: that.userInfo.intro,\n\t\t\t\t\t\tavatar: that.userInfo.avatar,\n\t\t\t\t\t\tgender: that.userInfo.gender,\n\t\t\t\t\t\tage: that.userInfo.age,\n\t\t\t\t\t\t// 新增字段\n\t\t\t\t\t\tschool: that.userInfo.school,\n\t\t\t\t\t\thometown: that.userInfo.hometown,\n\t\t\t\t\t\tprofession: that.userInfo.profession,\n\t\t\t\t\t\theight: that.userInfo.height,\n\t\t\t\t\t\tweight: that.userInfo.weight,\n\t\t\t\t\t\tbirthday: that.userInfo.birthday,\n\t\t\t\t\t\thome_background: that.userInfo.home_background\n\t\t\t\t\t};\n\t\t\t\t\tuni.setStorageSync('USER_INFO', userInfo);\n\t\t\t\t\tthat.originalUserInfo = JSON.parse(JSON.stringify(userInfo)); // 更新原始信息\n\t\t\t\t\t\n\t\t\t\t\tconst messages = [\n\t\t\t\t\t\t'个人信息更新成功',\n\t\t\t\t\t\t'昵称修改成功',\n\t\t\t\t\t\t'简介修改成功',\n\t\t\t\t\t\t'头像更新成功',\n\t\t\t\t\t\t'性别设置成功',\n\t\t\t\t\t\t'年龄设置成功'\n\t\t\t\t\t];\n\t\t\t\t\tthat.opTipsPopup(messages[type] || messages[0]);\n\t\t\t\t}, 300);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 保存所有修改\n\t\tsaveAllChanges() {\n\t\t\tconst that = this;\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '保存中...',\n\t\t\t\tmask: true\n\t\t\t});\n\t\t\t\n\t\t\t// 处理背景图片数据，确保以JSON字符串形式提交\n\t\t\tconst homeBackground = typeof that.backgroundImages === 'string' \n\t\t\t\t? that.backgroundImages \n\t\t\t\t: JSON.stringify(that.backgroundImages);\n\t\t\t\n\t\t\t// 准备要提交的所有数据\n\t\t\tconst submitData = {\n\t\t\t\t// 用户基本信息\n\t\t\t\tavatar: that.userInfo.avatar,\n\t\t\t\tnickname: that.userInfo.nickname,\n\t\t\t\tbirthday: that.userInfo.birthday,\n\t\t\t\t\n\t\t\t\t// 社交信息\n\t\t\t\tsex: that.userInfo.sex,\n\t\t\t\tabout_me: that.userInfo.about_me,\n\t\t\t\tschool: that.userInfo.school,\n\t\t\t\thometown: that.userInfo.hometown,\n\t\t\t\toccupation: that.userInfo.occupation,\n\t\t\t\theight: that.userInfo.height,\n\t\t\t\tweight: that.userInfo.weight,\n\t\t\t\thome_background: homeBackground,\n\t\t\t\tconstellation: that.userInfo.constellation,\n\t\t\t\t\n\t\t\t\t// 位置信息 - 提交residence字段\n\t\t\t\tresidence_lat: that.userInfo.residence_lat || 0,\n\t\t\t\tresidence_lng: that.userInfo.residence_lng || 0,\n\t\t\t\tresidence_name: that.userInfo.residence_name || \"\"\n\t\t\t};\n\t\t\t\n\t\t\tconsole.log('提交的用户数据:', JSON.stringify(submitData));\n\t\t\tconsole.log('包含的位置信息:');\n\t\t\tconsole.log('- residence_lat:', submitData.residence_lat);\n\t\t\tconsole.log('- residence_lng:', submitData.residence_lng);\n\t\t\tconsole.log('- residence_name:', submitData.residence_name);\n\t\t\t\n\t\t\t// 使用单一接口更新所有信息\n\t\t\tupdateUserSocialInfo(submitData).then(res => {\n\t\t\t\tconsole.log('保存用户信息结果:', JSON.stringify(res));\n\t\t\t\t\n\t\t\t\t// 检查是否有标签需要更新\n\t\t\t\tif (that.selectedTagIds && that.selectedTagIds.length > 0) {\n\t\t\t\t\t// 提交标签数据\n\t\t\t\t\treturn updateUserTags(that.selectedTagIds).then(tagRes => {\n\t\t\t\t\t\tconsole.log('标签更新结果:', JSON.stringify(tagRes));\n\t\t\t\t\t\treturn { userResult: res, tagResult: tagRes };\n\t\t\t\t\t}).catch(tagErr => {\n\t\t\t\t\t\tconsole.error('标签更新失败:', tagErr);\n\t\t\t\t\t\treturn { userResult: res, tagError: tagErr };\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 没有标签需要更新\n\t\t\t\t\treturn { userResult: res };\n\t\t\t\t}\n\t\t\t}).then(results => {\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\t// 检查用户信息更新结果\n\t\t\t\tif (results.userResult && (results.userResult.code == 200 || results.userResult.status == 200)) {\n\t\t\t\t\t// 检查标签更新结果\n\t\t\t\t\tif (results.tagResult && (results.tagResult.code != 200 && results.tagResult.status != 200)) {\n\t\t\t\t\t\tthat.opTipsPopup('用户信息已保存，但标签更新失败');\n\t\t\t\t\t} else if (results.tagError) {\n\t\t\t\t\t\tthat.opTipsPopup('用户信息已保存，但标签更新出错');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthat.opTipsPopup('所有信息保存成功，位置信息已更新');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 清空标签ID缓存\n\t\t\t\t\tthat.selectedTagIds = [];\n\t\t\t\t\t\n\t\t\t\t\t// 保存成功后，更新originalUserInfo以便与编辑中的数据比较\n\t\t\t\t\tthat.originalUserInfo = JSON.parse(JSON.stringify(that.userInfo));\n\t\t\t\t\t\n\t\t\t\t\t// 更新本地存储的用户信息\n\t\t\t\t\tlet cachedUserInfo = that.getUserInfoFromCache();\n\t\t\t\t\tcachedUserInfo = {...cachedUserInfo, ...that.userInfo};\n\t\t\t\t\tuni.setStorageSync('USER_INFO', cachedUserInfo);\n\t\t\t\t\t\n\t\t\t\t\t// 保存成功后，可以尝试刷新IP属地信息\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthat.refreshIpClick();\n\t\t\t\t\t}, 1000);\n\t\t\t\t} else {\n\t\t\t\t\tthat.opTipsPopup(results.userResult?.msg || '保存失败，请稍后重试');\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tconsole.error('保存失败:', err);\n\t\t\t\tthat.opTipsPopup('保存失败: ' + (typeof err === 'string' ? err : '网络错误'));\n\t\t\t});\n\t\t},\n\t\t\n\t\tgenderPopupClick(isOpen) {\n\t\t\tif (isOpen) {\n\t\t\t\tthis.tempGender = this.userInfo.sex; // 初始化临时选择为当前性别\n\t\t\t\tthis.$refs.genderPopup.open();\n\t\t\t} else {\n\t\t\t\tthis.$refs.genderPopup.close();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 点击年龄项，临时选择\n\t\tageItemClick(age) {\n\t\t\tthis.tempAge = age;\n\t\t},\n\t\t\n\t\t// 确认选择年龄\n\t\tconfirmAge() {\n\t\t\tif (this.tempAge) {\n\t\t\t\tthis.userInfo.age = this.tempAge;\n\t\t\t\tthis.tempAge = null;\n\t\t\t}\n\t\t\tthis.$refs.agePopup.close();\n\t\t},\n\t\t\n\t\t// 年龄选择弹窗\n\t\tagePopupClick(isOpen) {\n\t\t\tconst that = this;\n\t\t\tif (isOpen) {\n\t\t\t\t// 先显示加载中\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载兴趣标签中...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 强制重新加载标签数据\n\t\t\t\tthat.tagCategories = []; // 清空已有数据，强制重新获取\n\t\t\t\t\n\t\t\t\t// 使用API获取标签数据\n\t\t\t\tgetTagsWithCategories().then(function(res) {\n\t\t\t\t\tconsole.log('兴趣标签接口原始响应:', JSON.stringify(res));\n\t\t\t\t\t\n\t\t\t\t\t// 检查响应是否成功\n\t\t\t\t\tconst isSuccess = res.code == 200 || res.status == 200 || res.msg === 'success';\n\t\t\t\t\t\n\t\t\t\t\tif (isSuccess && res.data) {\n\t\t\t\t\t\t// 检查数据是否为空数组\n\t\t\t\t\t\tif (Array.isArray(res.data) && res.data.length === 0) {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tthat.opTipsPopup('暂无标签数据，请联系管理员添加标签');\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 使用处理方法处理API返回的数据\n\t\t\t\t\t\tthat.processTagsData(res)\n\t\t\t\t\t\t\t.then(() => {\n\t\t\t\t\t\t\t\t// 加载完成后初始化已选标签\n\t\t\t\t\t\t\t\tthat.initializeSelectedTags();\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tthat.$refs.agePopup.open();\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\tconsole.error('处理标签数据失败:', err);\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tthat.opTipsPopup('标签数据处理失败: ' + (err.message || '未知错误'));\n\t\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('加载标签失败:', res.msg || '未知错误');\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tthat.opTipsPopup('标签加载失败: ' + (res.msg || '未知错误'));\n\t\t\t\t\t}\n\t\t\t\t}).catch((err) => {\n\t\t\t\t\tconsole.error('加载兴趣标签错误:', err);\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthat.opTipsPopup('网络错误，请稍后重试');\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tthat.$refs.agePopup.close();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 处理标签数据的新方法，可以处理任何格式的响应\n\t\tprocessTagsData(response) {\n\t\t\tconst that = this;\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('处理标签数据开始:', JSON.stringify(response));\n\t\t\t\t\t\n\t\t\t\t\t// 检查响应格式\n\t\t\t\t\tif (!response || typeof response !== 'object') {\n\t\t\t\t\t\tconsole.error('响应数据不是对象');\n\t\t\t\t\t\treject(new Error('响应数据不是对象'));\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 获取数据部分\n\t\t\t\t\tlet data = response.data;\n\t\t\t\t\t\n\t\t\t\t\t// 确保数据是数组\n\t\t\t\t\tif (!data || !Array.isArray(data)) {\n\t\t\t\t\t\tconsole.error('响应数据格式不正确，data不是数组');\n\t\t\t\t\t\treject(new Error('响应数据格式不正确，data不是数组'));\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 检查数组是否为空\n\t\t\t\t\tif (data.length === 0) {\n\t\t\t\t\t\tconsole.error('API返回的标签数据为空数组');\n\t\t\t\t\t\treject(new Error('API返回的标签数据为空数组'));\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 初始化标签名到ID的映射\n\t\t\t\t\tthat.tagNameToIdMap = {};\n\t\t\t\t\t\n\t\t\t\t\t// 处理分类数据\n\t\t\t\t\tthat.tagCategories = data.map(category => {\n\t\t\t\t\t\tif (!category || typeof category !== 'object') {\n\t\t\t\t\t\t\tconsole.error('分类数据无效:', category);\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\tname: '未知分类',\n\t\t\t\t\t\t\t\tid: 0,\n\t\t\t\t\t\t\t\ttags: [],\n\t\t\t\t\t\t\t\ttagsData: []\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 确保标签数组存在\n\t\t\t\t\t\tconst categoryTags = Array.isArray(category.tags) ? category.tags : [];\n\t\t\t\t\t\tconsole.log(`分类[${category.name}]的原始标签数据:`, JSON.stringify(categoryTags));\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 从标签对象中提取名称列表\n\t\t\t\t\t\tconst tagNames = categoryTags.map(tag => {\n\t\t\t\t\t\t\tif (tag && typeof tag === 'object' && tag.name) {\n\t\t\t\t\t\t\t\t// 同时更新映射\n\t\t\t\t\t\t\t\tif (tag.id) {\n\t\t\t\t\t\t\t\t\tthat.tagNameToIdMap[tag.name] = tag.id;\n\t\t\t\t\t\t\t\t\tconsole.log(`添加API返回的标签映射: ${tag.name} => ${tag.id}`);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn tag.name;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tconsole.warn('发现无效标签:', tag);\n\t\t\t\t\t\t\treturn null;\n\t\t\t\t\t\t}).filter(name => name !== null);\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log(`分类[${category.name}]的标签名称:`, tagNames.join(', ') || '无标签');\n\t\t\t\t\t\t\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tname: category.name || '未命名分类',\n\t\t\t\t\t\t\tid: category.id || 0,\n\t\t\t\t\t\t\ttags: tagNames,       // 标签名称数组用于UI显示\n\t\t\t\t\t\t\ttagsData: categoryTags // 保存原始标签数据包含ID\n\t\t\t\t\t\t};\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('API返回的标签分类初步处理结果:', JSON.stringify(that.tagCategories));\n\t\t\t\t\t\n\t\t\t\t\t// 移除空标签的分类（可选，根据需要决定是否保留空分类）\n\t\t\t\t\tconst allCategories = [...that.tagCategories]; // 保存完整数据\n\t\t\t\t\tthat.tagCategories = that.tagCategories.filter(category => \n\t\t\t\t\t\tcategory.tags && category.tags.length > 0\n\t\t\t\t\t);\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('过滤后的标签分类结构:', JSON.stringify(that.tagCategories));\n\t\t\t\t\tconsole.log('API返回的标签ID映射:', that.tagNameToIdMap);\n\t\t\t\t\t\n\t\t\t\t\tif (that.tagCategories.length === 0) {\n\t\t\t\t\t\tconsole.warn('所有分类都没有标签，尝试使用原始分类');\n\t\t\t\t\t\t// 如果过滤后没有分类了，使用原始分类\n\t\t\t\t\t\tif (allCategories.length > 0) {\n\t\t\t\t\t\t\tthat.tagCategories = allCategories;\n\t\t\t\t\t\t\tconsole.log('使用完整分类(含空标签分类):', JSON.stringify(that.tagCategories));\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconsole.warn('API返回的原始分类也为空');\n\t\t\t\t\t\t\treject(new Error('API返回的分类数据无效'));\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 加载我的标签\n\t\t\t\t\tthat.loadMyTags();\n\t\t\t\t\tconsole.log('API标签数据处理完成');\n\t\t\t\t\tresolve();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('处理API标签数据时出错:', error);\n\t\t\t\t\treject(error);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 初始化已选标签\n\t\tinitializeSelectedTags() {\n\t\t\t// 如果已有年龄标签，初始化为已选中状态\n\t\t\tif (this.userInfo.age) {\n\t\t\t\tthis.selectedTags = this.userInfo.age.split(', ').filter(tag => tag);\n\t\t\t\tconsole.log('从用户信息初始化已选标签:', this.selectedTags);\n\t\t\t} else {\n\t\t\t\tthis.selectedTags = [];\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载标签分类\n\t\tloadTagCategories() {\n\t\t\tconst that = this;\n\t\t\tconsole.log('开始加载兴趣标签分类和列表');\n\t\t\t\n\t\t\t// 返回Promise以便外部等待加载完成\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t// 使用新的合并接口\n\t\t\t\tgetTagsWithCategories().then(function(res) {\n\t\t\t\t\tconsole.log('兴趣标签接口原始响应:', JSON.stringify(res));\n\t\t\t\t\t\n\t\t\t\t\t// 检查响应是否成功 - 增加更多成功状态的检查\n\t\t\t\t\tconst isSuccess = res.code == 200 || res.status == 200 || res.msg === 'success';\n\t\t\t\t\t\n\t\t\t\t\tif (isSuccess) {\n\t\t\t\t\t\t// 检查数据是否存在\n\t\t\t\t\t\tif (!res.data) {\n\t\t\t\t\t\t\tconsole.log('API响应成功但数据为空');\n\t\t\t\t\t\t\tthat.opTipsPopup('标签数据为空');\n\t\t\t\t\t\t\tresolve([]); // 空数据视为成功但无数据\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查数据是否为空数组\n\t\t\t\t\t\tif (Array.isArray(res.data) && res.data.length === 0) {\n\t\t\t\t\t\t\tconsole.log('API返回的标签数据为空数组');\n\t\t\t\t\t\t\tthat.opTipsPopup('暂无标签数据');\n\t\t\t\t\t\t\tresolve([]); // 空数组视为成功但无数据\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 使用新的处理方法\n\t\t\t\t\t\tthat.processTagsData(res)\n\t\t\t\t\t\t\t.then(resolve)\n\t\t\t\t\t\t\t.catch(error => {\n\t\t\t\t\t\t\t\tconsole.error('处理标签数据出错:', error);\n\t\t\t\t\t\t\t\tthat.opTipsPopup('标签数据格式错误');\n\t\t\t\t\t\t\t\tresolve([]); // 数据处理错误视为成功但无有效数据\n\t\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('加载标签分类和列表失败:', res.msg || '未知错误');\n\t\t\t\t\t\tthat.opTipsPopup('标签数据加载失败: ' + (res.msg || '未知错误'));\n\t\t\t\t\t\treject(new Error(res.msg || '加载标签失败'));\n\t\t\t\t\t}\n\t\t\t\t}).catch((err) => {\n\t\t\t\t\tconsole.error('加载兴趣标签分类和列表错误:', err);\n\t\t\t\t\tthat.opTipsPopup('网络错误，请稍后重试');\n\t\t\t\t\treject(err);\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 加载标签列表\n\t\tloadTagsList() {\n\t\t\tconst that = this;\n\t\t\tconsole.log('开始加载兴趣标签列表');\n\t\t\t\n\t\t\t// 初始化标签名到ID的映射\n\t\t\tthat.tagNameToIdMap = {};\n\t\t\t\n\t\t\t// 使用API加载标签列表\n\t\t\tgetTagsWithCategories().then(function(res) {\n\t\t\t\tif (res.code == 200 && res.data) {\n\t\t\t\t\tthat.processTagsData(res)\n\t\t\t\t\t\t.then(() => {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tconsole.log('标签列表加载完成');\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\tconsole.error('标签数据处理错误:', err);\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tthat.opTipsPopup('标签数据处理错误');\n\t\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tconsole.error('获取标签列表失败:', res.msg);\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthat.opTipsPopup('获取标签失败: ' + (res.msg || '未知错误'));\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('获取标签列表请求错误:', err);\n\t\t\t\tuni.hideLoading();\n\t\t\t\tthat.opTipsPopup('网络错误，请稍后重试');\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 加载我的标签\n\t\tasync loadMyTags() {\n\t\t\ttry {\n\t\t\t\tconst res = await getMyTags();\n\t\t\t\tconsole.log('获取我的标签返回数据:', JSON.stringify(res));\n\t\t\t\t\n\t\t\t\tif (res.status === 200 && res.data) {\n\t\t\t\t\t// 处理标签数据\n\t\t\t\t\tthis.myTags = res.data;\n\t\t\t\t\t// 更新选中的标签\n\t\t\t\t\tthis.selectedTags = res.data.map(tag => tag.name);\n\t\t\t\t\tthis.selectedNewTags = [...this.selectedTags];\n\t\t\t\t\t\n\t\t\t\t\t// 更新userInfo中的标签\n\t\t\t\t\tthis.userInfo.interest_tags = this.selectedTags;\n\t\t\t\t\t\n\t\t\t\t\treturn res.data;\n\t\t\t\t}\n\t\t\t\treturn [];\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载我的标签失败:', error);\n\t\t\t\treturn [];\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 根据星座值获取星座名称\n\t\tgetConstellationName(value) {\n\t\t\tconst constellations = [\n\t\t\t\t'水瓶座', '双鱼座', '白羊座', '金牛座', \n\t\t\t\t'双子座', '巨蟹座', '狮子座', '处女座', \n\t\t\t\t'天秤座', '天蝎座', '射手座', '摩羯座'\n\t\t\t];\n\t\t\t\n\t\t\tif (value !== null && value >= 0 && value < constellations.length) {\n\t\t\t\treturn constellations[value];\n\t\t\t}\n\t\t\treturn '未知';\n\t\t},\n\t\t\n\t\t// 表单提交\n\t\tformSubmit(e) {\n\t\t\tlet that = this;\n\t\t\tlet value = e.detail.value;\n\t\t\t\n\t\t\tif (!value.nickname) {\n\t\t\t\treturn that.opTipsPopup('请输入昵称');\n\t\t\t}\n\t\t\t\n\t\t\t// 更新用户信息\n\t\t\tthat.userInfo.nickname = value.nickname;\n\t\t\t\n\t\t\t// 保存全部修改\n\t\t\tthat.saveAllChanges();\n\t\t},\n\t\t\n\t\t/**\n\t\t * 创建默认的标签数据并返回，但不设置到tagCategories\n\t\t * @param {Boolean} returnOnly - 是否仅返回数据而不设置到全局\n\t\t * @return {Array} 默认标签数据数组\n\t\t */\n\t\t// 已删除createDefaultTagData方法\n\n\t\t/**\n\t\t * 创建默认的标签数据\n\t\t * 当接口加载失败时使用此方法\n\t\t */\n\t\t// 已删除createDefaultTagsData方法\n\t\t\n\t\t// 生日相关方法\n\t\tbirthdayPopupClick(isOpen) {\n\t\t\tconst that = this;\n\t\t\tif (isOpen) {\n\t\t\t\t// 初始化生日选择器\n\t\t\t\tthat.initBirthdayPicker();\n\t\t\t\tthat.$refs.birthdayPopup.open();\n\t\t\t} else {\n\t\t\t\tthat.$refs.birthdayPopup.close();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 初始化生日选择器\n\t\tinitBirthdayPicker() {\n\t\t\t// 如果用户已有生日，根据已有生日设置选择器\n\t\t\tif (this.userInfo.birthday) {\n\t\t\t\ttry {\n\t\t\t\t\tlet year, month, day;\n\t\t\t\t\t\n\t\t\t\t\t// 检查是否为时间戳格式\n\t\t\t\t\tif (typeof this.userInfo.birthday === 'number' || \n\t\t\t\t\t\t(typeof this.userInfo.birthday === 'string' && !isNaN(this.userInfo.birthday) && !this.userInfo.birthday.includes('-'))) {\n\t\t\t\t\t\t// 将时间戳转换为日期对象（假设是秒级时间戳）\n\t\t\t\t\t\tconst timestamp = parseInt(this.userInfo.birthday);\n\t\t\t\t\t\tconst date = new Date(timestamp * 1000);\n\t\t\t\t\t\t\n\t\t\t\t\t\tyear = date.getFullYear();\n\t\t\t\t\t\tmonth = date.getMonth() + 1; // 月份从0开始，需要加1\n\t\t\t\t\t\tday = date.getDate();\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 解析日期字符串 \"YYYY-MM-DD\"\n\t\t\t\t\t\tconst parts = this.userInfo.birthday.split('-');\n\t\t\t\t\t\tif (parts.length !== 3) return;\n\t\t\t\t\t\t\n\t\t\t\t\t\tyear = parseInt(parts[0]);\n\t\t\t\t\t\tmonth = parseInt(parts[1]);\n\t\t\t\t\t\tday = parseInt(parts[2]);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 找到对应的索引\n\t\t\t\t\tconst yearIndex = this.years.findIndex(y => y === year);\n\t\t\t\t\tconst monthIndex = this.months.findIndex(m => m === month);\n\t\t\t\t\tconst dayIndex = this.days.findIndex(d => d === day);\n\t\t\t\t\t\n\t\t\t\t\t// 更新选择器值\n\t\t\t\t\tif (yearIndex !== -1 && monthIndex !== -1 && dayIndex !== -1) {\n\t\t\t\t\t\tthis.birthdayPickerValue = [yearIndex, monthIndex, dayIndex];\n\t\t\t\t\t\tconsole.log('初始化生日选择器:', this.birthdayPickerValue, `(${year}-${month}-${day})`);\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('初始化生日选择器出错:', e);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\tbirthdayPickerChange(e) {\n\t\t\tthis.birthdayPickerValue = e.detail.value;\n\t\t},\n\t\t\n\t\t// 从生日字符串计算星座\n\t\tcalculateConstellationFromBirthday() {\n\t\t\tif (!this.userInfo.birthday) return;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tlet month, day;\n\t\t\t\t\n\t\t\t\t// 检查是否为时间戳格式\n\t\t\t\tif (typeof this.userInfo.birthday === 'number' || \n\t\t\t\t\t(typeof this.userInfo.birthday === 'string' && !isNaN(this.userInfo.birthday) && !this.userInfo.birthday.includes('-'))) {\n\t\t\t\t\t// 将时间戳转换为日期对象（假设是秒级时间戳）\n\t\t\t\t\tconst timestamp = parseInt(this.userInfo.birthday);\n\t\t\t\t\tconst date = new Date(timestamp * 1000);\n\t\t\t\t\t\n\t\t\t\t\tmonth = date.getMonth() + 1; // 月份从0开始，需要加1\n\t\t\t\t\tday = date.getDate();\n\t\t\t\t\t\n\t\t\t\t\tconsole.log(`从时间戳 ${this.userInfo.birthday} 解析出生日: ${month}月${day}日`);\n\t\t\t\t} else {\n\t\t\t\t\t// 解析日期字符串 \"YYYY-MM-DD\"\n\t\t\t\t\tconst parts = this.userInfo.birthday.split('-');\n\t\t\t\t\tif (parts.length !== 3) return;\n\t\t\t\t\t\n\t\t\t\t\tmonth = parseInt(parts[1], 10);\n\t\t\t\t\tday = parseInt(parts[2], 10);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 计算星座\n\t\t\t\tconst constellation = this.calculateConstellation(month, day);\n\t\t\t\tthis.userInfo.constellation = constellation.value;\n\t\t\t\t\n\t\t\t\tconsole.log('从生日计算星座:', constellation.name, '(值:', constellation.value, ')');\n\t\t\t\t\n\t\t\t\t// 如果星座不存在，或与生日不匹配，更新到服务器\n\t\t\t\tif (this.originalUserInfo.constellation === null || \n\t\t\t\t\tthis.originalUserInfo.constellation !== constellation.value) {\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('更新星座信息到服务器');\n\t\t\t\t\t\n\t\t\t\t\t// 准备提交的数据\n\t\t\t\t\tconst submitData = {\n\t\t\t\t\t\tconstellation: constellation.value\n\t\t\t\t\t};\n\t\t\t\t\t\n\t\t\t\t\t// 调用更新接口\n\t\t\t\t\tupdateUserSocialInfo(submitData).then(res => {\n\t\t\t\t\t\tconsole.log('星座更新结果:', JSON.stringify(res));\n\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\tconsole.error('星座更新错误:', err);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('计算星座时出错:', e);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 计算星座\n\t\tcalculateConstellation(month, day) {\n\t\t\t// 星座日期边界\n\t\t\tconst constellationDates = [\n\t\t\t\t{ name: '水瓶座', start: { month: 1, day: 20 }, end: { month: 2, day: 18 } },\n\t\t\t\t{ name: '双鱼座', start: { month: 2, day: 19 }, end: { month: 3, day: 20 } },\n\t\t\t\t{ name: '白羊座', start: { month: 3, day: 21 }, end: { month: 4, day: 19 } },\n\t\t\t\t{ name: '金牛座', start: { month: 4, day: 20 }, end: { month: 5, day: 20 } },\n\t\t\t\t{ name: '双子座', start: { month: 5, day: 21 }, end: { month: 6, day: 21 } },\n\t\t\t\t{ name: '巨蟹座', start: { month: 6, day: 22 }, end: { month: 7, day: 22 } },\n\t\t\t\t{ name: '狮子座', start: { month: 7, day: 23 }, end: { month: 8, day: 22 } },\n\t\t\t\t{ name: '处女座', start: { month: 8, day: 23 }, end: { month: 9, day: 22 } },\n\t\t\t\t{ name: '天秤座', start: { month: 9, day: 23 }, end: { month: 10, day: 23 } },\n\t\t\t\t{ name: '天蝎座', start: { month: 10, day: 24 }, end: { month: 11, day: 22 } },\n\t\t\t\t{ name: '射手座', start: { month: 11, day: 23 }, end: { month: 12, day: 21 } },\n\t\t\t\t{ name: '摩羯座', start: { month: 12, day: 22 }, end: { month: 1, day: 19 } }\n\t\t\t];\n\t\t\t\n\t\t\t// 循环判断日期所属星座\n\t\t\tfor (let i = 0; i < constellationDates.length; i++) {\n\t\t\t\tconst constellation = constellationDates[i];\n\t\t\t\t\n\t\t\t\t// 特殊处理摩羯座（跨年）\n\t\t\t\tif (i === 11) {\n\t\t\t\t\tif ((month === 12 && day >= constellation.start.day) || \n\t\t\t\t\t\t(month === 1 && day <= constellation.end.day)) {\n\t\t\t\t\t\treturn { name: constellation.name, value: i };\n\t\t\t\t\t}\n\t\t\t\t} \n\t\t\t\t// 处理其他星座\n\t\t\t\telse if ((month === constellation.start.month && day >= constellation.start.day) || \n\t\t\t\t\t\t(month === constellation.end.month && day <= constellation.end.day)) {\n\t\t\t\t\treturn { name: constellation.name, value: i };\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 默认返回水瓶座（异常情况）\n\t\t\treturn { name: '水瓶座', value: 0 };\n\t\t},\n\t\t\n\t\t// 确认生日选择\n\t\tconfirmBirthday() {\n\t\t\tconst that = this;\n\t\t\tconst year = this.years[this.birthdayPickerValue[0]];\n\t\t\tconst month = this.months[this.birthdayPickerValue[1]];\n\t\t\tconst day = this.days[this.birthdayPickerValue[2]];\n\t\t\t\n\t\t\t// 创建日期对象\n\t\t\tconst birthdayDate = new Date(year, month - 1, day); // 月份需要减1\n\t\t\t\n\t\t\t// 转换为时间戳（秒级）\n\t\t\tconst birthdayTimestamp = Math.floor(birthdayDate.getTime() / 1000);\n\t\t\t\n\t\t\t// 格式化为YYYY-MM-DD用于显示\n\t\t\tconst formattedBirthday = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;\n\t\t\t\n\t\t\t// 计算星座并更新\n\t\t\tconst constellation = this.calculateConstellation(month, day);\n\t\t\t\n\t\t\t// 更新本地用户信息（保存时间戳格式）\n\t\t\tthat.userInfo.birthday = birthdayTimestamp;\n\t\t\tthat.userInfo.constellation = constellation.value;\n\t\t\t\n\t\t\t\n\t\t\t// 关闭弹窗\n\t\t\tthis.$refs.birthdayPopup.close();\n\t\t},\n\t\t\n\t\t// 绑定手机号\n\t\tbindMobileClick(e) {\n\t\t\tlet that = this;\n\t\t\tif (e.detail.errMsg == 'getPhoneNumber:ok') {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: that.userInfo.phone ? '换绑授权中...' : '绑定授权中...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 使用实际的绑定手机号API\n\t\t\t\tthat.$util.userBindingPhone(e.detail).then(res => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t\t// 更新用户信息\n\t\t\t\t\t\tthat.userInfo.phone = res.data.phone || res.data.mobile;\n\t\t\t\t\t\t// 提示成功\n\t\t\t\t\t\tthat.opTipsPopup(res.msg || '手机号绑定成功');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthat.opTipsPopup(res.msg || '绑定失败');\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthat.opTipsPopup('绑定失败: ' + (typeof err === 'string' ? err : '网络错误'));\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 刷新IP属地\n\t\trefreshIpClick() {\n\t\t\tlet that = this;\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '刷新中...',\n\t\t\t\tmask: true\n\t\t\t});\n\t\t\t\n\t\t\t// 先尝试重新获取位置信息\n\t\t\tconst cachedLatitude = uni.getStorageSync('residence_lat');\n\t\t\tconst cachedLongitude = uni.getStorageSync('residence_lng');\n\t\t\t\n\t\t\tif (cachedLatitude && cachedLongitude) {\n\t\t\t\t// 如果有缓存的位置，重新进行逆地理编码\n\t\t\t\tthat.getAddressFromLocation(parseFloat(cachedLatitude), parseFloat(cachedLongitude));\n\t\t\t\tuni.hideLoading();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 如果没有位置缓存，先获取位置再刷新\n\t\t\tthat.getUserLocation();\n\t\t\t\n\t\t\t// 同时调用后端接口刷新IP属地\n\t\t\tgetUserSocialInfo().then(function(res) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t// 更新用户信息\n\t\t\t\t\tconst userData = res.data;\n\t\t\t\t\tuni.setStorageSync('USER_INFO', userData);\n\t\t\t\t\t\n\t\t\t\t\t// 如果后端返回了位置信息，使用后端的\n\t\t\t\t\tif (userData.residence_name) {\n\t\t\t\t\t\tthat.userInfo.residence_name = userData.residence_name;\n\t\t\t\t\t\tthat.userInfo.province = userData.residence_name;\n\t\t\t\t\t}\n\t\t\t\t\tif (userData.province && !userData.residence_name) {\n\t\t\t\t\t\tthat.userInfo.province = userData.province;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthat.originalUserInfo = JSON.parse(JSON.stringify(userData));\n\t\t\t\t\tthat.opTipsPopup('IP属地刷新成功');\n\t\t\t\t} else {\n\t\t\t\t\tthat.opTipsPopup(res.msg || 'IP属地刷新失败');\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tthat.opTipsPopup('刷新失败: ' + (typeof err === 'string' ? err : '网络错误'));\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 获取实名认证状态文本\n\t\tgetAuthStatusText() {\n\t\t\tswitch (this.userInfo.auth_status) {\n\t\t\t\tcase 0: return '未认证';\n\t\t\t\tcase 1: return '审核中';\n\t\t\t\tcase 2: return '已认证';\n\t\t\t\tcase 3: return '认证失败';\n\t\t\t\tdefault: return '未认证';\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 跳转到实名认证页面\n\t\tgoToRealAuth() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/setting/realname'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 显示提示弹窗\n\t\topTipsPopup(msg, duration = 2000) {\n\t\t\tlet that = this;\n\t\t\tthat.tipsTitle = msg;\n\t\t\tthat.$refs.tipsPopup.open();\n\t\t\tsetTimeout(function() {\n\t\t\t\tthat.$refs.tipsPopup.close();\n\t\t\t}, duration);\n\t\t},\n\t\t\n\t\t// 点击性别项，临时选择\n\t\tgenderItemClick(gender) {\n\t\t\tthis.tempGender = gender;\n\t\t},\n\t\t\n\t\t// 确认选择性别\n\t\tconfirmGender() {\n\t\t\tif (this.tempGender !== null) {\n\t\t\t\t// 只更新本地状态，不提交到服务器\n\t\t\t\tthis.userInfo.sex = this.tempGender;\n\t\t\t\tthis.opTipsPopup('性别选择已更新，点击保存按钮生效');\n\t\t\t}\n\t\t\tthis.$refs.genderPopup.close();\n\t\t},\n\t\t\n\t\t// 切换标签\n\t\ttoggleTag(tag) {\n\t\t\tconsole.log('切换标签:', tag);\n\t\t\tconsole.log('当前已选择标签:', JSON.stringify(this.selectedTags));\n\t\t\t\n\t\t\tif (!tag) {\n\t\t\t\tconsole.error('无效的标签名称');\n\t\t\t\tthis.opTipsPopup('标签无效');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查标签是否存在于映射中\n\t\t\tconst tagId = this.getTagIdByName(tag);\n\t\t\tconsole.log('标签ID:', tagId);\n\t\t\t\n\t\t\tif (this.selectedTags.includes(tag)) {\n\t\t\t\tthis.selectedTags = this.selectedTags.filter(t => t !== tag);\n\t\t\t\tconsole.log('取消选择后的标签:', JSON.stringify(this.selectedTags));\n\t\t\t\tthis.opTipsPopup(`已取消选择 ${tag}`);\n\t\t\t} else if (this.selectedTags.length < 5) {\n\t\t\t\tthis.selectedTags.push(tag);\n\t\t\t\tconsole.log('选择后的标签:', JSON.stringify(this.selectedTags));\n\t\t\t\tthis.opTipsPopup(`已选择 ${tag}`);\n\t\t\t} else {\n\t\t\t\tthis.opTipsPopup('最多只能选择5个兴趣标签');\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 切换标签分类\n\t\tswitchCategory(index) {\n\t\t\tthis.currentCategoryIndex = index;\n\t\t},\n\t\t\n\t\t// 滑动切换分类\n\t\tswiperChange(e) {\n\t\t\tthis.currentCategoryIndex = e.detail.current;\n\t\t},\n\t\t\n\t\t// 确认选择标签\n\t\tconfirmTags() {\n\t\t\tconst that = this;\n\t\t\tif (that.selectedTags.length > 5) {\n\t\t\t\tthat.opTipsPopup('最多只能选择5个兴趣标签');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 保存标签到用户信息\n\t\t\tthat.userInfo.age = that.selectedTags.join(', ');\n\t\t\t\n\t\t\t// 获取标签ID列表\n\t\t\tconst tagIds = [];\n\t\t\tconst tagNames = [];\n\t\t\t\n\t\t\t// 尝试从选择的标签名称获取ID\n\t\t\tfor (const tagName of that.selectedTags) {\n\t\t\t\tconst tagId = that.getTagIdByName(tagName);\n\t\t\t\tif (tagId) {\n\t\t\t\t\ttagIds.push(tagId);\n\t\t\t\t\ttagNames.push(tagName);\n\t\t\t\t\tconsole.log(`成功添加标签 \"${tagName}\" 的ID: ${tagId}`);\n\t\t\t\t} else {\n\t\t\t\t\tconsole.warn(`无法找到标签 \"${tagName}\" 的ID`);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tconsole.log('选中的标签:', tagNames);\n\t\t\tconsole.log('标签ID列表:', tagIds);\n\t\t\t\n\t\t\tif (tagIds.length === 0 && that.selectedTags.length > 0) {\n\t\t\t\tconsole.error('无法获取任何标签ID，可能是数据结构问题');\n\t\t\t\tthat.opTipsPopup('标签数据异常，请稍后重试');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 调用更新标签接口\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '保存兴趣标签中...',\n\t\t\t\tmask: true\n\t\t\t});\n\t\t\t\n\t\t\tupdateUserTags(tagIds).then(function(res) {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\tif (res.code == 200) {\n\t\t\t\t\t// 显示保存成功提示\n\t\t\t\t\tthat.opTipsPopup('兴趣标签更新成功', 3000);\n\t\t\t\t\t\n\t\t\t\t\t// 刷新用户信息\n\t\t\t\t\tgetUserSocialInfo().then(function(result) {\n\t\t\t\t\t\tif (result.code == 200) {\n\t\t\t\t\t\t\tconsole.log('刷新用户信息成功:', result.data);\n\t\t\t\t\t\t\tuni.setStorageSync('USER_INFO', result.data);\n\t\t\t\t\t\t\tthat.userInfo = result.data;\n\t\t\t\t\t\t\tthat.originalUserInfo = JSON.parse(JSON.stringify(result.data));\n\t\t\t\t\t\t\tthat.handleBackgroundImages();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tconsole.error('标签更新失败:', res);\n\t\t\t\t\tthat.opTipsPopup('标签更新失败: ' + (res.msg || ''));\n\t\t\t\t}\n\t\t\t}).catch((err) => {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tconsole.error('标签更新错误:', err);\n\t\t\t\tthat.opTipsPopup('标签更新失败: 网络错误');\n\t\t\t});\n\t\t\t\n\t\t\tthat.$refs.agePopup.close();\n\t\t},\n\t\t\n\t\t// 根据标签名获取标签ID\n\t\tgetTagIdByName(tagName) {\n\t\t\tif (!tagName) {\n\t\t\t\tconsole.error('标签名为空');\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\t\n\t\t\tconsole.log('查找标签ID，标签名:', tagName);\n\t\t\t\n\t\t\t// 先从映射中查找\n\t\t\tif (this.tagNameToIdMap && this.tagNameToIdMap[tagName]) {\n\t\t\t\tconsole.log('从映射中找到ID:', this.tagNameToIdMap[tagName]);\n\t\t\t\treturn this.tagNameToIdMap[tagName];\n\t\t\t}\n\t\t\t\n\t\t\t// 如果映射中没有，遍历所有分类查找\n\t\t\tfor (const category of this.tagCategories) {\n\t\t\t\tif (category.tagsData && Array.isArray(category.tagsData)) {\n\t\t\t\t\tfor (const tag of category.tagsData) {\n\t\t\t\t\t\tif (tag && tag.name === tagName) {\n\t\t\t\t\t\t\t// 找到了标签，更新映射并返回ID\n\t\t\t\t\t\t\tthis.tagNameToIdMap[tagName] = tag.id;\n\t\t\t\t\t\t\tconsole.log('在分类中找到ID:', tag.id);\n\t\t\t\t\t\t\treturn tag.id;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 如果找不到匹配的标签ID，打印警告并返回null\n\t\t\tconsole.warn(`未找到标签 \"${tagName}\" 的ID`);\n\t\t\treturn null;\n\t\t},\n\t\t\n\t\tpreviewImage(url) {\n\t\t\t// 实现图片预览功能\n\t\t\tif (!url) return;\n\t\t\t\n\t\t\tuni.previewImage({\n\t\t\t\turls: [url],\n\t\t\t\tcurrent: url\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 显示照片长按菜单\n\t\tshowPhotoMenu(photo, index) {\n\t\t\t// 实现照片长按菜单功能\n\t\t\tif (!photo) return;\n\t\t\t\n\t\t\t// 确定是否为头像\n\t\t\tconst isAvatar = index === -1 || photo.type === '头像';\n\t\t\tconst menuItems = isAvatar ? ['查看大图', '更换头像'] : ['查看大图', '删除', '设为头像'];\n\t\t\t\n\t\t\tuni.showActionSheet({\n\t\t\t\titemList: menuItems,\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (isAvatar) {\n\t\t\t\t\t\tswitch(res.tapIndex) {\n\t\t\t\t\t\t\tcase 0: // 查看大图\n\t\t\t\t\t\t\t\tthis.previewImage(photo.url);\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 1: // 更换头像\n\t\t\t\t\t\t\t\tthis.changeProfilePhoto();\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tswitch(res.tapIndex) {\n\t\t\t\t\t\t\tcase 0: // 查看大图\n\t\t\t\t\t\t\t\tthis.previewImage(photo.url);\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 1: // 删除\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\t\t\t\t\tcontent: '确定要删除这张照片吗？',\n\t\t\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\t\t\t\tthis.backgroundImages.splice(index, 1);\n\t\t\t\t\t\t\t\t\t\t\tthis.opTipsPopup('照片已删除');\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 2: // 设为头像\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '设为头像',\n\t\t\t\t\t\t\t\t\tcontent: '确定要将此照片设为头像吗？',\n\t\t\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\t\t\t\t// 保存原头像URL\n\t\t\t\t\t\t\t\t\t\t\tconst oldAvatar = this.userInfo.avatar;\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t// 将当前照片设为头像\n\t\t\t\t\t\t\t\t\t\t\tthis.userInfo.avatar = photo.url;\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t// 如果原头像存在且不是默认头像，将其添加到照片列表中\n\t\t\t\t\t\t\t\t\t\t\tif (oldAvatar && !oldAvatar.includes('/static/img/avatar.png')) {\n\t\t\t\t\t\t\t\t\t\t\t\t// 将原头像添加为当前照片类型\n\t\t\t\t\t\t\t\t\t\t\t\tthis.backgroundImages[index].url = oldAvatar;\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\tthis.opTipsPopup('已设为头像，点击保存按钮生效');\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 拖动相关变量\n\t\tdragStart(e) {\n\t\t\t// 记录开始拖动的元素索引\n\t\t\tthis.dragStartIndex = parseInt(e.currentTarget.dataset.index);\n\t\t\tthis.isDragging = true;\n\t\t\t\n\t\t\t// 记录触摸的起始位置\n\t\t\tthis.startX = e.touches[0].clientX;\n\t\t\tthis.startY = e.touches[0].clientY;\n\t\t},\n\t\t\n\t\tdragMove(e) {\n\t\t\tif (!this.isDragging) return;\n\t\t\t\n\t\t\t// 计算移动距离\n\t\t\tconst moveX = e.touches[0].clientX - this.startX;\n\t\t\tconst moveY = e.touches[0].clientY - this.startY;\n\t\t\t\n\t\t\t// 如果移动距离太小，不处理\n\t\t\tif (Math.abs(moveX) < 10 && Math.abs(moveY) < 10) return;\n\t\t\t\n\t\t\t// 防止长按菜单弹出\n\t\t\tthis.isDragging = true;\n\t\t\t\n\t\t\t// 获取当前触摸位置下的元素\n\t\t\tconst touch = e.touches[0];\n\t\t\tconst element = document.elementFromPoint(touch.clientX, touch.clientY);\n\t\t\t\n\t\t\tif (element) {\n\t\t\t\t// 查找最近的photo-item元素\n\t\t\t\tlet photoItem = element.closest('.photo-item');\n\t\t\t\tif (photoItem && photoItem.dataset && photoItem.dataset.index !== undefined) {\n\t\t\t\t\tconst targetIndex = parseInt(photoItem.dataset.index);\n\t\t\t\t\t\n\t\t\t\t\t// 如果拖到了不同的位置，进行交换\n\t\t\t\t\tif (targetIndex !== this.dragStartIndex && targetIndex >= 0 && targetIndex < this.backgroundImages.length) {\n\t\t\t\t\t\t// 保存临时变量\n\t\t\t\t\t\tconst temp = this.backgroundImages[this.dragStartIndex];\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 从数组中移除拖动项\n\t\t\t\t\t\tthis.backgroundImages.splice(this.dragStartIndex, 1);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 在目标位置插入\n\t\t\t\t\t\tthis.backgroundImages.splice(targetIndex, 0, temp);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 更新开始拖动的索引为新位置\n\t\t\t\t\t\tthis.dragStartIndex = targetIndex;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\tdragEnd(e) {\n\t\t\tthis.isDragging = false;\n\t\t},\n\t\t\n\t\tnewTagsPopupClick(isOpen) {\n\t\t\tif (isOpen) {\n\t\t\t\t// 显示加载中\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载标签中...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 先加载所有标签数据\n\t\t\t\tgetTagsWithCategories().then(res => {\n\t\t\t\t\tconsole.log('获取标签数据响应:', JSON.stringify(res));\n\t\t\t\t\t\n\t\t\t\t\t// 检查响应是否成功\n\t\t\t\t\tconst isSuccess = res.code == 200 || res.status == 200 || res.msg === 'success';\n\t\t\t\t\t\n\t\t\t\t\tif (isSuccess && res.data) {\n\t\t\t\t\t\t// 检查数据是否为空数组\n\t\t\t\t\t\tif (Array.isArray(res.data) && res.data.length === 0) {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tthis.opTipsPopup('暂无标签数据，请联系管理员添加标签');\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log('处理从API获取的标签数据');\n\t\t\t\t\t\t// 处理API返回的标签数据\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tif (Array.isArray(res.data)) {\n\t\t\t\t\t\t\t\tthis.processNewTagsData(res.data);\n\t\t\t\t\t\t\t} else if (typeof res.data === 'object') {\n\t\t\t\t\t\t\t\tconst dataArray = res.data.list || res.data.data || Object.values(res.data);\n\t\t\t\t\t\t\t\tif (Array.isArray(dataArray) && dataArray.length > 0) {\n\t\t\t\t\t\t\t\t\tthis.processNewTagsData(dataArray);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tconsole.error('无法从对象中提取标签数组');\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t\tthis.opTipsPopup('标签数据格式错误');\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tconsole.error('未知的数据格式:', typeof res.data);\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tthis.opTipsPopup('标签数据格式不支持');\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\t\tconsole.error('处理标签数据时出错:', err);\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tthis.opTipsPopup('标签数据处理失败');\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 加载我的标签，然后初始化并显示弹窗\n\t\t\t\t\t\tthis.loadMyTagsOnly()\n\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\tconsole.error('加载我的标签失败，但继续使用已加载的标签:', err);\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t.finally(() => {\n\t\t\t\t\t\t\t\tthis.initializeNewSelectedTags();\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tthis.$refs.newTagsPopup.open();\n\t\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('获取标签数据失败，状态码:', res.code || res.status);\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tthis.opTipsPopup('标签数据获取失败: ' + (res.msg || '未知错误'));\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('获取标签数据请求错误:', err);\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthis.opTipsPopup('网络错误，请稍后重试');\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tthis.$refs.newTagsPopup.close();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 处理新标签数据\n\t\tprocessNewTagsData(data) {\n\t\t\tconsole.log('开始处理标签数据:', typeof data === 'object' ? (Array.isArray(data) ? '数组格式' : '对象格式') : typeof data);\n\t\t\t\n\t\t\tif (!Array.isArray(data)) {\n\t\t\t\tconsole.error(`标签数据不是数组格式`);\n\t\t\t\t// 如果不是数组，尝试将其转换为数组\n\t\t\t\tif (data && typeof data === 'object') {\n\t\t\t\t\t// 尝试提取数组数据\n\t\t\t\t\tdata = data.list || data.data || Object.values(data);\n\t\t\t\t\tif (!Array.isArray(data)) {\n\t\t\t\t\t\tconsole.error(`无法将数据转换为数组`);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tconsole.error(`数据格式无效，无法处理`);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tconsole.log(`处理API数据数组，长度:`, data.length);\n\t\t\t\n\t\t\t// 重置或初始化标签分类数组\n\t\t\tthis.newTagCategories = [];\n\t\t\t\n\t\t\t// 处理标签分类和标签\n\t\t\tthis.newTagCategories = data.map(category => {\n\t\t\t\t// 如果是简单的标签对象，创建一个默认分类\n\t\t\t\tif (category && category.name && category.id && !category.tags) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tid: 0,\n\t\t\t\t\t\tname: '标签',\n\t\t\t\t\t\ttags: [category.name],\n\t\t\t\t\t\toriginalTags: [category]\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 确保标签数组存在\n\t\t\t\tlet tags = [];\n\t\t\t\tif (Array.isArray(category.tags)) {\n\t\t\t\t\ttags = category.tags;\n\t\t\t\t} else if (category.tag_list && Array.isArray(category.tag_list)) {\n\t\t\t\t\ttags = category.tag_list;\n\t\t\t\t} else if (category.tagList && Array.isArray(category.tagList)) {\n\t\t\t\t\ttags = category.tagList;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconsole.log(`API分类[${category.name}]的标签数量:`, tags.length);\n\t\t\t\t\n\t\t\t\t// 从标签对象中提取名称列表\n\t\t\t\tconst tagNames = tags.map(tag => {\n\t\t\t\t\tif (typeof tag === 'string') {\n\t\t\t\t\t\treturn tag;\n\t\t\t\t\t} else if (tag && tag.name) {\n\t\t\t\t\t\treturn tag.name;\n\t\t\t\t\t}\n\t\t\t\t\treturn null;\n\t\t\t\t}).filter(name => name !== null);\n\t\t\t\t\n\t\t\t\treturn {\n\t\t\t\t\tid: category.id || 0,\n\t\t\t\t\tname: category.name || '未命名分类',\n\t\t\t\t\ttags: tagNames,\n\t\t\t\t\toriginalTags: tags\n\t\t\t\t};\n\t\t\t});\n\t\t\t\n\t\t\t// 过滤掉没有标签的分类\n\t\t\tthis.newTagCategories = this.newTagCategories.filter(category => \n\t\t\t\tcategory.tags && category.tags.length > 0\n\t\t\t);\n\t\t\t\n\t\t\tconsole.log(`处理后的API标签分类数量:`, this.newTagCategories.length);\n\t\t\tif (this.newTagCategories.length === 0) {\n\t\t\t\tconsole.error(`没有有效的API标签分类`);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 初始化已选标签\n\t\t\tthis.initializeNewSelectedTags();\n\t\t\t\n\t\t\tconsole.log(`API标签处理完成`);\n\t\t},\n\t\t\n\t\t// 初始化已选标签\n\t\tinitializeNewSelectedTags() {\n\t\t\tconst that = this;\n\t\t\t\n\t\t\tconsole.log('初始化已选标签开始...');\n\t\t\tconsole.log('现有标签ID:', that.selectedTagIds);\n\t\t\tconsole.log('现有已选标签名称:', that.selectedNewTags);\n\t\t\t\n\t\t\t// 如果有标签ID，根据ID查找对应的标签名称\n\t\t\tif (that.selectedTagIds && that.selectedTagIds.length > 0) {\n\t\t\t\tconsole.log('根据标签ID初始化选中状态:', that.selectedTagIds);\n\t\t\t\t\n\t\t\t\t// 清空已选标签名称\n\t\t\t\tthat.selectedNewTags = [];\n\t\t\t\t\n\t\t\t\t// 遍历所有分类，查找匹配ID的标签\n\t\t\t\tthat.newTagCategories.forEach(category => {\n\t\t\t\t\tif (category.originalTags && Array.isArray(category.originalTags)) {\n\t\t\t\t\t\tcategory.originalTags.forEach(tag => {\n\t\t\t\t\t\t\t// 检查标签是否有ID字段\n\t\t\t\t\t\t\tconst tagId = tag.id || tag.tag_id;\n\t\t\t\t\t\t\tif (tagId && that.selectedTagIds.includes(tagId)) {\n\t\t\t\t\t\t\t\tif (tag.name) {\n\t\t\t\t\t\t\t\t\tthat.selectedNewTags.push(tag.name);\n\t\t\t\t\t\t\t\t\tconsole.log(`找到标签: ${tag.name}, ID: ${tagId}`);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconsole.log('根据ID找到的标签名称:', that.selectedNewTags);\n\t\t\t\t\n\t\t\t\t// 如果通过ID没有找到标签名称，尝试使用之前保存的名称\n\t\t\t\tif (that.selectedNewTags.length === 0 && that.userInfo.age) {\n\t\t\t\t\tconsole.log('通过ID没有找到标签，尝试从userInfo.age获取');\n\t\t\t\t\tthat.selectedNewTags = that.userInfo.age.split(', ').filter(tag => tag);\n\t\t\t\t\tconsole.log('从userInfo.age获取的标签:', that.selectedNewTags);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 更新userInfo.age字段\n\t\t\t\tif (that.selectedNewTags.length > 0) {\n\t\t\t\t\tthat.userInfo.age = that.selectedNewTags.join(', ');\n\t\t\t\t}\n\t\t\t}\n\t\t\t// 如果没有标签ID但有age字段，尝试从age中解析\n\t\t\telse if (that.userInfo.age) {\n\t\t\t\tconsole.log('从userInfo.age初始化标签');\n\t\t\t\tthat.selectedNewTags = that.userInfo.age.split(', ').filter(tag => tag);\n\t\t\t\tconsole.log('从用户信息字符串初始化标签:', that.selectedNewTags);\n\t\t\t\t\n\t\t\t\t// 尝试根据标签名称查找对应的ID\n\t\t\t\tthat.selectedTagIds = [];\n\t\t\t\tthat.selectedNewTags.forEach(tagName => {\n\t\t\t\t\tconst tagId = that.getNewTagIdByName(tagName);\n\t\t\t\t\tif (tagId) {\n\t\t\t\t\t\tthat.selectedTagIds.push(tagId);\n\t\t\t\t\t\tconsole.log(`找到标签\"${tagName}\"的ID: ${tagId}`);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log(`未找到标签\"${tagName}\"的ID`);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconsole.log('根据名称找到的标签ID:', that.selectedTagIds);\n\t\t\t} \n\t\t\telse {\n\t\t\t\tconsole.log('没有已选标签信息');\n\t\t\t\tthat.selectedNewTags = [];\n\t\t\t\tthat.selectedTagIds = [];\n\t\t\t}\n\t\t\t\n\t\t\tconsole.log('初始化已选标签完成, 标签数量:', that.selectedNewTags.length);\n\t\t},\n\t\t\n\t\tswitchNewCategory(index) {\n\t\t\tthis.newCategoryIndex = index;\n\t\t},\n\t\t\n\t\ttoggleNewTag(tag) {\n\t\t\tconsole.log('切换标签:', tag);\n\t\t\tconsole.log('当前已选择标签:', JSON.stringify(this.selectedNewTags));\n\t\t\t\n\t\t\tif (!tag) {\n\t\t\t\tconsole.error('无效的标签名称');\n\t\t\t\tthis.opTipsPopup('标签无效');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查标签是否存在于标签库中\n\t\t\tconst tagId = this.getNewTagIdByName(tag);\n\t\t\t\n\t\t\tif (tagId) {\n\t\t\t\tconsole.log('找到标签ID:', tagId);\n\t\t\t\t\n\t\t\t\tif (this.selectedNewTags.includes(tag)) {\n\t\t\t\t\t// 取消选择标签\n\t\t\t\t\tthis.selectedNewTags = this.selectedNewTags.filter(t => t !== tag);\n\t\t\t\t\t\n\t\t\t\t\t// 同时从标签ID数组中移除\n\t\t\t\t\tthis.selectedTagIds = this.selectedTagIds.filter(id => id !== tagId);\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('取消选择后的标签:', JSON.stringify(this.selectedNewTags));\n\t\t\t\t\tconsole.log('取消选择后的标签ID:', JSON.stringify(this.selectedTagIds));\n\t\t\t\t\tthis.opTipsPopup(`已取消选择 ${tag}`);\n\t\t\t\t} else if (this.selectedNewTags.length < 5) {\n\t\t\t\t\t// 选择标签\n\t\t\t\t\tthis.selectedNewTags.push(tag);\n\t\t\t\t\t\n\t\t\t\t\t// 同时添加到标签ID数组\n\t\t\t\t\tthis.selectedTagIds.push(tagId);\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('选择后的标签:', JSON.stringify(this.selectedNewTags));\n\t\t\t\t\tconsole.log('选择后的标签ID:', JSON.stringify(this.selectedTagIds));\n\t\t\t\t\tthis.opTipsPopup(`已选择 ${tag}`);\n\t\t\t\t} else {\n\t\t\t\t\tthis.opTipsPopup('最多只能选择5个兴趣标签');\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tconsole.error('标签在系统中不存在:', tag);\n\t\t\t\tthis.opTipsPopup('该标签不存在或已被删除');\n\t\t\t}\n\t\t},\n\t\t\n\t\tconfirmNewTags() {\n\t\t\tconst that = this;\n\t\t\tif (that.selectedNewTags.length > 5) {\n\t\t\t\tthat.opTipsPopup('最多只能选择5个兴趣标签');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 显示加载中\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '保存中...',\n\t\t\t\tmask: true\n\t\t\t});\n\t\t\t\n\t\t\t// 获取选中标签的ID列表\n\t\t\tconst tagIds = that.selectedNewTags.map(tagName => {\n\t\t\t\tfor (const category of that.newTagCategories) {\n\t\t\t\t\tif (category.originalTags) {\n\t\t\t\t\t\tconst tagObj = category.originalTags.find(t => t.name === tagName);\n\t\t\t\t\t\tif (tagObj) return tagObj.id;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn null;\n\t\t\t}).filter(id => id !== null);\n\t\t\t\n\t\t\t// 保存标签\n\t\t\tupdateUserTags(tagIds).then(res => {\n\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t// 更新本地数据\n\t\t\t\t\tthat.userInfo.interest_tags = [...that.selectedNewTags];\n\t\t\t\t\t\n\t\t\t\t\t// 更新本地存储\n\t\t\t\t\tconst cachedUserInfo = uni.getStorageSync('USER_INFO');\n\t\t\t\t\tif (cachedUserInfo) {\n\t\t\t\t\t\tcachedUserInfo.interest_tags = [...that.selectedNewTags];\n\t\t\t\t\t\tuni.setStorageSync('USER_INFO', cachedUserInfo);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 发送事件通知列表页面更新\n\t\t\t\t\tuni.$emit('updateUserTags', {\n\t\t\t\t\t\ttags: that.selectedNewTags,\n\t\t\t\t\t\tuserId: that.userInfo.id\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '保存成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\n\t\t\t// 关闭弹窗\n\t\t\tthat.$refs.newTagsPopup.close();\n\t\t\t\t} else {\n\t\t\t\t\tthrow new Error(res.msg || '保存失败');\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('更新标签失败:', err);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: err.message || '保存失败，请重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}).finally(() => {\n\t\t\t\tuni.hideLoading();\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 获取新标签的ID\n\t\tgetNewTagIdByName(tagName) {\n\t\t\tif (!tagName) {\n\t\t\t\tconsole.error('标签名为空');\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\t\n\t\t\tconsole.log('查找新标签ID，标签名:', tagName);\n\t\t\t\n\t\t\t// 遍历所有分类查找\n\t\t\tfor (const category of this.newTagCategories) {\n\t\t\t\tif (category.originalTags && Array.isArray(category.originalTags)) {\n\t\t\t\t\t// 在原始标签数组中查找\n\t\t\t\t\tfor (const tag of category.originalTags) {\n\t\t\t\t\t\tif (tag && tag.name === tagName) {\n\t\t\t\t\t\t\tconsole.log('在新分类中找到ID:', tag.id);\n\t\t\t\t\t\t\treturn tag.id;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 如果找不到匹配的标签ID，返回null\n\t\t\tconsole.warn(`未找到新标签 \"${tagName}\" 的ID`);\n\t\t\treturn null;\n\t\t},\n\t\t\n\t\tnewSwiperChange(e) {\n\t\t\tthis.newCategoryIndex = e.detail.current;\n\t\t},\n\t\t\n\t\t// 获取用户信息\n\t\tgetUserInfo() {\n\t\t\tconst that = this;\n\t\t\t\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\tgetUserSocialInfo().then(function(res) {\n\t\t\t\t\tif (res.code == 200 || res.status == 200) {\n\t\t\t\t\t\tconsole.log('刷新用户信息成功:', JSON.stringify(res.data));\n\t\t\t\t\t\tconst userData = res.data || res.result;\n\t\t\t\t\t\tif (userData) {\n\t\t\t\t\t\t\tuni.setStorageSync('USER_INFO', userData);\n\t\t\t\t\t\t\tthat.userInfo = userData;\n\t\t\t\t\t\t\tthat.originalUserInfo = JSON.parse(JSON.stringify(userData));\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 如果有生日但没有星座，计算星座\n\t\t\t\t\t\t\tif (that.userInfo.birthday && that.userInfo.constellation === null) {\n\t\t\t\t\t\t\t\tthat.calculateConstellationFromBirthday();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 处理背景图片\n\t\t\t\t\t\t\tthat.handleBackgroundImages();\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tresolve(userData);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('获取用户信息失败:', res.msg || '未知错误');\n\t\t\t\t\t\treject(new Error(res.msg || '获取用户信息失败'));\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('获取用户信息错误:', err);\n\t\t\t\t\treject(err);\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 不覆盖当前正在编辑的内容的刷新方法\n\t\trefreshUserInfoWithoutOverwrite() {\n\t\t\tconst that = this;\n\t\t\t\n\t\t\t// 保存当前正在编辑的头像\n\t\t\tconst currentAvatar = that.userInfo.avatar;\n\t\t\t\n\t\t\t// 获取用户信息\n\t\t\tgetUserSocialInfo().then(function(res) {\n\t\t\t\tif (res.code == 200 || res.status == 200) {\n\t\t\t\t\tconsole.log('刷新用户信息成功:', JSON.stringify(res.data));\n\t\t\t\t\tconst userData = res.data || res.result;\n\t\t\t\t\tif (userData) {\n\t\t\t\t\t\t// 保留当前正在编辑的头像\n\t\t\t\t\t\tuserData.avatar = currentAvatar;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 更新存储和视图\n\t\t\t\t\t\tuni.setStorageSync('USER_INFO', userData);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 合并数据，保留当前编辑的内容\n\t\t\t\t\t\tthat.userInfo = {\n\t\t\t\t\t\t\t...userData,\n\t\t\t\t\t\t\tavatar: currentAvatar // 确保头像不被覆盖\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 处理背景图片\n\t\t\t\t\t\tthat.handleBackgroundImages();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('刷新用户信息失败:', err);\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 仅加载我的标签数据，不更新用户信息\n\t\tloadMyTagsOnly() {\n\t\t\tconst that = this;\n\t\t\t\n\t\t\tconsole.log('开始加载我的标签数据...');\n\t\t\t\n\t\t\t// 返回Promise以便外部等待加载完成\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t// 使用getMyTags接口获取我的标签\n\t\t\t\tgetMyTags().then(function(res) {\n\t\t\t\t\tconsole.log('获取我的标签响应:', JSON.stringify(res));\n\t\t\t\t\t\n\t\t\t\t\tif (!res || typeof res !== 'object') {\n\t\t\t\t\t\tconsole.error('响应不是一个有效对象');\n\t\t\t\t\t\treject(new Error('响应格式无效'));\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (res.code == 200 || res.status == 200) {\n\t\t\t\t\t\t// 提取标签数据\n\t\t\t\t\t\tlet tagData = res.data || res.result;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查标签数据格式\n\t\t\t\t\t\tif (tagData) {\n\t\t\t\t\t\t\t// 如果不是数组但是对象，尝试提取数组\n\t\t\t\t\t\t\tif (!Array.isArray(tagData) && typeof tagData === 'object') {\n\t\t\t\t\t\t\t\ttagData = tagData.list || tagData.data || Object.values(tagData);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 确保是数组\n\t\t\t\t\t\t\tif (Array.isArray(tagData) && tagData.length > 0) {\n\t\t\t\t\t\t\t\tconsole.log('成功获取我的标签，数量:', tagData.length);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 保存标签ID\n\t\t\t\t\t\t\t\tthat.selectedTagIds = tagData.map(tag => {\n\t\t\t\t\t\t\t\t\tif (typeof tag === 'object' && tag.id) {\n\t\t\t\t\t\t\t\t\t\treturn tag.id;\n\t\t\t\t\t\t\t\t\t} else if (typeof tag === 'number') {\n\t\t\t\t\t\t\t\t\t\treturn tag;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn null;\n\t\t\t\t\t\t\t\t}).filter(id => id !== null);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tconsole.log('保存已选标签ID:', that.selectedTagIds);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t// 从API返回的数据中提取标签名称\n\t\t\t\t\t\t\t\tconst tagNames = tagData.map(tag => {\n\t\t\t\t\t\t\t\t\tif (typeof tag === 'string') {\n\t\t\t\t\t\t\t\t\t\treturn tag;\n\t\t\t\t\t\t\t\t\t} else if (tag && tag.name) {\n\t\t\t\t\t\t\t\t\t\treturn tag.name;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn null;\n\t\t\t\t\t\t\t\t}).filter(name => name !== null);\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tthat.selectedNewTags = tagNames;\n\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\tconsole.log('从API获取的已选标签:', tagNames);\n\t\t\t\t\t\t\t\tresolve(tagData);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tconsole.log('API返回的标签数据为空或格式不是数组');\n\t\t\t\t\t\t\t\tthat.selectedNewTags = [];\n\t\t\t\t\t\t\t\tthat.selectedTagIds = [];\n\t\t\t\t\t\t\t\tresolve([]);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconsole.log('API返回的标签数据为空');\n\t\t\t\t\t\t\tthat.selectedNewTags = [];\n\t\t\t\t\t\t\tthat.selectedTagIds = [];\n\t\t\t\t\t\t\tresolve([]);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('加载我的标签失败:', res.msg || '未知错误');\n\t\t\t\t\t\treject(new Error(res.msg || '加载标签失败'));\n\t\t\t\t\t}\n\t\t\t\t}).catch((err) => {\n\t\t\t\t\tconsole.error('加载我的标签请求错误:', err);\n\t\t\t\t\treject(err);\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t// 加载新标签数据\n\t\tloadNewTagsData() {\n\t\t\tconst that = this;\n\t\t\tconsole.log('开始加载新标签数据');\n\t\t\t\n\t\t\t// 返回Promise以便外部等待加载完成\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t// 使用API获取标签数据\n\t\t\t\tgetTagsWithCategories().then(function(res) {\n\t\t\t\t\tconsole.log('标签数据接口原始响应:', JSON.stringify(res));\n\t\t\t\t\t\n\t\t\t\t\t// 检查响应是否成功\n\t\t\t\t\tconst isSuccess = res.code == 200 || res.status == 200 || res.msg === 'success';\n\t\t\t\t\t\n\t\t\t\t\tif (isSuccess) {\n\t\t\t\t\t\t// 检查数据是否存在\n\t\t\t\t\t\tif (!res.data) {\n\t\t\t\t\t\t\tconsole.log('API响应成功但数据为空');\n\t\t\t\t\t\t\tthat.opTipsPopup('标签数据为空');\n\t\t\t\t\t\t\tresolve([]); // 空数据视为成功但无数据\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查数据是否为空数组\n\t\t\t\t\t\tif (Array.isArray(res.data) && res.data.length === 0) {\n\t\t\t\t\t\t\tconsole.log('API返回的标签数据为空数组');\n\t\t\t\t\t\t\tthat.opTipsPopup('暂无标签数据');\n\t\t\t\t\t\t\tresolve([]); // 空数组视为成功但无数据\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t// 处理API返回的标签数据\n\t\t\t\t\t\t\tif (Array.isArray(res.data)) {\n\t\t\t\t\t\t\t\tthat.processNewTagsData(res.data);\n\t\t\t\t\t\t\t\tresolve(res.data);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tconsole.log('标签数据格式不是数组，尝试处理对象格式');\n\t\t\t\t\t\t\t\tthat.processNewTagsData(res.data);\n\t\t\t\t\t\t\t\tresolve(res.data);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\t\tconsole.error('处理新标签数据时出错:', err);\n\t\t\t\t\t\t\tthat.opTipsPopup('标签数据处理失败');\n\t\t\t\t\t\t\tresolve([]); // 处理错误视为成功但无有效数据\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('加载新标签数据失败:', res.msg || '未知错误');\n\t\t\t\t\t\tthat.opTipsPopup('标签数据加载失败: ' + (res.msg || '未知错误'));\n\t\t\t\t\t\treject(new Error(res.msg || '加载标签失败'));\n\t\t\t\t\t}\n\t\t\t\t}).catch((err) => {\n\t\t\t\t\tconsole.error('加载新标签数据请求错误:', err);\n\t\t\t\t\tthat.opTipsPopup('网络错误，请稍后重试');\n\t\t\t\t\treject(err);\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style>\n/* 保持原有的CSS类名，只添加性能优化 */\n.container{\n\twidth: calc(100% - 60rpx);\n\tpadding: 0 30rpx 60rpx;\n}\n.title-box{\n\tpadding: 20rpx 0;\n\tfont-size: 40rpx;\n\tfont-weight: 700;\n}\n\n/* 相册部分样式 */\n.album-section {\n\tmargin: 30rpx 0;\n\twidth: 100%;\n}\n.album-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tmargin-bottom: 10rpx;\n\tcolor: #333;\n}\n.album-desc {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tmargin-bottom: 20rpx;\n\tline-height: 1.4;\n}\n.change-avatar {\n\tcolor: #FA5150;\n\tmargin-left: 10rpx;\n\tfont-weight: bold;\n}\n.photo-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tjustify-content: space-between;\n\tmargin-top: 20rpx;\n}\n.photo-item {\n\tposition: relative;\n\twidth: 220rpx;\n\theight: 220rpx;\n\tmargin-bottom: 20rpx;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tborder: 2rpx dashed #ddd;\n}\n.photo-image {\n\twidth: 100%;\n\theight: 100%;\n\tobject-fit: cover;\n}\n.photo-tag {\n\tposition: absolute;\n\ttop: 10rpx;\n\tleft: 10rpx;\n\tbackground-color: rgba(0, 0, 0, 0.6);\n\tcolor: #fff;\n\tfont-size: 20rpx;\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 10rpx;\n\tfont-weight: normal;\n}\n.photo-boost {\n\tposition: absolute;\n\tbottom: 0;\n\tleft: 0;\n\twidth: 100%;\n\tbackground-color: #FA5150;\n\tcolor: #fff;\n\tfont-size: 22rpx;\n\tpadding: 8rpx 0;\n\ttext-align: center;\n}\n.photo-placeholder {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 100%;\n\theight: 100%;\n\tbackground-color: #f5f5f5;\n}\n.photo-icon {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tmargin-bottom: 10rpx;\n}\n.photo-type {\n\tcolor: #666;\n\tfont-size: 24rpx;\n\tmargin-bottom: 10rpx;\n\ttext-align: center;\n\tpadding: 0 10rpx;\n\tfont-weight: normal;\n}\n.avatar-box{\n\tmargin: 10rpx 0 0;\n\twidth: 180rpx;\n\theight: 180rpx;\n\tpadding: 0;\n\tbackground: none;\n\tposition: relative;\n}\n.avatar-box .avatar{\n\twidth: 100%;\n\theight: 100%;\n\tbackground: #f8f8f8;\n\tborder-radius: 50%;\n}\n.avatar-box .icon{\n\tposition: absolute;\n\tright: 0;\n\tbottom: 0;\n\twidth: 48rpx;\n\theight: 48rpx;\n\tborder-radius: 50%;\n\tjustify-content: center;\n\tbackground: #000;\n\tborder: 4rpx solid #fff;\n}\n.title-label{\n\twidth: calc(100% - 48rpx);\n\tpadding: 30rpx 24rpx 12rpx;\n\tcolor: #999;\n\tfont-size: 24rpx;\n\tfont-weight: 700;\n}\n.subtitle {\n\tfont-weight: normal;\n\tfont-size: 20rpx;\n\tmargin-left: 10rpx;\n}\n.w50{\n\twidth: calc(50% - 80rpx) !important;\n}\n.w70{\n\twidth: calc(70% - 15rpx) !important;\n}\n.w25{\n\twidth: calc(25% - 15rpx) !important;\n}\n.sp{\n\tjustify-content: space-between;\n}\n.age{\n\twidth: calc(50% - 75rpx) !important;\n}\n.gender-box{\n\tpadding: 20rpx 0;\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tjustify-content: center;\n}\n.gender-box .active-1{\n\tborder-color: #fa5150 !important;\n\tbackground: rgba(250, 81, 80, 0.125);\n}\n.gender-box .active-2{\n\tborder-color: #4cd964 !important;\n\tbackground: rgba(76, 217, 100, 0.125);\n}\n.gender-box .gender-item{\n\theight: 90rpx;\n\twidth: 180rpx;\n\tborder-radius: 24rpx;\n\tborder-width: 4rpx;\n\tborder-style: solid;\n\tborder-color: #f5f5f5;\n\tjustify-content: center;\n\tmargin: 10rpx;\n}\n.gender-box .gender-item image{\n\twidth: 44rpx;\n\theight: 44rpx;\n\tmargin-right: 10rpx;\n}\n.input-box{\n\twidth: calc(100% - 68rpx);\n\tpadding: 0 30rpx;\n\theight: 90rpx;\n\tline-height: 90rpx;\n\tfont-size: 28rpx;\n\tfont-weight: 700;\n\tborder: 4rpx solid #f5f5f5;\n\tborder-radius: 24rpx;\n\tjustify-content: space-between;\n}\n.textarea-box{\n\twidth: calc(100% - 68rpx);\n\tpadding: 20rpx 30rpx;\n\tmin-height: 90rpx;\n\tline-height: 48rpx;\n\tcolor: #000;\n\tfont-size: 28rpx;\n\tfont-weight: 700;\n\tborder: 4rpx solid #f5f5f5;\n\tborder-radius: 24rpx;\n}\n.input-btn{\n\twidth: 90rpx;\n\theight: 90rpx;\n\tfont-size: 24rpx;\n\tjustify-content: space-between;\n\tmargin: 0;\n\tpadding: 0;\n\tbackground: #fff;\n}\n.input-btn image{\n\tmargin-right: 8rpx;\n\twidth: 32rpx;\n\theight: 32rpx;\n}\n.input-tips{\n\tmargin-top: 20rpx;\n\tcolor: #999;\n\tfont-size: 18rpx;\n}\n.popup-box{\n\twidth: calc(100% - 40rpx);\n\tpadding: 20rpx;\n\tbackground: #fff;\n\tborder-radius: 30rpx 30rpx 0 0;\n\tposition: relative;\n\toverflow: hidden;\n}\n.popup-box .popup-top{\n\twidth: calc(100% - 20rpx);\n\tpadding: 10rpx;\n\tjustify-content: space-between;\n}\n.popup-top .popup-title .t1{\n\tfont-size: 38rpx;\n\tfont-weight: 700;\n}\n.popup-top .popup-title .t2{\n\tcolor: #999;\n\tfont-size: 20rpx;\n\tfont-weight: 300;\n}\n.popup-top .popup-close{\n\twidth: 48rpx;\n\theight: 48rpx;\n\tborder-radius: 50%;\n\tbackground: #f8f8f8;\n\tjustify-content: center;\n\ttransform: rotate(45deg);\n}\n.popup-box .popup-btn{\n\tmargin: 40rpx 10rpx;\n\twidth: calc(100% - 20rpx);\n\theight: 90rpx;\n\tline-height: 90rpx;\n\ttext-align: center;\n\tfont-size: 24rpx;\n\tfont-weight: 700;\n\tcolor: #fff;\n\tbackground: #000;\n\tborder-radius: 90rpx;\n}\n.popup-box .age-box{\n\tpadding: 20rpx 0;\n\tdisplay: flex;\n\tflex-wrap: wrap;\n}\n.age-box .age-item{\n\tmargin: 10rpx;\n\tpadding: 30rpx 40rpx;\n\tcolor: #000;\n\tborder-width: 4rpx;\n\tborder-style: solid;\n\tborder-color: #f5f5f5;\n\tfont-size: 24rpx;\n\tfont-weight: 700;\n\tborder-radius: 30rpx;\n}\n.tips-box {\n\tpadding: 20rpx 30rpx;\n\tborder-radius: 12rpx;\n\tjustify-content: center;\n\tmargin-top: 40rpx;\n}\n\n.tips-box .tips-item {\n\tcolor: #fff;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\ttext-align: center;\n}\n.df{\n\tdisplay: flex;\n\talign-items: center;\n}\n\n/* 底部按钮样式 */\n.footer-box {\n\tposition: fixed;\n\tz-index: 99;\n\tbottom: 0;\n\tleft: 0;\n\twidth: 100%;\n\tpadding-bottom: env(safe-area-inset-bottom);\n}\n\n.footer-box .footer-item {\n\twidth: calc(100% - 60rpx);\n\tpadding: 20rpx 30rpx;\n\tjustify-content: center;\n}\n\n.footer-item .btn {\n\twidth: calc(100% - 30rpx);\n\theight: 90rpx;\n\tline-height: 90rpx;\n\ttext-align: center;\n\tfont-size: 28rpx;\n\tfont-weight: 700;\n\tborder-radius: 45rpx;\n}\n\n.bg2 {\n\tcolor: #fff;\n\tbackground: #000;\n}\n\n.bfw {\n\tbackground: #fff;\n}\n\n.bUp {\n\tbox-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);\n}\n\n/* 增加一些缺失的样式类 */\nbutton {\n\tpadding: 0;\n\tbackground-color: transparent;\n}\nbutton::after {\n\tborder: none;\n}\nimage {\n\tmax-width: 100%;\n\tmax-height: 100%;\n}\n\n/* 生日选择器样式 */\n.birthday-picker {\n\twidth: 100%;\n\theight: 400rpx;\n\tmargin-top: 20rpx;\n}\n.picker-item {\n\theight: 50px;\n\tline-height: 50px;\n\ttext-align: center;\n\tfont-size: 28rpx;\n}\n\n/* 年龄标签样式 */\n.age-tags {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tmargin: 10rpx 0 20rpx;\n}\n.age-tag {\n\tpadding: 10rpx 30rpx;\n\tmargin: 10rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 40rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n.age-tag-active {\n\tbackground-color: #000;\n\tcolor: #fff;\n}\n\n/* 标签相关样式 */\n.tags-box {\n\tpadding: 20rpx 10rpx;\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tmax-height: 500rpx;\n\toverflow-y: auto;\n}\n.tag-item {\n\tpadding: 16rpx 30rpx;\n\tmargin: 10rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 40rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n.tagactive {\n\tbackground-color: #000;\n\tcolor: #fff;\n}\n.tag-categories {\n\twhite-space: nowrap;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1px solid #f0f0f0;\n}\n.category-item {\n\tdisplay: inline-block;\n\tpadding: 16rpx 30rpx;\n\tmargin: 0 10rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 40rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n.category-active {\n\tbackground-color: #000;\n\tcolor: #fff;\n}\n\n/* 标签内容区 */\n.tags-swiper {\n\theight: 400rpx;\n\tmargin-top: 20rpx;\n}\n.tags-scroll {\n\theight: 100%;\n}\n\n.no-tags {\n\ttext-align: center;\n\tcolor: #999;\n\tfont-size: 28rpx;\n\tpadding: 30rpx 0;\n\twidth: 100%;\n}\n\n/* 底部安全区域 */\n.bottom-safe-area {\n\theight: 150rpx;\n\twidth: 100%;\n}\n\n/* 新增照片按钮样式 */\n.add-photo {\n\tborder: 2rpx dashed #ccc;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground-color: #f9f9f9;\n}\n.add-icon {\n\tfont-size: 60rpx;\n\tcolor: #999;\n\ttext-align: center;\n\tline-height: 1;\n}\n.add-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tmargin-top: 10rpx;\n}\n\n.photo-button {\n\twidth: 100%;\n\theight: 100%;\n\tpadding: 0;\n\tmargin: 0;\n\tbackground: none;\n\tborder: none;\n\tline-height: normal;\n\tposition: relative;\n\tdisplay: block;\n}\n\n.photo-button::after {\n\tborder: none;\n}\n\n.category-item {\n\tdisplay: inline-block;\n\tpadding: 16rpx 30rpx;\n\tmargin: 0 10rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 40rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n.category-active {\n\tbackground-color: #000;\n\tcolor: #fff;\n}\n\n.category-item {\n\tdisplay: inline-block;\n\tpadding: 16rpx 30rpx;\n\tmargin: 0 10rpx;\n\tbackground-color: #f5f5f5;\n\tborder-radius: 40rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n.category-active {\n\tbackground-color: #000;\n\tcolor: #fff;\n}\n.category-count {\n\tfont-size: 20rpx;\n\tmargin-left: 6rpx;\n\topacity: 0.8;\n}\n</style>", "import MiniProgramPage from 'D:/uniapp/vue3/pages/center/means.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getUserSocialInfo", "getTagsWithCategories", "updateUserSocialInfo", "updateUserTags", "getMyTags", "res"], "mappings": ";;;;AA2dA,eAAe,MAAW;AAC1B,iBAAiB,MAAW;AAEhB,OAAQ;AAEpB,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,IACA;AAAA,EACA;AAAA,EACD,OAAO;AAEN,UAAM,OAAO,oBAAI;AACjB,UAAM,cAAc,KAAK;AACzB,UAAM,QAAQ,MAAM,KAAK,EAAE,QAAQ,IAAI,GAAG,CAAC,GAAG,MAAM,cAAc,KAAK,CAAC;AACxE,UAAM,SAAS,MAAM,KAAK,EAAE,QAAQ,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC;AACzD,UAAM,OAAO,MAAM,KAAK,EAAE,QAAQ,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC;AAGvD,UAAM,mBAAmB,MAAM,UAAU,UAAQ,SAAS,cAAc,EAAE;AAE1E,WAAO;AAAA,MACN,UAAU;AAAA,QACT,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,UAAU;AAAA,QACV,KAAK;AAAA,QACL,UAAU;AAAA,QACV,eAAe;AAAA,QACf,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,eAAe,CAAE;AAAA;AAAA,MACjB;AAAA;AAAA,MAED,kBAAkB,CAAE;AAAA,MACpB,YAAY,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO;AAAA;AAAA,MAGhD;AAAA,MACA;AAAA,MACA;AAAA,MACA,qBAAqB,CAAC,kBAAkB,GAAG,CAAC;AAAA;AAAA,MAG5C,UAAU;AAAA,QACT,cAAc,CAAE;AAAA;AAAA,QAChB,QAAQ,CAAE;AAAA;AAAA,QACV,YAAY,CAAE;AAAA;AAAA,QACd,sBAAsB;AAAA;AAAA,QACtB,WAAW;AAAA;AAAA,MACX;AAAA;AAAA,MAGD,SAAS;AAAA,QACR,cAAc;AAAA,QACd,aAAa;AAAA,QACb,cAAc;AAAA,QACd,YAAY;AAAA;AAAA,QACZ,WAAW;AAAA;AAAA,MACX;AAAA;AAAA,MAGD,gBAAgB;AAAA,QACf,MAAM;AAAA,QACN,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,oBAAoB;AACnB,UAAI,CAAC,KAAK,SAAS;AAAU,eAAO;AAGpC,UAAI,OAAO,KAAK,SAAS,aAAa,YAAY,CAAC,MAAM,KAAK,SAAS,QAAQ,GAAG;AAChF,cAAM,YAAY,SAAS,KAAK,SAAS,QAAQ;AACjD,cAAM,OAAO,IAAI,KAAK,YAAY,GAAI;AACvC,eAAO,KAAK,WAAW,IAAI;AAAA,MAC5B;AAEA,aAAO,KAAK,SAAS;AAAA,IACrB;AAAA;AAAA,IAGD,sBAAsB;AACrB,YAAM,kBAAkB,KAAK,SAAS,WAAW,KAAK,SAAS,oBAAoB;AACnF,cAAO,mDAAiB,SAAQ;IACjC;AAAA,EACA;AAAA,EACD,SAAS;AAER,SAAK,eAAc;AAAA,EACnB;AAAA,EAED,SAAS;AAER,SAAK,gBAAe;AAAA,EACpB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,kBAAkB;AACvB,UAAI;AAEH,cAAM,gBAAgB,KAAK,OAAO,MAAM,IAAI;AAC5C,cAAM,eAAe,KAAK,OAAO,MAAM,IAAI;AAG3C,YAAI,CAAC,gBAAgB,CAAC,eAAe;AACpCA,wBAAAA,oDAAY,cAAc;AAC1BA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,UACN,CAAC;AACD;AAAA,QACD;AAGA,YAAI,CAAC,KAAK,SAAS,OAAO,kBAAkB,KAAK,SAAS,KAAK;AAC9DA,wBAAAA,MAAA,MAAA,OAAA,iCAAY,qBAAqB;AACjCA,wBAAAA,MAAY,MAAA,OAAA,iCAAA,WAAW,eAAe,WAAW,KAAK,SAAS,GAAG;AAClE,gBAAM,KAAK;QACZ;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,iCAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,kBAAkB;AACvB,UAAI;AACHA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAGD,cAAM,QAAQ,IAAI;AAAA,UACjB,KAAK,YAAa;AAAA,UAClB,KAAK,WAAW;AAAA,QACjB,CAAC;AAAA,MAEA,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,iCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,UAAU;AACTA,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACtB,UAAI;AAEHA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAGD,cAAM,QAAQ,IAAI;AAAA,UACjB,KAAK,YAAa;AAAA,UAClB,KAAK,kBAAmB;AAAA,UACxB,KAAK,WAAY;AAAA;AAAA,UACjB,KAAK,gBAAgB;AAAA,QACtB,CAAC;AAAA,MAEA,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,iCAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,UAAU;AACTA,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,cAAc;AACnB,UAAI;AACH,cAAM,MAAM,MAAMC,WAAAA;AAClB,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AACjCD,wBAAA,MAAA,MAAA,OAAA,iCAAY,aAAa,IAAI,IAAI;AAGjC,eAAK,WAAW,IAAI;AAGpB,cAAI,IAAI,KAAK,eAAe;AAC3B,iBAAK,SAAS,eAAe,CAAC,GAAG,IAAI,KAAK,aAAa;AAAA,iBACjD;AACN,iBAAK,SAAS,eAAe;UAC9B;AAGA,eAAK,mBAAmB,IAAI,KAAK,qBAAqB,CAAA;AAGtDA,wBAAAA,MAAI,eAAe,aAAa,IAAI,IAAI;AAAA,QACzC;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,iCAAc,aAAa,KAAK;AAChC,cAAM;AAAA,MACP;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACzB,UAAI,KAAK,SAAS;AAAW;AAC7B,WAAK,SAAS,YAAY;AAE1B,UAAI;AACH,cAAM,MAAM,MAAME,WAAAA;AAClB,YAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AACjC,eAAK,mBAAmB,IAAI,IAAI;AAAA,QACjC;AAAA,MACC,SAAO,OAAO;AACfF,sBAAA,MAAA,MAAA,SAAA,iCAAc,aAAa,KAAK;AAChC,cAAM;AAAA,MACP,UAAU;AACT,aAAK,SAAS,YAAY;AAAA,MAC3B;AAAA,IACA;AAAA;AAAA,IAGD,SAAS,MAAM,QAAQ,KAAK,WAAW,WAAW;AACjD,UAAI,KAAK,eAAe,QAAQ,GAAG;AAClC,qBAAa,KAAK,eAAe,QAAQ,CAAC;AAAA,MAC3C;AAEA,WAAK,eAAe,QAAQ,IAAI,WAAW,MAAM;AAChD,aAAK,KAAK,IAAI;AACd,aAAK,eAAe,QAAQ,IAAI;AAAA,MAChC,GAAE,KAAK;AAAA,IACR;AAAA;AAAA,IAGD,YAAY,OAAO,UAAU,MAAM;AAClCA,0BAAA,MAAA,SAAA,iCAAc,GAAG,OAAO,OAAO,KAAK;AAEpC,UAAI,UAAU,GAAG,OAAO;AACxB,UAAI,MAAM,SAAS;AAClB,kBAAU,MAAM;AAAA,MACjB,WAAW,MAAM,KAAK;AACrB,kBAAU,MAAM;AAAA,MACjB;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACX,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,MAAM;AACpB,aAAO,KAAK,iBAAiB,KAAK,SAAO,IAAI,SAAS,IAAI;AAAA,IAC1D;AAAA,IAED,eAAe,MAAM;AACpB,YAAM,QAAQ,KAAK,iBAAiB,KAAK,SAAO,IAAI,SAAS,IAAI;AACjE,aAAO,QAAQ,MAAM,MAAM;AAAA,IAC3B;AAAA,IAED,eAAe,MAAM;AACpB,aAAO,KAAK,iBAAiB,KAAK,SAAO,IAAI,SAAS,IAAI,KAAK;AAAA,IAC/D;AAAA,IAED,cAAc,MAAM;AACnB,aAAO,KAAK,iBAAiB,UAAU,SAAO,IAAI,SAAS,IAAI;AAAA,IAC/D;AAAA;AAAA,IAGD,mBAAmB,MAAM;AACxB,UAAI;AACH,cAAM,YAAY,MAAM,QAAQ,IAAI,IAAI,OAAQ,KAAK,QAAQ,KAAK,QAAQ,CAAE;AAE5E,aAAK,mBAAmB,UAAU,IAAI,cAAY;AACjD,gBAAM,OAAO,KAAK,YAAY,QAAQ;AACtC,gBAAM,eAAe,KAAK,gBAAgB,QAAQ;AAElD,iBAAO;AAAA,YACN,IAAI,SAAS,MAAM;AAAA,YACnB,MAAM,SAAS,QAAQ;AAAA,YACvB,MAAM,QAAQ,CAAE;AAAA,YAChB,cAAc,gBAAgB,CAAC;AAAA;SAEhC,EAAE,OAAO,cAAY,SAAS,QAAQ,SAAS,KAAK,SAAS,CAAC;AAAA,MAC9D,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,iCAAc,aAAa,KAAK;AAChC,aAAK,mBAAmB;MACzB;AAAA,IACA;AAAA;AAAA,IAGD,YAAY,UAAU;AACrB,UAAI,MAAM,QAAQ,SAAS,IAAI,GAAG;AACjC,eAAO,SAAS,KAAK,IAAI,SAAO,OAAO,QAAQ,WAAW,MAAM,IAAI,IAAI;AAAA,MACzE;AACA,UAAI,MAAM,QAAQ,SAAS,QAAQ,GAAG;AACrC,eAAO,SAAS,SAAS,IAAI,SAAO,OAAO,QAAQ,WAAW,MAAM,IAAI,IAAI;AAAA,MAC7E;AACA,UAAI,MAAM,QAAQ,SAAS,OAAO,GAAG;AACpC,eAAO,SAAS,QAAQ,IAAI,SAAO,OAAO,QAAQ,WAAW,MAAM,IAAI,IAAI;AAAA,MAC5E;AACA,aAAO,SAAS,OAAO,CAAC,SAAS,IAAI,IAAI,CAAA;AAAA,IACzC;AAAA;AAAA,IAGD,gBAAgB,UAAU;AACzB,UAAI,MAAM,QAAQ,SAAS,IAAI;AAAG,eAAO,SAAS;AAClD,UAAI,MAAM,QAAQ,SAAS,QAAQ;AAAG,eAAO,SAAS;AACtD,UAAI,MAAM,QAAQ,SAAS,OAAO;AAAG,eAAO,SAAS;AACrD,aAAO,SAAS,OAAO,CAAC,QAAQ,IAAI,CAAA;AAAA,IACpC;AAAA;AAAA,IAGD,WAAW,MAAM;AAChB,YAAM,OAAO,KAAK;AAClB,YAAM,SAAS,KAAK,aAAa,GAAG,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC9D,YAAM,MAAM,KAAK,QAAS,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACrD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC9B;AAAA;AAAA,IAGD,kBAAkB;AAEjB,YAAM,iBAAiBA,cAAAA,MAAI,eAAe,eAAe;AACzD,YAAM,kBAAkBA,cAAAA,MAAI,eAAe,eAAe;AAE1D,UAAI,kBAAkB,iBAAiB;AACtC,aAAK,SAAS,gBAAgB,WAAW,cAAc;AACvD,aAAK,SAAS,gBAAgB,WAAW,eAAe;AACxDA,sBAAAA,MAAY,MAAA,OAAA,iCAAA,qBAAqB,KAAK,SAAS,eAAe,KAAK,SAAS,aAAa;AAGzF,cAAM,gBAAgBA,cAAAA,MAAI,eAAe,gBAAgB;AACzD,YAAI,eAAe;AAClB,eAAK,SAAS,iBAAiB;AAC/B,eAAK,SAAS,WAAW;AACzBA,4EAAY,aAAa,aAAa;AAAA,eAChC;AAEN,eAAK,uBAAuB,KAAK,SAAS,eAAe,KAAK,SAAS,aAAa;AAAA,QACrF;AACA;AAAA,MACD;AAGAA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,kBAAkB;AA0B7BA,oBAAAA,MAAI,YAAY;AAAA,QACf,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;AACjB,eAAK,SAAS,gBAAgB,IAAI;AAClC,eAAK,SAAS,gBAAgB,IAAI;AAElCA,wBAAAA,MAAI,eAAe,iBAAiB,IAAI,QAAQ;AAChDA,wBAAAA,MAAI,eAAe,iBAAiB,IAAI,SAAS;AACjDA,8BAAA,MAAA,OAAA,iCAAY,gCAAgC,IAAI,UAAU,IAAI,SAAS;AAGvE,eAAK,uBAAuB,IAAI,UAAU,IAAI,SAAS;AAAA,QACvD;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAY,MAAA,MAAA,OAAA,iCAAA,wBAAwB,GAAG;AACvC,eAAK,mBAAkB;AAAA,QACxB;AAAA,MACD,CAAC;AAAA,IAIF;AAAA;AAAA,IAGD,qBAAqB;AACpB,WAAK,SAAS,gBAAgB;AAC9B,WAAK,SAAS,gBAAgB;AAC9B,WAAK,SAAS,iBAAiB;AAC/B,WAAK,SAAS,WAAW;AAEzBA,oBAAG,MAAC,eAAe,iBAAiB,KAAK,SAAS,aAAa;AAC/DA,oBAAG,MAAC,eAAe,iBAAiB,KAAK,SAAS,aAAa;AAC/DA,oBAAG,MAAC,eAAe,kBAAkB,KAAK,SAAS,cAAc;AACjEA,oBAAAA,MAAA,MAAA,OAAA,iCAAY,qBAAqB,KAAK,SAAS,eAAe,KAAK,SAAS,aAAa;AACzFA,0BAAA,MAAA,OAAA,iCAAY,WAAW,KAAK,SAAS,cAAc;AAAA,IACnD;AAAA;AAAA,IAGD,kBAAkB;AACjBA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,gBAAgB;AAE5BA,0BAAI,kBAAkB,eAAe;AACrCA,0BAAI,kBAAkB,eAAe;AACrCA,0BAAI,kBAAkB,gBAAgB;AAEtC,WAAK,gBAAe;AAAA,IACpB;AAAA;AAAA,IAGD,uBAAuB,UAAU,WAAW;AAC3CA,oBAAA,MAAA,MAAA,OAAA,iCAAY,gBAAgB,UAAU,SAAS;AAG/CA,oBAAAA,MAAI,QAAQ;AAAA,QACX,KAAK;AAAA,QACL,MAAM;AAAA,UACL,UAAU,GAAG,QAAQ,IAAI,SAAS;AAAA,UAClC,KAAK;AAAA;AAAA,UACL,SAAS;AAAA,QACT;AAAA,QACD,SAAS,CAAC,QAAQ;AACjBA,wBAAA,MAAA,MAAA,OAAA,iCAAY,YAAY,IAAI,IAAI;AAChC,cAAI,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,QAAQ;AAC7C,kBAAM,SAAS,IAAI,KAAK;AACxB,kBAAM,UAAU,OAAO;AACvB,kBAAM,WAAW,OAAO,QAAQ;AAChC,kBAAM,OAAO,OAAO,QAAQ;AAC5B,kBAAM,WAAW,OAAO,QAAQ;AAGhC,gBAAI,cAAc;AAClB,gBAAI,YAAY,MAAM;AACrB,kBAAI,aAAa,MAAM;AAEtB,8BAAc,YAAY,YAAY;AAAA,qBAChC;AAEN,8BAAc,WAAW,QAAQ,YAAY;AAAA,cAC9C;AAAA,mBACM;AAEN,4BAAc,WAAW;AAAA,YAC1B;AAGA,iBAAK,SAAS,iBAAiB;AAC/B,iBAAK,SAAS,WAAW;AAGzBA,0BAAAA,MAAI,eAAe,kBAAkB,WAAW;AAEhDA,0BAAY,MAAA,MAAA,OAAA,iCAAA,eAAe,WAAW;AACtC,iBAAK,YAAY,aAAa,WAAW;AAAA,iBACnC;AACNA,0BAAc,MAAA,MAAA,SAAA,iCAAA,YAAY,IAAI,IAAI;AAClC,iBAAK,mBAAkB;AAAA,UACxB;AAAA,QACA;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,8EAAc,cAAc,GAAG;AAC/B,eAAK,mBAAkB;AAAA,QACxB;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AAEpB,YAAM,iBAAiB;AACvB,WAAK,SAAS,iBAAiB;AAC/B,WAAK,SAAS,WAAW;AACzBA,oBAAY,MAAA,MAAA,OAAA,iCAAA,WAAW,cAAc;AAAA,IACrC;AAAA;AAAA,IAGD,mBAAmB;AAElB,YAAM,iBAAiBA,cAAAA,MAAI,eAAe,eAAe;AACzD,YAAM,kBAAkBA,cAAAA,MAAI,eAAe,eAAe;AAC1D,YAAM,gBAAgBA,cAAAA,MAAI,eAAe,gBAAgB;AAEzD,UAAI,kBAAkB,iBAAiB;AAEtC,YAAI,CAAC,KAAK,SAAS,iBAAiB,CAAC,KAAK,SAAS,eAAe;AACjE,eAAK,SAAS,gBAAgB,WAAW,cAAc;AACvD,eAAK,SAAS,gBAAgB,WAAW,eAAe;AACxDA,wBAAAA,MAAA,MAAA,OAAA,iCAAY,oBAAoB,KAAK,SAAS,eAAe,KAAK,SAAS,aAAa;AAAA,QACzF;AAGA,YAAI,iBAAiB,CAAC,KAAK,SAAS,gBAAgB;AACnD,eAAK,SAAS,iBAAiB;AAC/B,eAAK,SAAS,WAAW;AACzBA,wBAAA,MAAA,MAAA,OAAA,iCAAY,oBAAoB,aAAa;AAAA,QAC9C;AAAA,aACM;AAEN,aAAK,gBAAe;AAAA,MACrB;AAAA,IACA;AAAA;AAAA,IAGD,uBAAuB;AACtB,UAAI,WAAWA,cAAG,MAAC,eAAe,WAAW,KAAK,CAAA;AAGlD,UAAI,OAAO,aAAa,UAAU;AACjC,YAAI;AACH,qBAAW,KAAK,MAAM,QAAQ;AAAA,QAC/B,SAAS,GAAG;AACXA,wBAAA,MAAA,MAAA,SAAA,kCAAc,oBAAoB,CAAC;AACnC,qBAAW,CAAA;AAAA,QACZ;AAAA,MACD;AAEA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,yBAAyB;AACxB,UAAI,KAAK,SAAS,iBAAiB;AAClC,YAAI;AAEH,cAAI,OAAO,KAAK,SAAS,oBAAoB,UAAU;AACtD,iBAAK,mBAAmB,KAAK,MAAM,KAAK,SAAS,eAAe;AAAA,qBACtD,MAAM,QAAQ,KAAK,SAAS,eAAe,GAAG;AACxD,iBAAK,mBAAmB,KAAK,SAAS;AAAA,UACvC;AAAA,QACD,SAAS,GAAG;AACXA,+EAAc,cAAc,CAAC;AAC7B,eAAK,mBAAmB;QACzB;AAAA,aACM;AACN,aAAK,mBAAmB;MACzB;AAAA,IACA;AAAA;AAAA,IAGD,eAAe,MAAM;AACpB,aAAO,KAAK,iBAAiB,KAAK,UAAQ,KAAK,SAAS,IAAI;AAAA,IAC5D;AAAA;AAAA,IAGD,eAAe,MAAM;AACpB,YAAM,QAAQ,KAAK,iBAAiB,KAAK,UAAQ,KAAK,SAAS,IAAI;AACnE,aAAO,QAAQ,MAAM,MAAM;AAAA,IAC3B;AAAA;AAAA,IAGD,eAAe,MAAM;AACpB,aAAO,KAAK,iBAAiB,KAAK,UAAQ,KAAK,SAAS,IAAI,KAAK;AAAA,IACjE;AAAA;AAAA,IAGD,cAAc,MAAM;AACnB,aAAO,KAAK,iBAAiB,UAAU,UAAQ,KAAK,SAAS,IAAI;AAAA,IACjE;AAAA;AAAA,IAGD,eAAe,MAAM;AACpB,UAAI,OAAO;AAGX,UAAI,CAAC,MAAM;AACV,cAAM,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO;AAEvD,cAAM,gBAAgB,KAAK,iBAAiB,IAAI,UAAQ,KAAK,IAAI;AACjE,cAAM,iBAAiB,WAAW,OAAO,OAAK,CAAC,cAAc,SAAS,CAAC,CAAC;AAExE,YAAI,eAAe,WAAW,GAAG;AAChC,eAAK,YAAY,cAAc;AAC/B;AAAA,QACD;AAEAA,sBAAAA,MAAI,gBAAgB;AAAA,UACnB,UAAU;AAAA,UACV,SAAS,CAAC,QAAQ;AACjB,kBAAM,eAAe,eAAe,IAAI,QAAQ;AAChD,iBAAK,qBAAqB,YAAY;AAAA,UACvC;AAAA,QACD,CAAC;AACD;AAAA,MACD;AAGA,WAAK,qBAAqB,IAAI;AAAA,IAC9B;AAAA;AAAA,IAGD,qBAAqB,MAAM;AAC1B,UAAI,OAAO;AACX,WAAK,eAAe;AACpB,WAAK,MAAM,kBAAkB,gBAAgB,CAAC,QAAQ;AAErD,aAAK,mBAAmB,KAAK,iBAAiB,OAAO,UAAQ,KAAK,SAAS,IAAI;AAE/E,aAAK,iBAAiB,KAAK;AAAA,UAC1B;AAAA,UACA,KAAK,IAAI,KAAK;AAAA,QACf,CAAC;AACD,aAAK,eAAe;AACpB,aAAK,YAAY,MAAM;AAAA,MACvB,GAAE,CAAC,QAAQ;AACX,aAAK,eAAe;AACpB,aAAK,YAAY,MAAM;AAAA,MACvB,GAAE,CAAC,QAAQ;AACX,aAAK,cAAc,IAAI;AACvB,aAAK,eAAe,IAAI;AAAA,MACzB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACpB,UAAI,OAAO;AACX,WAAK,eAAe;AAEpB,WAAK,MAAM,kBAAkB,gBAAgB,CAAC,QAAQ;AACrD,aAAK,eAAe;AAGpB,aAAK,SAAS,SAAS,IAAI,KAAK;AAGhC,aAAK,YAAY,gBAAgB;AAAA,MACjC,GAAE,CAAC,QAAQ;AACX,aAAK,eAAe;AACpB,aAAK,YAAY,QAAQ;AAAA,MACzB,GAAE,CAAC,QAAQ;AACX,aAAK,cAAc,IAAI;AACvB,aAAK,eAAe,IAAI;AAAA,MACzB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,GAAG;AACjB,YAAM,OAAO;AACb,YAAM,EAAE,UAAQ,IAAM,EAAE;AAExB,WAAK,MAAM,WAAW,gBAAgB,WAAW,CAAC,QAAQ;AAEzD,aAAK,SAAS,SAAS,IAAI,KAAK;AAChC,aAAK,YAAY,gBAAgB;AAAA,MACjC,GAAE,CAAC,QAAQ;AACXA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,GAAG;AACf,aAAK,YAAY,QAAQ;AAAA,MAC1B,CAAC;AAAA,IACD;AAAA,IAED,WAAW;AAAA,IAGV;AAAA,IAED,YAAY;AAAA,IAGX;AAAA,IAED,WAAW,OAAO,GAAG;AACpB,UAAI,OAAO;AAGX,WAAK,SAAS,kBAAkB,KAAK,UAAU,KAAK,gBAAgB;AAGpE,UAAI,IAAI,WAAW,IAAI,QAAQ,OAAO,IAAI,QAAQ,IAAI,iBAAiB;AACtE,gBAAQ,IAAI,QAAQ,IAAI,iBAAiB;AAAA,UACxC;AAAA,UACA,MAAM,KAAK,SAAS;AAAA,UACpB,OAAO,KAAK,SAAS;AAAA,UACrB,QAAQ,KAAK,SAAS;AAAA,UACtB,QAAQ,KAAK,SAAS;AAAA,UACtB,KAAK,KAAK,SAAS;AAAA;AAAA,UAEnB,QAAQ,KAAK,SAAS;AAAA,UACtB,UAAU,KAAK,SAAS;AAAA,UACxB,YAAY,KAAK,SAAS;AAAA,UAC1B,QAAQ,KAAK,SAAS;AAAA,UACtB,QAAQ,KAAK,SAAS;AAAA,UACtB,UAAU,KAAK,SAAS;AAAA,UACxB,iBAAiB,KAAK,SAAS;AAAA,QAC/B,GAAE,MAAM,EAAE,KAAK,SAAS,KAAK;AAC7B,cAAI,IAAI,QAAQ,KAAK;AACpB,gBAAI,WAAW,KAAK;AACpB,uBAAW,EAAC,GAAG,UAAU,GAAG,KAAK,SAAQ;AACzCA,0BAAAA,MAAI,eAAe,aAAa,QAAQ;AACxC,iBAAK,mBAAmB,KAAK,MAAM,KAAK,UAAU,KAAK,QAAQ,CAAC;AAAA,UACjE;AACA,eAAK,YAAY,IAAI,GAAG;AAAA,QACzB,CAAC;AAAA,aACK;AAEN,mBAAW,MAAM;AAChB,cAAI,WAAW,KAAK;AACpB,qBAAW;AAAA,YACV,GAAG;AAAA,YACH,MAAM,KAAK,SAAS;AAAA,YACpB,OAAO,KAAK,SAAS;AAAA,YACrB,QAAQ,KAAK,SAAS;AAAA,YACtB,QAAQ,KAAK,SAAS;AAAA,YACtB,KAAK,KAAK,SAAS;AAAA;AAAA,YAEnB,QAAQ,KAAK,SAAS;AAAA,YACtB,UAAU,KAAK,SAAS;AAAA,YACxB,YAAY,KAAK,SAAS;AAAA,YAC1B,QAAQ,KAAK,SAAS;AAAA,YACtB,QAAQ,KAAK,SAAS;AAAA,YACtB,UAAU,KAAK,SAAS;AAAA,YACxB,iBAAiB,KAAK,SAAS;AAAA;AAEhCA,wBAAAA,MAAI,eAAe,aAAa,QAAQ;AACxC,eAAK,mBAAmB,KAAK,MAAM,KAAK,UAAU,QAAQ,CAAC;AAE3D,gBAAM,WAAW;AAAA,YAChB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA;AAED,eAAK,YAAY,SAAS,IAAI,KAAK,SAAS,CAAC,CAAC;AAAA,QAC9C,GAAE,GAAG;AAAA,MACP;AAAA,IACA;AAAA;AAAA,IAGD,iBAAiB;AAChB,YAAM,OAAO;AACbA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAGD,YAAM,iBAAiB,OAAO,KAAK,qBAAqB,WACrD,KAAK,mBACL,KAAK,UAAU,KAAK,gBAAgB;AAGvC,YAAM,aAAa;AAAA;AAAA,QAElB,QAAQ,KAAK,SAAS;AAAA,QACtB,UAAU,KAAK,SAAS;AAAA,QACxB,UAAU,KAAK,SAAS;AAAA;AAAA,QAGxB,KAAK,KAAK,SAAS;AAAA,QACnB,UAAU,KAAK,SAAS;AAAA,QACxB,QAAQ,KAAK,SAAS;AAAA,QACtB,UAAU,KAAK,SAAS;AAAA,QACxB,YAAY,KAAK,SAAS;AAAA,QAC1B,QAAQ,KAAK,SAAS;AAAA,QACtB,QAAQ,KAAK,SAAS;AAAA,QACtB,iBAAiB;AAAA,QACjB,eAAe,KAAK,SAAS;AAAA;AAAA,QAG7B,eAAe,KAAK,SAAS,iBAAiB;AAAA,QAC9C,eAAe,KAAK,SAAS,iBAAiB;AAAA,QAC9C,gBAAgB,KAAK,SAAS,kBAAkB;AAAA;AAGjDA,0BAAA,MAAA,OAAA,kCAAY,YAAY,KAAK,UAAU,UAAU,CAAC;AAClDA,oBAAAA,MAAY,MAAA,OAAA,kCAAA,UAAU;AACtBA,oBAAY,MAAA,MAAA,OAAA,kCAAA,oBAAoB,WAAW,aAAa;AACxDA,oBAAY,MAAA,MAAA,OAAA,kCAAA,oBAAoB,WAAW,aAAa;AACxDA,oBAAY,MAAA,MAAA,OAAA,kCAAA,qBAAqB,WAAW,cAAc;AAG1DG,iBAAAA,qBAAqB,UAAU,EAAE,KAAK,SAAO;AAC5CH,4BAAY,MAAA,OAAA,kCAAA,aAAa,KAAK,UAAU,GAAG,CAAC;AAG5C,YAAI,KAAK,kBAAkB,KAAK,eAAe,SAAS,GAAG;AAE1D,iBAAOI,WAAc,eAAC,KAAK,cAAc,EAAE,KAAK,YAAU;AACzDJ,gCAAA,MAAA,OAAA,kCAAY,WAAW,KAAK,UAAU,MAAM,CAAC;AAC7C,mBAAO,EAAE,YAAY,KAAK,WAAW;UACtC,CAAC,EAAE,MAAM,YAAU;AAClBA,iFAAc,WAAW,MAAM;AAC/B,mBAAO,EAAE,YAAY,KAAK,UAAU;UACrC,CAAC;AAAA,eACK;AAEN,iBAAO,EAAE,YAAY;QACtB;AAAA,MACD,CAAC,EAAE,KAAK,aAAW;;AAClBA,sBAAG,MAAC,YAAW;AAGf,YAAI,QAAQ,eAAe,QAAQ,WAAW,QAAQ,OAAO,QAAQ,WAAW,UAAU,MAAM;AAE/F,cAAI,QAAQ,cAAc,QAAQ,UAAU,QAAQ,OAAO,QAAQ,UAAU,UAAU,MAAM;AAC5F,iBAAK,YAAY,iBAAiB;AAAA,qBACxB,QAAQ,UAAU;AAC5B,iBAAK,YAAY,iBAAiB;AAAA,iBAC5B;AACN,iBAAK,YAAY,kBAAkB;AAAA,UACpC;AAGA,eAAK,iBAAiB;AAGtB,eAAK,mBAAmB,KAAK,MAAM,KAAK,UAAU,KAAK,QAAQ,CAAC;AAGhE,cAAI,iBAAiB,KAAK;AAC1B,2BAAiB,EAAC,GAAG,gBAAgB,GAAG,KAAK,SAAQ;AACrDA,wBAAAA,MAAI,eAAe,aAAa,cAAc;AAG9C,qBAAW,MAAM;AAChB,iBAAK,eAAc;AAAA,UACnB,GAAE,GAAI;AAAA,eACD;AACN,eAAK,cAAY,aAAQ,eAAR,mBAAoB,QAAO,YAAY;AAAA,QACzD;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAc,MAAA,MAAA,SAAA,kCAAA,SAAS,GAAG;AAC1B,aAAK,YAAY,YAAY,OAAO,QAAQ,WAAW,MAAM,OAAO;AAAA,MACrE,CAAC;AAAA,IACD;AAAA,IAED,iBAAiB,QAAQ;AACxB,UAAI,QAAQ;AACX,aAAK,aAAa,KAAK,SAAS;AAChC,aAAK,MAAM,YAAY;aACjB;AACN,aAAK,MAAM,YAAY;MACxB;AAAA,IACA;AAAA;AAAA,IAGD,aAAa,KAAK;AACjB,WAAK,UAAU;AAAA,IACf;AAAA;AAAA,IAGD,aAAa;AACZ,UAAI,KAAK,SAAS;AACjB,aAAK,SAAS,MAAM,KAAK;AACzB,aAAK,UAAU;AAAA,MAChB;AACA,WAAK,MAAM,SAAS;IACpB;AAAA;AAAA,IAGD,cAAc,QAAQ;AACrB,YAAM,OAAO;AACb,UAAI,QAAQ;AAEXA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAGD,aAAK,gBAAgB;AAGrBE,yCAAuB,EAAC,KAAK,SAAS,KAAK;AAC1CF,8BAAY,MAAA,OAAA,kCAAA,eAAe,KAAK,UAAU,GAAG,CAAC;AAG9C,gBAAM,YAAY,IAAI,QAAQ,OAAO,IAAI,UAAU,OAAO,IAAI,QAAQ;AAEtE,cAAI,aAAa,IAAI,MAAM;AAE1B,gBAAI,MAAM,QAAQ,IAAI,IAAI,KAAK,IAAI,KAAK,WAAW,GAAG;AACrDA,4BAAG,MAAC,YAAW;AACf,mBAAK,YAAY,mBAAmB;AACpC;AAAA,YACD;AAGA,iBAAK,gBAAgB,GAAG,EACtB,KAAK,MAAM;AAEX,mBAAK,uBAAsB;AAC3BA,4BAAG,MAAC,YAAW;AACf,mBAAK,MAAM,SAAS;aACpB,EACA,MAAM,SAAO;AACbA,4BAAA,MAAA,MAAA,SAAA,kCAAc,aAAa,GAAG;AAC9BA,4BAAG,MAAC,YAAW;AACf,mBAAK,YAAY,gBAAgB,IAAI,WAAW,OAAO;AAAA,YACxD,CAAC;AAAA,iBACI;AACNA,gCAAc,MAAA,SAAA,kCAAA,WAAW,IAAI,OAAO,MAAM;AAC1CA,0BAAG,MAAC,YAAW;AACf,iBAAK,YAAY,cAAc,IAAI,OAAO,OAAO;AAAA,UAClD;AAAA,QACD,CAAC,EAAE,MAAM,CAAC,QAAQ;AACjBA,+EAAc,aAAa,GAAG;AAC9BA,wBAAG,MAAC,YAAW;AACf,eAAK,YAAY,YAAY;AAAA,QAC9B,CAAC;AAAA,aACK;AACN,aAAK,MAAM,SAAS;MACrB;AAAA,IACA;AAAA;AAAA,IAGD,gBAAgB,UAAU;AACzB,YAAM,OAAO;AACb,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,YAAI;AACHA,8BAAY,MAAA,OAAA,kCAAA,aAAa,KAAK,UAAU,QAAQ,CAAC;AAGjD,cAAI,CAAC,YAAY,OAAO,aAAa,UAAU;AAC9CA,0BAAAA,uDAAc,UAAU;AACxB,mBAAO,IAAI,MAAM,UAAU,CAAC;AAC5B;AAAA,UACD;AAGA,cAAI,OAAO,SAAS;AAGpB,cAAI,CAAC,QAAQ,CAAC,MAAM,QAAQ,IAAI,GAAG;AAClCA,0BAAAA,MAAc,MAAA,SAAA,kCAAA,oBAAoB;AAClC,mBAAO,IAAI,MAAM,oBAAoB,CAAC;AACtC;AAAA,UACD;AAGA,cAAI,KAAK,WAAW,GAAG;AACtBA,0BAAAA,MAAc,MAAA,SAAA,kCAAA,gBAAgB;AAC9B,mBAAO,IAAI,MAAM,gBAAgB,CAAC;AAClC;AAAA,UACD;AAGA,eAAK,iBAAiB;AAGtB,eAAK,gBAAgB,KAAK,IAAI,cAAY;AACzC,gBAAI,CAAC,YAAY,OAAO,aAAa,UAAU;AAC9CA,4BAAA,MAAA,MAAA,SAAA,kCAAc,WAAW,QAAQ;AACjC,qBAAO;AAAA,gBACN,MAAM;AAAA,gBACN,IAAI;AAAA,gBACJ,MAAM,CAAE;AAAA,gBACR,UAAU,CAAC;AAAA;YAEb;AAGA,kBAAM,eAAe,MAAM,QAAQ,SAAS,IAAI,IAAI,SAAS,OAAO;AACpEA,0BAAAA,MAAA,MAAA,OAAA,kCAAY,MAAM,SAAS,IAAI,aAAa,KAAK,UAAU,YAAY,CAAC;AAGxE,kBAAM,WAAW,aAAa,IAAI,SAAO;AACxC,kBAAI,OAAO,OAAO,QAAQ,YAAY,IAAI,MAAM;AAE/C,oBAAI,IAAI,IAAI;AACX,uBAAK,eAAe,IAAI,IAAI,IAAI,IAAI;AACpCA,gCAAAA,MAAA,MAAA,OAAA,kCAAY,iBAAiB,IAAI,IAAI,OAAO,IAAI,EAAE,EAAE;AAAA,gBACrD;AACA,uBAAO,IAAI;AAAA,cACZ;AACAA,4BAAA,MAAA,MAAA,QAAA,kCAAa,WAAW,GAAG;AAC3B,qBAAO;AAAA,YACP,CAAA,EAAE,OAAO,UAAQ,SAAS,IAAI;AAE/BA,0BAAAA,MAAA,MAAA,OAAA,kCAAY,MAAM,SAAS,IAAI,WAAW,SAAS,KAAK,IAAI,KAAK,KAAK;AAEtE,mBAAO;AAAA,cACN,MAAM,SAAS,QAAQ;AAAA,cACvB,IAAI,SAAS,MAAM;AAAA,cACnB,MAAM;AAAA;AAAA,cACN,UAAU;AAAA;AAAA;UAEZ,CAAC;AAEDA,8BAAY,MAAA,OAAA,kCAAA,qBAAqB,KAAK,UAAU,KAAK,aAAa,CAAC;AAGnE,gBAAM,gBAAgB,CAAC,GAAG,KAAK,aAAa;AAC5C,eAAK,gBAAgB,KAAK,cAAc;AAAA,YAAO,cAC9C,SAAS,QAAQ,SAAS,KAAK,SAAS;AAAA;AAGzCA,8BAAA,MAAA,OAAA,kCAAY,eAAe,KAAK,UAAU,KAAK,aAAa,CAAC;AAC7DA,wBAAY,MAAA,MAAA,OAAA,kCAAA,iBAAiB,KAAK,cAAc;AAEhD,cAAI,KAAK,cAAc,WAAW,GAAG;AACpCA,0BAAAA,MAAa,MAAA,QAAA,kCAAA,oBAAoB;AAEjC,gBAAI,cAAc,SAAS,GAAG;AAC7B,mBAAK,gBAAgB;AACrBA,kCAAY,MAAA,OAAA,kCAAA,mBAAmB,KAAK,UAAU,KAAK,aAAa,CAAC;AAAA,mBAC3D;AACNA,4BAAAA,MAAA,MAAA,QAAA,kCAAa,eAAe;AAC5B,qBAAO,IAAI,MAAM,cAAc,CAAC;AAChC;AAAA,YACD;AAAA,UACD;AAGA,eAAK,WAAU;AACfA,wBAAAA,MAAY,MAAA,OAAA,kCAAA,aAAa;AACzB;QACC,SAAO,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,kCAAA,iBAAiB,KAAK;AACpC,iBAAO,KAAK;AAAA,QACb;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,yBAAyB;AAExB,UAAI,KAAK,SAAS,KAAK;AACtB,aAAK,eAAe,KAAK,SAAS,IAAI,MAAM,IAAI,EAAE,OAAO,SAAO,GAAG;AACnEA,sBAAY,MAAA,MAAA,OAAA,kCAAA,iBAAiB,KAAK,YAAY;AAAA,aACxC;AACN,aAAK,eAAe;MACrB;AAAA,IACA;AAAA;AAAA,IAGD,oBAAoB;AACnB,YAAM,OAAO;AACbA,oBAAAA,MAAA,MAAA,OAAA,kCAAY,eAAe;AAG3B,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEvCE,yCAAuB,EAAC,KAAK,SAAS,KAAK;AAC1CF,8BAAY,MAAA,OAAA,kCAAA,eAAe,KAAK,UAAU,GAAG,CAAC;AAG9C,gBAAM,YAAY,IAAI,QAAQ,OAAO,IAAI,UAAU,OAAO,IAAI,QAAQ;AAEtE,cAAI,WAAW;AAEd,gBAAI,CAAC,IAAI,MAAM;AACdA,4BAAAA,MAAA,MAAA,OAAA,kCAAY,cAAc;AAC1B,mBAAK,YAAY,QAAQ;AACzB,sBAAQ,CAAE,CAAA;AACV;AAAA,YACD;AAGA,gBAAI,MAAM,QAAQ,IAAI,IAAI,KAAK,IAAI,KAAK,WAAW,GAAG;AACrDA,4BAAAA,MAAY,MAAA,OAAA,kCAAA,gBAAgB;AAC5B,mBAAK,YAAY,QAAQ;AACzB,sBAAQ,CAAE,CAAA;AACV;AAAA,YACD;AAGA,iBAAK,gBAAgB,GAAG,EACtB,KAAK,OAAO,EACZ,MAAM,WAAS;AACfA,4BAAc,MAAA,MAAA,SAAA,kCAAA,aAAa,KAAK;AAChC,mBAAK,YAAY,UAAU;AAC3B,sBAAQ,CAAE,CAAA;AAAA,YACX,CAAC;AAAA,iBACI;AACNA,gCAAA,MAAA,SAAA,kCAAc,gBAAgB,IAAI,OAAO,MAAM;AAC/C,iBAAK,YAAY,gBAAgB,IAAI,OAAO,OAAO;AACnD,mBAAO,IAAI,MAAM,IAAI,OAAO,QAAQ,CAAC;AAAA,UACtC;AAAA,QACD,CAAC,EAAE,MAAM,CAAC,QAAQ;AACjBA,wBAAA,MAAA,MAAA,SAAA,kCAAc,kBAAkB,GAAG;AACnC,eAAK,YAAY,YAAY;AAC7B,iBAAO,GAAG;AAAA,QACX,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACd,YAAM,OAAO;AACbA,oBAAAA,MAAA,MAAA,OAAA,kCAAY,YAAY;AAGxB,WAAK,iBAAiB;AAGtBE,uCAAuB,EAAC,KAAK,SAAS,KAAK;AAC1C,YAAI,IAAI,QAAQ,OAAO,IAAI,MAAM;AAChC,eAAK,gBAAgB,GAAG,EACtB,KAAK,MAAM;AACXF,0BAAG,MAAC,YAAW;AACfA,0BAAAA,qDAAY,UAAU;AAAA,WACtB,EACA,MAAM,SAAO;AACbA,0BAAc,MAAA,MAAA,SAAA,kCAAA,aAAa,GAAG;AAC9BA,0BAAG,MAAC,YAAW;AACf,iBAAK,YAAY,UAAU;AAAA,UAC5B,CAAC;AAAA,eACI;AACNA,wBAAA,MAAA,MAAA,SAAA,kCAAc,aAAa,IAAI,GAAG;AAClCA,wBAAG,MAAC,YAAW;AACf,eAAK,YAAY,cAAc,IAAI,OAAO,OAAO;AAAA,QAClD;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,kCAAc,eAAe,GAAG;AAChCA,sBAAG,MAAC,YAAW;AACf,aAAK,YAAY,YAAY;AAAA,MAC9B,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,aAAa;AAClB,UAAI;AACH,cAAM,MAAM,MAAMK,WAAAA;AAClBL,4BAAY,MAAA,OAAA,kCAAA,eAAe,KAAK,UAAU,GAAG,CAAC;AAE9C,YAAI,IAAI,WAAW,OAAO,IAAI,MAAM;AAEnC,eAAK,SAAS,IAAI;AAElB,eAAK,eAAe,IAAI,KAAK,IAAI,SAAO,IAAI,IAAI;AAChD,eAAK,kBAAkB,CAAC,GAAG,KAAK,YAAY;AAG5C,eAAK,SAAS,gBAAgB,KAAK;AAEnC,iBAAO,IAAI;AAAA,QACZ;AACA,eAAO;MACN,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,kCAAc,aAAa,KAAK;AAChC,eAAO;MACR;AAAA,IACA;AAAA;AAAA,IAGD,qBAAqB,OAAO;AAC3B,YAAM,iBAAiB;AAAA,QACtB;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QACrB;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA,QACrB;AAAA,QAAO;AAAA,QAAO;AAAA,QAAO;AAAA;AAGtB,UAAI,UAAU,QAAQ,SAAS,KAAK,QAAQ,eAAe,QAAQ;AAClE,eAAO,eAAe,KAAK;AAAA,MAC5B;AACA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,WAAW,GAAG;AACb,UAAI,OAAO;AACX,UAAI,QAAQ,EAAE,OAAO;AAErB,UAAI,CAAC,MAAM,UAAU;AACpB,eAAO,KAAK,YAAY,OAAO;AAAA,MAChC;AAGA,WAAK,SAAS,WAAW,MAAM;AAG/B,WAAK,eAAc;AAAA,IACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAgBD,mBAAmB,QAAQ;AAC1B,YAAM,OAAO;AACb,UAAI,QAAQ;AAEX,aAAK,mBAAkB;AACvB,aAAK,MAAM,cAAc;aACnB;AACN,aAAK,MAAM,cAAc;MAC1B;AAAA,IACA;AAAA;AAAA,IAGD,qBAAqB;AAEpB,UAAI,KAAK,SAAS,UAAU;AAC3B,YAAI;AACH,cAAI,MAAM,OAAO;AAGjB,cAAI,OAAO,KAAK,SAAS,aAAa,YACpC,OAAO,KAAK,SAAS,aAAa,YAAY,CAAC,MAAM,KAAK,SAAS,QAAQ,KAAK,CAAC,KAAK,SAAS,SAAS,SAAS,GAAG,GAAI;AAEzH,kBAAM,YAAY,SAAS,KAAK,SAAS,QAAQ;AACjD,kBAAM,OAAO,IAAI,KAAK,YAAY,GAAI;AAEtC,mBAAO,KAAK;AACZ,oBAAQ,KAAK,SAAW,IAAE;AAC1B,kBAAM,KAAK;iBACL;AAEN,kBAAM,QAAQ,KAAK,SAAS,SAAS,MAAM,GAAG;AAC9C,gBAAI,MAAM,WAAW;AAAG;AAExB,mBAAO,SAAS,MAAM,CAAC,CAAC;AACxB,oBAAQ,SAAS,MAAM,CAAC,CAAC;AACzB,kBAAM,SAAS,MAAM,CAAC,CAAC;AAAA,UACxB;AAGA,gBAAM,YAAY,KAAK,MAAM,UAAU,OAAK,MAAM,IAAI;AACtD,gBAAM,aAAa,KAAK,OAAO,UAAU,OAAK,MAAM,KAAK;AACzD,gBAAM,WAAW,KAAK,KAAK,UAAU,OAAK,MAAM,GAAG;AAGnD,cAAI,cAAc,MAAM,eAAe,MAAM,aAAa,IAAI;AAC7D,iBAAK,sBAAsB,CAAC,WAAW,YAAY,QAAQ;AAC3DA,0BAAY,MAAA,MAAA,OAAA,kCAAA,aAAa,KAAK,qBAAqB,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,GAAG;AAAA,UAC/E;AAAA,QACD,SAAS,GAAG;AACXA,+EAAc,eAAe,CAAC;AAAA,QAC/B;AAAA,MACD;AAAA,IACA;AAAA,IAED,qBAAqB,GAAG;AACvB,WAAK,sBAAsB,EAAE,OAAO;AAAA,IACpC;AAAA;AAAA,IAGD,qCAAqC;AACpC,UAAI,CAAC,KAAK,SAAS;AAAU;AAE7B,UAAI;AACH,YAAI,OAAO;AAGX,YAAI,OAAO,KAAK,SAAS,aAAa,YACpC,OAAO,KAAK,SAAS,aAAa,YAAY,CAAC,MAAM,KAAK,SAAS,QAAQ,KAAK,CAAC,KAAK,SAAS,SAAS,SAAS,GAAG,GAAI;AAEzH,gBAAM,YAAY,SAAS,KAAK,SAAS,QAAQ;AACjD,gBAAM,OAAO,IAAI,KAAK,YAAY,GAAI;AAEtC,kBAAQ,KAAK,SAAW,IAAE;AAC1B,gBAAM,KAAK;AAEXA,wBAAY,MAAA,MAAA,OAAA,kCAAA,QAAQ,KAAK,SAAS,QAAQ,WAAW,KAAK,IAAI,GAAG,GAAG;AAAA,eAC9D;AAEN,gBAAM,QAAQ,KAAK,SAAS,SAAS,MAAM,GAAG;AAC9C,cAAI,MAAM,WAAW;AAAG;AAExB,kBAAQ,SAAS,MAAM,CAAC,GAAG,EAAE;AAC7B,gBAAM,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,QAC5B;AAGA,cAAM,gBAAgB,KAAK,uBAAuB,OAAO,GAAG;AAC5D,aAAK,SAAS,gBAAgB,cAAc;AAE5CA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,YAAY,cAAc,MAAM,OAAO,cAAc,OAAO,GAAG;AAG3E,YAAI,KAAK,iBAAiB,kBAAkB,QAC3C,KAAK,iBAAiB,kBAAkB,cAAc,OAAO;AAE7DA,wBAAAA,MAAY,MAAA,OAAA,kCAAA,YAAY;AAGxB,gBAAM,aAAa;AAAA,YAClB,eAAe,cAAc;AAAA;AAI9BG,qBAAAA,qBAAqB,UAAU,EAAE,KAAK,SAAO;AAC5CH,+EAAY,WAAW,KAAK,UAAU,GAAG,CAAC;AAAA,UAC3C,CAAC,EAAE,MAAM,SAAO;AACfA,0BAAc,MAAA,MAAA,SAAA,kCAAA,WAAW,GAAG;AAAA,UAC7B,CAAC;AAAA,QACF;AAAA,MACD,SAAS,GAAG;AACXA,6EAAc,YAAY,CAAC;AAAA,MAC5B;AAAA,IACA;AAAA;AAAA,IAGD,uBAAuB,OAAO,KAAK;AAElC,YAAM,qBAAqB;AAAA,QAC1B,EAAE,MAAM,OAAO,OAAO,EAAE,OAAO,GAAG,KAAK,GAAC,GAAK,KAAK,EAAE,OAAO,GAAG,KAAK,GAAG,EAAG;AAAA,QACzE,EAAE,MAAM,OAAO,OAAO,EAAE,OAAO,GAAG,KAAK,GAAC,GAAK,KAAK,EAAE,OAAO,GAAG,KAAK,GAAG,EAAG;AAAA,QACzE,EAAE,MAAM,OAAO,OAAO,EAAE,OAAO,GAAG,KAAK,GAAC,GAAK,KAAK,EAAE,OAAO,GAAG,KAAK,GAAG,EAAG;AAAA,QACzE,EAAE,MAAM,OAAO,OAAO,EAAE,OAAO,GAAG,KAAK,GAAC,GAAK,KAAK,EAAE,OAAO,GAAG,KAAK,GAAG,EAAG;AAAA,QACzE,EAAE,MAAM,OAAO,OAAO,EAAE,OAAO,GAAG,KAAK,GAAC,GAAK,KAAK,EAAE,OAAO,GAAG,KAAK,GAAG,EAAG;AAAA,QACzE,EAAE,MAAM,OAAO,OAAO,EAAE,OAAO,GAAG,KAAK,GAAC,GAAK,KAAK,EAAE,OAAO,GAAG,KAAK,GAAG,EAAG;AAAA,QACzE,EAAE,MAAM,OAAO,OAAO,EAAE,OAAO,GAAG,KAAK,GAAC,GAAK,KAAK,EAAE,OAAO,GAAG,KAAK,GAAG,EAAG;AAAA,QACzE,EAAE,MAAM,OAAO,OAAO,EAAE,OAAO,GAAG,KAAK,GAAC,GAAK,KAAK,EAAE,OAAO,GAAG,KAAK,GAAG,EAAG;AAAA,QACzE,EAAE,MAAM,OAAO,OAAO,EAAE,OAAO,GAAG,KAAK,GAAC,GAAK,KAAK,EAAE,OAAO,IAAI,KAAK,GAAG,EAAG;AAAA,QAC1E,EAAE,MAAM,OAAO,OAAO,EAAE,OAAO,IAAI,KAAK,GAAC,GAAK,KAAK,EAAE,OAAO,IAAI,KAAK,GAAG,EAAG;AAAA,QAC3E,EAAE,MAAM,OAAO,OAAO,EAAE,OAAO,IAAI,KAAK,GAAC,GAAK,KAAK,EAAE,OAAO,IAAI,KAAK,GAAG,EAAG;AAAA,QAC3E,EAAE,MAAM,OAAO,OAAO,EAAE,OAAO,IAAI,KAAK,GAAG,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,GAAC,EAAI;AAAA;AAI1E,eAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AACnD,cAAM,gBAAgB,mBAAmB,CAAC;AAG1C,YAAI,MAAM,IAAI;AACb,cAAK,UAAU,MAAM,OAAO,cAAc,MAAM,OAC9C,UAAU,KAAK,OAAO,cAAc,IAAI,KAAM;AAC/C,mBAAO,EAAE,MAAM,cAAc,MAAM,OAAO;UAC3C;AAAA,QACD,WAEU,UAAU,cAAc,MAAM,SAAS,OAAO,cAAc,MAAM,OACzE,UAAU,cAAc,IAAI,SAAS,OAAO,cAAc,IAAI,KAAM;AACtE,iBAAO,EAAE,MAAM,cAAc,MAAM,OAAO;QAC3C;AAAA,MACD;AAGA,aAAO,EAAE,MAAM,OAAO,OAAO,EAAA;AAAA,IAC7B;AAAA;AAAA,IAGD,kBAAkB;AACjB,YAAM,OAAO;AACb,YAAM,OAAO,KAAK,MAAM,KAAK,oBAAoB,CAAC,CAAC;AACnD,YAAM,QAAQ,KAAK,OAAO,KAAK,oBAAoB,CAAC,CAAC;AACrD,YAAM,MAAM,KAAK,KAAK,KAAK,oBAAoB,CAAC,CAAC;AAGjD,YAAM,eAAe,IAAI,KAAK,MAAM,QAAQ,GAAG,GAAG;AAGlD,YAAM,oBAAoB,KAAK,MAAM,aAAa,QAAO,IAAK,GAAI;AAGxC,SAAG,IAAI,IAAI,MAAM,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,IAAI,IAAI,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC;AAGzG,YAAM,gBAAgB,KAAK,uBAAuB,OAAO,GAAG;AAG5D,WAAK,SAAS,WAAW;AACzB,WAAK,SAAS,gBAAgB,cAAc;AAI5C,WAAK,MAAM,cAAc;IACzB;AAAA;AAAA,IAGD,gBAAgB,GAAG;AAClB,UAAI,OAAO;AACX,UAAI,EAAE,OAAO,UAAU,qBAAqB;AAC3CA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO,KAAK,SAAS,QAAQ,aAAa;AAAA,UAC1C,MAAM;AAAA,QACP,CAAC;AAGD,aAAK,MAAM,iBAAiB,EAAE,MAAM,EAAE,KAAK,SAAO;AACjDA,wBAAG,MAAC,YAAW;AACf,cAAI,IAAI,QAAQ,KAAK;AAEpB,iBAAK,SAAS,QAAQ,IAAI,KAAK,SAAS,IAAI,KAAK;AAEjD,iBAAK,YAAY,IAAI,OAAO,SAAS;AAAA,iBAC/B;AACN,iBAAK,YAAY,IAAI,OAAO,MAAM;AAAA,UACnC;AAAA,QACD,CAAC,EAAE,MAAM,SAAO;AACfA,wBAAG,MAAC,YAAW;AACf,eAAK,YAAY,YAAY,OAAO,QAAQ,WAAW,MAAM,OAAO;AAAA,QACrE,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,iBAAiB;AAChB,UAAI,OAAO;AACXA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAGD,YAAM,iBAAiBA,cAAAA,MAAI,eAAe,eAAe;AACzD,YAAM,kBAAkBA,cAAAA,MAAI,eAAe,eAAe;AAE1D,UAAI,kBAAkB,iBAAiB;AAEtC,aAAK,uBAAuB,WAAW,cAAc,GAAG,WAAW,eAAe,CAAC;AACnFA,sBAAG,MAAC,YAAW;AACf;AAAA,MACD;AAGA,WAAK,gBAAe;AAGpBC,mCAAmB,EAAC,KAAK,SAAS,KAAK;AACtCD,sBAAG,MAAC,YAAW;AACf,YAAI,IAAI,QAAQ,KAAK;AAEpB,gBAAM,WAAW,IAAI;AACrBA,wBAAAA,MAAI,eAAe,aAAa,QAAQ;AAGxC,cAAI,SAAS,gBAAgB;AAC5B,iBAAK,SAAS,iBAAiB,SAAS;AACxC,iBAAK,SAAS,WAAW,SAAS;AAAA,UACnC;AACA,cAAI,SAAS,YAAY,CAAC,SAAS,gBAAgB;AAClD,iBAAK,SAAS,WAAW,SAAS;AAAA,UACnC;AAEA,eAAK,mBAAmB,KAAK,MAAM,KAAK,UAAU,QAAQ,CAAC;AAC3D,eAAK,YAAY,UAAU;AAAA,eACrB;AACN,eAAK,YAAY,IAAI,OAAO,UAAU;AAAA,QACvC;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAG,MAAC,YAAW;AACf,aAAK,YAAY,YAAY,OAAO,QAAQ,WAAW,MAAM,OAAO;AAAA,MACrE,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AACnB,cAAQ,KAAK,SAAS,aAAW;AAAA,QAChC,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf;AAAS,iBAAO;AAAA,MACjB;AAAA,IACA;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,KAAK,WAAW,KAAM;AACjC,UAAI,OAAO;AACX,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU;AACrB,iBAAW,WAAW;AACrB,aAAK,MAAM,UAAU;MACrB,GAAE,QAAQ;AAAA,IACX;AAAA;AAAA,IAGD,gBAAgB,QAAQ;AACvB,WAAK,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,gBAAgB;AACf,UAAI,KAAK,eAAe,MAAM;AAE7B,aAAK,SAAS,MAAM,KAAK;AACzB,aAAK,YAAY,kBAAkB;AAAA,MACpC;AACA,WAAK,MAAM,YAAY;IACvB;AAAA;AAAA,IAGD,UAAU,KAAK;AACdA,oBAAA,MAAA,MAAA,OAAA,kCAAY,SAAS,GAAG;AACxBA,0BAAY,MAAA,OAAA,kCAAA,YAAY,KAAK,UAAU,KAAK,YAAY,CAAC;AAEzD,UAAI,CAAC,KAAK;AACTA,sBAAAA,MAAA,MAAA,SAAA,kCAAc,SAAS;AACvB,aAAK,YAAY,MAAM;AACvB;AAAA,MACD;AAGA,YAAM,QAAQ,KAAK,eAAe,GAAG;AACrCA,oBAAA,MAAA,MAAA,OAAA,kCAAY,SAAS,KAAK;AAE1B,UAAI,KAAK,aAAa,SAAS,GAAG,GAAG;AACpC,aAAK,eAAe,KAAK,aAAa,OAAO,OAAK,MAAM,GAAG;AAC3DA,4BAAA,MAAA,OAAA,kCAAY,aAAa,KAAK,UAAU,KAAK,YAAY,CAAC;AAC1D,aAAK,YAAY,SAAS,GAAG,EAAE;AAAA,MAChC,WAAW,KAAK,aAAa,SAAS,GAAG;AACxC,aAAK,aAAa,KAAK,GAAG;AAC1BA,4BAAA,MAAA,OAAA,kCAAY,WAAW,KAAK,UAAU,KAAK,YAAY,CAAC;AACxD,aAAK,YAAY,OAAO,GAAG,EAAE;AAAA,aACvB;AACN,aAAK,YAAY,cAAc;AAAA,MAChC;AAAA,IACA;AAAA;AAAA,IAGD,eAAe,OAAO;AACrB,WAAK,uBAAuB;AAAA,IAC5B;AAAA;AAAA,IAGD,aAAa,GAAG;AACf,WAAK,uBAAuB,EAAE,OAAO;AAAA,IACrC;AAAA;AAAA,IAGD,cAAc;AACb,YAAM,OAAO;AACb,UAAI,KAAK,aAAa,SAAS,GAAG;AACjC,aAAK,YAAY,cAAc;AAC/B;AAAA,MACD;AAGA,WAAK,SAAS,MAAM,KAAK,aAAa,KAAK,IAAI;AAG/C,YAAM,SAAS,CAAA;AACf,YAAM,WAAW,CAAA;AAGjB,iBAAW,WAAW,KAAK,cAAc;AACxC,cAAM,QAAQ,KAAK,eAAe,OAAO;AACzC,YAAI,OAAO;AACV,iBAAO,KAAK,KAAK;AACjB,mBAAS,KAAK,OAAO;AACrBA,8BAAY,MAAA,OAAA,kCAAA,WAAW,OAAO,UAAU,KAAK,EAAE;AAAA,eACzC;AACNA,8BAAa,MAAA,QAAA,kCAAA,WAAW,OAAO,OAAO;AAAA,QACvC;AAAA,MACD;AAEAA,oBAAY,MAAA,MAAA,OAAA,kCAAA,UAAU,QAAQ;AAC9BA,oBAAY,MAAA,MAAA,OAAA,kCAAA,WAAW,MAAM;AAE7B,UAAI,OAAO,WAAW,KAAK,KAAK,aAAa,SAAS,GAAG;AACxDA,sBAAAA,MAAc,MAAA,SAAA,kCAAA,sBAAsB;AACpC,aAAK,YAAY,cAAc;AAC/B;AAAA,MACD;AAGAA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAEDI,iBAAAA,eAAe,MAAM,EAAE,KAAK,SAAS,KAAK;AACxCJ,sBAAG,MAAC,YAAW;AAChB,YAAI,IAAI,QAAQ,KAAK;AAEpB,eAAK,YAAY,YAAY,GAAI;AAGjCC,uCAAmB,EAAC,KAAK,SAAS,QAAQ;AACzC,gBAAI,OAAO,QAAQ,KAAK;AACvBD,4BAAA,MAAA,MAAA,OAAA,kCAAY,aAAa,OAAO,IAAI;AACpCA,4BAAAA,MAAI,eAAe,aAAa,OAAO,IAAI;AAC3C,mBAAK,WAAW,OAAO;AACvB,mBAAK,mBAAmB,KAAK,MAAM,KAAK,UAAU,OAAO,IAAI,CAAC;AAC9D,mBAAK,uBAAsB;AAAA,YAC5B;AAAA,UACD,CAAC;AAAA,eACK;AACNA,+EAAc,WAAW,GAAG;AAC5B,eAAK,YAAY,cAAc,IAAI,OAAO,GAAG;AAAA,QAC9C;AAAA,MACD,CAAC,EAAE,MAAM,CAAC,QAAQ;AACjBA,sBAAG,MAAC,YAAW;AACfA,6EAAc,WAAW,GAAG;AAC5B,aAAK,YAAY,cAAc;AAAA,MAChC,CAAC;AAED,WAAK,MAAM,SAAS;IACpB;AAAA;AAAA,IAGD,eAAe,SAAS;AACvB,UAAI,CAAC,SAAS;AACbA,sBAAAA,MAAA,MAAA,SAAA,kCAAc,OAAO;AACrB,eAAO;AAAA,MACR;AAEAA,oBAAA,MAAA,MAAA,OAAA,kCAAY,eAAe,OAAO;AAGlC,UAAI,KAAK,kBAAkB,KAAK,eAAe,OAAO,GAAG;AACxDA,4BAAA,MAAA,OAAA,kCAAY,aAAa,KAAK,eAAe,OAAO,CAAC;AACrD,eAAO,KAAK,eAAe,OAAO;AAAA,MACnC;AAGA,iBAAW,YAAY,KAAK,eAAe;AAC1C,YAAI,SAAS,YAAY,MAAM,QAAQ,SAAS,QAAQ,GAAG;AAC1D,qBAAW,OAAO,SAAS,UAAU;AACpC,gBAAI,OAAO,IAAI,SAAS,SAAS;AAEhC,mBAAK,eAAe,OAAO,IAAI,IAAI;AACnCA,4BAAA,MAAA,MAAA,OAAA,kCAAY,aAAa,IAAI,EAAE;AAC/B,qBAAO,IAAI;AAAA,YACZ;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAGAA,0BAAa,MAAA,QAAA,kCAAA,UAAU,OAAO,OAAO;AACrC,aAAO;AAAA,IACP;AAAA,IAED,aAAa,KAAK;AAEjB,UAAI,CAAC;AAAK;AAEVA,oBAAAA,MAAI,aAAa;AAAA,QAChB,MAAM,CAAC,GAAG;AAAA,QACV,SAAS;AAAA,MACV,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,OAAO,OAAO;AAE3B,UAAI,CAAC;AAAO;AAGZ,YAAM,WAAW,UAAU,MAAM,MAAM,SAAS;AAChD,YAAM,YAAY,WAAW,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ,MAAM,MAAM;AAErEA,oBAAAA,MAAI,gBAAgB;AAAA,QACnB,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AACjB,cAAI,UAAU;AACb,oBAAO,IAAI,UAAQ;AAAA,cAClB,KAAK;AACJ,qBAAK,aAAa,MAAM,GAAG;AAC3B;AAAA,cACD,KAAK;AACJ,qBAAK,mBAAkB;AACvB;AAAA,YACF;AAAA,iBACM;AACN,oBAAO,IAAI,UAAQ;AAAA,cAClB,KAAK;AACJ,qBAAK,aAAa,MAAM,GAAG;AAC3B;AAAA,cACD,KAAK;AACJA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,SAAS;AAAA,kBACT,SAAS,CAACM,SAAQ;AACjB,wBAAIA,KAAI,SAAS;AAChB,2BAAK,iBAAiB,OAAO,OAAO,CAAC;AACrC,2BAAK,YAAY,OAAO;AAAA,oBACzB;AAAA,kBACD;AAAA,gBACD,CAAC;AACD;AAAA,cACD,KAAK;AACJN,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,SAAS;AAAA,kBACT,SAAS,CAACM,SAAQ;AACjB,wBAAIA,KAAI,SAAS;AAEhB,4BAAM,YAAY,KAAK,SAAS;AAGhC,2BAAK,SAAS,SAAS,MAAM;AAG7B,0BAAI,aAAa,CAAC,UAAU,SAAS,wBAAwB,GAAG;AAE/D,6BAAK,iBAAiB,KAAK,EAAE,MAAM;AAAA,sBACpC;AAEA,2BAAK,YAAY,gBAAgB;AAAA,oBAClC;AAAA,kBACD;AAAA,gBACD,CAAC;AACD;AAAA,YACF;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,UAAU,GAAG;AAEZ,WAAK,iBAAiB,SAAS,EAAE,cAAc,QAAQ,KAAK;AAC5D,WAAK,aAAa;AAGlB,WAAK,SAAS,EAAE,QAAQ,CAAC,EAAE;AAC3B,WAAK,SAAS,EAAE,QAAQ,CAAC,EAAE;AAAA,IAC3B;AAAA,IAED,SAAS,GAAG;AACX,UAAI,CAAC,KAAK;AAAY;AAGtB,YAAM,QAAQ,EAAE,QAAQ,CAAC,EAAE,UAAU,KAAK;AAC1C,YAAM,QAAQ,EAAE,QAAQ,CAAC,EAAE,UAAU,KAAK;AAG1C,UAAI,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI;AAAI;AAGlD,WAAK,aAAa;AAGlB,YAAM,QAAQ,EAAE,QAAQ,CAAC;AACzB,YAAM,UAAU,SAAS,iBAAiB,MAAM,SAAS,MAAM,OAAO;AAEtE,UAAI,SAAS;AAEZ,YAAI,YAAY,QAAQ,QAAQ,aAAa;AAC7C,YAAI,aAAa,UAAU,WAAW,UAAU,QAAQ,UAAU,QAAW;AAC5E,gBAAM,cAAc,SAAS,UAAU,QAAQ,KAAK;AAGpD,cAAI,gBAAgB,KAAK,kBAAkB,eAAe,KAAK,cAAc,KAAK,iBAAiB,QAAQ;AAE1G,kBAAM,OAAO,KAAK,iBAAiB,KAAK,cAAc;AAGtD,iBAAK,iBAAiB,OAAO,KAAK,gBAAgB,CAAC;AAGnD,iBAAK,iBAAiB,OAAO,aAAa,GAAG,IAAI;AAGjD,iBAAK,iBAAiB;AAAA,UACvB;AAAA,QACD;AAAA,MACD;AAAA,IACA;AAAA,IAED,QAAQ,GAAG;AACV,WAAK,aAAa;AAAA,IAClB;AAAA,IAED,kBAAkB,QAAQ;AACzB,UAAI,QAAQ;AAEXN,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAGDE,yCAAuB,EAAC,KAAK,SAAO;AACnCF,8BAAA,MAAA,OAAA,kCAAY,aAAa,KAAK,UAAU,GAAG,CAAC;AAG5C,gBAAM,YAAY,IAAI,QAAQ,OAAO,IAAI,UAAU,OAAO,IAAI,QAAQ;AAEtE,cAAI,aAAa,IAAI,MAAM;AAE1B,gBAAI,MAAM,QAAQ,IAAI,IAAI,KAAK,IAAI,KAAK,WAAW,GAAG;AACrDA,4BAAG,MAAC,YAAW;AACf,mBAAK,YAAY,mBAAmB;AACpC;AAAA,YACD;AAEAA,0BAAAA,MAAY,MAAA,OAAA,kCAAA,eAAe;AAE3B,gBAAI;AACH,kBAAI,MAAM,QAAQ,IAAI,IAAI,GAAG;AAC5B,qBAAK,mBAAmB,IAAI,IAAI;AAAA,cACjC,WAAW,OAAO,IAAI,SAAS,UAAU;AACxC,sBAAM,YAAY,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,OAAO,OAAO,IAAI,IAAI;AAC1E,oBAAI,MAAM,QAAQ,SAAS,KAAK,UAAU,SAAS,GAAG;AACrD,uBAAK,mBAAmB,SAAS;AAAA,uBAC3B;AACNA,gCAAAA,MAAc,MAAA,SAAA,kCAAA,cAAc;AAC5BA,gCAAG,MAAC,YAAW;AACf,uBAAK,YAAY,UAAU;AAC3B;AAAA,gBACD;AAAA,qBACM;AACNA,8BAAA,MAAA,MAAA,SAAA,kCAAc,YAAY,OAAO,IAAI,IAAI;AACzCA,8BAAG,MAAC,YAAW;AACf,qBAAK,YAAY,WAAW;AAC5B;AAAA,cACD;AAAA,YACC,SAAO,KAAK;AACbA,4BAAc,MAAA,MAAA,SAAA,kCAAA,cAAc,GAAG;AAC/BA,4BAAG,MAAC,YAAW;AACf,mBAAK,YAAY,UAAU;AAC3B;AAAA,YACD;AAGA,iBAAK,eAAe,EAClB,MAAM,SAAO;AACbA,4BAAA,MAAA,MAAA,SAAA,kCAAc,yBAAyB,GAAG;AAAA,aAC1C,EACA,QAAQ,MAAM;AACd,mBAAK,0BAAyB;AAC9BA,4BAAG,MAAC,YAAW;AACf,mBAAK,MAAM,aAAa;YACzB,CAAC;AAAA,iBACI;AACNA,gCAAc,MAAA,SAAA,kCAAA,iBAAiB,IAAI,QAAQ,IAAI,MAAM;AACrDA,0BAAG,MAAC,YAAW;AACf,iBAAK,YAAY,gBAAgB,IAAI,OAAO,OAAO;AAAA,UACpD;AAAA,QACD,CAAC,EAAE,MAAM,SAAO;AACfA,wBAAA,MAAA,MAAA,SAAA,kCAAc,eAAe,GAAG;AAChCA,wBAAG,MAAC,YAAW;AACf,eAAK,YAAY,YAAY;AAAA,QAC9B,CAAC;AAAA,aACK;AACN,aAAK,MAAM,aAAa;MACzB;AAAA,IACA;AAAA;AAAA,IAGD,mBAAmB,MAAM;AACxBA,oBAAY,MAAA,MAAA,OAAA,kCAAA,aAAa,OAAO,SAAS,WAAY,MAAM,QAAQ,IAAI,IAAI,SAAS,SAAU,OAAO,IAAI;AAEzG,UAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACzBA,sBAAc,MAAA,MAAA,SAAA,kCAAA,YAAY;AAE1B,YAAI,QAAQ,OAAO,SAAS,UAAU;AAErC,iBAAO,KAAK,QAAQ,KAAK,QAAQ,OAAO,OAAO,IAAI;AACnD,cAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACzBA,0BAAA,MAAA,MAAA,SAAA,kCAAc,YAAY;AAC1B;AAAA,UACD;AAAA,eACM;AACNA,wBAAc,MAAA,MAAA,SAAA,kCAAA,aAAa;AAC3B;AAAA,QACD;AAAA,MACD;AAEAA,0BAAY,MAAA,OAAA,kCAAA,iBAAiB,KAAK,MAAM;AAGxC,WAAK,mBAAmB;AAGxB,WAAK,mBAAmB,KAAK,IAAI,cAAY;AAE5C,YAAI,YAAY,SAAS,QAAQ,SAAS,MAAM,CAAC,SAAS,MAAM;AAC/D,iBAAO;AAAA,YACN,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,MAAM,CAAC,SAAS,IAAI;AAAA,YACpB,cAAc,CAAC,QAAQ;AAAA;QAEzB;AAGA,YAAI,OAAO,CAAA;AACX,YAAI,MAAM,QAAQ,SAAS,IAAI,GAAG;AACjC,iBAAO,SAAS;AAAA,mBACN,SAAS,YAAY,MAAM,QAAQ,SAAS,QAAQ,GAAG;AACjE,iBAAO,SAAS;AAAA,mBACN,SAAS,WAAW,MAAM,QAAQ,SAAS,OAAO,GAAG;AAC/D,iBAAO,SAAS;AAAA,QACjB;AAEAA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,SAAS,SAAS,IAAI,WAAW,KAAK,MAAM;AAGxD,cAAM,WAAW,KAAK,IAAI,SAAO;AAChC,cAAI,OAAO,QAAQ,UAAU;AAC5B,mBAAO;AAAA,qBACG,OAAO,IAAI,MAAM;AAC3B,mBAAO,IAAI;AAAA,UACZ;AACA,iBAAO;AAAA,QACP,CAAA,EAAE,OAAO,UAAQ,SAAS,IAAI;AAE/B,eAAO;AAAA,UACN,IAAI,SAAS,MAAM;AAAA,UACnB,MAAM,SAAS,QAAQ;AAAA,UACvB,MAAM;AAAA,UACN,cAAc;AAAA;MAEhB,CAAC;AAGD,WAAK,mBAAmB,KAAK,iBAAiB;AAAA,QAAO,cACpD,SAAS,QAAQ,SAAS,KAAK,SAAS;AAAA;AAGzCA,0BAAY,MAAA,OAAA,kCAAA,kBAAkB,KAAK,iBAAiB,MAAM;AAC1D,UAAI,KAAK,iBAAiB,WAAW,GAAG;AACvCA,6EAAc,cAAc;AAC5B;AAAA,MACD;AAGA,WAAK,0BAAyB;AAE9BA,oBAAY,MAAA,MAAA,OAAA,kCAAA,WAAW;AAAA,IACvB;AAAA;AAAA,IAGD,4BAA4B;AAC3B,YAAM,OAAO;AAEbA,oBAAAA,MAAA,MAAA,OAAA,kCAAY,cAAc;AAC1BA,oBAAY,MAAA,MAAA,OAAA,kCAAA,WAAW,KAAK,cAAc;AAC1CA,oBAAA,MAAA,MAAA,OAAA,kCAAY,aAAa,KAAK,eAAe;AAG7C,UAAI,KAAK,kBAAkB,KAAK,eAAe,SAAS,GAAG;AAC1DA,2EAAY,kBAAkB,KAAK,cAAc;AAGjD,aAAK,kBAAkB;AAGvB,aAAK,iBAAiB,QAAQ,cAAY;AACzC,cAAI,SAAS,gBAAgB,MAAM,QAAQ,SAAS,YAAY,GAAG;AAClE,qBAAS,aAAa,QAAQ,SAAO;AAEpC,oBAAM,QAAQ,IAAI,MAAM,IAAI;AAC5B,kBAAI,SAAS,KAAK,eAAe,SAAS,KAAK,GAAG;AACjD,oBAAI,IAAI,MAAM;AACb,uBAAK,gBAAgB,KAAK,IAAI,IAAI;AAClCA,gCAAAA,MAAA,MAAA,OAAA,kCAAY,SAAS,IAAI,IAAI,SAAS,KAAK,EAAE;AAAA,gBAC9C;AAAA,cACD;AAAA,YACD,CAAC;AAAA,UACF;AAAA,QACD,CAAC;AAEDA,2EAAY,gBAAgB,KAAK,eAAe;AAGhD,YAAI,KAAK,gBAAgB,WAAW,KAAK,KAAK,SAAS,KAAK;AAC3DA,wBAAAA,MAAA,MAAA,OAAA,kCAAY,8BAA8B;AAC1C,eAAK,kBAAkB,KAAK,SAAS,IAAI,MAAM,IAAI,EAAE,OAAO,SAAO,GAAG;AACtEA,6EAAY,uBAAuB,KAAK,eAAe;AAAA,QACxD;AAGA,YAAI,KAAK,gBAAgB,SAAS,GAAG;AACpC,eAAK,SAAS,MAAM,KAAK,gBAAgB,KAAK,IAAI;AAAA,QACnD;AAAA,MACD,WAES,KAAK,SAAS,KAAK;AAC3BA,sBAAAA,MAAY,MAAA,OAAA,kCAAA,oBAAoB;AAChC,aAAK,kBAAkB,KAAK,SAAS,IAAI,MAAM,IAAI,EAAE,OAAO,SAAO,GAAG;AACtEA,2EAAY,kBAAkB,KAAK,eAAe;AAGlD,aAAK,iBAAiB;AACtB,aAAK,gBAAgB,QAAQ,aAAW;AACvC,gBAAM,QAAQ,KAAK,kBAAkB,OAAO;AAC5C,cAAI,OAAO;AACV,iBAAK,eAAe,KAAK,KAAK;AAC9BA,+EAAY,QAAQ,OAAO,SAAS,KAAK,EAAE;AAAA,iBACrC;AACNA,gCAAA,MAAA,OAAA,kCAAY,SAAS,OAAO,MAAM;AAAA,UACnC;AAAA,QACD,CAAC;AAEDA,2EAAY,gBAAgB,KAAK,cAAc;AAAA,MAChD,OACK;AACJA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,UAAU;AACtB,aAAK,kBAAkB;AACvB,aAAK,iBAAiB;MACvB;AAEAA,0BAAY,MAAA,OAAA,kCAAA,oBAAoB,KAAK,gBAAgB,MAAM;AAAA,IAC3D;AAAA,IAED,kBAAkB,OAAO;AACxB,WAAK,mBAAmB;AAAA,IACxB;AAAA,IAED,aAAa,KAAK;AACjBA,oBAAA,MAAA,MAAA,OAAA,kCAAY,SAAS,GAAG;AACxBA,0BAAA,MAAA,OAAA,kCAAY,YAAY,KAAK,UAAU,KAAK,eAAe,CAAC;AAE5D,UAAI,CAAC,KAAK;AACTA,sBAAAA,MAAA,MAAA,SAAA,kCAAc,SAAS;AACvB,aAAK,YAAY,MAAM;AACvB;AAAA,MACD;AAGA,YAAM,QAAQ,KAAK,kBAAkB,GAAG;AAExC,UAAI,OAAO;AACVA,sBAAA,MAAA,MAAA,OAAA,kCAAY,WAAW,KAAK;AAE5B,YAAI,KAAK,gBAAgB,SAAS,GAAG,GAAG;AAEvC,eAAK,kBAAkB,KAAK,gBAAgB,OAAO,OAAK,MAAM,GAAG;AAGjE,eAAK,iBAAiB,KAAK,eAAe,OAAO,QAAM,OAAO,KAAK;AAEnEA,8BAAA,MAAA,OAAA,kCAAY,aAAa,KAAK,UAAU,KAAK,eAAe,CAAC;AAC7DA,8BAAA,MAAA,OAAA,kCAAY,eAAe,KAAK,UAAU,KAAK,cAAc,CAAC;AAC9D,eAAK,YAAY,SAAS,GAAG,EAAE;AAAA,QAChC,WAAW,KAAK,gBAAgB,SAAS,GAAG;AAE3C,eAAK,gBAAgB,KAAK,GAAG;AAG7B,eAAK,eAAe,KAAK,KAAK;AAE9BA,8BAAA,MAAA,OAAA,kCAAY,WAAW,KAAK,UAAU,KAAK,eAAe,CAAC;AAC3DA,8BAAA,MAAA,OAAA,kCAAY,aAAa,KAAK,UAAU,KAAK,cAAc,CAAC;AAC5D,eAAK,YAAY,OAAO,GAAG,EAAE;AAAA,eACvB;AACN,eAAK,YAAY,cAAc;AAAA,QAChC;AAAA,aACM;AACNA,sBAAA,MAAA,MAAA,SAAA,kCAAc,cAAc,GAAG;AAC/B,aAAK,YAAY,aAAa;AAAA,MAC/B;AAAA,IACA;AAAA,IAED,iBAAiB;AAChB,YAAM,OAAO;AACb,UAAI,KAAK,gBAAgB,SAAS,GAAG;AACpC,aAAK,YAAY,cAAc;AAC/B;AAAA,MACD;AAGAA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAGD,YAAM,SAAS,KAAK,gBAAgB,IAAI,aAAW;AAClD,mBAAW,YAAY,KAAK,kBAAkB;AAC7C,cAAI,SAAS,cAAc;AAC1B,kBAAM,SAAS,SAAS,aAAa,KAAK,OAAK,EAAE,SAAS,OAAO;AACjE,gBAAI;AAAQ,qBAAO,OAAO;AAAA,UAC3B;AAAA,QACD;AACA,eAAO;AAAA,MACP,CAAA,EAAE,OAAO,QAAM,OAAO,IAAI;AAG3BI,iBAAAA,eAAe,MAAM,EAAE,KAAK,SAAO;AAClC,YAAI,IAAI,SAAS,KAAK;AAErB,eAAK,SAAS,gBAAgB,CAAC,GAAG,KAAK,eAAe;AAGtD,gBAAM,iBAAiBJ,cAAAA,MAAI,eAAe,WAAW;AACrD,cAAI,gBAAgB;AACnB,2BAAe,gBAAgB,CAAC,GAAG,KAAK,eAAe;AACvDA,0BAAAA,MAAI,eAAe,aAAa,cAAc;AAAA,UAC/C;AAGAA,wBAAG,MAAC,MAAM,kBAAkB;AAAA,YAC3B,MAAM,KAAK;AAAA,YACX,QAAQ,KAAK,SAAS;AAAA,UACvB,CAAC;AAEDA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAGH,eAAK,MAAM,aAAa;eAChB;AACN,gBAAM,IAAI,MAAM,IAAI,OAAO,MAAM;AAAA,QAClC;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,6EAAc,WAAW,GAAG;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,IAAI,WAAW;AAAA,UACtB,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC,EAAE,QAAQ,MAAM;AAChBA,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,SAAS;AAC1B,UAAI,CAAC,SAAS;AACbA,sBAAAA,MAAA,MAAA,SAAA,kCAAc,OAAO;AACrB,eAAO;AAAA,MACR;AAEAA,oBAAA,MAAA,MAAA,OAAA,kCAAY,gBAAgB,OAAO;AAGnC,iBAAW,YAAY,KAAK,kBAAkB;AAC7C,YAAI,SAAS,gBAAgB,MAAM,QAAQ,SAAS,YAAY,GAAG;AAElE,qBAAW,OAAO,SAAS,cAAc;AACxC,gBAAI,OAAO,IAAI,SAAS,SAAS;AAChCA,4BAAY,MAAA,MAAA,OAAA,kCAAA,cAAc,IAAI,EAAE;AAChC,qBAAO,IAAI;AAAA,YACZ;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAGAA,0BAAa,MAAA,QAAA,kCAAA,WAAW,OAAO,OAAO;AACtC,aAAO;AAAA,IACP;AAAA,IAED,gBAAgB,GAAG;AAClB,WAAK,mBAAmB,EAAE,OAAO;AAAA,IACjC;AAAA;AAAA,IAGD,cAAc;AACb,YAAM,OAAO;AAEb,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCC,qCAAmB,EAAC,KAAK,SAAS,KAAK;AACtC,cAAI,IAAI,QAAQ,OAAO,IAAI,UAAU,KAAK;AACzCD,gCAAY,MAAA,OAAA,kCAAA,aAAa,KAAK,UAAU,IAAI,IAAI,CAAC;AACjD,kBAAM,WAAW,IAAI,QAAQ,IAAI;AACjC,gBAAI,UAAU;AACbA,4BAAAA,MAAI,eAAe,aAAa,QAAQ;AACxC,mBAAK,WAAW;AAChB,mBAAK,mBAAmB,KAAK,MAAM,KAAK,UAAU,QAAQ,CAAC;AAG3D,kBAAI,KAAK,SAAS,YAAY,KAAK,SAAS,kBAAkB,MAAM;AACnE,qBAAK,mCAAkC;AAAA,cACxC;AAGA,mBAAK,uBAAsB;AAE3B,sBAAQ,QAAQ;AAAA,YACjB;AAAA,iBACM;AACNA,iFAAc,aAAa,IAAI,OAAO,MAAM;AAC5C,mBAAO,IAAI,MAAM,IAAI,OAAO,UAAU,CAAC;AAAA,UACxC;AAAA,QACD,CAAC,EAAE,MAAM,SAAO;AACfA,+EAAc,aAAa,GAAG;AAC9B,iBAAO,GAAG;AAAA,QACX,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,kCAAkC;AACjC,YAAM,OAAO;AAGb,YAAM,gBAAgB,KAAK,SAAS;AAGpCC,mCAAmB,EAAC,KAAK,SAAS,KAAK;AACtC,YAAI,IAAI,QAAQ,OAAO,IAAI,UAAU,KAAK;AACzCD,8BAAY,MAAA,OAAA,kCAAA,aAAa,KAAK,UAAU,IAAI,IAAI,CAAC;AACjD,gBAAM,WAAW,IAAI,QAAQ,IAAI;AACjC,cAAI,UAAU;AAEb,qBAAS,SAAS;AAGlBA,0BAAAA,MAAI,eAAe,aAAa,QAAQ;AAGxC,iBAAK,WAAW;AAAA,cACf,GAAG;AAAA,cACH,QAAQ;AAAA;AAAA;AAIT,iBAAK,uBAAsB;AAAA,UAC5B;AAAA,QACD;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,kCAAc,aAAa,GAAG;AAAA,MAC/B,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AAChB,YAAM,OAAO;AAEbA,oBAAAA,MAAA,MAAA,OAAA,kCAAY,eAAe;AAG3B,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEvCK,6BAAW,EAAC,KAAK,SAAS,KAAK;AAC9BL,8BAAA,MAAA,OAAA,kCAAY,aAAa,KAAK,UAAU,GAAG,CAAC;AAE5C,cAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACpCA,0BAAAA,MAAA,MAAA,SAAA,kCAAc,YAAY;AAC1B,mBAAO,IAAI,MAAM,QAAQ,CAAC;AAC1B;AAAA,UACD;AAEA,cAAI,IAAI,QAAQ,OAAO,IAAI,UAAU,KAAK;AAEzC,gBAAI,UAAU,IAAI,QAAQ,IAAI;AAG9B,gBAAI,SAAS;AAEZ,kBAAI,CAAC,MAAM,QAAQ,OAAO,KAAK,OAAO,YAAY,UAAU;AAC3D,0BAAU,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,OAAO,OAAO;AAAA,cAChE;AAGA,kBAAI,MAAM,QAAQ,OAAO,KAAK,QAAQ,SAAS,GAAG;AACjDA,8BAAA,MAAA,MAAA,OAAA,kCAAY,gBAAgB,QAAQ,MAAM;AAG1C,qBAAK,iBAAiB,QAAQ,IAAI,SAAO;AACxC,sBAAI,OAAO,QAAQ,YAAY,IAAI,IAAI;AACtC,2BAAO,IAAI;AAAA,6BACD,OAAO,QAAQ,UAAU;AACnC,2BAAO;AAAA,kBACR;AACA,yBAAO;AAAA,gBACP,CAAA,EAAE,OAAO,QAAM,OAAO,IAAI;AAE3BA,8BAAY,MAAA,MAAA,OAAA,kCAAA,aAAa,KAAK,cAAc;AAG5C,sBAAM,WAAW,QAAQ,IAAI,SAAO;AACnC,sBAAI,OAAO,QAAQ,UAAU;AAC5B,2BAAO;AAAA,6BACG,OAAO,IAAI,MAAM;AAC3B,2BAAO,IAAI;AAAA,kBACZ;AACA,yBAAO;AAAA,gBACP,CAAA,EAAE,OAAO,UAAQ,SAAS,IAAI;AAE/B,qBAAK,kBAAkB;AAEvBA,8BAAY,MAAA,MAAA,OAAA,kCAAA,gBAAgB,QAAQ;AACpC,wBAAQ,OAAO;AAAA,qBACT;AACNA,8BAAAA,MAAA,MAAA,OAAA,kCAAY,qBAAqB;AACjC,qBAAK,kBAAkB;AACvB,qBAAK,iBAAiB;AACtB,wBAAQ,CAAE,CAAA;AAAA,cACX;AAAA,mBACM;AACNA,4BAAAA,MAAA,MAAA,OAAA,kCAAY,cAAc;AAC1B,mBAAK,kBAAkB;AACvB,mBAAK,iBAAiB;AACtB,sBAAQ,CAAE,CAAA;AAAA,YACX;AAAA,iBACM;AACNA,iFAAc,aAAa,IAAI,OAAO,MAAM;AAC5C,mBAAO,IAAI,MAAM,IAAI,OAAO,QAAQ,CAAC;AAAA,UACtC;AAAA,QACD,CAAC,EAAE,MAAM,CAAC,QAAQ;AACjBA,wBAAA,MAAA,MAAA,SAAA,kCAAc,eAAe,GAAG;AAChC,iBAAO,GAAG;AAAA,QACX,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA;AAAA,IAED,kBAAkB;AACjB,YAAM,OAAO;AACbA,oBAAAA,MAAY,MAAA,OAAA,kCAAA,WAAW;AAGvB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEvCE,yCAAuB,EAAC,KAAK,SAAS,KAAK;AAC1CF,8BAAY,MAAA,OAAA,kCAAA,eAAe,KAAK,UAAU,GAAG,CAAC;AAG9C,gBAAM,YAAY,IAAI,QAAQ,OAAO,IAAI,UAAU,OAAO,IAAI,QAAQ;AAEtE,cAAI,WAAW;AAEd,gBAAI,CAAC,IAAI,MAAM;AACdA,4BAAAA,MAAA,MAAA,OAAA,kCAAY,cAAc;AAC1B,mBAAK,YAAY,QAAQ;AACzB,sBAAQ,CAAE,CAAA;AACV;AAAA,YACD;AAGA,gBAAI,MAAM,QAAQ,IAAI,IAAI,KAAK,IAAI,KAAK,WAAW,GAAG;AACrDA,4BAAAA,MAAY,MAAA,OAAA,kCAAA,gBAAgB;AAC5B,mBAAK,YAAY,QAAQ;AACzB,sBAAQ,CAAE,CAAA;AACV;AAAA,YACD;AAEA,gBAAI;AAEH,kBAAI,MAAM,QAAQ,IAAI,IAAI,GAAG;AAC5B,qBAAK,mBAAmB,IAAI,IAAI;AAChC,wBAAQ,IAAI,IAAI;AAAA,qBACV;AACNA,8BAAAA,MAAA,MAAA,OAAA,kCAAY,qBAAqB;AACjC,qBAAK,mBAAmB,IAAI,IAAI;AAChC,wBAAQ,IAAI,IAAI;AAAA,cACjB;AAAA,YACC,SAAO,KAAK;AACbA,4BAAA,MAAA,MAAA,SAAA,kCAAc,eAAe,GAAG;AAChC,mBAAK,YAAY,UAAU;AAC3B,sBAAQ,CAAE,CAAA;AAAA,YACX;AAAA,iBACM;AACNA,iFAAc,cAAc,IAAI,OAAO,MAAM;AAC7C,iBAAK,YAAY,gBAAgB,IAAI,OAAO,OAAO;AACnD,mBAAO,IAAI,MAAM,IAAI,OAAO,QAAQ,CAAC;AAAA,UACtC;AAAA,QACD,CAAC,EAAE,MAAM,CAAC,QAAQ;AACjBA,wBAAA,MAAA,MAAA,SAAA,kCAAc,gBAAgB,GAAG;AACjC,eAAK,YAAY,YAAY;AAC7B,iBAAO,GAAG;AAAA,QACX,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvxFA,GAAG,WAAW,eAAe;"}