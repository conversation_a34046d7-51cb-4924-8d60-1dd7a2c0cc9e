<view class="container data-v-22b8a2a2"><view class="nav-bar data-v-22b8a2a2"><view class="nav-back data-v-22b8a2a2" bindtap="{{a}}"><text class="data-v-22b8a2a2">←</text></view><text class="nav-title data-v-22b8a2a2">放入纸条</text><view class="nav-submit data-v-22b8a2a2" bindtap="{{c}}"><text class="data-v-22b8a2a2">{{b}}</text></view></view><view class="form-container data-v-22b8a2a2"><view class="form-item data-v-22b8a2a2"><view class="form-label data-v-22b8a2a2">纸条类型</view><view class="type-list data-v-22b8a2a2"><view wx:for="{{d}}" wx:for-item="item" wx:key="c" class="{{['type-item', 'data-v-22b8a2a2', item.d && 'active']}}" bindtap="{{item.e}}"><text class="type-icon data-v-22b8a2a2">{{item.a}}</text><text class="type-name data-v-22b8a2a2">{{item.b}}</text></view></view></view><view class="form-item data-v-22b8a2a2"><view class="form-label data-v-22b8a2a2">纸条内容</view><textarea wx:if="{{e}}" class="form-textarea data-v-22b8a2a2" placeholder="写下你想说的话..." maxlength="500" value="{{f}}" bindinput="{{g}}"/><view wx:else class="voice-section data-v-22b8a2a2"><text class="data-v-22b8a2a2">语音功能开发中...</text></view><view wx:if="{{h}}" class="form-count data-v-22b8a2a2">{{i}}/500</view></view><view class="form-item data-v-22b8a2a2"><view class="form-label data-v-22b8a2a2">发布设置</view><view class="setting-row data-v-22b8a2a2" bindtap="{{k}}"><text class="setting-text data-v-22b8a2a2">匿名发布</text><view class="{{['switch', 'data-v-22b8a2a2', j && 'active']}}"><view class="switch-dot data-v-22b8a2a2"></view></view></view></view></view></view>