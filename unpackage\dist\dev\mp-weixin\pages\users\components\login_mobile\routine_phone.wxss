.mobile-bg {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 19;
}
.mobile-mask {
  z-index: 20;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 67rpx 30rpx;
  background: #fff;
  border-radius: 40rpx 40rpx 0 0;
  box-sizing: border-box;
}
.info-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.info-box image {
  width: 150rpx;
  height: 150rpx;
  border-radius: 20rpx;
}
.info-box .title {
  margin-top: 30rpx;
  margin-bottom: 20rpx;
  font-size: 40rpx;
  font-weight: 700;
  color: #000;
}
.info-box .sub-title {
  font-size: 26rpx;
  color: #999;
}
.btn-submit {
  margin-top: 60rpx;
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 700;
  background: #000;
  color: #fff;
  border-radius: 100rpx;
  justify-content: center;
}
.df {
  display: flex;
  align-items: center;
}
.animated {
  animation-duration: 0.4s;
}
@keyframes slideInUp {
from {
    transform: translate3d(0, 100%, 0);
}
to {
    transform: translate3d(0, 0, 0);
}
}