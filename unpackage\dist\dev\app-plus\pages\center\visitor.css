
.uni-load-more[data-v-9245e42c] {
  display: flex;
  flex-direction: row;
  height: 1.875rem;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-9245e42c] {
  font-size: 0.75rem;
  margin-left: 0.5rem;
}
.uni-load-more__img[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
}
.uni-load-more__img--nvue[data-v-9245e42c] {
  color: #999;
}
.uni-load-more__img--android[data-v-9245e42c],
.uni-load-more__img--ios[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
}
.uni-load-more__img--android[data-v-9245e42c] {
  animation: loading-ios 1s 0s linear infinite;
}
.uni-load-more__img--ios-H5[data-v-9245e42c] {
  position: relative;
  animation: loading-ios-H5-9245e42c 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-9245e42c] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-9245e42c {
0% {
    transform: rotate(0);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
to {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-9245e42c] {
  position: relative;
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 0.125rem transparent;
  border-top: solid 0.125rem #999;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(1) {
  animation: loading-android-MP-1-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(2) {
  animation: loading-android-MP-2-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(3) {
  animation: loading-android-MP-3-9245e42c 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(90deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(180deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(270deg);
}
to {
    transform: rotate(360deg);
}
}


/* 基础样式 - 参考 details.vue 的 empty-box 样式 */
.empty-page[data-v-5cea664a] {
  width: 100%;
  padding: 3.125rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.empty-content[data-v-5cea664a] {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

/* 图片样式 - 参考 details.vue */
.empty-image[data-v-5cea664a] {
  width: 9.375rem;
  height: 9.375rem;
  margin-bottom: 0.9375rem;
  opacity: 0.8;
}

/* 标题样式 - 参考 details.vue 的 .e1 */
.empty-title[data-v-5cea664a] {
  font-size: 0.9375rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.3125rem;
  line-height: 1.4;
}

/* 描述样式 - 参考 details.vue 的 .e2 */
.empty-description[data-v-5cea664a] {
  margin-top: 0.3125rem;
  color: #999;
  font-size: 0.8125rem;
  line-height: 1.5;
  margin-bottom: 1.25rem;
  max-width: 15.625rem;
  text-align: center;
}

/* 按钮样式 - 参考 details.vue 的 retry-btn */
.empty-button[data-v-5cea664a] {
  margin-top: 1.25rem;
  width: 6.25rem;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 700;
  color: #fff;
  background: #007aff;
  border-radius: 1.25rem;
  transition: all 0.3s ease;
}
.empty-button[data-v-5cea664a]:active {
  background: #0056cc;
  transform: scale(0.95);
}

/* 工具类 */
.df[data-v-5cea664a] {
  display: flex;
  align-items: center;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
.empty-title[data-v-5cea664a] {
    color: #fff;
}
.empty-description[data-v-5cea664a] {
    color: #ccc;
}
.empty-page[data-v-5cea664a] {
    background-color: #1a1a1a;
}
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.empty-image[data-v-5cea664a] {
    width: 7.8125rem;
    height: 7.8125rem;
}
.empty-title[data-v-5cea664a] {
    font-size: 0.875rem;
}
.empty-description[data-v-5cea664a] {
    font-size: 0.75rem;
    padding: 0 1.25rem;
}
}

/* 小程序兼容性 */






/* H5 兼容性 */








.container[data-v-971c42a7] {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 选项卡样式 */
.tab-bar[data-v-971c42a7] {
  display: flex;
  background: white;
  padding: 0 0.625rem;
}
.tab-item[data-v-971c42a7] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.625rem 0;
  position: relative;
}
.tab-text[data-v-971c42a7] {
  font-size: 0.875rem;
  color: #999;
  font-weight: 500;
  transition: all 0.3s ease;
}
.tab-item.active .tab-text[data-v-971c42a7] {
  color: #333;
  font-weight: bold;
}
.tab-line[data-v-971c42a7] {
  position: absolute;
  bottom: 0;
  width: 1.875rem;
  height: 0.125rem;
  background: #333;
  border-radius: 0.0625rem;
}

/* 访客统计 */
.visitor-stats[data-v-971c42a7] {
  display: flex;
  background: white;
  padding: 0.625rem 0.9375rem;
  margin-bottom: 0.3125rem;
  align-items: center;
  justify-content: center;
}
.stats-item[data-v-971c42a7] {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.stats-number[data-v-971c42a7] {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
  line-height: 1.2;
}
.stats-label[data-v-971c42a7] {
  font-size: 0.6875rem;
  color: #999;
  margin-top: 0.1875rem;
}
.stats-divider[data-v-971c42a7] {
  width: 0.03125rem;
  height: 1.25rem;
  background-color: #eee;
}

/* 访客列表 */
.visitor-list[data-v-971c42a7] {
  flex: 1;
}

/* 空状态 */
.empty-state[data-v-971c42a7] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.75rem 1.25rem;
  text-align: center;
}
.empty-image[data-v-971c42a7] {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.9375rem;
}
.empty-text[data-v-971c42a7] {
  font-size: 1rem;
  color: #666;
  margin-bottom: 0.625rem;
}
.empty-desc[data-v-971c42a7] {
  font-size: 0.8125rem;
  color: #999;
  line-height: 1.5;
}

/* 访客列表 */
.visitor-list-content[data-v-971c42a7] {
  background: white;
}
.visitor-item[data-v-971c42a7] {
  border-bottom: 0.03125rem solid #f0f0f0;
}
.visitor-item[data-v-971c42a7]:last-child {
  border-bottom: none;
}

/* 访客行信息 */
.visitor-row[data-v-971c42a7] {
  display: flex;
  align-items: center;
  padding: 0.625rem;
}
.avatar-wrapper[data-v-971c42a7] {
  position: relative;
  margin-right: 0.625rem;
}
.visitor-avatar[data-v-971c42a7] {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: #f0f0f0;
}
.vip-badge[data-v-971c42a7] {
  position: absolute;
  bottom: -0.09375rem;
  right: -0.09375rem;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
}
.vip-icon[data-v-971c42a7] {
  width: 0.5625rem;
  height: 0.5625rem;
}
.visitor-info[data-v-971c42a7] {
  flex: 1;
}
.visitor-name-row[data-v-971c42a7] {
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
}
.visitor-name[data-v-971c42a7] {
  font-size: 0.875rem;
  font-weight: 500;
  color: #333;
  margin-right: 0.25rem;
}
.gender-icon[data-v-971c42a7],
.auth-icon[data-v-971c42a7] {
  width: 0.75rem;
  height: 0.75rem;
  margin-left: 0.1875rem;
}

/* 访问信息 */
.visit-info[data-v-971c42a7] {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.visit-time[data-v-971c42a7] {
  font-size: 0.75rem;
  color: #666;
  margin-right: 0.625rem;
}
.visit-count[data-v-971c42a7] {
  font-size: 0.75rem;
  color: #1890ff;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.visitor-row[data-v-971c42a7] {
    padding: 0.46875rem 0.625rem;
}
.visitor-avatar[data-v-971c42a7] {
    width: 2.1875rem;
    height: 2.1875rem;
}
.visitor-name[data-v-971c42a7] {
    font-size: 0.8125rem;
}
.visitor-stats[data-v-971c42a7] {
    padding: 0.46875rem 0.625rem;
}
.stats-number[data-v-971c42a7] {
    font-size: 1rem;
}
.tab-text[data-v-971c42a7] {
    font-size: 0.8125rem;
}
}
