"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const libs_login = require("../../libs/login.js");
const api_user = require("../../api/user.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      tipsTitle: "",
      boxId: 0,
      boxData: {},
      responses: [],
      responseText: "",
      loading: false,
      isPlaying: false,
      currentUserId: null
      // 当前用户ID
    };
  },
  computed: {
    canSend() {
      return this.responseText.trim().length >= 5;
    },
    // 判断当前用户是否为纸条作者
    isAuthor() {
      return this.boxData && this.currentUserId && this.boxData.uid === this.currentUserId;
    }
  },
  async onLoad(options) {
    common_vendor.index.__f__("log", "at pages/note/detail.vue:171", "=== 纸条详情页面加载 ===");
    common_vendor.index.__f__("log", "at pages/note/detail.vue:172", "页面参数:", options);
    await this.getCurrentUser();
    if (options.id) {
      this.boxId = parseInt(options.id);
      this.loadBoxDetail();
    } else {
      common_vendor.index.showToast({
        title: "参数错误",
        icon: "none"
      });
    }
  },
  methods: {
    // 判断是否可以查看回应者信息
    canViewResponseUserInfo(responseItem) {
      if (this.isAuthor) {
        return true;
      }
      if (this.currentUserId && responseItem.uid === this.currentUserId) {
        return true;
      }
      return false;
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 获取当前用户信息
    async getCurrentUser() {
      try {
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        if (userInfo && userInfo.uid) {
          this.currentUserId = userInfo.uid;
          common_vendor.index.__f__("log", "at pages/note/detail.vue:214", "当前用户ID:", this.currentUserId);
        } else {
          const result = await api_user.getUserInfo();
          if (result.status === 200 && result.data) {
            this.currentUserId = result.data.uid;
            common_vendor.index.__f__("log", "at pages/note/detail.vue:220", "从API获取用户ID:", this.currentUserId);
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/detail.vue:224", "获取用户信息失败:", error);
      }
    },
    showMoreActions() {
      common_vendor.index.showActionSheet({
        itemList: ["举报", "分享"],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.reportBox();
          } else if (res.tapIndex === 1) {
            this.shareBox();
          }
        }
      });
    },
    async loadBoxDetail() {
      common_vendor.index.__f__("log", "at pages/note/detail.vue:242", "=== 加载纸条详情 ===");
      common_vendor.index.__f__("log", "at pages/note/detail.vue:243", "纸条ID:", this.boxId);
      this.loading = true;
      try {
        const result = await api_social.getTreeHoleBoxDetail(this.boxId);
        common_vendor.index.__f__("log", "at pages/note/detail.vue:249", "详情API响应:", result);
        if (result.status === 200 && result.data) {
          this.boxData = result.data;
          common_vendor.index.__f__("log", "at pages/note/detail.vue:253", "纸条数据:", this.boxData);
          this.loadResponses();
        } else {
          throw new Error(result.msg || "获取纸条详情失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/detail.vue:261", "加载纸条详情失败:", error);
        common_vendor.index.showToast({
          title: error.message || "加载失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    async loadResponses() {
      try {
        const result = await api_social.getTreeHoleResponseList({
          box_id: this.boxId,
          page: 1,
          limit: 50
        });
        if (result.status === 200 && result.data) {
          this.responses = result.data.list || [];
          common_vendor.index.__f__("log", "at pages/note/detail.vue:281", "回应列表:", this.responses);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/detail.vue:284", "加载回应列表失败:", error);
      }
    },
    async sendResponse() {
      if (!this.canSend) {
        return;
      }
      if (!libs_login.checkLogin()) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请先登录后再回应",
          confirmText: "去登录",
          success: (res) => {
            if (res.confirm) {
              libs_login.toLogin();
            }
          }
        });
        return;
      }
      try {
        const result = await api_social.responseTreeHoleBox({
          box_id: this.boxId,
          content: this.responseText.trim()
        });
        if (result.status === 200) {
          common_vendor.index.showToast({
            title: "回应成功",
            icon: "success"
          });
          this.responseText = "";
          this.loadResponses();
        } else {
          throw new Error(result.msg || "回应失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/detail.vue:326", "发送回应失败:", error);
        common_vendor.index.showToast({
          title: error.message || "回应失败",
          icon: "none"
        });
      }
    },
    toggleVoice() {
      this.isPlaying = !this.isPlaying;
      common_vendor.index.showToast({
        title: this.isPlaying ? "开始播放" : "暂停播放",
        icon: "none"
      });
    },
    getGenderIcon(sex) {
      return sex === 1 ? "♂" : sex === 2 ? "♀" : "⚪";
    },
    getTypeIcon(type) {
      const icons = {
        1: "❓",
        2: "🤫",
        3: "🌠",
        4: "🎵"
      };
      return icons[type] || "📝";
    },
    getTypeText(type) {
      const texts = {
        1: "问题咨询",
        2: "秘密",
        3: "心愿",
        4: "语音纸条"
      };
      return texts[type] || "纸条";
    },
    formatTime(time) {
      if (!time)
        return "";
      const now = /* @__PURE__ */ new Date();
      const createTime = new Date(time);
      const diff = now - createTime;
      const minutes = Math.floor(diff / 6e4);
      if (minutes < 1)
        return "刚刚";
      if (minutes < 60)
        return `${minutes}分钟前`;
      if (minutes < 1440)
        return `${Math.floor(minutes / 60)}小时前`;
      return `${Math.floor(minutes / 1440)}天前`;
    },
    reportBox() {
      common_vendor.index.showToast({
        title: "举报功能开发中",
        icon: "none"
      });
    },
    shareBox() {
      common_vendor.index.showToast({
        title: "分享功能开发中",
        icon: "none"
      });
    },
    // 显示提示信息
    opTipsPopup(msg) {
      this.tipsTitle = msg;
      this.$refs.tipsPopup.open();
      setTimeout(() => {
        this.$refs.tipsPopup.close();
      }, 2e3);
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_2$4,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.o((...args) => $options.showMoreActions && $options.showMoreActions(...args)),
    d: $data.titleBarHeight + "px",
    e: $data.statusBarHeight + "px",
    f: $data.loading
  }, $data.loading ? {} : $data.boxData.id ? common_vendor.e({
    h: $data.boxData.avatar || "/static/default-avatar.png",
    i: common_vendor.t($data.boxData.nickname || "匿名用户"),
    j: common_vendor.t($options.getGenderIcon($data.boxData.sex)),
    k: common_vendor.t($data.boxData.age || "未知"),
    l: common_vendor.t($data.boxData.location || "Sub"),
    m: common_vendor.t($options.getTypeIcon($data.boxData.type)),
    n: common_vendor.t($options.getTypeText($data.boxData.type)),
    o: common_vendor.n("type-" + $data.boxData.type),
    p: $data.boxData.type !== 4
  }, $data.boxData.type !== 4 ? {
    q: common_vendor.t($data.boxData.content)
  } : {
    r: common_vendor.t($data.isPlaying ? "⏸️" : "▶️"),
    s: common_vendor.t($data.boxData.voice_duration),
    t: common_vendor.o((...args) => $options.toggleVoice && $options.toggleVoice(...args))
  }, {
    v: common_vendor.t($options.formatTime($data.boxData.create_time)),
    w: common_vendor.t($data.responses.length),
    x: !$options.isAuthor && $data.responses.length > 0
  }, !$options.isAuthor && $data.responses.length > 0 ? {} : {}, {
    y: common_vendor.f($data.responses, (item, k0, i0) => {
      return common_vendor.e({
        a: $options.canViewResponseUserInfo(item) ? item.avatar || "/static/images/def_avatar.png" : "/static/images/def_avatar.png",
        b: common_vendor.t($options.canViewResponseUserInfo(item) ? item.nickname : "匿名用户"),
        c: common_vendor.t($options.formatTime(item.create_time)),
        d: $data.currentUserId && item.uid === $data.currentUserId
      }, $data.currentUserId && item.uid === $data.currentUserId ? {} : {}, {
        e: common_vendor.t(item.content),
        f: item.id
      });
    }),
    z: $data.responseText,
    A: common_vendor.o(($event) => $data.responseText = $event.detail.value),
    B: common_vendor.t($data.responseText.length),
    C: $options.canSend ? 1 : "",
    D: common_vendor.o((...args) => $options.sendResponse && $options.sendResponse(...args))
  }) : {
    E: common_vendor.o((...args) => $options.goBack && $options.goBack(...args))
  }, {
    g: $data.boxData.id,
    F: "calc(" + ($data.statusBarHeight + $data.titleBarHeight) + "px + 20rpx)",
    G: common_vendor.t($data.tipsTitle),
    H: common_vendor.sr("tipsPopup", "7c67bb18-0"),
    I: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/note/detail.js.map
