{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 顶部自定义导航栏 -->\n    <view class=\"nav-box\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\n      <view class=\"bar-box\" :style=\"{'height': titleBarHeight + 'px'}\">\n        <image class=\"bar-logo\" src=\"/static/img/logo3.png\" mode=\"heightFix\"></image>\n        <view class=\"bar-icons\" :style=\"{'padding-right': menuButtonWidth + 'px'}\">\n          <image class=\"bar-icon\" src=\"/static/img/s.png\" @tap=\"onSearch\"></image>\n          <image class=\"bar-icon\" src=\"/static/img/qd.png\" @tap=\"onSignIn\"></image>\n          <!-- <image class=\"bar-icon\" src=\"/static/img/user2.png\"></image> -->\n        </view>\n      </view>\n    </view>\n\n    <!-- 主要内容区 -->\n    <view class=\"content-box\" :style=\"{'padding-top': (statusBarHeight + titleBarHeight) + 'px'}\">\n      <scroll-view \n        scroll-y=\"true\" \n        @scrolltolower=\"onReachEnd\" \n        class=\"content-scroll\"\n        :show-scrollbar=\"true\"\n      >\n        <!-- 达人卡片和右侧功能区 -->\n        <view class=\"feature-grid\">\n          <!-- 使用新的布局模式 -->\n          <view class=\"grid-layout\">\n            <!-- 顶部布局：左边一个，右边两个 -->\n            <view class=\"top-grid\">\n              <!-- 左侧卡片 - 在线匹配 -->\n              <view class=\"left-item blue\" @tap=\"navigateToFeature('match')\">\n                <view class=\"feature-content\">\n                  <view class=\"feature-title\">在线匹配</view>\n                  <view class=\"feature-subtitle\">今天还有25次机会</view>\n                  <view class=\"feature-btn\">开始匹配</view>\n                </view>\n                <view class=\"feature-bubble\"></view>\n              </view>\n              \n              <!-- 右侧两个卡片 -->\n              <view class=\"right-stack\">\n                <!-- 缘分恋爱卡片 - 粉色 -->\n                <view class=\"right-item pink\" @tap=\"navigateToFeature('love')\">\n                  <view class=\"feature-content\">\n                    <view class=\"feature-title\">树洞盲盒</view>\n                    <view class=\"feature-subtitle\">发现附近缘分</view>\n                  </view>\n                  <view class=\"feature-icon heart-icon\">\n                    <image src=\"/static/img/logo.png\" mode=\"aspectFit\"></image>\n                  </view>\n                  <view class=\"feature-bubble\"></view>\n                </view>\n                \n                <!-- 声音匹配卡片 - 绿色 -->\n                <view class=\"right-item green\" @tap=\"navigateToFeature('voice')\">\n                  <view class=\"feature-content\">\n                    <view class=\"feature-title\">声音匹配</view>\n                    <view class=\"feature-subtitle\">选择你的声音恋人</view>\n                  </view>\n                  <view class=\"feature-icon\">\n                    <image src=\"/static/img/logo.png\" mode=\"aspectFit\"></image>\n                  </view>\n                  <view class=\"feature-bubble\"></view>\n                </view>\n              </view>\n            </view>\n            \n            <!-- 底部一排三个卡片 -->\n            <view class=\"bottom-row\">\n              <!-- 语音通话卡片 - 紫色 -->\n              <view class=\"bottom-item purple\" @tap=\"navigateToFeature('call')\">\n                <view class=\"feature-content\">\n                  <view class=\"feature-title\">语音通话</view>\n                  <view class=\"feature-subtitle\">还有3次机会</view>\n                </view>\n                <view class=\"feature-icon\">\n                  <image src=\"/static/img/logo.png\" mode=\"aspectFit\"></image>\n                </view>\n                <view class=\"feature-bubble\"></view>\n              </view>\n              \n              <!-- 兴趣群聊卡片 - 粉红色 -->\n              <view class=\"bottom-item hot-pink\" @tap=\"navigateToFeature('group')\">\n                <view class=\"feature-content\">\n                  <view class=\"feature-title\">兴趣群聊</view>\n                  <view class=\"feature-subtitle\">总有人懂你</view>\n                </view>\n                <view class=\"feature-icon\">\n                  <image src=\"/static/img/logo.png\" mode=\"aspectFit\"></image>\n                </view>\n                <view class=\"feature-bubble\"></view>\n              </view>\n              \n              <!-- 互动游戏卡片 - 青色 -->\n              <view class=\"bottom-item cyan\" @tap=\"navigateToFeature('game')\">\n                <view class=\"feature-content\">\n                  <view class=\"feature-title\">互动游戏</view>\n                  <view class=\"feature-subtitle\">找游戏伙伴</view>\n                </view>\n                <view class=\"feature-icon\">\n                  <image src=\"/static/img/logo.png\" mode=\"aspectFit\"></image>\n                </view>\n                <view class=\"feature-bubble\"></view>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 圈子推荐区域 -->\n        <view class=\"popular-section\">\n          <view class=\"popular-title\">\n            <text>圈子推荐</text>\n          </view>\n          <scroll-view scroll-x=\"true\" class=\"scroll-box\" style=\"height: 246rpx\">\n            <view class=\"circle-box\">\n              <view v-for=\"(item, index) in circle\" :key=\"index\" class=\"circle-item\" :data-url=\"'note/circle?id=' + item.id\" @tap=\"navigateToFun\">\n                <view class=\"circle-item-top\">\n                  <image :src=\"item.circle_avatar || item.avatar\" mode=\"aspectFill\"></image>\n                  <view v-if=\"item.is_official == 1\" class=\"circle-item-tag\" style=\"background: url(/static/img/gf.png) 0% 0% / 100% 100%;\"></view>\n                  <view v-else-if=\"item.is_hot == 1\" class=\"circle-item-tag\" style=\"background: url(/static/img/tj.png) 0% 0% / 100% 100%;\"></view>\n                </view>\n                \n                <view class=\"circle-name ohto\">{{item.circle_name || item.name}}</view>\n                <view class=\"circle-tips\">\n                  <text v-if=\"item.dynamic_count\">{{item.dynamic_count}}更新</text>\n                  <text v-else-if=\"item.member_count || item.user_count\">{{item.member_count || item.user_count}}新圈友</text>\n                  <text v-else>推荐的圈子</text>\n                </view>\n              </view>\n              <view class=\"circle-item\" data-url=\"center/circle\" @tap=\"navigateToFun\">\n                <view class=\"circle-item-top\">\n                  <image class=\"icon\" src=\"/static/img/more.png\"></image>\n                </view>\n                <view class=\"circle-name\">更多圈子</view>\n              </view>\n              <view class=\"circle-item\" style=\"width:10rpx\"></view>\n            </view>\n          </scroll-view>\n        </view>\n\n        <!-- 一起玩区域 -->\n        <view class=\"playwith-section\">\n          <!-- TAB栏 -->\n          <view class=\"playwith-tabs\">\n            <view \n              v-for=\"(tab, idx) in playTabs\" \n              :key=\"idx\" \n              class=\"playwith-tab\" \n              :class=\"{'active': playTabIndex === idx}\"\n              @tap=\"playTabIndex = idx\"\n            >\n              <text>{{tab}}</text>\n              <view v-if=\"playTabIndex === idx\" class=\"active-line\"></view>\n            </view>\n          </view>\n          <!-- TAB内容 -->\n          <view class=\"playwith-tab-content\">\n            <view v-if=\"playTabIndex === 0\">\n              <!-- 个人卡片风格内容 -->\n              <view class=\"profile-card\">\n                <view class=\"profile-header\">\n                  <image class=\"profile-avatar\" src=\"/static/img/logo.png\"></image>\n                  <view class=\"profile-info\">\n                    <view class=\"profile-title\">\n                      <text class=\"profile-name\">张见秋.</text>\n                      <text class=\"profile-gender female\">♀ 25</text>\n                    </view>\n                    <view class=\"profile-status-row\">\n                      <text class=\"profile-status online\">● 在线</text>\n                      <text class=\"profile-status city\">同城</text>\n                      <text class=\"profile-status\">巨蟹座</text>\n                      <text class=\"profile-status\">享受单身</text>\n                    </view>\n                    <view class=\"profile-tags\">\n                      <text class=\"profile-tag\">软萌妹子</text>\n                      <text class=\"profile-tag\">人皮话多</text>\n                      <text class=\"profile-tag\">投猫子</text>\n                      <text class=\"profile-tag\">无辣不欢</text>\n                    </view>\n                  </view>\n                  <view class=\"profile-hi-btn\">Hi</view>\n                </view>\n                <view class=\"profile-voice\">\n                  <view class=\"voice-btn\">\n                    <image src=\"/static/img/play.png\" class=\"voice-icon\"></image>\n                    <text>06'</text>\n                  </view>\n                </view>\n                <view class=\"profile-desc\">别叫我闭嘴</view>\n                <view class=\"profile-photos\">\n                  <image class=\"profile-photo\" src=\"/static/img/logo.png\"></image>\n                  <image class=\"profile-photo\" src=\"/static/img/logo.png\"></image>\n                </view>\n              </view>\n            </view>\n            <view v-else-if=\"playTabIndex === 1\">\n              <!-- 附近内容 -->\n              <view class=\"playwith-card shadow\">\n                <image class=\"playwith-avatar\" src=\"/static/img/logo.png\"></image>\n                <view class=\"playwith-info\">\n                  <view class=\"playwith-name\">小明</view>\n                  <view class=\"playwith-score\">\n                    <text class=\"star\">★</text>4.7 (980)\n                  </view>\n                  <view class=\"playwith-game\">🎮 王者荣耀</view>\n                  <view class=\"playwith-desc\">附近的玩家，快来组队！</view>\n                </view>\n              </view>\n            </view>\n            <view v-else-if=\"playTabIndex === 2\">\n              <!-- 最新内容 -->\n              <view class=\"playwith-card shadow\">\n                <image class=\"playwith-avatar\" src=\"/static/img/logo.png\"></image>\n                <view class=\"playwith-info\">\n                  <view class=\"playwith-name\">小红</view>\n                  <view class=\"playwith-score\">\n                    <text class=\"star\">★</text>4.9 (1500)\n                  </view>\n                  <view class=\"playwith-game\">🎮 英雄联盟手游</view>\n                  <view class=\"playwith-desc\">新加入的小伙伴，欢迎！</view>\n                </view>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 底部留白区域，避免内容被底部遮挡 -->\n        <view style=\"height: 100rpx;\"></view>\n      </scroll-view>\n    </view>\n    \n    <!-- 添加底部tabbar -->\n    <tabbar :currentPage=\"0\" :currentMsg=\"currentMsg\"></tabbar>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue'\nimport { useStore } from 'vuex'\nimport tabbar from '@/components/tabbar/tabbar'\nimport { getHotCircles as fetchHotCircles } from '@/api/social.js'\n\n// 定义组件名称\ndefineOptions({\n  name: 'TabbarIndex'\n})\n\n// 获取store和当前实例\nconst store = useStore()\nconst instance = getCurrentInstance()\n\n// 响应式数据\nconst statusBarHeight = ref(store?.state?.statusBarHeight || 20)\nconst titleBarHeight = ref(store?.state?.titleBarHeight || 44)\nconst menuButtonWidth = ref(0) // 胶囊按钮右边距\nconst playTabs = ref(['推荐', '附近', '最新'])\nconst playTabIndex = ref(0)\nconst currentMsg = ref(0)\nconst games = ref([])\nconst circle = ref([])\n\n// 方法定义\n// 获取胶囊按钮位置信息\nconst getMenuButtonInfo = () => {\n  // 尝试获取胶囊按钮信息\n  try {\n    // 微信小程序环境\n    if (uni.canIUse('getMenuButtonBoundingClientRect')) {\n      const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\n      if (menuButtonInfo) {\n        // 设置右边距，确保不与胶囊按钮重叠\n        // 胶囊右侧到屏幕右侧的距离 + 额外的间距\n        menuButtonWidth.value = menuButtonInfo.width + 16;\n      }\n    }\n  } catch (e) {\n    // 如果不是小程序环境或获取失败，设置默认值\n    menuButtonWidth.value = 88; // 默认预留空间\n  }\n}\n\n// 搜索点击事件\nconst onSearch = () => {\n  uni.navigateTo({\n    url: '/pages/search/index'\n  });\n}\n\n// 签到点击事件\nconst onSignIn = () => {\n  uni.navigateTo({\n    url: '/pages/users/user_sgin/index'\n  });\n}\n\n// 滚动到底部时触发\nconst onReachEnd = () => {\n  console.log('滚动到底部');\n}\n\n// 从store获取消息数量\nconst getCurrentMsg = () => {\n  if (store && store.state && store.state.app) {\n    const userInfo = uni.getStorageSync('USER_INFO') || {};\n    currentMsg.value = userInfo.service_num || 0;\n  }\n}\n\n// 导航到功能页面\nconst navigateToFun = (e) => {\n  const url = e.currentTarget.dataset.url;\n  if (url) {\n    uni.navigateTo({\n      url: '/pages/' + url\n    });\n  }\n}\n\n// 导航到特定功能\nconst navigateToFeature = (type) => {\n  console.log('Navigate to feature:', type);\n  // 根据type跳转到不同的页面\n  let url = '';\n  switch (type) {\n    case 'match':\n      url = 'match/index';\n      break;\n    case 'love':\n      url = 'note/manghe';\n      break;\n    case 'voice':\n      url = 'voice/index';\n      break;\n    case 'call':\n      url = 'call/index';\n      break;\n    case 'group':\n      url = 'group/index';\n      break;\n    case 'game':\n      url = 'game/index';\n      break;\n    default:\n      break;\n  }\n  if (url) {\n    uni.navigateTo({\n      url: '/pages/' + url\n    });\n  }\n}\n\n// 获取热门圈子\nconst getHotCircles = () => {\n  // 调用热门圈子接口\n  fetchHotCircles()\n    .then(res => {\n      if (res.status === 200 && res.data) {\n        // 处理圈子数据，确保字段兼容\n        circle.value = res.data.map(item => {\n          return {\n            id: item.id,\n            circle_name: item.circle_name || item.name,\n            circle_avatar: item.circle_avatar || item.avatar,\n            name: item.circle_name || item.name, // 兼容字段\n            avatar: item.circle_avatar || item.avatar, // 兼容字段\n            is_hot: item.is_hot || 0,\n            is_official: item.is_official || 0,\n            dynamic_count: item.dynamic_count || 0,\n            member_count: item.member_count || 0,\n            user_count: item.member_count || 0 // 兼容字段\n          };\n        });\n      }\n    })\n    .catch(err => {\n      console.log('获取热门圈子失败', err);\n      // 保留默认数据\n    });\n}\n\n// 生命周期钩子 - onLoad 等价于 onMounted\nonMounted(() => {\n  // 自定义tabbar，不需要隐藏原生tabbar\n  // uni.hideTabBar(); // 注释掉，因为使用自定义tabbar\n\n  // 获取胶囊按钮位置信息\n  getMenuButtonInfo();\n\n  // 获取消息数量\n  getCurrentMsg();\n\n  // 获取热门圈子数据\n  getHotCircles();\n})\n</script>\n\n<style>\n.container { \n  background: #ffffff; \n  min-height: 100vh;\n  width: 100%;\n  overflow-x: hidden; /* Prevent horizontal scrolling */\n}\n.content-box {\n  width: 100%;\n  height: 100%;\n  overflow-x: hidden; /* Prevent horizontal overflow */\n  position: relative;\n  z-index: 1;\n  will-change: transform;\n  padding-left: 10rpx;\n  padding-right: 10rpx;\n  box-sizing: border-box; /* Include padding in width calculation */\n}\n.content-scroll {\n  height: 100%;\n  -webkit-overflow-scrolling: touch;\n}\n.nav-box {\n  position: fixed;\n  top: 0;\n  width: 100%;\n  z-index: 99;\n  background: #fff;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);\n  transform: translateZ(0);\n  will-change: transform;\n}\n.bar-box {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  padding: 0 32rpx;\n}\n.bar-logo {\n  height: 48rpx;\n  width: 120rpx;\n}\n.bar-title { flex: 1; text-align: center; font-size: 34rpx; font-weight: bold; color: #222; letter-spacing: 2rpx; }\n.bar-icons {\n  display: flex;\n  align-items: center;\n  gap: 32rpx;\n}\n.bar-icon {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 50%;\n  background: #fff;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: background 0.2s;\n}\n.bar-icon:active {\n  background: #f0f0f0;\n}\n\n.shadow {\n  box-shadow: 0 4rpx 24rpx rgba(45,156,250,0.10);\n}\n.top-section {\n  display: flex;\n  justify-content: space-between;\n  margin: 10rpx 0 20rpx;\n  padding: 0 5rpx;\n}\n.influencer-card { \n  background: #2d9cfa; \n  border-radius: 20rpx; \n  padding: 15rpx; \n  width: 42%; \n  color: #fff; \n  position: relative; \n}\n.avatar { width: 100rpx; height: 100rpx; border-radius: 20rpx; }\n.voice-time { position: absolute; left: 15rpx; top: 15rpx; background: #fff; color: #2d9cfa; border-radius: 20rpx; padding: 2rpx 10rpx; font-size: 22rpx; }\n.name { font-size: 32rpx; font-weight: bold; margin-top: 10rpx; }\n.desc { font-size: 24rpx; margin-top: 4rpx; }\n.right-func { \n  width: 52%;\n  display: flex; \n  flex-direction: column; \n  justify-content: space-between;\n}\n.func-box {\n  margin-bottom: 10rpx;\n  padding: 12rpx;\n  background: #fff;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 24rpx rgba(45,156,250,0.10);\n}\n.func-title { font-size: 24rpx; font-weight: bold; margin-bottom: 6rpx; }\n.func-avatars { display: flex; }\n.func-avatar { width: 32rpx; height: 32rpx; border-radius: 50%; margin-right: 6rpx; }\n.func-status { font-size: 22rpx; }\n.closed { color: #b0b0b0; }\n.online { color: #ffb800; }\n\n.popular-section { margin: 20rpx 10rpx 30rpx; }\n.popular-title { display: flex; justify-content: space-between; align-items: center; font-size: 32rpx; font-weight: bold; margin-bottom: 16rpx; }\n.all-btn { color: #2d9cfa; font-size: 24rpx; }\n\n/* 圈子相关样式 */\n.scroll-box{\n  width:100%;\n  white-space:nowrap;\n  overflow:hidden;\n  transition:height .45s ease-in-out\n}\n\n.circle-box{\n  width: 100%;\n  display: flex;\n  padding: 30rpx 10rpx;\n  box-sizing: border-box; /* Include padding in width calculation */\n}\n\n.circle-box .circle-item{\n  flex-shrink:0\n}\n\n.circle-item .circle-item-top{\n  margin:0 20rpx;\n  width:116rpx;\n  height:116rpx;\n  border-radius:50%;\n  background:#f8f8f8;\n  border:2rpx solid #f5f5f5;\n  position:relative\n}\n\n.circle-item-top image{\n  width:100%;\n  height:100%;\n  border-radius:50%\n}\n\n.circle-item-top .icon{\n  margin:34rpx;\n  width:48rpx;\n  height:48rpx\n}\n\n.circle-item-top .circle-item-tag{\n  position:absolute;\n  right:0;\n  bottom:0;\n  width:24rpx;\n  height:24rpx;\n  border-radius:50%;\n  border:6rpx solid #fff\n}\n\n.circle-item .circle-name{\n  margin: 20rpx 0 10rpx;\n  width: 160rpx;\n  color: #000;\n  font-weight: 500;\n  font-size: 24rpx;\n  line-height: 24rpx;\n  text-align: center;\n  box-sizing: border-box; /* Include padding in width calculation */\n}\n\n.circle-item .circle-tips{\n  width: 160rpx;\n  color: #999;\n  font-size: 18rpx;\n  line-height: 18rpx;\n  font-weight: 300;\n  text-align: center;\n  box-sizing: border-box; /* Include padding in width calculation */\n}\n\n.playwith-section { margin: 20rpx 10rpx 30rpx; }\n.playwith-title { font-size: 32rpx; font-weight: bold; margin-bottom: 16rpx; }\n.playwith-tabs {\n  display: flex;\n  background: #fff;\n  border-radius: 16rpx;\n  overflow: hidden;\n  margin-bottom: 16rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);\n}\n.playwith-tab {\n  flex: 1;\n  text-align: center;\n  font-size: 30rpx;\n  color: #999;\n  padding: 20rpx 0;\n  font-weight: 500;\n  position: relative;\n  transition: all 0.3s ease;\n}\n.playwith-tab.active {\n  color: #000;\n  font-weight: 700;\n  transform: scale(1.05);\n}\n.playwith-tab .active-line {\n  position: absolute;\n  bottom: -5rpx;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 30rpx;\n  height: 6rpx;\n  background-color: #000;\n  border-radius: 3rpx;\n  transition: all 0.3s ease;\n}\n.playwith-tab-content {\n  /* 可加切换动画 */\n}\n\n/* 个人卡片样式 */\n.profile-card {\n  background: #fff;\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 24rpx rgba(45,156,250,0.10);\n  padding: 32rpx 24rpx 24rpx 24rpx;\n  margin-bottom: 24rpx;\n}\n.profile-header {\n  display: flex;\n  align-items: flex-start;\n  position: relative;\n}\n.profile-avatar {\n  width: 88rpx;\n  height: 88rpx;\n  border-radius: 50%;\n  margin-right: 20rpx;\n}\n.profile-info {\n  flex: 1;\n}\n.profile-title {\n  display: flex;\n  align-items: center;\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #222;\n}\n.profile-name {\n  margin-right: 12rpx;\n}\n.profile-gender {\n  font-size: 24rpx;\n  color: #ff7eb3;\n}\n.profile-gender.female {\n  color: #ff7eb3;\n}\n.profile-status-row {\n  margin-top: 8rpx;\n  display: flex;\n  gap: 12rpx;\n}\n.profile-status {\n  font-size: 22rpx;\n  color: #888;\n  background: #f5f6fa;\n  border-radius: 8rpx;\n  padding: 2rpx 12rpx;\n}\n.profile-status.online {\n  color: #2ecc71;\n  background: #eaffea;\n}\n.profile-status.city {\n  color: #2d9cfa;\n  background: #eaf6ff;\n}\n.profile-tags {\n  margin-top: 8rpx;\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8rpx;\n}\n.profile-tag {\n  font-size: 20rpx;\n  color: #666;\n  background: #f5f6fa;\n  border-radius: 8rpx;\n  padding: 2rpx 10rpx;\n}\n.profile-hi-btn {\n  position: absolute;\n  right: 0;\n  top: 0;\n  background: linear-gradient(90deg,#5b8cff,#b36fff);\n  color: #fff;\n  font-size: 28rpx;\n  font-weight: bold;\n  border-radius: 16rpx;\n  padding: 8rpx 32rpx;\n  box-shadow: 0 2rpx 8rpx rgba(91,140,255,0.10);\n}\n.profile-voice {\n  margin-top: 20rpx;\n}\n.voice-btn {\n  display: flex;\n  align-items: center;\n  background: #f5f6fa;\n  border-radius: 20rpx;\n  width: 90rpx;\n  padding: 6rpx 12rpx;\n  color: #222;\n  font-size: 22rpx;\n  font-weight: bold;\n}\n.voice-icon {\n  width: 28rpx;\n  height: 28rpx;\n  margin-right: 8rpx;\n}\n.profile-desc {\n  margin-top: 18rpx;\n  font-size: 26rpx;\n  color: #222;\n  font-weight: 500;\n}\n.profile-photos {\n  margin-top: 12rpx;\n  display: flex;\n  gap: 12rpx;\n}\n.profile-photo {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 12rpx;\n  object-fit: cover;\n}\n\n/* 一起玩卡片样式 */\n.playwith-card {\n  display: flex;\n  padding: 24rpx;\n  background: #fff;\n  border-radius: 20rpx;\n  margin-bottom: 16rpx;\n  box-shadow: 0 4rpx 24rpx rgba(45,156,250,0.10);\n}\n.playwith-avatar {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 20rpx;\n  margin-right: 20rpx;\n}\n.playwith-info {\n  flex: 1;\n}\n.playwith-name {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #222;\n}\n.playwith-score {\n  color: #ffb800;\n  font-size: 26rpx;\n  margin-bottom: 4rpx;\n}\n.playwith-game {\n  font-size: 24rpx;\n  color: #2d9cfa;\n  margin-bottom: 4rpx;\n}\n.playwith-desc {\n  font-size: 24rpx;\n  color: #888;\n}\n.star {\n  color: #ffb800;\n}\n\n/* 优化性能相关样式 */\n.hardware-accelerated {\n  transform: translateZ(0);\n  will-change: transform;\n  backface-visibility: hidden;\n}\n\n/* 卡片渐入动画 */\n@keyframes fade-in {\n  from { opacity: 0; transform: translateY(20rpx); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n.ohto {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n/* 添加底部tabbar相关样式 */\n.df {\n  display: flex;\n  align-items: center;\n}\n\n/* 新增功能卡片样式 */\n.feature-grid {\n  padding: 15rpx; /* 减小内边距 */\n}\n\n/* 新的网格布局样式 */\n.grid-layout {\n  display: flex;\n  flex-direction: column;\n  gap: 15rpx; /* 减小间距 */\n}\n\n/* 顶部网格：左一右二 */\n.top-grid {\n  display: flex;\n  gap: 15rpx; /* 减小间距 */\n  height: 220rpx; /* 减小高度 */\n}\n\n.left-item {\n  flex: 1;\n  border-radius: 20rpx;\n  padding: 20rpx; /* 减小内边距 */\n  position: relative;\n  overflow: hidden;\n}\n\n.right-stack {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 15rpx; /* 减小间距 */\n}\n\n.right-item {\n  flex: 1;\n  border-radius: 20rpx;\n  padding: 15rpx; /* 减小内边距 */\n  position: relative;\n  overflow: hidden;\n}\n\n/* 底部一行三个 */\n.bottom-row {\n  display: flex;\n  gap: 15rpx; /* 减小间距 */\n}\n\n.bottom-item {\n  flex: 1;\n  border-radius: 20rpx;\n  padding: 15rpx; /* 减小内边距 */\n  position: relative;\n  overflow: hidden;\n  min-height: 90rpx; /* 减小最小高度 */\n}\n\n.feature-bubble {\n  position: absolute;\n  right: 15rpx;\n  bottom: 15rpx;\n  width: 80rpx; /* 减小气泡大小 */\n  height: 80rpx; /* 减小气泡大小 */\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.15);\n  z-index: 0;\n}\n\n.feature-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: flex-start;\n  align-items: flex-start;\n  z-index: 2;\n  position: relative;\n}\n\n.feature-title {\n  font-size: 28rpx; /* 减小字体大小 */\n  font-weight: bold;\n  color: #fff;\n  margin-bottom: 6rpx; /* 减小下边距 */\n}\n\n.feature-subtitle {\n  font-size: 20rpx; /* 减小字体大小 */\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.feature-btn {\n  background: #ffffff;\n  color: #0099ff;\n  font-size: 24rpx; /* 减小字体大小 */\n  font-weight: bold;\n  border-radius: 50rpx;\n  padding: 6rpx 24rpx; /* 减小内边距 */\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n  margin-top: 15rpx; /* 减小上边距 */\n}\n\n.feature-icon {\n  position: absolute;\n  right: 20rpx;\n  bottom: 20rpx;\n  z-index: 1;\n}\n\n.feature-icon image {\n  width: 50rpx; /* 减小图标大小 */\n  height: 50rpx; /* 减小图标大小 */\n}\n\n.heart-icon image {\n  width: 40rpx; /* 减小图标大小 */\n  height: 40rpx; /* 减小图标大小 */\n}\n\n/* Card colors */\n.blue {\n  background: #0099ff;\n}\n\n.pink {\n  background: #ff66cc;\n}\n\n.green {\n  background: #33cc99;\n}\n\n.purple {\n  background: #9966ff;\n}\n\n.hot-pink {\n  background: #ff6699;\n}\n\n.cyan {\n  background: #00cccc;\n}\n</style>", "import MiniProgramPage from 'D:/uniapp/vue3/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useStore", "ref", "uni", "fetchHotCircles", "onMounted", "MiniProgramPage"], "mappings": ";;;;;;;AA8OA,MAAM,SAAS,MAAW;;;;;;;AAS1B,UAAM,QAAQA,cAAAA,SAAU;AAIxB,UAAM,kBAAkBC,cAAAA,MAAI,oCAAO,UAAP,mBAAc,oBAAmB,EAAE;AAC/D,UAAM,iBAAiBA,cAAAA,MAAI,oCAAO,UAAP,mBAAc,mBAAkB,EAAE;AAC7D,UAAM,kBAAkBA,cAAG,IAAC,CAAC;AAC7B,UAAM,WAAWA,cAAAA,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC;AACvC,UAAM,eAAeA,cAAG,IAAC,CAAC;AAC1B,UAAM,aAAaA,cAAG,IAAC,CAAC;AACVA,kBAAG,IAAC,EAAE;AACpB,UAAM,SAASA,cAAG,IAAC,EAAE;AAIrB,UAAM,oBAAoB,MAAM;AAE9B,UAAI;AAEF,YAAIC,cAAG,MAAC,QAAQ,iCAAiC,GAAG;AAClD,gBAAM,iBAAiBA,oBAAI;AAC3B,cAAI,gBAAgB;AAGlB,4BAAgB,QAAQ,eAAe,QAAQ;AAAA,UAChD;AAAA,QACF;AAAA,MACF,SAAQ,GAAG;AAEV,wBAAgB,QAAQ;AAAA,MACzB;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,aAAa,MAAM;AACvBA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,OAAO;AAAA,IACrB;AAGA,UAAM,gBAAgB,MAAM;AAC1B,UAAI,SAAS,MAAM,SAAS,MAAM,MAAM,KAAK;AAC3C,cAAM,WAAWA,cAAG,MAAC,eAAe,WAAW,KAAK,CAAA;AACpD,mBAAW,QAAQ,SAAS,eAAe;AAAA,MAC5C;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,MAAM;AAC3B,YAAM,MAAM,EAAE,cAAc,QAAQ;AACpC,UAAI,KAAK;AACPA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,YAAY;AAAA,QACvB,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,SAAS;AAClCA,oBAAA,MAAA,MAAA,OAAA,gCAAY,wBAAwB,IAAI;AAExC,UAAI,MAAM;AACV,cAAQ,MAAI;AAAA,QACV,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,QACF,KAAK;AACH,gBAAM;AACN;AAAA,MAGH;AACD,UAAI,KAAK;AACPA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,YAAY;AAAA,QACvB,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAE1BC,+BAAiB,EACd,KAAK,SAAO;AACX,YAAI,IAAI,WAAW,OAAO,IAAI,MAAM;AAElC,iBAAO,QAAQ,IAAI,KAAK,IAAI,UAAQ;AAClC,mBAAO;AAAA,cACL,IAAI,KAAK;AAAA,cACT,aAAa,KAAK,eAAe,KAAK;AAAA,cACtC,eAAe,KAAK,iBAAiB,KAAK;AAAA,cAC1C,MAAM,KAAK,eAAe,KAAK;AAAA;AAAA,cAC/B,QAAQ,KAAK,iBAAiB,KAAK;AAAA;AAAA,cACnC,QAAQ,KAAK,UAAU;AAAA,cACvB,aAAa,KAAK,eAAe;AAAA,cACjC,eAAe,KAAK,iBAAiB;AAAA,cACrC,cAAc,KAAK,gBAAgB;AAAA,cACnC,YAAY,KAAK,gBAAgB;AAAA;AAAA,YAC7C;AAAA,UACA,CAAS;AAAA,QACF;AAAA,MACP,CAAK,EACA,MAAM,SAAO;AACZD,sBAAY,MAAA,MAAA,OAAA,gCAAA,YAAY,GAAG;AAAA,MAEjC,CAAK;AAAA,IACL;AAGAE,kBAAAA,UAAU,MAAM;AAKd;AAGA;AAGA;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxYD,GAAG,WAAWC,SAAe;"}