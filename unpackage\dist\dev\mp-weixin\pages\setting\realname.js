"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const common_assets = require("../../common/assets.js");
const uniPopup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
const _sfc_main = {
  components: {
    uniPopup
  },
  data() {
    return {
      real_name: "",
      id_card_number: "",
      checked: false,
      auth_status: 0,
      // 0未认证 1待审核 2已认证 3失败
      verify_remark: "",
      loading: false,
      tipsTitle: "",
      userInfo: {
        phone: ""
      }
    };
  },
  computed: {
    isReadonly() {
      return this.auth_status === 2 || this.auth_status === 1;
    },
    canSubmit() {
      if (this.auth_status === 2)
        return false;
      return this.real_name && this.id_card_number && !this.isReadonly && !this.loading;
    }
  },
  onLoad() {
    common_vendor.index.showLoading({
      title: "加载中...",
      mask: true
    });
    Promise.all([
      this.loadAuthInfo(),
      this.loadUserInfo()
    ]).catch((err) => {
      common_vendor.index.__f__("error", "at pages/setting/realname.vue:147", "初始化数据失败:", err);
      this.opTipsPopup("加载失败，请重试");
    }).finally(() => {
      common_vendor.index.hideLoading();
    });
  },
  methods: {
    // 加载认证信息
    loadAuthInfo() {
      const that = this;
      return new Promise((resolve, reject) => {
        api_social.getRealAuthInfo().then((res) => {
          const isSuccess = res.code === 200 || res.status === 200 || res.msg === "success";
          if (isSuccess) {
            if (res.data) {
              that.auth_status = res.data.auth_status || 0;
              that.real_name = res.data.real_name || "";
              that.id_card_number = res.data.id_card_number || "";
              that.verify_remark = res.data.auth_remark || res.data.verify_remark || "";
              common_vendor.index.__f__("log", "at pages/setting/realname.vue:169", "获取认证信息成功:", res.data);
            }
            resolve(res.data || {});
          } else {
            common_vendor.index.__f__("error", "at pages/setting/realname.vue:173", "获取认证信息失败:", res.msg);
            reject(new Error(res.msg || "获取认证信息失败"));
          }
        }).catch((err) => {
          common_vendor.index.__f__("error", "at pages/setting/realname.vue:177", "获取认证信息请求错误:", err);
          reject(err);
        });
      });
    },
    // 加载用户信息
    loadUserInfo() {
      const that = this;
      const storeUserInfo = this.$store.state.userInfo;
      if (storeUserInfo && storeUserInfo.phone) {
        this.userInfo.phone = storeUserInfo.phone;
      }
      return new Promise((resolve, reject) => {
        api_social.getUserSocialInfo().then((res) => {
          const isSuccess = res.code === 200 || res.status === 200 || res.msg === "success";
          if (isSuccess) {
            if (res.data) {
              common_vendor.index.__f__("log", "at pages/setting/realname.vue:201", "获取用户信息成功:", res.data);
              that.userInfo = {
                ...that.userInfo,
                ...res.data
              };
              common_vendor.index.setStorageSync("USER_INFO", res.data);
            }
            resolve(res.data || {});
          } else {
            common_vendor.index.__f__("error", "at pages/setting/realname.vue:212", "获取用户信息失败:", res.msg);
            reject(new Error(res.msg || "获取用户信息失败"));
          }
        }).catch((err) => {
          common_vendor.index.__f__("error", "at pages/setting/realname.vue:216", "获取用户信息请求错误:", err);
          reject(err);
        });
      });
    },
    // 获取按钮文本
    getButtonText() {
      if (!this.userInfo.phone) {
        return "请先绑定手机号";
      }
      if (!this.real_name || !this.id_card_number) {
        return "请填写认证信息";
      }
      switch (this.auth_status) {
        case 0:
          return "提交认证";
        case 1:
          return "审核中";
        case 2:
          return "已认证";
        case 3:
          return "重新提交";
        default:
          return "提交认证";
      }
    },
    // 姓名输入验证
    nameBlur() {
      if (this.real_name && this.real_name.trim() && this.real_name.length < 2) {
        this.opTipsPopup("姓名长度不能少于2个字符");
      }
    },
    // 身份证号输入验证
    idCardBlur() {
      common_vendor.index.__f__("log", "at pages/setting/realname.vue:253", "身份证号失去焦点验证:", this.id_card_number);
      if (this.id_card_number && this.id_card_number.trim() && !this.validateIdCard(this.id_card_number)) {
        this.opTipsPopup("身份证号格式不正确");
      }
    },
    // 身份证号验证
    validateIdCard(idCard) {
      if (!/^\d{17}[\dXx]$/.test(idCard)) {
        return false;
      }
      const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      const verify = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
      let sum = 0;
      for (let i = 0; i < 17; i++) {
        sum += parseInt(idCard[i]) * factor[i];
      }
      const check = verify[sum % 11];
      return idCard[17].toUpperCase() === check;
    },
    // 提交认证
    onSubmit() {
      if (this.auth_status === 2) {
        this.opTipsPopup("您已完成认证，无需重复提交");
        return;
      }
      if (!this.userInfo.phone) {
        this.opTipsPopup("请先绑定手机号后再进行实名认证");
        return;
      }
      if (!this.real_name.trim()) {
        this.opTipsPopup("请输入真实姓名");
        return;
      }
      if (!this.id_card_number.trim()) {
        this.opTipsPopup("请输入身份证号");
        return;
      }
      if (!this.validateIdCard(this.id_card_number)) {
        this.opTipsPopup("身份证号格式不正确");
        return;
      }
      if (!this.checked && this.auth_status !== 2) {
        this.opTipsPopup("请先同意实名认证服务协议");
        return;
      }
      if (this.loading)
        return;
      const that = this;
      this.loading = true;
      api_social.submitRealAuth({
        real_name: this.real_name.trim(),
        id_card_number: this.id_card_number.trim()
      }).then((res) => {
        const isSuccess = res.code === 200 || res.status === 200 || res.msg === "success";
        if (isSuccess) {
          that.opTipsPopup("提交成功，等待审核");
          Promise.all([
            that.loadAuthInfo(),
            that.loadUserInfo()
          ]).catch((err) => {
            common_vendor.index.__f__("error", "at pages/setting/realname.vue:335", "刷新数据失败:", err);
          });
        } else {
          that.opTipsPopup(res.msg || "提交失败");
        }
      }).catch((err) => {
        that.opTipsPopup("提交失败: " + (err && err.msg ? err.msg : "网络错误"));
      }).finally(() => {
        that.loading = false;
      });
    },
    // 撤销申请
    onCancel() {
      const that = this;
      common_vendor.index.showModal({
        title: "确认撤销",
        content: "确定要撤销认证申请吗？撤销后可以重新提交。",
        success: (res) => {
          if (res.confirm) {
            that.loading = true;
            api_social.cancelRealAuth().then((res2) => {
              const isSuccess = res2.code === 200 || res2.status === 200 || res2.msg === "success";
              if (isSuccess) {
                that.opTipsPopup("撤销成功");
                Promise.all([
                  that.loadAuthInfo(),
                  that.loadUserInfo()
                ]).catch((err) => {
                  common_vendor.index.__f__("error", "at pages/setting/realname.vue:368", "刷新数据失败:", err);
                });
              } else {
                that.opTipsPopup(res2.msg || "撤销失败");
              }
            }).catch((err) => {
              that.opTipsPopup("撤销失败: " + (err && err.msg ? err.msg : "网络错误"));
            }).finally(() => {
              that.loading = false;
            });
          }
        }
      });
    },
    // 打开协议
    openProtocol() {
      common_vendor.index.navigateTo({
        url: "/pages/setting/xinxuan?type=5"
      });
    },
    // 复选框变化处理
    onCheckboxChange(e) {
      common_vendor.index.__f__("log", "at pages/setting/realname.vue:392", "协议勾选变化:", !this.checked, "->", this.checked);
      this.$set(this, "checked", !this.checked);
    },
    // 显示提示弹窗
    opTipsPopup(msg, duration = 2e3) {
      this.tipsTitle = msg;
      this.$refs.tipsPopup.open();
      setTimeout(() => {
        this.$refs.tipsPopup.close();
      }, duration);
    },
    // 绑定手机号
    bindMobileClick(e) {
      let that = this;
      if (e.detail.errMsg === "getPhoneNumber:ok") {
        common_vendor.index.showLoading({
          title: that.userInfo.phone ? "换绑授权中..." : "绑定授权中...",
          mask: true
        });
        that.$util.userBindingPhone(e.detail).then((res) => {
          common_vendor.index.hideLoading();
          const isSuccess = res.code == 200 || res.status == 200 || res.msg === "success";
          if (isSuccess) {
            that.userInfo.phone = res.data.phone || res.data.mobile;
            that.opTipsPopup(res.msg || "手机号绑定成功");
          } else {
            that.opTipsPopup(res.msg || "绑定失败");
          }
        }).catch((err) => {
          common_vendor.index.hideLoading();
          that.opTipsPopup("绑定失败: " + (typeof err === "string" ? err : "网络错误"));
        });
      } else {
        that.opTipsPopup("获取手机号失败");
      }
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.userInfo.phone || "未绑定"),
    b: common_assets._imports_0$8,
    c: common_vendor.t($data.userInfo.phone ? "换绑" : "绑定"),
    d: common_vendor.o((...args) => $options.bindMobileClick && $options.bindMobileClick(...args)),
    e: $options.isReadonly,
    f: common_vendor.o((...args) => $options.nameBlur && $options.nameBlur(...args)),
    g: $data.real_name,
    h: common_vendor.o(($event) => $data.real_name = $event.detail.value),
    i: $options.isReadonly,
    j: common_vendor.o((...args) => $options.idCardBlur && $options.idCardBlur(...args)),
    k: $data.id_card_number,
    l: common_vendor.o(($event) => $data.id_card_number = $event.detail.value),
    m: $data.auth_status === 3 && $data.verify_remark
  }, $data.auth_status === 3 && $data.verify_remark ? {
    n: common_vendor.t($data.verify_remark)
  } : {}, {
    o: $data.auth_status === 2
  }, $data.auth_status === 2 ? {} : {}, {
    p: $data.auth_status === 1
  }, $data.auth_status === 1 ? {} : {}, {
    q: $data.auth_status !== 2
  }, $data.auth_status !== 2 ? {
    r: $data.checked ? true : false,
    s: $options.isReadonly,
    t: common_vendor.o((...args) => $options.openProtocol && $options.openProtocol(...args)),
    v: common_vendor.o((...args) => $options.onCheckboxChange && $options.onCheckboxChange(...args))
  } : {}, {
    w: $data.auth_status === 1
  }, $data.auth_status === 1 ? {
    x: common_vendor.o((...args) => $options.onCancel && $options.onCancel(...args)),
    y: $data.loading
  } : {}, {
    z: common_vendor.t($data.loading ? "提交中..." : $options.getButtonText()),
    A: common_vendor.n($data.auth_status === 2 ? "btn-gray" : "bg2"),
    B: common_vendor.o((...args) => $options.onSubmit && $options.onSubmit(...args)),
    C: $data.loading || $data.auth_status === 2,
    D: common_vendor.t($data.tipsTitle),
    E: common_vendor.sr("tipsPopup", "a3319d4f-0"),
    F: common_vendor.p({
      type: "center"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a3319d4f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/setting/realname.js.map
