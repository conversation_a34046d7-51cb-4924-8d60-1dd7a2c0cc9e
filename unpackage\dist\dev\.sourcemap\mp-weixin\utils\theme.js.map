{"version": 3, "file": "theme.js", "sources": ["utils/theme.js"], "sourcesContent": ["const themeList = {\r\n\tgreen: '--view-theme: rgba(66,202,77,1);--view-theme-16: #42CA4D;--view-priceColor:#FF7600;--view-minorColor:rgba(108, 198, 94, 0.5);--view-minorColorT:rgba(66, 202, 77, 0.1);--view-bntColor:#FE960F;--view-op-ten: rgba(66,202,77, 0.1);--view-main-start:#70E038; --view-main-over:#42CA4D;--view-op-point-four: rgba(66,202,77, 0.04);--view-op-point-eight: rgba(66,202,77, 0.8);--view-linear:linear-gradient(180deg, rgba(66,202,77,0.2) 0%, rgba(255,255,255,0) 100%);',\r\n\tred: '--view-theme: rgba(233,51,35,1);--view-theme-16: #e93323;--view-priceColor:#e93323;--view-minorColor:rgba(233, 51, 35, 0.5);--view-minorColorT:rgba(233, 51, 35, 0.1);--view-bntColor:#FE960F;--view-op-ten: rgba(233,51,35, 0.1);--view-main-start:#FF6151; --view-main-over:#e93323;--view-op-point-four: rgba(233,51,35, 0.04);--view-op-point-eight: rgba(233,51,35, 0.8);--view-linear:linear-gradient(180deg, rgba(232,58,35,0.2) 0%, rgba(255,255,255,0) 100%)',\r\n\tblue: '--view-theme: rgba(29,176,252,1);--view-theme-16:#1db0fc;--view-priceColor:#FD502F;--view-minorColor:rgba(58, 139, 236, 0.5);--view-minorColorT:rgba(9, 139, 243, 0.1);--view-bntColor:#22CAFD;--view-op-ten: rgba(29,176,252, 0.1);--view-main-start:#40D1F4; --view-main-over:#1DB0FC;--view-op-point-four: rgba(29,176,252, 0.04);--view-op-point-eight: rgba(29,176,252, 0.8);--view-linear:linear-gradient(180deg, rgba(29,176,252,0.2) 0%, rgba(255,255,255,0) 100%);',\r\n\tpink: '--view-theme: rgba(255,68,143,1);--view-theme-16:#ff448f;--view-priceColor:#FF448F;--view-minorColor:rgba(255, 68, 143, 0.5);--view-minorColorT:rgba(255, 68, 143, 0.1);--view-bntColor:#282828;--view-op-ten: rgba(255,68,143, 0.1);--view-main-start:#FF67AD; --view-main-over:#FF448F;--view-op-point-four: rgba(255,68,143, 0.04);--view-op-point-eight: rgba(255,68,143, 0.8);--view-linear:linear-gradient(180deg, rgba(255,68,143,0.2) 0%, rgba(255,255,255,0) 100%);',\r\n\torange: '--view-theme: rgba(254,92,45,1); --view-theme-16:#FE5C2D;--view-priceColor:#FE5C2D;--view-minorColor:rgba(254, 92, 45, 0.5);--view-minorColorT:rgba(254, 92, 45, 0.1);--view-bntColor:#FDB000;--view-op-ten: rgba(254,92,45, 0.1);--view-main-start:#FF9445; --view-main-over:#FE5C2D;--view-op-point-four: rgba(254,92,45, 0.04);--view-op-point-eight: rgba(254,92,45, 0.8);--view-linear:linear-gradient(180deg, rgba(254,92,45,0.2) 0%, rgba(255,255,255,0) 100%);'\r\n}\r\n\r\nexport default themeList"], "names": [], "mappings": ";AAAK,MAAC,YAAY;AAAA,EACjB,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AACT;;"}