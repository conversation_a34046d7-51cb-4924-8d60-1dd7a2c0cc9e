"use strict";
const common_vendor = require("../common/vendor.js");
const api_user = require("../api/user.js");
const utils_cache = require("./cache.js");
require("./request.js");
const api_kefu = require("../api/kefu.js");
const store_index = require("../store/index.js");
const config_app = require("../config/app.js");
function silenceBindingSpread(app) {
  let puid = app.spid, code = app.code;
  puid = parseInt(puid);
  if (Number.isNaN(puid)) {
    puid = 0;
  }
  if ((code || puid) && store_index.store.state.app.token) {
    api_user.spread({
      puid,
      code
    }).then((res) => {
      app.spid = 0;
      app.code = 0;
    }).catch((res) => {
    });
  }
}
exports.VUE_APP_WS_URL = utils_cache.Cache.get("WORKERMAN_URL") || "";
function initWorkermanUrl() {
  if (!exports.VUE_APP_WS_URL) {
    api_kefu.getWorkermanUrl().then((res) => {
      utils_cache.Cache.set("WORKERMAN_URL", res.data.chat);
      exports.VUE_APP_WS_URL = res.data.chat;
    }).catch((err) => {
      common_vendor.index.__f__("warn", "at utils/index.js:166", "获取Workerman URL失败:", err);
    });
  }
}
const { HTTP_REQUEST_URL } = config_app.config;
const VUE_APP_API_URL = HTTP_REQUEST_URL;
exports.VUE_APP_API_URL = VUE_APP_API_URL;
exports.initWorkermanUrl = initWorkermanUrl;
exports.silenceBindingSpread = silenceBindingSpread;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/index.js.map
