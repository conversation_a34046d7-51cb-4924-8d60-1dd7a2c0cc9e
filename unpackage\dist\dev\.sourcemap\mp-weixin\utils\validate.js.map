{"version": 3, "file": "validate.js", "sources": ["utils/validate.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n\n/**\n * 验证小数点后两位及多个小数\n * money 金额\n*/ \nexport function isMoney(money) {\n  var reg = /(^[1-9]([0-9]+)?(\\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\\.[0-9]([0-9])?$)/\n  if (reg.test(money)) {\n    return true\n  } else {\n    return false\n  }\n}\n\n/**\n * 验证手机号码\n * money 金额\n*/ \nexport function checkPhone(c2543fff3bfa6f144c2f06a7de6cd10c0b650cae) {\n  var reg = /^1(3|4|5|6|7|8|9)\\d{9}$/\n  if (reg.test(c2543fff3bfa6f144c2f06a7de6cd10c0b650cae)) {\n    return true\n  } else {\n    return false\n  }\n}\n\n/**\n * 函数防抖 (只执行最后一次点击)\n * @param fn\n * @param delay\n * @returns {Function}\n * @constructor\n */\nexport const Debounce = (fn, t) => {\n  const delay = t || 500\n  let timer\n  return function() {\n    const args = arguments\n    if (timer) {\n      clearTimeout(timer)\n    }\n    timer = setTimeout(() => {\n      timer = null\n      fn.apply(this, args)\n    }, delay)\n  }\n}\n/**\n * 函数节流\n * @param fn\n * @param interval\n * @returns {Function}\n * @constructor\n */\nexport const Throttle = (fn, t) => {\n  let last\n  let timer\n  const interval = t || 500\n  return function() {\n    const args = arguments\n    const now = +new Date()\n    if (last && now - last < interval) {\n      clearTimeout(timer)\n      timer = setTimeout(() => {\n        last = now\n        fn.apply(this, args)\n      }, interval)\n    } else {\n      last = now\n      fn.apply(this, args)\n    }\n  }\n}\n\n/**\n * 验证字母数字\n */\nexport function alpha_num(value) {\n  return /^[a-zA-Z0-9]+$/.test(value);\n}\n\n/**\n * 验证中国手机号\n */\nexport function chs_phone(value) {\n  return /^1[3-9]\\d{9}$/.test(value);\n}\n\n/**\n * 必填验证\n */\nexport function required(value) {\n  return value !== null && value !== undefined && value !== '';\n}\n\n// 默认导出attrs对象\nconst attrs = {\n  required,\n  alpha_num,\n  chs_phone\n};\n\nexport default attrs;\n\n\n\n"], "names": [], "mappings": ";AA4CY,MAAC,WAAW,CAAC,IAAI,MAAM;AACjC,QAAM,QAAQ,KAAK;AACnB,MAAI;AACJ,SAAO,WAAW;AAChB,UAAM,OAAO;AACb,QAAI,OAAO;AACT,mBAAa,KAAK;AAAA,IACnB;AACD,YAAQ,WAAW,MAAM;AACvB,cAAQ;AACR,SAAG,MAAM,MAAM,IAAI;AAAA,IACpB,GAAE,KAAK;AAAA,EACT;AACH;;"}