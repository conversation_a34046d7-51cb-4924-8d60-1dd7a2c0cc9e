"use strict";
const common_vendor = require("../../common/vendor.js");
let recorderManager = null;
let audioContext = null;
if (typeof common_vendor.index.getRecorderManager === "function") {
  recorderManager = common_vendor.index.getRecorderManager();
}
const _sfc_main = {
  name: "jc-record",
  props: {
    voicePath: {
      type: String,
      default: ""
    },
    maxTime: {
      type: Number,
      default: 15
    },
    minTime: {
      type: Number,
      default: 5
    },
    autoShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      isRecording: false,
      isPlaying: false,
      recordTime: 0,
      playbackTime: 0,
      voiceFile: "",
      // 定时器
      recordTimer: null,
      playbackTimer: null,
      animationTimer: null,
      // Canvas相关
      canvasContext: null,
      canvasWidth: 0,
      canvasHeight: 0,
      centerX: 0,
      centerY: 0,
      indicatorRadius: 0,
      // 动画参数
      animationAngle: -0.5,
      frameInterval: 50
    };
  },
  computed: {
    formattedTime() {
      const time = this.isPlaying ? this.playbackTime : this.recordTime;
      const minutes = Math.floor(time / 60);
      const seconds = time % 60;
      return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    }
  },
  mounted() {
    this.initializeComponent();
  },
  beforeDestroy() {
    this.cleanup();
  },
  methods: {
    // 组件初始化
    async initializeComponent() {
      await this.requestPermissions();
      this.setupAudioContext();
      this.setupRecorderEvents();
      this.setupCanvasDimensions();
      if (this.autoShow) {
        this.$nextTick(() => this.showPicker());
      }
    },
    // 请求录音权限
    async requestPermissions() {
      try {
        await common_vendor.index.authorize({ scope: "scope.record" });
      } catch (error) {
        common_vendor.index.showModal({
          title: "权限申请",
          content: "需要录音权限才能使用此功能，请在设置中开启录音权限",
          showCancel: true,
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.openSetting();
            }
          }
        });
      }
    },
    // 设置音频上下文
    setupAudioContext() {
      audioContext = common_vendor.index.createInnerAudioContext();
      audioContext.onEnded(() => {
        this.isPlaying = false;
        this.playbackTime = 0;
        this.clearTimer("playbackTimer");
      });
      audioContext.onError((error) => {
        this.handleAudioError(error);
      });
    },
    // 设置录音事件
    setupRecorderEvents() {
      if (!recorderManager) {
        common_vendor.index.__f__("warn", "at components/jc-record/jc-record.vue:170", "录音管理器不可用");
        return;
      }
      recorderManager.onStop((result) => {
        this.voiceFile = result.tempFilePath;
        this.$emit("recorded", result.tempFilePath);
      });
      recorderManager.onError((error) => {
        this.handleRecordError(error);
      });
    },
    // 设置Canvas尺寸
    setupCanvasDimensions() {
      this.$nextTick(() => {
        const query = common_vendor.index.createSelectorQuery().in(this);
        query.select(".record-canvas").boundingClientRect((rect) => {
          if (rect) {
            this.canvasWidth = rect.width;
            this.canvasHeight = rect.height;
            this.centerX = rect.width / 2;
            this.centerY = rect.height / 2;
            this.canvasContext = common_vendor.index.createCanvasContext("recordCanvas", this);
          }
        });
        query.select(".record-indicator").boundingClientRect((rect) => {
          if (rect) {
            this.indicatorRadius = rect.width / 2 + 4;
          }
        });
        query.exec();
      });
    },
    // 显示录音面板
    showPicker() {
      this.visible = true;
      this.$emit("show");
    },
    // 关闭录音面板
    closePicker() {
      this.visible = false;
      this.resetState();
      this.$emit("close");
    },
    // 重置状态
    resetState() {
      this.stopPlayback();
      this.voiceFile = "";
      this.recordTime = 0;
      this.playbackTime = 0;
      this.clearAllTimers();
      this.clearCanvas();
    },
    // 触摸开始
    handleTouchStart() {
      this.stopPlayback();
      this.voiceFile = "";
      this.recordTime = 0;
    },
    // 开始录音
    startRecording() {
      if (this.isRecording)
        return;
      this.isRecording = true;
      this.recordTime = 0;
      if (!recorderManager) {
        common_vendor.index.showToast({
          title: "当前环境不支持录音",
          icon: "none"
        });
        return;
      }
      recorderManager.start({
        duration: this.maxTime * 1e3,
        sampleRate: 16e3,
        numberOfChannels: 1,
        encodeBitRate: 96e3,
        format: "mp3"
      });
      this.recordTimer = setInterval(() => {
        this.recordTime++;
        if (this.recordTime >= this.maxTime) {
          this.stopRecording();
        }
      }, 1e3);
      this.startRecordingAnimation();
    },
    // 停止录音
    stopRecording() {
      if (!this.isRecording)
        return;
      const recordDuration = this.recordTime;
      this.isRecording = false;
      this.clearTimer("recordTimer");
      this.clearTimer("animationTimer");
      this.clearCanvas();
      if (recordDuration < this.minTime) {
        if (recordDuration <= 0)
          return;
        common_vendor.index.showToast({
          title: `录音时长不能少于${this.minTime}秒`,
          icon: "none"
        });
        this.recordTime = 0;
        return;
      }
      if (recorderManager) {
        recorderManager.stop();
      }
    },
    // 开始录音动画
    startRecordingAnimation() {
      if (!this.canvasContext)
        return;
      this.drawBackgroundCircle();
      this.animationAngle = -0.5;
      this.animationTimer = setInterval(() => {
        this.drawProgressCircle();
      }, this.frameInterval);
    },
    // 绘制背景圆环
    drawBackgroundCircle() {
      this.canvasContext.beginPath();
      this.canvasContext.setStrokeStyle("#fe3b54");
      this.canvasContext.setGlobalAlpha(0.3);
      this.canvasContext.setLineWidth(3);
      this.canvasContext.arc(this.centerX, this.centerY, this.indicatorRadius, 0, 2 * Math.PI);
      this.canvasContext.stroke();
      this.canvasContext.draw();
    },
    // 绘制进度圆环
    drawProgressCircle() {
      this.canvasContext.beginPath();
      this.canvasContext.setStrokeStyle("#fe3b54");
      this.canvasContext.setGlobalAlpha(1);
      this.canvasContext.setLineWidth(3);
      const endAngle = this.animationAngle + 2 / (this.maxTime * 1e3 / this.frameInterval);
      this.canvasContext.arc(
        this.centerX,
        this.centerY,
        this.indicatorRadius,
        -0.5 * Math.PI,
        endAngle * Math.PI,
        false
      );
      this.canvasContext.stroke();
      this.canvasContext.draw(true);
      this.animationAngle = endAngle;
    },
    // 清除Canvas
    clearCanvas() {
      if (!this.canvasContext)
        return;
      this.canvasContext.setFillStyle("#fff");
      this.canvasContext.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
      this.canvasContext.draw();
    },
    // 开始播放
    startPlayback() {
      if (!this.voiceFile || this.isPlaying)
        return;
      audioContext.src = this.voiceFile;
      audioContext.stop();
      audioContext.play();
      this.isPlaying = true;
      this.playbackTime = this.recordTime;
      this.playbackTimer = setInterval(() => {
        this.playbackTime--;
        if (this.playbackTime <= 0) {
          this.stopPlayback();
        }
      }, 1e3);
    },
    // 停止播放
    stopPlayback() {
      if (audioContext) {
        audioContext.stop();
      }
      this.isPlaying = false;
      this.playbackTime = 0;
      this.clearTimer("playbackTimer");
    },
    // 确认录音
    confirmRecording() {
      if (!this.voiceFile)
        return;
      this.$emit("confirm", this.voiceFile);
      this.closePicker();
    },
    // 清除指定定时器
    clearTimer(timerName) {
      if (this[timerName]) {
        clearInterval(this[timerName]);
        this[timerName] = null;
      }
    },
    // 清除所有定时器
    clearAllTimers() {
      this.clearTimer("recordTimer");
      this.clearTimer("playbackTimer");
      this.clearTimer("animationTimer");
    },
    // 处理录音错误
    handleRecordError(error) {
      this.isRecording = false;
      this.clearAllTimers();
      this.clearCanvas();
      common_vendor.index.showToast({
        title: "录音失败，请重试",
        icon: "none"
      });
      this.$emit("error", { type: "record", error });
    },
    // 处理音频错误
    handleAudioError(error) {
      this.isPlaying = false;
      this.clearTimer("playbackTimer");
      common_vendor.index.showToast({
        title: "播放失败",
        icon: "none"
      });
      this.$emit("error", { type: "audio", error });
    },
    // 阻止默认事件
    preventMove() {
      return false;
    },
    // 组件清理
    cleanup() {
      this.clearAllTimers();
      if (audioContext) {
        audioContext.destroy();
        audioContext = null;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.visible
  }, $data.visible ? {
    b: common_vendor.o((...args) => $options.closePicker && $options.closePicker(...args)),
    c: common_vendor.o((...args) => $options.preventMove && $options.preventMove(...args))
  } : {}, {
    d: common_vendor.t($options.formattedTime),
    e: common_vendor.t($props.minTime),
    f: common_vendor.t($props.maxTime),
    g: $data.voiceFile && $data.isPlaying
  }, $data.voiceFile && $data.isPlaying ? {
    h: common_vendor.o((...args) => $options.stopPlayback && $options.stopPlayback(...args))
  } : {}, {
    i: $data.voiceFile && !$data.isPlaying
  }, $data.voiceFile && !$data.isPlaying ? {
    j: common_vendor.o((...args) => $options.startPlayback && $options.startPlayback(...args))
  } : {}, {
    k: $data.isRecording ? 1 : "",
    l: common_vendor.o((...args) => $options.handleTouchStart && $options.handleTouchStart(...args)),
    m: common_vendor.o((...args) => $options.startRecording && $options.startRecording(...args)),
    n: common_vendor.o((...args) => $options.stopRecording && $options.stopRecording(...args)),
    o: common_vendor.o((...args) => $options.preventMove && $options.preventMove(...args)),
    p: $data.voiceFile
  }, $data.voiceFile ? {
    q: common_vendor.o((...args) => $options.confirmRecording && $options.confirmRecording(...args))
  } : {}, {
    r: $data.visible ? 1 : "",
    s: common_vendor.o((...args) => $options.showPicker && $options.showPicker(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/jc-record/jc-record.js.map
