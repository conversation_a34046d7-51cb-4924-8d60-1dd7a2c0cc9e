"use strict";
const common_vendor = require("../../../common/vendor.js");
const mixins_SendVerifyCode = require("../../../mixins/SendVerifyCode.js");
const api_user = require("../../../api/user.js");
const api_social = require("../../../api/social.js");
const api_public = require("../../../api/public.js");
const utils_index = require("../../../utils/index.js");
const mixins_color = require("../../../mixins/color.js");
const common_assets = require("../../../common/assets.js");
const BACK_URL = "login_back_url";
const Verify = () => "../components/verify/index.js";
const _sfc_main = {
  name: "Login",
  components: {
    Verify
  },
  mixins: [mixins_SendVerifyCode.sendVerifyCode, mixins_color.colors],
  data: function() {
    return {
      copyRight: "",
      inAnimation: false,
      protocol: false,
      navList: [this.$t(`快速登录`), this.$t(`账号登录`)],
      current: 1,
      account: "",
      password: "",
      captcha: "",
      formItem: 1,
      type: "login",
      logoUrl: "",
      keyCode: "",
      codeUrl: "",
      codeVal: "",
      isShowCode: false,
      appLoginStatus: false,
      // 微信登录强制绑定手机号码状态
      appUserInfo: null,
      // 微信登录保存的用户信息
      appleLoginStatus: false,
      // 苹果登录强制绑定手机号码状态
      appleUserInfo: null,
      appleShow: false,
      // 苹果登录版本必须要求ios13以上的
      keyLock: true
    };
  },
  watch: {
    formItem: function(nval, oVal) {
      if (nval == 1) {
        this.type = "login";
      } else {
        this.type = "register";
      }
    }
  },
  onLoad() {
    let self = this;
    common_vendor.index.getSystemInfo({
      success: (res) => {
        if (res.platform.toLowerCase() == "ios" && this.getSystem(res.system)) {
          self.appleShow = true;
        }
      }
    });
    if (common_vendor.index.getStorageSync("copyRight").copyrightContext) {
      this.copyRight = common_vendor.index.getStorageSync("copyRight").copyrightContext;
    }
  },
  mounted() {
    this.getLogoImage();
  },
  methods: {
    ChangeIsDefault(e) {
      this.$set(this, "protocol", !this.protocol);
    },
    privacy(type) {
      common_vendor.index.navigateTo({
        url: "/pages/setting/xinxuan?type=" + type
      });
    },
    // IOS 版本号判断
    getSystem(system) {
      let str;
      system.toLowerCase().indexOf("ios") === -1 ? str = system : str = system.split(" ")[1];
      if (str.indexOf("."))
        return str.split(".")[0] >= 13;
      return str >= 13;
    },
    // 苹果登录
    appleLogin() {
      let self = this;
      this.account = "";
      this.captcha = "";
      if (!self.protocol) {
        this.inAnimation = true;
        return self.$util.Tips({
          title: "请先阅读并同意协议"
        });
      }
      common_vendor.index.showLoading({
        title: this.$t(`登录中`)
      });
      common_vendor.index.login({
        provider: "apple",
        timeout: 1e4,
        success(loginRes) {
          common_vendor.index.getUserInfo({
            provider: "apple",
            success: function(infoRes) {
              self.appleUserInfo = infoRes.userInfo;
              self.appleLoginApi();
            },
            fail() {
              common_vendor.index.showToast({
                title: self.$t(`获取用户信息失败`),
                icon: "none",
                duration: 2e3
              });
            },
            complete() {
              common_vendor.index.hideLoading();
            }
          });
        },
        fail(error) {
          common_vendor.index.__f__("log", "at pages/users/login/index.vue:230", error);
        }
      });
    },
    // 苹果登录Api
    appleLoginApi() {
      let self = this;
      api_user.appleLogin({
        openId: self.appleUserInfo.openId,
        email: self.appleUserInfo.email || "",
        phone: this.account,
        captcha: this.captcha
      }).then(({ data }) => {
        if (data.isbind) {
          common_vendor.index.showModal({
            title: self.$t(`提示`),
            content: self.$t(`请绑定手机号后，继续操作`),
            showCancel: false,
            success: function(res) {
              if (res.confirm) {
                self.current = 1;
                self.appleLoginStatus = true;
              }
            }
          });
        } else {
          self.$store.commit("LOGIN", {
            token: data.token,
            time: data.expires_time - self.$Cache.time()
          });
          let backUrl = self.$Cache.get(BACK_URL) || "/pages/index/index";
          self.$Cache.clear(BACK_URL);
          self.$store.commit("SETUID", data.userInfo.uid);
          api_user.getUserInfo().then((res) => {
            api_social.getUserSocialInfo().then((socialRes) => {
              if (socialRes.data) {
                self.$store.commit("UPDATE_USERINFO", socialRes.data);
              }
              common_vendor.index.reLaunch({
                url: backUrl
              });
            }).catch(() => {
              common_vendor.index.reLaunch({
                url: backUrl
              });
            });
          }).catch(() => {
            common_vendor.index.reLaunch({
              url: backUrl
            });
          });
        }
      }).catch((error) => {
        common_vendor.index.showModal({
          title: self.$t(`提示`),
          content: self.$t(`错误信息`) + `${error}`,
          success: function(res) {
            if (res.confirm) {
              common_vendor.index.__f__("log", "at pages/users/login/index.vue:299", self.$t(`用户点击确定`));
            } else if (res.cancel) {
              common_vendor.index.__f__("log", "at pages/users/login/index.vue:301", self.$t(`用户点击取消`));
            }
          }
        });
      });
    },
    // App微信登录
    wxLogin() {
      let self = this;
      this.account = "";
      this.captcha = "";
      if (!self.protocol) {
        this.inAnimation = true;
        return self.$util.Tips({
          title: "请先阅读并同意协议"
        });
      }
      common_vendor.index.showLoading({
        title: self.$t(`登录中`)
      });
      common_vendor.index.login({
        provider: "weixin",
        success: function(loginRes) {
          common_vendor.index.getUserInfo({
            provider: "weixin",
            success: function(infoRes) {
              self.appUserInfo = infoRes.userInfo;
              self.wxLoginApi();
            },
            fail() {
              common_vendor.index.showToast({
                title: self.$t(`获取用户信息失败`),
                icon: "none",
                duration: 2e3
              });
            },
            complete() {
              common_vendor.index.hideLoading();
            }
          });
        },
        fail() {
          common_vendor.index.showToast({
            title: self.$t(`登录失败`),
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    wxLoginApi() {
      let self = this;
      wechatAppAuth({
        userInfo: self.appUserInfo,
        phone: this.account,
        code: this.captcha
      }).then(({ data }) => {
        if (data.isbind) {
          common_vendor.index.showModal({
            title: self.$t(`提示`),
            content: self.$t(`请绑定手机号后，继续操作`),
            showCancel: false,
            success: function(res) {
              if (res.confirm) {
                self.current = 1;
                self.appLoginStatus = true;
              }
            }
          });
        } else {
          self.$store.commit("LOGIN", {
            token: data.token,
            time: data.expires_time - self.$Cache.time()
          });
          let backUrl = self.$Cache.get(BACK_URL) || "/pages/index/index";
          self.$Cache.clear(BACK_URL);
          self.$store.commit("SETUID", data.userInfo.uid);
          api_user.getUserInfo().then((res) => {
            api_social.getUserSocialInfo().then((socialRes) => {
              if (socialRes.data) {
                self.$store.commit("UPDATE_USERINFO", socialRes.data);
              }
              common_vendor.index.reLaunch({
                url: backUrl
              });
            }).catch(() => {
              common_vendor.index.reLaunch({
                url: backUrl
              });
            });
          }).catch(() => {
            common_vendor.index.reLaunch({
              url: backUrl
            });
          });
        }
      }).catch((error) => {
        common_vendor.index.showModal({
          title: self.$t(`提示`),
          content: self.$t(`错误信息`) + `${error}`,
          success: function(res) {
            if (res.confirm) {
              common_vendor.index.__f__("log", "at pages/users/login/index.vue:416", self.$t(`用户点击确定`));
            } else if (res.cancel) {
              common_vendor.index.__f__("log", "at pages/users/login/index.vue:418", self.$t(`用户点击取消`));
            }
          }
        });
      });
    },
    again() {
      this.codeUrl = utils_index.VUE_APP_API_URL + "/sms_captcha?key=" + this.keyCode + Date.parse(/* @__PURE__ */ new Date());
    },
    success(data) {
      this.$refs.verify.hide();
      api_user.getCodeApi().then((res) => {
        this.keyCode = res.data.key;
        this.getCode(data);
      }).catch((res) => {
        this.$util.Tips({
          title: res
        });
      });
    },
    code() {
      let that = this;
      if (!that.protocol) {
        this.inAnimation = true;
        return that.$util.Tips({
          title: "请先阅读并同意协议"
        });
      }
      if (!that.account)
        return that.$util.Tips({
          title: that.$t(`请填写手机号码`)
        });
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account))
        return that.$util.Tips({
          title: that.$t(`请输入正确的手机号码`)
        });
      this.$refs.verify.show();
    },
    async getLogoImage() {
      let that = this;
      api_public.getLogo().then((res) => {
        that.logoUrl = res.data.logo_url;
      });
    },
    async loginMobile() {
      let that = this;
      if (!that.protocol) {
        this.inAnimation = true;
        return that.$util.Tips({
          title: "请先阅读并同意协议"
        });
      }
      if (!that.account)
        return that.$util.Tips({
          title: that.$t(`请填写手机号码`)
        });
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account))
        return that.$util.Tips({
          title: that.$t(`请输入正确的手机号码`)
        });
      if (!that.captcha)
        return that.$util.Tips({
          title: that.$t(`请填写验证码`)
        });
      if (!/^[\w\d]+$/i.test(that.captcha))
        return that.$util.Tips({
          title: that.$t(`请输入正确的验证码`)
        });
      if (that.appLoginStatus) {
        that.wxLoginApi();
      } else if (that.appleLoginStatus) {
        that.appleLoginApi();
      } else {
        if (this.keyLock) {
          this.keyLock = !this.keyLock;
        } else {
          return that.$util.Tips({
            title: that.$t(`请勿重复点击`)
          });
        }
        api_user.loginMobile({
          phone: that.account,
          captcha: that.captcha,
          spread: that.$Cache.get("spread")
        }).then((res) => {
          let data = res.data;
          that.$store.commit("LOGIN", {
            token: data.token,
            time: data.expires_time - this.$Cache.time()
          });
          let backUrl = that.$Cache.get(BACK_URL) || "/pages/index/index";
          that.$Cache.clear(BACK_URL);
          api_user.getUserInfo().then((res2) => {
            this.keyLock = true;
            that.$store.commit("SETUID", res2.data.uid);
            api_social.getUserSocialInfo().then((socialRes) => {
              if (socialRes.data) {
                that.$store.commit("UPDATE_USERINFO", socialRes.data);
              }
              if (backUrl.indexOf("/pages/users/login/index") !== -1) {
                backUrl = "/pages/index/index";
              }
              common_vendor.index.reLaunch({
                url: backUrl
              });
            }).catch(() => {
              if (backUrl.indexOf("/pages/users/login/index") !== -1) {
                backUrl = "/pages/index/index";
              }
              common_vendor.index.reLaunch({
                url: backUrl
              });
            });
          });
        }).catch((res) => {
          this.keyLock = true;
          that.$util.Tips({
            title: res
          });
        });
      }
    },
    async register() {
      let that = this;
      if (!that.protocol) {
        this.inAnimation = true;
        return that.$util.Tips({
          title: "请先阅读并同意协议"
        });
      }
      if (!that.account)
        return that.$util.Tips({
          title: that.$t(`请填写手机号码`)
        });
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account))
        return that.$util.Tips({
          title: that.$t(`请输入正确的手机号码`)
        });
      if (!that.captcha)
        return that.$util.Tips({
          title: that.$t(`请填写验证码`)
        });
      if (!/^[\w\d]+$/i.test(that.captcha))
        return that.$util.Tips({
          title: that.$t(`请输入正确的验证码`)
        });
      if (!that.password)
        return that.$util.Tips({
          title: that.$t(`请填写密码`)
        });
      if (/^([0-9]|[a-z]|[A-Z]){0,6}$/i.test(that.password))
        return that.$util.Tips({
          title: that.$t(`您输入的密码过于简单`)
        });
      api_user.register({
        account: that.account,
        captcha: that.captcha,
        password: that.password,
        spread: that.$Cache.get("spread")
      }).then((res) => {
        that.$util.Tips({
          title: res
        });
        that.formItem = 1;
      }).catch((res) => {
        that.$util.Tips({
          title: res
        });
      });
    },
    async getCode(data) {
      let that = this;
      if (!that.protocol) {
        this.inAnimation = true;
        return that.$util.Tips({
          title: "请先阅读并同意协议"
        });
      }
      if (!that.account)
        return that.$util.Tips({
          title: that.$t(`请填写手机号码`)
        });
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account))
        return that.$util.Tips({
          title: that.$t(`请输入正确的手机号码`)
        });
      if (that.formItem == 2)
        that.type = "register";
      await api_user.registerVerify({
        phone: that.account,
        type: that.type,
        key: that.keyCode,
        captchaType: this.captchaType,
        captchaVerification: data.captchaVerification
      }).then((res) => {
        this.sendCode();
        that.$util.Tips({
          title: res.msg
        });
      }).catch((res) => {
        that.$util.Tips({
          title: res
        });
      });
    },
    navTap: function(index) {
      this.current = index;
    },
    async submit() {
      let that = this;
      if (!that.protocol) {
        this.inAnimation = true;
        return that.$util.Tips({
          title: "请先阅读并同意协议"
        });
      }
      if (!that.account)
        return that.$util.Tips({
          title: that.$t(`请填写账号`)
        });
      if (!/^[\w\d]{5,16}$/i.test(that.account))
        return that.$util.Tips({
          title: that.$t(`请输入正确的账号`)
        });
      if (!that.password)
        return that.$util.Tips({
          title: that.$t(`请填写密码`)
        });
      if (this.keyLock) {
        this.keyLock = !this.keyLock;
      } else {
        return that.$util.Tips({
          title: that.$t(`请勿重复点击`)
        });
      }
      api_user.loginH5({
        account: that.account,
        password: that.password,
        spread: that.$Cache.get("spread")
      }).then(({ data }) => {
        that.$store.commit("LOGIN", {
          token: data.token,
          time: data.expires_time - this.$Cache.time()
        });
        let backUrl = that.$Cache.get(BACK_URL) || "/pages/index/index";
        that.$Cache.clear(BACK_URL);
        api_user.getUserInfo().then((res) => {
          this.keyLock = true;
          that.$store.commit("SETUID", res.data.uid);
          api_social.getUserSocialInfo().then((socialRes) => {
            if (socialRes.data) {
              that.$store.commit("UPDATE_USERINFO", socialRes.data);
            }
            common_vendor.index.reLaunch({
              url: backUrl
            });
          }).catch(() => {
            common_vendor.index.reLaunch({
              url: backUrl
            });
          });
        }).catch((error) => {
          this.keyLock = true;
        });
      }).catch((e) => {
        this.keyLock = true;
        that.$util.Tips({
          title: e
        });
      });
    }
  }
};
if (!Array) {
  const _component_navbar = common_vendor.resolveComponent("navbar");
  const _component_Verify = common_vendor.resolveComponent("Verify");
  (_component_navbar + _component_Verify)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: _ctx.logoUrl,
    b: common_vendor.t(_ctx.$t(`账号登录`)),
    c: common_vendor.t(_ctx.$t(`为了提供更好的服务，请登录您的账号`)),
    d: _ctx.current !== 1
  }, _ctx.current !== 1 ? {
    e: common_assets._imports_0$16,
    f: _ctx.$t(`输入手机号码`),
    g: _ctx.account,
    h: common_vendor.o(($event) => _ctx.account = $event.detail.value),
    i: common_assets._imports_1$6,
    j: _ctx.$t(`填写登录密码`),
    k: _ctx.password,
    l: common_vendor.o(($event) => _ctx.password = $event.detail.value)
  } : {}, {
    m: _ctx.current !== 0 || _ctx.appLoginStatus || _ctx.appleLoginStatus
  }, _ctx.current !== 0 || _ctx.appLoginStatus || _ctx.appleLoginStatus ? {
    n: common_assets._imports_0$16,
    o: _ctx.$t(`输入手机号码`),
    p: _ctx.account,
    q: common_vendor.o(($event) => _ctx.account = $event.detail.value),
    r: common_assets._imports_2$13,
    s: _ctx.$t(`填写验证码`),
    t: _ctx.captcha,
    v: common_vendor.o(($event) => _ctx.captcha = $event.detail.value),
    w: common_vendor.t(_ctx.text),
    x: common_vendor.n(_ctx.disabled ? "active" : ""),
    y: common_vendor.o((...args) => $options.code && $options.code(...args))
  } : {}, {
    z: _ctx.current !== 1
  }, _ctx.current !== 1 ? {
    A: common_vendor.t(_ctx.$t(`忘记密码可以使用验证码登录`))
  } : {}, {
    B: _ctx.current !== 0
  }, _ctx.current !== 0 ? {
    C: common_vendor.t(_ctx.$t(`登录`)),
    D: common_vendor.o((...args) => $options.loginMobile && $options.loginMobile(...args))
  } : {}, {
    E: _ctx.current === 0
  }, _ctx.current === 0 ? {
    F: common_vendor.t(_ctx.$t(`登录`)),
    G: common_vendor.o((...args) => $options.submit && $options.submit(...args))
  } : {}, {
    H: _ctx.current == 0
  }, _ctx.current == 0 ? {
    I: common_vendor.t(_ctx.$t(`验证码登录`)),
    J: common_vendor.o(($event) => _ctx.current = 1)
  } : {}, {
    K: _ctx.current == 1
  }, _ctx.current == 1 ? {
    L: common_vendor.t(_ctx.$t(`账号登录`)),
    M: common_vendor.o(($event) => _ctx.current = 0)
  } : {}, {
    N: _ctx.protocol ? true : false,
    O: common_vendor.n(_ctx.inAnimation ? "trembling" : ""),
    P: common_vendor.o(($event) => _ctx.inAnimation = false),
    Q: common_vendor.t(_ctx.$t(`已阅读并同意`)),
    R: common_vendor.t(_ctx.$t(`《用户协议》`)),
    S: common_vendor.o(($event) => $options.privacy(4)),
    T: common_vendor.t(_ctx.$t(`与`)),
    U: common_vendor.t(_ctx.$t(`《隐私协议》`)),
    V: common_vendor.o(($event) => $options.privacy(3)),
    W: common_vendor.o((...args) => $options.ChangeIsDefault && $options.ChangeIsDefault(...args)),
    X: _ctx.copyRight
  }, _ctx.copyRight ? {
    Y: common_vendor.t(_ctx.copyRight)
  } : {}, {
    Z: common_vendor.sr("verify", "445f608b-1"),
    aa: common_vendor.o($options.success),
    ab: common_vendor.p({
      captchaType: _ctx.captchaType,
      imgSize: {
        width: "330px",
        height: "155px"
      }
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/users/login/index.js.map
