{"version": 3, "file": "mock.js", "sources": ["config/mock.js"], "sourcesContent": ["/**\r\n * 模拟数据配置文件\r\n * 用于在不调用接口的情况下提供页面展示数据\r\n */\r\n\r\n// 用户信息\r\nexport const userInfo = {\r\n  id: 1001,\r\n  nickname: \"用户昵称\",\r\n  avatar: \"/static/img/avatar.png\",\r\n  gender: 1,\r\n  age: \"90后\",\r\n  signature: \"这是我的个性签名\",\r\n  fans: 128,\r\n  follows: 86,\r\n  likes: 352,\r\n  is_vip: true,\r\n  background: \"/static/img/avatar.png\"\r\n};\r\n\r\n// 圈子列表数据\r\nexport const circleList = [\r\n  {\r\n    id: 1,\r\n    name: \"美食分享\",\r\n    avatar: \"/static/img/avatar.png\",\r\n    is_hot: true,\r\n    is_new: false,\r\n    dynamic_count: 25,\r\n    user_count: 256,\r\n    intro: \"分享美食制作和美食探店经验\",\r\n    user: [\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\"\r\n    ],\r\n    tags: [\"美食\", \"烹饪\", \"探店\"],\r\n    recent_topics: [\"夏日甜品特辑\", \"家常菜教程\", \"城市美食指南\"],\r\n    view_count: 1258\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"旅行日记\",\r\n    avatar: \"/static/img/avatar.png\",\r\n    is_hot: false,\r\n    is_new: true,\r\n    dynamic_count: 18,\r\n    user_count: 183,\r\n    intro: \"记录旅行见闻，分享旅途趣事\",\r\n    user: [\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\"\r\n    ],\r\n    tags: [\"旅行\", \"摄影\", \"探险\"],\r\n    recent_topics: [\"周末短途游\", \"国内小众景点\", \"旅行装备推荐\"],\r\n    view_count: 865\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"生活分享\",\r\n    avatar: \"/static/img/avatar.png\",\r\n    is_hot: true,\r\n    is_new: false,\r\n    dynamic_count: 32,\r\n    user_count: 415,\r\n    intro: \"记录日常生活点滴，分享生活妙招\",\r\n    user: [\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\"\r\n    ],\r\n    tags: [\"生活\", \"家居\", \"收纳\"],\r\n    recent_topics: [\"极简主义生活\", \"家居改造计划\", \"工作效率提升\"],\r\n    view_count: 1576\r\n  },\r\n  {\r\n    id: 4,\r\n    name: \"宠物社区\",\r\n    avatar: \"/static/img/avatar.png\",\r\n    is_hot: false,\r\n    is_new: false,\r\n    dynamic_count: 15,\r\n    user_count: 289,\r\n    intro: \"记录与宠物相处的日常，分享饲养经验\",\r\n    user: [\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\"\r\n    ],\r\n    tags: [\"宠物\", \"猫咪\", \"狗狗\"],\r\n    recent_topics: [\"宠物健康饮食\", \"有趣训练方法\", \"萌宠日常\"],\r\n    view_count: 952\r\n  },\r\n  {\r\n    id: 5,\r\n    name: \"摄影爱好\",\r\n    avatar: \"/static/img/avatar.png\",\r\n    is_hot: false,\r\n    is_new: true,\r\n    dynamic_count: 42,\r\n    user_count: 326,\r\n    intro: \"分享摄影技巧和优秀作品，探讨构图与色彩\",\r\n    user: [\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\"\r\n    ],\r\n    tags: [\"摄影\", \"器材\", \"后期\"],\r\n    recent_topics: [\"城市建筑摄影\", \"人像摄影指南\", \"后期调色技巧\"],\r\n    view_count: 1123\r\n  },\r\n  {\r\n    id: 6,\r\n    name: \"读书会\",\r\n    avatar: \"/static/img/avatar.png\",\r\n    is_hot: true,\r\n    is_new: false,\r\n    dynamic_count: 37,\r\n    user_count: 428,\r\n    intro: \"共享阅读心得，推荐好书，一起成长\",\r\n    user: [\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\"\r\n    ],\r\n    tags: [\"读书\", \"文学\", \"成长\"],\r\n    recent_topics: [\"月度好书推荐\", \"经典名著解读\", \"阅读方法分享\"],\r\n    view_count: 1345\r\n  },\r\n  {\r\n    id: 7,\r\n    name: \"电影爱好者\",\r\n    avatar: \"/static/img/avatar.png\",\r\n    is_hot: false,\r\n    is_new: false,\r\n    dynamic_count: 29,\r\n    user_count: 352,\r\n    intro: \"分享观影体验，讨论电影剧情与艺术\",\r\n    user: [\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\"\r\n    ],\r\n    tags: [\"电影\", \"影评\", \"导演\"],\r\n    recent_topics: [\"年度必看电影\", \"经典电影回顾\", \"国产电影发展\"],\r\n    view_count: 987\r\n  },\r\n  {\r\n    id: 8,\r\n    name: \"健身达人\",\r\n    avatar: \"/static/img/avatar.png\",\r\n    is_hot: true,\r\n    is_new: false,\r\n    dynamic_count: 48,\r\n    user_count: 486,\r\n    intro: \"分享健身经验，交流训练方法，共同进步\",\r\n    user: [\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\"\r\n    ],\r\n    tags: [\"健身\", \"营养\", \"减脂\"],\r\n    recent_topics: [\"居家训练计划\", \"健身饮食指南\", \"增肌训练方法\"],\r\n    view_count: 1432\r\n  },\r\n  {\r\n    id: 9,\r\n    name: \"手工创作\",\r\n    avatar: \"/static/img/avatar.png\",\r\n    is_hot: false,\r\n    is_new: true,\r\n    dynamic_count: 21,\r\n    user_count: 195,\r\n    intro: \"分享手工制作过程，交流创意和技巧\",\r\n    user: [\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\"\r\n    ],\r\n    tags: [\"手工\", \"DIY\", \"创意\"],\r\n    recent_topics: [\"布艺手作教程\", \"创意纸艺分享\", \"手工礼物制作\"],\r\n    view_count: 768\r\n  },\r\n  {\r\n    id: 10,\r\n    name: \"数码科技\",\r\n    avatar: \"/static/img/avatar.png\",\r\n    is_hot: true,\r\n    is_new: false,\r\n    dynamic_count: 53,\r\n    user_count: 512,\r\n    intro: \"关注科技动态，分享数码产品使用体验\",\r\n    user: [\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\"\r\n    ],\r\n    tags: [\"科技\", \"数码\", \"评测\"],\r\n    recent_topics: [\"新品手机评测\", \"智能家居体验\", \"数码产品选购指南\"],\r\n    view_count: 1678\r\n  },\r\n  {\r\n    id: 11,\r\n    name: \"植物爱好者\",\r\n    avatar: \"/static/img/avatar.png\",\r\n    is_hot: false,\r\n    is_new: true,\r\n    dynamic_count: 19,\r\n    user_count: 176,\r\n    intro: \"分享养护植物的经验，讨论各类植物品种\",\r\n    user: [\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\"\r\n    ],\r\n    tags: [\"植物\", \"园艺\", \"多肉\"],\r\n    recent_topics: [\"室内植物养护\", \"多肉植物品种\", \"阳台种植蔬果\"],\r\n    view_count: 642\r\n  },\r\n  {\r\n    id: 12,\r\n    name: \"咖啡爱好者\",\r\n    avatar: \"/static/img/avatar.png\",\r\n    is_hot: false,\r\n    is_new: false,\r\n    dynamic_count: 24,\r\n    user_count: 213,\r\n    intro: \"探索咖啡文化，分享冲泡技巧和咖啡馆推荐\",\r\n    user: [\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\"\r\n    ],\r\n    tags: [\"咖啡\", \"手冲\", \"品鉴\"],\r\n    recent_topics: [\"咖啡豆选购指南\", \"手冲咖啡技巧\", \"城市特色咖啡馆\"],\r\n    view_count: 895\r\n  }\r\n];\r\n\r\n// 活动数据\r\nexport const activityList = [\r\n  {\r\n    id: 1,\r\n    name: \"春季户外徒步活动\",\r\n    img: \"/static/img/activity1.png\",\r\n    status_str: \"进行中\",\r\n    activity_time: \"2023-05-20 09:00\",\r\n    adds_name: \"市民公园东门\",\r\n    user_count: 28,\r\n    avatar_list: [\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\"\r\n    ],\r\n    browse: 365,\r\n    is_join: false\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"城市摄影大赛\",\r\n    img: \"/static/img/activity2.png\",\r\n    status_str: \"报名中\",\r\n    activity_time: \"2023-06-15 14:00\",\r\n    adds_name: \"中央美术馆\",\r\n    user_count: 16,\r\n    avatar_list: [\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\",\r\n      \"/static/img/avatar.png\"\r\n    ],\r\n    browse: 245,\r\n    is_join: true\r\n  }\r\n];\r\n\r\n// 动态数据\r\nexport const dynamicList = [\r\n  {\r\n    id: 1,\r\n    user_id: 101,\r\n    user: {\r\n      id: 101,\r\n      avatar: \"/static/img/avatar.png\",\r\n      name: \"美食达人\"\r\n    },\r\n    content: \"今天做了超级美味的草莓蛋糕，口感非常好，分享一下制作过程...\",\r\n    type: 1, // 图片类型\r\n    like_count: 58,\r\n    comment_count: 12,\r\n    browse: 236,\r\n    is_like: false,\r\n    imgs: [\r\n      {url: \"/static/img/avatar.png\", wide: 800, high: 600}, \r\n      {url: \"/static/img/avatar.png\", wide: 800, high: 600}\r\n    ],\r\n    img_count: 2,\r\n    time_str: '3小时前',\r\n    create_time_d: '19',\r\n    create_time_ym: '05-19',\r\n    create_time_str: '3小时前',\r\n    province: '广州',\r\n    status_text: '',\r\n    circle_id: 1,\r\n    circle_name: \"美食分享\",\r\n    circle_avatar: \"/static/img/avatar.png\"\r\n  },\r\n  {\r\n    id: 2,\r\n    user_id: 102,\r\n    user: {\r\n      id: 102,\r\n      avatar: \"/static/img/avatar.png\",\r\n      name: \"旅行爱好者\"\r\n    },\r\n    content: \"这个周末去了附近的山区，风景真的太美了，强烈推荐给大家...\",\r\n    type: 1, // 图片类型\r\n    like_count: 36,\r\n    comment_count: 8,\r\n    browse: 185,\r\n    is_like: false,\r\n    imgs: [\r\n      {url: \"/static/img/avatar.png\", wide: 800, high: 800}\r\n    ],\r\n    img_count: 1,\r\n    time_str: '昨天',\r\n    create_time_d: '18',\r\n    create_time_ym: '05-18',\r\n    create_time_str: '昨天',\r\n    province: '杭州',\r\n    status_text: '',\r\n    adds_name: '莫干山',\r\n    lat: '30.6184',\r\n    lng: '119.8143'\r\n  },\r\n  {\r\n    id: 3,\r\n    user_id: 103,\r\n    user: {\r\n      id: 103,\r\n      avatar: \"/static/img/avatar.png\",\r\n      name: \"摄影师小明\"\r\n    },\r\n    content: \"用镜头记录城市中被忽略的角落，发现不一样的美...\",\r\n    type: 2, // 视频类型\r\n    like_count: 72,\r\n    comment_count: 18,\r\n    browse: 328,\r\n    is_like: false,\r\n    video: {\r\n      url: \"/static/img/avatar.png\",\r\n      cover: \"/static/img/avatar.png\",\r\n      wide: 1280,\r\n      high: 720\r\n    },\r\n    time_str: '3天前',\r\n    create_time_d: '16',\r\n    create_time_ym: '05-16',\r\n    create_time_str: '3天前',\r\n    province: '上海',\r\n    status_text: '',\r\n    circle_id: 5,\r\n    circle_name: \"摄影爱好\",\r\n    circle_avatar: \"/static/img/avatar.png\"\r\n  },\r\n  {\r\n    id: 4,\r\n    user_id: 104,\r\n    user: {\r\n      id: 104,\r\n      avatar: \"/static/img/avatar.png\",\r\n      name: \"健身达人\"\r\n    },\r\n    content: \"坚持训练一个月的效果，从此不再为健身而烦恼！分享几个适合新手的训练动作...\",\r\n    type: 1, // 图片类型\r\n    like_count: 95,\r\n    comment_count: 26,\r\n    browse: 412,\r\n    is_like: false,\r\n    imgs: [\r\n      {url: \"/static/img/avatar.png\", wide: 800, high: 1200},\r\n      {url: \"/static/img/avatar.png\", wide: 800, high: 1200},\r\n      {url: \"/static/img/avatar.png\", wide: 800, high: 1200}\r\n    ],\r\n    img_count: 3,\r\n    time_str: '4天前',\r\n    create_time_d: '15',\r\n    create_time_ym: '05-15',\r\n    create_time_str: '4天前',\r\n    province: '北京',\r\n    status_text: '',\r\n    circle_id: 8,\r\n    circle_name: \"健身达人\",\r\n    circle_avatar: \"/static/img/avatar.png\"\r\n  },\r\n  {\r\n    id: 5,\r\n    user_id: 105,\r\n    user: {\r\n      id: 105,\r\n      avatar: \"/static/img/avatar.png\",\r\n      name: \"音乐制作人\"\r\n    },\r\n    content: \"分享一首自己创作的歌曲《春天的约定》，欢迎大家收听和评论\",\r\n    type: 3, // 音频类型\r\n    like_count: 64,\r\n    comment_count: 17,\r\n    browse: 276,\r\n    is_like: false,\r\n    audio: {\r\n      url: \"/static/img/avatar.png\",\r\n      cover: \"/static/img/avatar.png\",\r\n      name: \"春天的约定\",\r\n      intro: \"原创·5分钟\"\r\n    },\r\n    time_str: '5天前',\r\n    create_time_d: '14',\r\n    create_time_ym: '05-14',\r\n    create_time_str: '5天前',\r\n    province: '成都',\r\n    status_text: ''\r\n  }\r\n];\r\n\r\n// 商品数据\r\nexport const goodsList = [\r\n  {\r\n    id: 1,\r\n    name: \"智能手表\",\r\n    price: \"699.00\",\r\n    original_price: \"899.00\",\r\n    sales: 256,\r\n    image: \"/static/img/avatar.png\"\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"无线耳机\",\r\n    price: \"299.00\",\r\n    original_price: \"399.00\",\r\n    sales: 428,\r\n    image: \"/static/img/avatar.png\"\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"便携充电宝\",\r\n    price: \"129.00\",\r\n    original_price: \"159.00\",\r\n    sales: 865,\r\n    image: \"/static/img/avatar.png\"\r\n  }\r\n];\r\n\r\n// 商品详情数据\r\nexport const goodsDetails = {\r\n  id: 1,\r\n  name: \"智能手表 2023新款\",\r\n  intro: \"多功能运动智能手表，支持心率监测、睡眠分析\",\r\n  imgs: [\"/static/img/avatar.png\", \"/static/img/avatar.png\", \"/static/img/avatar.png\"],\r\n  details: \"<p style='text-align:center;'><img src='/static/img/avatar.png' /></p><p>商品详情内容</p>\",\r\n  view: 2560,\r\n  buy: 389,\r\n  cart: 782,\r\n  comment: 156,\r\n  cart_count: 2,\r\n  type: 0,\r\n  product: [\r\n    {\r\n      id: 1,\r\n      specs_name: \"黑色标准版\",\r\n      price: \"699.00\",\r\n      line_price: \"899.00\",\r\n      stock: 200\r\n    },\r\n    {\r\n      id: 2,\r\n      specs_name: \"白色标准版\",\r\n      price: \"699.00\",\r\n      line_price: \"899.00\",\r\n      stock: 150\r\n    },\r\n    {\r\n      id: 3,\r\n      specs_name: \"黑色高级版\",\r\n      price: \"899.00\",\r\n      line_price: \"1099.00\",\r\n      stock: 100\r\n    }\r\n  ]\r\n};\r\n\r\n// 订单数据\r\nexport const orderList = [\r\n  {\r\n    id: 10001,\r\n    order_no: \"20230501123456\",\r\n    status: 1,\r\n    status_name: \"待付款\",\r\n    goods_price: \"699.00\",\r\n    pay_price: \"699.00\",\r\n    create_time: \"2023-05-01 12:34:56\",\r\n    goods: [\r\n      {\r\n        goods_id: 1,\r\n        goods_name: \"智能手表 2023新款\",\r\n        image: \"/static/img/avatar.png\",\r\n        specs_name: \"黑色标准版\",\r\n        price: \"699.00\",\r\n        num: 1\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: 10002,\r\n    order_no: \"20230502123456\",\r\n    status: 2,\r\n    status_name: \"待发货\",\r\n    goods_price: \"299.00\",\r\n    pay_price: \"299.00\",\r\n    create_time: \"2023-05-02 12:34:56\",\r\n    goods: [\r\n      {\r\n        goods_id: 2,\r\n        goods_name: \"无线耳机\",\r\n        image: \"/static/img/avatar.png\",\r\n        specs_name: \"白色\",\r\n        price: \"299.00\",\r\n        num: 1\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: 10003,\r\n    order_no: \"20230503123456\",\r\n    status: 3,\r\n    status_name: \"待收货\",\r\n    goods_price: \"129.00\",\r\n    pay_price: \"129.00\",\r\n    create_time: \"2023-05-03 12:34:56\",\r\n    goods: [\r\n      {\r\n        goods_id: 3,\r\n        goods_name: \"便携充电宝\",\r\n        image: \"/static/img/avatar.png\",\r\n        specs_name: \"10000mAh\",\r\n        price: \"129.00\",\r\n        num: 1\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n// 评价数据\r\nexport const evaluateList = [\r\n  {\r\n    id: 1,\r\n    user_avatar: \"/static/img/avatar.png\",\r\n    user_name: \"用户138****9012\",\r\n    content: \"商品质量很好，物流很快，包装也很精美，非常满意的一次购物体验！\",\r\n    images: [\"/static/img/avatar.png\", \"/static/img/avatar.png\"],\r\n    specs_name: \"黑色标准版\",\r\n    star: 5,\r\n    create_time: \"2023-05-05 14:23:45\"\r\n  },\r\n  {\r\n    id: 2,\r\n    user_avatar: \"/static/img/avatar.png\",\r\n    user_name: \"用户157****3456\",\r\n    content: \"手表功能很全面，电池续航也不错，总体使用感受良好。\",\r\n    images: [\"/static/img/avatar.png\"],\r\n    specs_name: \"白色标准版\",\r\n    star: 4,\r\n    create_time: \"2023-05-04 09:12:34\"\r\n  },\r\n  {\r\n    id: 3,\r\n    user_avatar: \"/static/img/avatar.png\",\r\n    user_name: \"用户186****7890\",\r\n    content: \"外观设计很好看，戴着很舒适，对得起这个价格。\",\r\n    images: [],\r\n    specs_name: \"黑色高级版\",\r\n    star: 5,\r\n    create_time: \"2023-05-03 18:45:12\"\r\n  }\r\n];\r\n\r\n// 购物车数据\r\nexport const cartList = [\r\n  {\r\n    id: 1,\r\n    goods_id: 1,\r\n    goods_name: \"智能手表 2023新款\",\r\n    image: \"/static/img/avatar.png\",\r\n    specs_name: \"黑色标准版\",\r\n    price: \"699.00\",\r\n    num: 1,\r\n    checked: true\r\n  },\r\n  {\r\n    id: 2,\r\n    goods_id: 2,\r\n    goods_name: \"无线耳机\",\r\n    image: \"/static/img/avatar.png\",\r\n    specs_name: \"白色\",\r\n    price: \"299.00\",\r\n    num: 1,\r\n    checked: true\r\n  },\r\n  {\r\n    id: 3,\r\n    goods_id: 3,\r\n    goods_name: \"便携充电宝\",\r\n    image: \"/static/img/avatar.png\",\r\n    specs_name: \"10000mAh\",\r\n    price: \"129.00\",\r\n    num: 1,\r\n    checked: false\r\n  }\r\n];\r\n\r\n// 分类数据\r\nexport const classifyList = [\r\n  {\r\n    id: 1,\r\n    name: \"电子产品\",\r\n    children: [\r\n      {\r\n        id: 101,\r\n        name: \"手机\",\r\n        image: \"/static/img/avatar.png\"\r\n      },\r\n      {\r\n        id: 102,\r\n        name: \"电脑\",\r\n        image: \"/static/img/avatar.png\"\r\n      },\r\n      {\r\n        id: 103,\r\n        name: \"智能手表\",\r\n        image: \"/static/img/avatar.png\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"服装鞋帽\",\r\n    children: [\r\n      {\r\n        id: 201,\r\n        name: \"男装\",\r\n        image: \"/static/img/avatar.png\"\r\n      },\r\n      {\r\n        id: 202,\r\n        name: \"女装\",\r\n        image: \"/static/img/avatar.png\"\r\n      },\r\n      {\r\n        id: 203,\r\n        name: \"童装\",\r\n        image: \"/static/img/avatar.png\"\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"美妆护肤\",\r\n    children: [\r\n      {\r\n        id: 301,\r\n        name: \"面部护理\",\r\n        image: \"/static/img/avatar.png\"\r\n      },\r\n      {\r\n        id: 302,\r\n        name: \"彩妆\",\r\n        image: \"/static/img/avatar.png\"\r\n      },\r\n      {\r\n        id: 303,\r\n        name: \"香水\",\r\n        image: \"/static/img/avatar.png\"\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n// 搜索数据\r\nexport const searchData = {\r\n  hot: [\"智能手表\", \"无线耳机\", \"充电宝\", \"手机壳\", \"平板电脑\"],\r\n  history: [\"运动鞋\", \"蓝牙音箱\", \"智能家居\"]\r\n};\r\n\r\n// 笔记详情数据\r\nexport const noteDetail = {\r\n  id: 2,\r\n  user_id: 1002,\r\n  user: {\r\n    id: 1002,\r\n    name: \"摄影爱好者\",\r\n    avatar: \"/static/img/avatar.png\"\r\n  },\r\n  content: \"分享几张我最近拍摄的照片，希望大家喜欢！这些都是在周末郊游时拍的，大自然的美景真的让人心旷神怡。\",\r\n  type: 1, // 图片类型\r\n  imgs: [\r\n    {url: \"/static/img/avatar.png\", wide: 750, high: 750},\r\n    {url: \"/static/img/avatar.png\", wide: 750, high: 500},\r\n    {url: \"/static/img/avatar.png\", wide: 750, high: 1000}\r\n  ],\r\n  comment_count: 32,\r\n  like_count: 156,\r\n  like_count_str: \"156\",\r\n  is_like: true,\r\n  create_time_str: \"2023-05-18\",\r\n  province: \"上海\",\r\n  adds_name: \"城市公园\",\r\n  lat: \"31.230416\",\r\n  lng: \"121.473701\",\r\n  circle_id: 5,\r\n  circle_name: \"摄影爱好\",\r\n  circle_avatar: \"/static/img/avatar.png\",\r\n  activity_id: 0,\r\n  activity_name: \"\",\r\n  activity_img: \"\",\r\n  browse: 528,\r\n  order_id: 0,\r\n  goods: []\r\n};\r\n\r\n// 笔记评论数据\r\nexport const noteComments = [\r\n  {\r\n    id: 2001,\r\n    user: {\r\n      id: 1002,\r\n      name: \"旅行爱好者\",\r\n      avatar: \"/static/img/avatar.png\"\r\n    },\r\n    content: \"照片拍得真美，请问是用什么相机拍的？\",\r\n    status: 1,\r\n    create_time_str: \"1小时前\",\r\n    province: \"上海\",\r\n    list: [\r\n      {\r\n        id: 3001,\r\n        user: {\r\n          id: 1001,\r\n          name: \"城市摄影师\",\r\n          avatar: \"/static/img/avatar.png\"\r\n        },\r\n        content: \"@旅行爱好者 谢谢喜欢，用的是索尼A7III\",\r\n        status: 1,\r\n        create_time_str: \"50分钟前\",\r\n        province: \"广州\",\r\n        reply_name: \"旅行爱好者\",\r\n        is_reply_to_reply: false\r\n      },\r\n      {\r\n        id: 3002,\r\n        user: {\r\n          id: 1003,\r\n          name: \"摄影达人\",\r\n          avatar: \"/static/img/avatar.png\"\r\n        },\r\n        content: \"@城市摄影师 这款相机确实不错，我也在用\",\r\n        status: 1,\r\n        create_time_str: \"30分钟前\",\r\n        province: \"北京\",\r\n        reply_name: \"城市摄影师\",\r\n        is_reply_to_reply: true\r\n      }\r\n    ],\r\n    list_count: 2\r\n  },\r\n  {\r\n    id: 2002,\r\n    user: {\r\n      id: 1004,\r\n      name: \"园艺爱好者\",\r\n      avatar: \"/static/img/avatar.png\"\r\n    },\r\n    content: \"公园里的花真漂亮，请问是什么品种的花？\",\r\n    status: 1,\r\n    create_time_str: \"1小时前\",\r\n    province: \"杭州\",\r\n    list: [],\r\n    list_count: 0\r\n  },\r\n  {\r\n    id: 2003,\r\n    user: {\r\n      id: 1005,\r\n      name: \"城市规划师\",\r\n      avatar: \"/static/img/avatar.png\"\r\n    },\r\n    content: \"这个公园规划得非常好，环境优美，是我们城市的一张名片。\",\r\n    status: 1,\r\n    create_time_str: \"2小时前\",\r\n    province: \"广州\",\r\n    list: [],\r\n    list_count: 0\r\n  }\r\n];\r\n\r\n// 通用消息数据\r\nexport const messageList = {\r\n  // 所有消息\r\n  all: [\r\n    {\r\n      id: 1001,\r\n      launch_id: 2001,\r\n      type: 1, // 笔记类型\r\n      read: 0,\r\n      title: \"评论了你的笔记\",\r\n      content: \"你的摄影技巧太棒了，请问是用什么相机拍摄的？\",\r\n      content_url: \"/pages/note/details?id=1001\",\r\n      avatar_url: \"/pages/user/details?id=2001\",\r\n      create_time_str: \"10分钟前\",\r\n      user: {\r\n        avatar: \"/static/img/avatar.png\",\r\n        name: \"小林摄影\"\r\n      }\r\n    },\r\n    {\r\n      id: 1002,\r\n      launch_id: 2002,\r\n      type: 2, // 圈子类型\r\n      read: 0,\r\n      title: \"加入了你创建的圈子\",\r\n      content: \"很喜欢\\\"旅行日记\\\"这个圈子，期待与大家分享旅行经历！\",\r\n      content_url: \"/pages/note/circle?id=3001\",\r\n      avatar_url: \"/pages/user/details?id=2002\",\r\n      create_time_str: \"30分钟前\",\r\n      user: {\r\n        avatar: \"/static/img/avatar.png\",\r\n        name: \"旅行达人\"\r\n      }\r\n    },\r\n    {\r\n      id: 1003,\r\n      type: 3, // 购物类型\r\n      read: 1,\r\n      title: \"您的订单已发货\",\r\n      content: \"订单号: 2023112509876，预计3-5天送达，请保持电话畅通。\",\r\n      content_url: \"/pages/order/details?id=4001\",\r\n      create_time_str: \"2小时前\"\r\n    }\r\n  ],\r\n  // 笔记消息\r\n  note: [\r\n    {\r\n      id: 1001,\r\n      launch_id: 2001,\r\n      type: 1,\r\n      read: 0,\r\n      title: \"评论了你的笔记\",\r\n      content: \"你的摄影技巧太棒了，请问是用什么相机拍摄的？\",\r\n      content_url: \"/pages/note/details?id=1001\",\r\n      avatar_url: \"/pages/user/details?id=2001\",\r\n      create_time_str: \"10分钟前\",\r\n      user: {\r\n        avatar: \"/static/img/avatar.png\",\r\n        name: \"小林摄影\"\r\n      }\r\n    },\r\n    {\r\n      id: 1004,\r\n      launch_id: 2003,\r\n      type: 1,\r\n      read: 0,\r\n      title: \"点赞了你的笔记\",\r\n      content: \"你分享的美食制作方法我试了一下，效果很棒！\",\r\n      content_url: \"/pages/note/details?id=1002\",\r\n      avatar_url: \"/pages/user/details?id=2003\",\r\n      create_time_str: \"3小时前\",\r\n      user: {\r\n        avatar: \"/static/img/avatar.png\",\r\n        name: \"美食家\"\r\n      }\r\n    }\r\n  ],\r\n  // 圈子消息\r\n  circle: [\r\n    {\r\n      id: 1002,\r\n      launch_id: 2002,\r\n      type: 2,\r\n      read: 0,\r\n      title: \"加入了你创建的圈子\",\r\n      content: \"很喜欢\\\"旅行日记\\\"这个圈子，期待与大家分享旅行经历！\",\r\n      content_url: \"/pages/note/circle?id=3001\",\r\n      avatar_url: \"/pages/user/details?id=2002\",\r\n      create_time_str: \"30分钟前\",\r\n      user: {\r\n        avatar: \"/static/img/avatar.png\",\r\n        name: \"旅行达人\"\r\n      }\r\n    },\r\n    {\r\n      id: 1006,\r\n      launch_id: 2004,\r\n      type: 2,\r\n      read: 0,\r\n      title: \"在圈子中@了你\",\r\n      content: \"@你 这款相机确实不错，我也入手了一台，拍摄效果很满意。\",\r\n      content_url: \"/pages/note/circle?id=3002&post=5001\",\r\n      avatar_url: \"/pages/user/details?id=2004\",\r\n      create_time_str: \"昨天\",\r\n      user: {\r\n        avatar: \"/static/img/avatar.png\",\r\n        name: \"摄影爱好者\"\r\n      }\r\n    }\r\n  ],\r\n  // 购物消息\r\n  shop: [\r\n    {\r\n      id: 1003,\r\n      type: 3,\r\n      read: 1,\r\n      title: \"您的订单已发货\",\r\n      content: \"订单号: 2023112509876，预计3-5天送达，请保持电话畅通。\",\r\n      content_url: \"/pages/order/details?id=4001\",\r\n      create_time_str: \"2小时前\"\r\n    },\r\n    {\r\n      id: 1005,\r\n      type: 3,\r\n      read: 1,\r\n      title: \"优惠券到账提醒\",\r\n      content: \"恭喜您获得满100减20元优惠券一张，有效期7天，请尽快使用。\",\r\n      content_url: \"/pages/center/card\",\r\n      create_time_str: \"昨天\"\r\n    }\r\n  ]\r\n};\r\n\r\n// 用户圈子数据\r\nexport const userCircleList = {\r\n  // 用户创建的圈子\r\n  created: [\r\n    {\r\n      id: 3,\r\n      name: \"生活分享\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_hot: true,\r\n      is_new: false,\r\n      dynamic_count: 32,\r\n      user_count: 415,\r\n      intro: \"记录日常生活点滴，分享生活妙招\",\r\n      user: [\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\"\r\n      ],\r\n      create_time: \"2023-01-15\"\r\n    },\r\n    {\r\n      id: 8,\r\n      name: \"健身达人\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_hot: true,\r\n      is_new: false,\r\n      dynamic_count: 48,\r\n      user_count: 486,\r\n      intro: \"分享健身经验，交流训练方法，共同进步\",\r\n      user: [\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\"\r\n      ],\r\n      create_time: \"2023-02-20\"\r\n    }\r\n  ],\r\n  \r\n  // 用户加入的圈子\r\n  joined: [\r\n    {\r\n      id: 1,\r\n      name: \"美食分享\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_hot: true,\r\n      is_new: false,\r\n      dynamic_count: 25,\r\n      user_count: 256,\r\n      intro: \"分享美食制作和美食探店经验\",\r\n      user: [\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\"\r\n      ],\r\n      join_time: \"2023-03-05\"\r\n    },\r\n    {\r\n      id: 5,\r\n      name: \"摄影爱好\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_hot: false,\r\n      is_new: true,\r\n      dynamic_count: 42,\r\n      user_count: 326,\r\n      intro: \"分享摄影技巧和优秀作品，探讨构图与色彩\",\r\n      user: [\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\"\r\n      ],\r\n      join_time: \"2023-04-12\"\r\n    },\r\n    {\r\n      id: 10,\r\n      name: \"数码科技\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_hot: true,\r\n      is_new: false,\r\n      dynamic_count: 53,\r\n      user_count: 512,\r\n      intro: \"关注科技动态，分享数码产品使用体验\",\r\n      user: [\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\"\r\n      ],\r\n      join_time: \"2023-04-18\"\r\n    }\r\n  ],\r\n  \r\n  // 推荐的圈子\r\n  recommend: [\r\n    {\r\n      id: 2,\r\n      name: \"旅行日记\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_hot: false,\r\n      is_new: true,\r\n      dynamic_count: 18,\r\n      user_count: 183,\r\n      intro: \"记录旅行见闻，分享旅途趣事\",\r\n      user: [\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\"\r\n      ]\r\n    },\r\n    {\r\n      id: 4,\r\n      name: \"宠物社区\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_hot: false,\r\n      is_new: false,\r\n      dynamic_count: 15,\r\n      user_count: 289,\r\n      intro: \"记录与宠物相处的日常，分享饲养经验\",\r\n      user: [\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\"\r\n      ]\r\n    },\r\n    {\r\n      id: 6,\r\n      name: \"读书会\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_hot: true,\r\n      is_new: false,\r\n      dynamic_count: 37,\r\n      user_count: 428,\r\n      intro: \"共享阅读心得，推荐好书，一起成长\",\r\n      user: [\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\"\r\n      ]\r\n    },\r\n    {\r\n      id: 9,\r\n      name: \"手工创作\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_hot: false,\r\n      is_new: true,\r\n      dynamic_count: 21,\r\n      user_count: 195,\r\n      intro: \"分享手工制作过程，交流创意和技巧\",\r\n      user: [\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\",\r\n        \"/static/img/avatar.png\"\r\n      ]\r\n    }\r\n  ]\r\n};\r\n\r\n// 圈子详情数据\r\nexport const circleDetail = {\r\n  id: 1,\r\n  name: \"美食分享\",\r\n  avatar: \"/static/img/avatar.png\",\r\n  background: \"/static/img/avatar.png\",\r\n  is_hot: true,\r\n  is_new: false,\r\n  dynamic_count: 25,\r\n  user_count: 256,\r\n  intro: \"分享美食制作和美食探店经验，交流烹饪技巧和食材选购知识，一起探索美食的无限可能。\",\r\n  tags: [\"美食\", \"烹饪\", \"探店\"],\r\n  create_time: \"2023-01-15\",\r\n  creator: {\r\n    id: 101,\r\n    name: \"美食达人\",\r\n    avatar: \"/static/img/avatar.png\"\r\n  },\r\n  is_join: true,\r\n  admin_list: [\r\n    {\r\n      id: 101,\r\n      name: \"美食达人\",\r\n      avatar: \"/static/img/avatar.png\"\r\n    },\r\n    {\r\n      id: 102,\r\n      name: \"烹饪专家\",\r\n      avatar: \"/static/img/avatar.png\"\r\n    }\r\n  ],\r\n  notice: \"欢迎加入美食分享圈子！请遵守社区规则，禁止发布广告和无关内容，共同维护良好的交流环境。\",\r\n  posts: [\r\n    {\r\n      id: 1001,\r\n      user: {\r\n        id: 101,\r\n        name: \"美食达人\",\r\n        avatar: \"/static/img/avatar.png\"\r\n      },\r\n      content: \"分享一道简单又美味的家常菜：蒜蓉西兰花。步骤简单，营养丰富，非常适合工作日的晚餐！\",\r\n      images: [\"/static/img/avatar.png\", \"/static/img/avatar.png\"],\r\n      create_time: \"2023-05-10 14:23\",\r\n      like_count: 28,\r\n      comment_count: 12\r\n    },\r\n    {\r\n      id: 1002,\r\n      user: {\r\n        id: 103,\r\n        name: \"甜点控\",\r\n        avatar: \"/static/img/avatar.png\"\r\n      },\r\n      content: \"周末做了一款草莓慕斯蛋糕，成品非常满意！分享一下制作过程和要点...\",\r\n      images: [\"/static/img/avatar.png\"],\r\n      create_time: \"2023-05-09 10:45\",\r\n      like_count: 36,\r\n      comment_count: 18\r\n    }\r\n  ]\r\n};\r\n\r\n// 评论列表数据\r\nexport const commentList = [\r\n  {\r\n    id: 101,\r\n    user: {\r\n      id: 2001,\r\n      name: \"评论用户1\",\r\n      avatar: \"/static/img/avatar.png\"\r\n    },\r\n    content: \"这些照片拍得真好，光影效果很棒！\",\r\n    create_time_str: \"2023-05-19\",\r\n    province: \"北京\",\r\n    status: 1,\r\n    list_count: 2,\r\n    list: [\r\n      {\r\n        id: 1011,\r\n        user: {\r\n          id: 1002,\r\n          name: \"摄影爱好者\",\r\n          avatar: \"/static/img/avatar.png\"\r\n        },\r\n        content: \"谢谢你的赞赏，我会继续努力的！\",\r\n        create_time_str: \"2023-05-19\",\r\n        province: \"上海\",\r\n        status: 1,\r\n        reply_user: {\r\n          id: 2001,\r\n          name: \"评论用户1\"\r\n        }\r\n      },\r\n      {\r\n        id: 1012,\r\n        user: {\r\n          id: 2003,\r\n          name: \"路人甲\",\r\n          avatar: \"/static/img/avatar.png\"\r\n        },\r\n        content: \"我也觉得拍得很好，请问用的什么相机？\",\r\n        create_time_str: \"2023-05-19\",\r\n        province: \"广州\",\r\n        status: 1,\r\n        reply_user: {\r\n          id: 2001,\r\n          name: \"评论用户1\"\r\n        }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    id: 102,\r\n    user: {\r\n      id: 2002,\r\n      name: \"评论用户2\",\r\n      avatar: \"/static/img/avatar.png\"\r\n    },\r\n    content: \"风景真美，下次有机会我也要去看看！\",\r\n    create_time_str: \"2023-05-18\",\r\n    province: \"深圳\",\r\n    status: 1,\r\n    list_count: 1,\r\n    list: [\r\n      {\r\n        id: 1021,\r\n        user: {\r\n          id: 1002,\r\n          name: \"摄影爱好者\",\r\n          avatar: \"/static/img/avatar.png\"\r\n        },\r\n        content: \"欢迎你来玩，这个地方真的很值得一去！\",\r\n        create_time_str: \"2023-05-18\",\r\n        province: \"上海\",\r\n        status: 1,\r\n        reply_user: {\r\n          id: 2002,\r\n          name: \"评论用户2\"\r\n        }\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n// 直播信息数据\r\nexport const liveInfo = [\r\n  {\r\n    id: 1,\r\n    title: \"正在直播：潮流穿搭分享会\",\r\n    path: \"note/live?id=1\",\r\n    content: \"潮流穿搭分享会\",\r\n    url: \"note/live?id=1\",\r\n    status: true\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"正在直播：美妆教程大公开\",\r\n    path: \"note/live?id=2\",\r\n    content: \"美妆教程大公开\",\r\n    url: \"note/live?id=2\",\r\n    status: true\r\n  },\r\n  {\r\n    id: 3,\r\n    title: \"正在直播：厨艺大师烹饪秀\",\r\n    path: \"note/live?id=3\",\r\n    content: \"厨艺大师烹饪秀\",\r\n    url: \"note/live?id=3\",\r\n    status: true\r\n  }\r\n];\r\n\r\n// 用户笔记列表\r\nexport const userNotes = [\r\n  {\r\n    id: 101,\r\n    type: 1, // 图片类型\r\n    content: '分享一张最近拍摄的照片，城市的夜景真美！',\r\n    imgs: [\r\n      {url: '/static/img/avatar.png', wide: 800, high: 600}\r\n    ],\r\n    img_count: 1,\r\n    view_count: 158,\r\n    browse: 158,\r\n    like_count: 32,\r\n    comment_count: 8,\r\n    time_str: '2小时前',\r\n    create_time_d: '19',\r\n    create_time_ym: '05-19',\r\n    create_time_str: '2小时前',\r\n    province: '广州',\r\n    status_text: '',\r\n    is_like: false,\r\n    user_id: 1001,\r\n    adds_name: '天河公园',\r\n    lat: '23.137903',\r\n    lng: '113.347306',\r\n    circle_id: 5,\r\n    circle_name: '摄影爱好',\r\n    circle_avatar: '/static/img/avatar.png'\r\n  },\r\n  {\r\n    id: 102,\r\n    type: 0, // 纯文本类型\r\n    content: '今天天气真好，心情也跟着好起来了！阳光明媚，微风不燥，是出门散步的好日子。',\r\n    browse: 89,\r\n    view_count: 89,\r\n    like_count: 15,\r\n    comment_count: 3,\r\n    time_str: '昨天',\r\n    create_time_d: '17',\r\n    create_time_ym: '05-17',\r\n    create_time_str: '昨天',\r\n    province: '北京',\r\n    status_text: '',\r\n    is_like: true,\r\n    user_id: 1001\r\n  },\r\n  {\r\n    id: 103,\r\n    type: 1, // 多图类型\r\n    content: '和朋友一起去了新开的咖啡店，环境很不错，推荐给大家~',\r\n    imgs: [\r\n      {url: '/static/img/avatar.png', wide: 800, high: 800},\r\n      {url: '/static/img/avatar.png', wide: 800, high: 800},\r\n      {url: '/static/img/avatar.png', wide: 800, high: 800}\r\n    ],\r\n    img_count: 3,\r\n    browse: 210,\r\n    view_count: 210,\r\n    like_count: 45,\r\n    comment_count: 12,\r\n    time_str: '3天前',\r\n    create_time_d: '15',\r\n    create_time_ym: '05-15',\r\n    create_time_str: '3天前',\r\n    province: '上海',\r\n    status_text: '',\r\n    is_like: false,\r\n    user_id: 1001,\r\n    adds_name: '星巴克咖啡(徐家汇店)',\r\n    lat: '31.193894',\r\n    lng: '121.436806'\r\n  },\r\n  {\r\n    id: 104,\r\n    type: 2, // 视频类型\r\n    content: '分享一段我最近学习的舞蹈视频，喜欢的朋友可以一起交流~',\r\n    video: {\r\n      url: '/static/img/avatar.png',\r\n      cover: '/static/img/avatar.png',\r\n      wide: 720,\r\n      high: 1280\r\n    },\r\n    browse: 315,\r\n    view_count: 315,\r\n    like_count: 67,\r\n    comment_count: 18,\r\n    time_str: '5天前',\r\n    create_time_d: '13',\r\n    create_time_ym: '05-13',\r\n    create_time_str: '5天前',\r\n    province: '深圳',\r\n    status_text: '',\r\n    is_like: false,\r\n    user_id: 1001,\r\n    circle_id: 9,\r\n    circle_name: '舞蹈爱好者',\r\n    circle_avatar: '/static/img/avatar.png'\r\n  },\r\n  {\r\n    id: 105,\r\n    type: 3, // 音频类型\r\n    content: '分享一段我最近录制的钢琴曲，《梦中的婚礼》，希望大家喜欢',\r\n    audio: {\r\n      url: '/static/img/avatar.png',\r\n      cover: '/static/img/avatar.png',\r\n      name: '梦中的婚礼',\r\n      intro: '钢琴曲·3分钟'\r\n    },\r\n    browse: 168,\r\n    view_count: 168,\r\n    like_count: 39,\r\n    comment_count: 8,\r\n    time_str: '1周前',\r\n    create_time_d: '11',\r\n    create_time_ym: '05-11',\r\n    create_time_str: '1周前',\r\n    province: '杭州',\r\n    status_text: '',\r\n    is_like: false,\r\n    user_id: 1001\r\n  },\r\n  {\r\n    id: 106,\r\n    type: 1, // 图片类型(带商品)\r\n    content: '今天收到了上周买的新衣服，质量很好，款式也很好看，分享给大家~',\r\n    imgs: [\r\n      {url: '/static/img/avatar.png', wide: 800, high: 1200},\r\n      {url: '/static/img/avatar.png', wide: 800, high: 1200}\r\n    ],\r\n    img_count: 2,\r\n    browse: 245,\r\n    view_count: 245,\r\n    like_count: 52,\r\n    comment_count: 14,\r\n    time_str: '1周前',\r\n    create_time_d: '10',\r\n    create_time_ym: '05-10',\r\n    create_time_str: '1周前',\r\n    province: '广州',\r\n    status_text: '',\r\n    is_like: false,\r\n    user_id: 1001,\r\n    order_id: 10001,\r\n    goods: [\r\n      {\r\n        goods_id: 1001,\r\n        product_img: '/static/img/avatar.png',\r\n        goods_name: '2023春夏新款连衣裙'\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n// 用户赞过的内容\r\nexport const userLikes = [\r\n  {\r\n    id: 201,\r\n    type: 1, // 图片类型\r\n    user: {\r\n      id: 301,\r\n      avatar: '/static/img/avatar.png',\r\n      name: '摄影爱好者'\r\n    },\r\n    user_id: 301,\r\n    content: '分享一组街拍照片，希望大家喜欢！',\r\n    imgs: [\r\n      {url: '/static/img/avatar.png', wide: 800, high: 600},\r\n      {url: '/static/img/avatar.png', wide: 800, high: 600},\r\n      {url: '/static/img/avatar.png', wide: 800, high: 600}\r\n    ],\r\n    img_count: 3,\r\n    browse: 320,\r\n    view_count: 320,\r\n    like_count: 86,\r\n    comment_count: 24,\r\n    time_str: '1天前',\r\n    create_time_d: '18',\r\n    create_time_ym: '05-18',\r\n    create_time_str: '1天前',\r\n    province: '上海',\r\n    status_text: '',\r\n    is_like: true,\r\n    adds_name: '人民广场',\r\n    lat: '31.23136',\r\n    lng: '121.47528',\r\n    circle_id: 2,\r\n    circle_name: '街拍达人',\r\n    circle_avatar: '/static/img/avatar.png'\r\n  },\r\n  {\r\n    id: 202,\r\n    type: 2, // 视频类型\r\n    user: {\r\n      id: 302,\r\n      avatar: '/static/img/avatar.png',\r\n      name: '美食博主'\r\n    },\r\n    user_id: 302,\r\n    content: '自制美食教程，简单又好吃！学会这道糖醋排骨，在家也能做出饭店的味道！',\r\n    video: {\r\n      url: '/static/img/avatar.png',\r\n      cover: '/static/img/avatar.png',\r\n      wide: 1280,\r\n      high: 720\r\n    },\r\n    browse: 560,\r\n    view_count: 560,\r\n    like_count: 120,\r\n    comment_count: 35,\r\n    time_str: '3天前',\r\n    create_time_d: '16',\r\n    create_time_ym: '05-16',\r\n    create_time_str: '3天前',\r\n    province: '北京',\r\n    status_text: '',\r\n    is_like: true,\r\n    circle_id: 1,\r\n    circle_name: '美食分享',\r\n    circle_avatar: '/static/img/avatar.png'\r\n  },\r\n  {\r\n    id: 203,\r\n    type: 3, // 音频类型\r\n    user: {\r\n      id: 303,\r\n      avatar: '/static/img/avatar.png',\r\n      name: '音乐爱好者'\r\n    },\r\n    user_id: 303,\r\n    content: '翻唱一首《夏天的风》，希望你们喜欢这个版本~',\r\n    audio: {\r\n      url: '/static/img/avatar.png',\r\n      cover: '/static/img/avatar.png',\r\n      name: '夏天的风(翻唱版)',\r\n      intro: '翻唱·4分钟'\r\n    },\r\n    browse: 428,\r\n    view_count: 428,\r\n    like_count: 98,\r\n    comment_count: 27,\r\n    time_str: '4天前',\r\n    create_time_d: '15',\r\n    create_time_ym: '05-15',\r\n    create_time_str: '4天前',\r\n    province: '成都',\r\n    status_text: '',\r\n    is_like: true\r\n  },\r\n  {\r\n    id: 204,\r\n    type: 0, // 纯文本类型\r\n    user: {\r\n      id: 304,\r\n      avatar: '/static/img/avatar.png',\r\n      name: '读书达人'\r\n    },\r\n    user_id: 304,\r\n    content: '今天读完了《活着》这本书，感触很深。余华的文字朴实无华却直击人心。推荐给大家，这是一本能让人思考生命意义的好书。',\r\n    browse: 312,\r\n    view_count: 312,\r\n    like_count: 76,\r\n    comment_count: 19,\r\n    time_str: '5天前',\r\n    create_time_d: '14',\r\n    create_time_ym: '05-14',\r\n    create_time_str: '5天前',\r\n    province: '杭州',\r\n    status_text: '',\r\n    is_like: true,\r\n    circle_id: 6,\r\n    circle_name: '读书会',\r\n    circle_avatar: '/static/img/avatar.png'\r\n  }\r\n];\r\n\r\n// 用户草稿箱\r\nexport const userDrafts = [\r\n  {\r\n    id: 301,\r\n    type: 0, // 纯文本类型\r\n    content: '这是一篇尚未发布的草稿，记录了一些随手的想法...',\r\n    browse: 0,\r\n    view_count: 0,\r\n    like_count: 0,\r\n    comment_count: 0,\r\n    time_str: '刚刚',\r\n    create_time_d: '19',\r\n    create_time_ym: '05-19',\r\n    create_time_str: '刚刚',\r\n    province: '广州',\r\n    status_text: '草稿',\r\n    is_like: false,\r\n    user_id: 1001\r\n  },\r\n  {\r\n    id: 302,\r\n    type: 1, // 图片类型\r\n    content: '上周去爬山拍的照片，准备整理一下发布...',\r\n    imgs: [\r\n      {url: '/static/img/avatar.png', wide: 800, high: 600},\r\n      {url: '/static/img/avatar.png', wide: 800, high: 600}\r\n    ],\r\n    img_count: 2,\r\n    browse: 0,\r\n    view_count: 0,\r\n    like_count: 0,\r\n    comment_count: 0,\r\n    time_str: '昨天',\r\n    create_time_d: '18',\r\n    create_time_ym: '05-18',\r\n    create_time_str: '昨天',\r\n    province: '广州',\r\n    status_text: '草稿',\r\n    is_like: false,\r\n    user_id: 1001,\r\n    adds_name: '白云山',\r\n    lat: '23.18576',\r\n    lng: '113.29749'\r\n  }\r\n];\r\n\r\n// 话题列表数据\r\nexport const topicList = [\r\n  {\r\n    id: 1,\r\n    title: \"夏日穿搭大赏\",\r\n    cover: \"/static/img/avatar.png\",\r\n    intro: \"分享你的夏日穿搭灵感，轻松清爽过夏天\",\r\n    user_count: 562,\r\n    post_count: 287,\r\n    view_count: 12893,\r\n    is_hot: true,\r\n    is_new: false,\r\n    tags: [\"穿搭\", \"夏季\", \"时尚\"]\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"居家美食recipes\",\r\n    cover: \"/static/img/avatar.png\",\r\n    intro: \"在家也能做出米其林级别的美食\",\r\n    user_count: 782,\r\n    post_count: 456,\r\n    view_count: 18652,\r\n    is_hot: true,\r\n    is_new: false,\r\n    tags: [\"美食\", \"烹饪\", \"家常菜\"]\r\n  },\r\n  {\r\n    id: 3,\r\n    title: \"城市摄影指南\",\r\n    cover: \"/static/img/avatar.png\",\r\n    intro: \"教你如何在城市中发现美丽的画面\",\r\n    user_count: 345,\r\n    post_count: 198,\r\n    view_count: 8765,\r\n    is_hot: false,\r\n    is_new: true,\r\n    tags: [\"摄影\", \"城市\", \"构图\"]\r\n  },\r\n  {\r\n    id: 4,\r\n    title: \"阅读与生活\",\r\n    cover: \"/static/img/avatar.png\",\r\n    intro: \"分享阅读心得，讨论如何将书中智慧融入生活\",\r\n    user_count: 429,\r\n    post_count: 233,\r\n    view_count: 9876,\r\n    is_hot: false,\r\n    is_new: false,\r\n    tags: [\"阅读\", \"书籍\", \"心得\"]\r\n  },\r\n  {\r\n    id: 5,\r\n    title: \"旅行探险家\",\r\n    cover: \"/static/img/avatar.png\",\r\n    intro: \"分享旅行故事，探索世界各地的奇妙风景\",\r\n    user_count: 678,\r\n    post_count: 345,\r\n    view_count: 15432,\r\n    is_hot: true,\r\n    is_new: false,\r\n    tags: [\"旅行\", \"探险\", \"风景\"]\r\n  }\r\n];\r\n\r\n// 标签列表数据\r\nexport const tagList = [\r\n  {\r\n    id: 1,\r\n    name: \"美食\",\r\n    count: 12897,\r\n    is_hot: true,\r\n    cover: \"/static/img/avatar.png\"\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"摄影\",\r\n    count: 9865,\r\n    is_hot: true,\r\n    cover: \"/static/img/avatar.png\"\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"旅行\",\r\n    count: 8756,\r\n    is_hot: true,\r\n    cover: \"/static/img/avatar.png\"\r\n  },\r\n  {\r\n    id: 4,\r\n    name: \"穿搭\",\r\n    count: 7654,\r\n    is_hot: false,\r\n    cover: \"/static/img/avatar.png\"\r\n  },\r\n  {\r\n    id: 5,\r\n    name: \"健身\",\r\n    count: 6543,\r\n    is_hot: false,\r\n    cover: \"/static/img/avatar.png\"\r\n  },\r\n  {\r\n    id: 6,\r\n    name: \"读书\",\r\n    count: 5432,\r\n    is_hot: false,\r\n    cover: \"/static/img/avatar.png\"\r\n  },\r\n  {\r\n    id: 7,\r\n    name: \"宠物\",\r\n    count: 4321,\r\n    is_hot: true,\r\n    cover: \"/static/img/avatar.png\"\r\n  },\r\n  {\r\n    id: 8,\r\n    name: \"手工\",\r\n    count: 3210,\r\n    is_hot: false,\r\n    cover: \"/static/img/avatar.png\"\r\n  }\r\n];\r\n\r\n// 优惠券数据\r\nexport const couponList = [\r\n  {\r\n    id: 1,\r\n    name: \"新人专享券\",\r\n    type: 1, // 满减券\r\n    value: 10,\r\n    min_price: 99,\r\n    start_time: \"2023-05-01\",\r\n    end_time: \"2023-05-31\",\r\n    status: 1, // 未使用\r\n    is_expired: false,\r\n    category: \"全场通用\"\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"618活动券\",\r\n    type: 1, // 满减券\r\n    value: 50,\r\n    min_price: 299,\r\n    start_time: \"2023-06-01\",\r\n    end_time: \"2023-06-18\",\r\n    status: 1, // 未使用\r\n    is_expired: false,\r\n    category: \"全场通用\"\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"生日专享券\",\r\n    type: 2, // 折扣券\r\n    value: 8.5, // 8.5折\r\n    min_price: 0,\r\n    start_time: \"2023-05-15\",\r\n    end_time: \"2023-06-15\",\r\n    status: 1, // 未使用\r\n    is_expired: false,\r\n    category: \"个护美妆\"\r\n  },\r\n  {\r\n    id: 4,\r\n    name: \"数码专享券\",\r\n    type: 1, // 满减券\r\n    value: 100,\r\n    min_price: 1000,\r\n    start_time: \"2023-05-10\",\r\n    end_time: \"2023-05-20\",\r\n    status: 2, // 已使用\r\n    is_expired: true,\r\n    category: \"数码电器\"\r\n  },\r\n  {\r\n    id: 5,\r\n    name: \"服饰专享券\",\r\n    type: 1, // 满减券\r\n    value: 30,\r\n    min_price: 199,\r\n    start_time: \"2023-05-01\",\r\n    end_time: \"2023-05-10\",\r\n    status: 3, // 已过期\r\n    is_expired: true,\r\n    category: \"服饰鞋包\"\r\n  }\r\n];\r\n\r\n// 用户收藏数据\r\nexport const userCollections = [\r\n  {\r\n    id: 1,\r\n    type: 1, // 笔记\r\n    note: {\r\n      id: 101,\r\n      user: {\r\n        id: 301,\r\n        name: \"美食达人\",\r\n        avatar: \"/static/img/avatar.png\"\r\n      },\r\n      content: \"分享一道简单好吃的家常菜：糖醋排骨，酸甜可口，非常下饭！\",\r\n      images: [\"/static/img/avatar.png\"],\r\n      like_count: 68,\r\n      comment_count: 15,\r\n      time_str: \"3天前\"\r\n    },\r\n    collect_time: \"2023-05-15\"\r\n  },\r\n  {\r\n    id: 2,\r\n    type: 2, // 商品\r\n    goods: {\r\n      id: 201,\r\n      name: \"无线蓝牙耳机\",\r\n      image: \"/static/img/avatar.png\",\r\n      price: \"199.00\",\r\n      original_price: \"299.00\",\r\n      sales: 1256\r\n    },\r\n    collect_time: \"2023-05-10\"\r\n  },\r\n  {\r\n    id: 3,\r\n    type: 3, // 话题\r\n    topic: {\r\n      id: 301,\r\n      title: \"夏日穿搭大赏\",\r\n      cover: \"/static/img/avatar.png\",\r\n      user_count: 562,\r\n      post_count: 287\r\n    },\r\n    collect_time: \"2023-05-05\"\r\n  },\r\n  {\r\n    id: 4,\r\n    type: 4, // 圈子\r\n    circle: {\r\n      id: 401,\r\n      name: \"摄影爱好\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      user_count: 326,\r\n      dynamic_count: 42\r\n    },\r\n    collect_time: \"2023-04-28\"\r\n  }\r\n];\r\n\r\n// 视频笔记数据\r\nexport const videoNotes = [\r\n  {\r\n    id: 1,\r\n    user: {\r\n      id: 101,\r\n      name: \"美食博主\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_follow: false\r\n    },\r\n    content: \"15分钟快手料理教程，上班族必备！\",\r\n    video: {\r\n      url: \"/static/img/avatar.png\",\r\n      cover: \"/static/img/avatar.png\",\r\n      duration: \"02:45\",\r\n      width: 720,\r\n      height: 1280\r\n    },\r\n    view_count: 2456,\r\n    like_count: 356,\r\n    comment_count: 89,\r\n    share_count: 56,\r\n    time_str: \"2小时前\",\r\n    tags: [\"美食\", \"快手菜\", \"家常菜\"]\r\n  },\r\n  {\r\n    id: 2,\r\n    user: {\r\n      id: 102,\r\n      name: \"健身教练\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_follow: true\r\n    },\r\n    content: \"10分钟居家徒手训练，每天坚持，30天见效！\",\r\n    video: {\r\n      url: \"/static/img/avatar.png\",\r\n      cover: \"/static/img/avatar.png\",\r\n      duration: \"10:15\",\r\n      width: 720,\r\n      height: 1280\r\n    },\r\n    view_count: 3578,\r\n    like_count: 482,\r\n    comment_count: 125,\r\n    share_count: 98,\r\n    time_str: \"昨天\",\r\n    tags: [\"健身\", \"居家训练\", \"徒手训练\"]\r\n  },\r\n  {\r\n    id: 3,\r\n    user: {\r\n      id: 103,\r\n      name: \"旅行vlogger\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_follow: false\r\n    },\r\n    content: \"探秘小众旅行地，这里的风景美得让人窒息！\",\r\n    video: {\r\n      url: \"/static/img/avatar.png\",\r\n      cover: \"/static/img/avatar.png\",\r\n      duration: \"05:30\",\r\n      width: 1280,\r\n      height: 720\r\n    },\r\n    view_count: 1892,\r\n    like_count: 267,\r\n    comment_count: 56,\r\n    share_count: 34,\r\n    time_str: \"3天前\",\r\n    tags: [\"旅行\", \"风景\", \"vlog\"]\r\n  }\r\n];\r\n\r\n// 音频笔记数据\r\nexport const audioNotes = [\r\n  {\r\n    id: 1,\r\n    user: {\r\n      id: 201,\r\n      name: \"读书达人\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_follow: true\r\n    },\r\n    content: \"《活着》读书笔记分享，余华的文字总是那么直击人心\",\r\n    audio: {\r\n      url: \"/static/img/avatar.png\",\r\n      cover: \"/static/img/avatar.png\",\r\n      duration: \"15:30\",\r\n      name: \"《活着》读书笔记\",\r\n      intro: \"文学作品·15分钟\"\r\n    },\r\n    view_count: 1456,\r\n    like_count: 243,\r\n    comment_count: 67,\r\n    share_count: 28,\r\n    time_str: \"昨天\",\r\n    tags: [\"读书\", \"文学\", \"余华\"]\r\n  },\r\n  {\r\n    id: 2,\r\n    user: {\r\n      id: 202,\r\n      name: \"音乐爱好者\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_follow: false\r\n    },\r\n    content: \"分享一首近期很喜欢的民谣，安静时听特别有感觉\",\r\n    audio: {\r\n      url: \"/static/img/avatar.png\",\r\n      cover: \"/static/img/avatar.png\",\r\n      duration: \"04:25\",\r\n      name: \"《夏天的风》\",\r\n      intro: \"民谣·4分钟\"\r\n    },\r\n    view_count: 982,\r\n    like_count: 186,\r\n    comment_count: 34,\r\n    share_count: 15,\r\n    time_str: \"3天前\",\r\n    tags: [\"音乐\", \"民谣\", \"分享\"]\r\n  },\r\n  {\r\n    id: 3,\r\n    user: {\r\n      id: 203,\r\n      name: \"心理咨询师\",\r\n      avatar: \"/static/img/avatar.png\",\r\n      is_follow: true\r\n    },\r\n    content: \"如何缓解工作压力？听听专业心理咨询师的建议\",\r\n    audio: {\r\n      url: \"/static/img/avatar.png\",\r\n      cover: \"/static/img/avatar.png\",\r\n      duration: \"18:45\",\r\n      name: \"压力管理指南\",\r\n      intro: \"心理·18分钟\"\r\n    },\r\n    view_count: 2375,\r\n    like_count: 426,\r\n    comment_count: 103,\r\n    share_count: 87,\r\n    time_str: \"5天前\",\r\n    tags: [\"心理\", \"减压\", \"职场\"]\r\n  }\r\n];\r\n\r\n// 系统通知数据\r\nexport const systemNotices = [\r\n  {\r\n    id: 1,\r\n    title: \"系统升级通知\",\r\n    content: \"为了提供更好的用户体验，系统将于2023年5月20日凌晨2:00-4:00进行升级维护，期间部分功能可能无法正常使用，请您谅解。\",\r\n    time: \"2023-05-18 15:30:00\",\r\n    is_read: false,\r\n    type: 1 // 系统维护\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"新功能上线通知\",\r\n    content: \"视频笔记功能已上线，现在您可以上传最长5分钟的视频内容，分享更丰富的生活瞬间。\",\r\n    time: \"2023-05-15 10:00:00\",\r\n    is_read: true,\r\n    type: 2 // 功能更新\r\n  },\r\n  {\r\n    id: 3,\r\n    title: \"账号安全提醒\",\r\n    content: \"系统检测到您的账号于2023年5月12日在非常用设备登录，如非本人操作，请及时修改密码。\",\r\n    time: \"2023-05-12 20:15:00\",\r\n    is_read: true,\r\n    type: 3 // 安全提醒\r\n  },\r\n  {\r\n    id: 4,\r\n    title: \"活动邀请函\",\r\n    content: \"诚邀您参加「夏日摄影大赛」，上传您的作品，赢取丰厚奖品！活动时间：2023年6月1日-6月30日。\",\r\n    time: \"2023-05-10 14:00:00\",\r\n    is_read: false,\r\n    type: 4 // 活动邀请\r\n  }\r\n];\r\n\r\n// 收货地址数据\r\nexport const addressList = [\r\n  {\r\n    id: 1,\r\n    name: \"张三\",\r\n    phone: \"13812345678\",\r\n    province: \"广东省\",\r\n    city: \"广州市\",\r\n    district: \"天河区\",\r\n    address: \"五山路华南理工大学A栋123室\",\r\n    is_default: true,\r\n    postal_code: \"510000\",\r\n    tag: \"学校\"\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"张三\",\r\n    phone: \"13812345678\",\r\n    province: \"广东省\",\r\n    city: \"广州市\",\r\n    district: \"海珠区\",\r\n    address: \"新港中路123号A座456室\",\r\n    is_default: false,\r\n    postal_code: \"510000\",\r\n    tag: \"家\"\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"李四\",\r\n    phone: \"13987654321\",\r\n    province: \"广东省\",\r\n    city: \"深圳市\",\r\n    district: \"南山区\",\r\n    address: \"科技园南区T3栋789室\",\r\n    is_default: false,\r\n    postal_code: \"518000\",\r\n    tag: \"公司\"\r\n  }\r\n];\r\n\r\n// 广告/轮播图数据\r\nexport const bannerList = [\r\n  {\r\n    id: 1,\r\n    image: \"/static/img/avatar.png\",\r\n    title: \"618年中大促\",\r\n    url: \"/pages/activity/sale?id=1\",\r\n    type: 1 // 活动\r\n  },\r\n  {\r\n    id: 2,\r\n    image: \"/static/img/avatar.png\",\r\n    title: \"新品首发\",\r\n    url: \"/pages/goods/details?id=101\",\r\n    type: 2 // 商品\r\n  },\r\n  {\r\n    id: 3,\r\n    image: \"/static/img/avatar.png\",\r\n    title: \"摄影大赛\",\r\n    url: \"/pages/activity/photo?id=2\",\r\n    type: 1 // 活动\r\n  },\r\n  {\r\n    id: 4,\r\n    image: \"/static/img/avatar.png\",\r\n    title: \"会员专享\",\r\n    url: \"/pages/user/vip\",\r\n    type: 3 // 会员\r\n  }\r\n];\r\n\r\n// 用户足迹/浏览历史\r\nexport const browseHistory = [\r\n  {\r\n    id: 1,\r\n    type: 1, // 商品\r\n    goods: {\r\n      id: 101,\r\n      name: \"无线蓝牙耳机\",\r\n      image: \"/static/img/avatar.png\",\r\n      price: \"199.00\"\r\n    },\r\n    time: \"2023-05-19 15:30:00\"\r\n  },\r\n  {\r\n    id: 2,\r\n    type: 2, // 笔记\r\n    note: {\r\n      id: 201,\r\n      title: \"城市街拍分享\",\r\n      cover: \"/static/img/avatar.png\",\r\n      user: {\r\n        name: \"摄影爱好者\",\r\n        avatar: \"/static/img/avatar.png\"\r\n      }\r\n    },\r\n    time: \"2023-05-18 10:15:00\"\r\n  },\r\n  {\r\n    id: 3,\r\n    type: 1, // 商品\r\n    goods: {\r\n      id: 102,\r\n      name: \"机械键盘\",\r\n      image: \"/static/img/avatar.png\",\r\n      price: \"299.00\"\r\n    },\r\n    time: \"2023-05-17 20:45:00\"\r\n  },\r\n  {\r\n    id: 4,\r\n    type: 3, // 店铺\r\n    shop: {\r\n      id: 301,\r\n      name: \"数码专营店\",\r\n      logo: \"/static/img/avatar.png\",\r\n      follow_count: 5678\r\n    },\r\n    time: \"2023-05-16 14:20:00\"\r\n  }\r\n];\r\n\r\n// 直播间详情数据\r\nexport const liveRoomDetail = {\r\n  id: 1,\r\n  title: \"夏季新品服装展示\",\r\n  cover: \"/static/img/avatar.png\",\r\n  start_time: \"2023-05-20 20:00:00\",\r\n  end_time: \"2023-05-20 22:00:00\",\r\n  status: 1, // 1-未开始 2-直播中 3-已结束\r\n  viewer_count: 1256,\r\n  like_count: 3578,\r\n  host: {\r\n    id: 101,\r\n    name: \"时尚达人\",\r\n    avatar: \"/static/img/avatar.png\",\r\n    fans_count: 12580\r\n  },\r\n  notice: \"今晚8点准时开播，带来夏季最新服装展示，还有超值优惠券发放！\",\r\n  goods_list: [\r\n    {\r\n      id: 1001,\r\n      name: \"夏季薄款T恤\",\r\n      image: \"/static/img/avatar.png\",\r\n      price: \"89.00\",\r\n      original_price: \"129.00\"\r\n    },\r\n    {\r\n      id: 1002,\r\n      name: \"休闲短裤\",\r\n      image: \"/static/img/avatar.png\",\r\n      price: \"129.00\",\r\n      original_price: \"199.00\"\r\n    },\r\n    {\r\n      id: 1003,\r\n      name: \"防晒衣\",\r\n      image: \"/static/img/avatar.png\",\r\n      price: \"159.00\",\r\n      original_price: \"259.00\"\r\n    }\r\n  ],\r\n  chat_messages: [\r\n    {\r\n      id: 10001,\r\n      user: {\r\n        id: 2001,\r\n        name: \"用户A\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        level: 5\r\n      },\r\n      content: \"主播好漂亮啊！\",\r\n      time: \"20:05:30\"\r\n    },\r\n    {\r\n      id: 10002,\r\n      user: {\r\n        id: 2002,\r\n        name: \"用户B\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        level: 3\r\n      },\r\n      content: \"这件T恤有S码吗？\",\r\n      time: \"20:06:15\"\r\n    },\r\n    {\r\n      id: 10003,\r\n      user: {\r\n        id: 101,\r\n        name: \"时尚达人\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        level: 10,\r\n        is_host: true\r\n      },\r\n      content: \"S码有的，库存充足，大家可以放心下单~\",\r\n      time: \"20:06:45\"\r\n    }\r\n  ]\r\n};\r\n\r\n// 视频推荐数据\r\nexport const videoRecommend = [\r\n  {\r\n    id: 1,\r\n    title: \"如何30天练出马甲线\",\r\n    cover: \"/static/img/avatar.png\",\r\n    duration: \"10:25\",\r\n    play_count: 25678,\r\n    user: {\r\n      id: 101,\r\n      name: \"健身教练小王\",\r\n      avatar: \"/static/img/avatar.png\"\r\n    },\r\n    like_count: 1256,\r\n    comment_count: 356\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"一日三餐健康食谱\",\r\n    cover: \"/static/img/avatar.png\",\r\n    duration: \"15:30\",\r\n    play_count: 18765,\r\n    user: {\r\n      id: 102,\r\n      name: \"营养师小李\",\r\n      avatar: \"/static/img/avatar.png\"\r\n    },\r\n    like_count: 987,\r\n    comment_count: 245\r\n  },\r\n  {\r\n    id: 3,\r\n    title: \"10分钟快速妆容教程\",\r\n    cover: \"/static/img/avatar.png\",\r\n    duration: \"08:45\",\r\n    play_count: 32156,\r\n    user: {\r\n      id: 103,\r\n      name: \"美妆博主小张\",\r\n      avatar: \"/static/img/avatar.png\"\r\n    },\r\n    like_count: 2356,\r\n    comment_count: 678\r\n  },\r\n  {\r\n    id: 4,\r\n    title: \"夏日街拍穿搭指南\",\r\n    cover: \"/static/img/avatar.png\",\r\n    duration: \"12:15\",\r\n    play_count: 15678,\r\n    user: {\r\n      id: 104,\r\n      name: \"时尚达人小赵\",\r\n      avatar: \"/static/img/avatar.png\"\r\n    },\r\n    like_count: 1123,\r\n    comment_count: 289\r\n  }\r\n];\r\n\r\n// 积分商城数据\r\nexport const pointsMall = {\r\n  user_points: 1256,\r\n  banner: [\r\n    {\r\n      id: 1,\r\n      image: \"/static/img/avatar.png\",\r\n      url: \"/pages/points_mall/activity?id=1\"\r\n    },\r\n    {\r\n      id: 2,\r\n      image: \"/static/img/avatar.png\",\r\n      url: \"/pages/points_mall/activity?id=2\"\r\n    }\r\n  ],\r\n  categories: [\r\n    {\r\n      id: 1,\r\n      name: \"实物商品\",\r\n      icon: \"/static/img/avatar.png\"\r\n    },\r\n    {\r\n      id: 2,\r\n      name: \"优惠券\",\r\n      icon: \"/static/img/avatar.png\"\r\n    },\r\n    {\r\n      id: 3,\r\n      name: \"会员特权\",\r\n      icon: \"/static/img/avatar.png\"\r\n    }\r\n  ],\r\n  products: [\r\n    {\r\n      id: 1,\r\n      name: \"时尚双肩包\",\r\n      image: \"/static/img/avatar.png\",\r\n      points: 1999,\r\n      original_price: \"299.00\",\r\n      stock: 100,\r\n      exchange_count: 56\r\n    },\r\n    {\r\n      id: 2,\r\n      name: \"无线充电器\",\r\n      image: \"/static/img/avatar.png\",\r\n      points: 999,\r\n      original_price: \"129.00\",\r\n      stock: 200,\r\n      exchange_count: 128\r\n    },\r\n    {\r\n      id: 3,\r\n      name: \"精美保温杯\",\r\n      image: \"/static/img/avatar.png\",\r\n      points: 699,\r\n      original_price: \"89.00\",\r\n      stock: 150,\r\n      exchange_count: 87\r\n    },\r\n    {\r\n      id: 4,\r\n      name: \"满200减50优惠券\",\r\n      image: \"/static/img/avatar.png\",\r\n      points: 299,\r\n      original_price: \"50.00\",\r\n      stock: 500,\r\n      exchange_count: 356\r\n    }\r\n  ],\r\n  exchange_records: [\r\n    {\r\n      user_avatar: \"/static/img/avatar.png\",\r\n      product_name: \"精美保温杯\",\r\n      time: \"刚刚\"\r\n    },\r\n    {\r\n      user_avatar: \"/static/img/avatar.png\",\r\n      product_name: \"无线充电器\",\r\n      time: \"5分钟前\"\r\n    },\r\n    {\r\n      user_avatar: \"/static/img/avatar.png\",\r\n      product_name: \"满200减50优惠券\",\r\n      time: \"10分钟前\"\r\n    }\r\n  ]\r\n};\r\n\r\n// 模拟API响应\r\nexport const getMockData = (url, data, method) => {\r\n  // 笔记详情\r\n  if (url === 'note/details' && method === 'GET') {\r\n    return {\r\n      code: 200,\r\n      msg: '获取成功',\r\n      data: noteDetail\r\n    }\r\n  }\r\n  \r\n  // 评论列表\r\n  if (url === 'comment/list' && method === 'GET') {\r\n    return {\r\n      code: 200,\r\n      msg: '获取成功',\r\n      data: {\r\n        data: commentList,\r\n        current_page: 1,\r\n        last_page: 1,\r\n        total: commentList.length\r\n      }\r\n    }\r\n  }\r\n  \r\n  // 子评论列表\r\n  if (url === 'comment/list/son' && method === 'GET') {\r\n    const commentId = data.id\r\n    const parentComment = commentList.find(item => item.id === commentId)\r\n    if (parentComment) {\r\n      return {\r\n        code: 200,\r\n        msg: '获取成功',\r\n        data: {\r\n          data: parentComment.list,\r\n          current_page: 1,\r\n          last_page: 1,\r\n          total: parentComment.list.length\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  // 保存评论\r\n  if (url === 'comment/save' && method === 'POST') {\r\n    return {\r\n      code: 200,\r\n      msg: '评论成功',\r\n      data: {\r\n        id: Math.floor(Math.random() * 10000),\r\n        user: {\r\n          id: 1001,\r\n          name: \"当前用户\",\r\n          avatar: \"/static/img/avatar1.png\"\r\n        },\r\n        content: data.content,\r\n        create_time_str: \"刚刚\",\r\n        province: \"本地\",\r\n        status: 1,\r\n        list_count: 0,\r\n        list: []\r\n      }\r\n    }\r\n  }\r\n  \r\n  // 删除评论\r\n  if (url === 'comment/del' && method === 'POST') {\r\n    return {\r\n      code: 200,\r\n      msg: '删除成功'\r\n    }\r\n  }\r\n  \r\n  // 删除笔记\r\n  if (url === 'note/del' && method === 'POST') {\r\n    return {\r\n      code: 200,\r\n      msg: '删除成功'\r\n    }\r\n  }\r\n  \r\n  // 举报笔记\r\n  if (url === 'note/report' && method === 'POST') {\r\n    return {\r\n      code: 200,\r\n      msg: '举报成功'\r\n    }\r\n  }\r\n  \r\n  // 点赞笔记\r\n  if (url === 'note/like' && method === 'POST') {\r\n    return {\r\n      code: 200,\r\n      msg: '操作成功'\r\n    }\r\n  }\r\n  \r\n  // 圈子列表\r\n  if (url === 'circle/list' && method === 'GET') {\r\n    const pageSize = 4\r\n    const page = data.page || 1\r\n    const startIndex = (page - 1) * pageSize\r\n    const endIndex = startIndex + pageSize\r\n    \r\n    let list = [...circleList]\r\n    if (data.type === 1) {\r\n      // 我的圈子，显示特定ID的圈子\r\n      const myCircleIds = [1, 3, 5, 8, 10]\r\n      list = list.filter(item => myCircleIds.includes(item.id))\r\n    }\r\n    \r\n    const pageData = list.slice(startIndex, endIndex)\r\n    \r\n    return {\r\n      code: 200,\r\n      msg: '获取成功',\r\n      data: {\r\n        data: pageData,\r\n        current_page: page,\r\n        last_page: Math.ceil(list.length / pageSize),\r\n        total: list.length\r\n      }\r\n    }\r\n  }\r\n  \r\n  // 获取直播信息\r\n  if (url === 'live/details' && method === 'GET') {\r\n    const randomIndex = Math.floor(Math.random() * liveInfo.length);\r\n    const live = liveInfo[randomIndex];\r\n    return {\r\n      code: 200,\r\n      msg: '获取成功',\r\n      data: [live.title, live.path, live.content, live.url, live.status]\r\n    }\r\n  }\r\n  \r\n  // 如果没有匹配的模拟数据，返回null，表示需要进行真实请求\r\n  return null\r\n}\r\n\r\nexport default {\r\n  userInfo,\r\n  circleList,\r\n  activityList,\r\n  dynamicList,\r\n  goodsList,\r\n  goodsDetails,\r\n  orderList,\r\n  evaluateList,\r\n  cartList,\r\n  classifyList,\r\n  searchData,\r\n  noteDetail,\r\n  noteComments,\r\n  messageList,\r\n  userCircleList,\r\n  circleDetail,\r\n  commentList,\r\n  getMockData,\r\n  liveInfo,\r\n  userNotes,\r\n  userLikes,\r\n  userDrafts,\r\n  \r\n  // 新增数据类型\r\n  topicList,\r\n  tagList,\r\n  couponList,\r\n  userCollections,\r\n  videoNotes,\r\n  audioNotes,\r\n  systemNotices,\r\n  addressList,\r\n  bannerList,\r\n  browseHistory,\r\n  liveRoomDetail,\r\n  videoRecommend,\r\n  pointsMall\r\n} "], "names": [], "mappings": ";AA2eY,MAAC,YAAY;AAAA,EACvB;AAAA,IACE,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,OAAO;AAAA,MACL;AAAA,QACE,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,KAAK;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,OAAO;AAAA,MACL;AAAA,QACE,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,KAAK;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,OAAO;AAAA,MACL;AAAA,QACE,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,KAAK;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACH;;"}