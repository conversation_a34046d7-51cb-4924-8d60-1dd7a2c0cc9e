<view class="container"><view class="nav-box" style="{{'padding-top:' + x}}"><view class="nav-bar df" style="{{'height:' + r}}"><view class="nav-icon df" data-url="search/index?bar=1" bindtap="{{b}}"><image src="{{a}}"></image></view><view wx:if="{{c}}" class="nav-icon df" data-url="goods/classify" bindtap="{{e}}"><image src="{{d}}"></image></view><view class="nav-icon df" data-url="order/index" bindtap="{{h}}"><image src="{{f}}"></image><view wx:if="{{g}}" class="microlabel" style="top:12rpx;right:8rpx"></view></view><view wx:if="{{i}}" class="{{['nav-icon', 'df', l]}}" data-url="center/card" bindtap="{{m}}"><image src="{{j}}"></image><view wx:if="{{k}}" class="microlabel" style="top:12rpx;right:8rpx"></view></view><view class="nav-icon df" data-url="goods/cart" bindtap="{{q}}"><image src="{{n}}"></image><view wx:if="{{o}}" class="msg">{{p}}</view></view></view><view class="nav-classify" style="{{'height:' + w}}"><scroll-view scroll-x="true" class="classify-scroll"><view class="classify-box df"><view wx:for="{{s}}" wx:for-item="item" wx:key="b" class="classify-item" style="{{'color:' + item.c + ';' + ('font-weight:' + item.d)}}" data-idx="{{item.e}}" bindtap="{{item.f}}">{{item.a}}</view><view style="flex-shrink:0;width:120rpx;height:68rpx"></view></view></scroll-view><view class="classify-all df" data-url="goods/classify" bindtap="{{v}}"><image src="{{t}}"></image><text>分类</text></view></view></view><view class="content-box" style="{{'padding-top:' + J}}"><view class="heio df" style="{{'height:' + z}}"><uni-load-more wx:if="{{true}}" u-i="33b4e39d-0" bind:__l="__l" u-p="{{y}}"></uni-load-more></view><view class="goods-box"><view wx:if="{{A}}" class="goods-banner"><view class="banner-swiper"><swiper class="banner-swiper" circular autoplay bindchange="{{C}}"><swiper-item wx:for="{{B}}" wx:for-item="item" wx:key="c" data-url="{{item.d}}" bindtap="{{item.e}}"><lazy-image wx:if="{{item.b}}" u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}"></lazy-image></swiper-item></swiper><view class="swiper-idor df"><view wx:for="{{D}}" wx:for-item="item" wx:key="a" class="{{['idor-item', item.b]}}"></view></view></view></view><block wx:if="{{E}}"><view wx:for="{{F}}" wx:for-item="item" wx:key="i" class="goods-item" data-url="{{item.j}}" bindtap="{{item.k}}"><view class="goods-img"><view class="goods-img-item"><lazy-image wx:if="{{item.b}}" u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}"></lazy-image></view></view><view class="goods-name ohto2">{{item.c}}</view><view class="goods-price"><money wx:if="{{item.e}}" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"></money><view class="price-h" style="text-decoration:line-through">¥{{item.f}}</view><view class="price-h">{{item.g}}</view></view><view class="goods-tag df"><view wx:for="{{item.h}}" wx:for-item="tag" wx:key="b" class="tag-item">{{tag.a}}</view></view></view></block></view><view wx:if="{{G}}" class="empty-box df"><image src="{{H}}"/><view class="e1">暂无推荐商品</view><view class="e2">正在为您制造更多美好的商品</view></view><uni-load-more wx:if="{{true}}" u-i="33b4e39d-4" bind:__l="__l" u-p="{{I}}"></uni-load-more></view><tabbar wx:if="{{true}}" u-i="33b4e39d-5" bind:__l="__l" u-p="{{K}}"></tabbar></view>