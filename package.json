{"name": "uniapp-vue3-project", "version": "1.0.0", "description": "UniApp Vue3电商社交应用", "main": "main.js", "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "dev:h5": "uni build --watch", "build:h5": "uni build", "dev:mp-weixin": "uni build -p mp-weixin --watch", "build:mp-weixin": "uni build -p mp-weixin", "dev:app": "uni build -p app --watch", "build:app": "uni build -p app"}, "dependencies": {"vue": "^3.3.4", "vuex": "^4.1.0", "pinia": "^2.1.7", "@vue/compat": "^3.3.4", "vue-i18n": "^9.2.2"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^3.0.0", "@dcloudio/uni-stacktracey": "^3.0.0", "@dcloudio/webpack-uni-mp-loader": "^3.0.0", "@vue/cli-service": "^5.0.8", "node-sass": "^7.0.3", "sass-loader": "^13.3.2"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}