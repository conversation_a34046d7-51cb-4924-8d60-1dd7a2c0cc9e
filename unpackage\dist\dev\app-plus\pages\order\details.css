
.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.money-box[data-v-433ff6d7] {
  display: flex;
  align-items: flex-end;
  font-weight: bolder;
}
.money-box .sale[data-v-433ff6d7] {
  margin: 0 0.0625rem;
}
.money-box .unit[data-v-433ff6d7] {
  margin-bottom: 0.0625rem;
}


body { background: #f8f8f8;
}
.container { padding-bottom: 7.5rem;
}
.nav-box {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 99;
  box-sizing: border-box;
}
.nav-box .back {
  padding: 0 0.9375rem;
  height: 100%;
  justify-content: center;
}
.nav-box .nav-title {
  font-size: 1rem;
  font-weight: 700;
}
.w100p30 {
  z-index: 1;
  margin: 0.625rem 0.625rem 0;
  width: calc(100% - 2.5rem);
  padding: 0.9375rem 0.625rem;
  border-radius: 0.9375rem;
  background: #fff;
  overflow: hidden;
  position: relative;
}
.title {
  width: 100%;
  color: #000;
  font-size: 0.8125rem;
  font-weight: 700;
}
.w100p30 .map-bg {
  position: absolute;
  z-index: -2;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}
.w100p30 .mask-bg {
  position: absolute;
  z-index: -1;
  right: -0.0625rem;
  bottom: -0.0625rem;
  width: calc(100% - 0.75rem);
  height: calc(100% - 0.75rem);
  border: 0.4375rem solid #fff;
  border-radius: 0.9375rem;
  background-image: linear-gradient(to right, #fff, rgba(255, 255, 255, .9), rgba(255, 255, 255, .6));
}
.adds-box {
  width: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}
.adds-box .adds-item {
  width: calc(100% - 1.875rem);
  font-size: 0.75rem;
  font-weight: 300;
}
.adds-box .adds-item .nm {
  padding: 0.625rem 0 0.3125rem;
  font-size: 1rem;
  font-weight: 700;
}
.adds-item .express {
  margin-top: 0.625rem;
  width: 100%;
  font-size: 0.8125rem;
}
.adds-item .express uni-text {
  margin: 0 0.25rem;
  font-weight: 700;
}
.adds-box .adds-icon {
  margin: 0 0.3125rem 0.625rem 0;
  width: 1.25rem;
  height: 1.25rem;
  background: #000;
  justify-content: center;
  border-radius: 0.625rem 0.625rem 0.125rem;
  box-shadow: 0.25rem 0.25rem 0.25rem -0.125rem rgba(0, 0, 0, .1);
  transform: rotate(45deg);
}
.adds-box .adds-icon uni-view {
  width: 0.5rem;
  height: 0.5rem;
  background: #fff;
  border-radius: 50%;
}
.goods-item {
  padding-top: 0.9375rem;
  display: flex;
  justify-content: space-between;
  animation: fadeIn .45s ease;
}
.goods-item uni-image {
  width: 4.375rem;
  height: 4.375rem;
  border-radius: 0.25rem;
}
.goods-item .goods-info {
  width: calc(100% - 5rem);
  font-weight: 700;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.goods-item .goods-info .t1 {
  color: #000;
  font-size: 0.8125rem;
}
.goods-item .goods-info .t2 {
  color: #999;
  font-size: 0.75rem;
}
.goods-item .goods-info .goods-info-bom {
  margin-top: 0.78125rem;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.goods-info .goods-info-bom .sum {
  color: #999;
  font-size: 0.625rem;
}
.list-item {
  padding: 0.46875rem 0;
  justify-content: space-between;
  color: #000;
  font-size: 0.75rem;
}
.list-item .list-right .zs {
  color: #999;
  margin-right: 0.3125rem;
}
.footer {
  position: fixed;
  z-index: 99;
  bottom: 0;
  left: 0;
  width: calc(100% - 0.625rem);
  padding: 0.625rem 0.3125rem;
  background-color: #fff;
  border-top: 0.0625rem solid #f5f5f5;
  padding-bottom: env(safe-area-inset-bottom);
}
.footer .icon-box {
  margin: 0;
  width: 2.5rem !important;
  height: 2.75rem;
  padding: 0.3125rem 0;
  flex-direction: column;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0);
  position: relative;
}
.footer .icon-box uni-image {
  width: 1.25rem;
  height: 1.25rem;
}
.footer .icon-box uni-text {
  line-height: 0.5625rem;
  font-size: 0.5625rem;
}
.footer .footer-btn {
  width: calc(100% - 2.5rem);
  justify-content: flex-end;
}
.footer-btn uni-view {
  margin: 0 0.3125rem;
  padding: 0 1.25rem;
  height: 2.5rem;
  line-height: 2.5rem;
  font-size: 0.75rem;
  font-weight: bolder;
  border-radius: 1.25rem;
}
.footer-btn uni-view uni-image {
  width: 1rem;
  height: 1rem;
  margin-right: 0.3125rem;
}
.footer-btn .btn1 {
  color: #999;
  border: 0.0625rem solid #F5F5F5;
}
.footer-btn .btn2 {
  color: #fff;
  background: #000;
  border: 0.0625rem solid #000;
}
.note-box {
  padding: 0.46875rem;
  background: #fff;
  border-radius: 0.9375rem;
}
.note-box .note-add {
  margin: 0.9375rem;
  width: 12.5rem;
  height: 2.8125rem;
  font-size: 0.75rem;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 1.40625rem;
  justify-content: center;
}
.note-box .note-add uni-image {
  margin-right: 0.3125rem;
  width: 1.25rem;
  height: 1.25rem;
}
.popup-box {
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  background: #fff;
  border-radius: 0.9375rem 0.9375rem 0 0;
  position: relative;
  overflow: hidden;
}
.popup-box .popup-top {
  width: calc(100% - 0.625rem);
  padding: 0.3125rem;
  justify-content: space-between;
}
.popup-top .popup-title .t1 {
  font-size: 1.1875rem;
  font-weight: 700;
}
.popup-top .popup-title .t2 {
  color: #999;
  font-size: 0.625rem;
  font-weight: 300;
}
.popup-top .popup-close {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: #f8f8f8;
  justify-content: center;
  transform: rotate(45deg);
}
.popup-box .popup-btn {
  margin: 1.25rem 0.3125rem;
  width: calc(100% - 0.625rem);
  height: 3.125rem;
  line-height: 3.125rem;
  text-align: center;
  font-size: 0.75rem;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 3.125rem;
}
.popup-item {
  width: calc(100% - 0.625rem);
  padding: 0.3125rem;
}
.popup-item .popup-type uni-view {
  margin-right: 0.625rem;
  padding: 0 1.25rem;
  height: 2.5rem;
  line-height: 2.5rem;
  font-size: 0.625rem;
  font-weight: 700;
  border-radius: 0.25rem;
  border: 1px solid #f5f5f5;
}
.popup-item .popup-type .active {
  border: 1px solid #000;
  background: rgba(0, 0, 0, .125);
}
.popup-item .popup-textarea,
.popup-item .popup-adds {
  margin-top: 0.9375rem;
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  border-radius: 0.25rem;
  background: #f8f8f8;
}
.popup-item .popup-textarea {
  font-size: 0.75rem;
  font-weight: 700;
  min-height: 3.75rem;
}
.popup-item .popup-adds {
  display: flex;
  flex-direction: column;
}
.popup-item .popup-adds .a1 {
  font-size: 0.75rem;
  font-weight: 700;
}
.popup-item .popup-adds .a2 {
  padding-top: 0.3125rem;
  color: #999;
  font-size: 0.625rem;
  font-weight: 300;
}
.popup-item .popup-wl {
  margin-top: 0.9375rem;
  width: 100%;
  justify-content: space-between;
}
.popup-item .popup-wl uni-input {
  width: calc(50% - 1.5625rem);
  padding: 0.625rem;
  border-radius: 0.25rem;
  background: #f8f8f8;
  font-size: 0.75rem;
  font-weight: 700;
}
.tips-box {
  padding: 0.625rem 0.9375rem;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 0.375rem;
  justify-content: center;
}
.tips-box .tips-item {
  color: #fff;
  font-size: 0.875rem;
  font-weight: 700;
}
.bf8 {
  background: #f8f8f8;
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
