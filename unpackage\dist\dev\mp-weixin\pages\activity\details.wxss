
.container {
  width: 100%;
  padding-bottom: calc(env(safe-area-inset-bottom) + 160rpx);
}
.nav-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.nav-box .nav-back {
  padding: 0 30rpx;
  width: 34rpx;
  height: 100%;
}
.nav-box .nav-back image {
  width: 34rpx;
  height: 34rpx;
}
.nav-box .nav-title {
  max-width: 60%;
  font-size: 32rpx;
  font-weight: 700;
}
.swiper-box {
  z-index: -1;
  width: 100%;
  height: auto;
  aspect-ratio: 5/4;
  overflow: hidden;
}
.swiper-box .swiper-item {
  width: 100%;
  height: 100%;
}
.indicator {
  margin-top: -68rpx;
  width: 100%;
  justify-content: center;
}
.indicator .indicator-item {
  z-index: 1;
  margin: 0 6rpx;
  width: 8rpx;
  height: 8rpx;
  border-radius: 8rpx;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s;
}
.indicator .active {
  width: 24rpx;
  background: rgba(255, 255, 255, 0.9);
}
.arr {
  width: 44rpx;
  height: 44rpx;
  background: #fff;
  border-radius: 50%;
  justify-content: center;
}
.arr image {
  width: 20rpx;
  height: 20rpx;
}
.content-box {
  width: 100%;
  padding-top: 30rpx;
}
.content-box .content-bar {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 99;
  width: 100%;
  height: 100rpx;
  background: #fff;
  border-radius: 30rpx 30rpx 0 0;
  justify-content: space-between;
}
.content-bar .bar-nav {
  padding: 0 15rpx;
  height: 80rpx;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.content-bar .bar-nav text {
  font-weight: 700;
  transition: all 0.3s ease-in-out;
}
.content-bar .bar-nav .line {
  position: absolute;
  bottom: 12rpx;
  width: 18rpx;
  height: 6rpx;
  border-radius: 6rpx;
  background: #000;
  transition: opacity 0.3s ease-in-out;
}
.content-bar .bar-btn {
  padding: 0 16rpx;
  height: 50rpx;
  line-height: 50rpx;
  border-radius: 8rpx;
  background: #fff;
  border: 1px solid #f5f5f5;
}
.bar-btn text {
  margin-left: 8rpx;
  font-size: 20rpx;
  font-weight: 700;
}
.bar-btn .avatar {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
}
.bar-btn .icon {
  width: 20rpx;
  height: 20rpx;
}
.content-bar .s1 {
  color: #fa5150;
  background: rgba(250, 81, 80, 0.082);
  border: 1px solid #FA515015;
}
.content-bar .s2 {
  color: #000;
  background: rgba(0, 0, 0, 0.082);
  border: 1px solid #000;
}
.content-box .content-item {
  width: calc(100% - 60rpx);
  padding: 0 30rpx;
}
.content-box .dynamic-box {
  width: calc(100% - 16rpx);
  padding: 0 8rpx;
}
.content-box .joins-box {
  margin-bottom: 30rpx;
  width: calc(100% - 60rpx);
  padding: 30rpx;
  background: #000;
  border-radius: 8rpx;
  justify-content: space-between;
}
.content-box .joins-box .txt {
  color: #fff;
  font-size: 22rpx;
  font-weight: 700;
}
.content-box .joins-box .txt image {
  margin-right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
}
.content-box .info-map {
  width: calc(100% - 2px);
  border-radius: 8rpx;
  border: 1px solid #f5f5f5;
  position: relative;
  overflow: hidden;
}
.content-box .info-map .bg, .content-box .info-map .mk {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.content-box .info-map .mk {
  z-index: -1;
  background-image: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.6));
}
.content-box .info-item {
  width: calc(100% - 60rpx);
  padding: 30rpx;
  font-weight: 700;
}
.info-item .info-item-tit {
  color: #999;
  font-size: 24rpx;
  margin-bottom: 8rpx;
}
.info-item .info-item-tit text {
  color: #000;
}
.info-item .adds-box {
  width: 44rpx;
  height: 44rpx;
  justify-content: center;
  border-radius: 22rpx 22rpx 4rpx;
  box-shadow: 5px 5px 5px -4px rgba(0, 0, 0, 0.1);
  background: #000;
  transform: rotate(45deg);
}
.info-item .adds-box image {
  width: 24rpx;
  height: 24rpx;
  transform: rotate(-45deg);
}
.info-item .cu-img-group {
  margin-left: 16rpx;
  direction: ltr;
  unicode-bidi: bidi-override;
  display: inline-block;
}
.cu-img-group .img-group {
  width: 32rpx;
  height: 32rpx;
  display: inline-flex;
  position: relative;
  margin-left: -16rpx;
  border: 2rpx solid #f8f8f8;
  background: #eee;
  vertical-align: middle;
  border-radius: 8rpx;
  border-radius: 50%;
}
.cu-img-group .img-group image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  border-radius: 50%;
}
.cu-img-group .img-tit {
  display: inline-flex;
  margin-left: 8rpx;
  color: #999;
  font-size: 20rpx;
}
.content-box .info-title {
  margin: 30rpx 0;
  width: 100%;
  font-size: 32rpx;
  font-weight: 700;
}
.content-box .info-intro {
  width: 100%;
  color: #333;
  font-size: 26rpx;
  font-weight: 400;
  word-break: break-word;
  white-space: pre-line;
}
.footer-box {
  position: fixed;
  z-index: 99;
  left: 0;
  bottom: 0;
  width: calc(100% - 60rpx);
  padding: 30rpx;
  border-top: 1px solid #f8f8f8;
  padding-bottom: max(env(safe-area-inset-bottom), 30rpx);
}
.btn-box {
  width: 100%;
  justify-content: space-between;
}
.btn-box .btn-price {
  height: 100rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.btn-price .nian {
  margin-right: 6rpx;
  font-size: 20rpx;
  font-weight: 700;
}
.btn-price .through {
  color: #999;
  font-size: 20rpx;
  text-decoration: line-through;
}
.btn-box .btn-item {
  padding: 0 60rpx;
  height: 100rpx;
  font-size: 24rpx;
  color: #fff;
  font-weight: 700;
  background: #000;
  border-radius: 100rpx;
}
.btn-box .btn-means {
  padding: 0 30rpx;
  height: 90rpx;
  font-size: 20rpx;
  font-weight: 700;
  background: #f5f5f5;
  border-radius: 100rpx;
}
.btn-box .btn-means image {
  margin-right: 12rpx;
  width: 20rpx;
  height: 20rpx;
}
.btn-box .btn-item .icon {
  margin-right: 12rpx;
  width: 32rpx;
  height: 32rpx;
}
.popup-box {
  width: calc(100% - 60rpx);
  padding: 30rpx;
  background: #fff;
  border-radius: 30rpx 30rpx 0 0;
  padding-bottom: max(env(safe-area-inset-bottom), 60rpx);
  position: relative;
}
.popup-box .popup-top {
  width: 100%;
  justify-content: space-between;
}
.popup-top .popup-title .t1 {
  font-size: 38rpx;
  font-weight: 700;
}
.popup-top .popup-title .t2 {
  color: #999;
  font-size: 20rpx;
  font-weight: 300;
}
.popup-top .popup-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f8f8;
  justify-content: center;
  transform: rotate(45deg);
}
.popup-box .popup-btn {
  margin: 60rpx 0 30rpx;
  width: 100%;
  height: 100rpx;
  font-size: 24rpx;
  color: #fff;
  font-weight: 700;
  background: #000;
  border-radius: 100rpx;
  justify-content: center;
}
.popup-box .popup-btn image {
  margin-right: 12rpx;
  width: 32rpx;
  height: 32rpx;
}
.scroll-box {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
}
.popup-box .product-box {
  width: 100%;
  padding: 46rpx 0 30rpx;
  display: flex;
}
.product-box .product-item {
  flex-shrink: 0;
  margin-right: 20rpx;
  padding: 36rpx 20rpx 20rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
  border: 1px solid #f8f8f8;
  position: relative;
}
.product-box .active {
  background: #fff;
  border: 1px solid #000;
}
.product-item .tag {
  position: absolute;
  top: -16rpx;
  left: -1px;
  padding: 0 16rpx;
  height: 32rpx;
  line-height: 32rpx;
  text-align: center;
  font-size: 18rpx;
  font-weight: 500;
  color: #fff;
  background: linear-gradient(to right, #000, #555);
  border-radius: 24rpx 32rpx 32rpx 0rpx;
}
.product-item .name {
  font-size: 24rpx;
  line-height: 24rpx;
  font-weight: 500;
}
.product-item .time {
  margin: 15rpx 0;
  font-weight: 300;
  font-size: 20rpx;
  line-height: 20rpx;
}
.product-item .td-lt {
  margin-left: 15rpx;
  color: #999;
  font-size: 20rpx;
  line-height: 20rpx;
  text-decoration: line-through;
}
.popup-box .quantity-box {
  padding: 30rpx 0;
  width: 100%;
  justify-content: space-between;
  border-top: 1px solid #f8f8f8;
}
.quantity-box .quantity-tit {
  font-size: 24rpx;
}
.quantity-box .quantity-item {
  height: 64rpx;
  line-height: 64rpx;
  border-radius: 32rpx;
  border: 1px solid #f5f5f5;
  font-size: 24rpx;
  font-weight: 700;
  text-align: center;
}
.quantity-item input {
  width: 48rpx;
  height: 64rpx;
  line-height: 64rpx;
  color: #000;
}
.quantity-item .quantity-btn {
  width: 64rpx;
  height: 64rpx;
  line-height: 64rpx;
}
.share-popup {
  background: #fff;
  border-radius: 30rpx;
  padding: 30rpx;
  overflow: hidden;
}
.share-popup .share-img {
  width: 473rpx;
  height: 237.5rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  display: block;
}
.share-popup .share-tips {
  margin: 30rpx 0;
  width: 473rpx;
  font-size: 26rpx;
  line-height: 48rpx;
  position: relative;
}
.share-popup .share-tips image {
  position: absolute;
  top: 0;
  width: 48rpx;
  height: 48rpx;
  margin: 0 15rpx;
}
.share-popup .share-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 16rpx;
}
.note-box {
  padding: 15rpx;
  background: #fff;
  border-radius: 30rpx;
}
.note-box .note-add {
  margin: 30rpx;
  width: 400rpx;
  height: 90rpx;
  font-size: 24rpx;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 45rpx;
  justify-content: center;
}
.note-box .note-add image {
  margin-right: 10rpx;
  width: 40rpx;
  height: 40rpx;
}
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-box image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-box .e1 {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.empty-box .e2 {
  font-size: 24rpx;
  color: #999;
}
.tips-box {
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
  justify-content: center;
}
.tips-box .tips-item {
  color: #fff;
  font-size: 28rpx;
  font-weight: 700;
}
.xwb {
  filter: brightness(0);
}
.df {
  display: flex;
  align-items: center;
}
.bfw {
  background: #fff;
}
.bUp {
  box-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.effect {
  transition: all 0.3s ease-in-out;
}
