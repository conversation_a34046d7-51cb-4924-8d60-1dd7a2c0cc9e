{"version": 3, "file": "Authorize.js", "sources": ["components/Authorize.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy9BdXRob3JpemUudnVl"], "sourcesContent": ["<template>\n\t<view>\n\t\t<view class=\"Popup\" v-if=\"isShowAuth\">\n\t\t\t<image :src=\"logoUrl\"></image>\n\t\t\t<view class=\"title\">授权提醒</view>\n\t\t\t<view class=\"tip\">请授权头像等信息，以便为您提供更好的服务</view>\n\t\t\t<view class=\"bottom flex\">\n\t\t\t\t<view class=\"item\" @click=\"close\">随便逛逛</view>\n\t\t\t\t<!-- #ifdef APP-PLUS -->\n\t\t\t\t<button class=\"item grant\" @click=\"setUserInfo\">去授权</button>\n\t\t\t\t<!-- #endif -->\n\t\t\t\t<!-- #ifdef MP -->\n\t\t\t\t<button class=\"item grant\" type=\"primary\" open-type=\"getPhoneNumber\" lang=\"zh_CN\" @getphonenumber=\"setUserInfo\">去授权</button>\n\t\t\t\t<!-- #endif -->\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"mask\" v-if=\"isShowAuth\" @click=\"close\"></view>\n\t</view>\n</template>\n\n<script>\nconst app = getApp();\nimport Cache from '../utils/cache';\nimport { getLogo, silenceAuth, routineBindingPhone } from '../api/public';\nimport cacheConfig from '../config/cache.js';\nconst { LOGO_URL, EXPIRES_TIME, USER_INFO, STATE_R_KEY } = cacheConfig;\nimport { useUserStore } from '@/stores/user.js';\nimport Routine from '../libs/routine';\n\nexport default {\n\tname: 'Authorize',\n\tprops: {\n\t\tisAuto: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\tisGoIndex: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\tisShowAuth: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tuserStore: useUserStore(),\n\t\t\tlogoUrl: '',\n\t\t\tauthKey: ''\n\t\t};\n\t},\n\tcomputed: {\n\t\tisLogin() {\n\t\t\treturn this.userStore.isLoggedIn;\n\t\t},\n\t\tuserInfo() {\n\t\t\treturn this.userStore.userInfo;\n\t\t}\n\t},\n\twatch: {\n\t\tisLogin(n) {\n\t\t\tn === true && this.$emit('onLoadFun', this.userInfo);\n\t\t}\n\t},\n\tmounted() {\n\t\tthis.getLogoUrl();\n\t\tlet that = this;\n\t\tif (!this.isLogin && !Cache.has(STATE_R_KEY)) {\n\t\t\twx.login({\n\t\t\t\tsuccess(res) {\n\t\t\t\t\tCache.set(STATE_R_KEY, res.code, 10800);\n\t\t\t\t\tlet spread = app.globalData.spid ? app.globalData.spid : '';\n\t\t\t\t\t// silenceAuth({ code: res.code, spread: spread, spid: app.globalData.code })\n\t\t\t\t\t// \t.then(res => {\n\t\t\t\t\t// \t\tif (res.data.key !== undefined && res.data.key) {\n\t\t\t\t\t// \t\t\tthat.authKey = res.data.key;\n\t\t\t\t\t// \t\t} else {\n\t\t\t\t\t// \t\t\tapp.globalData.code = 0;\n\t\t\t\t\t// \t\t\tlet time = res.data.expires_time - Cache.time();\n\t\t\t\t\t// \t\t\t// store.commit('UPDATE_USERINFO', res.data.userInfo);\n\t\t\t\t\t// \t\t\tstore.commit('LOGIN', { token: res.data.token, time: time });\n\t\t\t\t\t// \t\t\t// store.commit('SETUID', res.data.userInfo.uid);\n\t\t\t\t\t// \t\t\t// Cache.set(EXPIRES_TIME,res.data.expires_time,time);\n\t\t\t\t\t// \t\t\t// Cache.set(USER_INFO,res.data.userInfo,time);\n\t\t\t\t\t// \t\t}\n\t\t\t\t\t// \t})\n\t\t\t\t\t// \t.catch(res => {\n\t\t\t\t\t// \t});\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tthis.setAuthStatus();\n\t\t}\n\t},\n\tmethods: {\n\t\tsetAuthStatus() {\n\t\t\tRoutine.authorize()\n\t\t\t\t.then(res => {\n\t\t\t\t\tif (res.islogin === false) this.setUserInfo();\n\t\t\t\t\telse this.$emit('onLoadFun', this.userInfo);\n\t\t\t\t})\n\t\t\t\t.catch(res => {\n\t\t\t\t\tif (this.isAuto) this.$emit('authColse', true);\n\t\t\t\t});\n\t\t},\n\t\tgetUserInfo(code) {\n\t\t\tRoutine.getUserInfo()\n\t\t\t\t.then(res => {\n\t\t\t\t\tlet userInfo = res.userInfo;\n\t\t\t\t\tuserInfo.code = code;\n\t\t\t\t\tuserInfo.spread_spid = app.globalData.spid; //获取推广人ID\n\t\t\t\t\tuserInfo.spread_code = app.globalData.code; //获取推广人分享二维码ID\n\t\t\t\t\tRoutine.authUserInfo(userInfo)\n\t\t\t\t\t\t.then(res => {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tthis.$emit('authColse', false);\n\t\t\t\t\t\t\tthis.$emit('onLoadFun', this.userInfo);\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(res => {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t})\n\t\t\t\t.catch(res => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t});\n\t\t},\n\t\tgetUserPhoneNumber(encryptedData, iv, code) {\n\t\t\troutineBindingPhone({\n\t\t\t\tencryptedData: encryptedData,\n\t\t\t\tiv: iv,\n\t\t\t\tcode: code,\n\t\t\t\tspid: app.globalData.spid,\n\t\t\t\tspread: app.globalData.code\n\t\t\t})\n\t\t\t\t.then(res => {\n\t\t\t\t\t// 使用Pinia更新登录状态\n\t\t\t\t\tthis.userStore.setToken({\n\t\t\t\t\t\ttoken: res.data.token,\n\t\t\t\t\t\ttime: res.data.expires_time - this.$Cache.time()\n\t\t\t\t\t});\n\t\t\t\t\tthis.$emit('authColse', false);\n\t\t\t\t\tthis.$emit('onLoadFun', res.data.userInfo);\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t})\n\t\t\t\t.catch(res => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t});\n\t\t},\n\t\tsetUserInfo(e) {\n\t\t\tuni.showLoading({ title: '正在登录中' });\n\t\t\tRoutine.getCode()\n\t\t\t\t.then(code => {\n\t\t\t\t\tthis.getUserPhoneNumber(e.detail.encryptedData, e.detail.iv, code);\n\t\t\t\t})\n\t\t\t\t.catch(res => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t});\n\t\t},\n\t\tgetLogoUrl() {\n\t\t\tlet that = this;\n\t\t\tif (Cache.has(LOGO_URL)) {\n\t\t\t\tthis.logoUrl = Cache.get(LOGO_URL);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tgetLogo().then(res => {\n\t\t\t\tthat.logoUrl = res.data.logo_url;\n\t\t\t\tCache.set(LOGO_URL, that.logoUrl);\n\t\t\t});\n\t\t},\n\t\tclose() {\n\t\t\tlet pages = getCurrentPages(),\n\t\t\t\tcurrPage = pages[pages.length - 1];\n\t\t\tif (this.isGoIndex) {\n\t\t\t\tuni.navigateTo({ url: '/pages/index/index' });\n\t\t\t} else {\n\t\t\t\tthis.$emit('authColse', false);\n\t\t\t}\n\t\t\t// if (currPage && currPage.isShowAuth != undefined){\n\t\t\t// \tcurrPage.isShowAuth = true;\n\t\t\t// }\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.Popup {\n\twidth: 500rpx;\n\tbackground-color: #fff;\n\tposition: fixed;\n\ttop: 50%;\n\tleft: 50%;\n\tmargin-left: -250rpx;\n\ttransform: translateY(-50%);\n\tz-index: 320;\n}\n.Popup image {\n\twidth: 150rpx;\n\theight: 150rpx;\n\tmargin: -67rpx auto 0 auto;\n\tdisplay: block;\n\tborder: 8rpx solid #fff;\n\tborder-radius: 50%;\n}\n.Popup .title {\n\tfont-size: 28rpx;\n\tcolor: #000;\n\ttext-align: center;\n\tmargin-top: 30rpx;\n}\n.Popup .tip {\n\tfont-size: 22rpx;\n\tcolor: #555;\n\tpadding: 0 24rpx;\n\tmargin-top: 25rpx;\n}\n.Popup .bottom .item {\n\twidth: 50%;\n\theight: 80rpx;\n\tbackground-color: #eeeeee;\n\ttext-align: center;\n\tline-height: 80rpx;\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tmargin-top: 54rpx;\n}\n.Popup .bottom .item.on {\n\twidth: 100%;\n}\n.flex {\n\tdisplay: flex;\n}\n.Popup .bottom .item.grant {\n\tfont-size: 28rpx;\n\tcolor: #fff;\n\tfont-weight: bold;\n\tbackground-color: var(--view-theme);\n\tborder-radius: 0;\n\tpadding: 0;\n}\n.mask {\n\tposition: fixed;\n\ttop: 0;\n\tright: 0;\n\tleft: 0;\n\tbottom: 0;\n\tbackground-color: rgba(0, 0, 0, 0.65);\n\tz-index: 310;\n}\n</style>\n", "import Component from 'D:/uniapp/vue3/components/Authorize.vue'\nwx.createComponent(Component)"], "names": ["cacheConfig", "useUserStore", "<PERSON><PERSON>", "wx", "Routine", "res", "uni", "routineBindingPhone", "get<PERSON>ogo"], "mappings": ";;;;;;;AAqBA,MAAM,MAAM,OAAM;AAIlB,MAAM,EAAE,UAAU,cAAc,WAAW,YAAU,IAAMA,aAAAA;AAI3D,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA,IACN,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,WAAWC,YAAAA,aAAc;AAAA,MACzB,SAAS;AAAA,MACT,SAAS;AAAA;EAEV;AAAA,EACD,UAAU;AAAA,IACT,UAAU;AACT,aAAO,KAAK,UAAU;AAAA,IACtB;AAAA,IACD,WAAW;AACV,aAAO,KAAK,UAAU;AAAA,IACvB;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,QAAQ,GAAG;AACV,YAAM,QAAQ,KAAK,MAAM,aAAa,KAAK,QAAQ;AAAA,IACpD;AAAA,EACA;AAAA,EACD,UAAU;AACT,SAAK,WAAU;AAEf,QAAI,CAAC,KAAK,WAAW,CAACC,YAAAA,MAAM,IAAI,WAAW,GAAG;AAC7CC,oBAAAA,KAAG,MAAM;AAAA,QACR,QAAQ,KAAK;AACZD,sBAAK,MAAC,IAAI,aAAa,IAAI,MAAM,KAAK;AACzB,cAAI,WAAW,OAAO,IAAI,WAAW,OAAO;AAAA,QAiB1D;AAAA,MACD,CAAC;AAAA,WACK;AACN,WAAK,cAAa;AAAA,IACnB;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,gBAAgB;AACfE,mBAAAA,QAAQ,UAAU,EAChB,KAAK,SAAO;AACZ,YAAI,IAAI,YAAY;AAAO,eAAK,YAAW;AAAA;AACtC,eAAK,MAAM,aAAa,KAAK,QAAQ;AAAA,OAC1C,EACA,MAAM,SAAO;AACb,YAAI,KAAK;AAAQ,eAAK,MAAM,aAAa,IAAI;AAAA,MAC9C,CAAC;AAAA,IACF;AAAA,IACD,YAAY,MAAM;AACjBA,mBAAAA,QAAQ,YAAY,EAClB,KAAK,SAAO;AACZ,YAAI,WAAW,IAAI;AACnB,iBAAS,OAAO;AAChB,iBAAS,cAAc,IAAI,WAAW;AACtC,iBAAS,cAAc,IAAI,WAAW;AACtCA,qBAAO,QAAC,aAAa,QAAQ,EAC3B,KAAK,CAAAC,SAAO;AACZC,wBAAG,MAAC,YAAW;AACf,eAAK,MAAM,aAAa,KAAK;AAC7B,eAAK,MAAM,aAAa,KAAK,QAAQ;AAAA,SACrC,EACA,MAAM,CAAAD,SAAO;AACbC,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAOD,KAAI;AAAA,YACX,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AAAA,QACF,CAAC;AAAA,OACF,EACA,MAAM,SAAO;AACbC,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACF;AAAA,IACD,mBAAmB,eAAe,IAAI,MAAM;AAC3CC,qCAAoB;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA,MAAM,IAAI,WAAW;AAAA,QACrB,QAAQ,IAAI,WAAW;AAAA,OACvB,EACC,KAAK,SAAO;AAEZ,aAAK,UAAU,SAAS;AAAA,UACvB,OAAO,IAAI,KAAK;AAAA,UAChB,MAAM,IAAI,KAAK,eAAe,KAAK,OAAO,KAAK;AAAA,QAChD,CAAC;AACD,aAAK,MAAM,aAAa,KAAK;AAC7B,aAAK,MAAM,aAAa,IAAI,KAAK,QAAQ;AACzCD,sBAAG,MAAC,YAAW;AAAA,OACf,EACA,MAAM,SAAO;AACbA,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACF;AAAA,IACD,YAAY,GAAG;AACdA,oBAAAA,MAAI,YAAY,EAAE,OAAO,QAAS,CAAA;AAClCF,mBAAAA,QAAQ,QAAQ,EACd,KAAK,UAAQ;AACb,aAAK,mBAAmB,EAAE,OAAO,eAAe,EAAE,OAAO,IAAI,IAAI;AAAA,OACjE,EACA,MAAM,SAAO;AACbE,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IACF;AAAA,IACD,aAAa;AACZ,UAAI,OAAO;AACX,UAAIJ,YAAK,MAAC,IAAI,QAAQ,GAAG;AACxB,aAAK,UAAUA,YAAAA,MAAM,IAAI,QAAQ;AACjC;AAAA,MACD;AACAM,yBAAS,EAAC,KAAK,SAAO;AACrB,aAAK,UAAU,IAAI,KAAK;AACxBN,oBAAAA,MAAM,IAAI,UAAU,KAAK,OAAO;AAAA,MACjC,CAAC;AAAA,IACD;AAAA,IACD,QAAQ;AACH,UAAA,QAAQ;AACA,YAAM,MAAM,SAAS,CAAC;AAClC,UAAI,KAAK,WAAW;AACnBI,sBAAAA,MAAI,WAAW,EAAE,KAAK,qBAAsB,CAAA;AAAA,aACtC;AACN,aAAK,MAAM,aAAa,KAAK;AAAA,MAC9B;AAAA,IAID;AAAA,EACD;AACD;;;;;;;;;;;;;;;AC3LA,GAAG,gBAAgB,SAAS;"}