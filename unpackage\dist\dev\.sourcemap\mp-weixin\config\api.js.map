{"version": 3, "file": "api.js", "sources": ["config/api.js"], "sourcesContent": ["// API接口定义\r\nconst api = {\r\n    // 笔记详情相关接口\r\n    dynamicDetailsUrl: 'note/details', // 获取笔记详情\r\n    commentListUrl: 'comment/list', // 获取评论列表\r\n    sonCommentUrl: 'comment/list/son', // 获取子评论列表\r\n    saveCommentUrl: 'comment/save', // 保存评论\r\n    delCommentUrl: 'comment/del', // 删除评论\r\n    delDynamicUrl: 'note/del', // 删除笔记\r\n    dynamicReasonUrl: 'note/report', // 举报笔记\r\n    likeDynamicUrl: 'note/like', // 点赞笔记\r\n    \r\n    // 圈子相关接口\r\n    circleDetailsUrl: 'circle/details', // 圈子详情\r\n    takeColumnUrl: 'circle/take', // 加入圈子\r\n    \r\n    // 动态推荐接口\r\n    dynamicRecommendUrl: 'dynamic/recommend', // 动态推荐\r\n    waterfallRecommendUrl: 'dynamic/waterfall', // 瀑布流推荐\r\n    \r\n    // 上传相关接口\r\n    uploadFileUrl: 'upload/image', // 上传文件\r\n  }\r\n  \r\n  export {\r\n    api\r\n  }\r\n  \r\n  export default {\r\n    api\r\n  }"], "names": [], "mappings": ";AACK,MAAC,MAAM;AAAA;AAAA,EAER,mBAAmB;AAAA;AAAA,EACnB,gBAAgB;AAAA;AAAA,EAChB,eAAe;AAAA;AAAA,EACf,gBAAgB;AAAA;AAAA,EAChB,eAAe;AAAA;AAAA,EACf,eAAe;AAAA;AAAA,EACf,kBAAkB;AAAA;AAAA,EAClB,gBAAgB;AAAA;AAAA;AAAA,EAGhB,kBAAkB;AAAA;AAAA,EAClB,eAAe;AAAA;AAAA;AAAA,EAGf,qBAAqB;AAAA;AAAA,EACrB,uBAAuB;AAAA;AAAA;AAAA,EAGvB,eAAe;AAAA;AAChB;AAMD,MAAe,QAAA;AAAA,EACb;AACJ;;;"}