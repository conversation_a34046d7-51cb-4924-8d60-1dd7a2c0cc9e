{"version": 3, "file": "waterfall.js", "sources": ["components/waterfall/waterfall.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy93YXRlcmZhbGwvd2F0ZXJmYWxsLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"waterfall-box\">\n    <!-- 左侧栏 -->\n    <view class=\"waterfall-item\">\n      <!-- 活动区域 -->\n      <view v-if=\"activity.length\" class=\"waterfall-activity\" style=\"height:556rpx\">\n        <swiper class=\"waterfall-activity-item\" circular autoplay>\n          <swiper-item v-for=\"(item, index) in activity\" :key=\"index\" :data-url=\"'activity/details?id=' + item.id\" @tap=\"toPages\">\n            <view class=\"waterfall-activity-item\">\n              <view class=\"waterfall-activity-img\">\n                <lazy-image :src=\"item.img\"></lazy-image>\n                <view class=\"zt df\">{{item.status_str || \"加载中\"}}</view>\n                <view class=\"xxbt\">\n                  <view class=\"waterfall-activity-name ohto\">{{item.name || \"活动名称加载中\"}}</view>\n                  <view class=\"waterfall-activity-tag df\">\n                    <image src=\"/static/img/wz.png\" style=\"margin-right:4rpx\"></image>\n                    <view class=\"ohto\">{{item.adds_name || \"活动地址加载中\"}}</view>\n                  </view>\n                </view>\n              </view>\n              <view class=\"waterfall-activity-tag df\">\n                <image src=\"/static/img/sj.png\" style=\"margin-right:4rpx\"></image>\n                <view class=\"ohto\">{{item.activity_time || \"活动时间加载中\"}}</view>\n              </view>\n              <view v-if=\"item.user_count\" class=\"waterfall-activity-group\">\n                <view v-for=\"(img, imgIndex) in item.avatar_list\" :key=\"imgIndex\" class=\"group-img\">\n                  <image :src=\"img\" mode=\"aspectFill\"></image>\n                </view>\n                <view class=\"group-tit\">{{item.user_count}}人已参加</view>\n              </view>\n              <view v-else style=\"margin-left:16rpx;font-size:20rpx\">{{item.browse}} 人想参加</view>\n              <view class=\"waterfall-activity-btn small df\">\n                <text>{{item.is_join ? \"查看详情\" : \"立即参加\"}}</text>\n                <image class=\"effect\" src=\"/static/img/z.png\" style=\"margin-left:16rpx\"></image>\n              </view>\n            </view>\n          </swiper-item>\n        </swiper>\n        <view class=\"waterfall-activity-btn big df\" data-url=\"activity/index?type=0\" @tap=\"toPages\">\n          <text>查看全部活动</text>\n        </view>\n      </view>\n      \n      <!-- 左侧笔记列表 -->\n      <view \n        v-for=\"(v, i) in leftList\" \n        :key=\"i\" \n        class=\"waterfall-note\" \n        :style=\"{'height': v.bgHigh + 'rpx'}\" \n        :data-url=\"(v.type == 2 || v.type == 3 || v.type == 4) ? 'note/video?id=' + v.id : 'note/details?id=' + v.id\" \n        @tap=\"toPages\"\n      >\n        <!-- 纯文本类型 -->\n        <view v-if=\"v.type == 1 && (!v.images || v.images.length === 0)\" class=\"waterfall-note-top text-only\" :style=\"{'height': v.high + 'rpx', 'max-height': v.high + 'rpx', 'overflow': 'hidden'}\">\n          <view class=\"waterfall-note-content ohto2 text-content\" :style=\"{'max-height': (v.high - 40) + 'rpx', '-webkit-line-clamp': 'unset'}\">{{v.content}}</view>\n        </view>\n        <!-- 图片、视频类型 -->\n        <view v-else-if=\"v.type != 1\" class=\"waterfall-note-top\" :style=\"{'height': v.high + 'rpx'}\">\n          <lazy-image \n            v-if=\"v.type == 2 || v.type == 3\" \n            :src=\"getMediaUrl(v)\"\n            :style=\"{'border-radius': '8rpx 8rpx 0 0'}\"\n          ></lazy-image>\n          <image v-if=\"v.type == 2 && getImageCount(v) > 1\" class=\"xxiv\" src=\"/static/img/i.png\"></image>\n          <image v-if=\"v.type == 3\" class=\"xxiv\" src=\"/static/img/v.png\"></image>\n          \n          <!-- 音频类型 -->\n          <view v-if=\"v.type == 4\" class=\"xxa\">\n            <image class=\"xxa-bg\" style=\"z-index:-2\" :src=\"v.audio_cover || (v.audio && v.audio.cover ? v.audio.cover : '/static/img/audio_cover.png')\"></image>\n            <view class=\"xxa-mb\"></view>\n            <view class=\"xxa-top\">\n              <image class=\"xxa-top-img\" :src=\"v.audio_cover || (v.audio && v.audio.cover ? v.audio.cover : '/static/img/audio_cover.png')\"></image>\n              <image class=\"xxa-icon\" src=\"/static/img/yw.png\"></image>\n            </view>\n            <view class=\"xxa-t ohto\">{{v.audio_title || (v.audio && v.audio.name ? v.audio.name : '音频')}}</view>\n            <view class=\"xxa-tt ohto\">{{v.audio_intro || (v.audio && v.audio.intro ? v.audio.intro : '点击播放音频')}}</view>\n            <view class=\"xxa-play df\">\n              <image src=\"/static/img/play.png\"></image>\n              <text>去播放</text>\n            </view>\n          </view>\n          \n          <!-- 置顶标记 -->\n          <view v-if=\"v.top || v.is_top\" class=\"xxzd df\">置顶</view>\n        </view>\n        \n        <!-- 内容部分 - 只对非纯文本且非音频类型显示 -->\n        <view v-if=\"(v.type != 1 && v.type != 4) || (v.images && v.images.length > 0)\" class=\"waterfall-note-content ohto2\" :class=\"{'wlc1': v.contentOne}\">{{v.content}}</view>\n        \n        <!-- 笔记底部 -->\n        <view class=\"waterfall-note-bottom df\">\n          <view class=\"waterfall-note-user df\">\n            <image :src=\"getUserAvatar(v)\" mode=\"aspectFill\"></image>\n            <view class=\"ohto\">{{getUserName(v)}}</view>\n          </view>\n          <view class=\"waterfall-note-like df\" data-type=\"1\" :data-i=\"i\" @tap.stop=\"handleLike(v, i)\">\n            <image v-if=\"v.is_like\" class=\"hi\" src=\"/static/img/dz1.png\"></image>\n            <image v-else class=\"hi\" src=\"/static/img/dz.png\"></image>\n            <text>{{v.likes || v.like_count || v.like_count_str || 0}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 右侧栏 -->\n    <view class=\"waterfall-item\">\n      <!-- 右侧笔记列表 -->\n      <view \n        v-for=\"(v, i) in rightList\" \n        :key=\"i\" \n        class=\"waterfall-note\" \n        :style=\"{'height': v.bgHigh + 'rpx'}\" \n        :data-url=\"(v.type == 2 || v.type == 3 || v.type == 4) ? 'note/video?id=' + v.id : 'note/details?id=' + v.id\" \n        @tap=\"toPages\"\n      >\n        <!-- 纯文本类型 -->\n        <view v-if=\"v.type == 1 && (!v.images || v.images.length === 0)\" class=\"waterfall-note-top text-only\" :style=\"{'height': v.high + 'rpx', 'max-height': v.high + 'rpx', 'overflow': 'hidden'}\">\n          <view class=\"waterfall-note-content ohto2 text-content\" :style=\"{'max-height': (v.high - 40) + 'rpx', '-webkit-line-clamp': 'unset'}\">{{v.content}}</view>\n        </view>\n        <!-- 图片、视频类型 -->\n        <view v-else-if=\"v.type != 1\" class=\"waterfall-note-top\" :style=\"{'height': v.high + 'rpx'}\">\n          <lazy-image \n            v-if=\"v.type == 2 || v.type == 3\" \n            :src=\"getMediaUrl(v)\"\n            :style=\"{'border-radius': '8rpx 8rpx 0 0'}\"\n          ></lazy-image>\n          <image v-if=\"v.type == 2 && getImageCount(v) > 1\" class=\"xxiv\" src=\"/static/img/i.png\"></image>\n          <image v-if=\"v.type == 3\" class=\"xxiv\" src=\"/static/img/v.png\"></image>\n          \n          <!-- 音频类型 -->\n          <view v-if=\"v.type == 4\" class=\"xxa\">\n            <image class=\"xxa-bg\" style=\"z-index:-2\" :src=\"v.audio_cover || (v.audio && v.audio.cover ? v.audio.cover : '/static/img/audio_cover.png')\"></image>\n            <view class=\"xxa-mb\"></view>\n            <view class=\"xxa-top\">\n              <image class=\"xxa-top-img\" :src=\"v.audio_cover || (v.audio && v.audio.cover ? v.audio.cover : '/static/img/audio_cover.png')\"></image>\n              <image class=\"xxa-icon\" src=\"/static/img/yw.png\"></image>\n            </view>\n            <view class=\"xxa-t ohto\">{{v.audio_title || (v.audio && v.audio.name ? v.audio.name : '音频')}}</view>\n            <view class=\"xxa-tt ohto\">{{v.audio_intro || (v.audio && v.audio.intro ? v.audio.intro : '点击播放音频')}}</view>\n            <view class=\"xxa-play df\">\n              <image src=\"/static/img/play.png\"></image>\n              <text>去播放</text>\n            </view>\n          </view>\n          \n          <!-- 置顶标记 -->\n          <view v-if=\"v.top || v.is_top\" class=\"xxzd df\">置顶</view>\n        </view>\n        \n        <!-- 内容部分 - 只对非纯文本且非音频类型显示 -->\n        <view v-if=\"(v.type != 1 && v.type != 4) || (v.images && v.images.length > 0)\" class=\"waterfall-note-content ohto2\" :class=\"{'wlc1': v.contentOne}\">{{v.content}}</view>\n        \n        <!-- 笔记底部 -->\n        <view class=\"waterfall-note-bottom df\">\n          <view class=\"waterfall-note-user df\">\n            <image :src=\"getUserAvatar(v)\" mode=\"aspectFill\"></image>\n            <view class=\"ohto\">{{getUserName(v)}}</view>\n          </view>\n          <view class=\"waterfall-note-like df\" data-type=\"2\" :data-i=\"i\" @tap.stop=\"handleLike(v, i)\">\n            <image v-if=\"v.is_like\" class=\"hi\" src=\"/static/img/dz1.png\"></image>\n            <image v-else class=\"hi\" src=\"/static/img/dz.png\"></image>\n            <text>{{v.likes || v.like_count || v.like_count_str || 0}}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport lazyImage from '@/components/lazyImage/lazyImage.vue'\nimport { likeDynamic } from '@/api/social.js'\n\nexport default {\n  name: 'waterfall',\n  components: {\n    lazyImage\n  },\n  props: {\n    page: {\n      type: Number,\n      default: 1\n    },\n    activity: {\n      type: Array,\n      default: () => [],\n      required: false\n    },\n    note: {\n      type: Array,\n      default: () => [],\n      required: false\n    }\n  },\n  data() {\n    return {\n      left: 0,\n      right: 0,\n      leftList: [],\n      rightList: [],\n      isNoteVideo: true,\n      _lastProcessedLength: 0,\n\n      // 性能优化相关\n      renderLeftItems: 10,      // 左侧渲染数量\n      renderRightItems: 10,     // 右侧渲染数量\n      renderStep: 5,            // 每次增加渲染数量\n      maxRenderItems: 50,       // 最大渲染数量\n\n      // 缓存优化\n      processedItems: new Map(), // 已处理项目缓存\n      layoutCache: new Map(),    // 布局计算缓存\n\n      // 防抖定时器\n      layoutTimer: null,\n\n      // 性能监控\n      performanceMetrics: {\n        layoutTime: 0,\n        renderTime: 0,\n        lastUpdate: 0\n      }\n    }\n  },\n  created() {\n    // 尝试从全局获取状态\n    try {\n      if (typeof getApp === 'function') {\n        const app = getApp()\n        if (app && app.globalData) {\n          this.isNoteVideo = app.globalData.isNoteVideo !== undefined ? app.globalData.isNoteVideo : true\n        }\n      }\n    } catch (e) {\n      console.log('获取配置失败', e)\n    }\n  },\n  watch: {\n    note: {\n      handler(newVal, oldVal) {\n        if (!newVal || newVal.length === 0) {\n          this.clearLayout();\n          return;\n        }\n\n        // 防抖处理，避免频繁重新布局\n        if (this.layoutTimer) {\n          clearTimeout(this.layoutTimer);\n        }\n\n        this.layoutTimer = setTimeout(() => {\n          this.handleNoteChange(newVal, oldVal);\n        }, 100);\n      },\n      immediate: false,\n      deep: false // 不需要深度监听，只监听数组引用变化\n    }\n  },\n\n  computed: {\n    // 虚拟滚动 - 左侧可见列表\n    visibleLeftList() {\n      return this.leftList.slice(0, this.renderLeftItems);\n    },\n\n    // 虚拟滚动 - 右侧可见列表\n    visibleRightList() {\n      return this.rightList.slice(0, this.renderRightItems);\n    },\n\n    // 性能指标\n    performanceInfo() {\n      return {\n        totalItems: this.leftList.length + this.rightList.length,\n        renderedItems: this.renderLeftItems + this.renderRightItems,\n        cacheSize: this.layoutCache.size,\n        lastLayoutTime: this.performanceMetrics.layoutTime\n      };\n    }\n  },\n\n  mounted() {\n    // 检查初始数据\n    if (this.note && this.note.length > 0) {\n      this.xinxuan(this.note);\n    }\n  },\n\n  methods: {\n    // 处理note数据变化\n    handleNoteChange(newVal, oldVal) {\n      const startTime = Date.now();\n\n      try {\n        // 检查是否有新数据需要处理\n        const needProcess = this.page === 1 ||\n                            !this._lastProcessedLength ||\n                            newVal.length > this._lastProcessedLength;\n\n        if (!needProcess) {\n          return;\n        }\n\n        // 处理数据\n        this.xinxuan(newVal);\n\n        // 记录性能指标\n        this.performanceMetrics.layoutTime = Date.now() - startTime;\n        this.performanceMetrics.lastUpdate = Date.now();\n\n      } catch (error) {\n        console.error('瀑布流布局处理失败:', error);\n        this.handleLayoutError(error);\n      }\n    },\n\n    // 清空布局\n    clearLayout() {\n      this.left = 0;\n      this.right = 0;\n      this.leftList = [];\n      this.rightList = [];\n      this._lastProcessedLength = 0;\n      this.processedItems.clear();\n      this.layoutCache.clear();\n    },\n\n    // 处理布局错误\n    handleLayoutError(error) {\n      console.error('瀑布流布局错误:', error);\n      // 重置为安全状态\n      this.clearLayout();\n    },\n\n    // 获取缓存的布局信息\n    getCachedLayout(item) {\n      const cacheKey = `${item.id}_${item.type}_${item.content?.length || 0}`;\n      return this.layoutCache.get(cacheKey);\n    },\n\n    // 缓存布局信息\n    setCachedLayout(item, layout) {\n      const cacheKey = `${item.id}_${item.type}_${item.content?.length || 0}`;\n      this.layoutCache.set(cacheKey, layout);\n\n      // 限制缓存大小\n      if (this.layoutCache.size > 200) {\n        const firstKey = this.layoutCache.keys().next().value;\n        this.layoutCache.delete(firstKey);\n      }\n    },\n    // 获取媒体URL - 根据内容类型返回合适的URL\n    getMediaUrl(item) {\n      if (!item) return '';\n      \n      // 图片类型 (type=2 或旧数据中的type=1)\n      if (item.type === 2 || item.type === 1) {\n        // 优先使用imgs字段\n        if (item.imgs?.length) {\n          return typeof item.imgs[0] === 'string' ? item.imgs[0] : (item.imgs[0].url || '');\n        }\n        \n        // 处理旧版数据结构中的img对象\n        if (item.img?.url) {\n          return item.img.url;\n        }\n        \n        // 处理images字段\n        if (item.images) {\n          if (typeof item.images === 'string') {\n            try {\n              const images = JSON.parse(item.images);\n              return Array.isArray(images) && images.length > 0 ? images[0] : '';\n            } catch (e) {\n              return item.images;\n            }\n          } else if (Array.isArray(item.images) && item.images.length > 0) {\n            return typeof item.images[0] === 'string' ? item.images[0] : (item.images[0].url || '');\n          }\n        }\n      } \n      // 视频类型 (type=3 或旧数据中的type=2)\n      else if (item.type === 3 || item.type === 2) {\n        // 优先使用视频封面\n        if (item.video_cover) {\n          return item.video_cover;\n        }\n        // 如果是对象形式的video\n        if (item.video?.cover) {\n          return item.video.cover;\n        }\n        // 如果是字符串形式且有图片，使用第一张图片作为封面\n        if (item.images?.length) {\n          return typeof item.images[0] === 'string' ? item.images[0] : (item.images[0].url || '');\n        }\n      }\n      // 音频类型 (type=4 或旧数据中的type=3)\n      else if (item.type === 4 || item.type === 3) {\n        // 优先使用音频封面\n        if (item.audio_cover) {\n          return item.audio_cover;\n        }\n        // 如果是对象形式的audio\n        if (item.audio?.cover) {\n          return item.audio.cover;\n        }\n      }\n      \n      return '';\n    },\n    \n    // 获取图片数量\n    getImageCount(item) {\n      if (!item) return 0;\n      \n      // 首先检查imgs字段\n      if (item.imgs?.length) {\n        return item.imgs.length;\n      }\n      \n      // 兼容旧版数据结构 img_count\n      if (item.img_count !== undefined) {\n        return parseInt(item.img_count) || 0;\n      }\n      \n      // 检查images字段\n      if (item.images) {\n        if (Array.isArray(item.images)) {\n          return item.images.length;\n        }\n        \n        if (typeof item.images === 'string') {\n          try {\n            const images = JSON.parse(item.images);\n            return Array.isArray(images) ? images.length : 0;\n          } catch (e) {\n            return item.images.trim() ? 1 : 0;\n          }\n        }\n      }\n      \n      return 0;\n    },\n    \n    // 获取媒体尺寸信息，返回{width, height, ratio}\n    getMediaDimensions(item) {\n      if (!item) return { width: 0, height: 0, ratio: 1 };\n      \n      let width = 0, height = 0, ratio = 1;\n      \n      // 1. 旧数据结构: 图片类型的img对象\n      if (item.img?.wide && item.img?.high && item.img.high > 0) {\n        width = item.img.wide;\n        height = item.img.high;\n        ratio = width / height;\n      }\n      // 2. 旧数据结构: 视频对象\n      else if (item.video?.wide && item.video?.high && item.video.high > 0) {\n        width = item.video.wide;\n        height = item.video.high;\n        ratio = width / height;\n      }\n      // 3. 新数据结构: imgs数组的第一个元素\n      else if (item.imgs?.[0]?.wide && item.imgs?.[0]?.high && item.imgs[0].high > 0) {\n        width = item.imgs[0].wide;\n        height = item.imgs[0].high;\n          ratio = width / height;\n        }\n      // 4. 新数据结构: 图片宽高信息\n      else if (item.image_width && item.image_height && item.image_height > 0) {\n        width = item.image_width;\n        height = item.image_height;\n        ratio = width / height;\n      }\n      // 5. 新数据结构: 视频宽高信息\n      else if (item.video_width && item.video_height && item.video_height > 0) {\n        width = item.video_width;\n        height = item.video_height;\n        ratio = width / height;\n      }\n      \n      // 默认使用1:1比例\n      if (ratio <= 0) {\n        ratio = 1;\n      }\n      \n      return { width, height, ratio };\n    },\n    \n    // 优化后的瀑布流布局计算\n    xinxuan(noteList) {\n      const startTime = Date.now();\n\n      // 第一页重置布局\n      if (this.page == 1) {\n        this.clearLayout();\n        if (this.activity.length) {\n          this.left = 556;\n        }\n      } else {\n        // 加载更多时只处理新数据\n        let existCount = this.leftList.length + this.rightList.length;\n        let newCount = noteList.length - existCount;\n        if (newCount <= 0) {\n          return;\n        }\n        noteList = noteList.slice(-newCount);\n      }\n\n      // 批量处理，提高性能\n      this.batchProcessItems(noteList);\n\n      // 更新渲染数量\n      this.updateRenderCounts();\n\n      // 记录处理时间\n      const processTime = Date.now() - startTime;\n      this.performanceMetrics.layoutTime = processTime;\n\n      if (processTime > 100) {\n        console.warn('瀑布流布局计算较慢:', processTime + 'ms');\n      }\n    },\n\n    // 批量处理项目\n    batchProcessItems(noteList) {\n      // 使用requestAnimationFrame分批处理，避免阻塞UI\n      const batchSize = 10;\n      let currentIndex = 0;\n\n      const processBatch = () => {\n        const endIndex = Math.min(currentIndex + batchSize, noteList.length);\n\n        for (let i = currentIndex; i < endIndex; i++) {\n          this.processItem(noteList[i], i);\n        }\n\n        currentIndex = endIndex;\n\n        if (currentIndex < noteList.length) {\n          // 继续处理下一批\n          this.$nextTick(processBatch);\n        } else {\n          // 处理完成，更新记录\n          this._lastProcessedLength = this.leftList.length + this.rightList.length;\n        }\n      };\n\n      processBatch();\n    },\n\n    // 处理单个项目\n    processItem(originalItem, index) {\n      // 检查缓存\n      const cached = this.getCachedLayout(originalItem);\n      if (cached) {\n        this.addItemToColumn(cached);\n        return;\n      }\n\n      // 创建副本避免修改原始数据\n      let item = { ...originalItem };\n\n      // 计算布局\n      const layout = this.calculateItemLayout(item);\n\n      // 缓存结果\n      this.setCachedLayout(originalItem, layout);\n\n      // 添加到列\n      this.addItemToColumn(layout);\n    },\n\n    // 添加项目到列\n    addItemToColumn(item) {\n      // 决定放在左侧还是右侧 - 总是选择当前高度较小的一列\n      if (this.left <= this.right) {\n        this.left += item.bgHigh + 8; // 加上间距\n        this.leftList.push(item);\n      } else {\n        this.right += item.bgHigh + 8; // 加上间距\n        this.rightList.push(item);\n      }\n    },\n\n    // 更新渲染数量\n    updateRenderCounts() {\n      this.renderLeftItems = Math.min(this.leftList.length, this.maxRenderItems);\n      this.renderRightItems = Math.min(this.rightList.length, this.maxRenderItems);\n    },\n\n    // 计算项目布局\n    calculateItemLayout(item) {\n      // 瀑布流容器宽度 - 小程序设计宽度为750rpx，每列约占一半减去间距\n      const COLUMN_WIDTH = 359; // rpx\n\n      // 确保内容字段\n      if (!item.content) {\n        item.content = '';\n      }\n\n      // 处理数据类型映射 (兼容旧数据结构)\n      // 新版: 1=纯文本, 2=图片, 3=视频, 4=音频\n      // 旧版: 0=纯文本, 1=图片, 2=视频, 3=音频\n      if (item.type === 0) {\n        // 旧版纯文本转为新版纯文本\n        item.type = 1;\n        item.isTextOnly = true;\n      } else if (item.type === 1 && item.img) {\n        // 旧版图片转为新版图片\n        item.type = 2;\n      } else if (item.type === 2 && item.video) {\n        // 旧版视频转为新版视频\n        item.type = 3;\n      } else if (item.type === 3 && item.audio) {\n        // 旧版音频转为新版音频\n        item.type = 4;\n      }\n\n      // 如果type未定义，通过内容判断\n      if (!item.type) {\n        if (item.images && Array.isArray(item.images) && item.images.length > 0) {\n          item.type = 2; // 有图片，认为是图片类型\n        } else if (item.video || item.video_cover) {\n          item.type = 3; // 有视频，认为是视频类型\n        } else if (item.audio || item.audio_cover) {\n          item.type = 4; // 有音频，认为是音频类型\n        } else {\n          item.type = 1; // 默认为纯文本类型\n        }\n      }\n\n      // 计算内容总高度\n      let totalHeight = 0;\n      let contentHeight = 0;\n      let bottomHeight = 60; // 底部区域高度（用户头像和点赞）\n\n      // 根据内容类型计算主要内容区域高度\n      if (item.type === 1) {\n        // 纯文本类型\n        item.isTextOnly = true;\n\n        // 根据文本长度计算高度，参考效果图\n        const contentLength = item.content ? Array.from(item.content).length : 0;\n\n        // 根据字符数估算高度\n        if (contentLength <= 30) {\n          contentHeight = 120; // 短文本\n        } else if (contentLength <= 100) {\n          contentHeight = 180; // 中等文本\n        } else {\n          contentHeight = 280; // 长文本，可以显示多行\n        }\n\n        // 保存高度\n        item.high = contentHeight;\n      }\n      else if (item.type === 4) {\n        // 音频类型 - 固定高度\n        contentHeight = 374; // 根据原始组件中音频卡片高度设置\n        item.high = contentHeight;\n        item.contentOne = true; // 标记不显示额外内容\n      }\n      else if (item.type === 2 || item.type === 3) {\n        // 图片或视频类型\n        const dimensions = this.getMediaDimensions(item);\n\n        // 基于宽度计算高度，保持原始比例\n        const ratio = dimensions.ratio || 1;\n\n        if (ratio >= 1.5) {\n          // 横向图片/视频 (宽/高 >= 1.5)\n          contentHeight = Math.round(COLUMN_WIDTH / ratio);\n          contentHeight = Math.max(contentHeight, 180); // 最小高度\n        } else if (ratio <= 0.7) {\n          // 纵向图片/视频 (宽/高 <= 0.7)\n          contentHeight = Math.round(COLUMN_WIDTH / ratio);\n          contentHeight = Math.min(contentHeight, 400); // 最大高度限制\n        } else {\n          // 接近方形的图片/视频\n          contentHeight = COLUMN_WIDTH;\n        }\n\n        // 保存媒体区域高度\n        item.high = contentHeight;\n      }\n\n      // 计算文本内容额外高度 (仅适用于图片/视频类型)\n      let extraContentHeight = 0;\n      if ((item.type === 2 || item.type === 3) && item.content) {\n        const contentLength = Array.from(item.content).length;\n\n        if (contentLength <= 15) {\n          // 内容很短，只占一行\n          extraContentHeight = 40;\n          item.contentOne = true;\n        } else if (contentLength <= 50) {\n          // 中等长度，2-3行\n          extraContentHeight = 80;\n          item.contentOne = false;\n        } else {\n          // 较长内容，但在UI中通常会被截断\n          extraContentHeight = 100;\n          item.contentOne = false;\n        }\n      }\n\n      // 计算关联信息高度\n      let relatedInfoHeight = 0;\n      let hasRelatedInfo = false;\n\n      // 位置信息\n      if (item.location_name || item.adds_name) {\n        hasRelatedInfo = true;\n      }\n\n      // 话题信息\n      if ((item.topic_info && item.topic_info.length) ||\n          (item.topics && item.topics.length)) {\n        hasRelatedInfo = true;\n      }\n\n      // 商品信息\n      if (item.product_info || item.goods_info || item.goods || item.order_id) {\n        hasRelatedInfo = true;\n      }\n\n      // 活动信息\n      if (item.activity_id || (item.activity && item.activity.id)) {\n        hasRelatedInfo = true;\n      }\n\n      // 圈子信息\n      if (item.circle_id) {\n        hasRelatedInfo = true;\n      }\n\n      // 如果有关联信息，添加高度\n      if (hasRelatedInfo) {\n        relatedInfoHeight = 44; // 通常关联信息只占一行\n      }\n\n      // 计算总高度 = 内容高度 + 文本内容高度 + 关联信息高度 + 底部区域高度\n      totalHeight = contentHeight + extraContentHeight + relatedInfoHeight + bottomHeight;\n\n      // 保存计算结果\n      item.bgHigh = totalHeight;\n\n      // 返回处理后的项目\n      return item;\n    },\n\n    // 获取媒体尺寸信息\n    getMediaDimensions(item) {\n      let width = 0;\n      let height = 0;\n      let ratio = 1;\n\n      // 尝试从不同字段获取尺寸信息\n      if (item.image_width && item.image_height) {\n        width = item.image_width;\n        height = item.image_height;\n      } else if (item.video_width && item.video_height) {\n        width = item.video_width;\n        height = item.video_height;\n      } else if (item.video && item.video.wide && item.video.high) {\n        width = item.video.wide;\n        height = item.video.high;\n      } else if (item.imgs && item.imgs[0] && item.imgs[0].wide && item.imgs[0].high) {\n        width = item.imgs[0].wide;\n        height = item.imgs[0].high;\n      } else {\n        // 默认尺寸\n        width = 359;\n        height = 359;\n      }\n\n      if (width > 0 && height > 0) {\n        ratio = width / height;\n      }\n\n      return { width, height, ratio };\n    },\n    \n    /**\n     * 处理点赞事件\n     * @param {Object} item 动态项目\n     * @param {Number} index 索引\n     */\n    handleLike(item, index) {\n      // 判断用户是否登录\n      if (!this.$store.getters.isLogin) {\n        uni.navigateTo({\n          url: '/pages/login/index'\n        });\n        return;\n      }\n      \n      // 防止重复点击\n      if (item.isProcessing) return;\n      this.$set(item, 'isProcessing', true);\n      \n      const newLikeStatus = item.is_like !== 1;\n      const oldLikeCount = parseInt(item.likes || 0);\n      \n      // 先更新UI，提高用户体验\n      this.$set(item, 'is_like', newLikeStatus ? 1 : 0);\n      this.$set(item, 'likes', newLikeStatus ? oldLikeCount + 1 : Math.max(0, oldLikeCount - 1));\n      \n      // 调用点赞API\n      likeDynamic({\n        id: item.id,\n        is_like: newLikeStatus ? 1 : 0\n      }).then(res => {\n        // 点赞成功，通知父组件\n        this.$emit('likeback', {\n          index: index,\n          id: item.id,\n          isLike: newLikeStatus\n        });\n      }).catch(err => {\n        // 点赞失败，恢复原状态\n        this.$set(item, 'is_like', newLikeStatus ? 0 : 1);\n        this.$set(item, 'likes', oldLikeCount);\n        uni.showToast({\n          title: '操作失败，请重试',\n          icon: 'none'\n        });\n      }).finally(() => {\n        this.$set(item, 'isProcessing', false);\n      });\n    },\n    \n    // 页面跳转\n    toPages(e) {\n      const url = e.currentTarget.dataset.url;\n      uni.navigateTo({\n        url: '/pages/' + url\n      })\n    },\n    \n    // 获取用户头像\n    getUserAvatar(item) {\n      if (!item) return '/static/img/avatar.png';\n      \n      // 兼容旧版数据结构\n      if (item.user && item.user.avatar) {\n        return item.user.avatar;\n      }\n      \n      // 新版数据结构\n      if (item.avatar) {\n        return item.avatar;\n      } else if (item.user_info && item.user_info.avatar) {\n        return item.user_info.avatar;\n      }\n      \n      return '/static/img/avatar.png';\n    },\n    \n    // 获取用户名称\n    getUserName(item) {\n      if (!item) return '用户';\n      \n      // 兼容旧版数据结构\n      if (item.user && item.user.name) {\n        return item.user.name;\n      }\n      \n      // 新版数据结构\n      if (item.nickname) {\n        return item.nickname;\n      } else if (item.user && item.user.nickname) {\n        return item.user.nickname;\n      } else if (item.user_info && item.user_info.nickname) {\n        return item.user_info.nickname;\n      } else if (item.user_info && item.user_info.name) {\n        return item.user_info.name;\n      }\n      \n      return '用户';\n    }\n  }\n}\n</script>\n\n<style scoped>\n.waterfall-box {\n  width: 100%;\n  display: flex;\n  justify-content: space-between;\n  padding: 0 8rpx;\n  box-sizing: border-box;\n}\n\n.waterfall-item {\n  width: calc(50% - 4rpx);\n}\n\n.waterfall-note {\n  margin-bottom: 8rpx;\n  border-radius: 8rpx;\n  overflow: hidden;\n  background: #fff;\n  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.03);\n}\n\n.waterfall-note .waterfall-note-top {\n  width: 100%;\n  position: relative;\n  overflow: hidden;\n  background: #f8f8f8;\n}\n\n.waterfall-note-top.text-only {\n  background: #f8f8f8;\n  padding: 16rpx;\n  box-sizing: border-box;\n  width: 100%;\n  border-radius: 8rpx 8rpx 0 0;\n  display: flex;\n  flex-direction: column;\n}\n\n.text-content {\n  margin: 0 !important;\n  font-size: 26rpx;\n  line-height: 36rpx;\n  color: #333;\n  word-break: break-word;\n  white-space: pre-wrap;\n}\n\n.waterfall-note-top .xxiv,\n.waterfall-note-top .xxa .xxa-icon {\n  filter: drop-shadow(0 2rpx 2rpx rgba(0, 0, 0, 0.2));\n}\n\n.waterfall-note-top .xxiv {\n  position: absolute;\n  top: 20rpx;\n  right: 20rpx;\n  width: 28rpx;\n  height: 28rpx;\n}\n\n.waterfall-note-top .xxa {\n  width: calc(100% - 64rpx);\n  height: calc(100% - 64rpx);\n  padding: 32rpx;\n  position: relative;\n  z-index: 1;\n  color: #fff;\n}\n\n.waterfall-note-top .xxa .xxa-bg {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -2;\n  object-fit: cover;\n}\n\n.waterfall-note-top .xxa .xxa-mb {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -1;\n  -webkit-backdrop-filter: saturate(150%) blur(25px);\n  backdrop-filter: saturate(150%) blur(25px);\n  background: rgba(0, 0, 0, 0.5);\n}\n\n.waterfall-note-top .xxa .xxa-top {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 16rpx;\n  position: relative;\n  overflow: hidden;\n}\n\n.waterfall-note-top .xxa .xxa-top-img {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  top: 0;\n  left: 0;\n  object-fit: cover;\n}\n\n.waterfall-note-top .xxa .xxa-icon {\n  position: absolute;\n  top: 35rpx;\n  right: 35rpx;\n  bottom: 35rpx;\n  left: 35rpx;\n  width: 50rpx;\n  height: 50rpx;\n}\n\n.waterfall-note-top .xxa .xxa-t {\n  margin-top: 32rpx;\n  font-size: 26rpx;\n  font-weight: 700;\n}\n\n.waterfall-note-top .xxa .xxa-tt {\n  margin: 8rpx 0 32rpx;\n  opacity: 0.8;\n  font-size: 20rpx;\n}\n\n.waterfall-note-top .xxa .xxa-play {\n  width: 100%;\n  height: 60rpx;\n  font-size: 18rpx;\n  font-weight: 700;\n  border-radius: 60rpx;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.15);\n}\n\n.waterfall-note-top .xxa .xxa-play image {\n  margin-right: 8rpx;\n  width: 16rpx;\n  height: 16rpx;\n}\n\n.waterfall-note-top .xxzd {\n  position: absolute;\n  top: 16rpx;\n  left: 16rpx;\n  width: 52rpx;\n  height: 32rpx;\n  color: #fff;\n  font-size: 16rpx;\n  justify-content: center;\n  background: rgba(0, 0, 0, 0.4);\n  border: 1px solid rgba(255, 255, 255, 0.16);\n  border-radius: 8rpx;\n}\n\n.waterfall-note .waterfall-note-content {\n  width: calc(100% - 32rpx);\n  margin: 12rpx 16rpx;\n  font-size: 26rpx;\n  line-height: 36rpx;\n  word-break: break-word;\n  white-space: pre-line;\n  color: #333;\n}\n\n.waterfall-note .waterfall-note-bottom {\n  width: calc(100% - 32rpx);\n  margin: 0 16rpx;\n  height: 60rpx;\n  justify-content: space-between;\n}\n\n.waterfall-note-bottom .waterfall-note-user {\n  display: flex;\n  align-items: center;\n}\n\n.waterfall-note-bottom .waterfall-note-user image {\n  width: 32rpx;\n  height: 32rpx;\n  border-radius: 50%;\n}\n\n.waterfall-note-bottom .waterfall-note-like image {\n  width: 28rpx;\n  height: 28rpx;\n}\n\n.waterfall-note .waterfall-note-top,\n.waterfall-note-bottom .waterfall-note-user image,\n.waterfall-activity .waterfall-activity-item,\n.waterfall-activity .big,\n.wlc10 {\n  background: #f8f8f8;\n}\n\n.waterfall-note-bottom .waterfall-note-user view,\n.waterfall-note-bottom .waterfall-note-like text {\n  margin-left: 8rpx;\n  line-height: 32rpx;\n}\n\n.waterfall-note-bottom .waterfall-note-user view {\n  color: #333;\n  max-width: 140rpx;\n  font-size: 20rpx;\n}\n\n.waterfall-note-bottom .waterfall-note-like text {\n  color: #999;\n  font-size: 20rpx;\n}\n\n.wlc1 {\n  -webkit-line-clamp: 1 !important;\n}\n\n.ohto {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.ohto2 {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 3;\n}\n\n.df {\n  display: flex;\n  align-items: center;\n}\n\n/* 活动区域样式调整 */\n.waterfall-activity {\n  border-radius: 8rpx;\n  overflow: hidden;\n  margin-bottom: 8rpx;\n}\n\n.waterfall-activity-item {\n  border-radius: 8rpx 8rpx 0 0;\n  overflow: hidden;\n}\n\n.waterfall-activity-img {\n  position: relative;\n  border-radius: 8rpx 8rpx 0 0;\n  overflow: hidden;\n}\n\n.waterfall-activity .big {\n  height: 72rpx;\n  border-radius: 0 0 8rpx 8rpx;\n  font-size: 24rpx;\n  font-weight: 500;\n  color: #333;\n  justify-content: center;\n}\n\n/* 图片类卡片的图片样式 */\n.lazy-image {\n  border-radius: 8rpx 8rpx 0 0;\n  overflow: hidden;\n}\n\n/* 活动区域更详细样式（从原始组件复制） */\n.waterfall-activity .waterfall-activity-item {\n  height: 470rpx;\n  overflow: hidden;\n}\n\n.waterfall-activity-item .waterfall-activity-img {\n  margin-bottom: 16rpx;\n  width: 100%;\n  height: 290rpx;\n  position: relative;\n}\n\n.waterfall-activity-img .zt {\n  position: absolute;\n  top: 16rpx;\n  left: 16rpx;\n  width: 68rpx;\n  height: 38rpx;\n  color: #fff;\n  font-size: 16rpx;\n  font-weight: 700;\n  background: rgba(0, 0, 0, 0.4);\n  border: 1px solid rgba(255, 255, 255, 0.16);\n  border-radius: 8rpx;\n  justify-content: center;\n}\n\n.waterfall-activity-img .xxbt {\n  position: absolute;\n  left: 0;\n  bottom: 0;\n  width: 100%;\n  padding: 24rpx 0 8rpx;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));\n}\n\n.waterfall-activity-img .waterfall-activity-name,\n.waterfall-activity-item .waterfall-activity-tag {\n  width: calc(100% - 32rpx);\n  padding: 0 16rpx 8rpx;\n}\n\n.waterfall-activity-img .waterfall-activity-name {\n  color: #fff;\n  font-size: 24rpx;\n  font-weight: 700;\n}\n\n.waterfall-activity-item .waterfall-activity-tag image,\n.waterfall-activity .waterfall-activity-btn image {\n  width: 20rpx;\n  height: 20rpx;\n}\n\n.waterfall-activity-item .waterfall-activity-tag view {\n  width: calc(100% - 28rpx);\n  color: #999;\n  font-size: 20rpx;\n  font-weight: 500;\n}\n\n.waterfall-activity-group {\n  margin-left: 31rpx;\n  direction: ltr;\n  unicode-bidi: bidi-override;\n  display: inline-block;\n}\n\n.waterfall-activity-group .group-img {\n  width: 32rpx;\n  height: 32rpx;\n  display: inline-flex;\n  position: relative;\n  margin-left: -16rpx;\n  border: 2rpx solid #f8f8f8;\n  background: #fff;\n  vertical-align: middle;\n  border-radius: 50%;\n}\n\n.waterfall-activity-group .group-img image {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n}\n\n.waterfall-activity-group .group-tit {\n  display: inline-flex;\n  margin-left: 8rpx;\n  color: #999;\n  font-size: 20rpx;\n  font-weight: 500;\n}\n\n.waterfall-activity .waterfall-activity-btn {\n  margin-top: 8rpx;\n  font-weight: 700;\n  justify-content: center;\n}\n\n.waterfall-activity .big {\n  width: 100%;\n  font-size: 22rpx;\n  height: 70rpx;\n}\n\n.waterfall-activity .small {\n  position: absolute;\n  left: 16rpx;\n  bottom: 16rpx;\n  font-size: 20rpx;\n  width: calc(100% - 32rpx);\n  height: 60rpx;\n  background: #fff;\n}\n</style> ", "import Component from 'D:/uniapp/vue3/components/waterfall/waterfall.vue'\nwx.createComponent(Component)"], "names": ["uni", "likeDynamic"], "mappings": ";;;;AA0KA,MAAK,YAAa,MAAW;AAG7B,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAM,CAAE;AAAA,MACjB,UAAU;AAAA,IACX;AAAA,IACD,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAM,CAAE;AAAA,MACjB,UAAU;AAAA,IACZ;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,CAAE;AAAA,MACZ,WAAW,CAAE;AAAA,MACb,aAAa;AAAA,MACb,sBAAsB;AAAA;AAAA,MAGtB,iBAAiB;AAAA;AAAA,MACjB,kBAAkB;AAAA;AAAA,MAClB,YAAY;AAAA;AAAA,MACZ,gBAAgB;AAAA;AAAA;AAAA,MAGhB,gBAAgB,oBAAI,IAAK;AAAA;AAAA,MACzB,aAAa,oBAAI,IAAK;AAAA;AAAA;AAAA,MAGtB,aAAa;AAAA;AAAA,MAGb,oBAAoB;AAAA,QAClB,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAER,QAAI;AACF,UAAI,OAAO,WAAW,YAAY;AAChC,cAAM,MAAM,OAAO;AACnB,YAAI,OAAO,IAAI,YAAY;AACzB,eAAK,cAAc,IAAI,WAAW,gBAAgB,SAAY,IAAI,WAAW,cAAc;AAAA,QAC7F;AAAA,MACF;AAAA,IACF,SAAS,GAAG;AACVA,oBAAAA,gEAAY,UAAU,CAAC;AAAA,IACzB;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,QAAQ,QAAQ,QAAQ;AACtB,YAAI,CAAC,UAAU,OAAO,WAAW,GAAG;AAClC,eAAK,YAAW;AAChB;AAAA,QACF;AAGA,YAAI,KAAK,aAAa;AACpB,uBAAa,KAAK,WAAW;AAAA,QAC/B;AAEA,aAAK,cAAc,WAAW,MAAM;AAClC,eAAK,iBAAiB,QAAQ,MAAM;AAAA,QACrC,GAAE,GAAG;AAAA,MACP;AAAA,MACD,WAAW;AAAA,MACX,MAAM;AAAA;AAAA,IACR;AAAA,EACD;AAAA,EAED,UAAU;AAAA;AAAA,IAER,kBAAkB;AAChB,aAAO,KAAK,SAAS,MAAM,GAAG,KAAK,eAAe;AAAA,IACnD;AAAA;AAAA,IAGD,mBAAmB;AACjB,aAAO,KAAK,UAAU,MAAM,GAAG,KAAK,gBAAgB;AAAA,IACrD;AAAA;AAAA,IAGD,kBAAkB;AAChB,aAAO;AAAA,QACL,YAAY,KAAK,SAAS,SAAS,KAAK,UAAU;AAAA,QAClD,eAAe,KAAK,kBAAkB,KAAK;AAAA,QAC3C,WAAW,KAAK,YAAY;AAAA,QAC5B,gBAAgB,KAAK,mBAAmB;AAAA;IAE5C;AAAA,EACD;AAAA,EAED,UAAU;AAER,QAAI,KAAK,QAAQ,KAAK,KAAK,SAAS,GAAG;AACrC,WAAK,QAAQ,KAAK,IAAI;AAAA,IACxB;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,iBAAiB,QAAQ,QAAQ;AAC/B,YAAM,YAAY,KAAK;AAEvB,UAAI;AAEF,cAAM,cAAc,KAAK,SAAS,KACd,CAAC,KAAK,wBACN,OAAO,SAAS,KAAK;AAEzC,YAAI,CAAC,aAAa;AAChB;AAAA,QACF;AAGA,aAAK,QAAQ,MAAM;AAGnB,aAAK,mBAAmB,aAAa,KAAK,IAAG,IAAK;AAClD,aAAK,mBAAmB,aAAa,KAAK,IAAG;AAAA,MAE7C,SAAO,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,6CAAc,cAAc,KAAK;AACjC,aAAK,kBAAkB,KAAK;AAAA,MAC9B;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACZ,WAAK,OAAO;AACZ,WAAK,QAAQ;AACb,WAAK,WAAW;AAChB,WAAK,YAAY;AACjB,WAAK,uBAAuB;AAC5B,WAAK,eAAe;AACpB,WAAK,YAAY;IAClB;AAAA;AAAA,IAGD,kBAAkB,OAAO;AACvBA,sFAAc,YAAY,KAAK;AAE/B,WAAK,YAAW;AAAA,IACjB;AAAA;AAAA,IAGD,gBAAgB,MAAM;;AACpB,YAAM,WAAW,GAAG,KAAK,EAAE,IAAI,KAAK,IAAI,MAAI,UAAK,YAAL,mBAAc,WAAU,CAAC;AACrE,aAAO,KAAK,YAAY,IAAI,QAAQ;AAAA,IACrC;AAAA;AAAA,IAGD,gBAAgB,MAAM,QAAQ;;AAC5B,YAAM,WAAW,GAAG,KAAK,EAAE,IAAI,KAAK,IAAI,MAAI,UAAK,YAAL,mBAAc,WAAU,CAAC;AACrE,WAAK,YAAY,IAAI,UAAU,MAAM;AAGrC,UAAI,KAAK,YAAY,OAAO,KAAK;AAC/B,cAAM,WAAW,KAAK,YAAY,KAAI,EAAG,KAAM,EAAC;AAChD,aAAK,YAAY,OAAO,QAAQ;AAAA,MAClC;AAAA,IACD;AAAA;AAAA,IAED,YAAY,MAAM;;AAChB,UAAI,CAAC;AAAM,eAAO;AAGlB,UAAI,KAAK,SAAS,KAAK,KAAK,SAAS,GAAG;AAEtC,aAAI,UAAK,SAAL,mBAAW,QAAQ;AACrB,iBAAO,OAAO,KAAK,KAAK,CAAC,MAAM,WAAW,KAAK,KAAK,CAAC,IAAK,KAAK,KAAK,CAAC,EAAE,OAAO;AAAA,QAChF;AAGA,aAAI,UAAK,QAAL,mBAAU,KAAK;AACjB,iBAAO,KAAK,IAAI;AAAA,QAClB;AAGA,YAAI,KAAK,QAAQ;AACf,cAAI,OAAO,KAAK,WAAW,UAAU;AACnC,gBAAI;AACF,oBAAM,SAAS,KAAK,MAAM,KAAK,MAAM;AACrC,qBAAO,MAAM,QAAQ,MAAM,KAAK,OAAO,SAAS,IAAI,OAAO,CAAC,IAAI;AAAA,YAClE,SAAS,GAAG;AACV,qBAAO,KAAK;AAAA,YACd;AAAA,qBACS,MAAM,QAAQ,KAAK,MAAM,KAAK,KAAK,OAAO,SAAS,GAAG;AAC/D,mBAAO,OAAO,KAAK,OAAO,CAAC,MAAM,WAAW,KAAK,OAAO,CAAC,IAAK,KAAK,OAAO,CAAC,EAAE,OAAO;AAAA,UACtF;AAAA,QACF;AAAA,MACF,WAES,KAAK,SAAS,KAAK,KAAK,SAAS,GAAG;AAE3C,YAAI,KAAK,aAAa;AACpB,iBAAO,KAAK;AAAA,QACd;AAEA,aAAI,UAAK,UAAL,mBAAY,OAAO;AACrB,iBAAO,KAAK,MAAM;AAAA,QACpB;AAEA,aAAI,UAAK,WAAL,mBAAa,QAAQ;AACvB,iBAAO,OAAO,KAAK,OAAO,CAAC,MAAM,WAAW,KAAK,OAAO,CAAC,IAAK,KAAK,OAAO,CAAC,EAAE,OAAO;AAAA,QACtF;AAAA,MACF,WAES,KAAK,SAAS,KAAK,KAAK,SAAS,GAAG;AAE3C,YAAI,KAAK,aAAa;AACpB,iBAAO,KAAK;AAAA,QACd;AAEA,aAAI,UAAK,UAAL,mBAAY,OAAO;AACrB,iBAAO,KAAK,MAAM;AAAA,QACpB;AAAA,MACF;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,cAAc,MAAM;;AAClB,UAAI,CAAC;AAAM,eAAO;AAGlB,WAAI,UAAK,SAAL,mBAAW,QAAQ;AACrB,eAAO,KAAK,KAAK;AAAA,MACnB;AAGA,UAAI,KAAK,cAAc,QAAW;AAChC,eAAO,SAAS,KAAK,SAAS,KAAK;AAAA,MACrC;AAGA,UAAI,KAAK,QAAQ;AACf,YAAI,MAAM,QAAQ,KAAK,MAAM,GAAG;AAC9B,iBAAO,KAAK,OAAO;AAAA,QACrB;AAEA,YAAI,OAAO,KAAK,WAAW,UAAU;AACnC,cAAI;AACF,kBAAM,SAAS,KAAK,MAAM,KAAK,MAAM;AACrC,mBAAO,MAAM,QAAQ,MAAM,IAAI,OAAO,SAAS;AAAA,UACjD,SAAS,GAAG;AACV,mBAAO,KAAK,OAAO,KAAI,IAAK,IAAI;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,mBAAmB,MAAM;;AACvB,UAAI,CAAC;AAAM,eAAO,EAAE,OAAO,GAAG,QAAQ,GAAG,OAAO;AAEhD,UAAI,QAAQ,GAAG,SAAS,GAAG,QAAQ;AAGnC,YAAI,UAAK,QAAL,mBAAU,WAAQ,UAAK,QAAL,mBAAU,SAAQ,KAAK,IAAI,OAAO,GAAG;AACzD,gBAAQ,KAAK,IAAI;AACjB,iBAAS,KAAK,IAAI;AAClB,gBAAQ,QAAQ;AAAA,MAClB,aAES,UAAK,UAAL,mBAAY,WAAQ,UAAK,UAAL,mBAAY,SAAQ,KAAK,MAAM,OAAO,GAAG;AACpE,gBAAQ,KAAK,MAAM;AACnB,iBAAS,KAAK,MAAM;AACpB,gBAAQ,QAAQ;AAAA,MAClB,aAES,gBAAK,SAAL,mBAAY,OAAZ,mBAAgB,WAAQ,gBAAK,SAAL,mBAAY,OAAZ,mBAAgB,SAAQ,KAAK,KAAK,CAAC,EAAE,OAAO,GAAG;AAC9E,gBAAQ,KAAK,KAAK,CAAC,EAAE;AACrB,iBAAS,KAAK,KAAK,CAAC,EAAE;AACpB,gBAAQ,QAAQ;AAAA,MAClB,WAEO,KAAK,eAAe,KAAK,gBAAgB,KAAK,eAAe,GAAG;AACvE,gBAAQ,KAAK;AACb,iBAAS,KAAK;AACd,gBAAQ,QAAQ;AAAA,MAClB,WAES,KAAK,eAAe,KAAK,gBAAgB,KAAK,eAAe,GAAG;AACvE,gBAAQ,KAAK;AACb,iBAAS,KAAK;AACd,gBAAQ,QAAQ;AAAA,MAClB;AAGA,UAAI,SAAS,GAAG;AACd,gBAAQ;AAAA,MACV;AAEA,aAAO,EAAE,OAAO,QAAQ;IACzB;AAAA;AAAA,IAGD,QAAQ,UAAU;AAChB,YAAM,YAAY,KAAK;AAGvB,UAAI,KAAK,QAAQ,GAAG;AAClB,aAAK,YAAW;AAChB,YAAI,KAAK,SAAS,QAAQ;AACxB,eAAK,OAAO;AAAA,QACd;AAAA,aACK;AAEL,YAAI,aAAa,KAAK,SAAS,SAAS,KAAK,UAAU;AACvD,YAAI,WAAW,SAAS,SAAS;AACjC,YAAI,YAAY,GAAG;AACjB;AAAA,QACF;AACA,mBAAW,SAAS,MAAM,CAAC,QAAQ;AAAA,MACrC;AAGA,WAAK,kBAAkB,QAAQ;AAG/B,WAAK,mBAAkB;AAGvB,YAAM,cAAc,KAAK,IAAG,IAAK;AACjC,WAAK,mBAAmB,aAAa;AAErC,UAAI,cAAc,KAAK;AACrBA,sBAAa,MAAA,MAAA,QAAA,6CAAA,cAAc,cAAc,IAAI;AAAA,MAC/C;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,UAAU;AAE1B,YAAM,YAAY;AAClB,UAAI,eAAe;AAEnB,YAAM,eAAe,MAAM;AACzB,cAAM,WAAW,KAAK,IAAI,eAAe,WAAW,SAAS,MAAM;AAEnE,iBAAS,IAAI,cAAc,IAAI,UAAU,KAAK;AAC5C,eAAK,YAAY,SAAS,CAAC,GAAG,CAAC;AAAA,QACjC;AAEA,uBAAe;AAEf,YAAI,eAAe,SAAS,QAAQ;AAElC,eAAK,UAAU,YAAY;AAAA,eACtB;AAEL,eAAK,uBAAuB,KAAK,SAAS,SAAS,KAAK,UAAU;AAAA,QACpE;AAAA;AAGF;IACD;AAAA;AAAA,IAGD,YAAY,cAAc,OAAO;AAE/B,YAAM,SAAS,KAAK,gBAAgB,YAAY;AAChD,UAAI,QAAQ;AACV,aAAK,gBAAgB,MAAM;AAC3B;AAAA,MACF;AAGA,UAAI,OAAO,EAAE,GAAG;AAGhB,YAAM,SAAS,KAAK,oBAAoB,IAAI;AAG5C,WAAK,gBAAgB,cAAc,MAAM;AAGzC,WAAK,gBAAgB,MAAM;AAAA,IAC5B;AAAA;AAAA,IAGD,gBAAgB,MAAM;AAEpB,UAAI,KAAK,QAAQ,KAAK,OAAO;AAC3B,aAAK,QAAQ,KAAK,SAAS;AAC3B,aAAK,SAAS,KAAK,IAAI;AAAA,aAClB;AACL,aAAK,SAAS,KAAK,SAAS;AAC5B,aAAK,UAAU,KAAK,IAAI;AAAA,MAC1B;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,kBAAkB,KAAK,IAAI,KAAK,SAAS,QAAQ,KAAK,cAAc;AACzE,WAAK,mBAAmB,KAAK,IAAI,KAAK,UAAU,QAAQ,KAAK,cAAc;AAAA,IAC5E;AAAA;AAAA,IAGD,oBAAoB,MAAM;AAExB,YAAM,eAAe;AAGrB,UAAI,CAAC,KAAK,SAAS;AACjB,aAAK,UAAU;AAAA,MACjB;AAKA,UAAI,KAAK,SAAS,GAAG;AAEnB,aAAK,OAAO;AACZ,aAAK,aAAa;AAAA,MACpB,WAAW,KAAK,SAAS,KAAK,KAAK,KAAK;AAEtC,aAAK,OAAO;AAAA,MACd,WAAW,KAAK,SAAS,KAAK,KAAK,OAAO;AAExC,aAAK,OAAO;AAAA,MACd,WAAW,KAAK,SAAS,KAAK,KAAK,OAAO;AAExC,aAAK,OAAO;AAAA,MACd;AAGA,UAAI,CAAC,KAAK,MAAM;AACd,YAAI,KAAK,UAAU,MAAM,QAAQ,KAAK,MAAM,KAAK,KAAK,OAAO,SAAS,GAAG;AACvE,eAAK,OAAO;AAAA,QACd,WAAW,KAAK,SAAS,KAAK,aAAa;AACzC,eAAK,OAAO;AAAA,QACd,WAAW,KAAK,SAAS,KAAK,aAAa;AACzC,eAAK,OAAO;AAAA,eACP;AACL,eAAK,OAAO;AAAA,QACd;AAAA,MACF;AAGA,UAAI,cAAc;AAClB,UAAI,gBAAgB;AACpB,UAAI,eAAe;AAGnB,UAAI,KAAK,SAAS,GAAG;AAEnB,aAAK,aAAa;AAGlB,cAAM,gBAAgB,KAAK,UAAU,MAAM,KAAK,KAAK,OAAO,EAAE,SAAS;AAGvE,YAAI,iBAAiB,IAAI;AACvB,0BAAgB;AAAA,mBACP,iBAAiB,KAAK;AAC/B,0BAAgB;AAAA,eACX;AACL,0BAAgB;AAAA,QAClB;AAGA,aAAK,OAAO;AAAA,MACd,WACS,KAAK,SAAS,GAAG;AAExB,wBAAgB;AAChB,aAAK,OAAO;AACZ,aAAK,aAAa;AAAA,MACpB,WACS,KAAK,SAAS,KAAK,KAAK,SAAS,GAAG;AAE3C,cAAM,aAAa,KAAK,mBAAmB,IAAI;AAG/C,cAAM,QAAQ,WAAW,SAAS;AAElC,YAAI,SAAS,KAAK;AAEhB,0BAAgB,KAAK,MAAM,eAAe,KAAK;AAC/C,0BAAgB,KAAK,IAAI,eAAe,GAAG;AAAA,QAC7C,WAAW,SAAS,KAAK;AAEvB,0BAAgB,KAAK,MAAM,eAAe,KAAK;AAC/C,0BAAgB,KAAK,IAAI,eAAe,GAAG;AAAA,eACtC;AAEL,0BAAgB;AAAA,QAClB;AAGA,aAAK,OAAO;AAAA,MACd;AAGA,UAAI,qBAAqB;AACzB,WAAK,KAAK,SAAS,KAAK,KAAK,SAAS,MAAM,KAAK,SAAS;AACxD,cAAM,gBAAgB,MAAM,KAAK,KAAK,OAAO,EAAE;AAE/C,YAAI,iBAAiB,IAAI;AAEvB,+BAAqB;AACrB,eAAK,aAAa;AAAA,mBACT,iBAAiB,IAAI;AAE9B,+BAAqB;AACrB,eAAK,aAAa;AAAA,eACb;AAEL,+BAAqB;AACrB,eAAK,aAAa;AAAA,QACpB;AAAA,MACF;AAGA,UAAI,oBAAoB;AACxB,UAAI,iBAAiB;AAGrB,UAAI,KAAK,iBAAiB,KAAK,WAAW;AACxC,yBAAiB;AAAA,MACnB;AAGA,UAAK,KAAK,cAAc,KAAK,WAAW,UACnC,KAAK,UAAU,KAAK,OAAO,QAAS;AACvC,yBAAiB;AAAA,MACnB;AAGA,UAAI,KAAK,gBAAgB,KAAK,cAAc,KAAK,SAAS,KAAK,UAAU;AACvE,yBAAiB;AAAA,MACnB;AAGA,UAAI,KAAK,eAAgB,KAAK,YAAY,KAAK,SAAS,IAAK;AAC3D,yBAAiB;AAAA,MACnB;AAGA,UAAI,KAAK,WAAW;AAClB,yBAAiB;AAAA,MACnB;AAGA,UAAI,gBAAgB;AAClB,4BAAoB;AAAA,MACtB;AAGA,oBAAc,gBAAgB,qBAAqB,oBAAoB;AAGvE,WAAK,SAAS;AAGd,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,mBAAmB,MAAM;AACvB,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,UAAI,QAAQ;AAGZ,UAAI,KAAK,eAAe,KAAK,cAAc;AACzC,gBAAQ,KAAK;AACb,iBAAS,KAAK;AAAA,MAChB,WAAW,KAAK,eAAe,KAAK,cAAc;AAChD,gBAAQ,KAAK;AACb,iBAAS,KAAK;AAAA,iBACL,KAAK,SAAS,KAAK,MAAM,QAAQ,KAAK,MAAM,MAAM;AAC3D,gBAAQ,KAAK,MAAM;AACnB,iBAAS,KAAK,MAAM;AAAA,MACtB,WAAW,KAAK,QAAQ,KAAK,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE,QAAQ,KAAK,KAAK,CAAC,EAAE,MAAM;AAC9E,gBAAQ,KAAK,KAAK,CAAC,EAAE;AACrB,iBAAS,KAAK,KAAK,CAAC,EAAE;AAAA,aACjB;AAEL,gBAAQ;AACR,iBAAS;AAAA,MACX;AAEA,UAAI,QAAQ,KAAK,SAAS,GAAG;AAC3B,gBAAQ,QAAQ;AAAA,MAClB;AAEA,aAAO,EAAE,OAAO,QAAQ;IACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOD,WAAW,MAAM,OAAO;AAEtB,UAAI,CAAC,KAAK,OAAO,QAAQ,SAAS;AAChCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACP,CAAC;AACD;AAAA,MACF;AAGA,UAAI,KAAK;AAAc;AACvB,WAAK,KAAK,MAAM,gBAAgB,IAAI;AAEpC,YAAM,gBAAgB,KAAK,YAAY;AACvC,YAAM,eAAe,SAAS,KAAK,SAAS,CAAC;AAG7C,WAAK,KAAK,MAAM,WAAW,gBAAgB,IAAI,CAAC;AAChD,WAAK,KAAK,MAAM,SAAS,gBAAgB,eAAe,IAAI,KAAK,IAAI,GAAG,eAAe,CAAC,CAAC;AAGzFC,6BAAY;AAAA,QACV,IAAI,KAAK;AAAA,QACT,SAAS,gBAAgB,IAAI;AAAA,OAC9B,EAAE,KAAK,SAAO;AAEb,aAAK,MAAM,YAAY;AAAA,UACrB;AAAA,UACA,IAAI,KAAK;AAAA,UACT,QAAQ;AAAA,QACV,CAAC;AAAA,MACH,CAAC,EAAE,MAAM,SAAO;AAEd,aAAK,KAAK,MAAM,WAAW,gBAAgB,IAAI,CAAC;AAChD,aAAK,KAAK,MAAM,SAAS,YAAY;AACrCD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,CAAC,EAAE,QAAQ,MAAM;AACf,aAAK,KAAK,MAAM,gBAAgB,KAAK;AAAA,MACvC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,QAAQ,GAAG;AACT,YAAM,MAAM,EAAE,cAAc,QAAQ;AACpCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,YAAY;AAAA,OAClB;AAAA,IACF;AAAA;AAAA,IAGD,cAAc,MAAM;AAClB,UAAI,CAAC;AAAM,eAAO;AAGlB,UAAI,KAAK,QAAQ,KAAK,KAAK,QAAQ;AACjC,eAAO,KAAK,KAAK;AAAA,MACnB;AAGA,UAAI,KAAK,QAAQ;AACf,eAAO,KAAK;AAAA,MACZ,WAAS,KAAK,aAAa,KAAK,UAAU,QAAQ;AAClD,eAAO,KAAK,UAAU;AAAA,MACxB;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,YAAY,MAAM;AAChB,UAAI,CAAC;AAAM,eAAO;AAGlB,UAAI,KAAK,QAAQ,KAAK,KAAK,MAAM;AAC/B,eAAO,KAAK,KAAK;AAAA,MACnB;AAGA,UAAI,KAAK,UAAU;AACjB,eAAO,KAAK;AAAA,MACd,WAAW,KAAK,QAAQ,KAAK,KAAK,UAAU;AAC1C,eAAO,KAAK,KAAK;AAAA,MACjB,WAAS,KAAK,aAAa,KAAK,UAAU,UAAU;AACpD,eAAO,KAAK,UAAU;AAAA,MACxB,WAAW,KAAK,aAAa,KAAK,UAAU,MAAM;AAChD,eAAO,KAAK,UAAU;AAAA,MACxB;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACl3BA,GAAG,gBAAgB,SAAS;"}