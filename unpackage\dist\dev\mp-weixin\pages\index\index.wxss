
.container { 
  background: #ffffff; 
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}
.content-box {
  width: 100%;
  height: 100%;
  overflow-x: hidden; /* Prevent horizontal overflow */
  position: relative;
  z-index: 1;
  will-change: transform;
  padding-left: 10rpx;
  padding-right: 10rpx;
  box-sizing: border-box; /* Include padding in width calculation */
}
.content-scroll {
  height: 100%;
  -webkit-overflow-scrolling: touch;
}
.nav-box {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
  transform: translateZ(0);
  will-change: transform;
}
.bar-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
}
.bar-logo {
  height: 48rpx;
  width: 120rpx;
}
.bar-title { flex: 1; text-align: center; font-size: 34rpx; font-weight: bold; color: #222; letter-spacing: 2rpx;
}
.bar-icons {
  display: flex;
  align-items: center;
  gap: 32rpx;
}
.bar-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}
.bar-icon:active {
  background: #f0f0f0;
}
.shadow {
  box-shadow: 0 4rpx 24rpx rgba(45,156,250,0.10);
}
.top-section {
  display: flex;
  justify-content: space-between;
  margin: 10rpx 0 20rpx;
  padding: 0 5rpx;
}
.influencer-card { 
  background: #2d9cfa; 
  border-radius: 20rpx; 
  padding: 15rpx; 
  width: 42%; 
  color: #fff; 
  position: relative;
}
.avatar { width: 100rpx; height: 100rpx; border-radius: 20rpx;
}
.voice-time { position: absolute; left: 15rpx; top: 15rpx; background: #fff; color: #2d9cfa; border-radius: 20rpx; padding: 2rpx 10rpx; font-size: 22rpx;
}
.name { font-size: 32rpx; font-weight: bold; margin-top: 10rpx;
}
.desc { font-size: 24rpx; margin-top: 4rpx;
}
.right-func { 
  width: 52%;
  display: flex; 
  flex-direction: column; 
  justify-content: space-between;
}
.func-box {
  margin-bottom: 10rpx;
  padding: 12rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx rgba(45,156,250,0.10);
}
.func-title { font-size: 24rpx; font-weight: bold; margin-bottom: 6rpx;
}
.func-avatars { display: flex;
}
.func-avatar { width: 32rpx; height: 32rpx; border-radius: 50%; margin-right: 6rpx;
}
.func-status { font-size: 22rpx;
}
.closed { color: #b0b0b0;
}
.online { color: #ffb800;
}
.popular-section { margin: 20rpx 10rpx 30rpx;
}
.popular-title { display: flex; justify-content: space-between; align-items: center; font-size: 32rpx; font-weight: bold; margin-bottom: 16rpx;
}
.all-btn { color: #2d9cfa; font-size: 24rpx;
}

/* 圈子相关样式 */
.scroll-box{
  width:100%;
  white-space:nowrap;
  overflow:hidden;
  transition:height .45s ease-in-out
}
.circle-box{
  width: 100%;
  display: flex;
  padding: 30rpx 10rpx;
  box-sizing: border-box; /* Include padding in width calculation */
}
.circle-box .circle-item{
  flex-shrink:0
}
.circle-item .circle-item-top{
  margin:0 20rpx;
  width:116rpx;
  height:116rpx;
  border-radius:50%;
  background:#f8f8f8;
  border:2rpx solid #f5f5f5;
  position:relative
}
.circle-item-top image{
  width:100%;
  height:100%;
  border-radius:50%
}
.circle-item-top .icon{
  margin:34rpx;
  width:48rpx;
  height:48rpx
}
.circle-item-top .circle-item-tag{
  position:absolute;
  right:0;
  bottom:0;
  width:24rpx;
  height:24rpx;
  border-radius:50%;
  border:6rpx solid #fff
}
.circle-item .circle-name{
  margin: 20rpx 0 10rpx;
  width: 160rpx;
  color: #000;
  font-weight: 500;
  font-size: 24rpx;
  line-height: 24rpx;
  text-align: center;
  box-sizing: border-box; /* Include padding in width calculation */
}
.circle-item .circle-tips{
  width: 160rpx;
  color: #999;
  font-size: 18rpx;
  line-height: 18rpx;
  font-weight: 300;
  text-align: center;
  box-sizing: border-box; /* Include padding in width calculation */
}
.playwith-section { margin: 20rpx 10rpx 30rpx;
}
.playwith-title { font-size: 32rpx; font-weight: bold; margin-bottom: 16rpx;
}
.playwith-tabs {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}
.playwith-tab {
  flex: 1;
  text-align: center;
  font-size: 30rpx;
  color: #999;
  padding: 20rpx 0;
  font-weight: 500;
  position: relative;
  transition: all 0.3s ease;
}
.playwith-tab.active {
  color: #000;
  font-weight: 700;
  transform: scale(1.05);
}
.playwith-tab .active-line {
  position: absolute;
  bottom: -5rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 6rpx;
  background-color: #000;
  border-radius: 3rpx;
  transition: all 0.3s ease;
}
.playwith-tab-content {
  /* 可加切换动画 */
}

/* 个人卡片样式 */
.profile-card {
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx rgba(45,156,250,0.10);
  padding: 32rpx 24rpx 24rpx 24rpx;
  margin-bottom: 24rpx;
}
.profile-header {
  display: flex;
  align-items: flex-start;
  position: relative;
}
.profile-avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.profile-info {
  flex: 1;
}
.profile-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.profile-name {
  margin-right: 12rpx;
}
.profile-gender {
  font-size: 24rpx;
  color: #ff7eb3;
}
.profile-gender.female {
  color: #ff7eb3;
}
.profile-status-row {
  margin-top: 8rpx;
  display: flex;
  gap: 12rpx;
}
.profile-status {
  font-size: 22rpx;
  color: #888;
  background: #f5f6fa;
  border-radius: 8rpx;
  padding: 2rpx 12rpx;
}
.profile-status.online {
  color: #2ecc71;
  background: #eaffea;
}
.profile-status.city {
  color: #2d9cfa;
  background: #eaf6ff;
}
.profile-tags {
  margin-top: 8rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}
.profile-tag {
  font-size: 20rpx;
  color: #666;
  background: #f5f6fa;
  border-radius: 8rpx;
  padding: 2rpx 10rpx;
}
.profile-hi-btn {
  position: absolute;
  right: 0;
  top: 0;
  background: linear-gradient(90deg,#5b8cff,#b36fff);
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  border-radius: 16rpx;
  padding: 8rpx 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(91,140,255,0.10);
}
.profile-voice {
  margin-top: 20rpx;
}
.voice-btn {
  display: flex;
  align-items: center;
  background: #f5f6fa;
  border-radius: 20rpx;
  width: 90rpx;
  padding: 6rpx 12rpx;
  color: #222;
  font-size: 22rpx;
  font-weight: bold;
}
.voice-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}
.profile-desc {
  margin-top: 18rpx;
  font-size: 26rpx;
  color: #222;
  font-weight: 500;
}
.profile-photos {
  margin-top: 12rpx;
  display: flex;
  gap: 12rpx;
}
.profile-photo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  object-fit: cover;
}

/* 一起玩卡片样式 */
.playwith-card {
  display: flex;
  padding: 24rpx;
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 24rpx rgba(45,156,250,0.10);
}
.playwith-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
}
.playwith-info {
  flex: 1;
}
.playwith-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.playwith-score {
  color: #ffb800;
  font-size: 26rpx;
  margin-bottom: 4rpx;
}
.playwith-game {
  font-size: 24rpx;
  color: #2d9cfa;
  margin-bottom: 4rpx;
}
.playwith-desc {
  font-size: 24rpx;
  color: #888;
}
.star {
  color: #ffb800;
}

/* 优化性能相关样式 */
.hardware-accelerated {
  transform: translateZ(0);
  will-change: transform;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}

/* 卡片渐入动画 */
@keyframes fade-in {
from { opacity: 0; transform: translateY(20rpx);
}
to { opacity: 1; transform: translateY(0);
}
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 添加底部tabbar相关样式 */
.df {
  display: flex;
  align-items: center;
}

/* 新增功能卡片样式 */
.feature-grid {
  padding: 15rpx; /* 减小内边距 */
}

/* 新的网格布局样式 */
.grid-layout {
  display: flex;
  flex-direction: column;
  gap: 15rpx; /* 减小间距 */
}

/* 顶部网格：左一右二 */
.top-grid {
  display: flex;
  gap: 15rpx; /* 减小间距 */
  height: 220rpx; /* 减小高度 */
}
.left-item {
  flex: 1;
  border-radius: 20rpx;
  padding: 20rpx; /* 减小内边距 */
  position: relative;
  overflow: hidden;
}
.right-stack {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15rpx; /* 减小间距 */
}
.right-item {
  flex: 1;
  border-radius: 20rpx;
  padding: 15rpx; /* 减小内边距 */
  position: relative;
  overflow: hidden;
}

/* 底部一行三个 */
.bottom-row {
  display: flex;
  gap: 15rpx; /* 减小间距 */
}
.bottom-item {
  flex: 1;
  border-radius: 20rpx;
  padding: 15rpx; /* 减小内边距 */
  position: relative;
  overflow: hidden;
  min-height: 90rpx; /* 减小最小高度 */
}
.feature-bubble {
  position: absolute;
  right: 15rpx;
  bottom: 15rpx;
  width: 80rpx; /* 减小气泡大小 */
  height: 80rpx; /* 减小气泡大小 */
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  z-index: 0;
}
.feature-content {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  z-index: 2;
  position: relative;
}
.feature-title {
  font-size: 28rpx; /* 减小字体大小 */
  font-weight: bold;
  color: #fff;
  margin-bottom: 6rpx; /* 减小下边距 */
}
.feature-subtitle {
  font-size: 20rpx; /* 减小字体大小 */
  color: rgba(255, 255, 255, 0.8);
}
.feature-btn {
  background: #ffffff;
  color: #0099ff;
  font-size: 24rpx; /* 减小字体大小 */
  font-weight: bold;
  border-radius: 50rpx;
  padding: 6rpx 24rpx; /* 减小内边距 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-top: 15rpx; /* 减小上边距 */
}
.feature-icon {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  z-index: 1;
}
.feature-icon image {
  width: 50rpx; /* 减小图标大小 */
  height: 50rpx; /* 减小图标大小 */
}
.heart-icon image {
  width: 40rpx; /* 减小图标大小 */
  height: 40rpx; /* 减小图标大小 */
}

/* Card colors */
.blue {
  background: #0099ff;
}
.pink {
  background: #ff66cc;
}
.green {
  background: #33cc99;
}
.purple {
  background: #9966ff;
}
.hot-pink {
  background: #ff6699;
}
.cyan {
  background: #00cccc;
}
