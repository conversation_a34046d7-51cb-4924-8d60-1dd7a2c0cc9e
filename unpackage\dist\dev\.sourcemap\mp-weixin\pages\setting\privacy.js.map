{"version": 3, "file": "privacy.js", "sources": ["pages/setting/privacy.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc2V0dGluZy9wcml2YWN5LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <navbar :bg=\"1\"></navbar>\r\n    <view class=\"title-box\" :style=\"{'margin-top': statusBarHeight + titleBarHeight + 'px'}\">\r\n      <view>隐私与显示设置 🔏️</view>\r\n    </view>\r\n    \r\n    <view class=\"table\">隐私设置</view>\r\n    <view class=\"list-box\">\r\n      <view class=\"list df\">\r\n        <image class=\"icon\" src=\"/static/img/setting/101.png\"></image>\r\n        <view class=\"list-item df bb1\">\r\n          <view class=\"title\">\r\n            <view class=\"t1\">允许别人查看我的粉丝</view>\r\n            <view class=\"t2\">他人是否可以查看我的粉丝列表</view>\r\n          </view>\r\n          <switch :checked=\"privacySettings.showFans\" @change=\"switchPrivacy('showFans')\" color=\"#000\" style=\"transform:scale(0.8)\"/>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"list df\">\r\n        <image class=\"icon\" src=\"/static/img/setting/102.png\"></image>\r\n        <view class=\"list-item df bb1\">\r\n          <view class=\"title\">\r\n            <view class=\"t1\">允许别人查看我的关注</view>\r\n            <view class=\"t2\">他人是否可以查看我的关注列表</view>\r\n          </view>\r\n          <switch :checked=\"privacySettings.showFollows\" @change=\"switchPrivacy('showFollows')\" color=\"#000\" style=\"transform:scale(0.8)\"/>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"list df\">\r\n        <image class=\"icon\" src=\"/static/img/setting/103.png\"></image>\r\n        <view class=\"list-item df bb1\">\r\n          <view class=\"title\">\r\n            <view class=\"t1\">允许别人查看我的访客</view>\r\n            <view class=\"t2\">他人是否可以查看谁访问过我的主页</view>\r\n          </view>\r\n          <switch :checked=\"privacySettings.showVisitors\" @change=\"switchPrivacy('showVisitors')\" color=\"#000\" style=\"transform:scale(0.8)\"/>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"list df\">\r\n        <image class=\"icon\" src=\"/static/img/setting/104.png\"></image>\r\n        <view class=\"list-item df\">\r\n          <view class=\"title\">\r\n            <view class=\"t1\">允许被搜索到</view>\r\n            <view class=\"t2\">是否允许其他用户通过搜索找到我</view>\r\n          </view>\r\n          <switch :checked=\"privacySettings.allowSearch\" @change=\"switchPrivacy('allowSearch')\" color=\"#000\" style=\"transform:scale(0.8)\"/>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <view class=\"table\">瀑布流设置</view>\r\n    <view class=\"list-box\">\r\n      <view class=\"list df\">\r\n        <image class=\"icon\" src=\"/static/img/setting/201.png\"></image>\r\n        <view class=\"list-item df bb1\">\r\n          <view class=\"title\">\r\n            <view class=\"t1\">动态瀑布流</view>\r\n            <view class=\"t2\">首页是否显示动态瀑布流</view>\r\n          </view>\r\n          <switch :checked=\"flowSettings.dynamicFlow\" @change=\"switchFlow('dynamicFlow')\" color=\"#000\" style=\"transform:scale(0.8)\"/>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"list df\">\r\n        <image class=\"icon\" src=\"/static/img/setting/202.png\"></image>\r\n        <view class=\"list-item df\">\r\n          <view class=\"title\">\r\n            <view class=\"t1\">圈子瀑布流</view>\r\n            <view class=\"t2\">圈子页面是否显示瀑布流布局</view>\r\n          </view>\r\n          <switch :checked=\"flowSettings.circleFlow\" @change=\"switchFlow('circleFlow')\" color=\"#000\" style=\"transform:scale(0.8)\"/>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <view class=\"description-box\">\r\n      <view class=\"description-title\">关于瀑布流布局</view>\r\n      <view class=\"description-content\">\r\n        瀑布流布局是一种多列布局，内容按照从上到下、从左到右的顺序进行排列。它能够更好地展示图片内容，提高浏览效率，但也会消耗更多的流量。\r\n      </view>\r\n    </view>\r\n       <!-- #ifdef MP -->   \r\n    <view class=\"table\">其他</view>\r\n    <view class=\"list-box\">\r\n      <button class=\"list df\" open-type=\"openSetting\">\r\n        <image class=\"icon\" src=\"/static/img/setting/105.png\"></image>\r\n        <view class=\"list-item df bb1\">\r\n          <view class=\"title\">\r\n            <view class=\"t1\">系统权限</view>\r\n          </view>\r\n          <image src=\"/static/img/x.png\"></image>\r\n        </view>\r\n      </button>\r\n    </view>\r\n          <!-- #endif -->\r\n    \r\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\r\n      <view class=\"tips-box df\">\r\n        <view class=\"tips-item\">{{uiState.tipsTitle}}</view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, onUnmounted, computed } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport navbar from '@/components/navbar/navbar.vue'\r\nimport { updatePrivacySettings, updateFlowSettings } from '@/api/social.js'\r\n\r\n// uni-popup 通过 easycom 自动导入，无需手动导入\r\n\r\n// 获取 store 实例\r\nconst store = useStore()\r\n\r\n// 响应式数据\r\nconst statusBarHeight = computed(() => store.state.statusBarHeight || 20)\r\nconst titleBarHeight = computed(() => store.state.titleBarHeight || 44)\r\n\r\nconst privacySettings = reactive({\r\n  showFans: true,\r\n  showFollows: true,\r\n  showVisitors: true,\r\n  allowSearch: true\r\n})\r\n\r\nconst flowSettings = reactive({\r\n  dynamicFlow: false,\r\n  circleFlow: false\r\n})\r\n\r\n// UI状态管理\r\nconst uiState = reactive({\r\n  tipsTitle: \"\",\r\n  privacyLoading: false,\r\n  flowLoading: false\r\n})\r\n\r\n// 弹窗引用\r\nconst tipsPopup = ref(null)\r\n\r\n// 防抖定时器\r\nconst debounceTimers = reactive({\r\n  privacy: null,\r\n  flow: null\r\n})\r\n\r\n// 防抖处理方法\r\nconst debounce = (func, delay = 300, timerKey = 'default') => {\r\n  if (debounceTimers[timerKey]) {\r\n    clearTimeout(debounceTimers[timerKey])\r\n  }\r\n\r\n  debounceTimers[timerKey] = setTimeout(() => {\r\n    func()\r\n    debounceTimers[timerKey] = null\r\n  }, delay)\r\n}\r\n\r\n// 统一的错误处理方法\r\nconst handleError = (error, context = '操作') => {\r\n  console.error(`${context}失败:`, error)\r\n\r\n  let message = `${context}失败，请稍后重试`\r\n  if (error.message) {\r\n    message = error.message\r\n  } else if (error.msg) {\r\n    message = error.msg\r\n  }\r\n\r\n  showTip(message)\r\n}\r\n\r\n// 显示提示信息\r\nconst showTip = (msg) => {\r\n  uiState.tipsTitle = msg\r\n  tipsPopup.value.open()\r\n\r\n  setTimeout(() => {\r\n    tipsPopup.value.close()\r\n  }, 2000)\r\n}\r\n\r\n// 优化的缓存解析方法\r\nconst getUserInfoFromCache = () => {\r\n  try {\r\n    let userInfo = uni.getStorageSync('USER_INFO') || {}\r\n\r\n    // 如果缓存返回字符串，先解析它\r\n    if (typeof userInfo === 'string') {\r\n      userInfo = JSON.parse(userInfo)\r\n    }\r\n\r\n    // 确保返回的是对象\r\n    return typeof userInfo === 'object' && userInfo !== null ? userInfo : {}\r\n  } catch (e) {\r\n    console.error('解析USER_INFO缓存失败:', e)\r\n    return {}\r\n  }\r\n}\r\n\r\n// 安全的JSON解析方法\r\nconst safeJsonParse = (data, defaultValue = {}) => {\r\n  try {\r\n    if (typeof data === 'string') {\r\n      return JSON.parse(data)\r\n    } else if (typeof data === 'object' && data !== null) {\r\n      return data\r\n    }\r\n    return defaultValue\r\n  } catch (e) {\r\n    console.error('JSON解析失败:', e)\r\n    return defaultValue\r\n  }\r\n}\r\n\r\n// 优化的从本地缓存加载配置\r\nconst loadFromCache = () => {\r\n  try {\r\n    // 获取本地存储的用户信息\r\n    const userInfo = getUserInfoFromCache()\r\n    console.log('从缓存加载用户信息:', userInfo)\r\n\r\n    // 从缓存中提取隐私设置\r\n    if (userInfo.privacy_settings) {\r\n      const cachedPrivacySettings = safeJsonParse(userInfo.privacy_settings, {})\r\n\r\n      if (Object.keys(cachedPrivacySettings).length > 0) {\r\n        // 合并默认设置，确保所有字段都存在\r\n        Object.assign(privacySettings, cachedPrivacySettings)\r\n        console.log('成功加载隐私设置:', privacySettings)\r\n      }\r\n    } else {\r\n      console.log('缓存中没有隐私设置，使用默认值')\r\n    }\r\n\r\n    // 从缓存中提取瀑布流设置\r\n    if (userInfo.flow_settings) {\r\n      const cachedFlowSettings = safeJsonParse(userInfo.flow_settings, {})\r\n\r\n      if (Object.keys(cachedFlowSettings).length > 0) {\r\n        console.log('读取到的瀑布流设置:', cachedFlowSettings)\r\n\r\n        // 确保所有必需字段存在并且是布尔值\r\n        const normalizedSettings = {\r\n          dynamicFlow: Boolean(cachedFlowSettings.dynamicFlow),\r\n          circleFlow: Boolean(cachedFlowSettings.circleFlow)\r\n        }\r\n\r\n        // 合并默认设置，确保所有字段都存在\r\n        Object.assign(flowSettings, normalizedSettings)\r\n        console.log('成功加载瀑布流设置:', flowSettings)\r\n      }\r\n    } else {\r\n      console.log('缓存中没有瀑布流设置，使用默认值:', flowSettings)\r\n    }\r\n  } catch (e) {\r\n    console.error('从缓存加载配置出错:', e)\r\n  }\r\n}\r\n\r\n// 优化的更新本地缓存方法\r\nconst updateLocalCache = (settingType, data) => {\r\n  try {\r\n    // 先获取当前缓存\r\n    const userInfo = getUserInfoFromCache()\r\n    console.log(`更新${settingType}缓存`)\r\n\r\n    // 数据标准化处理\r\n    let normalizedData = { ...data }\r\n\r\n    if (settingType === 'flow_settings') {\r\n      // 确保瀑布流设置是布尔值\r\n      normalizedData = {\r\n        dynamicFlow: Boolean(data.dynamicFlow),\r\n        circleFlow: Boolean(data.circleFlow)\r\n      }\r\n      console.log('标准化后的flow_settings:', normalizedData)\r\n    }\r\n\r\n    // 更新缓存中的设置\r\n    userInfo[settingType] = normalizedData\r\n\r\n    // 保存回缓存\r\n    uni.setStorageSync('USER_INFO', userInfo)\r\n    console.log(`${settingType}缓存更新完成`)\r\n\r\n    // 通知其他页面用户信息已更新\r\n    uni.$emit('userInfoUpdated', {\r\n      type: settingType,\r\n      data: userInfo\r\n    })\r\n\r\n    // 更新全局状态\r\n    if (settingType === 'flow_settings') {\r\n      try {\r\n        store.commit('UPDATE_FLOW_SETTINGS', normalizedData)\r\n        console.log('全局状态更新完成:', normalizedData)\r\n      } catch (error) {\r\n        console.warn('更新全局状态失败:', error.message)\r\n      }\r\n    }\r\n  } catch (e) {\r\n    console.error('更新本地缓存失败:', e)\r\n  }\r\n}\r\n\r\n// 获取设置项的显示名称\r\nconst getSettingDisplayName = (type, isEnabled) => {\r\n  const names = {\r\n    showVisitors: '访客查看',\r\n    allowSearch: '搜索发现',\r\n    showFans: '粉丝查看',\r\n    showFollows: '关注查看',\r\n    dynamicFlow: '动态瀑布流',\r\n    circleFlow: '圈子瀑布流'\r\n  }\r\n\r\n  const status = isEnabled ? '开启' : '关闭'\r\n  return `${names[type] || type}已${status}`\r\n}\r\n\r\n// 优化的切换隐私设置\r\nconst switchPrivacy = (type) => {\r\n  if (uiState.privacyLoading) return\r\n\r\n  // 使用防抖处理，避免快速点击\r\n  debounce(() => {\r\n    uiState.privacyLoading = true\r\n\r\n    const updateData = { ...privacySettings }\r\n    updateData[type] = !updateData[type]\r\n\r\n    // 立即更新本地状态\r\n    Object.assign(privacySettings, updateData)\r\n\r\n    // 立即更新本地缓存\r\n    updateLocalCache('privacy_settings', privacySettings)\r\n\r\n    // 准备请求数据\r\n    const requestData = {\r\n      privacy_settings: updateData\r\n    }\r\n\r\n    // 静默提交到服务器，不阻塞用户界面\r\n    updatePrivacySettings(requestData).then(res => {\r\n      if (res.code === 200 && res.data) {\r\n        // 如果服务器返回数据与本地不一致，静默更新本地数据\r\n        if (JSON.stringify(privacySettings) !== JSON.stringify(res.data)) {\r\n          Object.assign(privacySettings, res.data)\r\n          updateLocalCache('privacy_settings', privacySettings)\r\n        }\r\n\r\n        // 显示成功提示\r\n        showTip(getSettingDisplayName(type, privacySettings[type]))\r\n      }\r\n    }).catch(error => {\r\n      handleError(error, '隐私设置更新')\r\n    }).finally(() => {\r\n      uiState.privacyLoading = false\r\n    })\r\n  }, 300, 'privacy')\r\n}\r\n\r\n// 优化的切换瀑布流设置\r\nconst switchFlow = (type) => {\r\n  if (uiState.flowLoading) return\r\n\r\n  // 使用防抖处理，避免快速点击\r\n  debounce(() => {\r\n    uiState.flowLoading = true\r\n\r\n    const updateData = { ...flowSettings }\r\n    // 确保是布尔值取反\r\n    updateData[type] = !Boolean(updateData[type])\r\n\r\n    console.log('瀑布流设置变更:', type, '从', flowSettings[type], '到', updateData[type])\r\n\r\n    // 立即更新本地状态\r\n    Object.assign(flowSettings, updateData)\r\n\r\n    // 立即更新本地缓存\r\n    updateLocalCache('flow_settings', flowSettings)\r\n\r\n    // 准备请求数据\r\n    const requestData = {\r\n      flow_settings: updateData\r\n    }\r\n\r\n    // 静默提交到服务器，不阻塞用户界面\r\n    updateFlowSettings(requestData).then(res => {\r\n      if (res.code === 200 && res.data) {\r\n        // 如果服务器返回数据与本地不一致，静默更新本地数据\r\n        if (JSON.stringify(flowSettings) !== JSON.stringify(res.data)) {\r\n          console.log('从服务器获取到不同的瀑布流设置，更新本地缓存')\r\n\r\n          // 确保从服务器返回的数据格式正确\r\n          const serverData = { ...res.data }\r\n          if (typeof serverData.dynamicFlow === 'undefined') {\r\n            serverData.dynamicFlow = false\r\n          }\r\n          if (typeof serverData.circleFlow === 'undefined') {\r\n            serverData.circleFlow = false\r\n          }\r\n\r\n          // 确保是布尔值类型\r\n          serverData.dynamicFlow = Boolean(serverData.dynamicFlow)\r\n          serverData.circleFlow = Boolean(serverData.circleFlow)\r\n\r\n          Object.assign(flowSettings, serverData)\r\n          updateLocalCache('flow_settings', flowSettings)\r\n        }\r\n\r\n        // 显示成功提示\r\n        showTip(getSettingDisplayName(type, flowSettings[type]))\r\n      }\r\n    }).catch(error => {\r\n      handleError(error, '瀑布流设置更新')\r\n    }).finally(() => {\r\n      uiState.flowLoading = false\r\n    })\r\n  }, 300, 'flow')\r\n}\r\n\r\n// 清理定时器\r\nconst clearTimers = () => {\r\n  Object.keys(debounceTimers).forEach(key => {\r\n    if (debounceTimers[key]) {\r\n      clearTimeout(debounceTimers[key])\r\n      debounceTimers[key] = null\r\n    }\r\n  })\r\n}\r\n\r\n// 生命周期 - 页面加载时\r\nonMounted(() => {\r\n  // 立即从缓存加载数据\r\n  loadFromCache()\r\n})\r\n\r\n// 生命周期 - 页面卸载时\r\nonUnmounted(() => {\r\n  clearTimers()\r\n})\r\n</script>\r\n\r\n<style>\r\npage {\r\n  background: #f8f8f8;\r\n  padding: 0 30rpx;\r\n  box-sizing: border-box;\r\n  overflow-x: hidden; \r\n}\r\n\r\n.title-box {\r\n\tpadding: 20rpx 0;\r\n\tfont-size: 40rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.table {\r\n\tpadding: 30rpx 0;\r\n\tcolor: #999;\r\n\tfont-size: 24rpx;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.list-box {\r\n  width: 100%;\r\n  border-radius: 24rpx;\r\n  overflow: hidden;\r\n  box-sizing: border-box; \r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.list-box .list {\r\n\tmargin: 0;\r\n\tpadding: 0;\r\n\twidth: 100%;\r\n\tbackground: #fff!important;\r\n\tborder-radius: 0;\r\n}\r\n\r\n\r\n.list-box .list:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.list-box .list .icon {\r\n  margin: 0 30rpx;\r\n  width: 38rpx;\r\n  height: 38rpx;\r\n}\r\n\r\n.list-box .list-item {\r\n  width: calc(100% - 98rpx);\r\n  padding: 30rpx 30rpx 30rpx 0;\r\n  justify-content: space-between;\r\n  border-bottom: 1px solid #f8f8f8;\r\n}\r\n\r\n.list-item .title {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n}\r\n\r\n.list-item .title .t1 {\r\n  font-size: 24rpx;\r\n  font-weight: 500;\r\n  line-height: 48rpx;\r\n}\r\n\r\n.list-item .title .t2 {\r\n  color: #999;\r\n  font-size: 18rpx;\r\n  font-weight: 300;\r\n  line-height: 18rpx;\r\n}\r\n\r\n.list-box .list-item image {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  transform: rotate(-90deg);\r\n}\r\n\r\n.description-box {\r\n  background: #fff;\r\n  border-radius: 24rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.description-title {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.description-content {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  line-height: 1.6;\r\n}\r\n\r\n.version {\r\n  padding: 60rpx 0;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n}\r\n\r\n.version image {\r\n  margin-right: 10rpx;\r\n  width: 20rpx;\r\n  height: 20rpx;\r\n}\r\n\r\n.version text {\r\n  color: #999;\r\n  font-size: 18rpx;\r\n}\r\n\r\n.tips-box {\r\n  padding: 20rpx 30rpx;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  border-radius: 12rpx;\r\n  justify-content: center;\r\n}\r\n\r\n.tips-box .tips-item {\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n.df {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/uniapp/vue3/pages/setting/privacy.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useStore", "computed", "reactive", "ref", "uni", "updatePrivacySettings", "updateFlowSettings", "onMounted", "onUnmounted", "MiniProgramPage"], "mappings": ";;;;;;;;;;;;AA+GA,MAAM,SAAS,MAAW;;;;AAM1B,UAAM,QAAQA,cAAAA,SAAU;AAGxB,UAAM,kBAAkBC,cAAQ,SAAC,MAAM,MAAM,MAAM,mBAAmB,EAAE;AACxE,UAAM,iBAAiBA,cAAQ,SAAC,MAAM,MAAM,MAAM,kBAAkB,EAAE;AAEtE,UAAM,kBAAkBC,cAAAA,SAAS;AAAA,MAC/B,UAAU;AAAA,MACV,aAAa;AAAA,MACb,cAAc;AAAA,MACd,aAAa;AAAA,IACf,CAAC;AAED,UAAM,eAAeA,cAAAA,SAAS;AAAA,MAC5B,aAAa;AAAA,MACb,YAAY;AAAA,IACd,CAAC;AAGD,UAAM,UAAUA,cAAAA,SAAS;AAAA,MACvB,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf,CAAC;AAGD,UAAM,YAAYC,cAAG,IAAC,IAAI;AAG1B,UAAM,iBAAiBD,cAAAA,SAAS;AAAA,MAC9B,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAGD,UAAM,WAAW,CAAC,MAAM,QAAQ,KAAK,WAAW,cAAc;AAC5D,UAAI,eAAe,QAAQ,GAAG;AAC5B,qBAAa,eAAe,QAAQ,CAAC;AAAA,MACtC;AAED,qBAAe,QAAQ,IAAI,WAAW,MAAM;AAC1C,aAAM;AACN,uBAAe,QAAQ,IAAI;AAAA,MAC5B,GAAE,KAAK;AAAA,IACV;AAGA,UAAM,cAAc,CAAC,OAAO,UAAU,SAAS;AAC7CE,0BAAc,MAAA,SAAA,oCAAA,GAAG,OAAO,OAAO,KAAK;AAEpC,UAAI,UAAU,GAAG,OAAO;AACxB,UAAI,MAAM,SAAS;AACjB,kBAAU,MAAM;AAAA,MACpB,WAAa,MAAM,KAAK;AACpB,kBAAU,MAAM;AAAA,MACjB;AAED,cAAQ,OAAO;AAAA,IACjB;AAGA,UAAM,UAAU,CAAC,QAAQ;AACvB,cAAQ,YAAY;AACpB,gBAAU,MAAM,KAAM;AAEtB,iBAAW,MAAM;AACf,kBAAU,MAAM,MAAO;AAAA,MACxB,GAAE,GAAI;AAAA,IACT;AAGA,UAAM,uBAAuB,MAAM;AACjC,UAAI;AACF,YAAI,WAAWA,cAAG,MAAC,eAAe,WAAW,KAAK,CAAE;AAGpD,YAAI,OAAO,aAAa,UAAU;AAChC,qBAAW,KAAK,MAAM,QAAQ;AAAA,QAC/B;AAGD,eAAO,OAAO,aAAa,YAAY,aAAa,OAAO,WAAW,CAAE;AAAA,MACzE,SAAQ,GAAG;AACVA,sBAAAA,MAAc,MAAA,SAAA,oCAAA,oBAAoB,CAAC;AACnC,eAAO,CAAE;AAAA,MACV;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,MAAM,eAAe,OAAO;AACjD,UAAI;AACF,YAAI,OAAO,SAAS,UAAU;AAC5B,iBAAO,KAAK,MAAM,IAAI;AAAA,QACvB,WAAU,OAAO,SAAS,YAAY,SAAS,MAAM;AACpD,iBAAO;AAAA,QACR;AACD,eAAO;AAAA,MACR,SAAQ,GAAG;AACVA,sBAAAA,yDAAc,aAAa,CAAC;AAC5B,eAAO;AAAA,MACR;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1B,UAAI;AAEF,cAAM,WAAW,qBAAsB;AACvCA,sBAAAA,MAAY,MAAA,OAAA,oCAAA,cAAc,QAAQ;AAGlC,YAAI,SAAS,kBAAkB;AAC7B,gBAAM,wBAAwB,cAAc,SAAS,kBAAkB,CAAA,CAAE;AAEzE,cAAI,OAAO,KAAK,qBAAqB,EAAE,SAAS,GAAG;AAEjD,mBAAO,OAAO,iBAAiB,qBAAqB;AACpDA,0BAAAA,MAAA,MAAA,OAAA,oCAAY,aAAa,eAAe;AAAA,UACzC;AAAA,QACP,OAAW;AACLA,wBAAAA,uDAAY,iBAAiB;AAAA,QAC9B;AAGD,YAAI,SAAS,eAAe;AAC1B,gBAAM,qBAAqB,cAAc,SAAS,eAAe,CAAA,CAAE;AAEnE,cAAI,OAAO,KAAK,kBAAkB,EAAE,SAAS,GAAG;AAC9CA,0BAAAA,MAAY,MAAA,OAAA,oCAAA,cAAc,kBAAkB;AAG5C,kBAAM,qBAAqB;AAAA,cACzB,aAAa,QAAQ,mBAAmB,WAAW;AAAA,cACnD,YAAY,QAAQ,mBAAmB,UAAU;AAAA,YAClD;AAGD,mBAAO,OAAO,cAAc,kBAAkB;AAC9CA,0BAAAA,MAAY,MAAA,OAAA,oCAAA,cAAc,YAAY;AAAA,UACvC;AAAA,QACP,OAAW;AACLA,wBAAAA,MAAA,MAAA,OAAA,oCAAY,qBAAqB,YAAY;AAAA,QAC9C;AAAA,MACF,SAAQ,GAAG;AACVA,sBAAAA,MAAA,MAAA,SAAA,oCAAc,cAAc,CAAC;AAAA,MAC9B;AAAA,IACH;AAGA,UAAM,mBAAmB,CAAC,aAAa,SAAS;AAC9C,UAAI;AAEF,cAAM,WAAW,qBAAsB;AACvCA,sBAAY,MAAA,MAAA,OAAA,oCAAA,KAAK,WAAW,IAAI;AAGhC,YAAI,iBAAiB,EAAE,GAAG,KAAM;AAEhC,YAAI,gBAAgB,iBAAiB;AAEnC,2BAAiB;AAAA,YACf,aAAa,QAAQ,KAAK,WAAW;AAAA,YACrC,YAAY,QAAQ,KAAK,UAAU;AAAA,UACpC;AACDA,wBAAAA,MAAY,MAAA,OAAA,oCAAA,uBAAuB,cAAc;AAAA,QAClD;AAGD,iBAAS,WAAW,IAAI;AAGxBA,4BAAI,eAAe,aAAa,QAAQ;AACxCA,sBAAY,MAAA,MAAA,OAAA,oCAAA,GAAG,WAAW,QAAQ;AAGlCA,sBAAG,MAAC,MAAM,mBAAmB;AAAA,UAC3B,MAAM;AAAA,UACN,MAAM;AAAA,QACZ,CAAK;AAGD,YAAI,gBAAgB,iBAAiB;AACnC,cAAI;AACF,kBAAM,OAAO,wBAAwB,cAAc;AACnDA,0BAAAA,MAAA,MAAA,OAAA,oCAAY,aAAa,cAAc;AAAA,UACxC,SAAQ,OAAO;AACdA,0BAAa,MAAA,MAAA,QAAA,oCAAA,aAAa,MAAM,OAAO;AAAA,UACxC;AAAA,QACF;AAAA,MACF,SAAQ,GAAG;AACVA,sBAAAA,yDAAc,aAAa,CAAC;AAAA,MAC7B;AAAA,IACH;AAGA,UAAM,wBAAwB,CAAC,MAAM,cAAc;AACjD,YAAM,QAAQ;AAAA,QACZ,cAAc;AAAA,QACd,aAAa;AAAA,QACb,UAAU;AAAA,QACV,aAAa;AAAA,QACb,aAAa;AAAA,QACb,YAAY;AAAA,MACb;AAED,YAAM,SAAS,YAAY,OAAO;AAClC,aAAO,GAAG,MAAM,IAAI,KAAK,IAAI,IAAI,MAAM;AAAA,IACzC;AAGA,UAAM,gBAAgB,CAAC,SAAS;AAC9B,UAAI,QAAQ;AAAgB;AAG5B,eAAS,MAAM;AACb,gBAAQ,iBAAiB;AAEzB,cAAM,aAAa,EAAE,GAAG,gBAAiB;AACzC,mBAAW,IAAI,IAAI,CAAC,WAAW,IAAI;AAGnC,eAAO,OAAO,iBAAiB,UAAU;AAGzC,yBAAiB,oBAAoB,eAAe;AAGpD,cAAM,cAAc;AAAA,UAClB,kBAAkB;AAAA,QACnB;AAGDC,mBAAAA,sBAAsB,WAAW,EAAE,KAAK,SAAO;AAC7C,cAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEhC,gBAAI,KAAK,UAAU,eAAe,MAAM,KAAK,UAAU,IAAI,IAAI,GAAG;AAChE,qBAAO,OAAO,iBAAiB,IAAI,IAAI;AACvC,+BAAiB,oBAAoB,eAAe;AAAA,YACrD;AAGD,oBAAQ,sBAAsB,MAAM,gBAAgB,IAAI,CAAC,CAAC;AAAA,UAC3D;AAAA,QACP,CAAK,EAAE,MAAM,WAAS;AAChB,sBAAY,OAAO,QAAQ;AAAA,QACjC,CAAK,EAAE,QAAQ,MAAM;AACf,kBAAQ,iBAAiB;AAAA,QAC/B,CAAK;AAAA,MACL,GAAK,KAAK,SAAS;AAAA,IACnB;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,UAAI,QAAQ;AAAa;AAGzB,eAAS,MAAM;AACb,gBAAQ,cAAc;AAEtB,cAAM,aAAa,EAAE,GAAG,aAAc;AAEtC,mBAAW,IAAI,IAAI,CAAC,QAAQ,WAAW,IAAI,CAAC;AAE5CD,sBAAAA,MAAA,MAAA,OAAA,oCAAY,YAAY,MAAM,KAAK,aAAa,IAAI,GAAG,KAAK,WAAW,IAAI,CAAC;AAG5E,eAAO,OAAO,cAAc,UAAU;AAGtC,yBAAiB,iBAAiB,YAAY;AAG9C,cAAM,cAAc;AAAA,UAClB,eAAe;AAAA,QAChB;AAGDE,mBAAAA,mBAAmB,WAAW,EAAE,KAAK,SAAO;AAC1C,cAAI,IAAI,SAAS,OAAO,IAAI,MAAM;AAEhC,gBAAI,KAAK,UAAU,YAAY,MAAM,KAAK,UAAU,IAAI,IAAI,GAAG;AAC7DF,4BAAAA,uDAAY,wBAAwB;AAGpC,oBAAM,aAAa,EAAE,GAAG,IAAI,KAAM;AAClC,kBAAI,OAAO,WAAW,gBAAgB,aAAa;AACjD,2BAAW,cAAc;AAAA,cAC1B;AACD,kBAAI,OAAO,WAAW,eAAe,aAAa;AAChD,2BAAW,aAAa;AAAA,cACzB;AAGD,yBAAW,cAAc,QAAQ,WAAW,WAAW;AACvD,yBAAW,aAAa,QAAQ,WAAW,UAAU;AAErD,qBAAO,OAAO,cAAc,UAAU;AACtC,+BAAiB,iBAAiB,YAAY;AAAA,YAC/C;AAGD,oBAAQ,sBAAsB,MAAM,aAAa,IAAI,CAAC,CAAC;AAAA,UACxD;AAAA,QACP,CAAK,EAAE,MAAM,WAAS;AAChB,sBAAY,OAAO,SAAS;AAAA,QAClC,CAAK,EAAE,QAAQ,MAAM;AACf,kBAAQ,cAAc;AAAA,QAC5B,CAAK;AAAA,MACL,GAAK,KAAK,MAAM;AAAA,IAChB;AAGA,UAAM,cAAc,MAAM;AACxB,aAAO,KAAK,cAAc,EAAE,QAAQ,SAAO;AACzC,YAAI,eAAe,GAAG,GAAG;AACvB,uBAAa,eAAe,GAAG,CAAC;AAChC,yBAAe,GAAG,IAAI;AAAA,QACvB;AAAA,MACL,CAAG;AAAA,IACH;AAGAG,kBAAAA,UAAU,MAAM;AAEd,oBAAe;AAAA,IACjB,CAAC;AAGDC,kBAAAA,YAAY,MAAM;AAChB,kBAAa;AAAA,IACf,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9bD,GAAG,WAAWC,SAAe;"}