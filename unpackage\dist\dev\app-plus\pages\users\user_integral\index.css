.integral-details .header[data-v-9da6c933] {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: 14.375rem;
  font-size: 2.25rem;
  color: #fff;
  padding: 0.96875rem 0 1.40625rem 0;
  box-sizing: border-box;
  text-align: center;
  font-family: "Guildford Pro";
  background-color: var(--view-theme);
}
.integral-details .header .currentScore[data-v-9da6c933] {
  font-size: 0.8125rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 0.34375rem;
}
.integral-details .header .scoreNum[data-v-9da6c933] {
  font-family: "Guildford Pro";
}
.integral-details .header .line[data-v-9da6c933] {
  width: 1.875rem;
  height: 0.09375rem;
  background-color: #fff;
  margin: 0.625rem auto 0 auto;
}
.integral-details .header .nav[data-v-9da6c933] {
  font-size: 0.6875rem;
  color: rgba(255, 255, 255, 0.8);
  flex: 1;
  margin-top: 1.09375rem;
}
.integral-details .header .nav .item[data-v-9da6c933] {
  width: 33.33%;
  text-align: center;
}
.integral-details .header .nav .item .num[data-v-9da6c933] {
  color: #fff;
  font-size: 1.25rem;
  margin-bottom: 0.15625rem;
  font-family: "Guildford Pro";
}
.integral-details .wrapper .nav[data-v-9da6c933] {
  flex: 1;
  width: 21.5625rem;
  border-radius: 0.625rem 0.625rem 0 0;
  margin: -3rem auto 0 auto;
  background-color: #f7f7f7;
  height: 3rem;
  font-size: 0.9375rem;
  color: #bbb;
}
.integral-details .wrapper .nav .item[data-v-9da6c933] {
  text-align: center;
  width: 50%;
}
.integral-details .wrapper .nav .item.on[data-v-9da6c933] {
  background-color: #fff;
  color: var(--view-theme);
  font-weight: bold;
  border-radius: 0.625rem 0 0 0;
}
.integral-details .wrapper .nav .item:nth-of-type(2).on[data-v-9da6c933] {
  border-radius: 0 0.625rem 0 0;
}
.integral-details .wrapper .nav .item .iconfont[data-v-9da6c933] {
  font-size: 1.1875rem;
  margin-right: 0.3125rem;
}
.integral-details .wrapper .list[data-v-9da6c933] {
  padding: 0.75rem 0.9375rem;
}
.integral-details .wrapper .list.bag-white[data-v-9da6c933] {
  background-color: #fff;
}
.integral-details .wrapper .list .tip[data-v-9da6c933] {
  font-size: 0.78125rem;
  width: 21.5625rem;
  height: 1.875rem;
  border-radius: 1.5625rem;
  background-color: #fff5e2;
  border: 0.03125rem solid #ffeac1;
  color: #c8a86b;
  padding: 0 0.625rem;
  box-sizing: border-box;
  margin-bottom: 0.75rem;
}
.integral-details .wrapper .list .tip .iconfont[data-v-9da6c933] {
  font-size: 1.09375rem;
  margin-right: 0.46875rem;
}
.integral-details .wrapper .list .item[data-v-9da6c933] {
  height: 3.875rem;
  border-bottom: 0.03125rem solid #eee;
  font-size: 0.75rem;
  color: #999;
}
.integral-details .wrapper .list .item .state[data-v-9da6c933] {
  font-size: 0.875rem;
  color: #282828;
  margin-bottom: 0.25rem;
}
.integral-details .wrapper .list .item .num[data-v-9da6c933] {
  font-size: 1.125rem;
  font-family: "Guildford Pro";
  color: #16AC57;
}
.integral-details .wrapper .list .item .num.font-color[data-v-9da6c933] {
  color: #E93323 !important;
}
.integral-details .wrapper .list2[data-v-9da6c933] {
  background-color: #fff;
  padding: 0.75rem 0;
}
.integral-details .wrapper .list2 .item[data-v-9da6c933] {
  background-image: linear-gradient(to right, #fff7e7 0%, #fffdf9 100%);
  width: 21.5625rem;
  height: 5.625rem;
  position: relative;
  border-radius: 0.3125rem;
  margin: 0 auto 0.625rem auto;
  padding: 0 0.78125rem 0 5.625rem;
  box-sizing: border-box;
}
.integral-details .wrapper .list2 .item .pictrue[data-v-9da6c933] {
  width: 2.8125rem;
  height: 4.6875rem;
  position: absolute;
  bottom: 0;
  left: 1.40625rem;
}
.integral-details .wrapper .list2 .item .pictrue uni-image[data-v-9da6c933] {
  width: 100%;
  height: 100%;
}
.integral-details .wrapper .list2 .item .name[data-v-9da6c933] {
  width: 8.90625rem;
  font-size: 0.9375rem;
  font-weight: bold;
  color: #c8a86b;
}
.integral-details .wrapper .list2 .item .earn[data-v-9da6c933] {
  font-size: 0.8125rem;
  color: #c8a86b;
  border: 0.0625rem solid #c8a86b;
  text-align: center;
  line-height: 1.625rem;
  height: 1.625rem;
  width: 5rem;
  border-radius: 1.5625rem;
}
.apply[data-v-9da6c933] {
  top: 1.625rem;
  right: 0;
  position: absolute;
  width: -webkit-max-content;
  width: max-content;
  height: 1.75rem;
  padding: 0 0.4375rem;
  background-color: #fff1db;
  color: #a56a15;
  font-size: 0.6875rem;
  border-radius: 0.9375rem 0 0 0.9375rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.empty-box[data-v-9da6c933] {
  padding-bottom: 9.375rem;
}