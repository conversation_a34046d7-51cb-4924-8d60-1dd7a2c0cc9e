"use strict";
const common_vendor = require("../../common/vendor.js");
const mixins_color = require("../../mixins/color.js");
require("../../utils/request.js");
const _sfc_main = {
  mixins: [mixins_color.colors],
  data() {
    return {
      isShow: false,
      agreementName: "",
      mpData: common_vendor.index.getStorageSync("copyRight")
    };
  },
  mounted() {
    common_vendor.wx$1.getPrivacySetting({
      success: (res) => {
        if (res.needAuthorization) {
          this.isShow = true;
          this.agreementName = res.privacyContractName;
        } else {
          this.$emit("onAgree");
        }
      },
      fail: () => {
      },
      complete: () => {
      }
    });
  },
  methods: {
    // 同意
    handleAgree() {
      this.isShow = false;
      this.$emit("onAgree");
    },
    // 拒绝
    rejectAgreement() {
      this.isShow = false;
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
      this.$emit("onReject");
    },
    closeAttr() {
      this.$emit("onCloseAgePop");
    },
    // 跳转协议
    privacy(type) {
      common_vendor.index.navigateTo({
        url: "/pages/users/privacy/index?type=" + type
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o(() => {
    }),
    b: common_vendor.t($data.mpData.siteName),
    c: common_vendor.t(_ctx.$t(`服务与隐私协议`)),
    d: common_vendor.t(_ctx.$t(`欢迎您使用${$data.mpData.siteName}！请仔细阅读以下内容，并作出适当的选择：`)),
    e: common_vendor.t(_ctx.$t(`隐私政策概要`)),
    f: common_vendor.t(_ctx.$t(`当您点击同意并开始时用产品服务时，即表示您已理解并同息该条款内容，该条款将对您产生法律约束力。如您拒绝，将无法继续下一步操作。`)),
    g: common_vendor.t(_ctx.$t(`点击阅读`)),
    h: common_vendor.t($data.agreementName),
    i: common_vendor.o(($event) => $options.privacy(3)),
    j: common_vendor.t(_ctx.$t(`同意并继续`)),
    k: common_vendor.o((...args) => $options.handleAgree && $options.handleAgree(...args)),
    l: common_vendor.t(_ctx.$t(`取消`)),
    m: common_vendor.o((...args) => $options.rejectAgreement && $options.rejectAgreement(...args)),
    n: $data.isShow ? 1 : "",
    o: common_vendor.s(_ctx.colorStyle)
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-6020d7bd"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/privacyAgreementPopup/index.js.map
