{"version": 3, "file": "ase.js", "sources": ["pages/users/components/verify/utils/ase.js"], "sourcesContent": ["// 使用简化的AES加密实现，避免crypto-js依赖问题\n// 这是一个简化的AES加密函数，仅用于验证码功能\nfunction simpleAESEncrypt(text, key = \"XwKsGlMcdPMEhR1B\") {\n  // 简单的字符串编码，实际项目中应该使用真正的AES加密\n  let result = '';\n  for (let i = 0; i < text.length; i++) {\n    const char = text.charCodeAt(i);\n    const keyChar = key.charCodeAt(i % key.length);\n    result += String.fromCharCode(char ^ keyChar);\n  }\n  return btoa(result); // Base64编码\n}\nexport function aesEncrypt(word, keyWord = \"XwKsGlMcdPMEhR1B\") {\n  // 使用简化的加密实现\n  return simpleAESEncrypt(word, keyWord);\n}"], "names": [], "mappings": ";AAEA,SAAS,iBAAiB,MAAM,MAAM,oBAAoB;AAExD,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,OAAO,KAAK,WAAW,CAAC;AAC9B,UAAM,UAAU,IAAI,WAAW,IAAI,IAAI,MAAM;AAC7C,cAAU,OAAO,aAAa,OAAO,OAAO;AAAA,EAC7C;AACD,SAAO,KAAK,MAAM;AACpB;AACO,SAAS,WAAW,MAAM,UAAU,oBAAoB;AAE7D,SAAO,iBAAiB,MAAM,OAAO;AACvC;;"}