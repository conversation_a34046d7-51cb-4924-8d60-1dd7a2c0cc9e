"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "play",
  props: {
    color: {
      type: String,
      default: "#000"
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $props.color,
    b: $props.color,
    c: $props.color
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/play/play.js.map
