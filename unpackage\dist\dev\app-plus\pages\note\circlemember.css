
.container {
  background: #fff;
  min-height: 100vh;
  padding-bottom: 1.25rem;
}
.search-box {
  margin: 0.9375rem 0.9375rem 0.9375rem 0.9375rem;
  background: #f6f7fa;
  border-radius: 0.9375rem;
  height: 1.875rem;
  align-items: center;
  padding: 0 0.625rem;
}
.search-icon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.3125rem;
}
.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 0.8125rem;
  color: #333;
}
.clear-btn {
  width: 1rem;
  height: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.3125rem;
}
.clear-icon {
  width: 0.625rem;
  height: 0.625rem;
}
/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.loading-spinner {
  width: 1.875rem;
  height: 1.875rem;
  border: 0.125rem solid #f3f3f3;
  border-top: 0.125rem solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text {
  margin-top: 0.625rem;
  font-size: 0.8125rem;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.75rem 1.25rem;
}
.empty-icon {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.9375rem;
  opacity: 0.6;
}
.empty-text {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.3125rem;
}
.empty-tip {
  font-size: 0.75rem;
  color: #999;
}

/* 内容区域 */
.content-area {
  min-height: 60vh;
}
.section {
  margin-bottom: 1.25rem;
}
.section-title {
  font-size: 0.875rem;
  font-weight: bold;
  color: #222;
  margin: 0.9375rem 0 0.5625rem 0.9375rem;
  display: flex;
  align-items: center;
}
.member-count {
  font-size: 0.75rem;
  color: #999;
  font-weight: normal;
  margin-left: 0.25rem;
}
.member-list {
  margin: 0;
}
.member-item {
  padding: 0.15625rem 0.78125rem;
  display: flex;
  align-items: center;
  position: relative;
  min-height: 3.125rem;
}
.avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  margin-right: 0.625rem;
  background: #f5f5f5;
  flex-shrink: 0;
}
.info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.name {
  font-size: 0.875rem;
  font-weight: 700;
  color: #222;
  display: flex;
  align-items: center;
  margin-bottom: 0.1875rem;
}
.role-tag {
  font-size: 0.625rem;
  border-radius: 0.25rem;
  padding: 0.0625rem 0.4375rem;
  margin-left: 0.375rem;
  color: #fff;
  font-weight: 400;
}
.role-tag.owner {
  background: #ff9500;
}
.role-tag.admin {
  background: #3da0ff;
}
.status-tag {
  font-size: 0.625rem;
  border-radius: 0.25rem;
  padding: 0.0625rem 0.4375rem;
  margin-left: 0.375rem;
  font-weight: 400;
}
.status-tag.muted {
  background: #ff4757;
  color: #fff;
}
.meta {
  font-size: 0.6875rem;
  color: #999;
  display: flex;
  align-items: center;
  line-height: 1.4;
}
.gender {
  margin-right: 0.1875rem;
}
.male {
  color: #4e6ef2;
}
.female {
  color: #fa5a8a;
}
.age {
  margin-right: 0.375rem;
}
.contrib {
  color: #999;
}
.action-buttons {
  margin-left: 0.625rem;
  flex-shrink: 0;
}
.action-btn {
  background: #007aff;
  border-radius: 0.5rem;
  padding: 0.1875rem 0.5rem;
  font-size: 0.6875rem;
  color: #fff;
  min-width: 1.875rem;
  text-align: center;
}
.action-btn:active {
  background: #0056cc;
}
.df {
  display: flex;
  align-items: center;
}
