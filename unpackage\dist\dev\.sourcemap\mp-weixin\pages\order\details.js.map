{"version": 3, "file": "details.js", "sources": ["pages/order/details.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvb3JkZXIvZGV0YWlscy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 导航栏 -->\r\n    <view class=\"nav-box bf8\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\r\n      <view class=\"nav-item df\" :style=\"{'height': titleBarHeight + 'px', 'width': '100%'}\">\r\n        <view class=\"back df\" @tap=\"navBack\">\r\n          <image src=\"/static/img/back.png\" style=\"width:34rpx;height:34rpx\"></image>\r\n        </view>\r\n        <view class=\"nav-title\">{{orderInfo.status_str}}</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <view class=\"content\" :style=\"{'padding-top': statusBarHeight + titleBarHeight + 'px'}\">\r\n      <view class=\"w100p30\">\r\n        <image class=\"map-bg\" src=\"/static/img/inset/map.jpg\" mode=\"widthFix\"></image>\r\n        <view class=\"mask-bg\"></view>\r\n        <view class=\"title\">收货地址</view>\r\n        <view class=\"adds-box\">\r\n          <view class=\"adds-item\">\r\n            <view class=\"nm\">{{orderInfo.user_name}} {{orderInfo.user_mobile}}</view>\r\n            <view>{{orderInfo.user_adds}}</view>\r\n            <view v-if=\"orderInfo.express\" class=\"express\">\r\n              订单由<text user-select=\"true\">{{orderInfo.express}}：{{orderInfo.express_no}}</text>为您配送到家\r\n            </view>\r\n          </view>\r\n          <view class=\"adds-icon df\">\r\n            <view></view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"w100p30\">\r\n        <view class=\"title\">商品清单</view>\r\n        <view \r\n          v-for=\"(item, index) in orderInfo.goods\" \r\n          :key=\"index\" \r\n          class=\"goods-item\" \r\n          :data-url=\"'goods/details?id='+item.goods_id\" \r\n          @tap=\"navigateToFun\">\r\n          <image :src=\"item.product_img\" mode=\"aspectFill\"></image>\r\n          <view class=\"goods-info\">\r\n            <view class=\"t1 ohto\">{{item.goods_name}}</view>\r\n            <view class=\"t2 ohto\">{{item.product_name}}</view>\r\n            <view class=\"goods-info-bom\">\r\n              <view class=\"sum\" style=\"margin-right:20rpx\">{{item.quantity}}×</view>\r\n              <money :price=\"item.product_price\"></money>\r\n              <view v-if=\"item.status == 2\" class=\"sum\" style=\"margin-left:20rpx\">已退款</view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"w100p30\">\r\n        <view class=\"title\">订单信息</view>\r\n        <view class=\"list-item df\" style=\"margin-top:15rpx\">\r\n          <view>订单号</view>\r\n          <view class=\"list-right df\" :data-nt=\"orderInfo.order_no\">\r\n            <text class=\"zs\" user-select=\"true\">{{orderInfo.order_no}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"list-item df\">\r\n          <view>实际商品总额</view>\r\n          <view class=\"list-right df\">\r\n            <text class=\"zs\">共{{orderInfo.goods_count}}件</text>\r\n            <money :price=\"orderInfo.goods_amount\" cor=\"#999\" qs=\"28rpx\" ts=\"18rpx\"></money>\r\n          </view>\r\n        </view>\r\n        \r\n        <view v-if=\"orderInfo.discount_amount\" class=\"list-item df\">\r\n          <view>优惠金额</view>\r\n          <view class=\"list-right df\">\r\n            <text class=\"zs\">立减</text>\r\n            <money :price=\"orderInfo.discount_amount\" cor=\"#999\" qs=\"28rpx\" ts=\"18rpx\"></money>\r\n          </view>\r\n        </view>\r\n        \r\n        <view v-if=\"orderInfo.card_price\" class=\"list-item df\">\r\n          <view>卡券</view>\r\n          <view class=\"list-right df\">\r\n            <text class=\"zs\">优惠</text>\r\n            <money :price=\"orderInfo.card_price\" cor=\"#999\" qs=\"28rpx\" ts=\"18rpx\"></money>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"list-item df\">\r\n          <view>运费</view>\r\n          <view class=\"list-right df\">\r\n            <text class=\"zs\">包邮</text>\r\n            <money :price=\"orderInfo.express_amount\" cor=\"#999\" qs=\"28rpx\" ts=\"18rpx\"></money>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"list-item df\">\r\n          <view>总计</view>\r\n          <view class=\"list-right df\">\r\n            <money :price=\"orderInfo.order_amount\" qs=\"28rpx\" ts=\"18rpx\"></money>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"list-item df\">\r\n          <view>下单时间</view>\r\n          <view class=\"list-right df\">\r\n            <text class=\"zs\">{{orderInfo.create_time}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view v-if=\"orderInfo.ship_time_str\" class=\"list-item df\">\r\n          <view>发货时间</view>\r\n          <view class=\"list-right df\">\r\n            <text class=\"zs\">{{orderInfo.ship_time_str}}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view v-if=\"orderInfo.confirm_time_str\" class=\"list-item df\">\r\n          <view>确认收货时间</view>\r\n          <view class=\"list-right df\">\r\n            <text class=\"zs\">{{orderInfo.confirm_time_str}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"w100p30\">\r\n        <view class=\"title\">备注信息</view>\r\n        <view class=\"list-item\" style=\"margin-top:15rpx;display:flex\">\r\n          <view>\r\n            <text user-select=\"true\">{{orderInfo.remark ? orderInfo.remark : '无'}}</text>\r\n            <view style=\"font-size:16rpx;margin-top:10rpx;color:#999\">注：如需修改请联系客服</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"footer df\">\r\n        <button class=\"icon-box df\" open-type=\"contact\" \r\n          :send-message-title=\"orderInfo.order_no\" \r\n          :send-message-path=\"'/pages/order/details?id='+orderInfo.id\" \r\n          :show-message-card=\"true\">\r\n          <image src=\"/static/img/kf.png\"></image>\r\n          <text>客服</text>\r\n        </button>\r\n        \r\n        <view class=\"footer-btn df\">\r\n          <view v-if=\"orderInfo.status == 0 || orderInfo.status == 4 || orderInfo.status == 5\" \r\n            class=\"df btn1\" data-type=\"0\" @tap=\"editClick\">\r\n            <text>删除</text>\r\n          </view>\r\n          \r\n          <view v-if=\"!(orderInfo.status != 1 && orderInfo.status != 2 || orderInfo.pay_status != 1 && orderInfo.pay_status != 0)\" \r\n            class=\"df btn1\" data-type=\"1\" @tap=\"editClick\">\r\n            <text>取消订单</text>\r\n          </view>\r\n          \r\n          <view v-if=\"(orderInfo.pay_status == 1 || orderInfo.pay_status == 3) && orderInfo.status == 3\" \r\n            class=\"df btn1\" @tap=\"orderRefundClick(true)\">\r\n            <text>申请售后</text>\r\n          </view>\r\n          \r\n          <view v-if=\"orderInfo.status == 2\" \r\n            class=\"df btn2\" @tap=\"urgeDeliveryClick\">\r\n            <text>催发货</text>\r\n          </view>\r\n          \r\n          <view v-if=\"orderInfo.status == 3\" \r\n            class=\"df btn2\" data-type=\"2\" @tap=\"editClick\">\r\n            <text>确认收货</text>\r\n          </view>\r\n          \r\n          <view v-if=\"orderInfo.status == 4\" \r\n            class=\"df btn2\" @tap=\"openOrderNote\">\r\n            <text>评价</text>\r\n          </view>\r\n          \r\n          <view v-if=\"orderInfo.status == 1 && orderInfo.pay_status == 0\" \r\n            class=\"df btn2\" @tap=\"paymentClick\">\r\n            <image src=\"/static/img/pay.png\"></image>\r\n            <text>支付</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 申请售后弹窗 -->\r\n    <uni-popup ref=\"refundPopup\" type=\"bottom\" :safe-area=\"false\">\r\n      <view class=\"popup-box\">\r\n        <view class=\"popup-top df\">\r\n          <view class=\"popup-title\">\r\n            <view class=\"t1\">申请售后</view>\r\n            <view class=\"t2\">提交后您可以联系客服为您及时处理</view>\r\n          </view>\r\n          <view class=\"popup-close df\" @tap=\"orderRefundClick(false)\">\r\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"popup-item\">\r\n          <view class=\"popup-type df\" style=\"margin-top:20rpx\">\r\n            <view :class=\"[refundType == 0 ? 'active' : '']\" @tap=\"refundType = 0\">未收到货</view>\r\n            <view :class=\"[refundType == 1 ? 'active' : '']\" @tap=\"refundType = 1\">已收到货</view>\r\n          </view>\r\n          \r\n          <view v-if=\"refundType == 1\" class=\"popup-adds\">\r\n            <text class=\"a1\" user-select=\"true\">收件人：{{orderInfo.refund[0]}}     收件电话：{{orderInfo.refund[1]}}</text>\r\n            <text class=\"a1\" user-select=\"true\" style=\"margin-top:8rpx\">收件地址：{{orderInfo.refund[2]}}</text>\r\n            <text class=\"a2\">如您已收到货物，请通过上方收件地址寄回货物</text>\r\n          </view>\r\n          \r\n          <view v-if=\"refundType == 1\" class=\"popup-wl df\">\r\n            <input placeholder=\"寄回物流名称\" v-model=\"refundExpress\"/>\r\n            <input placeholder=\"寄回物流单号\" v-model=\"refundExpressNo\"/>\r\n          </view>\r\n          \r\n          <textarea class=\"popup-textarea\" auto-height=\"true\" placeholder=\"售后原因（最多200字）\" maxlength=\"200\" v-model=\"refundReason\"></textarea>\r\n        </view>\r\n        \r\n        <view class=\"popup-btn\" @tap=\"refundClick\">确认提交</view>\r\n      </view>\r\n    </uni-popup>\r\n    \r\n    <!-- 评价弹窗 -->\r\n    <uni-popup ref=\"notePopup\" type=\"center\">\r\n      <view class=\"note-box\">\r\n        <view class=\"note-add df\" @tap=\"toOrderNote(1)\">\r\n          <image src=\"/static/img/tw.png\"></image>\r\n          <text>图文评价</text>\r\n        </view>\r\n        <view class=\"note-add df\" @tap=\"toOrderNote(2)\">\r\n          <image src=\"/static/img/sp.png\"></image>\r\n          <text>视频评价</text>\r\n        </view>\r\n        <view class=\"note-add df\" @tap=\"toOrderNote(3)\">\r\n          <image src=\"/static/img/yw.png\"></image>\r\n          <text>音文评价</text>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n    \r\n    <!-- 提示弹窗 -->\r\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\r\n      <view class=\"tips-box df\">\r\n        <view class=\"tips-item\">{{tipsTitle}}</view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request.js'\r\nimport * as api from '@/config/api.js'\r\nimport money from '@/components/money/money.vue'\r\n\r\nconst app = getApp()\r\n\r\nexport default {\r\n  components: {\r\n    money\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\r\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\r\n      orderInfo: {\r\n        id: 0,\r\n        status_str: \"状态加载中\",\r\n        address: \"\",\r\n        adds_mobile: \"\",\r\n        adds_name: \"\",\r\n        express: \"\",\r\n        express_no: \"\",\r\n        order_no: \"\",\r\n        goods_count: 0,\r\n        goods_amount: \"0.00\",\r\n        order_amount: \"0.00\",\r\n        discount_amount: \"0.00\",\r\n        express_amount: \"0.00\",\r\n        pay_status: 0,\r\n        create_time: \"\",\r\n        ship_time_str: \"\",\r\n        confirm_time_str: \"\",\r\n        remark: \"\",\r\n        status: 0,\r\n        refund: [\"\", \"\", \"\"],\r\n        user_name: \"\",\r\n        user_mobile: \"\",\r\n        user_adds: \"\",\r\n        goods: []\r\n      },\r\n      refundType: 0,\r\n      refundExpress: \"\",\r\n      refundExpressNo: \"\",\r\n      refundReason: \"\",\r\n      tipsTitle: \"\",\r\n      \r\n      // Mock数据\r\n      mockOrderDetails: {\r\n        id: 123456,\r\n        status_str: \"待收货\",\r\n        address: \"广东省广州市天河区天河路123号\",\r\n        adds_mobile: \"13800138000\",\r\n        adds_name: \"张三\",\r\n        express: \"顺丰速运\",\r\n        express_no: \"SF1234567890123\",\r\n        order_no: \"202305201234567890\",\r\n        goods_count: 3,\r\n        goods_amount: \"599.00\",\r\n        order_amount: \"599.00\",\r\n        discount_amount: \"100.00\",\r\n        express_amount: \"0.00\",\r\n        pay_status: 1,\r\n        create_time: \"2023-05-20 12:34:56\",\r\n        ship_time_str: \"2023-05-21 10:24:36\",\r\n        confirm_time_str: \"\",\r\n        remark: \"请尽快发货，谢谢！\",\r\n        status: 3,\r\n        refund: [\"张小姐\", \"13912345678\", \"广东省广州市天河区天河路789号\"],\r\n        user_name: \"张三\",\r\n        user_mobile: \"13800138000\",\r\n        user_adds: \"广东省广州市天河区天河路123号\",\r\n        goods: [\r\n          {\r\n            goods_id: 1001,\r\n            goods_name: \"2023夏季新款连衣裙\",\r\n            product_name: \"白色 L码\",\r\n            product_img: \"/static/img/avatar.png\",\r\n            product_price: \"299.00\",\r\n            quantity: 1,\r\n            status: 0\r\n          },\r\n          {\r\n            goods_id: 1002,\r\n            goods_name: \"轻薄透气运动鞋\",\r\n            product_name: \"黑色 40码\",\r\n            product_img: \"/static/img/avatar.png\",\r\n            product_price: \"199.00\",\r\n            quantity: 1,\r\n            status: 0\r\n          },\r\n          {\r\n            goods_id: 1003,\r\n            goods_name: \"时尚斜挎小包\",\r\n            product_name: \"米色 标准\",\r\n            product_img: \"/static/img/avatar.png\",\r\n            product_price: \"101.00\",\r\n            quantity: 1,\r\n            status: 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    if (options.id) {\r\n      this.orderInfo.id = options.id\r\n      this.orderDetails()\r\n    } else {\r\n      this.opTipsPopup(\"未找到订单，请稍后重试！\", true)\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取订单详情\r\n    orderDetails() {\r\n      let that = this\r\n      \r\n      // 检查API是否可用\r\n      if (api.api && api.api.orderDetailsUrl) {\r\n        request(api.api.orderDetailsUrl, {\r\n          id: that.orderInfo.id\r\n        }).then(function(res) {\r\n          if (res.code == 200) {\r\n            that.orderInfo = res.data\r\n          } else {\r\n            that.opTipsPopup(res.msg, true)\r\n          }\r\n        })\r\n      } else {\r\n        // 使用Mock数据\r\n        setTimeout(() => {\r\n          that.orderInfo = that.mockOrderDetails\r\n        }, 500)\r\n      }\r\n    },\r\n    \r\n    // 支付订单\r\n    paymentClick() {\r\n      let that = this\r\n      \r\n      uni.showLoading({\r\n        title: \"唤起支付中..\",\r\n        mask: true\r\n      })\r\n      \r\n      // 检查API是否可用\r\n      if (api.api && api.api.orderPaymentUrl) {\r\n        request(api.api.orderPaymentUrl, {\r\n          id: that.orderInfo.id\r\n        }, \"POST\").then(function(res) {\r\n          uni.hideLoading()\r\n          \r\n          let payData = res.data\r\n          uni.requestPayment({\r\n            provider: \"weixin\",\r\n            timeStamp: payData.timestamp,\r\n            nonceStr: payData.nonceStr,\r\n            package: payData.package,\r\n            signType: payData.signType,\r\n            paySign: payData.paySign,\r\n            success: function() {\r\n              that.opTipsPopup(\"支付成功，我们会尽快为您发货 🎉\")\r\n              that.orderInfo.pay_status = 1\r\n              that.orderInfo.status = 2\r\n              that.orderInfo.status_str = \"待发货\"\r\n            }\r\n          })\r\n        })\r\n      } else {\r\n        // 模拟支付流程\r\n        setTimeout(() => {\r\n          uni.hideLoading()\r\n          \r\n          uni.showModal({\r\n            title: '模拟支付',\r\n            content: '这是一个模拟的支付流程，点击确定将模拟支付成功',\r\n            success: function(res) {\r\n              if (res.confirm) {\r\n                that.opTipsPopup(\"支付成功，我们会尽快为您发货 🎉\")\r\n                that.orderInfo.pay_status = 1\r\n                that.orderInfo.status = 2\r\n                that.orderInfo.status_str = \"待发货\"\r\n              }\r\n            }\r\n          })\r\n        }, 1000)\r\n      }\r\n    },\r\n    \r\n    // 编辑订单（删除、取消、确认收货）\r\n    editClick(e) {\r\n      let that = this\r\n      let type = e.currentTarget.dataset.type\r\n      let refund = 0\r\n      \r\n      // 订单已支付且为待发货状态时，申请售后需要退款\r\n      if (type == 1 && that.orderInfo.pay_status == 1 && that.orderInfo.status == 2) {\r\n        refund = 1\r\n      }\r\n      \r\n      uni.showLoading({\r\n        mask: true\r\n      })\r\n      \r\n      // 检查API是否可用\r\n      if (api.api && api.api.editOrderUrl) {\r\n        request(api.api.editOrderUrl, {\r\n          id: that.orderInfo.id,\r\n          type: type,\r\n          refund: refund\r\n        }, \"POST\").then(function(res) {\r\n          uni.hideLoading()\r\n          that.opTipsPopup(res.msg)\r\n          \r\n          if (res.code == 200) {\r\n            if (type == 0) {\r\n              app.globalData.isCenterPage = true\r\n              that.opTipsPopup(res.msg, true)\r\n              return\r\n            }\r\n            \r\n            if (type == 1) {\r\n              that.orderInfo.status = 0\r\n              that.orderInfo.status_str = \"已取消\"\r\n            } else if (type == 3) {\r\n              that.orderInfo.status = 4\r\n              that.orderInfo.status_str = \"待评价\"\r\n            }\r\n          }\r\n        })\r\n      } else {\r\n        // 模拟编辑流程\r\n        setTimeout(() => {\r\n          uni.hideLoading()\r\n          \r\n          if (type == 0) {\r\n            app.globalData.isCenterPage = true\r\n            that.opTipsPopup(\"订单已删除\", true)\r\n            return\r\n          }\r\n          \r\n          if (type == 1) {\r\n            that.orderInfo.status = 0\r\n            that.orderInfo.status_str = \"已取消\"\r\n            that.opTipsPopup(\"订单已取消\")\r\n          } else if (type == 2) {\r\n            that.orderInfo.status = 4\r\n            that.orderInfo.status_str = \"待评价\"\r\n            that.opTipsPopup(\"已确认收货\")\r\n          }\r\n        }, 800)\r\n      }\r\n    },\r\n    \r\n    // 提交售后申请\r\n    refundClick() {\r\n      let that = this\r\n      \r\n      if (that.refundType == 1 && !that.refundExpress) {\r\n        that.opTipsPopup(\"请填写寄回物流名称\")\r\n        return\r\n      }\r\n      \r\n      if (that.refundType == 1 && !that.refundExpressNo) {\r\n        that.opTipsPopup(\"请填写寄回物流单号\")\r\n        return\r\n      }\r\n      \r\n      if (!that.refundReason) {\r\n        that.opTipsPopup(\"请填写售后原因\")\r\n        return\r\n      }\r\n      \r\n      uni.showLoading({\r\n        mask: true\r\n      })\r\n      \r\n      // 检查API是否可用\r\n      if (api.api && api.api.refundOrderUrl) {\r\n        request(api.api.refundOrderUrl, {\r\n          id: that.orderInfo.id,\r\n          type: that.refundType,\r\n          express: that.refundExpress,\r\n          express_no: that.refundExpressNo,\r\n          reason: that.refundReason\r\n        }, \"POST\").then(function(res) {\r\n          uni.hideLoading()\r\n          that.opTipsPopup(res.msg)\r\n          \r\n          if (res.code == 200) {\r\n            that.orderInfo.pay_status = 2\r\n            that.orderInfo.status_str = that.orderInfo.status_str + \"（售后中）\"\r\n            that.$refs.refundPopup.close()\r\n          }\r\n        })\r\n      } else {\r\n        // 模拟售后申请流程\r\n        setTimeout(() => {\r\n          uni.hideLoading()\r\n          that.opTipsPopup(\"售后申请已提交，请等待商家处理\")\r\n          that.orderInfo.pay_status = 2\r\n          that.orderInfo.status_str = that.orderInfo.status_str + \"（售后中）\"\r\n          that.$refs.refundPopup.close()\r\n        }, 800)\r\n      }\r\n    },\r\n    \r\n    // 打开/关闭售后弹窗\r\n    orderRefundClick(isOpen) {\r\n      if (isOpen) {\r\n        this.$refs.refundPopup.open()\r\n      } else {\r\n        this.$refs.refundPopup.close()\r\n      }\r\n    },\r\n    \r\n    // 打开评价弹窗\r\n    openOrderNote() {\r\n      this.$refs.notePopup.open()\r\n    },\r\n    \r\n    // 跳转到评价页面\r\n    toOrderNote(type) {\r\n      uni.navigateTo({\r\n        url: \"/pages/note/add?type=\" + type + \"&oid=\" + this.orderInfo.id\r\n      })\r\n    },\r\n    \r\n    // 催发货\r\n    urgeDeliveryClick() {\r\n      this.opTipsPopup(\"操作成功，我们会尽快为您发货 🎉\")\r\n      \r\n      // 检查API是否可用\r\n      if (api.api && api.api.editOrderUrl) {\r\n        request(api.api.editOrderUrl, {\r\n          id: this.orderInfo.id,\r\n          type: 3,\r\n          refund: 0\r\n        }, \"POST\")\r\n      }\r\n    },\r\n    \r\n    // 页面跳转\r\n    navigateToFun(e) {\r\n      uni.navigateTo({\r\n        url: \"/pages/\" + e.currentTarget.dataset.url\r\n      })\r\n    },\r\n    \r\n    // 返回上一页\r\n    navBack() {\r\n      if (getCurrentPages().length > 1) {\r\n        uni.navigateBack()\r\n      } else {\r\n        uni.switchTab({\r\n          url: \"/pages/tabbar/shop\"\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 提示弹窗\r\n    opTipsPopup(msg, isBack = false) {\r\n      let that = this\r\n      that.tipsTitle = msg\r\n      that.$refs.tipsPopup.open()\r\n      \r\n      setTimeout(function() {\r\n        that.$refs.tipsPopup.close()\r\n        \r\n        if (isBack) {\r\n          that.navBack()\r\n        }\r\n      }, 2000)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\npage { background: #f8f8f8; }\r\n.container { padding-bottom: 240rpx; }\r\n.nav-box {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 99;\r\n  box-sizing: border-box;\r\n}\r\n.nav-box .back {\r\n  padding: 0 30rpx;\r\n  height: 100%;\r\n  justify-content: center;\r\n}\r\n.nav-box .nav-title {\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n}\r\n.w100p30 {\r\n  z-index: 1;\r\n  margin: 20rpx 20rpx 0;\r\n  width: calc(100% - 80rpx);\r\n  padding: 30rpx 20rpx;\r\n  border-radius: 30rpx;\r\n  background: #fff;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n.title {\r\n  width: 100%;\r\n  color: #000;\r\n  font-size: 26rpx;\r\n  font-weight: 700;\r\n}\r\n.w100p30 .map-bg {\r\n  position: absolute;\r\n  z-index: -2;\r\n  right: 0;\r\n  bottom: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.w100p30 .mask-bg {\r\n  position: absolute;\r\n  z-index: -1;\r\n  right: -2rpx;\r\n  bottom: -2rpx;\r\n  width: calc(100% - 24rpx);\r\n  height: calc(100% - 24rpx);\r\n  border: 14rpx solid #fff;\r\n  border-radius: 30rpx;\r\n  background-image: linear-gradient(to right, #fff, rgba(255, 255, 255, .9), rgba(255, 255, 255, .6));\r\n}\r\n.adds-box {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: flex-end;\r\n  justify-content: space-between;\r\n}\r\n.adds-box .adds-item {\r\n  width: calc(100% - 60rpx);\r\n  font-size: 24rpx;\r\n  font-weight: 300;\r\n}\r\n.adds-box .adds-item .nm {\r\n  padding: 20rpx 0 10rpx;\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n}\r\n.adds-item .express {\r\n  margin-top: 20rpx;\r\n  width: 100%;\r\n  font-size: 26rpx;\r\n}\r\n.adds-item .express text {\r\n  margin: 0 8rpx;\r\n  font-weight: 700;\r\n}\r\n.adds-box .adds-icon {\r\n  margin: 0 10rpx 20rpx 0;\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  background: #000;\r\n  justify-content: center;\r\n  border-radius: 20rpx 20rpx 4rpx;\r\n  box-shadow: 8rpx 8rpx 8rpx -4rpx rgba(0, 0, 0, .1);\r\n  transform: rotate(45deg);\r\n}\r\n.adds-box .adds-icon view {\r\n  width: 16rpx;\r\n  height: 16rpx;\r\n  background: #fff;\r\n  border-radius: 50%;\r\n}\r\n.goods-item {\r\n  padding-top: 30rpx;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  animation: fadeIn .45s ease;\r\n}\r\n.goods-item image {\r\n  width: 140rpx;\r\n  height: 140rpx;\r\n  border-radius: 8rpx;\r\n}\r\n.goods-item .goods-info {\r\n  width: calc(100% - 160rpx);\r\n  font-weight: 700;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n.goods-item .goods-info .t1 {\r\n  color: #000;\r\n  font-size: 26rpx;\r\n}\r\n.goods-item .goods-info .t2 {\r\n  color: #999;\r\n  font-size: 24rpx;\r\n}\r\n.goods-item .goods-info .goods-info-bom {\r\n  margin-top: 25rpx;\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n}\r\n.goods-info .goods-info-bom .sum {\r\n  color: #999;\r\n  font-size: 20rpx;\r\n}\r\n.list-item {\r\n  padding: 15rpx 0;\r\n  justify-content: space-between;\r\n  color: #000;\r\n  font-size: 24rpx;\r\n}\r\n.list-item .list-right .zs {\r\n  color: #999;\r\n  margin-right: 10rpx;\r\n}\r\n.footer {\r\n  position: fixed;\r\n  z-index: 99;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: calc(100% - 20rpx);\r\n  padding: 20rpx 10rpx;\r\n  background-color: #fff;\r\n  border-top: 2rpx solid #f5f5f5;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n.footer .icon-box {\r\n  margin: 0;\r\n  width: 80rpx !important;\r\n  height: 88rpx;\r\n  padding: 10rpx 0;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  background: rgba(255, 255, 255, 0);\r\n  position: relative;\r\n}\r\n.footer .icon-box image {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n.footer .icon-box text {\r\n  line-height: 18rpx;\r\n  font-size: 18rpx;\r\n}\r\n.footer .footer-btn {\r\n  width: calc(100% - 80rpx);\r\n  justify-content: flex-end;\r\n}\r\n.footer-btn view {\r\n  margin: 0 10rpx;\r\n  padding: 0 40rpx;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  font-size: 24rpx;\r\n  font-weight: bolder;\r\n  border-radius: 40rpx;\r\n}\r\n.footer-btn view image {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  margin-right: 10rpx;\r\n}\r\n.footer-btn .btn1 {\r\n  color: #999;\r\n  border: 2rpx solid #F5F5F5;\r\n}\r\n.footer-btn .btn2 {\r\n  color: #fff;\r\n  background: #000;\r\n  border: 2rpx solid #000;\r\n}\r\n.note-box {\r\n  padding: 15rpx;\r\n  background: #fff;\r\n  border-radius: 30rpx;\r\n}\r\n.note-box .note-add {\r\n  margin: 30rpx;\r\n  width: 400rpx;\r\n  height: 90rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n  color: #fff;\r\n  background: #000;\r\n  border-radius: 45rpx;\r\n  justify-content: center;\r\n}\r\n.note-box .note-add image {\r\n  margin-right: 10rpx;\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n}\r\n.popup-box {\r\n  width: calc(100% - 40rpx);\r\n  padding: 20rpx;\r\n  background: #fff;\r\n  border-radius: 30rpx 30rpx 0 0;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n.popup-box .popup-top {\r\n  width: calc(100% - 20rpx);\r\n  padding: 10rpx;\r\n  justify-content: space-between;\r\n}\r\n.popup-top .popup-title .t1 {\r\n  font-size: 38rpx;\r\n  font-weight: 700;\r\n}\r\n.popup-top .popup-title .t2 {\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  font-weight: 300;\r\n}\r\n.popup-top .popup-close {\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  border-radius: 50%;\r\n  background: #f8f8f8;\r\n  justify-content: center;\r\n  transform: rotate(45deg);\r\n}\r\n.popup-box .popup-btn {\r\n  margin: 40rpx 10rpx;\r\n  width: calc(100% - 20rpx);\r\n  height: 100rpx;\r\n  line-height: 100rpx;\r\n  text-align: center;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n  color: #fff;\r\n  background: #000;\r\n  border-radius: 100rpx;\r\n}\r\n.popup-item {\r\n  width: calc(100% - 20rpx);\r\n  padding: 10rpx;\r\n}\r\n.popup-item .popup-type view {\r\n  margin-right: 20rpx;\r\n  padding: 0 40rpx;\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  font-size: 20rpx;\r\n  font-weight: 700;\r\n  border-radius: 8rpx;\r\n  border: 1px solid #f5f5f5;\r\n}\r\n.popup-item .popup-type .active {\r\n  border: 1px solid #000;\r\n  background: rgba(0, 0, 0, .125);\r\n}\r\n.popup-item .popup-textarea,\r\n.popup-item .popup-adds {\r\n  margin-top: 30rpx;\r\n  width: calc(100% - 40rpx);\r\n  padding: 20rpx;\r\n  border-radius: 8rpx;\r\n  background: #f8f8f8;\r\n}\r\n.popup-item .popup-textarea {\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n  min-height: 120rpx;\r\n}\r\n.popup-item .popup-adds {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n.popup-item .popup-adds .a1 {\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n}\r\n.popup-item .popup-adds .a2 {\r\n  padding-top: 10rpx;\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  font-weight: 300;\r\n}\r\n.popup-item .popup-wl {\r\n  margin-top: 30rpx;\r\n  width: 100%;\r\n  justify-content: space-between;\r\n}\r\n.popup-item .popup-wl input {\r\n  width: calc(50% - 50rpx);\r\n  padding: 20rpx;\r\n  border-radius: 8rpx;\r\n  background: #f8f8f8;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n}\r\n.tips-box {\r\n  padding: 20rpx 30rpx;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  border-radius: 12rpx;\r\n  justify-content: center;\r\n}\r\n.tips-box .tips-item {\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  font-weight: 700;\r\n}\r\n.bf8 {\r\n  background: #f8f8f8;\r\n}\r\n.df {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.ohto {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/uniapp/vue3/pages/order/details.vue'\nwx.createPage(MiniProgramPage)"], "names": ["api.api", "request", "uni"], "mappings": ";;;;;AAwPA,MAAO,QAAO,MAAW;AAEzB,MAAM,MAAM,OAAO;AAEnB,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB,KAAK,OAAO,MAAM,mBAAmB;AAAA,MACtD,gBAAgB,KAAK,OAAO,MAAM,kBAAkB;AAAA,MACpD,WAAW;AAAA,QACT,IAAI;AAAA,QACJ,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,QACd,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ,CAAC,IAAI,IAAI,EAAE;AAAA,QACnB,WAAW;AAAA,QACX,aAAa;AAAA,QACb,WAAW;AAAA,QACX,OAAO,CAAC;AAAA,MACT;AAAA,MACD,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA;AAAA,MAGX,kBAAkB;AAAA,QAChB,IAAI;AAAA,QACJ,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,QACd,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ,CAAC,OAAO,eAAe,kBAAkB;AAAA,QACjD,WAAW;AAAA,QACX,aAAa;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,UACL;AAAA,YACE,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,aAAa;AAAA,YACb,eAAe;AAAA,YACf,UAAU;AAAA,YACV,QAAQ;AAAA,UACT;AAAA,UACD;AAAA,YACE,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,aAAa;AAAA,YACb,eAAe;AAAA,YACf,UAAU;AAAA,YACV,QAAQ;AAAA,UACT;AAAA,UACD;AAAA,YACE,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,aAAa;AAAA,YACb,eAAe;AAAA,YACf,UAAU;AAAA,YACV,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AACd,QAAI,QAAQ,IAAI;AACd,WAAK,UAAU,KAAK,QAAQ;AAC5B,WAAK,aAAa;AAAA,WACb;AACL,WAAK,YAAY,gBAAgB,IAAI;AAAA,IACvC;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,eAAe;AACb,UAAI,OAAO;AAGX,UAAIA,WAAM,OAAKA,WAAO,IAAC,iBAAiB;AACtCC,sBAAO,QAACD,WAAO,IAAC,iBAAiB;AAAA,UAC/B,IAAI,KAAK,UAAU;AAAA,QACrB,CAAC,EAAE,KAAK,SAAS,KAAK;AACpB,cAAI,IAAI,QAAQ,KAAK;AACnB,iBAAK,YAAY,IAAI;AAAA,iBAChB;AACL,iBAAK,YAAY,IAAI,KAAK,IAAI;AAAA,UAChC;AAAA,SACD;AAAA,aACI;AAEL,mBAAW,MAAM;AACf,eAAK,YAAY,KAAK;AAAA,QACvB,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACb,UAAI,OAAO;AAEXE,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAGD,UAAIF,WAAM,OAAKA,WAAO,IAAC,iBAAiB;AACtCC,sBAAO,QAACD,WAAO,IAAC,iBAAiB;AAAA,UAC/B,IAAI,KAAK,UAAU;AAAA,QACpB,GAAE,MAAM,EAAE,KAAK,SAAS,KAAK;AAC5BE,wBAAAA,MAAI,YAAY;AAEhB,cAAI,UAAU,IAAI;AAClBA,wBAAAA,MAAI,eAAe;AAAA,YACjB,UAAU;AAAA,YACV,WAAW,QAAQ;AAAA,YACnB,UAAU,QAAQ;AAAA,YAClB,SAAS,QAAQ;AAAA,YACjB,UAAU,QAAQ;AAAA,YAClB,SAAS,QAAQ;AAAA,YACjB,SAAS,WAAW;AAClB,mBAAK,YAAY,mBAAmB;AACpC,mBAAK,UAAU,aAAa;AAC5B,mBAAK,UAAU,SAAS;AACxB,mBAAK,UAAU,aAAa;AAAA,YAC9B;AAAA,WACD;AAAA,SACF;AAAA,aACI;AAEL,mBAAW,MAAM;AACfA,wBAAAA,MAAI,YAAY;AAEhBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,SAAS,SAAS,KAAK;AACrB,kBAAI,IAAI,SAAS;AACf,qBAAK,YAAY,mBAAmB;AACpC,qBAAK,UAAU,aAAa;AAC5B,qBAAK,UAAU,SAAS;AACxB,qBAAK,UAAU,aAAa;AAAA,cAC9B;AAAA,YACF;AAAA,WACD;AAAA,QACF,GAAE,GAAI;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,UAAU,GAAG;AACX,UAAI,OAAO;AACX,UAAI,OAAO,EAAE,cAAc,QAAQ;AACnC,UAAI,SAAS;AAGb,UAAI,QAAQ,KAAK,KAAK,UAAU,cAAc,KAAK,KAAK,UAAU,UAAU,GAAG;AAC7E,iBAAS;AAAA,MACX;AAEAA,oBAAAA,MAAI,YAAY;AAAA,QACd,MAAM;AAAA,OACP;AAGD,UAAIF,WAAQ,OAAGA,WAAO,IAAC,cAAc;AACnCC,sBAAO,QAACD,WAAO,IAAC,cAAc;AAAA,UAC5B,IAAI,KAAK,UAAU;AAAA,UACnB;AAAA,UACA;AAAA,QACD,GAAE,MAAM,EAAE,KAAK,SAAS,KAAK;AAC5BE,wBAAAA,MAAI,YAAY;AAChB,eAAK,YAAY,IAAI,GAAG;AAExB,cAAI,IAAI,QAAQ,KAAK;AACnB,gBAAI,QAAQ,GAAG;AACb,kBAAI,WAAW,eAAe;AAC9B,mBAAK,YAAY,IAAI,KAAK,IAAI;AAC9B;AAAA,YACF;AAEA,gBAAI,QAAQ,GAAG;AACb,mBAAK,UAAU,SAAS;AACxB,mBAAK,UAAU,aAAa;AAAA,YAC9B,WAAW,QAAQ,GAAG;AACpB,mBAAK,UAAU,SAAS;AACxB,mBAAK,UAAU,aAAa;AAAA,YAC9B;AAAA,UACF;AAAA,SACD;AAAA,aACI;AAEL,mBAAW,MAAM;AACfA,wBAAAA,MAAI,YAAY;AAEhB,cAAI,QAAQ,GAAG;AACb,gBAAI,WAAW,eAAe;AAC9B,iBAAK,YAAY,SAAS,IAAI;AAC9B;AAAA,UACF;AAEA,cAAI,QAAQ,GAAG;AACb,iBAAK,UAAU,SAAS;AACxB,iBAAK,UAAU,aAAa;AAC5B,iBAAK,YAAY,OAAO;AAAA,UAC1B,WAAW,QAAQ,GAAG;AACpB,iBAAK,UAAU,SAAS;AACxB,iBAAK,UAAU,aAAa;AAC5B,iBAAK,YAAY,OAAO;AAAA,UAC1B;AAAA,QACD,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACZ,UAAI,OAAO;AAEX,UAAI,KAAK,cAAc,KAAK,CAAC,KAAK,eAAe;AAC/C,aAAK,YAAY,WAAW;AAC5B;AAAA,MACF;AAEA,UAAI,KAAK,cAAc,KAAK,CAAC,KAAK,iBAAiB;AACjD,aAAK,YAAY,WAAW;AAC5B;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,YAAY,SAAS;AAC1B;AAAA,MACF;AAEAA,oBAAAA,MAAI,YAAY;AAAA,QACd,MAAM;AAAA,OACP;AAGD,UAAIF,WAAM,OAAKA,WAAO,IAAC,gBAAgB;AACrCC,sBAAO,QAACD,WAAO,IAAC,gBAAgB;AAAA,UAC9B,IAAI,KAAK,UAAU;AAAA,UACnB,MAAM,KAAK;AAAA,UACX,SAAS,KAAK;AAAA,UACd,YAAY,KAAK;AAAA,UACjB,QAAQ,KAAK;AAAA,QACd,GAAE,MAAM,EAAE,KAAK,SAAS,KAAK;AAC5BE,wBAAAA,MAAI,YAAY;AAChB,eAAK,YAAY,IAAI,GAAG;AAExB,cAAI,IAAI,QAAQ,KAAK;AACnB,iBAAK,UAAU,aAAa;AAC5B,iBAAK,UAAU,aAAa,KAAK,UAAU,aAAa;AACxD,iBAAK,MAAM,YAAY,MAAM;AAAA,UAC/B;AAAA,SACD;AAAA,aACI;AAEL,mBAAW,MAAM;AACfA,wBAAAA,MAAI,YAAY;AAChB,eAAK,YAAY,iBAAiB;AAClC,eAAK,UAAU,aAAa;AAC5B,eAAK,UAAU,aAAa,KAAK,UAAU,aAAa;AACxD,eAAK,MAAM,YAAY,MAAM;AAAA,QAC9B,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB,QAAQ;AACvB,UAAI,QAAQ;AACV,aAAK,MAAM,YAAY,KAAK;AAAA,aACvB;AACL,aAAK,MAAM,YAAY,MAAM;AAAA,MAC/B;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACd,WAAK,MAAM,UAAU,KAAK;AAAA,IAC3B;AAAA;AAAA,IAGD,YAAY,MAAM;AAChBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0BAA0B,OAAO,UAAU,KAAK,UAAU;AAAA,OAChE;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB;AAClB,WAAK,YAAY,mBAAmB;AAGpC,UAAIF,WAAQ,OAAGA,WAAO,IAAC,cAAc;AACnCC,sBAAO,QAACD,WAAO,IAAC,cAAc;AAAA,UAC5B,IAAI,KAAK,UAAU;AAAA,UACnB,MAAM;AAAA,UACN,QAAQ;AAAA,QACT,GAAE,MAAM;AAAA,MACX;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,GAAG;AACfE,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,YAAY,EAAE,cAAc,QAAQ;AAAA,OAC1C;AAAA,IACF;AAAA;AAAA,IAGD,UAAU;AACR,UAAI,gBAAe,EAAG,SAAS,GAAG;AAChCA,sBAAAA,MAAI,aAAa;AAAA,aACZ;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,SACN;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,KAAK,SAAS,OAAO;AAC/B,UAAI,OAAO;AACX,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU,KAAK;AAE1B,iBAAW,WAAW;AACpB,aAAK,MAAM,UAAU,MAAM;AAE3B,YAAI,QAAQ;AACV,eAAK,QAAQ;AAAA,QACf;AAAA,MACD,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3mBA,GAAG,WAAW,eAAe;"}