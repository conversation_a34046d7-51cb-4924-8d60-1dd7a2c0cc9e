"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const common_assets = require("../../common/assets.js");
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const money = () => "../../components/money/money.js";
const tabbar = () => "../../components/tabbar/tabbar.js";
const mockData = {
  // 商品分类数据
  classifyList: [
    { id: 0, name: "推荐" },
    { id: 1, name: "服饰" },
    { id: 2, name: "美妆" },
    { id: 3, name: "数码" },
    { id: 4, name: "家居" },
    { id: 5, name: "食品" },
    { id: 6, name: "母婴" },
    { id: 7, name: "运动" }
  ],
  // 轮播图数据
  bannerList: [
    {
      id: 1,
      cover: "/static/img/avatar.png",
      type: 1,
      // 普通链接
      type_url: "goods/details?id=1"
    },
    {
      id: 2,
      cover: "/static/img/avatar.png",
      type: 2,
      // 商品
      type_url: "goods/details?id=2"
    }
  ],
  // 状态信息
  statusInfo: {
    is_order: true,
    is_card: true,
    cart_count: 5
  },
  // 商品列表 - 按分类
  goodsList: {
    // 推荐商品
    0: [
      {
        id: 1,
        name: "2023新款连衣裙夏季气质淑女雪纺长裙显瘦温柔风长款仙女裙",
        imgs: ["/static/img/avatar.png", "/static/img/avatar.png"],
        product: {
          price: 199,
          line_price: 298
        },
        buy: 328,
        cart: 56,
        browse: 1245,
        tags: ["夏季热卖", "气质淑女"]
      },
      {
        id: 2,
        name: "轻奢品牌真皮小方包2023新款潮女包单肩斜挎包时尚百搭链条包",
        imgs: ["/static/img/avatar.png", "/static/img/avatar.png"],
        product: {
          price: 398,
          line_price: 658
        },
        buy: 156,
        cart: 43,
        browse: 876,
        tags: ["轻奢", "真皮"]
      },
      {
        id: 3,
        name: "超薄智能手表男士多功能运动防水电子表学生特种兵机械男表",
        imgs: ["/static/img/avatar.png", "/static/img/avatar.png"],
        product: {
          price: 299,
          line_price: 399
        },
        buy: 268,
        cart: 35,
        browse: 1024,
        tags: ["智能手表", "防水"]
      },
      {
        id: 4,
        name: "原创设计师品牌女装小香风外套春秋季新款高端复古港风小个子西装",
        imgs: ["/static/img/avatar.png", "/static/img/avatar.png"],
        product: {
          price: 458,
          line_price: 698
        },
        buy: 126,
        cart: 28,
        browse: 756,
        tags: ["设计师品牌", "小香风"]
      }
    ],
    // 服饰类商品
    1: [
      {
        id: 11,
        name: "法式复古裙山本风连衣裙女春秋宽松长袖气质长裙温柔风碎花裙子",
        imgs: ["/static/img/avatar.png", "/static/img/avatar.png"],
        product: {
          price: 189,
          line_price: 289
        },
        buy: 213,
        cart: 45,
        browse: 980,
        tags: ["法式复古", "碎花裙"]
      },
      {
        id: 12,
        name: "小众设计感西装外套女2023春装新款韩版宽松休闲西服上衣",
        imgs: ["/static/img/avatar.png", "/static/img/avatar.png"],
        product: {
          price: 258,
          line_price: 428
        },
        buy: 109,
        cart: 32,
        browse: 765,
        tags: ["小众设计", "西装外套"]
      }
    ],
    // 美妆类商品
    2: [
      {
        id: 21,
        name: "日本进口资生堂红腰子精华保湿补水抗皱紧致淡斑修护精华液",
        imgs: ["/static/img/avatar.png", "/static/img/avatar.png"],
        product: {
          price: 699,
          line_price: 998
        },
        buy: 567,
        cart: 89,
        browse: 2310,
        tags: ["日本进口", "抗皱"]
      }
    ]
  }
};
const _sfc_main = {
  components: {
    uniLoadMore,
    lazyImage,
    money,
    tabbar
  },
  data() {
    return {
      statusBarHeight: 20,
      titleBarHeight: 44,
      appCard: false,
      scrollTop: 0,
      currentMsg: 0,
      isOrder: false,
      isCard: false,
      cartCount: 0,
      classifyList: [],
      classifyIdx: 0,
      classifyScroll: true,
      bannerList: [],
      swiperIdx: 0,
      isThrottling: false,
      list: [],
      page: 1,
      isEmpty: false,
      loadStatus: "more",
      useMockData: true
      // 开发环境下使用mock数据
    };
  },
  async onLoad() {
    try {
      if (typeof common_vendor.index.showShareMenu === "function") {
        common_vendor.index.showShareMenu({
          withShareTicket: true,
          menus: ["shareAppMessage", "shareTimeline"]
        });
      }
    } catch (e) {
      common_vendor.index.__f__("warn", "at pages/index/shop.vue:283", "showShareMenu not supported on this platform:", e);
    }
    this.$store.commit("SET_CURRENT_MSG", false);
    if (this.$onLaunched) {
      await this.$onLaunched;
    }
    this.$store.commit("SET_CURRENT_MSG", true);
    this.getUserSocialData();
    if (this.$store.state.isClassify) {
      this.getClassify();
    }
    this.getBanner();
    this.goodsList();
  },
  onShow() {
    var _a;
    if (this.$store.state.isCurrentMsg) {
      this.userAvatar = ((_a = common_vendor.index.getStorageSync("userInfo")) == null ? void 0 : _a.avatar) || "/static/img/tabbar/4-hl.png";
      this.getUserSocialData();
      this.getBanner();
    }
  },
  onPullDownRefresh() {
    this.refreshPath();
  },
  onPageScroll(e) {
    let that = this;
    let scrollTop = parseInt(e.scrollTop);
    if (that.scrollTop > scrollTop) {
      that.classifyScroll = true;
    } else if (that.scrollTop <= scrollTop && scrollTop > 0) {
      that.classifyScroll = false;
    }
    setTimeout(function() {
      that.scrollTop = scrollTop;
    }, 300);
  },
  onReachBottom() {
    if (this.list && this.list.length) {
      this.loadStatus = "loading";
      this.page = this.page + 1;
      this.goodsList();
    }
  },
  onShareAppMessage() {
    var _a, _b;
    return {
      title: ((_a = this.$store.state.appXx) == null ? void 0 : _a[1]) || "小程序示例",
      imageUrl: ((_b = this.$store.state.appXx) == null ? void 0 : _b[2]) || "/static/img/avatar.png"
    };
  },
  onShareTimeline() {
    var _a, _b;
    return {
      title: ((_a = this.$store.state.appXx) == null ? void 0 : _a[1]) || "小程序示例",
      imageUrl: ((_b = this.$store.state.appXx) == null ? void 0 : _b[2]) || "/static/img/avatar.png"
    };
  },
  methods: {
    // 刷新页面数据
    refreshPath() {
      common_vendor.index.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
      this.isThrottling = false;
      if (this.$store.state.isClassify) {
        this.getClassify();
      }
      this.getBanner();
      this.page = 1;
      this.goodsList();
      common_vendor.index.stopPullDownRefresh();
    },
    // 获取轮播图和状态信息
    getBanner() {
      let that = this;
      if (this.useMockData) {
        setTimeout(() => {
          that.bannerList = mockData.bannerList;
          that.isOrder = mockData.statusInfo.is_order;
          that.isCard = mockData.statusInfo.is_card;
          that.cartCount = mockData.statusInfo.cart_count;
        }, 300);
        return;
      }
      this.$util.request(this.$api.bannerUrl).then(function(res) {
        if (res && res.code == 200) {
          that.bannerList = res.data.banner || [];
          that.isOrder = res.data.is_order || false;
          that.isCard = res.data.is_card || false;
          that.cartCount = res.data.cart_count || 0;
        }
      }).catch(function(err) {
        common_vendor.index.__f__("log", "at pages/index/shop.vue:395", "获取轮播图失败", err);
        that.bannerList = mockData.bannerList;
        that.isOrder = mockData.statusInfo.is_order;
        that.isCard = mockData.statusInfo.is_card;
        that.cartCount = mockData.statusInfo.cart_count;
      });
    },
    // 获取商品分类
    getClassify() {
      let that = this;
      if (this.useMockData) {
        setTimeout(() => {
          that.classifyList = mockData.classifyList;
        }, 300);
        return;
      }
      this.$util.request(this.$api.classifyHomeUrl).then(function(res) {
        if (res && res.code == 200) {
          that.classifyList = res.data || [];
        }
      }).catch(function(err) {
        common_vendor.index.__f__("log", "at pages/index/shop.vue:421", "获取商品分类失败", err);
        that.classifyList = mockData.classifyList;
      });
    },
    // 获取商品列表
    goodsList() {
      let that = this;
      that.isEmpty = false;
      if (this.useMockData) {
        setTimeout(() => {
          const categoryId = that.classifyIdx;
          let mockResult = mockData.goodsList[categoryId] || mockData.goodsList[0];
          if (that.page > 1) {
            if (that.page === 2 && mockResult.length > 2) {
              mockResult = mockResult.slice(0, 2);
            } else {
              mockResult = [];
            }
          }
          if (mockResult.length > 0) {
            if (that.page === 1) {
              that.list = mockResult;
            } else {
              that.list = that.list.concat(mockResult);
            }
            that.isEmpty = false;
          } else if (that.page === 1) {
            that.isEmpty = true;
            that.list = [];
          } else {
            that.loadStatus = "no-more";
          }
          that.isThrottling = true;
        }, 500);
        return;
      }
      let classifyId = that.classifyList.length ? that.classifyList[that.classifyIdx].id : 0;
      let params = {
        page: that.page || 1,
        classify_id: classifyId
      };
      this.$util.request(this.$api.goodsListUrl, params).then(function(res) {
        that.isThrottling = true;
        that.loadStatus = "more";
        if (res && res.data && res.data.data && res.data.data.length > 0) {
          if (that.page == 1) {
            that.list = res.data.data;
          } else {
            that.list = that.list.concat(res.data.data);
          }
          that.page = res.data.current_page || that.page;
        } else if (that.page == 1) {
          that.isEmpty = true;
          that.list = [];
        } else {
          that.loadStatus = "no-more";
        }
      }).catch(function(err) {
        common_vendor.index.__f__("log", "at pages/index/shop.vue:498", "获取商品列表失败", err);
        that.isThrottling = true;
        const categoryId = that.classifyIdx;
        const mockResult = mockData.goodsList[categoryId] || mockData.goodsList[0];
        if (that.page === 1) {
          that.list = mockResult;
          that.isEmpty = mockResult.length === 0;
        } else {
          that.loadStatus = "no-more";
        }
      });
    },
    // 分类切换
    classifyClick(e) {
      if (!e || !e.currentTarget || !e.currentTarget.dataset)
        return;
      this.isThrottling = false;
      this.classifyIdx = parseInt(e.currentTarget.dataset.idx) || 0;
      this.page = 1;
      this.goodsList();
      if (this.classifyIdx == 0) {
        this.getBanner();
        if (this.$store.state.isClassify) {
          this.getClassify();
        }
      }
    },
    // 轮播图点击
    bannerClick(e) {
      if (!e || !e.currentTarget || !e.currentTarget.dataset)
        return;
      let url = e.currentTarget.dataset.url;
      if (url) {
        common_vendor.index.navigateTo({
          url
        });
      }
    },
    // 轮播图切换
    swiperChange(e) {
      this.swiperIdx = e.detail.current || 0;
    },
    // 页面跳转
    navigateToFun(e) {
      if (!e || !e.currentTarget || !e.currentTarget.dataset || !e.currentTarget.dataset.url)
        return;
      common_vendor.index.navigateTo({
        url: "/pages/" + e.currentTarget.dataset.url
      });
    },
    // 获取通知消息数量
    getUserSocialData() {
      let that = this;
      if (this.useMockData) {
        setTimeout(() => {
          that.currentMsg = 3;
        }, 300);
        return;
      }
      api_social.getUserSocialInfo().then(function(res) {
        if (res && (res.code == 200 || res.status == 200) && res.data) {
          if (res.data.service_num !== void 0) {
            that.currentMsg = res.data.service_num;
          }
        }
      }).catch(function(err) {
        common_vendor.index.__f__("error", "at pages/index/shop.vue:579", "获取消息数量失败", err);
        that.currentMsg = 0;
      });
    }
  }
};
if (!Array) {
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _component_lazy_image = common_vendor.resolveComponent("lazy-image");
  const _component_money = common_vendor.resolveComponent("money");
  const _component_tabbar = common_vendor.resolveComponent("tabbar");
  (_easycom_uni_load_more2 + _component_lazy_image + _component_money + _component_tabbar)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
if (!Math) {
  _easycom_uni_load_more();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$1,
    b: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    c: $data.classifyList.length > 1
  }, $data.classifyList.length > 1 ? {
    d: common_assets._imports_1$1,
    e: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {}, {
    f: common_assets._imports_2$1,
    g: $data.isOrder
  }, $data.isOrder ? {} : {}, {
    h: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    i: $data.appCard || $data.isCard
  }, $data.appCard || $data.isCard ? common_vendor.e({
    j: common_assets._imports_3$2,
    k: $data.isCard
  }, $data.isCard ? {} : {}, {
    l: common_vendor.n($data.isCard ? "animate" : ""),
    m: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  }) : {}, {
    n: common_assets._imports_4,
    o: $data.cartCount > 0
  }, $data.cartCount > 0 ? {
    p: common_vendor.t($data.cartCount)
  } : {}, {
    q: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    r: $data.titleBarHeight + "px",
    s: common_vendor.f($data.classifyList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: index,
        c: index == $data.classifyIdx ? "#000" : "#999",
        d: index == $data.classifyIdx ? "bold" : "400",
        e: index,
        f: common_vendor.o((...args) => $options.classifyClick && $options.classifyClick(...args), index)
      };
    }),
    t: common_assets._imports_1$1,
    v: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    w: $data.classifyList.length > 1 && $data.classifyScroll ? "68rpx" : "0",
    x: $data.statusBarHeight + "px"
  }, {
    y: common_vendor.p({
      status: "loading"
    })
  }, {
    z: $data.isThrottling || $data.loadStatus == "loading" ? "0px" : "60rpx",
    A: $data.bannerList.length > 0 && 0 == $data.classifyIdx
  }, $data.bannerList.length > 0 && 0 == $data.classifyIdx ? {
    B: common_vendor.f($data.bannerList, (item, index, i0) => {
      return {
        a: "33b4e39d-1-" + i0,
        b: common_vendor.p({
          src: item.cover
        }),
        c: index,
        d: item.type_url,
        e: common_vendor.o((...args) => $options.bannerClick && $options.bannerClick(...args), index)
      };
    }),
    C: common_vendor.o((...args) => $options.swiperChange && $options.swiperChange(...args)),
    D: common_vendor.f($data.bannerList.length, (item, index, i0) => {
      return {
        a: index,
        b: common_vendor.n($data.swiperIdx == index ? "active" : "")
      };
    })
  } : {}, {
    E: !$data.isEmpty
  }, !$data.isEmpty ? {
    F: common_vendor.f($data.list, (item, index, i0) => {
      return {
        a: "33b4e39d-2-" + i0,
        b: common_vendor.p({
          src: item.imgs[0]
        }),
        c: common_vendor.t(item.name),
        d: "33b4e39d-3-" + i0,
        e: common_vendor.p({
          type: 1,
          price: item.product.price
        }),
        f: common_vendor.t(item.product.line_price),
        g: common_vendor.t(item.buy ? item.buy + "人已买" : item.cart + item.browse + "人想买"),
        h: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        i: index,
        j: "goods/details?id=" + item.id,
        k: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), index)
      };
    })
  } : {}, {
    G: $data.isEmpty
  }, $data.isEmpty ? {
    H: common_assets._imports_5$1
  } : {}, {
    I: common_vendor.p({
      status: $data.loadStatus
    })
  }, {
    J: $data.classifyList.length > 1 ? "calc(" + ($data.statusBarHeight + $data.titleBarHeight) + "px + 68rpx)" : "calc(" + ($data.statusBarHeight + $data.titleBarHeight) + "px)"
  }, {
    K: common_vendor.p({
      currentPage: 1,
      currentMsg: $data.currentMsg
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 7;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/shop.js.map
