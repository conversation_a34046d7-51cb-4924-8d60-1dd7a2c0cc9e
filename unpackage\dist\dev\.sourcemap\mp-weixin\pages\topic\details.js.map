{"version": 3, "file": "details.js", "sources": ["pages/topic/details.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdG9waWMvZGV0YWlscy52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 导航栏 -->\r\n    <view class=\"nav-box df\" :style=\"{'padding-top': statusBarHeight + 'px', 'background': 'rgba(255, 255, 255,' + navbarTrans + ')'}\">\r\n      <view class=\"nav-back df\" :style=\"{'height': titleBarHeight + 'px'}\" @tap=\"navBack\">\r\n        <image src=\"/static/img/back.png\" style=\"width:34rpx;height:34rpx\"></image>\r\n      </view>\r\n      <view v-if=\"navbarTrans == 1\" class=\"nav-title ohto\">{{ topicInfo.title || '话题详情' }}</view>\r\n    </view>\r\n\r\n    <!-- 话题头部区域 -->\r\n    <view class=\"topic-header\">\r\n      <!-- 主背景容器 -->\r\n      <view class=\"images-box df\">\r\n        <!-- 话题背景图片容器 -->\r\n        <view class=\"topic-image-container\">\r\n          <lazy-image\r\n            v-if=\"topicInfo.cover_image\"\r\n            :src=\"topicInfo.cover_image\"\r\n            class=\"topic-bg-image\"\r\n            @tap=\"previewTopicImage\">\r\n          </lazy-image>\r\n\r\n          <!-- 背景遮罩层 -->\r\n          <view class=\"bg-overlay\"></view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 话题信息叠加层 -->\r\n      <view class=\"topic-info-overlay\" :style=\"{'padding-top': statusBarHeight + titleBarHeight + 160 + 'rpx'}\">\r\n        <view class=\"topic-main-info df\">\r\n          <!-- 话题图标 -->\r\n          <view class=\"topic-icon\">\r\n            <view class=\"icon-wrapper\">\r\n              <image src=\"/static/img/topic_icon.png\" class=\"topic-icon-img\" mode=\"aspectFit\"></image>\r\n            </view>\r\n          </view>\r\n\r\n          <!-- 话题名称和统计信息 -->\r\n          <view class=\"topic-details\">\r\n            <view class=\"topic-name\">\r\n              <text v-if=\"!showLoading\">{{ topicInfo.title || '话题加载中...' }}</text>\r\n              <view v-else class=\"skeleton-text\"></view>\r\n            </view>\r\n\r\n          </view>\r\n\r\n          <!-- 分享按钮 -->\r\n          <view class=\"topic-share-btn\" @tap=\"shareClick(true)\">\r\n            <view class=\"share-btn-wrapper\">\r\n              <image src=\"/static/img/fx1.png\" class=\"share-icon\" mode=\"aspectFit\"></image>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 话题描述 -->\r\n        <view class=\"topic-description\" v-if=\"!showLoading && topicInfo.description\">\r\n          <text>{{ topicInfo.description }}</text>\r\n        </view>\r\n\r\n\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 分类标签 -->\r\n    <view class=\"bar-box df\">\r\n      <view class=\"bar-left df\">\r\n        <view\r\n          v-for=\"(item, index) in barList\"\r\n          :key=\"index\"\r\n          class=\"bar-item\"\r\n          @tap=\"barClick\"\r\n          :data-idx=\"index\">\r\n          <view class=\"bar-item-content\">\r\n            <text :style=\"{\r\n              'color': index == barIdx ? '#000' : '#999',\r\n              'font-size': index == barIdx ? '28rpx' : '26rpx'\r\n            }\">{{ item }}</text>\r\n            <view :class=\"['bar-line', { active: index == barIdx }]\"></view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 话题统计信息 -->\r\n      <view class=\"topic-stats-right df\">\r\n        <text v-if=\"!showLoading\" class=\"stat-item\">{{ topicInfo.post_count || 0 }}篇</text>\r\n        <text v-if=\"!showLoading\" class=\"stat-divider\">·</text>\r\n        <text v-if=\"!showLoading\" class=\"stat-item\">{{ topicInfo.view_count || 0 }}浏览</text>\r\n        <!-- 骨架屏 -->\r\n        <view v-if=\"showLoading\" class=\"skeleton-stats\">\r\n          <view class=\"skeleton-item\"></view>\r\n          <view class=\"skeleton-item\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 内容区域 -->\r\n    <view class=\"content-area\">\r\n      <!-- 加载中提示 -->\r\n      <view v-if=\"showLoading\" class=\"loading-container\">\r\n        <view class=\"loading-indicator\"></view>\r\n      </view>\r\n\r\n      <!-- 空状态 -->\r\n      <view v-if=\"isEmpty\" class=\"empty-box df\">\r\n        <image src=\"/static/img/empty.png\"/>\r\n        <view class=\"e1\">{{ (topicInfo.title || '该话题') }} 暂无动态</view>\r\n        <view class=\"e2\">快来发布第一篇相关动态吧</view>\r\n      </view>\r\n\r\n      <!-- 动态列表 -->\r\n      <view v-else :class=\"[isWaterfall ? 'dynamic-box' : '']\">\r\n        <!-- 瀑布流布局 -->\r\n        <waterfall v-if=\"isWaterfall\" :note=\"list\" :page=\"page\"></waterfall>\r\n\r\n        <!-- 普通列表布局 -->\r\n        <block v-else>\r\n          <card-gg\r\n            v-for=\"(item, index) in list\"\r\n            :key=\"index\"\r\n            @likeback=\"likeClick\"\r\n            :item=\"item\"\r\n            :idx=\"index\"\r\n            @update=\"onCardUpdate\"\r\n          ></card-gg>\r\n        </block>\r\n      </view>\r\n\r\n      <!-- 加载更多 -->\r\n      <view v-if=\"list.length > 0 && loadStatus === 'noMore'\" style=\"text-align: center; padding: 20rpx 0; color: #999; font-size: 24rpx;\">\r\n        没有更多数据了\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 分享组件 -->\r\n    <share-component\r\n      :show=\"showShare\"\r\n      :noteInfo=\"topicShareInfo\"\r\n      :userId=\"userId\"\r\n      @close=\"closeShare\"\r\n      @share=\"handleShare\"\r\n      @dislike=\"handleDislike\"\r\n      @report=\"handleReport\">\r\n    </share-component>\r\n\r\n    <!-- 提示弹窗 -->\r\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\r\n      <view class=\"tips-box df\">\r\n        <view class=\"tips-item\">{{ tipsTitle }}</view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'\r\nimport { onLoad, onShow, onHide, onPullDownRefresh, onReachBottom, onPageScroll, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'\r\nimport { useStore } from 'vuex'\r\nimport lazyImage from '@/components/lazyImage/lazyImage.vue'\r\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\r\nimport waterfall from '@/components/waterfall/waterfall.vue'\r\nimport cardGg from '@/components/card-gg/card-gg.vue'\r\nimport shareComponent from '@/components/share/index.vue'\r\nimport { getTopicList, getTopicDetail, getTopicDynamicList } from '@/api/social'\r\n\r\n// 定义组件名称\r\ndefineOptions({\r\n  name: 'TopicDetails'\r\n})\r\n\r\n// 使用store\r\nconst store = useStore()\r\n\r\n// 计算属性\r\nconst statusBarHeight = computed(() => store.state.statusBarHeight || 20)\r\nconst titleBarHeight = computed(() => store.state.titleBarHeight || 44)\r\nconst userInfo = computed(() => store.state.app.userInfo)\r\nconst userId = computed(() => userInfo.value?.uid || 0)\r\n\r\n// 响应式数据\r\nconst navbarTrans = ref(0)\r\nconst barList = ref([\"最新\", \"热门\"])\r\nconst barIdx = ref(0)\r\nconst topicId = ref(0)\r\nconst topicInfo = reactive({\r\n  id: 0,\r\n  title: '',\r\n  description: '',\r\n  cover_image: '',\r\n  post_count: 0,\r\n  view_count: 0\r\n})\r\nconst list = ref([])\r\nconst page = ref(1)\r\nconst limit = ref(10)\r\nconst totalCount = ref(0)\r\nconst isThrottling = ref(true)\r\nconst isEmpty = ref(false)\r\nconst loadStatus = ref(\"more\")\r\nconst tipsTitle = ref(\"\")\r\nconst isWaterfall = ref(false)\r\nconst showLoading = ref(true)\r\nconst loadingTimer = ref(null)\r\nconst debounceTimer = ref(null)\r\nconst showShare = ref(false)\r\n\r\n// 计算属性 - 话题分享信息\r\nconst topicShareInfo = computed(() => {\r\n  return {\r\n    id: topicInfo.id || topicId.value,\r\n    title: topicInfo.title,\r\n    content: topicInfo.description,\r\n    image: topicInfo.cover_image,\r\n    type: 'topic',\r\n    share_url: `/pages/topic/details?id=${topicId.value}`\r\n  }\r\n})\r\n\r\n// 初始化页面\r\nconst initPage = (options) => {\r\n  // 开启分享功能 - 仅在小程序环境中有效\r\n  // #ifdef MP\r\n  uni.showShareMenu()\r\n  // #endif\r\n\r\n  if (options && options.id) {\r\n    topicId.value = parseInt(options.id)\r\n    loadTopicDetail()\r\n    loadTopicDynamicList(true)\r\n  } else {\r\n    uni.showToast({\r\n      title: '话题ID不能为空',\r\n      icon: 'none'\r\n    })\r\n    setTimeout(() => {\r\n      uni.navigateBack()\r\n    }, 1500)\r\n  }\r\n}\r\n// 预览话题背景图片\r\nconst previewTopicImage = () => {\r\n  if (topicInfo.cover_image) {\r\n    uni.previewImage({\r\n      current: topicInfo.cover_image,\r\n      urls: [topicInfo.cover_image]\r\n    })\r\n  }\r\n}\r\n\r\n// 切换分类标签\r\nconst barClick = (e) => {\r\n  const idx = parseInt(e.currentTarget.dataset.idx)\r\n  if (barIdx.value === idx || !isThrottling.value) return\r\n\r\n  barIdx.value = idx\r\n  page.value = 1\r\n  list.value = []\r\n  isEmpty.value = false\r\n  loadStatus.value = 'more'\r\n\r\n  loadTopicDynamicList(true)\r\n}\r\n\r\n// 获取话题详情\r\nconst loadTopicDetail = async () => {\r\n  showLoading.value = true\r\n\r\n  try {\r\n    const res = await getTopicDetail(topicId.value)\r\n    showLoading.value = false\r\n\r\n    if (res.status === 200 && res.data) {\r\n      Object.assign(topicInfo, {\r\n        ...res.data,\r\n        id: res.data.id || topicId.value\r\n      })\r\n    } else {\r\n      uni.showToast({\r\n        title: res.msg || \"获取话题详情失败\",\r\n        icon: 'none'\r\n      })\r\n    }\r\n  } catch (error) {\r\n    showLoading.value = false\r\n    // 如果话题详情接口失败，回退到话题列表接口\r\n    try {\r\n      const res = await getTopicList({\r\n        page: 1,\r\n        limit: 50,\r\n        keyword: ''\r\n      })\r\n\r\n      if (res.status === 200 && res.data && res.data.list) {\r\n        const topic = res.data.list.find(item => item.id == topicId.value)\r\n        if (topic) {\r\n          Object.assign(topicInfo, {\r\n            ...topic,\r\n            id: topic.id || topicId.value\r\n          })\r\n        } else {\r\n          uni.showToast({\r\n            title: \"话题不存在\",\r\n            icon: 'none'\r\n          })\r\n        }\r\n      } else {\r\n        uni.showToast({\r\n          title: res.msg || \"获取话题详情失败\",\r\n          icon: 'none'\r\n        })\r\n      }\r\n    } catch (listErr) {\r\n      showLoading.value = false\r\n      uni.showToast({\r\n        title: \"网络错误，请稍后重试\",\r\n        icon: 'none'\r\n      })\r\n      setTimeout(() => {\r\n        uni.navigateBack()\r\n      }, 2000)\r\n    }\r\n  }\r\n}\r\n\r\n// 获取话题下的动态\r\nconst loadTopicDynamicList = async (isRefresh = false) => {\r\n  if (!isThrottling.value) return\r\n\r\n  isThrottling.value = false\r\n  showLoading.value = true\r\n\r\n  // 清空加载定时器\r\n  if (loadingTimer.value) {\r\n    clearTimeout(loadingTimer.value)\r\n    loadingTimer.value = null\r\n  }\r\n\r\n  // 设置最小加载时间\r\n  loadingTimer.value = setTimeout(() => {\r\n    showLoading.value = false\r\n  }, 800)\r\n\r\n  try {\r\n    const res = await getTopicDynamicList({\r\n      topic_id: topicId.value,\r\n      page: page.value,\r\n      limit: limit.value,\r\n      sort: barIdx.value === 0 ? 'latest' : 'hot'\r\n    })\r\n\r\n    // 清除加载定时器\r\n    if (loadingTimer.value) {\r\n      clearTimeout(loadingTimer.value)\r\n      loadingTimer.value = null\r\n    }\r\n    showLoading.value = false\r\n\r\n    if (res.status === 200 && res.data) {\r\n      const data = res.data\r\n      const newList = data.list || []\r\n\r\n      if (isRefresh || page.value === 1) {\r\n        list.value = newList\r\n      } else {\r\n        list.value = [...list.value, ...newList]\r\n      }\r\n\r\n      totalCount.value = data.total || 0\r\n      isEmpty.value = list.value.length === 0\r\n\r\n      // 判断是否还有更多数据\r\n      const hasMore = page.value * limit.value < totalCount.value\r\n      loadStatus.value = hasMore ? 'more' : 'noMore'\r\n\r\n      // 恢复节流状态\r\n      isThrottling.value = true\r\n\r\n      // 停止下拉刷新\r\n      if (isRefresh) {\r\n        uni.stopPullDownRefresh()\r\n      }\r\n    } else {\r\n      handleError(res.msg || '获取话题动态失败')\r\n    }\r\n  } catch (err) {\r\n    // 清除加载定时器\r\n    if (loadingTimer.value) {\r\n      clearTimeout(loadingTimer.value)\r\n      loadingTimer.value = null\r\n    }\r\n    showLoading.value = false\r\n    isThrottling.value = true\r\n\r\n    // 停止下拉刷新\r\n    if (isRefresh) {\r\n      uni.stopPullDownRefresh()\r\n    }\r\n\r\n    if (page.value === 1) {\r\n      isEmpty.value = true\r\n    }\r\n    handleError(err, '获取话题动态失败，请稍后重试')\r\n  }\r\n}\r\n\r\n\r\n// 处理错误\r\nconst handleError = (error, defaultMessage = '操作失败') => {\r\n  console.error('错误详情:', error)\r\n  const message = error?.msg || error?.message || defaultMessage\r\n  uni.showToast({\r\n    title: message,\r\n    icon: 'none',\r\n    duration: 2000\r\n  })\r\n}\r\n\r\n// 返回上一页\r\nconst navBack = () => {\r\n  uni.navigateBack()\r\n}\r\n\r\n// 点赞回调\r\nconst likeClick = (data) => {\r\n  if (data && data.idx !== undefined) {\r\n    list.value[data.idx] = data.item\r\n  }\r\n}\r\n\r\n// 卡片更新回调\r\nconst onCardUpdate = (data) => {\r\n  if (data && data.idx !== undefined) {\r\n    list.value[data.idx] = data.item\r\n  }\r\n}\r\n\r\n// 关闭分享弹窗\r\nconst closeShare = () => {\r\n  showShare.value = false\r\n}\r\n\r\n// 处理分享事件\r\nconst handleShare = (type) => {\r\n  console.log('分享话题:', type, topicInfo)\r\n  uni.showToast({\r\n    title: '分享成功',\r\n    icon: 'success'\r\n  })\r\n  closeShare()\r\n}\r\n\r\n// 处理不喜欢事件\r\nconst handleDislike = () => {\r\n  console.log('不喜欢话题:', topicInfo)\r\n  uni.showToast({\r\n    title: '已标记为不感兴趣',\r\n    icon: 'success'\r\n  })\r\n  closeShare()\r\n}\r\n\r\n// 处理举报事件\r\nconst handleReport = () => {\r\n  console.log('举报话题:', topicInfo)\r\n  uni.showToast({\r\n    title: '举报成功',\r\n    icon: 'success'\r\n  })\r\n  closeShare()\r\n}\r\n\r\n// 生命周期钩子\r\nonLoad((options) => {\r\n  initPage(options)\r\n})\r\n\r\nonPullDownRefresh(() => {\r\n  page.value = 1\r\n  list.value = []\r\n  isEmpty.value = false\r\n  loadStatus.value = 'more'\r\n  loadTopicDynamicList(true)\r\n})\r\n\r\nonReachBottom(() => {\r\n  if (loadStatus.value === 'noMore' || !isThrottling.value) return\r\n  page.value++\r\n  loadStatus.value = 'loading'\r\n  loadTopicDynamicList()\r\n})\r\n\r\nonPageScroll((e) => {\r\n  const scrollTop = e.scrollTop > 150 ? 150 : e.scrollTop\r\n  navbarTrans.value = scrollTop / 150\r\n})\r\n\r\nonUnmounted(() => {\r\n  // 清理定时器\r\n  if (loadingTimer.value) {\r\n    clearTimeout(loadingTimer.value)\r\n    loadingTimer.value = null\r\n  }\r\n  if (debounceTimer.value) {\r\n    clearTimeout(debounceTimer.value)\r\n    debounceTimer.value = null\r\n  }\r\n})\r\n\r\nonShareAppMessage(() => {\r\n  return {\r\n    title: topicInfo.title || '话题分享',\r\n    path: `/pages/topic/details?id=${topicId.value}`,\r\n    imageUrl: topicInfo.cover_image || '/static/img/avatar.png'\r\n  }\r\n})\r\n\r\nonShareTimeline(() => {\r\n  return {\r\n    title: topicInfo.title || '话题分享',\r\n    query: \"id=\" + topicId.value,\r\n    imageUrl: topicInfo.cover_image || '/static/img/avatar.png'\r\n  }\r\n})\r\n</script>\r\n\r\n<style>\r\n/* 基础样式 */\r\n.container {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  overflow-x: hidden;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.nav-box {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 99;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.nav-box .nav-back {\r\n  padding: 0 30rpx;\r\n  width: 34rpx;\r\n  height: 100%;\r\n}\r\n\r\n.nav-box .nav-title {\r\n  max-width: 60%;\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n/* 话题头部区域 */\r\n.topic-header {\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 主背景容器 */\r\n.images-box {\r\n  width: 100%;\r\n  flex-direction: column;\r\n  position: relative;\r\n  background: #f8f8f8;\r\n  border-radius: 0 0 24rpx 24rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 话题背景图片容器 */\r\n.topic-image-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 400rpx;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  overflow: hidden;\r\n}\r\n\r\n/* 话题背景图片 */\r\n.topic-bg-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n/* 图片点击效果 */\r\n.topic-image-container:active .topic-bg-image {\r\n  transform: scale(0.98);\r\n}\r\n\r\n/* 背景遮罩层 */\r\n.bg-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background:\r\n    linear-gradient(to bottom,\r\n      transparent 0%,\r\n      rgba(0,0,0,0.1) 30%,\r\n      rgba(0,0,0,0.4) 70%,\r\n      rgba(0,0,0,0.8) 100%\r\n    ),\r\n    radial-gradient(circle at center bottom,\r\n      rgba(0,0,0,0.2) 0%,\r\n      rgba(0,0,0,0.6) 70%,\r\n      rgba(0,0,0,0.9) 100%\r\n    );\r\n\r\n  backdrop-filter: blur(1px) saturate(180%);\r\n  -webkit-backdrop-filter: blur(1px) saturate(180%);\r\n\r\n  box-shadow:\r\n    inset 0 -80rpx 120rpx -40rpx rgba(0,0,0,0.4),\r\n    inset 0 80rpx 120rpx -40rpx rgba(0,0,0,0.1);\r\n\r\n  transition: all 0.3s ease-out;\r\n}\r\n\r\n/* 话题信息叠加层 */\r\n.topic-info-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 40rpx 30rpx 30rpx;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n}\r\n\r\n.topic-main-info {\r\n  align-items: flex-start;\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n}\r\n\r\n/* 话题图标 */\r\n.topic-icon {\r\n  margin-right: 20rpx;\r\n}\r\n\r\n\r\n\r\n.topic-icon-img {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  filter: brightness(0) invert(1);\r\n}\r\n\r\n/* 话题详情 */\r\n.topic-details {\r\n  flex: 1;\r\n}\r\n\r\n.topic-name {\r\n  color: #fff;\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 10rpx;\r\n  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);\r\n}\r\n\r\n\r\n\r\n/* 话题描述 */\r\n.topic-description {\r\n  color: rgba(255,255,255,0.9);\r\n  font-size: 24rpx;\r\n  line-height: 1.4;\r\n  margin-bottom: 15rpx;\r\n  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);\r\n}\r\n\r\n/* 话题分享按钮 */\r\n.topic-share-btn {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n}\r\n\r\n.share-btn-wrapper {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  background: rgba(255,255,255,0.2);\r\n  border: 2rpx solid rgba(255,255,255,0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(10rpx);\r\n  -webkit-backdrop-filter: blur(10rpx);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.share-btn-wrapper:active {\r\n  transform: scale(0.95);\r\n  background: rgba(255,255,255,0.3);\r\n}\r\n\r\n.share-icon {\r\n  width: 36rpx;\r\n  height: 36rpx;\r\n  filter: brightness(0) invert(1);\r\n}\r\n\r\n\r\n\r\n/* 骨架屏样式 */\r\n.skeleton-text {\r\n  width: 200rpx;\r\n  height: 36rpx;\r\n  background: linear-gradient(90deg, rgba(255,255,255,0.3) 25%, rgba(255,255,255,0.6) 50%, rgba(255,255,255,0.3) 75%);\r\n  background-size: 200% 100%;\r\n  border-radius: 6rpx;\r\n  animation: skeletonLoading 1.5s infinite;\r\n}\r\n\r\n.skeleton-stats {\r\n  display: flex;\r\n  gap: 16rpx;\r\n}\r\n\r\n.skeleton-item {\r\n  width: 80rpx;\r\n  height: 22rpx;\r\n  background: linear-gradient(90deg, rgba(255,255,255,0.3) 25%, rgba(255,255,255,0.6) 50%, rgba(255,255,255,0.3) 75%);\r\n  background-size: 200% 100%;\r\n  border-radius: 4rpx;\r\n  animation: skeletonLoading 1.5s infinite;\r\n}\r\n\r\n@keyframes skeletonLoading {\r\n  0% {\r\n    background-position: -200% 0;\r\n  }\r\n  100% {\r\n    background-position: 200% 0;\r\n  }\r\n}\r\n\r\n/* 分类标签 */\r\n.bar-box {\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 98;\r\n  width: 100%;\r\n  height: 80rpx;\r\n  background: #fff;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n  margin-top: -24rpx;\r\n  border-radius: 24rpx 24rpx 0 0;\r\n  justify-content: space-between;\r\n  padding: 0 30rpx;\r\n  box-sizing: border-box;\r\n  overflow: hidden;\r\n}\r\n\r\n.bar-left {\r\n  flex: 1;\r\n  overflow: hidden;\r\n  min-width: 0;\r\n}\r\n\r\n.bar-box .bar-item {\r\n  padding: 0 30rpx 0 0;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-shrink: 0;\r\n  min-width: 0;\r\n}\r\n\r\n.bar-item-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  position: relative;\r\n}\r\n\r\n.bar-item-content text {\r\n  font-weight: 700;\r\n  transition: all 0.3s ease-in-out;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.bar-line {\r\n  width: 18rpx;\r\n  height: 6rpx;\r\n  border-radius: 6rpx;\r\n  background: #000;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease-in-out;\r\n}\r\n\r\n.bar-line.active {\r\n  opacity: 1;\r\n}\r\n\r\n/* 话题统计信息 */\r\n.topic-stats-right {\r\n  color: #999;\r\n  font-size: 24rpx;\r\n  line-height: 1.4;\r\n}\r\n\r\n.topic-stats-right .stat-item {\r\n  font-size: 24rpx;\r\n  font-weight: 400;\r\n}\r\n\r\n.topic-stats-right .stat-divider {\r\n  margin: 0 8rpx;\r\n  font-size: 24rpx;\r\n}\r\n\r\n/* 内容区域 */\r\n.content-area {\r\n  min-height: 600rpx;\r\n  background: #fff;\r\n}\r\n\r\n.dynamic-box {\r\n  width: calc(100% - 16rpx);\r\n  padding: 22rpx 8rpx 0;\r\n}\r\n\r\n/* 加载中状态样式 */\r\n.loading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 60rpx;\r\n  margin: 20rpx 0;\r\n}\r\n\r\n.loading-indicator {\r\n  width: 30rpx;\r\n  height: 30rpx;\r\n  border: 3rpx solid #f3f3f3;\r\n  border-top: 3rpx solid #000;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 空状态 */\r\n.empty-box {\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n\r\n.empty-box image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.empty-box .e1 {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.empty-box .e2 {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 提示弹窗 */\r\n.tips-box {\r\n  padding: 20rpx 30rpx;\r\n  border-radius: 12rpx;\r\n  justify-content: center;\r\n}\r\n\r\n.tips-box .tips-item {\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n/* 工具类 */\r\n.df {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ohto {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/uniapp/vue3/pages/topic/details.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useStore", "computed", "ref", "reactive", "uni", "getTopicDetail", "getTopicList", "getTopicDynamicList", "onLoad", "onPullDownRefresh", "onReachBottom", "onPageScroll", "onUnmounted", "onShareAppMessage", "onShareTimeline", "MiniProgramPage"], "mappings": ";;;;;;;;;;;;AA8JA,MAAA,YAAA,MAAA;AAEA,MAAA,YAAA,MAAA;AACA,MAAA,SAAA,MAAA;AACA,MAAA,iBAAA,MAAA;;;;;;AASA,UAAA,QAAAA,cAAAA,SAAA;AAGA,UAAA,kBAAAC,cAAA,SAAA,MAAA,MAAA,MAAA,mBAAA,EAAA;AACA,UAAA,iBAAAA,cAAA,SAAA,MAAA,MAAA,MAAA,kBAAA,EAAA;AACA,UAAA,WAAAA,cAAA,SAAA,MAAA,MAAA,MAAA,IAAA,QAAA;AACA,UAAA,SAAAA,cAAA,SAAA,MAAA;;AAAA,6BAAA,UAAA,mBAAA,QAAA;AAAA,KAAA;AAGA,UAAA,cAAAC,cAAA,IAAA,CAAA;AACA,UAAA,UAAAA,cAAA,IAAA,CAAA,MAAA,IAAA,CAAA;AACA,UAAA,SAAAA,cAAA,IAAA,CAAA;AACA,UAAA,UAAAA,cAAA,IAAA,CAAA;AACA,UAAA,YAAAC,cAAAA,SAAA;AAAA,MACA,IAAA;AAAA,MACA,OAAA;AAAA,MACA,aAAA;AAAA,MACA,aAAA;AAAA,MACA,YAAA;AAAA,MACA,YAAA;AAAA,IACA,CAAA;AACA,UAAA,OAAAD,cAAA,IAAA,EAAA;AACA,UAAA,OAAAA,cAAA,IAAA,CAAA;AACA,UAAA,QAAAA,cAAA,IAAA,EAAA;AACA,UAAA,aAAAA,cAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAA,IAAA,IAAA;AACA,UAAA,UAAAA,cAAA,IAAA,KAAA;AACA,UAAA,aAAAA,cAAA,IAAA,MAAA;AACA,UAAA,YAAAA,cAAA,IAAA,EAAA;AACA,UAAA,cAAAA,cAAA,IAAA,KAAA;AACA,UAAA,cAAAA,cAAA,IAAA,IAAA;AACA,UAAA,eAAAA,cAAA,IAAA,IAAA;AACA,UAAA,gBAAAA,cAAA,IAAA,IAAA;AACA,UAAA,YAAAA,cAAA,IAAA,KAAA;AAGA,UAAA,iBAAAD,cAAA,SAAA,MAAA;AACA,aAAA;AAAA,QACA,IAAA,UAAA,MAAA,QAAA;AAAA,QACA,OAAA,UAAA;AAAA,QACA,SAAA,UAAA;AAAA,QACA,OAAA,UAAA;AAAA,QACA,MAAA;AAAA,QACA,WAAA,2BAAA,QAAA,KAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,WAAA,CAAA,YAAA;AAGAG,oBAAAA,MAAA,cAAA;AAGA,UAAA,WAAA,QAAA,IAAA;AACA,gBAAA,QAAA,SAAA,QAAA,EAAA;AACA,wBAAA;AACA,6BAAA,IAAA;AAAA,MACA,OAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACA,mBAAA,MAAA;AACAA,wBAAAA,MAAA,aAAA;AAAA,QACA,GAAA,IAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,oBAAA,MAAA;AACA,UAAA,UAAA,aAAA;AACAA,sBAAAA,MAAA,aAAA;AAAA,UACA,SAAA,UAAA;AAAA,UACA,MAAA,CAAA,UAAA,WAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,WAAA,CAAA,MAAA;AACA,YAAA,MAAA,SAAA,EAAA,cAAA,QAAA,GAAA;AACA,UAAA,OAAA,UAAA,OAAA,CAAA,aAAA;AAAA;AAEA,aAAA,QAAA;AACA,WAAA,QAAA;AACA,WAAA,QAAA,CAAA;AACA,cAAA,QAAA;AACA,iBAAA,QAAA;AAEA,2BAAA,IAAA;AAAA,IACA;AAGA,UAAA,kBAAA,YAAA;AACA,kBAAA,QAAA;AAEA,UAAA;AACA,cAAA,MAAA,MAAAC,0BAAA,QAAA,KAAA;AACA,oBAAA,QAAA;AAEA,YAAA,IAAA,WAAA,OAAA,IAAA,MAAA;AACA,iBAAA,OAAA,WAAA;AAAA,YACA,GAAA,IAAA;AAAA,YACA,IAAA,IAAA,KAAA,MAAA,QAAA;AAAA,UACA,CAAA;AAAA,QACA,OAAA;AACAD,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA,IAAA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACA,oBAAA,QAAA;AAEA,YAAA;AACA,gBAAA,MAAA,MAAAE,wBAAA;AAAA,YACA,MAAA;AAAA,YACA,OAAA;AAAA,YACA,SAAA;AAAA,UACA,CAAA;AAEA,cAAA,IAAA,WAAA,OAAA,IAAA,QAAA,IAAA,KAAA,MAAA;AACA,kBAAA,QAAA,IAAA,KAAA,KAAA,KAAA,UAAA,KAAA,MAAA,QAAA,KAAA;AACA,gBAAA,OAAA;AACA,qBAAA,OAAA,WAAA;AAAA,gBACA,GAAA;AAAA,gBACA,IAAA,MAAA,MAAA,QAAA;AAAA,cACA,CAAA;AAAA,YACA,OAAA;AACAF,4BAAAA,MAAA,UAAA;AAAA,gBACA,OAAA;AAAA,gBACA,MAAA;AAAA,cACA,CAAA;AAAA,YACA;AAAA,UACA,OAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,OAAA,IAAA,OAAA;AAAA,cACA,MAAA;AAAA,YACA,CAAA;AAAA,UACA;AAAA,QACA,SAAA,SAAA;AACA,sBAAA,QAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AACA,qBAAA,MAAA;AACAA,0BAAAA,MAAA,aAAA;AAAA,UACA,GAAA,GAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,uBAAA,OAAA,YAAA,UAAA;AACA,UAAA,CAAA,aAAA;AAAA;AAEA,mBAAA,QAAA;AACA,kBAAA,QAAA;AAGA,UAAA,aAAA,OAAA;AACA,qBAAA,aAAA,KAAA;AACA,qBAAA,QAAA;AAAA,MACA;AAGA,mBAAA,QAAA,WAAA,MAAA;AACA,oBAAA,QAAA;AAAA,MACA,GAAA,GAAA;AAEA,UAAA;AACA,cAAA,MAAA,MAAAG,+BAAA;AAAA,UACA,UAAA,QAAA;AAAA,UACA,MAAA,KAAA;AAAA,UACA,OAAA,MAAA;AAAA,UACA,MAAA,OAAA,UAAA,IAAA,WAAA;AAAA,QACA,CAAA;AAGA,YAAA,aAAA,OAAA;AACA,uBAAA,aAAA,KAAA;AACA,uBAAA,QAAA;AAAA,QACA;AACA,oBAAA,QAAA;AAEA,YAAA,IAAA,WAAA,OAAA,IAAA,MAAA;AACA,gBAAA,OAAA,IAAA;AACA,gBAAA,UAAA,KAAA,QAAA,CAAA;AAEA,cAAA,aAAA,KAAA,UAAA,GAAA;AACA,iBAAA,QAAA;AAAA,UACA,OAAA;AACA,iBAAA,QAAA,CAAA,GAAA,KAAA,OAAA,GAAA,OAAA;AAAA,UACA;AAEA,qBAAA,QAAA,KAAA,SAAA;AACA,kBAAA,QAAA,KAAA,MAAA,WAAA;AAGA,gBAAA,UAAA,KAAA,QAAA,MAAA,QAAA,WAAA;AACA,qBAAA,QAAA,UAAA,SAAA;AAGA,uBAAA,QAAA;AAGA,cAAA,WAAA;AACAH,0BAAAA,MAAA,oBAAA;AAAA,UACA;AAAA,QACA,OAAA;AACA,sBAAA,IAAA,OAAA,UAAA;AAAA,QACA;AAAA,MACA,SAAA,KAAA;AAEA,YAAA,aAAA,OAAA;AACA,uBAAA,aAAA,KAAA;AACA,uBAAA,QAAA;AAAA,QACA;AACA,oBAAA,QAAA;AACA,qBAAA,QAAA;AAGA,YAAA,WAAA;AACAA,wBAAAA,MAAA,oBAAA;AAAA,QACA;AAEA,YAAA,KAAA,UAAA,GAAA;AACA,kBAAA,QAAA;AAAA,QACA;AACA,oBAAA,KAAA,gBAAA;AAAA,MACA;AAAA,IACA;AAIA,UAAA,cAAA,CAAA,OAAA,iBAAA,WAAA;AACAA,oBAAAA,MAAA,MAAA,SAAA,kCAAA,SAAA,KAAA;AACA,YAAA,WAAA,+BAAA,SAAA,+BAAA,YAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,QACA,UAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,UAAA,MAAA;AACAA,oBAAAA,MAAA,aAAA;AAAA,IACA;AAGA,UAAA,YAAA,CAAA,SAAA;AACA,UAAA,QAAA,KAAA,QAAA,QAAA;AACA,aAAA,MAAA,KAAA,GAAA,IAAA,KAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,eAAA,CAAA,SAAA;AACA,UAAA,QAAA,KAAA,QAAA,QAAA;AACA,aAAA,MAAA,KAAA,GAAA,IAAA,KAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,aAAA,MAAA;AACA,gBAAA,QAAA;AAAA,IACA;AAGA,UAAA,cAAA,CAAA,SAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,kCAAA,SAAA,MAAA,SAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,MACA,CAAA;AACA,iBAAA;AAAA,IACA;AAGA,UAAA,gBAAA,MAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,UAAA,SAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,MACA,CAAA;AACA,iBAAA;AAAA,IACA;AAGA,UAAA,eAAA,MAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,SAAA,SAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,MACA,CAAA;AACA,iBAAA;AAAA,IACA;AAGAI,kBAAA,OAAA,CAAA,YAAA;AACA,eAAA,OAAA;AAAA,IACA,CAAA;AAEAC,kBAAAA,kBAAA,MAAA;AACA,WAAA,QAAA;AACA,WAAA,QAAA,CAAA;AACA,cAAA,QAAA;AACA,iBAAA,QAAA;AACA,2BAAA,IAAA;AAAA,IACA,CAAA;AAEAC,kBAAAA,cAAA,MAAA;AACA,UAAA,WAAA,UAAA,YAAA,CAAA,aAAA;AAAA;AACA,WAAA;AACA,iBAAA,QAAA;AACA,2BAAA;AAAA,IACA,CAAA;AAEAC,kBAAA,aAAA,CAAA,MAAA;AACA,YAAA,YAAA,EAAA,YAAA,MAAA,MAAA,EAAA;AACA,kBAAA,QAAA,YAAA;AAAA,IACA,CAAA;AAEAC,kBAAAA,YAAA,MAAA;AAEA,UAAA,aAAA,OAAA;AACA,qBAAA,aAAA,KAAA;AACA,qBAAA,QAAA;AAAA,MACA;AACA,UAAA,cAAA,OAAA;AACA,qBAAA,cAAA,KAAA;AACA,sBAAA,QAAA;AAAA,MACA;AAAA,IACA,CAAA;AAEAC,kBAAAA,kBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA,UAAA,SAAA;AAAA,QACA,MAAA,2BAAA,QAAA,KAAA;AAAA,QACA,UAAA,UAAA,eAAA;AAAA,MACA;AAAA,IACA,CAAA;AAEAC,kBAAAA,gBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA,UAAA,SAAA;AAAA,QACA,OAAA,QAAA,QAAA;AAAA,QACA,UAAA,UAAA,eAAA;AAAA,MACA;AAAA,IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzgBA,GAAG,WAAWC,SAAe;"}