
.gg-box {
  border-bottom: 1px solid #f8f8f8;
  width: calc(100% - 60rpx);
  padding: 30rpx;
  display: flex;
}
.gg-box .gg-avatar {
  width: 68rpx;
  height: 68rpx;
  border-radius: 50%;
  border: 1px solid #f8f8f8;
  position: relative;
}
.gg-avatar .top {
  position: absolute;
  right: -4rpx;
  bottom: -4rpx;
  width: 26rpx;
  height: 26rpx;
  border-radius: 50%;
  justify-content: center;
  background: #000;
}
.gg-avatar .top image {
  width: 100%;
  height: 100%;
}
.gg-box .gg-item {
  width: calc(100% - 88rpx - 2px);
  margin-left: 20rpx;
}

/* 用户名容器 */
.gg-item .gg-item-user .name-container {
  align-items: center;
}
.gg-item .gg-item-user .name {
  color: #000;
  font-size: 28rpx;
  line-height: 34rpx;
  font-weight: 700;
}

/* VIP图标样式 - 参考center.vue */
.gg-item .gg-item-user .status-icon {
  width: 70rpx;
  height: 30rpx;
  margin-right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.gg-item .gg-item-user .vip-icon {
  border-radius: 6rpx;
  padding: 2rpx;
}
.gg-item .gg-item-user .vip-icon image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.gg-item .gg-item-user .tag {
  margin-left: 12rpx;
  padding: 0 6rpx;
  height: 34rpx;
  border-radius: 4rpx;
  background: #f5f5f5;
}
.gg-item-user .tag image {
  margin: 0 3rpx;
  width: 20rpx;
  height: 20rpx;
}
.gg-item-user .tag text {
  margin: 0 3rpx;
  font-size: 18rpx;
}
.gg-item .gg-item-content {
  margin-top: 12rpx;
  font-size: 26rpx;
  font-weight: 400;
  line-height: 36rpx;
  word-break: break-word;
  white-space: pre-line;
}
.gg-item .gg-item-file {
  margin-top: 20rpx;
  display: flex;
  position: relative;
  z-index: 1;
  flex-wrap: wrap;
}
.gg-item-file .file-h,
.gg-item-file .file-w,
.gg-item-file .file-img {
  border-radius: 8rpx;
  overflow: hidden;
}
.gg-item-file .file-h {
  width: 320rpx;
  height: 420rpx;
}
.gg-item-file .file-w {
  width: 420rpx;
  height: 320rpx;
}
.gg-item-file .file-img {
  width: 196rpx;
  height: 196rpx;
  margin-right: 4rpx;
  margin-bottom: 4rpx;
}
.gg-item-file .file-img:nth-child(3n) {
  margin-right: 0rpx !important;
}
.gg-item-file .file-count {
  position: absolute;
  right: 20rpx;
  bottom: 30rpx;
  padding: 0 10rpx;
  height: 40rpx;
  color: #fff;
  font-size: 20rpx;
  font-weight: 700;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 8rpx;
}
.gg-item-file .file-count image,
.gg-item-file .file-video image {
  width: 20rpx;
  height: 20rpx;
}
.gg-item-file .file-count image {
  margin-right: 10rpx;
}
.gg-item-file .file-video {
  position: absolute;
  top: 20rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  justify-content: center;
  border-radius: 50%;
}
.gg-item-file .file-audio {
  width: 100%;
  height: 140rpx;
  border-radius: 8rpx;
  color: #fff;
  position: relative;
  overflow: hidden;
}
.file-audio .audio-left {
  margin-right: 30rpx;
  width: 140rpx;
  height: 140rpx;
  position: relative;
}
.file-audio .audio-left .icon {
  position: absolute;
  top: 45rpx;
  right: 45rpx;
  bottom: 45rpx;
  left: 45rpx;
  width: 50rpx;
  height: 50rpx;
}
.file-audio .audio-bg,
.file-audio .audio-mb {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.file-audio .audio-mb {
  z-index: -1;
  -webkit-backdrop-filter: saturate(150%) blur(25px);
  backdrop-filter: saturate(150%) blur(25px);
  background: rgba(0, 0, 0, 0.5);
}
.file-audio .audio-t1 {
  font-size: 26rpx;
  font-weight: 700;
}
.file-audio .audio-t2 {
  margin-top: 10rpx;
  opacity: 0.8;
  font-size: 20rpx;
}
.file-audio .audio-play {
  margin: 0 30rpx;
  width: 100rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 18rpx;
  font-weight: 700;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 60rpx;
}
.gg-item .gg-item-g {
  margin-top: 10rpx;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.gg-item-g .g-item {
  margin: 10rpx 10rpx 0 0;
  padding: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
  border-radius: 8rpx;
  background: #f8f8f8;
}
.g-item .g-item-img {
  width: 40rpx;
  height: 40rpx;
  background: #f8f8f8;
  border-radius: 4rpx;
  overflow: hidden;
}
.gg-item .gg-item-time {
  margin-top: 8rpx;
  margin-bottom: 12rpx;
  color: #999;
  font-size: 20rpx;
}
.gg-item .gg-item-comment {
  margin-top: 20rpx;
  width: calc(100% - 40rpx);
  padding: 20rpx;
  color: #999;
  font-size: 24rpx;
  font-weight: 400;
  word-break: break-word;
  white-space: pre-line;
  border-radius: 8rpx;
  background: #f8f8f8;
}
.gg-item .gg-item-unm {
  display: flex;
  align-items: center;
  width: 100%;
}
.gg-item-unm .unm-item {
  margin-top: 30rpx;
  display: flex;
  align-items: center;
}
.gg-item-unm .unm-item image {
  width: 44rpx;
  height: 44rpx;
}
.gg-item-unm .unm-item text {
  margin: 0 30rpx 0 6rpx;
  color: #999;
  font-size: 18rpx;
  font-weight: 700;
}
.wlc8 {
  -webkit-line-clamp: 8 !important;
  line-clamp: 8 !important;
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2 {
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
}

/* 关注按钮样式 */
.gg-item-user .follow-btn {
  margin-left: auto;
  padding: 0 20rpx;
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 700;
  color: #000;
  background: #f8f8f8;
  text-align: center;
}
.gg-item-user .follow-btn.active {
  color: #999;
  background: #f5f5f5;
}
.gg-item-user .follow-btn.mutual {
  color: #576b95;
  background: rgba(87, 107, 149, 0.1);
}

/* 移除原有 .topic-tag 样式，话题和圈子统一用 g-item/g-item-img/text 结构 */
.topic-tag,
.topic-tag text {
  /* 移除样式 */
  display: none !important;
}

/* 评论样式 */
.gg-item .gg-item-comments {
  margin-top: 20rpx;
  width: calc(100% - 40rpx);
  padding: 20rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
}
.gg-item-comments .comment-item {
  margin-bottom: 15rpx;
  padding-bottom: 15rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.gg-item-comments .comment-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.gg-item-comments .comment-user {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}
.gg-item-comments .comment-content {
  font-size: 24rpx;
  color: #666;
  line-height: 32rpx;
  word-break: break-all;
}
.gg-item-comments .comment-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
}
.gg-item-comments .comment-time {
  font-size: 20rpx;
  color: #999;
}
.gg-item-comments .comment-like {
  display: flex;
  align-items: center;
}
.gg-item-comments .comment-like image {
  width: 28rpx;
  height: 28rpx;
  margin-right: 4rpx;
}
.gg-item-comments .comment-like text {
  font-size: 20rpx;
  color: #999;
}
.gg-item-comments .comment-like text.active {
  color: #FA5150;
}
.gg-item-comments .more-comments {
  margin-top: 15rpx;
  text-align: center;
  font-size: 22rpx;
  color: #576b95;
  padding: 10rpx 0;
}



