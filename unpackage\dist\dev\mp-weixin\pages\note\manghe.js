"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const libs_login = require("../../libs/login.js");
const _sfc_main = {
  name: "TreeHoleBox",
  data() {
    return {
      currentType: 0,
      // 0-随机 1-问题 2-秘密 3-心愿 4-语音
      typeList: [
        { value: 0, name: "随机", icon: "🎲" },
        { value: 1, name: "问题", icon: "❓" },
        { value: 2, name: "秘密", icon: "🤫" },
        { value: 3, name: "心愿", icon: "🌠" },
        { value: 4, name: "语音", icon: "🎵" }
      ],
      showDetailModal: false,
      // 是否显示详情弹窗
      currentBoxData: null,
      // 当前抽取的纸条数据
      currentDrawId: null,
      // 当前抽取记录ID
      isPlaying: false,
      // 语音播放状态
      responseText: "",
      // 回应内容
      responseCount: 0
      // 回应数量
    };
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 前往我的盲盒
    goMyBox() {
      common_vendor.index.navigateTo({
        url: "/pages/note/my-tree-hole"
      });
    },
    // 选择类型
    selectType(type) {
      this.currentType = type;
    },
    // 抽取盲盒
    async drawBox() {
      common_vendor.index.__f__("log", "at pages/note/manghe.vue:263", "=== 开始抽取盲盒 ===");
      common_vendor.index.__f__("log", "at pages/note/manghe.vue:264", "当前选择类型:", this.currentType);
      if (!this.checkLoginStatus()) {
        return;
      }
      common_vendor.index.showToast({
        title: "正在为您抽取纸条...",
        icon: "loading"
      });
      try {
        const requestData = {
          type: this.currentType
          // 根据当前选择的类型抽取
        };
        common_vendor.index.__f__("log", "at pages/note/manghe.vue:281", "抽取请求参数:", requestData);
        const result = await api_social.drawTreeHoleBox(requestData);
        common_vendor.index.__f__("log", "at pages/note/manghe.vue:284", "抽取API响应:", result);
        if (result.status === 200 && result.data) {
          common_vendor.index.__f__("log", "at pages/note/manghe.vue:287", "抽取成功，纸条数据:", result.data);
          this.currentBoxData = result.data;
          this.currentDrawId = result.data.draw_id || null;
          this.responseCount = result.data.response_count || 0;
          common_vendor.index.hideToast();
          common_vendor.index.showToast({
            title: "抽取成功！",
            icon: "success"
          });
          setTimeout(() => {
            this.showDetailModal = true;
          }, 1500);
        } else {
          throw new Error(result.msg || "抽取失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/manghe.vue:306", "抽取盲盒失败:", error);
        common_vendor.index.hideToast();
        common_vendor.index.showToast({
          title: error.message || "抽取失败，请稍后重试",
          icon: "none",
          duration: 2e3
        });
        if (error.message && error.message.includes("网络")) {
          common_vendor.index.__f__("log", "at pages/note/manghe.vue:318", "网络错误，使用模拟数据");
          this.showMockData();
        }
      }
    },
    // 关闭详情弹窗
    closeDetailModal() {
      this.showDetailModal = false;
      this.currentBoxData = null;
      this.currentDrawId = null;
      this.isPlaying = false;
      this.responseText = "";
      this.responseCount = 0;
    },
    // 放回纸条
    async returnBox() {
      if (!this.currentDrawId) {
        common_vendor.index.showToast({
          title: "抽取记录不存在",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认放回",
        content: "确定要放回这张纸条吗？放回后将不再显示在您的抽取列表中。",
        success: async (res) => {
          if (res.confirm) {
            try {
              common_vendor.index.showLoading({
                title: "正在放回..."
              });
              common_vendor.index.__f__("log", "at pages/note/manghe.vue:354", "放回纸条，draw_id:", this.currentDrawId);
              const result = await api_social.returnTreeHoleBox(this.currentDrawId);
              common_vendor.index.hideLoading();
              if (result.status === 200) {
                common_vendor.index.showToast({
                  title: "已放回纸条",
                  icon: "success"
                });
                setTimeout(() => {
                  this.closeDetailModal();
                }, 1500);
              } else {
                throw new Error(result.msg || "放回失败");
              }
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/note/manghe.vue:372", "放回纸条失败:", error);
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: error.message || "放回失败，请稍后重试",
                icon: "none"
              });
            }
          }
        }
      });
    },
    // 发送回应
    async sendResponse() {
      if (!this.responseText.trim()) {
        common_vendor.index.showToast({
          title: "请输入回应内容",
          icon: "none"
        });
        return;
      }
      if (this.responseText.length < 5) {
        common_vendor.index.showToast({
          title: "回应内容至少5个字符",
          icon: "none"
        });
        return;
      }
      if (!this.currentBoxData || !this.currentBoxData.id) {
        common_vendor.index.showToast({
          title: "纸条信息不存在",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "正在发送回应..."
        });
        const responseData = {
          box_id: this.currentBoxData.id,
          content: this.responseText.trim()
        };
        if (this.currentDrawId) {
          responseData.draw_id = this.currentDrawId;
        }
        common_vendor.index.__f__("log", "at pages/note/manghe.vue:426", "发送回应，参数:", responseData);
        const result = await api_social.responseTreeHoleBox(responseData);
        common_vendor.index.hideLoading();
        if (result.status === 200) {
          common_vendor.index.showToast({
            title: "回应成功",
            icon: "success"
          });
          const boxId = this.currentBoxData.id;
          setTimeout(() => {
            this.closeDetailModal();
            common_vendor.index.navigateTo({
              url: `/pages/note/detail?id=${boxId}`
            });
          }, 1500);
        } else {
          throw new Error(result.msg || "回应失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/manghe.vue:451", "发送回应失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: error.message || "回应失败，请稍后重试",
          icon: "none"
        });
      }
    },
    // 切换语音播放
    toggleVoice() {
      if (!this.currentBoxData || !this.currentBoxData.voice_url) {
        common_vendor.index.showToast({
          title: "暂无语音内容",
          icon: "none"
        });
        return;
      }
      this.isPlaying = !this.isPlaying;
      if (this.isPlaying) {
        common_vendor.index.showToast({
          title: "开始播放语音",
          icon: "none"
        });
      } else {
        common_vendor.index.showToast({
          title: "停止播放语音",
          icon: "none"
        });
      }
    },
    // 获取性别图标
    getGenderIcon(sex) {
      return sex === 1 ? "♂" : sex === 2 ? "♀" : "⚪";
    },
    // 获取类型图标
    getTypeIcon(type) {
      const icons = {
        1: "❓",
        2: "🤫",
        3: "🌠",
        4: "🎵"
      };
      return icons[type] || "📝";
    },
    // 获取类型文本
    getTypeText(type) {
      const texts = {
        1: "问题咨询",
        2: "秘密",
        3: "心愿",
        4: "语音纸条"
      };
      return texts[type] || "纸条";
    },
    // 获取随机内容
    getRandomContent(type) {
      const contents = {
        1: ["有什么好的学习方法吗？", "如何提高工作效率？", "怎样保持健康的生活习惯？"],
        2: ["其实我一直很喜欢你", "我有一个不为人知的梦想", "有些话我一直不敢说出口"],
        3: ["希望能找到真爱", "想要环游世界", "希望家人身体健康"],
        4: ["这是一条语音纸条", "听听我的心声吧", "用声音传递温暖"]
      };
      const typeContents = contents[type] || ["这是一张神秘的纸条"];
      return typeContents[Math.floor(Math.random() * typeContents.length)];
    },
    // 获取随机昵称
    getRandomNickname() {
      const adjectives = ["优雅的", "神秘的", "可爱的", "勇敢的", "温柔的", "聪明的"];
      const animals = ["小猫", "小狗", "兔子", "熊猫", "狐狸", "小鸟"];
      const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
      const animal = animals[Math.floor(Math.random() * animals.length)];
      return adj + animal;
    },
    // 发布盲盒
    publishBox() {
      if (!this.checkLoginStatus()) {
        return;
      }
      common_vendor.index.navigateTo({
        url: "/pages/note/publish-tree-hole-simple"
      });
    },
    // 检查登录状态
    checkLoginStatus() {
      var _a, _b, _c;
      common_vendor.index.__f__("log", "at pages/note/manghe.vue:552", "=== 主页登录状态检查 ===");
      const checkLoginResult = libs_login.checkLogin();
      const storeToken = (_c = (_b = (_a = this.$store) == null ? void 0 : _a.state) == null ? void 0 : _b.app) == null ? void 0 : _c.token;
      const isLoggedIn = checkLoginResult && storeToken;
      common_vendor.index.__f__("log", "at pages/note/manghe.vue:558", "checkLogin()结果:", checkLoginResult);
      common_vendor.index.__f__("log", "at pages/note/manghe.vue:559", "store token:", storeToken);
      common_vendor.index.__f__("log", "at pages/note/manghe.vue:560", "最终登录状态:", isLoggedIn);
      if (!isLoggedIn) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请先登录后再使用此功能",
          confirmText: "去登录",
          cancelText: "取消",
          success: (res) => {
            if (res.confirm) {
              libs_login.toLogin();
            }
          }
        });
        return false;
      }
      return true;
    },
    // 统一错误处理
    handleError(error, defaultMessage = "操作失败") {
      var _a, _b, _c;
      common_vendor.index.__f__("error", "at pages/note/manghe.vue:582", "错误处理:", error);
      let message = defaultMessage;
      if (typeof error === "string") {
        message = error;
      } else if (error && typeof error === "object") {
        if (error.code === "NETWORK_ERROR" || ((_a = error.message) == null ? void 0 : _a.includes("Network"))) {
          message = "网络连接异常，请检查网络设置";
        } else if (error.code === "TIMEOUT" || ((_b = error.message) == null ? void 0 : _b.includes("timeout"))) {
          message = "请求超时，请稍后重试";
        } else {
          message = error.msg || error.message || ((_c = error.data) == null ? void 0 : _c.msg) || defaultMessage;
        }
      }
      common_vendor.index.showToast({
        title: message,
        icon: "none",
        duration: 2e3
      });
      return message;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o((...args) => $options.goMyBox && $options.goMyBox(...args)),
    c: common_vendor.f($data.typeList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.icon),
        b: common_vendor.t(item.name),
        c: $data.currentType === item.value ? 1 : "",
        d: item.value,
        e: index * 0.2 + "s",
        f: common_vendor.o(($event) => $options.selectType(item.value), item.value)
      };
    }),
    d: common_vendor.o((...args) => $options.drawBox && $options.drawBox(...args)),
    e: common_vendor.o((...args) => $options.drawBox && $options.drawBox(...args)),
    f: common_vendor.o((...args) => $options.publishBox && $options.publishBox(...args)),
    g: $data.showDetailModal
  }, $data.showDetailModal ? common_vendor.e({
    h: common_vendor.o((...args) => $options.closeDetailModal && $options.closeDetailModal(...args)),
    i: $data.currentBoxData
  }, $data.currentBoxData ? {
    j: $data.currentBoxData.avatar || "/static/default-avatar.png",
    k: common_vendor.t($data.currentBoxData.nickname || "匿名用户"),
    l: common_vendor.t($options.getGenderIcon($data.currentBoxData.sex)),
    m: common_vendor.t($data.currentBoxData.age || "24"),
    n: common_vendor.t($data.currentBoxData.location || "Sub"),
    o: common_vendor.t($options.getTypeIcon($data.currentBoxData.type)),
    p: common_vendor.t($options.getTypeText($data.currentBoxData.type)),
    q: common_vendor.n("badge-type-" + $data.currentBoxData.type)
  } : {}, {
    r: $data.currentBoxData
  }, $data.currentBoxData ? common_vendor.e({
    s: $data.currentBoxData.type !== 4
  }, $data.currentBoxData.type !== 4 ? {
    t: common_vendor.t($data.currentBoxData.content)
  } : {
    v: common_vendor.t($data.isPlaying ? "⏸️" : "▶️"),
    w: common_vendor.t($data.currentBoxData.voice_duration),
    x: common_vendor.o((...args) => $options.toggleVoice && $options.toggleVoice(...args))
  }) : {}, {
    y: common_vendor.t($data.responseCount),
    z: $data.responseText,
    A: common_vendor.o(($event) => $data.responseText = $event.detail.value),
    B: common_vendor.t($data.responseText.length),
    C: common_vendor.o((...args) => $options.returnBox && $options.returnBox(...args)),
    D: common_vendor.o((...args) => $options.sendResponse && $options.sendResponse(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-792157c3"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/note/manghe.js.map
