{"version": 3, "file": "uni-popup.js", "sources": ["uni_modules/uni-popup/components/uni-popup/uni-popup.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvdW5pX21vZHVsZXMvdW5pLXBvcHVwL2NvbXBvbmVudHMvdW5pLXBvcHVwL3VuaS1wb3B1cC52dWU"], "sourcesContent": ["<template>\r\n  <view v-if=\"showPopup\" class=\"uni-popup\" :class=\"[popupstyle, isDesktop ? 'fixforpc-z-index' : '']\">\r\n    <view @touchstart=\"touchstart\">\r\n      <uni-transition v-if=\"maskShow\" key=\"1\" :name=\"'mask'\" :mode-class=\"'fade'\" :styles=\"maskClass\" \r\n        :duration=\"duration\" :show=\"showTrans\" @click=\"onTap\" />\r\n      <uni-transition v-if=\"showTrans\" key=\"2\" :mode-class=\"ani\" :name=\"'content'\" :styles=\"transClass\" \r\n        :duration=\"duration\" :show=\"showTrans\" @click=\"onTap\">\r\n        <view :style=\"getStyles\" class=\"uni-popup__wrapper\" :class=\"[popupstyle]\" @tap=\"clear\">\r\n          <slot />\r\n        </view>\r\n      </uni-transition>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport uniTransition from '@/uni_modules/uni-transition/components/uni-transition/uni-transition.vue'\r\n\r\nexport default {\r\n  name: \"uniPopup\",\r\n  components: {\r\n    uniTransition\r\n  },\r\n  emits: ['change', 'maskClick'],\r\n  props: {\r\n    animation: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    type: {\r\n      type: String,\r\n      default: \"center\"\r\n    },\r\n    isMaskClick: {\r\n      type: Boolean,\r\n      default: null\r\n    },\r\n    maskClick: {\r\n      type: Boolean,\r\n      default: null\r\n    },\r\n    backgroundColor: {\r\n      type: String,\r\n      default: \"none\"\r\n    },\r\n    safeArea: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    maskBackgroundColor: {\r\n      type: String,\r\n      default: \"rgba(0, 0, 0, 0.4)\"\r\n    },\r\n    borderRadius: {\r\n      type: String,\r\n      default: \"0\"\r\n    }\r\n  },\r\n  watch: {\r\n    type: {\r\n      handler: function(type) {\r\n        if (this.config[type]) {\r\n          this[this.config[type]](true)\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    isDesktop: {\r\n      handler: function(newVal) {\r\n        if (this.config[newVal]) {\r\n          this[this.config[this.type]](true)\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    maskClick: {\r\n      handler: function(val) {\r\n        this.mkclick = val\r\n      },\r\n      immediate: true\r\n    },\r\n    isMaskClick: {\r\n      handler: function(val) {\r\n        this.mkclick = val\r\n      },\r\n      immediate: true\r\n    },\r\n    showPopup(show) {}\r\n  },\r\n  data() {\r\n    return {\r\n      duration: 300,\r\n      ani: [],\r\n      showPopup: false,\r\n      showTrans: false,\r\n      popupWidth: 0,\r\n      popupHeight: 0,\r\n      config: {\r\n        top: \"top\",\r\n        bottom: \"bottom\",\r\n        center: \"center\",\r\n        left: \"left\",\r\n        right: \"right\",\r\n        message: \"top\",\r\n        dialog: \"center\",\r\n        share: \"bottom\"\r\n      },\r\n      maskClass: {\r\n        position: \"fixed\",\r\n        bottom: 0,\r\n        top: 0,\r\n        left: 0,\r\n        right: 0,\r\n        backgroundColor: \"rgba(0, 0, 0, 0.4)\"\r\n      },\r\n      transClass: {\r\n        position: \"fixed\",\r\n        left: 0,\r\n        right: 0,\r\n        backgroundColor: \"transparent\",\r\n        borderRadius: \"0\"\r\n      },\r\n      maskShow: true,\r\n      mkclick: true,\r\n      popupstyle: \"top\"\r\n    }\r\n  },\r\n  computed: {\r\n    getStyles() {\r\n      let styles = {\r\n        backgroundColor: this.bg\r\n      }\r\n      styles = Object.assign(styles, {\r\n        borderRadius: this.borderRadius\r\n      })\r\n      return styles\r\n    },\r\n    isDesktop() {\r\n      return this.popupWidth >= 500 && this.popupHeight >= 500\r\n    },\r\n    bg() {\r\n      if (this.backgroundColor === '' || this.backgroundColor === 'none') {\r\n        return 'transparent'\r\n      }\r\n      return this.backgroundColor\r\n    }\r\n  },\r\n  mounted() {\r\n    const info = uni.getSystemInfoSync()\r\n    this.popupWidth = info.windowWidth\r\n    this.popupHeight = info.windowHeight + (info.windowTop || 0)\r\n    if (info.safeArea && this.safeArea) {\r\n      this.safeAreaInsets = info.screenHeight - info.safeArea.bottom\r\n    } else {\r\n      this.safeAreaInsets = 0\r\n    }\r\n  },\r\n  unmounted() {\r\n    this.setH5Visible()\r\n  },\r\n  activated() {\r\n    // 应用程序从后台前台激活时触发\r\n    this.setH5Visible(!this.showPopup)\r\n  },\r\n  deactivated() {\r\n    // 应用程序从前台切换到后台时触发\r\n    this.setH5Visible(true)\r\n  },\r\n  created() {\r\n    if (this.isMaskClick === null && this.maskClick === null) {\r\n      this.mkclick = true\r\n    } else {\r\n      this.mkclick = this.isMaskClick !== null ? this.isMaskClick : this.maskClick\r\n    }\r\n    if (this.animation) {\r\n      this.duration = 300\r\n    } else {\r\n      this.duration = 0\r\n    }\r\n    this.messageChild = null\r\n    this.clearPropagation = false\r\n    this.maskClass.backgroundColor = this.maskBackgroundColor\r\n  },\r\n  methods: {\r\n    setH5Visible(visible = true) {\r\n      // 处理 H5 平台下应用从后台切换到前台的问题\r\n    },\r\n    closeMask() {\r\n      this.maskShow = false\r\n    },\r\n    disableMask() {\r\n      this.mkclick = false\r\n    },\r\n    clear(e) {\r\n      e.stopPropagation()\r\n      this.clearPropagation = true\r\n    },\r\n    open(type) {\r\n      if (this.showPopup) {\r\n        return\r\n      }\r\n      let valid = ['top', 'center', 'bottom', 'left', 'right', 'message', 'dialog', 'share']\r\n      if (type && valid.indexOf(type) !== -1) {\r\n        this.type = type\r\n      }\r\n      if (this.config[this.type]) {\r\n        this[this.config[this.type]]()\r\n        this.$emit('change', {\r\n          show: true,\r\n          type: this.type\r\n        })\r\n      } else {\r\n        console.error('缺少类型：', this.type)\r\n      }\r\n    },\r\n    close(type) {\r\n      this.showTrans = false\r\n      this.$emit('change', {\r\n        show: false,\r\n        type: this.type\r\n      })\r\n      clearTimeout(this.timer)\r\n      this.timer = setTimeout(() => {\r\n        this.showPopup = false\r\n      }, 300)\r\n    },\r\n    touchstart() {\r\n      this.clearPropagation = false\r\n    },\r\n    onTap() {\r\n      if (this.clearPropagation) {\r\n        this.clearPropagation = false\r\n        return\r\n      }\r\n      this.$emit('maskClick')\r\n      if (this.mkclick) {\r\n        this.close()\r\n      }\r\n    },\r\n    /**\r\n     * 顶部弹出样式处理\r\n     */\r\n    top(type) {\r\n      this.popupstyle = this.isDesktop ? 'fixforpc-top' : 'top'\r\n      this.ani = ['slide-top']\r\n      this.transClass = {\r\n        position: 'fixed',\r\n        left: 0,\r\n        right: 0,\r\n        backgroundColor: this.bg,\r\n        borderRadius: this.borderRadius || '0'\r\n      }\r\n      if (!type) {\r\n        this.showPopup = true\r\n        this.showTrans = true\r\n        this.$nextTick(() => {\r\n          if (this.messageChild && this.type === 'message') {\r\n            this.messageChild.timerClose()\r\n          }\r\n        })\r\n      }\r\n    },\r\n    /**\r\n     * 底部弹出样式处理\r\n     */\r\n    bottom(type) {\r\n      this.popupstyle = 'bottom'\r\n      this.ani = ['slide-bottom']\r\n      this.transClass = {\r\n        position: 'fixed',\r\n        left: 0,\r\n        right: 0,\r\n        bottom: 0,\r\n        paddingBottom: this.safeAreaInsets + 'px',\r\n        backgroundColor: this.bg,\r\n        borderRadius: this.borderRadius || '0'\r\n      }\r\n      if (!type) {\r\n        this.showPopup = true\r\n        this.showTrans = true\r\n      }\r\n    },\r\n    /**\r\n     * 中间弹出样式处理\r\n     */\r\n    center(type) {\r\n      this.popupstyle = 'center'\r\n      this.ani = ['fade']\r\n      this.transClass = {\r\n        position: 'fixed',\r\n        display: 'flex',\r\n        flexDirection: 'column',\r\n        bottom: 0,\r\n        left: 0,\r\n        right: 0,\r\n        top: 0,\r\n        justifyContent: 'center',\r\n        alignItems: 'center',\r\n        borderRadius: this.borderRadius || '0'\r\n      }\r\n      if (!type) {\r\n        this.showPopup = true\r\n        this.showTrans = true\r\n      }\r\n    },\r\n    left(type) {\r\n      this.popupstyle = 'left'\r\n      this.ani = ['slide-left']\r\n      this.transClass = {\r\n        position: 'fixed',\r\n        left: 0,\r\n        bottom: 0,\r\n        top: 0,\r\n        backgroundColor: this.bg,\r\n        borderRadius: this.borderRadius || '0',\r\n        display: 'flex',\r\n        flexDirection: 'column'\r\n      }\r\n      if (!type) {\r\n        this.showPopup = true\r\n        this.showTrans = true\r\n      }\r\n    },\r\n    right(type) {\r\n      this.popupstyle = 'right'\r\n      this.ani = ['slide-right']\r\n      this.transClass = {\r\n        position: 'fixed',\r\n        bottom: 0,\r\n        right: 0,\r\n        top: 0,\r\n        backgroundColor: this.bg,\r\n        borderRadius: this.borderRadius || '0',\r\n        display: 'flex',\r\n        flexDirection: 'column'\r\n      }\r\n      if (!type) {\r\n        this.showPopup = true\r\n        this.showTrans = true\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.uni-popup {\r\n  position: fixed;\r\n  z-index: 99;\r\n}\r\n\r\n.uni-popup.top,\r\n.uni-popup.left,\r\n.uni-popup.right {\r\n  top: 0;\r\n}\r\n\r\n.uni-popup .uni-popup__wrapper {\r\n  display: block;\r\n  position: relative;\r\n  /* iphonex 等安全区设置，底部安全区适配 */\r\n}\r\n\r\n.uni-popup .uni-popup__wrapper.left,\r\n.uni-popup .uni-popup__wrapper.right {\r\n  padding-top: 0;\r\n  flex: 1;\r\n}\r\n\r\n.fixforpc-z-index {\r\n  /* #ifndef APP-NVUE */\r\n  z-index: 999;\r\n  /* #endif */\r\n}\r\n\r\n.fixforpc-top {\r\n  top: 0;\r\n}\r\n</style> ", "import Component from 'D:/uniapp/vue3/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AAgBA,sBAAsB,MAAW;AAEjC,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO,CAAC,UAAU,WAAW;AAAA,EAC7B,OAAO;AAAA,IACL,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,qBAAqB;AAAA,MACnB,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,cAAc;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,SAAS,SAAS,MAAM;AACtB,YAAI,KAAK,OAAO,IAAI,GAAG;AACrB,eAAK,KAAK,OAAO,IAAI,CAAC,EAAE,IAAI;AAAA,QAC9B;AAAA,MACD;AAAA,MACD,WAAW;AAAA,IACZ;AAAA,IACD,WAAW;AAAA,MACT,SAAS,SAAS,QAAQ;AACxB,YAAI,KAAK,OAAO,MAAM,GAAG;AACvB,eAAK,KAAK,OAAO,KAAK,IAAI,CAAC,EAAE,IAAI;AAAA,QACnC;AAAA,MACD;AAAA,MACD,WAAW;AAAA,IACZ;AAAA,IACD,WAAW;AAAA,MACT,SAAS,SAAS,KAAK;AACrB,aAAK,UAAU;AAAA,MAChB;AAAA,MACD,WAAW;AAAA,IACZ;AAAA,IACD,aAAa;AAAA,MACX,SAAS,SAAS,KAAK;AACrB,aAAK,UAAU;AAAA,MAChB;AAAA,MACD,WAAW;AAAA,IACZ;AAAA,IACD,UAAU,MAAM;AAAA,IAAC;AAAA,EAClB;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,UAAU;AAAA,MACV,KAAK,CAAE;AAAA,MACP,WAAW;AAAA,MACX,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,MACR;AAAA,MACD,WAAW;AAAA,QACT,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,iBAAiB;AAAA,MAClB;AAAA,MACD,YAAY;AAAA,QACV,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,iBAAiB;AAAA,QACjB,cAAc;AAAA,MACf;AAAA,MACD,UAAU;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,YAAY;AACV,UAAI,SAAS;AAAA,QACX,iBAAiB,KAAK;AAAA,MACxB;AACA,eAAS,OAAO,OAAO,QAAQ;AAAA,QAC7B,cAAc,KAAK;AAAA,OACpB;AACD,aAAO;AAAA,IACR;AAAA,IACD,YAAY;AACV,aAAO,KAAK,cAAc,OAAO,KAAK,eAAe;AAAA,IACtD;AAAA,IACD,KAAK;AACH,UAAI,KAAK,oBAAoB,MAAM,KAAK,oBAAoB,QAAQ;AAClE,eAAO;AAAA,MACT;AACA,aAAO,KAAK;AAAA,IACd;AAAA,EACD;AAAA,EACD,UAAU;AACR,UAAM,OAAOA,cAAG,MAAC,kBAAkB;AACnC,SAAK,aAAa,KAAK;AACvB,SAAK,cAAc,KAAK,gBAAgB,KAAK,aAAa;AAC1D,QAAI,KAAK,YAAY,KAAK,UAAU;AAClC,WAAK,iBAAiB,KAAK,eAAe,KAAK,SAAS;AAAA,WACnD;AACL,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACD;AAAA,EACD,YAAY;AACV,SAAK,aAAa;AAAA,EACnB;AAAA,EACD,YAAY;AAEV,SAAK,aAAa,CAAC,KAAK,SAAS;AAAA,EAClC;AAAA,EACD,cAAc;AAEZ,SAAK,aAAa,IAAI;AAAA,EACvB;AAAA,EACD,UAAU;AACR,QAAI,KAAK,gBAAgB,QAAQ,KAAK,cAAc,MAAM;AACxD,WAAK,UAAU;AAAA,WACV;AACL,WAAK,UAAU,KAAK,gBAAgB,OAAO,KAAK,cAAc,KAAK;AAAA,IACrE;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,WAAW;AAAA,WACX;AACL,WAAK,WAAW;AAAA,IAClB;AACA,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,SAAK,UAAU,kBAAkB,KAAK;AAAA,EACvC;AAAA,EACD,SAAS;AAAA,IACP,aAAa,UAAU,MAAM;AAAA,IAE5B;AAAA,IACD,YAAY;AACV,WAAK,WAAW;AAAA,IACjB;AAAA,IACD,cAAc;AACZ,WAAK,UAAU;AAAA,IAChB;AAAA,IACD,MAAM,GAAG;AACP,QAAE,gBAAgB;AAClB,WAAK,mBAAmB;AAAA,IACzB;AAAA,IACD,KAAK,MAAM;AACT,UAAI,KAAK,WAAW;AAClB;AAAA,MACF;AACA,UAAI,QAAQ,CAAC,OAAO,UAAU,UAAU,QAAQ,SAAS,WAAW,UAAU,OAAO;AACrF,UAAI,QAAQ,MAAM,QAAQ,IAAI,MAAM,IAAI;AACtC,aAAK,OAAO;AAAA,MACd;AACA,UAAI,KAAK,OAAO,KAAK,IAAI,GAAG;AAC1B,aAAK,KAAK,OAAO,KAAK,IAAI,CAAC,EAAE;AAC7B,aAAK,MAAM,UAAU;AAAA,UACnB,MAAM;AAAA,UACN,MAAM,KAAK;AAAA,SACZ;AAAA,aACI;AACLA,sBAAc,MAAA,MAAA,SAAA,mEAAA,SAAS,KAAK,IAAI;AAAA,MAClC;AAAA,IACD;AAAA,IACD,MAAM,MAAM;AACV,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU;AAAA,QACnB,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,OACZ;AACD,mBAAa,KAAK,KAAK;AACvB,WAAK,QAAQ,WAAW,MAAM;AAC5B,aAAK,YAAY;AAAA,MAClB,GAAE,GAAG;AAAA,IACP;AAAA,IACD,aAAa;AACX,WAAK,mBAAmB;AAAA,IACzB;AAAA,IACD,QAAQ;AACN,UAAI,KAAK,kBAAkB;AACzB,aAAK,mBAAmB;AACxB;AAAA,MACF;AACA,WAAK,MAAM,WAAW;AACtB,UAAI,KAAK,SAAS;AAChB,aAAK,MAAM;AAAA,MACb;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAID,IAAI,MAAM;AACR,WAAK,aAAa,KAAK,YAAY,iBAAiB;AACpD,WAAK,MAAM,CAAC,WAAW;AACvB,WAAK,aAAa;AAAA,QAChB,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,iBAAiB,KAAK;AAAA,QACtB,cAAc,KAAK,gBAAgB;AAAA,MACrC;AACA,UAAI,CAAC,MAAM;AACT,aAAK,YAAY;AACjB,aAAK,YAAY;AACjB,aAAK,UAAU,MAAM;AACnB,cAAI,KAAK,gBAAgB,KAAK,SAAS,WAAW;AAChD,iBAAK,aAAa,WAAW;AAAA,UAC/B;AAAA,SACD;AAAA,MACH;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAID,OAAO,MAAM;AACX,WAAK,aAAa;AAClB,WAAK,MAAM,CAAC,cAAc;AAC1B,WAAK,aAAa;AAAA,QAChB,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,eAAe,KAAK,iBAAiB;AAAA,QACrC,iBAAiB,KAAK;AAAA,QACtB,cAAc,KAAK,gBAAgB;AAAA,MACrC;AACA,UAAI,CAAC,MAAM;AACT,aAAK,YAAY;AACjB,aAAK,YAAY;AAAA,MACnB;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAID,OAAO,MAAM;AACX,WAAK,aAAa;AAClB,WAAK,MAAM,CAAC,MAAM;AAClB,WAAK,aAAa;AAAA,QAChB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,eAAe;AAAA,QACf,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,cAAc,KAAK,gBAAgB;AAAA,MACrC;AACA,UAAI,CAAC,MAAM;AACT,aAAK,YAAY;AACjB,aAAK,YAAY;AAAA,MACnB;AAAA,IACD;AAAA,IACD,KAAK,MAAM;AACT,WAAK,aAAa;AAClB,WAAK,MAAM,CAAC,YAAY;AACxB,WAAK,aAAa;AAAA,QAChB,UAAU;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,iBAAiB,KAAK;AAAA,QACtB,cAAc,KAAK,gBAAgB;AAAA,QACnC,SAAS;AAAA,QACT,eAAe;AAAA,MACjB;AACA,UAAI,CAAC,MAAM;AACT,aAAK,YAAY;AACjB,aAAK,YAAY;AAAA,MACnB;AAAA,IACD;AAAA,IACD,MAAM,MAAM;AACV,WAAK,aAAa;AAClB,WAAK,MAAM,CAAC,aAAa;AACzB,WAAK,aAAa;AAAA,QAChB,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,KAAK;AAAA,QACL,iBAAiB,KAAK;AAAA,QACtB,cAAc,KAAK,gBAAgB;AAAA,QACnC,SAAS;AAAA,QACT,eAAe;AAAA,MACjB;AACA,UAAI,CAAC,MAAM;AACT,aAAK,YAAY;AACjB,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrVA,GAAG,gBAAgB,SAAS;"}