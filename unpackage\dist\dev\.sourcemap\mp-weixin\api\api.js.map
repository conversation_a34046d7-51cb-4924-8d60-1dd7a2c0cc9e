{"version": 3, "file": "api.js", "sources": ["api/api.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport request from \"@/utils/request.js\";\n/**\n * 公共接口 ，优惠券接口 , 行业此讯 , 手机号码注册\n *\n */\nexport function getAjcaptcha(data) {\n\treturn request.get(\"ajcaptcha\", data, {\n\t\tnoAuth: true\n\t});\n}\n\nexport function ajcaptchaCheck(data) {\n\treturn request.post(\"ajcheck\", data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取主页数据 无需授权\n *\n */\nexport function getIndexData() {\n\treturn request.get(\"v2/index\", {}, {\n\t\tnoAuth: true\n\t});\n}\n/**\n * 获取服务器类型\n *\n */\nexport function getServerType() {\n\treturn request.get(\"v2/site_serve\", {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取登录授权login\n *\n */\nexport function getLogo() {\n\treturn request.get('wechat/get_logo', {}, {\n\t\tnoAuth: true\n\t});\n}\n\n\n/**\n * 保存form_id\n * @param string formId\n */\nexport function setFormId(formId) {\n\treturn request.post(\"wechat/set_form_id\", {\n\t\tformId: formId\n\t});\n}\n\n/**\n * 领取优惠卷\n * @param int couponId\n *\n */\nexport function setCouponReceive(couponId) {\n\treturn request.post('coupon/receive', {\n\t\tcouponId: couponId\n\t});\n}\n/**\n * 优惠券列表\n * @param object data\n */\nexport function getCoupons(data) {\n\treturn request.get('v2/coupons', data, {\n\t\tnoAuth: true\n\t})\n}\n/**\n * 首页优惠券列表组件数据\n * @param object data\n */\nexport function getCouponsIndex(data) {\n\treturn request.get('coupons', data, {\n\t\tnoAuth: true\n\t})\n}\n\n/**\n * 我的优惠券\n * @param int types 0全部  1未使用 2已使用\n */\nexport function getUserCoupons(types, data) {\n\treturn request.get('coupons/user/' + types, data)\n}\n\n/**\n * 首页新人优惠券\n *\n */\nexport function getNewCoupon() {\n\treturn request.get('v2/new_coupon')\n}\n\n/**\n * 文章分类列表\n *\n */\nexport function getArticleCategoryList() {\n\treturn request.get('article/category/list', {}, {\n\t\tnoAuth: true\n\t})\n}\n\n/**\n * 文章列表\n * @param int cid\n *\n */\nexport function getArticleList(cid, data) {\n\treturn request.get('article/list/' + cid, data, {\n\t\tnoAuth: true\n\t})\n}\n\n/**\n * 文章 热门列表\n *\n */\nexport function getArticleHotList() {\n\treturn request.get('article/hot/list', {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 文章 轮播列表\n *\n */\nexport function getArticleBannerList() {\n\treturn request.get('article/banner/list', {}, {\n\t\tnoAuth: true\n\t})\n}\n\n/**\n * 文章详情\n * @param int id\n *\n */\nexport function getArticleDetails(id) {\n\treturn request.get('article/details/' + id, {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 手机号+验证码登录接口\n * @param object data\n */\nexport function loginMobile(data) {\n\treturn request.post('login/mobile', data, {\n\t\tnoAuth: true\n\t})\n}\n\n/**\n * 获取短信KEY\n * @param object phone\n */\nexport function verifyCode() {\n\treturn request.get('verify_code', {}, {\n\t\tnoAuth: true\n\t})\n}\n\n/**\n * 验证码发送\n * @param object phone\n */\nexport function registerVerify(phone, reset, key, captchaType, captchaVerification) {\n\treturn request.post('register/verify', {\n\t\tphone: phone,\n\t\ttype: reset === undefined ? 'reset' : reset,\n\t\tkey: key,\n\t\tcaptchaType: captchaType,\n\t\tcaptchaVerification: captchaVerification\n\t}, {\n\t\tnoAuth: true\n\t})\n}\n\n/**\n * 手机号注册\n * @param object data\n *\n */\nexport function phoneRegister(data) {\n\treturn request.post('register', data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 手机号修改密码\n * @param object data\n *\n */\nexport function phoneRegisterReset(data) {\n\treturn request.post('register/reset', data, {\n\t\tnoAuth: true\n\t})\n}\n\n/**\n * 手机号+密码登录\n * @param object data\n *\n */\nexport function phoneLogin(data) {\n\treturn request.post('login', data, {\n\t\tnoAuth: true\n\t})\n}\n\n/**\n * 切换H5登录\n * @param object data\n */\n// #ifdef MP\nexport function switchH5Login() {\n\treturn request.post('switch_h5', {\n\t\t'from': 'routine'\n\t});\n}\n// #endif\n\n/*\n * h5切换公众号登陆\n * */\n// #ifdef H5\nexport function switchH5Login() {\n\treturn request.post(\"switch_h5\", {\n\t\t'from': \"wechat\"\n\t});\n}\n// #endif\n\n/*\n * APP切换登录\n * */\n// #ifdef APP-PLUS\nexport function switchH5Login() {\n\treturn request.post(\"switch_h5\", {\n\t\t'from': \"app\"\n\t});\n}\n// #endif\n\n/**\n * 绑定手机号\n *\n */\nexport function bindingPhone(data) {\n\treturn request.post('binding', data, {\n\t\tnoAuth: true\n\t});\n}\n\n\n\n/**\n * 绑定手机号\n *\n */\nexport function bindingUserPhone(data) {\n\treturn request.post('user/binding', data);\n}\n\n/**\n * 退出登錄\n *\n */\nexport function logout() {\n\treturn request.get('logout');\n}\n\n/**\n * 获取订阅消息id\n */\nexport function getTempIds() {\n\treturn request.get('wechat/temp_ids', {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 首页拼团数据\n */\nexport function pink() {\n\treturn request.get('pink', {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取城市信息\n */\nexport function getCity() {\n\treturn request.get('city_list', {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取列表\n */\nexport function getLiveList(page, limit) {\n\treturn request.get('wechat/live', {\n\t\tpage,\n\t\tlimit\n\t}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取首页DIY；\n */\nexport function getDiy(id) {\n\treturn request.get(`v2/diy/get_diy/default${id ? '?id='+id:''}`, {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 一键换色；\n */\nexport function colorChange(name) {\n\treturn request.get('v2/diy/color_change/' + name, {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取公众号关注\n * @returns {*}\n */\nexport function follow() {\n\treturn request.get(\"wechat/follow\", {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 更换手机号码\n * @returns {*}\n */\nexport function updatePhone(data) {\n\treturn request.post(\"user/updatePhone\", data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 首页优惠券弹窗\n * @returns {*}\n */\nexport function getCouponV2() {\n\treturn request.get(\"v2/get_today_coupon\", {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 新用户优惠券弹窗\n * @returns {*}\n */\nexport function getCouponNewUser() {\n\treturn request.get(\"v2/new_coupon\", {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 首页快速选择数据\n * @param {Object} data\n */\nexport function category(data) {\n\treturn request.get(\"category\", data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 个人搜索历史\n * @param {Object} data\n */\nexport function searchList(data) {\n\treturn request.get('v2/user/search_list', data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 删除搜索历史\n */\nexport function clearSearch() {\n\treturn request.get('v2/user/clean_search');\n}\n/**\n * 获取网站基础配置\n */\nexport function siteConfig(data) {\n\treturn request.get('site_config', data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * App微信登录\n * @returns {*}\n */\nexport function wechatAppAuth(data) {\n\treturn request.post(\"wechat/app_auth\", data, {\n\t\tnoAuth: true\n\t});\n}\n/**\n * 获取客服类型\n * @returns {*}\n */\nexport function getCustomerType() {\n\treturn request.get(\"get_customer_type\", {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取开屏广告\n * @returns {*}\n */\nexport function getOpenAdv() {\n\treturn request.get(\"get_open_adv\", {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取版权信息\n */\nexport function getCrmebCopyRight() {\n\treturn request.get('copyright', {}, {\n\t\tnoAuth: true\n\t})\n}\n"], "names": ["request"], "mappings": ";;AAeO,SAAS,aAAa,MAAM;AAClC,SAAOA,sBAAQ,IAAI,aAAa,MAAM;AAAA,IACrC,QAAQ;AAAA,EACV,CAAE;AACF;AAEO,SAAS,eAAe,MAAM;AACpC,SAAOA,sBAAQ,KAAK,WAAW,MAAM;AAAA,IACpC,QAAQ;AAAA,EACV,CAAE;AACF;AAyJO,SAAS,aAAa;AAC5B,SAAOA,sBAAQ,IAAI,eAAe,IAAI;AAAA,IACrC,QAAQ;AAAA,EACV,CAAE;AACF;AAMO,SAAS,eAAe,OAAO,OAAO,KAAK,aAAa,qBAAqB;AACnF,SAAOA,cAAO,QAAC,KAAK,mBAAmB;AAAA,IACtC;AAAA,IACA,MAAM,UAAU,SAAY,UAAU;AAAA,IACtC;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAI;AAAA,IACF,QAAQ;AAAA,EACV,CAAE;AACF;AAkBO,SAAS,mBAAmB,MAAM;AACxC,SAAOA,sBAAQ,KAAK,kBAAkB,MAAM;AAAA,IAC3C,QAAQ;AAAA,EACV,CAAE;AACF;AAkBO,SAAS,gBAAgB;AAC/B,SAAOA,cAAO,QAAC,KAAK,aAAa;AAAA,IAChC,QAAQ;AAAA,EACV,CAAE;AACF;AAyCO,SAAS,iBAAiB,MAAM;AACtC,SAAOA,sBAAQ,KAAK,gBAAgB,IAAI;AACzC;AA+BO,SAAS,UAAU;AACzB,SAAOA,sBAAQ,IAAI,aAAa,IAAI;AAAA,IACnC,QAAQ;AAAA,EACV,CAAE;AACF;AA0BO,SAAS,YAAY,MAAM;AACjC,SAAOA,cAAO,QAAC,IAAI,yBAAyB,MAAM,CAAA,GAAI;AAAA,IACrD,QAAQ;AAAA,EACV,CAAE;AACF;AAgBO,SAAS,YAAY,MAAM;AACjC,SAAOA,sBAAQ,KAAK,oBAAoB,MAAM;AAAA,IAC7C,QAAQ;AAAA,EACV,CAAE;AACF;AAmDO,SAAS,WAAW,MAAM;AAChC,SAAOA,sBAAQ,IAAI,eAAe,MAAM;AAAA,IACvC,QAAQ;AAAA,EACV,CAAE;AACF;AAkCO,SAAS,oBAAoB;AACnC,SAAOA,sBAAQ,IAAI,aAAa,IAAI;AAAA,IACnC,QAAQ;AAAA,EACV,CAAE;AACF;;;;;;;;;;;;;"}