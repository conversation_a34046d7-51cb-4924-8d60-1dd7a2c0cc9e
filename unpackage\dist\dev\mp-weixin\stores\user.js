"use strict";
const common_vendor = require("../common/vendor.js");
const api_user = require("../api/user.js");
const utils_cache = require("../utils/cache.js");
const config_cache = require("../config/cache.js");
const { UID, LOGIN_STATUS, USER_INFO } = config_cache.cacheConfig;
const useUserStore = common_vendor.defineStore("user", {
  state: () => ({
    // 用户基本信息
    userInfo: {
      uid: 0,
      nickname: "",
      avatar: "",
      phone: "",
      spread_count: 0,
      integral: 0,
      now_money: 0,
      brokerage_price: 0,
      exp_num: 0,
      user_type: "",
      status: 1,
      level: 0,
      exp_name: "",
      is_promoter: 0,
      pay_count: 0,
      order_count: 0,
      coupon_count: 0,
      like_count: 0,
      follow_count: 0,
      fans_count: 0,
      visitor_count: 0,
      visitor_badge: 0,
      vip_status: 0,
      is_money_level: 0,
      svip_open: 0,
      activity_count: 0,
      activity_img: ""
    },
    // 登录状态
    isLogin: false,
    token: "",
    // 用户权限
    permissions: [],
    // 用户设置
    settings: {
      theme: "light",
      language: "zh-CN",
      notifications: true
    }
  }),
  getters: {
    // 是否已登录
    isLoggedIn: (state) => state.isLogin && !!state.token,
    // 用户ID
    uid: (state) => state.userInfo.uid || state.uid || 0,
    // 用户昵称（带默认值）
    displayName: (state) => state.userInfo.nickname || "未登录用户",
    // 用户头像（带默认值）
    displayAvatar: (state) => state.userInfo.avatar || "/static/img/avatar.png",
    // 是否为VIP用户
    isVip: (state) => state.userInfo.vip_status === 1,
    // 是否为推广员
    isPromoter: (state) => state.userInfo.is_promoter === 1,
    // 用户等级信息
    levelInfo: (state) => ({
      level: state.userInfo.level,
      exp_name: state.userInfo.exp_name,
      exp_num: state.userInfo.exp_num
    }),
    // 用户统计信息
    statsInfo: (state) => ({
      like_count: state.userInfo.like_count,
      follow_count: state.userInfo.follow_count,
      fans_count: state.userInfo.fans_count,
      visitor_count: state.userInfo.visitor_count
    })
  },
  actions: {
    // 设置登录状态
    setLoginStatus(status) {
      this.isLogin = status;
      if (!status) {
        this.token = "";
        this.resetUserInfo();
      }
    },
    // 设置token
    setToken(token) {
      this.token = token;
      this.isLogin = !!token;
      if (token) {
        common_vendor.index.setStorageSync("token", token);
        utils_cache.Cache.set(LOGIN_STATUS, token);
      } else {
        common_vendor.index.removeStorageSync("token");
        utils_cache.Cache.clear(LOGIN_STATUS);
      }
    },
    // 更新用户信息
    updateUserInfo(userInfo) {
      if (!userInfo || typeof userInfo !== "object") {
        common_vendor.index.__f__("warn", "at stores/user.js:130", "updateUserInfo: 无效的用户信息数据", userInfo);
        return;
      }
      const updatedInfo = { ...this.userInfo, ...userInfo };
      if (!updatedInfo.uid && userInfo.uid) {
        updatedInfo.uid = userInfo.uid;
      }
      this.userInfo = updatedInfo;
      try {
        common_vendor.index.setStorageSync("USER_INFO", this.userInfo);
        common_vendor.index.__f__("log", "at stores/user.js:147", "用户信息已更新到缓存:", {
          uid: this.userInfo.uid,
          nickname: this.userInfo.nickname,
          timestamp: Date.now()
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/user.js:153", "保存用户信息到缓存失败:", error);
        if (error.name === "QuotaExceededError") {
          this.clearStorageCache();
          try {
            common_vendor.index.setStorageSync("USER_INFO", this.userInfo);
          } catch (retryError) {
            common_vendor.index.__f__("error", "at stores/user.js:162", "重试保存用户信息失败:", retryError);
          }
        }
      }
    },
    // 重置用户信息
    resetUserInfo() {
      this.userInfo = {
        uid: 0,
        nickname: "",
        avatar: "",
        phone: "",
        spread_count: 0,
        integral: 0,
        now_money: 0,
        brokerage_price: 0,
        exp_num: 0,
        user_type: "",
        status: 1,
        level: 0,
        exp_name: "",
        is_promoter: 0,
        pay_count: 0,
        order_count: 0,
        coupon_count: 0,
        like_count: 0,
        follow_count: 0,
        fans_count: 0,
        visitor_count: 0,
        visitor_badge: 0,
        vip_status: 0,
        is_money_level: 0,
        svip_open: 0,
        activity_count: 0,
        activity_img: ""
      };
      common_vendor.index.removeStorageSync("USER_INFO");
    },
    // 获取用户信息
    async fetchUserInfo() {
      try {
        const res = await api_user.getUserInfo();
        if (res.status === 200) {
          this.updateUserInfo(res.data);
          return res.data;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at stores/user.js:213", "获取用户信息失败:", error);
        throw error;
      }
    },
    // 登出
    logout() {
      this.setLoginStatus(false);
      this.setToken("");
      this.resetUserInfo();
      this.permissions = [];
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync(USER_INFO);
      common_vendor.index.removeStorageSync(UID);
      common_vendor.index.removeStorageSync("pinia_user");
      utils_cache.Cache.clear(LOGIN_STATUS);
      utils_cache.Cache.clear(USER_INFO);
      utils_cache.Cache.clear(UID);
      common_vendor.index.__f__("log", "at stores/user.js:236", "用户已登出，所有存储已清理");
    },
    // 设置用户ID
    setUid(uid) {
      this.userInfo.uid = uid;
      this.uid = uid;
      try {
        common_vendor.index.setStorageSync(UID, uid);
        utils_cache.Cache.set(UID, uid);
      } catch (error) {
        common_vendor.index.__f__("warn", "at stores/user.js:249", "保存用户ID失败:", error);
      }
    },
    // 更新用户设置
    updateSettings(settings) {
      this.settings = { ...this.settings, ...settings };
    },
    // 从本地存储初始化状态
    initFromStorage() {
      try {
        let token = common_vendor.index.getStorageSync("token") || utils_cache.Cache.get(LOGIN_STATUS);
        if (token) {
          this.setToken(token);
          common_vendor.index.__f__("log", "at stores/user.js:265", "Token已恢复:", { hasToken: !!token, tokenLength: token == null ? void 0 : token.length });
        } else {
          common_vendor.index.__f__("log", "at stores/user.js:267", "未找到有效的token");
        }
        const userInfo = common_vendor.index.getStorageSync(USER_INFO) || utils_cache.Cache.get(USER_INFO, true);
        if (userInfo) {
          this.updateUserInfo(userInfo);
          common_vendor.index.__f__("log", "at stores/user.js:274", "用户信息已恢复:", { uid: userInfo.uid, nickname: userInfo.nickname });
        }
        const uid = common_vendor.index.getStorageSync(UID) || utils_cache.Cache.get(UID);
        if (uid) {
          this.userInfo.uid = uid;
          common_vendor.index.__f__("log", "at stores/user.js:281", "用户ID已恢复:", uid);
        }
        const isLoggedIn = this.isLogin && !!this.token && !!this.userInfo.uid;
        common_vendor.index.__f__("log", "at stores/user.js:286", "登录状态验证:", {
          isLogin: this.isLogin,
          hasToken: !!this.token,
          hasUid: !!this.userInfo.uid,
          finalStatus: isLoggedIn
        });
      } catch (error) {
        common_vendor.index.__f__("warn", "at stores/user.js:294", "从本地存储初始化用户状态失败:", error);
        if (error.name === "QuotaExceededError") {
          this.clearStorageCache();
        }
      }
    },
    // 清理存储缓存
    clearStorageCache() {
      try {
        const keysToRemove = [
          "pinia_social",
          "SIDEBAR_MENU",
          "APP_STATE",
          "BASIC_CONFIG"
        ];
        keysToRemove.forEach((key) => {
          try {
            common_vendor.index.removeStorageSync(key);
          } catch (e) {
            common_vendor.index.__f__("warn", "at stores/user.js:318", `清理${key}失败:`, e);
          }
        });
        common_vendor.index.__f__("log", "at stores/user.js:322", "已清理存储缓存");
      } catch (error) {
        common_vendor.index.__f__("warn", "at stores/user.js:324", "清理存储缓存失败:", error);
      }
    }
  }
});
exports.useUserStore = useUserStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/stores/user.js.map
