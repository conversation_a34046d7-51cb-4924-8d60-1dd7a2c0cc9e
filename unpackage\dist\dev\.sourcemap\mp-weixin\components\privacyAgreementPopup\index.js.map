{"version": 3, "file": "index.js", "sources": ["components/privacyAgreementPopup/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy9wcml2YWN5QWdyZWVtZW50UG9wdXAvaW5kZXgudnVl"], "sourcesContent": ["<template>\n\t<view :style=\"colorStyle\">\n\t\t<view class=\"mask\" @touchmove.prevent :hidden=\"isShow === false\"></view>\n\t\t<view class=\"product-window\" :class=\"{'on':isShow}\">\n\t\t\t<!-- 关闭 icon -->\n\t\t\t<!-- <view class=\"iconfont icon-guanbi\" @click=\"closeAttr\"></view> -->\n\t\t\t<view class=\"mp-data\">\n\t\t\t\t<text class=\"mp-name\">{{mpData.siteName}}{{$t(`服务与隐私协议`)}}</text>\n\t\t\t</view>\n\t\t\t<view class=\"trip-msg\">\n\t\t\t\t<view class=\"trip\">\n\t\t\t\t\t{{$t(`欢迎您使用${mpData.siteName}！请仔细阅读以下内容，并作出适当的选择：`)}}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"trip-title\">\n\t\t\t\t{{$t(`隐私政策概要`)}}\n\t\t\t</view>\n\t\t\t<view class=\"trip-msg\">\n\t\t\t\t<view class=\"trip\">\n\t\t\t\t\t{{$t(`当您点击同意并开始时用产品服务时，即表示您已理解并同息该条款内容，该条款将对您产生法律约束力。如您拒绝，将无法继续下一步操作。`)}}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"main-color\" @click.stop=\"privacy(3)\">{{$t(`点击阅读`)}}{{agreementName}}</view>\n\t\t\t<view class=\"bottom\">\n\t\t\t\t<button class=\"save open\" type=\"default\" id=\"agree-btn\" open-type=\"agreePrivacyAuthorization\"\n\t\t\t\t\t@agreeprivacyauthorization=\"handleAgree\">{{$t(`同意并继续`)}}</button>\n\t\t\t\t<button class=\"reject\" @click=\"rejectAgreement\">\n\t\t\t\t\t{{$t(`取消`)}}\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n\n</template>\n\n<script>\n\timport colors from \"@/mixins/color\";\n\timport {\n\t\tuserEdit,\n\t} from '@/api/user.js';\n\texport default {\n\t\tmixins: [colors],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisShow: false,\n\t\t\t\tagreementName: '',\n\t\t\t\tmpData: uni.getStorageSync('copyRight'),\n\t\t\t};\n\t\t},\n\t\tmounted() {\n\t\t\twx.getPrivacySetting({\n\t\t\t\tsuccess: res => {\n\t\t\t\t\tif (res.needAuthorization) {\n\t\t\t\t\t\t// 需要弹出隐私协议\n\t\t\t\t\t\tthis.isShow = true\n\t\t\t\t\t\tthis.agreementName = res.privacyContractName\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.$emit('onAgree');\n\t\t\t\t\t\t// 用户已经同意过隐私协议，所以不需要再弹出隐私协议，也能调用已声明过的隐私接口\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: () => {},\n\t\t\t\tcomplete: () => {}\n\t\t\t})\n\t\t},\n\t\tmethods: {\n\t\t\t// 同意\n\t\t\thandleAgree() {\n\t\t\t\tthis.isShow = false\n\t\t\t\tthis.$emit('onAgree');\n\t\t\t},\n\t\t\t// 拒绝\n\t\t\trejectAgreement() {\n\t\t\t\tthis.isShow = false\n\t\t\t\tuni.switchTab({\n\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t})\n\t\t\t\tthis.$emit('onReject');\n\t\t\t},\n\t\t\tcloseAttr() {\n\t\t\t\tthis.$emit('onCloseAgePop');\n\t\t\t},\n\t\t\t// 跳转协议\n\t\t\tprivacy(type) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: \"/pages/users/privacy/index?type=\" + type\n\t\t\t\t})\n\t\t\t},\n\t\t}\n\t}\n</script>\n<style>\n\t.pl-sty {\n\t\tcolor: #999999;\n\t\tfont-size: 30rpx;\n\t}\n</style>\n<style scoped lang=\"scss\">\n\t.product-window.on {\n\t\ttransform: translate3d(0, 0, 0);\n\t}\n\n\t.mask {\n\t\tz-index: 99;\n\t}\n\n\t.product-window {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\twidth: 100%;\n\t\tleft: 0;\n\t\tbackground-color: #fff;\n\t\tz-index: 1000;\n\t\tborder-radius: 40rpx 40rpx 0 0;\n\t\ttransform: translate3d(0, 100%, 0);\n\t\ttransition: all .3s cubic-bezier(.25, .5, .5, .9);\n\t\tpadding: 64rpx 40rpx;\n\t\tpadding-bottom: 38rpx;\n\t\tpadding-bottom: calc(38rpx + constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/\n\t\tpadding-bottom: calc(38rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);\n\n\t\t.icon-guanbi {\n\t\t\tposition: absolute;\n\t\t\ttop: 40rpx;\n\t\t\tright: 40rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-weight: bold;\n\t\t\tcolor: #999;\n\t\t}\n\n\t\t.mp-data {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tmargin-bottom: 40rpx;\n\n\t\t\t.mp-name {\n\t\t\t\tfont-size: 34rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #333333;\n\t\t\t\tline-height: 48rpx;\n\t\t\t}\n\t\t}\n\n\t\t.trip-msg {\n\t\t\tpadding-bottom: 32rpx;\n\n\t\t\t.title {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #000;\n\t\t\t\tmargin-bottom: 6rpx;\n\t\t\t}\n\n\t\t\t.trip {\n\t\t\t\tcolor: #333333;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-family: PingFang SC-Regular, PingFang SC;\n\t\t\t\tfont-weight: 400;\n\t\t\t}\n\t\t}\n\n\t\t.trip-title {\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #333333;\n\t\t\tmargin-bottom: 8rpx;\n\t\t}\n\n\t\t.main-color {\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: var(--view-theme);\n\t\t\tmargin-bottom: 40rpx;\n\t\t}\n\n\t\t.bottom {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tflex-direction: column;\n\n\t\t\t.save,\n\t\t\t.reject {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\twidth: 670rpx;\n\t\t\t\theight: 80rpx;\n\t\t\t\tborder-radius: 80rpx;\n\t\t\t\tbackground-color: #F5F5F5;\n\t\t\t\tcolor: #333;\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t}\n\n\t\t\t.save {\n\t\t\t\tbackground-color: var(--view-theme);\n\t\t\t\tcolor: #fff;\n\t\t\t\tmargin-bottom: 24rpx;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/uniapp/vue3/components/privacyAgreementPopup/index.vue'\nwx.createComponent(Component)"], "names": ["colors", "uni", "wx"], "mappings": ";;;;AAwCC,MAAK,YAAU;AAAA,EACd,QAAQ,CAACA,aAAAA,MAAM;AAAA,EACf,OAAO;AACN,WAAO;AAAA,MACN,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,QAAQC,cAAAA,MAAI,eAAe,WAAW;AAAA;EAEvC;AAAA,EACD,UAAU;AACTC,kBAAAA,KAAG,kBAAkB;AAAA,MACpB,SAAS,SAAO;AACf,YAAI,IAAI,mBAAmB;AAE1B,eAAK,SAAS;AACd,eAAK,gBAAgB,IAAI;AAAA,eACnB;AACN,eAAK,MAAM,SAAS;AAAA,QAErB;AAAA,MACA;AAAA,MACD,MAAM,MAAM;AAAA,MAAE;AAAA,MACd,UAAU,MAAM;AAAA,MAAC;AAAA,KACjB;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAER,cAAc;AACb,WAAK,SAAS;AACd,WAAK,MAAM,SAAS;AAAA,IACpB;AAAA;AAAA,IAED,kBAAkB;AACjB,WAAK,SAAS;AACdD,oBAAAA,MAAI,UAAU;AAAA,QACb,KAAK;AAAA,OACL;AACD,WAAK,MAAM,UAAU;AAAA,IACrB;AAAA,IACD,YAAY;AACX,WAAK,MAAM,eAAe;AAAA,IAC1B;AAAA;AAAA,IAED,QAAQ,MAAM;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,qCAAqC;AAAA,OAC1C;AAAA,IACD;AAAA,EACF;AACD;;;;;;;;;;;;;;;;;;;;;;ACxFD,GAAG,gBAAgB,SAAS;"}