{"version": 3, "file": "index.js", "sources": ["pages/users/user_sgin_list/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlcnMvdXNlcl9zZ2luX2xpc3QvaW5kZXgudnVl"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"content-box\">\n      <!-- 调试信息 -->\n      <view v-if=\"false\" class=\"debug-info\" style=\"padding: 20rpx; background: #f0f0f0; margin-bottom: 20rpx; border-radius: 10rpx;\">\n        <text style=\"font-size: 24rpx; color: #666;\">调试信息：</text>\n        <text style=\"font-size: 24rpx; color: #333;\">signList长度: {{signList.length}}</text>\n        <text style=\"font-size: 24rpx; color: #333;\">loading: {{loading}}</text>\n        <text style=\"font-size: 24rpx; color: #333;\">isLogin: {{isLogin}}</text>\n        <text style=\"font-size: 24rpx; color: #333;\">数据: {{JSON.stringify(signList)}}</text>\n      </view>\n\n      <!-- 加载状态 -->\n      <view v-if=\"loading && signList.length === 0\" class=\"loading-container\">\n        <view class=\"loading-indicator\"></view>\n      </view>\n\n      <!-- 签到记录列表 -->\n      <view v-else-if=\"signList.length > 0\" class=\"sign-list\">\n        <view v-for=\"(item, index) in signList\" :key=\"index\" class=\"month-group\">\n          <!-- 月份标题 -->\n          <view class=\"month-title\">{{item.month}}</view>\n\n          <!-- 签到记录项 -->\n          <view class=\"sign-items\">\n            <view\n              v-for=\"(record, recordIndex) in item.list\"\n              :key=\"recordIndex\"\n              class=\"sign-item\"\n            >\n              <view class=\"sign-info\">\n                <view class=\"sign-title\">{{$t(record.title)}}</view>\n                <view class=\"sign-date\">{{record.add_time}}</view>\n              </view>\n              <view class=\"sign-reward\">\n                <text class=\"reward-text\">+{{record.number}}</text>\n                <image class=\"reward-icon\" src=\"/static/img/sign-icon-01.png\"></image>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 底部加载状态 -->\n        <view class=\"load-more\">\n          <view v-if=\"loading\" class=\"loading-text\">加载中...</view>\n          <view v-else-if=\"loadend\" class=\"no-more-text\">没有更多内容</view>\n          <view v-else class=\"load-more-text\" @tap=\"getSignMoneList\">加载更多</view>\n        </view>\n      </view>\n\n      <!-- 空状态 -->\n      <emptyPage\n        v-else\n        title=\"暂无签到记录\"\n        description=\"快去签到获得奖励吧\"\n        buttonText=\"去签到\"\n        @buttonClick=\"goToSign\"\n      />\n    </view>\n\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\n      <view class=\"tips-box df\">\n        <view class=\"tips-item bfh\">{{ tipsTitle }}</view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\n\timport { getSignMonthList } from '@/api/user.js';\n\timport { toLogin } from '@/libs/login.js';\n\t import { mapGetters } from \"vuex\";\n\t import emptyPage from '@/components/emptyPage/emptyPage.vue';\n\t // #ifdef MP\n\t import authorize from '@/components/Authorize';\n\t // #endif\n\texport default {\n\t\tcomponents: {\n\t\t\temptyPage,\n\t\t\t// #ifdef MP\n\t\t\tauthorize\n\t\t\t// #endif\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttipsTitle: '',\n\t\t\t\tloading: false,\n\t\t\t\tloadend: false,\n\t\t\t\tloadtitle: this.$t(`加载更多`),\n\t\t\t\tpage: 1,\n\t\t\t\tlimit: 8,\n\t\t\t\tsignList: [],\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\n\t\t\t\tisShowAuth: false //是否隐藏授权\n\t\t\t};\n\t\t},\n\t\tcomputed: mapGetters(['isLogin']),\n\t\twatch:{\n\t\t\tisLogin:{\n\t\t\t\thandler:function(newV,oldV){\n\t\t\t\t\tif(newV){\n\t\t\t\t\t\tthis.getSignMoneList();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep:true\n\t\t\t}\n\t\t},\n\t\tonLoad(){\n\t\t\tif(this.isLogin){\n\t\t\t\tthis.getSignMoneList();\n\t\t\t}else{\n\t\t\t\ttoLogin();\n\t\t\t}\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tthis.page = 1;\n\t\t\tthis.loadend = false;\n\t\t\tthis.signList = [];\n\t\t\tthis.getSignMoneList();\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t}, 1000);\n\t\t},\n\t\tonReachBottom: function () {\n\t\t    if (!this.loadend && !this.loading) {\n\t\t      this.getSignMoneList();\n\t\t    }\n\t\t  },\n\t\tmethods: {\n\t\t\t// 显示提示信息\n\t\t\topTipsPopup(msg) {\n\t\t\t\tthis.tipsTitle = msg;\n\t\t\t\tthis.$refs.tipsPopup.open();\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.$refs.tipsPopup.close();\n\t\t\t\t}, 2000);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t *\n\t\t\t * 授权回调\n\t\t\t*/\n\t\t\tonLoadFun:function(){\n\t\t\t\tthis.getSignMoneList();\n\t\t\t},\n\t\t\t// 授权关闭\n\t\t\tauthColse:function(e){\n\t\t\t\tthis.isShowAuth = e\n\t\t\t},\n\t\t\t  /**\n\t\t\t     * 获取签到记录列表\n\t\t\t    */\n\t\t\t    getSignMoneList:function(){\n\t\t\t      let that=this;\n\t\t\t      if(that.loading) return;\n\t\t\t      if(that.loadend) return;\n\t\t\t\t  that.loading = true;\n\t\t\t\t  that.loadtitle = \"\";\n\t\t\t      getSignMonthList({ page: that.page, limit: that.limit }).then(res=>{\n\t\t\t        console.log('签到记录API响应:', res);\n\t\t\t        let list = res.data || [];\n\t\t\t        let loadend = list.length < that.limit;\n\n\t\t\t        // 如果是第一页，直接赋值；否则合并数据\n\t\t\t        if (that.page === 1) {\n\t\t\t          that.signList = list;\n\t\t\t        } else {\n\t\t\t          that.signList = that.signList.concat(list);\n\t\t\t        }\n\n\t\t\t\t\tthat.$set(that,'signList',that.signList);\n\t\t\t\t\tthat.page++;\n\t\t\t\t\tthat.loadend = loadend;\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tthat.loadtitle = loadend ? that.$t(`我也是有底线的`) : that.$t(`加载更多`);\n\n\t\t\t\t\tconsole.log('处理后的签到列表:', that.signList);\n\t\t\t      }).catch(err=>{\n\t\t\t        console.error('获取签到记录失败:', err);\n\t\t\t\t\tthat.loading = false;\n\t\t\t\t\tthat.loadtitle = that.$t(`加载更多`);\n\t\t\t      });\n\t\t\t    },\n\n\t\t\t    // 去签到\n\t\t\t    goToSign() {\n\t\t\t      uni.navigateTo({\n\t\t\t        url: '/pages/users/user_sgin/index'\n\t\t\t      });\n\t\t\t    }\n\t\t}\n\t}\n</script>\n\n<style>\n.content-box {\n  width: calc(100% - 60rpx);\n  padding: 30rpx;\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n\n/* 加载中状态样式 */\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 60rpx;\n  margin-bottom: 20rpx;\n}\n.loading-indicator {\n  width: 30rpx;\n  height: 30rpx;\n  border: 3rpx solid #f3f3f3;\n  border-top: 3rpx solid #000;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 签到记录列表样式 */\n.sign-list {\n  width: 100%;\n}\n\n.month-group {\n  margin-bottom: 40rpx;\n}\n\n.month-title {\n  font-size: 28rpx;\n  color: #999;\n  margin-bottom: 20rpx;\n  padding-left: 10rpx;\n}\n\n.sign-items {\n  background: #fff;\n  border-radius: 16rpx;\n  overflow: hidden;\n}\n\n.sign-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 32rpx 30rpx;\n  border-bottom: 1rpx solid #f5f5f5;\n}\n\n.sign-item:last-child {\n  border-bottom: none;\n}\n\n.sign-info {\n  flex: 1;\n}\n\n.sign-title {\n  font-size: 30rpx;\n  color: #333;\n  font-weight: 500;\n  margin-bottom: 8rpx;\n  line-height: 1.4;\n}\n\n.sign-date {\n  font-size: 26rpx;\n  color: #999;\n}\n\n.sign-reward {\n  display: flex;\n  align-items: center;\n  gap: 8rpx;\n}\n\n.reward-text {\n  font-size: 32rpx;\n  color: #ff6b35;\n  font-weight: bold;\n}\n\n.reward-icon {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n/* 底部加载状态 */\n.load-more {\n  text-align: center;\n  padding: 40rpx 0;\n}\n\n.loading-text,\n.no-more-text,\n.load-more-text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.load-more-text {\n  color: #007aff;\n}\n\n/* 空状态 */\n.empty-box {\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n}\n.empty-box image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 30rpx;\n}\n.empty-box .e1 {\n  font-size: 28rpx;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n}\n.empty-box .e2 {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.df {\n  display: flex;\n  align-items: center;\n}\n.bfh {\n  background: #000;\n  color: #fff;\n  padding: 20rpx 40rpx;\n  border-radius: 12rpx;\n  font-size: 24rpx;\n  font-weight: 700;\n}\n.tips-box {\n  justify-content: center;\n  width: 100%;\n}\n</style>\n", "import MiniProgramPage from 'D:/uniapp/vue3/pages/users/user_sgin_list/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["mapGetters", "<PERSON><PERSON><PERSON><PERSON>", "uni", "getSignMonthList"], "mappings": ";;;;;AAwEE,MAAK,YAAa,MAAW;AAE7B,MAAO,YAAW,MAAW;AAE9B,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,IAEA;AAAA,EAEA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,MACT,WAAW,KAAK,GAAG,MAAM;AAAA,MACzB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,CAAE;AAAA,MACZ,QAAQ;AAAA;AAAA,MACR,YAAY;AAAA;AAAA;EAEb;AAAA,EACD,UAAUA,cAAAA,WAAW,CAAC,SAAS,CAAC;AAAA,EAChC,OAAM;AAAA,IACL,SAAQ;AAAA,MACP,SAAQ,SAAS,MAAK,MAAK;AAC1B,YAAG,MAAK;AACP,eAAK,gBAAe;AAAA,QACrB;AAAA,MACA;AAAA,MACD,MAAK;AAAA,IACN;AAAA,EACA;AAAA,EACD,SAAQ;AACP,QAAG,KAAK,SAAQ;AACf,WAAK,gBAAe;AAAA,WAChB;AACJC,iBAAAA;IACD;AAAA,EACA;AAAA,EACD,oBAAoB;AACnB,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,gBAAe;AACpB,eAAW,MAAM;AAChBC,oBAAG,MAAC,oBAAmB;AAAA,IACvB,GAAE,GAAI;AAAA,EACP;AAAA,EACD,eAAe,WAAY;AACvB,QAAI,CAAC,KAAK,WAAW,CAAC,KAAK,SAAS;AAClC,WAAK,gBAAe;AAAA,IACtB;AAAA,EACD;AAAA,EACH,SAAS;AAAA;AAAA,IAER,YAAY,KAAK;AAChB,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU;AACrB,iBAAW,MAAM;AAChB,aAAK,MAAM,UAAU;MACrB,GAAE,GAAI;AAAA,IACP;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,WAAU,WAAU;AACnB,WAAK,gBAAe;AAAA,IACpB;AAAA;AAAA,IAED,WAAU,SAAS,GAAE;AACpB,WAAK,aAAa;AAAA,IAClB;AAAA;AAAA;AAAA;AAAA,IAIG,iBAAgB,WAAU;AACxB,UAAI,OAAK;AACT,UAAG,KAAK;AAAS;AACjB,UAAG,KAAK;AAAS;AACpB,WAAK,UAAU;AACf,WAAK,YAAY;AACdC,eAAAA,iBAAiB,EAAE,MAAM,KAAK,MAAM,OAAO,KAAK,MAAO,CAAA,EAAE,KAAK,SAAK;AACjED,sBAAY,MAAA,MAAA,OAAA,+CAAA,cAAc,GAAG;AAC7B,YAAI,OAAO,IAAI,QAAQ;AACvB,YAAI,UAAU,KAAK,SAAS,KAAK;AAGjC,YAAI,KAAK,SAAS,GAAG;AACnB,eAAK,WAAW;AAAA,eACX;AACL,eAAK,WAAW,KAAK,SAAS,OAAO,IAAI;AAAA,QAC3C;AAEN,aAAK,KAAK,MAAK,YAAW,KAAK,QAAQ;AACvC,aAAK;AACL,aAAK,UAAU;AACf,aAAK,UAAU;AACf,aAAK,YAAY,UAAU,KAAK,GAAG,SAAS,IAAI,KAAK,GAAG,MAAM;AAE9DA,wFAAY,aAAa,KAAK,QAAQ;AAAA,MAClC,CAAC,EAAE,MAAM,SAAK;AACZA,sBAAc,MAAA,MAAA,SAAA,+CAAA,aAAa,GAAG;AACpC,aAAK,UAAU;AACf,aAAK,YAAY,KAAK,GAAG,MAAM;AAAA,MAC3B,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,WAAW;AACTA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACL;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9LD,GAAG,WAAW,eAAe;"}