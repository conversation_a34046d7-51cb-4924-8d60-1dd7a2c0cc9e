{"version": 3, "file": "lang.js", "sources": ["utils/lang.js"], "sourcesContent": ["import { createI18n } from 'vue-i18n'\r\nimport Cache from '@/utils/cache.js';\r\n\r\nlet lang = '';\r\n// #ifdef MP || APP-PLUS\r\nlang = Cache.has('locale') ? Cache.get('locale') : 'zh-CN';\r\n// #endif\r\n// #ifdef H5\r\nlang = Cache.has('locale') ? Cache.get('locale') : navigator.language;\r\n// #endif\r\n\r\nconst i18n = createI18n({\r\n\tlocale: lang,\r\n\tfallbackLocale: 'zh-CN',\r\n\tmessages: uni.getStorageSync('localeJson') || {},\r\n\tlegacy: false, // Vue3 Composition API模式\r\n\tglobalInjection: true, // 全局注入$t函数\r\n\tsilentTranslationWarn: true, // 去除国际化警告\r\n})\r\n\r\nexport default i18n\r\n"], "names": ["<PERSON><PERSON>", "createI18n", "uni"], "mappings": ";;;AAGA,IAAI,OAAO;AAEX,OAAOA,YAAK,MAAC,IAAI,QAAQ,IAAIA,YAAK,MAAC,IAAI,QAAQ,IAAI;AAM9C,MAAC,OAAOC,cAAAA,WAAW;AAAA,EACvB,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,UAAUC,cAAG,MAAC,eAAe,YAAY,KAAK,CAAE;AAAA,EAChD,QAAQ;AAAA;AAAA,EACR,iBAAiB;AAAA;AAAA,EACjB,uBAAuB;AAAA;AACxB,CAAC;;"}