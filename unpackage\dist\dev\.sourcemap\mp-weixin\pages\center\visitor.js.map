{"version": 3, "file": "visitor.js", "sources": ["pages/center/visitor.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2VudGVyL3Zpc2l0b3IudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 选项卡 -->\r\n    <view class=\"tab-bar\">\r\n      <view\r\n        v-for=\"(tab, index) in tabList\"\r\n        :key=\"index\"\r\n        class=\"tab-item\"\r\n        :class=\"{ active: currentTab === index }\"\r\n        @tap=\"switchTab(index)\"\r\n      >\r\n        <text class=\"tab-text\">{{ tab }}</text>\r\n        <view class=\"tab-line\" v-if=\"currentTab === index\"></view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 访客统计 -->\r\n    <view class=\"visitor-stats\">\r\n      <view class=\"stats-item\">\r\n        <text class=\"stats-number\">{{ totalVisitors }}</text>\r\n        <text class=\"stats-label\">{{ currentTab === 0 ? '总访客数' : '总浏览数' }}</text>\r\n      </view>\r\n      <view class=\"stats-divider\"></view>\r\n      <view class=\"stats-item\">\r\n        <text class=\"stats-number\">{{ todayVisitors }}</text>\r\n        <text class=\"stats-label\">{{ currentTab === 0 ? '今日访客' : '今日浏览' }}</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 访客列表 -->\r\n    <view class=\"visitor-list\">\r\n      <!-- 空状态 -->\r\n      <emptyPage\r\n        v-if=\"isEmpty && !loading\"\r\n        title=\"暂无访客记录\"\r\n        description=\"快去分享你的主页吸引更多访客吧~\"\r\n        image=\"/static/img/empty.png\"\r\n      />\r\n\r\n      <!-- 访客列表 -->\r\n      <view v-else class=\"visitor-list-content\">\r\n        <view\r\n          v-for=\"(visitor, index) in visitorList\"\r\n          :key=\"visitor.visitor_uid || index\"\r\n          class=\"visitor-item\"\r\n          @tap=\"goToUserProfile(visitor.visitor_uid)\"\r\n        >\r\n          <view class=\"visitor-row\">\r\n            <view class=\"avatar-wrapper\">\r\n              <image\r\n                :src=\"visitor.avatar || '/static/img/avatar.png'\"\r\n                class=\"visitor-avatar\"\r\n                mode=\"aspectFill\"\r\n              ></image>\r\n              <!-- VIP标识 -->\r\n              <view v-if=\"visitor.vip\" class=\"vip-badge\">\r\n                <image :src=\"visitor.vip_icon\" class=\"vip-icon\"></image>\r\n              </view>\r\n            </view>\r\n\r\n            <view class=\"visitor-info\">\r\n              <view class=\"visitor-name-row\">\r\n                <text class=\"visitor-name\">{{ visitor.nickname || '匿名用户' }}</text>\r\n                <!-- 性别图标 -->\r\n                <image\r\n                  v-if=\"visitor.sex === 1\"\r\n                  src=\"/static/img/male.png\"\r\n                  class=\"gender-icon\"\r\n                ></image>\r\n                <image\r\n                  v-else-if=\"visitor.sex === 2\"\r\n                  src=\"/static/img/female.png\"\r\n                  class=\"gender-icon\"\r\n                ></image>\r\n                <!-- 认证标识 -->\r\n                <image\r\n                  v-if=\"visitor.auth_status === 2\"\r\n                  src=\"/static/img/rz.png\"\r\n                  class=\"auth-icon\"\r\n                ></image>\r\n              </view>\r\n\r\n              <!-- 访问信息 -->\r\n              <view class=\"visit-info\">\r\n                <text class=\"visit-time\">{{ formatVisitTime(visitor.last_visit_time_str || visitor.last_visit_time) }}</text>\r\n                <text class=\"visit-count\">访问{{ visitor.visit_count }}次</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载更多 -->\r\n    <uni-load-more\r\n      v-if=\"!isEmpty\"\r\n      :status=\"loadStatus\"\r\n      :content-text=\"loadText\"\r\n      @clickLoadMore=\"loadMore\"\r\n    ></uni-load-more>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue'\r\nimport { onLoad, onShow, onHide, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'\r\nimport { useStore } from 'vuex'\r\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more'\r\nimport emptyPage from '@/components/emptyPage/emptyPage.vue'\r\nimport { getVisitorDetails } from '@/api/social.js'\r\n\r\n// 定义组件名称\r\ndefineOptions({\r\n  name: 'VisitorPage'\r\n})\r\n\r\n// 使用store\r\nconst store = useStore()\r\n\r\n// 计算属性\r\nconst isLogin = computed(() => store.getters.isLogin)\r\nconst userInfo = computed(() => store.getters.userInfo)\r\n\r\n// 响应式数据\r\nconst tabList = ref(['谁看过我', '我看过谁'])\r\nconst currentTab = ref(0)\r\nconst visitorList = ref([])\r\nconst page = ref(1)\r\nconst limit = ref(20)\r\nconst totalVisitors = ref(0)\r\nconst todayVisitors = ref(0)\r\nconst loading = ref(false)\r\nconst isEmpty = ref(false)\r\nconst loadStatus = ref('more') // more, loading, noMore\r\nconst dataFromCenter = ref(false) // 标记是否从center页面获取了数据\r\n\r\n// 加载文本配置\r\nconst loadText = reactive({\r\n  contentdown: '上拉显示更多',\r\n  contentrefresh: '正在加载...',\r\n  contentnomore: '没有更多数据了'\r\n})\r\n\r\n// 初始化页面\r\nconst initPage = () => {\r\n  if (!isLogin.value) {\r\n    uni.showToast({\r\n      title: '请先登录',\r\n      icon: 'none'\r\n    })\r\n    setTimeout(() => {\r\n      uni.navigateBack()\r\n    }, 1500)\r\n    return\r\n  }\r\n\r\n  // 延迟加载，等待可能的center页面数据传递\r\n  setTimeout(() => {\r\n    if (visitorList.value.length === 0) {\r\n      loadVisitorData()\r\n    }\r\n  }, 100)\r\n}\r\n\r\n// 切换选项卡\r\nconst switchTab = (index) => {\r\n  if (currentTab.value === index || loading.value) return\r\n\r\n  currentTab.value = index\r\n  page.value = 1\r\n  visitorList.value = []\r\n  isEmpty.value = false\r\n  loadStatus.value = 'more'\r\n  dataFromCenter.value = false // 重置标志位\r\n\r\n  // 重新加载数据\r\n  loadVisitorData(true)\r\n}\r\n\r\n// 加载访客数据\r\nconst loadVisitorData = async (isRefresh = false) => {\r\n  if (loading.value) return\r\n\r\n  if (isRefresh) {\r\n    page.value = 1\r\n    visitorList.value = []\r\n    isEmpty.value = false\r\n    loadStatus.value = 'more'\r\n  }\r\n\r\n  loading.value = true\r\n\r\n  try {\r\n    const res = await getVisitorDetails({\r\n      page: page.value,\r\n      limit: limit.value,\r\n      type: currentTab.value // 0-谁看过我，1-我看过谁\r\n    })\r\n\r\n    if (res.status === 200 || res.code === 200) {\r\n      const data = res.data || {}\r\n      const list = data.list || []\r\n\r\n      if (isRefresh) {\r\n        visitorList.value = list\r\n      } else {\r\n        visitorList.value = [...visitorList.value, ...list]\r\n      }\r\n\r\n      totalVisitors.value = data.total || 0\r\n      calculateTodayVisitors(list)\r\n\r\n      // 判断是否还有更多数据\r\n      const hasMore = page.value * limit.value < totalVisitors.value\r\n      if (!hasMore) {\r\n        loadStatus.value = 'noMore'\r\n      }\r\n\r\n      // 判断是否为空\r\n      isEmpty.value = visitorList.value.length === 0\r\n\r\n    } else {\r\n      handleLoadError(res.msg || '获取访客数据失败')\r\n    }\r\n\r\n  } catch (error) {\r\n    handleLoadError('网络错误，请稍后重试')\r\n  } finally {\r\n    loading.value = false\r\n\r\n    if (isRefresh) {\r\n      uni.stopPullDownRefresh()\r\n    }\r\n  }\r\n}\r\n\r\n// 处理加载错误\r\nconst handleLoadError = (message) => {\r\n  uni.showToast({\r\n    title: message,\r\n    icon: 'none'\r\n  })\r\n\r\n  if (page.value === 1) {\r\n    isEmpty.value = true\r\n  }\r\n}\r\n\r\n// 计算今日访客数\r\nconst calculateTodayVisitors = (list) => {\r\n  const today = new Date()\r\n  const todayStr = today.getFullYear() + '-' +\r\n    String(today.getMonth() + 1).padStart(2, '0') + '-' +\r\n    String(today.getDate()).padStart(2, '0')\r\n\r\n  todayVisitors.value = list.filter(visitor => {\r\n    // 处理datetime字符串格式 \"2025-07-20 22:21:57\"\r\n    if (typeof visitor.last_visit_time_str === 'string') {\r\n      const visitDate = visitor.last_visit_time_str.split(' ')[0] // 获取日期部分\r\n      return visitDate === todayStr\r\n    } else if (typeof visitor.last_visit_time === 'string') {\r\n      const visitDate = visitor.last_visit_time.split(' ')[0] // 获取日期部分\r\n      return visitDate === todayStr\r\n    }\r\n    return false\r\n  }).length\r\n}\r\n\r\n// 刷新数据\r\nconst refreshData = () => {\r\n  loadVisitorData(true)\r\n}\r\n\r\n// 加载更多\r\nconst loadMore = () => {\r\n  if (loadStatus.value === 'noMore' || loading.value) return\r\n\r\n  loadStatus.value = 'loading'\r\n  page.value++\r\n  loadVisitorData()\r\n}\r\n\r\n// 处理从center页面传来的访客数据更新\r\nconst handleVisitorUpdate = (data) => {\r\n  if (data && data.visitors && !dataFromCenter.value) {\r\n    visitorList.value = data.visitors\r\n    totalVisitors.value = data.total || 0\r\n    calculateTodayVisitors(data.visitors)\r\n    isEmpty.value = visitorList.value.length === 0\r\n\r\n    // 更新分页信息\r\n    if (data.page) {\r\n      page.value = data.page\r\n    }\r\n    if (data.limit) {\r\n      limit.value = data.limit\r\n    }\r\n\r\n    // 更新加载状态\r\n    loadStatus.value = data.has_more ? 'more' : 'noMore'\r\n\r\n    // 标记已从center获取数据\r\n    dataFromCenter.value = true\r\n  }\r\n}\r\n\r\n// 跳转到用户主页\r\nconst goToUserProfile = (uid) => {\r\n  if (!uid) return\r\n\r\n  uni.navigateTo({\r\n    url: `/pages/user/details?uid=${uid}`\r\n  })\r\n}\r\n\r\n// 格式化访问时间\r\nconst formatVisitTime = (timeStr) => {\r\n  if (!timeStr) return ''\r\n\r\n  // 处理字符串时间格式 \"2025-07-20 22:21:57\"\r\n  let visitTime\r\n  if (typeof timeStr === 'string') {\r\n    visitTime = new Date(timeStr).getTime()\r\n  } else if (typeof timeStr === 'number') {\r\n    // 如果是时间戳，判断是秒还是毫秒\r\n    visitTime = timeStr > 1000000000000 ? timeStr : timeStr * 1000\r\n  } else {\r\n    return ''\r\n  }\r\n\r\n  const now = Date.now()\r\n  const diff = now - visitTime\r\n\r\n  // 小于1分钟\r\n  if (diff < 60 * 1000) {\r\n    return '刚刚'\r\n  }\r\n\r\n  // 小于1小时\r\n  if (diff < 60 * 60 * 1000) {\r\n    const minutes = Math.floor(diff / (60 * 1000))\r\n    return `${minutes}分钟前`\r\n  }\r\n\r\n  // 小于1天\r\n  if (diff < 24 * 60 * 60 * 1000) {\r\n    const hours = Math.floor(diff / (60 * 60 * 1000))\r\n    return `${hours}小时前`\r\n  }\r\n\r\n  // 小于7天\r\n  if (diff < 7 * 24 * 60 * 60 * 1000) {\r\n    const days = Math.floor(diff / (24 * 60 * 60 * 1000))\r\n    return `${days}天前`\r\n  }\r\n\r\n  // 超过7天显示具体日期\r\n  const date = new Date(visitTime)\r\n  const month = date.getMonth() + 1\r\n  const day = date.getDate()\r\n  return `${month}月${day}日`\r\n}\r\n\r\n// 生命周期钩子\r\nonLoad(() => {\r\n  initPage()\r\n})\r\n\r\nonShow(() => {\r\n  // 监听从center页面传来的访客数据更新\r\n  uni.$on('updateVisitorList', handleVisitorUpdate)\r\n\r\n  // 如果是从其他页面返回，且没有数据，则重新加载\r\n  if (visitorList.value.length === 0 && !loading.value) {\r\n    loadVisitorData(true)\r\n  }\r\n})\r\n\r\nonHide(() => {\r\n  // 移除事件监听\r\n  uni.$off('updateVisitorList', handleVisitorUpdate)\r\n})\r\n\r\nonPullDownRefresh(() => {\r\n  refreshData()\r\n})\r\n\r\nonReachBottom(() => {\r\n  loadMore()\r\n})\r\n\r\n// 组件卸载时清理\r\nonUnmounted(() => {\r\n  uni.$off('updateVisitorList', handleVisitorUpdate)\r\n})\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n/* 选项卡样式 */\r\n.tab-bar {\r\n  display: flex;\r\n  background: white;\r\n  padding: 0 20rpx;\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 20rpx 0;\r\n  position: relative;\r\n}\r\n\r\n.tab-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.tab-item.active .tab-text {\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.tab-line {\r\n  position: absolute;\r\n  bottom: 0;\r\n  width: 60rpx;\r\n  height: 4rpx;\r\n  background: #333;\r\n  border-radius: 2rpx;\r\n}\r\n\r\n/* 访客统计 */\r\n.visitor-stats {\r\n  display: flex;\r\n  background: white;\r\n  padding: 20rpx 30rpx;\r\n  margin-bottom: 10rpx;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.stats-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  flex: 1;\r\n}\r\n\r\n.stats-number {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  line-height: 1.2;\r\n}\r\n\r\n.stats-label {\r\n  font-size: 22rpx;\r\n  color: #999;\r\n  margin-top: 6rpx;\r\n}\r\n\r\n.stats-divider {\r\n  width: 1rpx;\r\n  height: 40rpx;\r\n  background-color: #eee;\r\n}\r\n\r\n/* 访客列表 */\r\n.visitor-list {\r\n  flex: 1;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 120rpx 40rpx;\r\n  text-align: center;\r\n}\r\n\r\n.empty-image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 32rpx;\r\n  color: #666;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.empty-desc {\r\n  font-size: 26rpx;\r\n  color: #999;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 访客列表 */\r\n.visitor-list-content {\r\n  background: white;\r\n}\r\n\r\n.visitor-item {\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.visitor-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n/* 访客行信息 */\r\n.visitor-row {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx;\r\n}\r\n\r\n.avatar-wrapper {\r\n  position: relative;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.visitor-avatar {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  background-color: #f0f0f0;\r\n}\r\n\r\n.vip-badge {\r\n  position: absolute;\r\n  bottom: -3rpx;\r\n  right: -3rpx;\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  border-radius: 50%;\r\n  background: white;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.vip-icon {\r\n  width: 18rpx;\r\n  height: 18rpx;\r\n}\r\n\r\n.visitor-info {\r\n  flex: 1;\r\n}\r\n\r\n.visitor-name-row {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.visitor-name {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-right: 8rpx;\r\n}\r\n\r\n.gender-icon,\r\n.auth-icon {\r\n  width: 24rpx;\r\n  height: 24rpx;\r\n  margin-left: 6rpx;\r\n}\r\n\r\n/* 访问信息 */\r\n.visit-info {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.visit-time {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.visit-count {\r\n  font-size: 24rpx;\r\n  color: #1890ff;\r\n}\r\n\r\n/* 响应式适配 */\r\n@media screen and (max-width: 750rpx) {\r\n  .visitor-row {\r\n    padding: 15rpx 20rpx;\r\n  }\r\n\r\n  .visitor-avatar {\r\n    width: 70rpx;\r\n    height: 70rpx;\r\n  }\r\n\r\n  .visitor-name {\r\n    font-size: 26rpx;\r\n  }\r\n\r\n  .visitor-stats {\r\n    padding: 15rpx 20rpx;\r\n  }\r\n\r\n  .stats-number {\r\n    font-size: 32rpx;\r\n  }\r\n\r\n  .tab-text {\r\n    font-size: 26rpx;\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/uniapp/vue3/pages/center/visitor.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useStore", "computed", "ref", "reactive", "uni", "getVisitorDetails", "onLoad", "onShow", "onHide", "onPullDownRefresh", "onReachBottom", "onUnmounted"], "mappings": ";;;;;;;AA2GA,MAAM,cAAc,MAAW;AAC/B,MAAM,YAAY,MAAW;;;;;;AAS7B,UAAM,QAAQA,cAAAA,SAAU;AAGxB,UAAM,UAAUC,cAAAA,SAAS,MAAM,MAAM,QAAQ,OAAO;AACnCA,kBAAAA,SAAS,MAAM,MAAM,QAAQ,QAAQ;AAGtD,UAAM,UAAUC,cAAG,IAAC,CAAC,QAAQ,MAAM,CAAC;AACpC,UAAM,aAAaA,cAAG,IAAC,CAAC;AACxB,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,OAAOA,cAAG,IAAC,CAAC;AAClB,UAAM,QAAQA,cAAG,IAAC,EAAE;AACpB,UAAM,gBAAgBA,cAAG,IAAC,CAAC;AAC3B,UAAM,gBAAgBA,cAAG,IAAC,CAAC;AAC3B,UAAM,UAAUA,cAAG,IAAC,KAAK;AACzB,UAAM,UAAUA,cAAG,IAAC,KAAK;AACzB,UAAM,aAAaA,cAAG,IAAC,MAAM;AAC7B,UAAM,iBAAiBA,cAAG,IAAC,KAAK;AAGhC,UAAM,WAAWC,cAAAA,SAAS;AAAA,MACxB,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,eAAe;AAAA,IACjB,CAAC;AAGD,UAAM,WAAW,MAAM;AACrB,UAAI,CAAC,QAAQ,OAAO;AAClBC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,mBAAW,MAAM;AACfA,wBAAAA,MAAI,aAAc;AAAA,QACnB,GAAE,IAAI;AACP;AAAA,MACD;AAGD,iBAAW,MAAM;AACf,YAAI,YAAY,MAAM,WAAW,GAAG;AAClC,0BAAiB;AAAA,QAClB;AAAA,MACF,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,YAAY,CAAC,UAAU;AAC3B,UAAI,WAAW,UAAU,SAAS,QAAQ;AAAO;AAEjD,iBAAW,QAAQ;AACnB,WAAK,QAAQ;AACb,kBAAY,QAAQ,CAAE;AACtB,cAAQ,QAAQ;AAChB,iBAAW,QAAQ;AACnB,qBAAe,QAAQ;AAGvB,sBAAgB,IAAI;AAAA,IACtB;AAGA,UAAM,kBAAkB,OAAO,YAAY,UAAU;AACnD,UAAI,QAAQ;AAAO;AAEnB,UAAI,WAAW;AACb,aAAK,QAAQ;AACb,oBAAY,QAAQ,CAAE;AACtB,gBAAQ,QAAQ;AAChB,mBAAW,QAAQ;AAAA,MACpB;AAED,cAAQ,QAAQ;AAEhB,UAAI;AACF,cAAM,MAAM,MAAMC,6BAAkB;AAAA,UAClC,MAAM,KAAK;AAAA,UACX,OAAO,MAAM;AAAA,UACb,MAAM,WAAW;AAAA;AAAA,QACvB,CAAK;AAED,YAAI,IAAI,WAAW,OAAO,IAAI,SAAS,KAAK;AAC1C,gBAAM,OAAO,IAAI,QAAQ,CAAE;AAC3B,gBAAM,OAAO,KAAK,QAAQ,CAAE;AAE5B,cAAI,WAAW;AACb,wBAAY,QAAQ;AAAA,UAC5B,OAAa;AACL,wBAAY,QAAQ,CAAC,GAAG,YAAY,OAAO,GAAG,IAAI;AAAA,UACnD;AAED,wBAAc,QAAQ,KAAK,SAAS;AACpC,iCAAuB,IAAI;AAG3B,gBAAM,UAAU,KAAK,QAAQ,MAAM,QAAQ,cAAc;AACzD,cAAI,CAAC,SAAS;AACZ,uBAAW,QAAQ;AAAA,UACpB;AAGD,kBAAQ,QAAQ,YAAY,MAAM,WAAW;AAAA,QAEnD,OAAW;AACL,0BAAgB,IAAI,OAAO,UAAU;AAAA,QACtC;AAAA,MAEF,SAAQ,OAAO;AACd,wBAAgB,YAAY;AAAA,MAChC,UAAY;AACR,gBAAQ,QAAQ;AAEhB,YAAI,WAAW;AACbD,wBAAAA,MAAI,oBAAqB;AAAA,QAC1B;AAAA,MACF;AAAA,IACH;AAGA,UAAM,kBAAkB,CAAC,YAAY;AACnCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAED,UAAI,KAAK,UAAU,GAAG;AACpB,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH;AAGA,UAAM,yBAAyB,CAAC,SAAS;AACvC,YAAM,QAAQ,oBAAI,KAAM;AACxB,YAAM,WAAW,MAAM,YAAW,IAAK,MACrC,OAAO,MAAM,aAAa,CAAC,EAAE,SAAS,GAAG,GAAG,IAAI,MAChD,OAAO,MAAM,QAAO,CAAE,EAAE,SAAS,GAAG,GAAG;AAEzC,oBAAc,QAAQ,KAAK,OAAO,aAAW;AAE3C,YAAI,OAAO,QAAQ,wBAAwB,UAAU;AACnD,gBAAM,YAAY,QAAQ,oBAAoB,MAAM,GAAG,EAAE,CAAC;AAC1D,iBAAO,cAAc;AAAA,QACtB,WAAU,OAAO,QAAQ,oBAAoB,UAAU;AACtD,gBAAM,YAAY,QAAQ,gBAAgB,MAAM,GAAG,EAAE,CAAC;AACtD,iBAAO,cAAc;AAAA,QACtB;AACD,eAAO;AAAA,MACR,CAAA,EAAE;AAAA,IACL;AAGA,UAAM,cAAc,MAAM;AACxB,sBAAgB,IAAI;AAAA,IACtB;AAGA,UAAM,WAAW,MAAM;AACrB,UAAI,WAAW,UAAU,YAAY,QAAQ;AAAO;AAEpD,iBAAW,QAAQ;AACnB,WAAK;AACL,sBAAiB;AAAA,IACnB;AAGA,UAAM,sBAAsB,CAAC,SAAS;AACpC,UAAI,QAAQ,KAAK,YAAY,CAAC,eAAe,OAAO;AAClD,oBAAY,QAAQ,KAAK;AACzB,sBAAc,QAAQ,KAAK,SAAS;AACpC,+BAAuB,KAAK,QAAQ;AACpC,gBAAQ,QAAQ,YAAY,MAAM,WAAW;AAG7C,YAAI,KAAK,MAAM;AACb,eAAK,QAAQ,KAAK;AAAA,QACnB;AACD,YAAI,KAAK,OAAO;AACd,gBAAM,QAAQ,KAAK;AAAA,QACpB;AAGD,mBAAW,QAAQ,KAAK,WAAW,SAAS;AAG5C,uBAAe,QAAQ;AAAA,MACxB;AAAA,IACH;AAGA,UAAM,kBAAkB,CAAC,QAAQ;AAC/B,UAAI,CAAC;AAAK;AAEVA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,2BAA2B,GAAG;AAAA,MACvC,CAAG;AAAA,IACH;AAGA,UAAM,kBAAkB,CAAC,YAAY;AACnC,UAAI,CAAC;AAAS,eAAO;AAGrB,UAAI;AACJ,UAAI,OAAO,YAAY,UAAU;AAC/B,oBAAY,IAAI,KAAK,OAAO,EAAE,QAAS;AAAA,MAC3C,WAAa,OAAO,YAAY,UAAU;AAEtC,oBAAY,UAAU,OAAgB,UAAU,UAAU;AAAA,MAC9D,OAAS;AACL,eAAO;AAAA,MACR;AAED,YAAM,MAAM,KAAK,IAAK;AACtB,YAAM,OAAO,MAAM;AAGnB,UAAI,OAAO,KAAK,KAAM;AACpB,eAAO;AAAA,MACR;AAGD,UAAI,OAAO,KAAK,KAAK,KAAM;AACzB,cAAM,UAAU,KAAK,MAAM,QAAQ,KAAK,IAAK;AAC7C,eAAO,GAAG,OAAO;AAAA,MAClB;AAGD,UAAI,OAAO,KAAK,KAAK,KAAK,KAAM;AAC9B,cAAM,QAAQ,KAAK,MAAM,QAAQ,KAAK,KAAK,IAAK;AAChD,eAAO,GAAG,KAAK;AAAA,MAChB;AAGD,UAAI,OAAO,IAAI,KAAK,KAAK,KAAK,KAAM;AAClC,cAAM,OAAO,KAAK,MAAM,QAAQ,KAAK,KAAK,KAAK,IAAK;AACpD,eAAO,GAAG,IAAI;AAAA,MACf;AAGD,YAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,YAAM,QAAQ,KAAK,SAAQ,IAAK;AAChC,YAAM,MAAM,KAAK,QAAS;AAC1B,aAAO,GAAG,KAAK,IAAI,GAAG;AAAA,IACxB;AAGAE,kBAAAA,OAAO,MAAM;AACX,eAAU;AAAA,IACZ,CAAC;AAEDC,kBAAAA,OAAO,MAAM;AAEXH,0BAAI,IAAI,qBAAqB,mBAAmB;AAGhD,UAAI,YAAY,MAAM,WAAW,KAAK,CAAC,QAAQ,OAAO;AACpD,wBAAgB,IAAI;AAAA,MACrB;AAAA,IACH,CAAC;AAEDI,kBAAAA,OAAO,MAAM;AAEXJ,0BAAI,KAAK,qBAAqB,mBAAmB;AAAA,IACnD,CAAC;AAEDK,kBAAAA,kBAAkB,MAAM;AACtB,kBAAa;AAAA,IACf,CAAC;AAEDC,kBAAAA,cAAc,MAAM;AAClB,eAAU;AAAA,IACZ,CAAC;AAGDC,kBAAAA,YAAY,MAAM;AAChBP,0BAAI,KAAK,qBAAqB,mBAAmB;AAAA,IACnD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzYD,GAAG,WAAW,eAAe;"}