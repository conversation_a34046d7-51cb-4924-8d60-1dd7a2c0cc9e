"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_social = require("../../api/social.js");
if (!Math) {
  common_vendor.unref(tabbar)();
}
const tabbar = () => "../../components/tabbar/tabbar.js";
const _sfc_main = /* @__PURE__ */ Object.assign({
  name: "TabbarIndex"
}, {
  __name: "index",
  setup(__props) {
    var _a, _b;
    const store = common_vendor.useStore();
    const statusBarHeight = common_vendor.ref(((_a = store == null ? void 0 : store.state) == null ? void 0 : _a.statusBarHeight) || 20);
    const titleBarHeight = common_vendor.ref(((_b = store == null ? void 0 : store.state) == null ? void 0 : _b.titleBarHeight) || 44);
    const menuButtonWidth = common_vendor.ref(0);
    const playTabs = common_vendor.ref(["推荐", "附近", "最新"]);
    const playTabIndex = common_vendor.ref(0);
    const currentMsg = common_vendor.ref(0);
    common_vendor.ref([]);
    const circle = common_vendor.ref([]);
    const getMenuButtonInfo = () => {
      try {
        if (common_vendor.index.canIUse("getMenuButtonBoundingClientRect")) {
          const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
          if (menuButtonInfo) {
            menuButtonWidth.value = menuButtonInfo.width + 16;
          }
        }
      } catch (e) {
        menuButtonWidth.value = 88;
      }
    };
    const onSearch = () => {
      common_vendor.index.navigateTo({
        url: "/pages/search/index"
      });
    };
    const onSignIn = () => {
      common_vendor.index.navigateTo({
        url: "/pages/users/user_sgin/index"
      });
    };
    const onReachEnd = () => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:297", "滚动到底部");
    };
    const getCurrentMsg = () => {
      if (store && store.state && store.state.app) {
        const userInfo = common_vendor.index.getStorageSync("USER_INFO") || {};
        currentMsg.value = userInfo.service_num || 0;
      }
    };
    const navigateToFun = (e) => {
      const url = e.currentTarget.dataset.url;
      if (url) {
        common_vendor.index.navigateTo({
          url: "/pages/" + url
        });
      }
    };
    const navigateToFeature = (type) => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:320", "Navigate to feature:", type);
      let url = "";
      switch (type) {
        case "match":
          url = "match/index";
          break;
        case "love":
          url = "note/manghe";
          break;
        case "voice":
          url = "voice/index";
          break;
        case "call":
          url = "call/index";
          break;
        case "group":
          url = "group/index";
          break;
        case "game":
          url = "game/index";
          break;
      }
      if (url) {
        common_vendor.index.navigateTo({
          url: "/pages/" + url
        });
      }
    };
    const getHotCircles = () => {
      api_social.getHotCircles().then((res) => {
        if (res.status === 200 && res.data) {
          circle.value = res.data.map((item) => {
            return {
              id: item.id,
              circle_name: item.circle_name || item.name,
              circle_avatar: item.circle_avatar || item.avatar,
              name: item.circle_name || item.name,
              // 兼容字段
              avatar: item.circle_avatar || item.avatar,
              // 兼容字段
              is_hot: item.is_hot || 0,
              is_official: item.is_official || 0,
              dynamic_count: item.dynamic_count || 0,
              member_count: item.member_count || 0,
              user_count: item.member_count || 0
              // 兼容字段
            };
          });
        }
      }).catch((err) => {
        common_vendor.index.__f__("log", "at pages/index/index.vue:376", "获取热门圈子失败", err);
      });
    };
    common_vendor.onMounted(() => {
      getMenuButtonInfo();
      getCurrentMsg();
      getHotCircles();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0,
        b: common_assets._imports_0$1,
        c: common_vendor.o(onSearch),
        d: common_assets._imports_2,
        e: common_vendor.o(onSignIn),
        f: menuButtonWidth.value + "px",
        g: titleBarHeight.value + "px",
        h: statusBarHeight.value + "px",
        i: common_vendor.o(($event) => navigateToFeature("match")),
        j: common_assets._imports_3,
        k: common_vendor.o(($event) => navigateToFeature("love")),
        l: common_assets._imports_3,
        m: common_vendor.o(($event) => navigateToFeature("voice")),
        n: common_assets._imports_3,
        o: common_vendor.o(($event) => navigateToFeature("call")),
        p: common_assets._imports_3,
        q: common_vendor.o(($event) => navigateToFeature("group")),
        r: common_assets._imports_3,
        s: common_vendor.o(($event) => navigateToFeature("game")),
        t: common_vendor.f(circle.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.circle_avatar || item.avatar,
            b: item.is_official == 1
          }, item.is_official == 1 ? {} : item.is_hot == 1 ? {} : {}, {
            c: item.is_hot == 1,
            d: common_vendor.t(item.circle_name || item.name),
            e: item.dynamic_count
          }, item.dynamic_count ? {
            f: common_vendor.t(item.dynamic_count)
          } : item.member_count || item.user_count ? {
            h: common_vendor.t(item.member_count || item.user_count)
          } : {}, {
            g: item.member_count || item.user_count,
            i: index,
            j: "note/circle?id=" + item.id,
            k: common_vendor.o(navigateToFun, index)
          });
        }),
        v: common_assets._imports_5,
        w: common_vendor.o(navigateToFun),
        x: common_vendor.f(playTabs.value, (tab, idx, i0) => {
          return common_vendor.e({
            a: common_vendor.t(tab),
            b: playTabIndex.value === idx
          }, playTabIndex.value === idx ? {} : {}, {
            c: idx,
            d: playTabIndex.value === idx ? 1 : "",
            e: common_vendor.o(($event) => playTabIndex.value = idx, idx)
          });
        }),
        y: playTabIndex.value === 0
      }, playTabIndex.value === 0 ? {
        z: common_assets._imports_3,
        A: common_assets._imports_6,
        B: common_assets._imports_3,
        C: common_assets._imports_3
      } : playTabIndex.value === 1 ? {
        E: common_assets._imports_3
      } : playTabIndex.value === 2 ? {
        G: common_assets._imports_3
      } : {}, {
        D: playTabIndex.value === 1,
        F: playTabIndex.value === 2,
        H: common_vendor.o(onReachEnd),
        I: statusBarHeight.value + titleBarHeight.value + "px",
        J: common_vendor.p({
          currentPage: 0,
          currentMsg: currentMsg.value
        })
      });
    };
  }
});
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
