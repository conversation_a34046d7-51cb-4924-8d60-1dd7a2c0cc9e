{"version": 3, "file": "logout.js", "sources": ["pages/setting/logout.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc2V0dGluZy9sb2dvdXQudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- <navbar :bg=\"1\"></navbar>\r\n    <view class=\"title-box\" :style=\"{'margin-top': statusBarHeight + titleBarHeight + 'px'}\">\r\n      <view>申请注销账号</view>\r\n      <view>过程不可逆，请谨慎操作</view>\r\n    </view> -->\r\n    \r\n    <!-- 使用原生滚动视图，性能更好 -->\r\n    <view class=\"content-wrapper\">\r\n      <!-- 加载状态显示 -->\r\n      <view class=\"loading-box\" v-if=\"isLoading\">\r\n        <view class=\"loading-icon\"></view>\r\n        <text class=\"loading-text\">加载中...</text>\r\n      </view>\r\n      \r\n      <!-- 用户信息区域 -->\r\n      <view class=\"user-info\" v-if=\"!isLoading && agreementData.avatar\">\r\n        <image class=\"user-avatar\" :src=\"agreementData.avatar\" mode=\"aspectFill\"></image>\r\n        <view class=\"user-name\">{{agreementData.name}}</view>\r\n      </view>\r\n      \r\n      <!-- 协议内容显示区域 - 使用简化的内容渲染方式 -->\r\n      <view class=\"agreement-content\" v-if=\"!isLoading\">\r\n        <rich-text :nodes=\"agreementContent\"></rich-text>\r\n      </view>\r\n      \r\n      <!-- 底部安全区域，防止内容被按钮遮挡 -->\r\n      <view class=\"bottom-safe-area\"></view>\r\n    </view>\r\n    \r\n    <view class=\"footer-box bfw bUp\">\r\n      <view class=\"notice\">点击【确认注销】即代表您已经同意《用户注销协议》</view>\r\n      <view class=\"footer-item\">\r\n        <view class=\"btn-box\" @tap=\"submitClick\">确认注销</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\r\n      <view class=\"tips-box df\">\r\n        <view class=\"tips-item\">{{tipsTitle}}</view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport navbar from '@/components/navbar/navbar.vue'\r\nimport uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'\r\nimport { getUserAgreement, cancelUser } from '@/api/user.js'\r\n\r\nconst app = getApp()\r\n\r\nexport default {\r\n  components: {\r\n    navbar,\r\n    uniPopup\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: app.globalData.statusBarHeight || 20,\r\n      titleBarHeight: app.globalData.titleBarHeight || 44,\r\n      tipsTitle: \"\",\r\n      isLoading: true,\r\n      agreementData: {\r\n        avatar: '',\r\n        name: '',\r\n        content: ''\r\n      },\r\n      agreementContent: '' // 使用简化的内容格式\r\n    }\r\n  },\r\n  onReady() {\r\n    // 页面渲染完成后立即加载数据\r\n    this.getAgreement()\r\n  },\r\n  methods: {\r\n    getAgreement() {\r\n      // 设置一个超时，如果请求时间过长，也显示界面\r\n      const timeout = setTimeout(() => {\r\n        if (this.isLoading) {\r\n          this.isLoading = false\r\n        }\r\n      }, 1500)\r\n      \r\n      // 检查是否有缓存的协议内容\r\n      const cachedAgreement = uni.getStorageSync('user_cancel_agreement')\r\n      if (cachedAgreement) {\r\n        try {\r\n          const agreementData = JSON.parse(cachedAgreement)\r\n          this.agreementData = agreementData\r\n          this.agreementContent = agreementData.content\r\n          this.isLoading = false\r\n          clearTimeout(timeout)\r\n          return\r\n        } catch (e) {\r\n          console.log('缓存协议解析失败', e)\r\n        }\r\n      }\r\n      \r\n      // 没有缓存或解析失败，从API获取\r\n      getUserAgreement(5).then(res => {\r\n        clearTimeout(timeout)\r\n        this.agreementData = res.data\r\n        this.agreementContent = res.data.content\r\n        // 缓存协议内容，避免重复加载\r\n        uni.setStorageSync('user_cancel_agreement', JSON.stringify(res.data))\r\n        this.isLoading = false\r\n      }).catch(err => {\r\n        clearTimeout(timeout)\r\n        this.isLoading = false\r\n        this.tipsTitle = \"获取协议失败\"\r\n        this.$refs.tipsPopup.open()\r\n        setTimeout(() => {\r\n          this.$refs.tipsPopup.close()\r\n        }, 1500)\r\n      })\r\n    },\r\n    submitClick() {\r\n      let self = this\r\n      \r\n      uni.showModal({\r\n        content: \"确定要注销账号吗？\",\r\n        confirmColor: \"#FA5150\",\r\n        success: function(res) {\r\n          if (res.confirm) {\r\n            uni.showLoading({\r\n              mask: true\r\n            })\r\n            \r\n            // 使用真实的注销API\r\n            cancelUser().then(function(res) {\r\n              uni.hideLoading()\r\n              \r\n              // 清除登录状态\r\n              app.globalData.spid = ''\r\n              app.globalData.pid = ''\r\n              self.$store.commit(\"LOGOUT\")\r\n              \r\n              // 显示成功提示\r\n              self.tipsTitle = \"账号注销申请已提交，将在7个工作日内处理\"\r\n              self.$refs.tipsPopup.open()\r\n              \r\n              // 清除本地存储\r\n              uni.clearStorage()\r\n              \r\n              setTimeout(function() {\r\n                self.$refs.tipsPopup.close()\r\n                \r\n                // 重定向到首页\r\n                uni.reLaunch({\r\n                  url: '/pages/index/index'\r\n                })\r\n              }, 1500)\r\n            }).catch(function(err) {\r\n              uni.hideLoading()\r\n              self.tipsTitle = err || \"注销失败，请稍后重试\"\r\n              self.$refs.tipsPopup.open()\r\n              \r\n              setTimeout(function() {\r\n                self.$refs.tipsPopup.close()\r\n              }, 1500)\r\n            })\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n}\r\n.title-box {\r\n  font-size: 40rpx;\r\n  font-weight: 700;\r\n}\r\n/* 内容区域 - 使用原生滚动，性能更好 */\r\n.content-wrapper {\r\n  flex: 1;\r\n  padding: 0 30rpx;\r\n  box-sizing: border-box;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch; /* 增强iOS滚动流畅度 */\r\n}\r\n/* 加载状态 */\r\n.loading-box {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40rpx 0;\r\n  min-height: 200rpx;\r\n}\r\n.loading-icon {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  margin-bottom: 20rpx;\r\n  border: 4rpx solid #f3f3f3;\r\n  border-top: 4rpx solid #666;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n.loading-text {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n}\r\n/* 用户信息样式 */\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20rpx 0;\r\n  margin-bottom: 20rpx;\r\n}\r\n.user-avatar {\r\n  width: 76rpx;\r\n  height: 76rpx;\r\n  margin-right: 20rpx;\r\n  border-radius: 50%;\r\n}\r\n.user-name {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n}\r\n/* 协议内容样式 */\r\n.agreement-content {\r\n  margin: 20rpx 0 40rpx;\r\n  font-size: 26rpx;\r\n  line-height: 1.6;\r\n  color: #333;\r\n}\r\n/* 底部安全区域 */\r\n.bottom-safe-area {\r\n  height: 180rpx;\r\n  width: 100%;\r\n}\r\n.footer-box {\r\n  position: fixed;\r\n  z-index: 99;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  width: 100%;\r\n  padding: 30rpx;\r\n  box-sizing: border-box;\r\n  padding-bottom: max(env(safe-area-inset-bottom), 30rpx);\r\n}\r\n.notice {\r\n  color: #999;\r\n  font-size: 24rpx;\r\n  text-align: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n.btn-box {\r\n  width: 100%;\r\n  height: 100rpx;\r\n  line-height: 100rpx;\r\n  text-align: center;\r\n  font-size: 24rpx;\r\n  color: #fff;\r\n  font-weight: 700;\r\n  background: #000;\r\n  border-radius: 100rpx;\r\n}\r\n.bfw {\r\n  background: #fff;\r\n}\r\n.bUp {\r\n  box-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n.df {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.tips-box {\r\n  padding: 20rpx 30rpx;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  border-radius: 12rpx;\r\n  justify-content: center;\r\n}\r\n.tips-item {\r\n  color: #fff;\r\n  font-size: 28rpx;\r\n  font-weight: 700;\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/uniapp/vue3/pages/setting/logout.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getUserAgreement", "cancelUser", "res"], "mappings": ";;;AA+CA,eAAe,MAAW;AAC1B,iBAAiB,MAAW;AAG5B,MAAM,MAAM,OAAO;AAEnB,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB,IAAI,WAAW,mBAAmB;AAAA,MACnD,gBAAgB,IAAI,WAAW,kBAAkB;AAAA,MACjD,WAAW;AAAA,MACX,WAAW;AAAA,MACX,eAAe;AAAA,QACb,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACD,kBAAkB;AAAA;AAAA,IACpB;AAAA,EACD;AAAA,EACD,UAAU;AAER,SAAK,aAAa;AAAA,EACnB;AAAA,EACD,SAAS;AAAA,IACP,eAAe;AAEb,YAAM,UAAU,WAAW,MAAM;AAC/B,YAAI,KAAK,WAAW;AAClB,eAAK,YAAY;AAAA,QACnB;AAAA,MACD,GAAE,IAAI;AAGP,YAAM,kBAAkBA,cAAAA,MAAI,eAAe,uBAAuB;AAClE,UAAI,iBAAiB;AACnB,YAAI;AACF,gBAAM,gBAAgB,KAAK,MAAM,eAAe;AAChD,eAAK,gBAAgB;AACrB,eAAK,mBAAmB,cAAc;AACtC,eAAK,YAAY;AACjB,uBAAa,OAAO;AACpB;AAAA,QACF,SAAS,GAAG;AACVA,wBAAAA,MAAA,MAAA,OAAA,kCAAY,YAAY,CAAC;AAAA,QAC3B;AAAA,MACF;AAGAC,eAAAA,iBAAiB,CAAC,EAAE,KAAK,SAAO;AAC9B,qBAAa,OAAO;AACpB,aAAK,gBAAgB,IAAI;AACzB,aAAK,mBAAmB,IAAI,KAAK;AAEjCD,sBAAG,MAAC,eAAe,yBAAyB,KAAK,UAAU,IAAI,IAAI,CAAC;AACpE,aAAK,YAAY;AAAA,MACnB,CAAC,EAAE,MAAM,SAAO;AACd,qBAAa,OAAO;AACpB,aAAK,YAAY;AACjB,aAAK,YAAY;AACjB,aAAK,MAAM,UAAU,KAAK;AAC1B,mBAAW,MAAM;AACf,eAAK,MAAM,UAAU,MAAM;AAAA,QAC5B,GAAE,IAAI;AAAA,OACR;AAAA,IACF;AAAA,IACD,cAAc;AACZ,UAAI,OAAO;AAEXA,oBAAAA,MAAI,UAAU;AAAA,QACZ,SAAS;AAAA,QACT,cAAc;AAAA,QACd,SAAS,SAAS,KAAK;AACrB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,YAAY;AAAA,cACd,MAAM;AAAA,aACP;AAGDE,gCAAY,EAAC,KAAK,SAASC,MAAK;AAC9BH,4BAAAA,MAAI,YAAY;AAGhB,kBAAI,WAAW,OAAO;AACtB,kBAAI,WAAW,MAAM;AACrB,mBAAK,OAAO,OAAO,QAAQ;AAG3B,mBAAK,YAAY;AACjB,mBAAK,MAAM,UAAU,KAAK;AAG1BA,4BAAAA,MAAI,aAAa;AAEjB,yBAAW,WAAW;AACpB,qBAAK,MAAM,UAAU,MAAM;AAG3BA,8BAAAA,MAAI,SAAS;AAAA,kBACX,KAAK;AAAA,iBACN;AAAA,cACF,GAAE,IAAI;AAAA,aACR,EAAE,MAAM,SAAS,KAAK;AACrBA,4BAAAA,MAAI,YAAY;AAChB,mBAAK,YAAY,OAAO;AACxB,mBAAK,MAAM,UAAU,KAAK;AAE1B,yBAAW,WAAW;AACpB,qBAAK,MAAM,UAAU,MAAM;AAAA,cAC5B,GAAE,IAAI;AAAA,aACR;AAAA,UACH;AAAA,QACF;AAAA,OACD;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvKA,GAAG,WAAW,eAAe;"}