<view class="container"><view class="nav-box df" style="{{'padding-top:' + f + ';' + ('background:' + g)}}"><view class="nav-back df" style="{{'height:' + b}}" bindtap="{{c}}"><image src="{{a}}" style="width:34rpx;height:34rpx"></image></view><view wx:if="{{d}}" class="nav-title ohto">{{e}}</view></view><view class="images-box df"><swiper class="images-swiper" circular autoplay bindchange="{{i}}" data-type="1"><swiper-item wx:for="{{h}}" wx:for-item="item" wx:key="c" data-idx="{{item.d}}" data-type="1" bindtap="{{item.e}}"><lazy-image wx:if="{{item.b}}" u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}"></lazy-image></swiper-item></swiper><view wx:if="{{j}}" class="indicator"><view wx:for="{{k}}" wx:for-item="v" wx:key="a" class="{{['indicator-item', v.b]}}" style="{{'width:' + l}}"></view></view></view><view class="info-box"><view class="tags"><view wx:for="{{m}}" wx:for-item="tag" wx:key="b" class="tag-item">{{tag.a}}</view></view><view class="title">{{n}}</view><view class="desc">{{o}}</view><view class="price"><money wx:if="{{p}}" u-i="122fecef-1" bind:__l="__l" u-p="{{p}}"></money><view wx:if="{{q}}" class="price-line" style="text-decoration:line-through"> ¥{{r}}</view><view class="price-line">{{s}}</view></view></view><view class="content"><view class="content-title">规格</view><scroll-view scroll-x="true"><view class="specs-scroll"><view wx:for="{{t}}" wx:for-item="item" wx:key="f" class="specs-item" style="{{'border-color:' + item.g}}" bindtap="{{item.h}}"><view class="fd df" data-idx="{{item.a}}" data-type="2" catchtap="{{item.b}}"><image src="{{v}}" style="width:22rpx;height:22rpx"></image></view><image class="img" src="{{item.c}}" mode="aspectFill"></image><view class="name"><view>{{item.d}}</view><view style="margin-top:10rpx">¥ {{item.e}}</view></view></view></view></scroll-view></view><view class="content"><view class="content-title">数量</view><view class="quantity-box df"><view class="quantity-btn" style="{{'color:' + w}}" bindtap="{{x}}" data-type="0">－</view><input bindblur="{{y}}" data-type="2" type="number" maxlength="4" value="{{z}}" bindinput="{{A}}"/><view class="quantity-btn" style="{{'color:' + B}}" bindtap="{{C}}" data-type="1">＋</view></view></view><view class="content"><view class="content-title">商品详情</view><view wx:if="{{D}}" class="evaluate df" bindtap="{{I}}"><view>评价（{{E}}）</view><view class="df"><view wx:if="{{F}}" class="cu-img-group"><view wx:for="{{G}}" wx:for-item="img" wx:key="b" class="cu-img"><image src="{{img.a}}" mode="aspectFill"></image></view></view><image class="effect" src="{{H}}"></image></view></view><rich-text nodes="{{J}}"></rich-text></view><view class="footer-box bUp df"><view class="footer-item df"><button class="icon-box df" open-type="contact" send-message-title="{{L}}" send-message-path="{{M}}" send-message-img="{{N}}" show-message-card="{{true}}"><image src="{{K}}"></image><text>客服</text></button><button class="icon-box df" bindtap="{{R}}"><image src="{{O}}"></image><text>购物车</text><view wx:if="{{P}}" class="badge">{{Q}}</view></button><view wx:if="{{S}}" class="btn df"><view bindtap="{{U}}" class="bg1 df" style="width:100%;justify-content:center"><image src="{{T}}" style="width:40rpx;height:40rpx"></image><text style="margin-left:10rpx">复制优惠链接</text></view></view><view wx:else class="btn df"><view bindtap="{{V}}">加入购物车</view><view bindtap="{{X}}" class="{{['bg1', Y]}}">{{W}}</view></view></view></view><uni-popup wx:if="{{ab}}" class="r" u-s="{{['d']}}" u-r="tipsPopup" u-i="122fecef-2" bind:__l="__l" u-p="{{ab}}"><view class="tips-box df"><view class="tips-item">{{Z}}</view></view></uni-popup></view>