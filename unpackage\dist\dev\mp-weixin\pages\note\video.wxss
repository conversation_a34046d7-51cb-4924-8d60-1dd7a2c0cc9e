
page{
  background:#000;
}
.nav-box{
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  box-sizing: border-box;
}
.nav-item .nav-back{
  padding: 0 30rpx;
  width: 34rpx;
  height: 100%;
  border-radius: 50%;
}
.video-box{
  width: 100%;
  transition: height .45s ease-in-out;
}
.video-box video{
  width: 100%;
  height: 100%;
}
.content-box{
  position: fixed;
  z-index: 99;
  left: 0;
  right: 0;
  width: calc(100% - 60rpx);
  margin: 20rpx 30rpx;
  color: #fff;
}
.content-box .nav-user{
  width: 100%;
  justify-content: space-between;
}
.content-box .user-info{
  display: flex;
  align-items: center;
}
.content-box .nav-user .nav-user-avatar{
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255,255,255,.8);
}
.content-box .nav-user .nav-user-name{
  margin-left: 20rpx;
  font-size: 26rpx;
  font-weight: 700;
  opacity: .8;
}
/* 关注按钮样式 */
.content-box .nav-user .follow-btn{
  padding: 0 20rpx;
  height: 48rpx;
  line-height: 48rpx;
  font-size: 20rpx;
  font-weight: 700;
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  text-align: center;
}
.content-box .nav-user .follow-btn.active{
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
}
.content-box .content-item{
  width: 100%;
  margin: 20rpx 0;
  word-break: break-word;
}
.content-box .content-item text{
  font-size: 28rpx;
  font-weight: 400;
}
.content-box .content-tag{
  margin-bottom: 10rpx;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.content-tag .tag-item{
  margin: 0 10rpx 10rpx 0;
  padding: 8rpx;
  height: 40rpx;
  border-radius: 8rpx;
  background: rgba(255, 255, 255, 0.1);
}
.content-tag .tag-item .icon{
  width: 40rpx;
  height: 40rpx;
  border-radius: 4rpx;
}
.content-tag .tag-item text{
  font-size: 20rpx;
  padding: 0 8rpx 0 12rpx;
}
.content-box .content-tips{
  margin-top: 8rpx;
  opacity: .6;
  font-size: 20rpx;
}
.comment-box{
  width: calc(100% - 60rpx);
  padding: 30rpx;
  background: #fff;
  border-radius: 30rpx 30rpx 0 0;
}
.comment-box .comment-top{
  width: 100%;
  height: 56rpx;
  justify-content: space-between;
}
.comment-top .top-title{
  font-size: 26rpx;
  font-weight: 700;
}
.comment-top .top-btn{
  width: 136rpx;
  padding: 6rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  position: relative;
}
.comment-top .top-btn .btn-item, .comment-top .top-btn .btn-active{
  width: 68rpx;
  height: 44rpx;
}
.comment-top .top-btn .btn-item{
  z-index: 2;
  line-height: 44rpx;
  text-align: center;
  font-size: 20rpx;
  font-weight: 500;
  transition: color .3s;
}
.comment-top .top-btn .btn-active{
  position: absolute;
  z-index: 1;
  background: #fff;
  border-radius: 4rpx;
  transition: left .3s;
}
.comment-top .top-close{
  width: 48rpx;
  height: 48rpx;
  background: #f5f5f5;
  border-radius: 50%;
  transform: rotate(45deg);
  justify-content: center;
}
.comment-scroll{
  width: 100%;
  height: calc(70vh - 196rpx);
}
.comment-box .comment-item{
  width: 100%;
  margin-top: 30rpx;
  justify-content: space-between;
  align-items: flex-start!important;
}
.comment-item .comment-avatar, .comment-item .comment-avatar-z{
  background: #f8f8f8;
  border: 1px solid #f5f5f5;
  border-radius: 50%;
  overflow: hidden;
}
.comment-item .comment-avatar{
  width: 64rpx;
  height: 64rpx;
}
.comment-item .comment-avatar-z{
  width: 44rpx;
  height: 44rpx;
}
.comment-item .comment-info{
  width: calc(100% - 88rpx);
}
.unfold{
  padding: 20rpx 68rpx;
  color: #999;
  font-size: 20rpx;
  font-weight: 700;
}
.comment-info .comment-info-top{
  font-size: 24rpx;
  color: #999;
}
.comment-info .comment-info-top-z view{
  max-width: 230rpx;
  font-size: 22rpx;
  color: #999;
}
.comment-info .comment-info-top-z text{
  margin-right: 8rpx;
  color: #333;
  font-size: 22rpx;
  font-weight: 500;
}
.comment-info .nn, .comment-info .zz, .comment-info .wo{
  margin-right: 8rpx;
}
.comment-info .zz{
  color: #FA5150!important;
  font-weight: 700;
}
.comment-info .wo{
  color: #000!important;
  font-weight: 700;
}
.comment-info .db{
  color: #ccc!important;
}
.comment-info .comment-info-content{
  word-break: break-word;
  white-space: pre-line;
}
.comment-info .comment-info-content text{
  color: #333;
  font-size: 26rpx;
  font-weight: 400;
}
.comment-info .comment-info-bottom{
  margin-top: 15rpx;
  color: #999;
  font-size: 20rpx;
}
.comment-info .comment-info-bottom view{
  margin-left: 30rpx;
  font-weight: 700;
}
.comment-box .no-more{
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  color: #999;
  font-size: 20rpx;
}
.comment-box .comment-btn{
  width: 100%;
  height: 80rpx;
  background: #f8f8f8;
  border-radius: 40rpx;
}
.comment-box .comment-btn image{
  margin: 0 20rpx 0 10rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}
.footer-box{
  position: fixed;
  z-index: 99;
  left: 0;
  right: 0;
  bottom: 0;
  width: calc(100% - 30rpx);
  padding: 20rpx 15rpx;
  background: #000;
  padding-bottom: max(env(safe-area-inset-bottom), 20rpx);
}
.footer-box .footer-item{
  width: 100%;
  height: 80rpx;
  justify-content: space-between;
}
.footer-means{
  margin-left: 15rpx;
  padding: 0 30rpx;
  height: 80rpx;
  font-size: 20rpx;
  font-weight: 700;
  color: #fff;
  background: #181818;
  border-radius: 40rpx;
}
.footer-means image{
  margin-left: 10rpx;
  width: 20rpx;
  height: 20rpx;
}
.footer-comment{
  margin-left: 20rpx;
  padding: 0 30rpx;
  width: 200rpx;
  height: 80rpx;
  background: #181818;
  border-radius: 40rpx;
}
.pl-str{
  line-height: 80rpx;
  color: #999;
  font-size: 24rpx;
  font-weight: 400;
  word-break: break-word;
  white-space: pre-line;
  display: block;
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}
.footer-item .footer-icon{
  padding: 16rpx 15rpx;
  display: flex;
}
.footer-item .footer-icon image{
  width: 48rpx;
  height: 48rpx;
}
.footer-item .footer-icon text{
  margin-left: 8rpx;
  color: #999;
  font-size: 18rpx;
  font-weight: 700;
}
.popup-comment-mask{
  position: fixed;
  z-index: 99998;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
}
.popup-comment{
  position: fixed;
  z-index: 99999;
  left: 0;
  width: calc(100% - 60rpx);
  padding: 30rpx;
  background: #fff;
  border-top: 1px solid #f8f8f8;
  display: flex;
  align-items: flex-end;
}
.popup-comment .send{
  margin: 0 0 15rpx 30rpx;
  width: 48rpx;
  height: 48rpx;
}
.popup-comment .comment-textarea{
  width: calc(100% - 98rpx);
  padding: 10rpx 20rpx;
  background: #f8f8f8;
  border-radius: 30rpx;
}
.popup-comment .comment-textarea textarea{
  width: 100%;
  line-height: 32rpx;
  min-height: 96rpx;
  max-height: 320rpx;
  font-size: 26rpx;
}
.popup-comment .comment-icon{
  width: calc(100% - 20rpx);
  padding: 30rpx 10rpx;
}
.empty-box{
  width: 100%;
  padding: 100rpx 0;
  flex-direction: column;
}
.empty-box image{
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 30rpx;
}
.empty-box .e1{
  font-size: 30rpx;
  font-weight: 700;
}
.empty-box .e2{
  margin-top: 10rpx;
  color: #999;
  font-size: 26rpx;
}
.tips-box{
  justify-content: center;
  width: 100%;
}
.tips-box .tips-item{
  background: #000;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 700;
}
.df{
  display: flex;
  align-items: center;
}
.ohto{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2{
  display: -webkit-box;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.xwb{
  filter: invert(1);
}

/* 单图样式 */
.image-box {
  width: 100%;
  background: #000;
  transition: height .45s ease-in-out;
  justify-content: center;
  align-items: center;
}
.image-box .full-image {
  width: 100%;
  height: 100%;
}

/* 多图样式 */
.multi-image-box {
  width: 100%;
  background: #000;
  transition: height .45s ease-in-out;
}
.multi-image-box .image-swiper {
  width: 100%;
  height: 100%;
}
.multi-image-box .swiper-image {
  width: 100%;
  height: 100%;
}
.multi-image-box .image-counter {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 4rpx 20rpx;
  border-radius: 30rpx;
  font-size: 20rpx;
}

/* 内容区域调整 */
.nav-counter{
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}

/* 音频播放器样式 */
.audio-player-container {
  position: relative;
  width: 100%;
  background: #000;
  transition: height .45s ease-in-out;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

/* 背景模糊层 */
.audio-bg-blur {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.audio-bg-blur image {
  width: 100%;
  height: 100%;
}
.audio-bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  -webkit-backdrop-filter: saturate(150%) blur(25px);
  backdrop-filter: saturate(150%) blur(25px);
  background: rgba(0, 0, 0, 0.5);
}

/* 音频播放内容 */
.audio-player-content {
  position: relative;
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  width: 100%;
  max-width: 700rpx;
}

/* 碟机外圈 */
.vinyl-outer {
  position: relative;
  width: 400rpx;
  height: 400rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
}

/* 外圈刻槽 */
.vinyl-groove {
  position: absolute;
  width: 350rpx;
  height: 350rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border-style: dashed;
}
.vinyl-groove-2 {
  position: absolute;
  width: 320rpx;
  height: 320rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  border-style: dotted;
}
.vinyl-groove-3 {
  position: absolute;
  width: 290rpx;
  height: 290rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

/* 内圈唱片 */
.vinyl-inner {
  position: relative;
  width: 260rpx;
  height: 260rpx;
  background: rgba(20, 20, 20, 0.8);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: inset 0 0 30rpx rgba(0, 0, 0, 0.5);
}
.vinyl-center {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 专辑封面 */
.album-cover {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
}
.album-cover image {
  width: 100%;
  height: 100%;
}

/* 中心圆点 */
.vinyl-dot {
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  z-index: 2;
}

/* 音频信息 */
.audio-info {
  text-align: center;
  margin-bottom: 40rpx;
  width: 100%;
}
.audio-title {
  font-size: 32rpx;
  color: #fff;
  font-weight: 600;
  margin-bottom: 10rpx;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.audio-artist {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

/* 播放控制 */
.audio-controls {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.audio-play-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}
.audio-play-btn:active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(0.95);
}
.audio-play-btn image {
  width: 50rpx;
  height: 50rpx;
}

/* 进度条容器 */
.audio-progress-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.audio-time-start,
.audio-time-end {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
  min-width: 80rpx;
}
.audio-progress {
  flex: 1;
  margin: 0 20rpx;
}

/* 旋转动画 */
.rotating {
  animation: rotate 10s linear infinite;
}
.rotating-slow {
  animation: rotate 30s linear infinite;
}
@keyframes rotate {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 20rpx;
}
.loading-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.comment-info-bottom .delete-btn{
  margin-left: 24rpx;
  font-size: 24rpx;
  color: #999;
}

/* 评论点赞按钮样式 */
.comment-info .user-info-left {
  display: flex;
  align-items: center;
}
.comment-info .like-icon {
  display: flex;
  align-items: center;
  margin-left: auto;
}
.comment-info .like-icon image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 4rpx;
}
.comment-info .like-icon text {
  font-size: 20rpx;
  color: #999;
}

/* 评论图片样式 */
.comment-image {
  margin-top: 10rpx;
  max-width: 300rpx;
  max-height: 400rpx;
  border-radius: 8rpx;
}
.reply-comment-image {
  max-width: 200rpx;
  max-height: 300rpx;
}

/* 已删除评论样式 */
.deleted-comment {
  color: #ccc;
  font-style: italic;
  font-size: 24rpx;
}

/* 评论富文本样式 */
.comment-rich-text {
  word-break: break-word;
  white-space: pre-wrap;
}
.reply-rich-text {
  font-size: 24rpx;
}

/* 系统消息样式 */
.system-message {
  color: #999;
  font-style: italic;
  font-size: 24rpx;
  background-color: #f8f8f8;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
}

/* 表情预览弹窗 */
.emoji-preview-popup {
  position: fixed;
  z-index: 999;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
}
.emoji-preview-image {
  width: 60%;
  height: auto;
  max-width: 400rpx;
  max-height: 400rpx;
}
.emoji-img-inline {
  display: inline-block;
  width: 32rpx !important;
  height: 32rpx !important;
  vertical-align: middle;
  margin: 0 4rpx;
}
