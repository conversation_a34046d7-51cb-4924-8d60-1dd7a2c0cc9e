{"version": 3, "file": "comment-input.js", "sources": ["components/comment-input/comment-input.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy9jb21tZW50LWlucHV0L2NvbW1lbnQtaW5wdXQudnVl"], "sourcesContent": ["<template>\n  <view class=\"comment-input-wrapper\" v-if=\"show\">\n    <!-- 输入框区域 -->\n    <view class=\"comment-input-box\">\n      <editor\n        id=\"commentEditor\"\n        class=\"comment-editor\"\n        :placeholder=\"placeholder\"\n        :read-only=\"disabled\"\n        @ready=\"onEditorReady\"\n        @focus=\"editorFocus\"\n        @blur=\"editorBlur\"\n        @input=\"onEditorInput\"\n        aria-label=\"评论输入框\"\n        role=\"textbox\"\n      ></editor>\n    </view>\n    \n    <!-- 预览已选图片 -->\n    <view class=\"selected-image-preview\" v-if=\"commentImage\">\n      <image class=\"selected-image\" :src=\"commentImage\" mode=\"aspectFill\" @tap=\"previewImage\"></image>\n      <view class=\"delete-image\" @tap=\"removeCommentImage\">×</view>\n    </view>\n    \n    <!-- 工具栏 -->\n    <view class=\"comment-tools-bar\">\n      <view class=\"tools-left\">\n        <view class=\"tool-item\" @tap=\"showImagePicker\">\n          <image src=\"/static/img/add/photo.png\"></image>\n        </view>\n        <view class=\"tool-item\" @tap=\"showAtUsers\">\n          <image src=\"/static/img/add/publish_at.png\"></image>\n        </view>\n        <view class=\"tool-item\" @tap=\"toggleEmoji\">\n          <image src=\"/static/img/add/emoji.png\"></image>\n        </view>\n      </view>\n      <view \n        :class=\"sendBtnClass\" \n        @tap.stop=\"onSendButtonClick\" \n        @click.stop=\"onSendButtonClick\"\n        @touchend.stop=\"onSendButtonClick\"\n      >\n        {{ isSubmittingComment ? '发送中...' : '发送' }}\n      </view>\n    </view>\n    \n    <!-- 表情面板组件 -->\n    <emoji-panel \n      :show=\"showEmoji\" \n      :content=\"commentText\"\n      @select=\"selectEmoji\"\n      @select-gif=\"selectGif\"\n      @delete=\"deleteEmoji\"\n    ></emoji-panel>\n    \n    <!-- @用户选择弹窗 -->\n    <uni-popup ref=\"atUserPopup\" type=\"bottom\">\n      <view class=\"at-user-popup\">\n        <view class=\"at-user-header\">\n          <text>选择要@的用户</text>\n          <view class=\"close-btn\" @tap=\"closeAtUsers\">关闭</view>\n        </view>\n        <scroll-view scroll-y class=\"at-user-list\">\n          <view \n            class=\"at-user-item\" \n            v-for=\"(user, index) in atUserList\" \n            :key=\"index\"\n            @tap=\"selectAtUser(user)\"\n          >\n            <image class=\"at-user-avatar\" :src=\"user.avatar\"></image>\n            <text class=\"at-user-name\">{{user.nickname}}</text>\n          </view>\n        </scroll-view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, watch, onMounted, onBeforeUnmount, nextTick, getCurrentInstance } from 'vue'\nimport EmojiPanel from '@/components/emoji-panel/emoji-panel.vue'\nimport uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'\n// 导入表情列表\nimport emojiList from '@/components/emoji-panel/sina.js'\n\n// 定义组件名称\ndefineOptions({\n  name: 'CommentInput'\n})\n\n// 定义Props\nconst props = defineProps({\n  show: {\n    type: Boolean,\n    default: false\n  },\n  placeholder: {\n    type: String,\n    default: '写评论...'\n  },\n  focus: {\n    type: Boolean,\n    default: false\n  },\n  maxLength: {\n    type: Number,\n    default: 500,\n    validator: (value) => value > 0 && value <= 2000\n  },\n  disabled: {\n    type: Boolean,\n    default: false\n  }\n})\n\n// 定义Emits\nconst emit = defineEmits(['send', 'focus', 'blur', 'editorError'])\n\n// 获取当前实例\nconst instance = getCurrentInstance()\n\n// 响应式数据\nconst editorCtx = ref(null)\nconst commentText = ref('')\nconst commentImage = ref(null)\nconst isFocus = ref(false)\nconst showEmoji = ref(false)\nconst atUserList = ref([])\nconst isSubmittingComment = ref(false)\nconst keepPanelOpen = ref(false)\nconst hasContent = ref(false)\nconst lastInputChar = ref('')\nconst isToggleEmoji = ref(false)\nconst isToggleAtUser = ref(false)\nconst isAnyPopupOpen = ref(false)\nconst isEditorReady = ref(false)\nconst savedContent = ref(null)\n\n// 表情映射\nconst emojiMap = reactive({})\nconst phraseMap = reactive({})\nconst reverseMap = reactive({})\n\n// 定时器\nconst inputDebounceTimer = ref(null)\nconst retryTimer = ref(null)\nconst retryCount = ref(0)\nconst observer = ref(null)\n\n// 引用\nconst atUserPopup = ref(null)\n\n// 计算属性\nconst sendBtnClass = computed(() => {\n  const isActive = hasContent.value && !isSubmittingComment.value\n  console.log('发送按钮状态计算:', {\n    hasContent: hasContent.value,\n    isSubmittingComment: isSubmittingComment.value,\n    isActive: isActive\n  })\n\n  return {\n    'send-btn': true,\n    'active': isActive\n  }\n})\n\n// 监听器\nwatch(() => props.focus, (val) => {\n  isFocus.value = val\n}, { immediate: true })\n\nwatch(() => props.show, (val) => {\n  if (!val) {\n    showEmoji.value = false\n    isFocus.value = false\n  }\n}, { immediate: true })\n\n// 初始化表情映射\nconst initEmojiMap = () => {\n  if (!Array.isArray(emojiList) || emojiList.length === 0) {\n    console.warn('表情列表为空或格式错误')\n    return\n  }\n\n  // 清空现有映射\n  Object.keys(emojiMap).forEach(key => delete emojiMap[key])\n  Object.keys(phraseMap).forEach(key => delete phraseMap[key])\n  Object.keys(reverseMap).forEach(key => delete reverseMap[key])\n\n  // 批量处理表情数据\n  emojiList.forEach(emoji => {\n    const key = emoji.phrase || emoji.alt || emoji.value\n    const url = emoji.url || emoji.icon\n\n    if (!key || !url) return\n\n    try {\n      // 编码映射\n      const encodedKey = encodeURIComponent(key)\n      emojiMap[encodedKey] = url\n\n      // 短代码映射\n      const bracketKey = key.startsWith('[') ? key : `[${key}]`\n      phraseMap[bracketKey] = url\n\n      // 反向映射\n      createReverseMapping(url, bracketKey)\n    } catch (err) {\n      console.warn('处理表情映射出错:', key, err)\n    }\n  })\n}\n\n// 创建反向映射\nconst createReverseMapping = (url, bracketKey) => {\n  // 完整URL映射\n  reverseMap[url] = bracketKey\n\n  // 文件名映射\n  const urlParts = url.split('/')\n  const fileName = urlParts[urlParts.length - 1]\n  if (fileName) {\n    reverseMap[fileName] = bracketKey\n    reverseMap['/' + fileName] = bracketKey\n  }\n\n  // 路径段映射\n  if (urlParts.length >= 2) {\n    reverseMap[urlParts.slice(-2).join('/')] = bracketKey\n  }\n  if (urlParts.length >= 3) {\n    reverseMap[urlParts.slice(-3).join('/')] = bracketKey\n  }\n}\n\n// 编辑器初始化完成\nconst onEditorReady = () => {\n  console.log('编辑器开始初始化')\n\n  // #ifdef MP-BAIDU\n  editorCtx.value = requireDynamicLib('editorLib').createEditorContext('commentEditor')\n  isEditorReady.value = true\n  console.log('百度小程序编辑器初始化完成')\n  // #endif\n\n  // #ifndef MP-BAIDU\n  uni.createSelectorQuery()\n    .in(instance)\n    .select('#commentEditor')\n    .context((res) => {\n      console.log('编辑器上下文查询结果:', res)\n      if (res && res.context) {\n        editorCtx.value = res.context\n        isEditorReady.value = true\n        console.log('编辑器初始化完成')\n\n        // 编辑器准备好后，如果有保存的内容则恢复\n        if (savedContent.value) {\n          restoreContent()\n        }\n      } else {\n        console.error('编辑器上下文获取失败')\n        // 最多重试3次\n        if (!retryCount.value) retryCount.value = 0\n        if (retryCount.value < 3) {\n          retryCount.value++\n          retryTimer.value = setTimeout(() => {\n            onEditorReady()\n          }, 500 * retryCount.value)\n        } else {\n          console.error('编辑器初始化失败，已达到最大重试次数')\n          emit('editorError', '编辑器初始化失败')\n        }\n      }\n    })\n    .exec()\n  // #endif\n}\n\n// 获取当前选区\nconst getSelection = () => {\n  return new Promise((resolve) => {\n    if (!editorCtx.value || !isEditorReady.value) {\n      resolve(null)\n      return\n    }\n\n    editorCtx.value.getSelection({\n      success: res => resolve(res),\n      fail: () => resolve(null)\n    })\n  })\n}\n\n// 获取指定范围的内容\nconst getContents = (start, end) => {\n  return new Promise((resolve) => {\n    if (!editorCtx.value || !isEditorReady.value) {\n      console.error('编辑器未就绪:', {\n        editorCtx: !!editorCtx.value,\n        isEditorReady: isEditorReady.value\n      })\n      resolve(null)\n      return\n    }\n\n    const options = {\n      success: res => {\n        console.log('getContents成功:', res)\n        resolve(res)\n      },\n      fail: (err) => {\n        console.error('getContents失败:', err)\n        resolve(null)\n      }\n    }\n\n    if (typeof start !== 'undefined' && typeof end !== 'undefined') {\n      options.start = start\n      options.end = end\n    }\n\n    try {\n      editorCtx.value.getContents(options)\n    } catch (err) {\n      console.error('调用getContents异常:', err)\n      resolve(null)\n    }\n  })\n}\n\n// 编辑器内容变化\nconst onEditorInput = async (e) => {\n  if (!editorCtx.value || !isEditorReady.value) {\n    return\n  }\n\n  // 使用防抖避免频繁调用\n  if (inputDebounceTimer.value) {\n    clearTimeout(inputDebounceTimer.value)\n  }\n\n  inputDebounceTimer.value = setTimeout(async () => {\n    try {\n      // 获取当前内容\n      const contents = await getContents()\n      if (!contents) return\n\n      // 更新文本内容\n      commentText.value = contents.text || ''\n\n      // 检查是否有表情图片\n      const hasEmoji = contents.html && contents.html.includes('emoji-img')\n\n      // 更新内容状态 - 使用与发送时相同的逻辑\n      const cleanText = commentText.value ? commentText.value.replace(/\\n/g, '').trim() : ''\n      const hasTextContent = cleanText.length > 0\n      const hasImageContent = !!commentImage.value\n      hasContent.value = hasTextContent || hasImageContent || hasEmoji\n\n      console.log('内容状态更新:', {\n        originalText: commentText.value,\n        cleanText: cleanText,\n        hasTextContent,\n        hasImageContent,\n        hasEmoji,\n        hasContent: hasContent.value\n      })\n\n      // 检测@符号\n      const lastCharValue = commentText.value.slice(-1)\n      if (lastCharValue === '@' && lastInputChar.value !== '@') {\n        showAtUsers()\n      }\n      lastInputChar.value = lastCharValue\n\n      // 处理删除操作\n      if (e && e.detail && (e.detail.keyCode === 8 || e.detail.keyCode === 46)) {\n        await handleDelete()\n      }\n    } catch (err) {\n      console.error('处理编辑器内容变化出错:', err)\n    }\n  }, 150)\n}\n\n// 处理删除操作\nconst handleDelete = async () => {\n  if (!editorCtx.value || !isEditorReady.value) return\n\n  try {\n    // 获取当前选区\n    const selection = await getSelection()\n    if (!selection) return\n\n    const { start, end } = selection\n\n    // 检查是否删除的是图片\n    const contents = await getContents(Math.max(0, start.offset - 1), end.offset + 1)\n    if (!contents || !contents.nodes) return\n\n    const hasEmoji = contents.nodes.some(node =>\n      node.name === 'img' && node.attrs && node.attrs.class && node.attrs.class.includes('emoji-img')\n    )\n\n    if (hasEmoji) {\n      // 直接删除内容,不插入零宽度空格\n      editorCtx.value.deleteContents({\n        success: () => {\n          // 触发内容更新\n          onEditorInput()\n        }\n      })\n    }\n  } catch (err) {\n    console.error('处理删除操作出错:', err)\n  }\n}\n\n// 编辑器获取焦点\nconst editorFocus = () => {\n  if (!keepPanelOpen.value && !isToggleEmoji.value && !isToggleAtUser.value) {\n    showEmoji.value = false\n    if (atUserPopup.value) {\n      atUserPopup.value.close()\n    }\n  }\n  keepPanelOpen.value = false\n  isFocus.value = true\n\n  emit('focus')\n}\n\n// 编辑器失去焦点\nconst editorBlur = () => {\n  isFocus.value = false\n\n  // 如果有弹窗打开或正在提交,不触发blur事件\n  if (!isAnyPopupOpen.value && !keepPanelOpen.value && !isSubmittingComment.value) {\n    // 保存内容\n    if (editorCtx.value && isEditorReady.value) {\n      editorCtx.value.getContents({\n        success: res => {\n          savedContent.value = res\n        }\n      })\n    }\n    emit('blur')\n  }\n}\n\n// 显示表情面板\nconst toggleEmoji = () => {\n  keepPanelOpen.value = true\n  isToggleEmoji.value = true\n\n  if (isToggleAtUser.value) {\n    atUserPopup.value.close()\n    isToggleAtUser.value = false\n  }\n\n  setTimeout(() => {\n    showEmoji.value = !showEmoji.value\n    isAnyPopupOpen.value = showEmoji.value\n\n    // 如果关闭表情面板\n    if (!showEmoji.value) {\n      isToggleEmoji.value = false\n      keepPanelOpen.value = false\n    }\n  }, 100)\n}\n\n// 选择表情\nconst selectEmoji = (emoji) => {\n  if (!editorCtx.value || !isEditorReady.value) {\n    uni.showToast({\n      title: '编辑器未就绪',\n      icon: 'none'\n    })\n    return\n  }\n\n  keepPanelOpen.value = true\n  isToggleEmoji.value = true\n\n  const url = emoji.url || emoji.icon\n  if (!url) return\n\n  // 调试输出\n  console.log('插入表情:', {\n    url: url,\n    phrase: emoji.phrase || emoji.alt || emoji.value,\n    mapping: reverseMap[url]\n  })\n\n  // 确保表情映射存在\n  if (!reverseMap[url]) {\n    const key = emoji.phrase || emoji.alt || emoji.value || ''\n    if (key) {\n      const bracketKey = key.startsWith('[') ? key : `[${key}]`\n      reverseMap[url] = bracketKey\n\n      // 同时保存文件名映射\n      const fileName = url.split('/').pop()\n      reverseMap[fileName] = bracketKey\n      reverseMap['/' + fileName] = bracketKey\n    }\n  }\n\n  try {\n    // 插入表情图片\n    editorCtx.value.insertImage({\n      src: url,\n      width: '18px',\n      height: '18px',\n      extClass: 'emoji-img',\n      success: () => {\n        onEditorInput()\n        setTimeout(() => {\n          isToggleEmoji.value = false\n        }, 100)\n      },\n      fail: (err) => {\n        console.error('插入表情失败:', err)\n        uni.showToast({\n          title: '插入表情失败',\n          icon: 'none'\n        })\n      }\n    })\n  } catch (err) {\n    console.error('插入表情出错:', err)\n    uni.showToast({\n      title: '插入表情失败',\n      icon: 'none'\n    })\n  }\n}\n\n// 选择GIF表情\nconst selectGif = (gif) => {\n  keepPanelOpen.value = true\n\n  console.log('选择了GIF表情', gif)\n\n  uni.showToast({\n    title: 'GIF表情功能开发中',\n    icon: 'none'\n  })\n}\n\n// 删除表情/文字\nconst deleteEmoji = () => {\n  if (editorCtx.value) {\n    editorCtx.value.undo()\n  }\n}\n\n// 选择@用户\nconst selectAtUser = (user) => {\n  const atText = `@${user.nickname} `\n\n  if (editorCtx.value && isEditorReady.value) {\n    // 保存当前内容\n    saveContent()\n\n    editorCtx.value.insertText({\n      text: atText,\n      success: () => {\n        onEditorInput()\n        closeAtUsers()\n\n        // 延迟聚焦,等待popup完全关闭\n        setTimeout(() => {\n          if (editorCtx.value && isEditorReady.value) {\n            editorCtx.value.focus()\n          }\n        }, 500)\n      },\n      fail: (err) => {\n        console.error('插入@用户失败:', err)\n        // 恢复之前的内容\n        restoreContent()\n      }\n    })\n  }\n}\n\n// 将HTML内容转换为纯文本，同时处理表情图片\nconst convertHtmlToText = async (html) => {\n  try {\n    if (!html) return ''\n\n    console.log('转换前的HTML:', html)\n\n    // 先检查是否包含表情图片\n    const hasEmojiImg = html.includes('emoji-img')\n\n    // 如果没有表情图片，直接处理纯文本\n    if (!hasEmojiImg) {\n      // 移除HTML标签\n      let text = html.replace(/<[^>]+>/g, '')\n\n      // 解码HTML实体\n      text = text.replace(/&nbsp;/g, ' ')\n        .replace(/&lt;/g, '<')\n        .replace(/&gt;/g, '>')\n        .replace(/&amp;/g, '&')\n        .replace(/&quot;/g, '\"')\n        .replace(/&#39;/g, \"'\")\n\n      console.log('纯文本转换结果:', text)\n      return text.trim()\n    }\n\n    // 处理包含表情的内容\n    const emojis = []\n    const imgRegex = /<img[^>]*class=\"[^\"]*emoji-img[^\"]*\"[^>]*>/g\n    const srcRegex = /src=[\"']([^\"']+)[\"']/\n\n    let text = html.replace(imgRegex, (imgTag) => {\n      console.log('匹配到的表情标签:', imgTag)\n\n      const srcMatch = imgTag.match(srcRegex)\n      if (!srcMatch) {\n        return '[表情]'\n      }\n\n      const src = srcMatch[1]\n\n      // 尝试匹配表情文本\n      let emojiText = reverseMap[src]\n\n      if (!emojiText) {\n        // 从emojiList中查找\n        const emoji = emojiList.find(e => {\n          const emojiUrl = e.url || e.icon || ''\n          return src === emojiUrl || src.endsWith(emojiUrl.split('/').pop())\n        })\n\n        if (emoji) {\n          emojiText = emoji.phrase || emoji.alt || emoji.value\n        } else {\n          emojiText = '[表情]'\n        }\n      }\n\n      emojis.push(emojiText)\n      return emojiText\n    })\n\n    // 移除其他HTML标签\n    text = text.replace(/<[^>]+>/g, '')\n\n    // 解码HTML实体\n    text = text.replace(/&nbsp;/g, ' ')\n      .replace(/&lt;/g, '<')\n      .replace(/&gt;/g, '>')\n      .replace(/&amp;/g, '&')\n      .replace(/&quot;/g, '\"')\n      .replace(/&#39;/g, \"'\")\n\n    console.log('包含表情的转换结果:', text)\n    return text.trim() || emojis.join('')\n\n  } catch (err) {\n    console.error('转换HTML出错:', err)\n    // 如果转换失败，至少返回去除HTML标签的文本\n    const fallbackText = html.replace(/<[^>]+>/g, '').trim()\n    return fallbackText || '[表情]'\n  }\n}\n\n// 处理发送按钮点击\nconst onSendButtonClick = (e) => {\n  console.log('=== 发送按钮点击事件触发 ===')\n  console.log('点击事件详情:', e)\n  console.log('事件类型:', e.type)\n  console.log('当前组件状态:', {\n    hasContent: hasContent.value,\n    isSubmittingComment: isSubmittingComment.value,\n    disabled: props.disabled,\n    editorReady: isEditorReady.value,\n    showEmoji: showEmoji.value,\n    isFocus: isFocus.value\n  })\n\n  // 阻止事件冒泡\n  if (e && e.stopPropagation) {\n    e.stopPropagation()\n  }\n  if (e && e.preventDefault) {\n    e.preventDefault()\n  }\n\n  // 检查按钮是否可点击\n  if (!hasContent.value) {\n    console.log('按钮不可点击：没有内容')\n    return\n  }\n\n  if (isSubmittingComment.value) {\n    console.log('按钮不可点击：正在提交中')\n    return\n  }\n\n  // 调用实际的发送方法\n  console.log('准备调用发送方法...')\n  handleSendComment()\n}\n\n// 发送评论\nconst handleSendComment = async () => {\n  console.log('=== 发送按钮被点击 ===')\n  console.log('=== 开始发送评论 ===')\n  console.log('编辑器状态:', {\n    editorCtx: !!editorCtx.value,\n    isEditorReady: isEditorReady.value,\n    isSubmittingComment: isSubmittingComment.value\n  })\n\n  if (!editorCtx.value || !isEditorReady.value) {\n    console.error('编辑器未就绪')\n    uni.showToast({\n      title: '编辑器未就绪，请稍候',\n      icon: 'none'\n    })\n    return\n  }\n\n  if (isSubmittingComment.value) {\n    console.log('正在提交中，忽略重复点击')\n    return\n  }\n\n  try {\n    console.log('开始获取编辑器内容...')\n    const contents = await getContents()\n    console.log('发送时获取到的编辑器内容:', contents)\n\n    if (!contents) {\n      console.error('未获取到编辑器内容')\n      uni.showToast({\n        title: '获取内容失败，请重试',\n        icon: 'none'\n      })\n      return\n    }\n\n    const { html, text } = contents\n\n    // 检查是否有表情图片\n    const hasEmoji = html && html.includes('emoji-img')\n\n    // 验证是否有内容 - 注意处理换行符\n    const cleanText = text ? text.replace(/\\n/g, '').trim() : ''\n    const hasTextContent = cleanText.length > 0\n    const hasImageContent = !!commentImage.value\n\n    console.log('内容检查详情:', {\n      originalText: text,\n      cleanText: cleanText,\n      hasTextContent,\n      hasImageContent,\n      hasEmoji,\n      textLength: cleanText.length,\n      commentImage: commentImage.value\n    })\n\n    if (!hasTextContent && !hasImageContent && !hasEmoji) {\n      console.log('没有任何内容，提示用户')\n      uni.showToast({\n        title: '请输入评论内容',\n        icon: 'none'\n      })\n      return\n    }\n\n    console.log('开始处理评论内容...')\n    isSubmittingComment.value = true\n\n    let finalContent = ''\n\n    if (hasTextContent) {\n      // 有文字内容，进行转换\n      if (hasEmoji) {\n        // 有表情，需要转换\n        console.log('处理包含表情的文字内容')\n        finalContent = await convertHtmlToText(html || '')\n      } else {\n        // 纯文字，直接使用text并清理换行符\n        console.log('处理纯文字内容')\n        finalContent = cleanText\n      }\n    } else if (hasEmoji) {\n      // 只有表情\n      console.log('处理纯表情内容')\n      finalContent = await convertHtmlToText(html || '')\n    }\n\n    // 最终内容检查\n    if (!finalContent.trim() && !hasImageContent) {\n      finalContent = '[表情]' // 备用内容\n    }\n\n    console.log('=== 准备发送评论 ===')\n    console.log('最终发送内容:', {\n      content: finalContent,\n      image: commentImage.value || '',\n      contentLength: finalContent.length\n    })\n\n    // 发送评论事件\n    emit('send', {\n      content: finalContent.trim(),\n      image: commentImage.value || ''\n    })\n\n    console.log('评论事件已发送')\n\n    // 显示成功提示\n    uni.showToast({\n      title: '发送成功',\n      icon: 'success',\n      duration: 1500\n    })\n\n    // 延迟清理，确保事件发送完成\n    setTimeout(() => {\n      // 重置编辑器内容\n      if (editorCtx.value) {\n        editorCtx.value.clear()\n      }\n      commentImage.value = null\n      showEmoji.value = false\n      hasContent.value = false\n      savedContent.value = null\n      console.log('评论框状态已重置')\n    }, 500)\n\n  } catch (err) {\n    console.error('=== 发送评论失败 ===')\n    console.error('错误详情:', err)\n    uni.showToast({\n      title: '发送失败，请重试',\n      icon: 'none'\n    })\n  } finally {\n    setTimeout(() => {\n      isSubmittingComment.value = false\n      console.log('提交状态已重置')\n    }, 800)\n  }\n}\n\n// 清空内容\nconst clear = () => {\n  if (editorCtx.value) {\n    editorCtx.value.clear()\n  }\n  commentImage.value = null\n  hasContent.value = false\n  savedContent.value = null\n}\n\n// 设置评论内容\nconst setContent = (text) => {\n  if (editorCtx.value && text) {\n    editorCtx.value.setContents({\n      html: text,\n      success: () => {\n        onEditorInput()\n      }\n    })\n  }\n}\n\n// 显示图片选择器\nconst showImagePicker = () => {\n  keepPanelOpen.value = true\n\n  // 关闭表情面板\n  showEmoji.value = false\n\n  // 使用全局上传工具方法\n  if (instance && instance.appContext.config.globalProperties.$util && instance.appContext.config.globalProperties.$util.uploadImageChange) {\n    instance.appContext.config.globalProperties.$util.uploadImageChange('upload/image',\n      // 上传成功\n      (res) => {\n        uni.hideLoading()\n        if (res.data && res.data.url) {\n          commentImage.value = res.data.url\n          hasContent.value = true\n          uni.showToast({\n            title: '图片已添加',\n            icon: 'success'\n          })\n        } else {\n          console.error('图片上传返回数据异常:', res)\n          uni.showToast({\n            title: '图片添加失败',\n            icon: 'none'\n          })\n        }\n      },\n      // 上传失败或取消\n      (err) => {\n        uni.hideLoading()\n        console.log('图片上传取消或失败:', err)\n      },\n      // 处理图片尺寸\n      (res) => {\n        if (res && res.w && res.h) {\n          console.log('图片尺寸:', res.w, 'x', res.h)\n        }\n      }\n    )\n  } else {\n    // 使用原始的chooseImage方法\n    uni.chooseImage({\n      count: 1,\n      sizeType: ['original', 'compressed'],\n      sourceType: ['album', 'camera'],\n      success: (res) => {\n        const tempFilePath = res.tempFilePaths[0]\n\n        // 检查文件大小\n        uni.getFileInfo({\n          filePath: tempFilePath,\n          success: (fileInfo) => {\n            const fileSizeMB = fileInfo.size / (1024 * 1024)\n            if (fileSizeMB > 5) {\n              uni.showToast({\n                title: '图片大小不能超过5MB',\n                icon: 'none'\n              })\n              return\n            }\n\n            // 显示上传进度\n            uni.showLoading({\n              title: '上传中...',\n              mask: true\n            })\n\n            // 上传图片\n            uni.uploadFile({\n              url: (instance && instance.appContext.config.globalProperties.$api && instance.appContext.config.globalProperties.$api.uploadUrl) || '/api/upload/image',\n              filePath: tempFilePath,\n              name: 'file',\n              success: (uploadRes) => {\n                uni.hideLoading()\n                try {\n                  const data = JSON.parse(uploadRes.data)\n                  if (data.code === 200 && data.data && data.data.url) {\n                    commentImage.value = data.data.url\n                    hasContent.value = true\n                    uni.showToast({\n                      title: '图片已添加',\n                      icon: 'success'\n                    })\n                  } else {\n                    throw new Error('上传返回数据异常')\n                  }\n                } catch (e) {\n                  console.error('图片上传解析失败:', e)\n                  uni.showToast({\n                    title: '图片添加失败',\n                    icon: 'none'\n                  })\n                }\n              },\n              fail: (err) => {\n                uni.hideLoading()\n                console.error('图片上传失败:', err)\n                uni.showToast({\n                  title: '图片上传失败',\n                  icon: 'none'\n                })\n              }\n            })\n          },\n          fail: (err) => {\n            console.error('获取文件信息失败:', err)\n            uni.showToast({\n              title: '无法获取图片信息',\n              icon: 'none'\n            })\n          }\n        })\n      }\n    })\n  }\n}\n\n// 移除评论图片\nconst removeCommentImage = () => {\n  commentImage.value = null\n}\n\n// 关闭@用户选择弹窗\nconst closeAtUsers = () => {\n  isToggleAtUser.value = false\n  isAnyPopupOpen.value = showEmoji.value\n  atUserPopup.value.close()\n\n  // 恢复保存的内容\n  restoreContent()\n}\n\n// 显示@用户选择弹窗\nconst showAtUsers = () => {\n  keepPanelOpen.value = true\n  isToggleAtUser.value = true\n\n  // 保存当前内容\n  saveContent()\n\n  // 关闭表情面板\n  if (showEmoji.value) {\n    showEmoji.value = false\n    isToggleEmoji.value = false\n  }\n\n  // 打开@用户选择弹窗\n  atUserPopup.value.open()\n  isAnyPopupOpen.value = true\n}\n\n// 预览图片\nconst previewImage = () => {\n  if (commentImage.value) {\n    uni.previewImage({\n      urls: [commentImage.value],\n      current: commentImage.value\n    })\n  }\n}\n\n// 初始化DOM观察器\nconst initDOMObserver = () => {\n  // #ifdef H5\n  if (typeof MutationObserver !== 'undefined') {\n    try {\n      observer.value = new MutationObserver((mutations) => {\n        mutations.forEach((mutation) => {\n          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {\n            handleDOMChange(mutation.addedNodes)\n          }\n        })\n      })\n\n      // 查找编辑器元素\n      const editorEl = document.querySelector('#commentEditor')\n      if (editorEl) {\n        const config = {\n          childList: true,\n          subtree: true,\n          attributes: false, // 不监听属性变化，减少性能开销\n          characterData: false // 不监听文本内容变化\n        }\n\n        observer.value.observe(editorEl, config)\n        console.log('DOM观察器初始化成功')\n      }\n    } catch (err) {\n      console.warn('DOM观察器初始化失败:', err)\n    }\n  }\n  // #endif\n}\n\n// 处理DOM变化\nconst handleDOMChange = (nodes) => {\n  // #ifdef H5\n  Array.from(nodes).forEach(node => {\n    if (node.nodeType === 1) { // 元素节点\n      // 处理新插入的元素\n      processNewElement(node)\n    }\n  })\n  // #endif\n}\n\n// 处理新插入的元素\nconst processNewElement = (element) => {\n  // #ifdef H5\n  // 处理图片元素\n  if (element.tagName === 'IMG') {\n    handleImageElement(element)\n  }\n\n  // 递归处理子元素\n  if (element.children && element.children.length > 0) {\n    Array.from(element.children).forEach(child => {\n      processNewElement(child)\n    })\n  }\n  // #endif\n}\n\n// 处理图片元素\nconst handleImageElement = (img) => {\n  // #ifdef H5\n  // 在H5环境中可以进行DOM操作，但在小程序中通过CSS类来设置样式\n  if (img.classList && img.classList.contains('emoji-img')) {\n    // 确保表情图片样式正确\n    img.style.verticalAlign = 'middle'\n    img.style.margin = '0 1px'\n    img.style.display = 'inline-block'\n  }\n  // #endif\n}\n\n// 保存编辑器内容\nconst saveContent = () => {\n  if (!editorCtx.value || !isEditorReady.value) return\n\n  editorCtx.value.getContents({\n    success: res => {\n      savedContent.value = res\n    },\n    fail: err => {\n      console.error('保存内容失败:', err)\n    }\n  })\n}\n\n// 恢复编辑器内容\nconst restoreContent = () => {\n  if (!editorCtx.value || !isEditorReady.value || !savedContent.value) return\n\n  editorCtx.value.setContents({\n    html: savedContent.value.html,\n    success: () => {\n      onEditorInput()\n    },\n    fail: err => {\n      console.error('恢复内容失败:', err)\n    }\n  })\n}\n\n// 添加安全的focus方法\nconst setEditorFocus = () => {\n  if (!editorCtx.value || !isEditorReady.value) return\n\n  try {\n    // #ifdef H5\n    // H5环境下直接使用DOM focus\n    const editor = document.querySelector('#commentEditor')\n    if (editor) {\n      editor.focus()\n      return\n    }\n    // #endif\n\n    // 其他环境尝试使用editorCtx.focus\n    if (typeof editorCtx.value.focus === 'function') {\n      editorCtx.value.focus()\n    }\n  } catch (err) {\n    console.error('设置编辑器焦点失败:', err)\n  }\n}\n\n// 重置组件状态\nconst resetState = () => {\n  commentText.value = ''\n  commentImage.value = null\n  showEmoji.value = false\n  isToggleEmoji.value = false\n  isToggleAtUser.value = false\n  isAnyPopupOpen.value = false\n  keepPanelOpen.value = false\n  savedContent.value = null\n  lastInputChar.value = ''\n  retryCount.value = 0\n\n  // 清理定时器\n  if (inputDebounceTimer.value) {\n    clearTimeout(inputDebounceTimer.value)\n    inputDebounceTimer.value = null\n  }\n  if (retryTimer.value) {\n    clearTimeout(retryTimer.value)\n    retryTimer.value = null\n  }\n}\n\n// 生命周期钩子\nonMounted(() => {\n  console.log('comment-input组件已挂载')\n\n  // 初始化表情映射\n  initEmojiMap()\n\n  // #ifdef H5\n  // H5环境使用现代的MutationObserver监听DOM变化\n  // 替代已弃用的DOMNodeInserted事件\n  nextTick(() => {\n    initDOMObserver()\n  })\n  // #endif\n})\n\nonBeforeUnmount(() => {\n  // 清理定时器\n  if (inputDebounceTimer.value) {\n    clearTimeout(inputDebounceTimer.value)\n    inputDebounceTimer.value = null\n  }\n  if (retryTimer.value) {\n    clearTimeout(retryTimer.value)\n    retryTimer.value = null\n  }\n\n  // 清理MutationObserver\n  if (observer.value) {\n    observer.value.disconnect()\n    observer.value = null\n  }\n\n  // 清理编辑器上下文\n  editorCtx.value = null\n})\n\n// 暴露给模板的方法\ndefineExpose({\n  clear,\n  setContent,\n  resetState,\n  setEditorFocus\n})\n</script>\n\n<style lang=\"scss\">\n.comment-input-wrapper {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #fff;\n  z-index: 999;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n}\n\n.comment-input-box {\n  padding: 20rpx;\n  background: #f5f5f5;\n  position: relative;\n}\n\n/* 编辑器样式 */\n.comment-editor {\n  width: 100%;\n  min-height: 60rpx; /* 减小最小高度 */\n  max-height: 240rpx; /* 减小最大高度 */\n  font-size: 28rpx;\n  line-height: 36rpx;\n  padding: 10rpx 20rpx;\n  box-sizing: border-box;\n  background-color: #fff;\n  border-radius: 8rpx;\n}\n\n/* 为编辑器中的表情图片添加样式 */\n.emoji-img {\n  vertical-align: middle;\n  margin: 0 1px;\n  display: inline-block;\n  transform: translateY(-1px);\n  line-height: 1;\n  width: 18px !important;\n  height: 18px !important;\n}\n\n.comment-tools-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10rpx 20rpx;\n  border-top: 1rpx solid #f1f1f1;\n}\n\n.tools-left {\n  display: flex;\n  align-items: center;\n}\n\n.tool-item {\n  width: 64rpx;\n  height: 64rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 10rpx;\n}\n\n.tool-item image {\n  width: 44rpx;\n  height: 44rpx;\n}\n\n.send-btn {\n  width: 120rpx;\n  height: 64rpx;\n  line-height: 64rpx;\n  text-align: center;\n  background-color: #f5f5f5;\n  color: #ccc;\n  font-size: 28rpx;\n  border-radius: 32rpx;\n  cursor: pointer;\n  position: relative;\n  z-index: 10;\n}\n\n.send-btn.active {\n  background-color: #ff4d6a;\n  color: #fff;\n  cursor: pointer;\n}\n\n.selected-image-preview {\n  padding: 20rpx;\n  background: #f5f5f5;\n  position: relative;\n}\n\n.selected-image {\n  width: 160rpx;\n  height: 160rpx;\n  border-radius: 8rpx;\n  object-fit: cover;\n}\n\n.delete-image {\n  position: absolute;\n  top: 10rpx;\n  right: 10rpx;\n  width: 40rpx;\n  height: 40rpx;\n  line-height: 36rpx;\n  text-align: center;\n  background: rgba(0, 0, 0, 0.5);\n  color: #fff;\n  border-radius: 50%;\n  font-size: 32rpx;\n}\n\n.at-user-popup {\n  background-color: #fff;\n  border-radius: 20rpx 20rpx 0 0;\n  padding-bottom: env(safe-area-inset-bottom);\n}\n\n.at-user-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx;\n  border-bottom: 1rpx solid #f1f1f1;\n}\n\n.close-btn {\n  color: #999;\n  font-size: 28rpx;\n}\n\n.at-user-list {\n  max-height: 600rpx;\n}\n\n.at-user-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  border-bottom: 1rpx solid #f5f5f5;\n}\n\n.at-user-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  margin-right: 20rpx;\n}\n\n.at-user-name {\n  font-size: 28rpx;\n}\n</style>", "import Component from 'D:/uniapp/vue3/components/comment-input/comment-input.vue'\nwx.createComponent(Component)"], "names": ["getCurrentInstance", "ref", "reactive", "computed", "uni", "watch", "emojiList", "text", "onMounted", "onBeforeUnmount", "Component"], "mappings": ";;;;;;;AAiFA,MAAM,aAAa,MAAW;AAC9B,MAAM,WAAW,MAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5B,UAAM,QAAQ;AAyBd,UAAM,OAAO;AAGb,UAAM,WAAWA,cAAAA,mBAAoB;AAGrC,UAAM,YAAYC,cAAG,IAAC,IAAI;AAC1B,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,eAAeA,cAAG,IAAC,IAAI;AAC7B,UAAM,UAAUA,cAAG,IAAC,KAAK;AACzB,UAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,UAAM,aAAaA,cAAG,IAAC,EAAE;AACzB,UAAM,sBAAsBA,cAAG,IAAC,KAAK;AACrC,UAAM,gBAAgBA,cAAG,IAAC,KAAK;AAC/B,UAAM,aAAaA,cAAG,IAAC,KAAK;AAC5B,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAC5B,UAAM,gBAAgBA,cAAG,IAAC,KAAK;AAC/B,UAAM,iBAAiBA,cAAG,IAAC,KAAK;AAChC,UAAM,iBAAiBA,cAAG,IAAC,KAAK;AAChC,UAAM,gBAAgBA,cAAG,IAAC,KAAK;AAC/B,UAAM,eAAeA,cAAG,IAAC,IAAI;AAG7B,UAAM,WAAWC,cAAQ,SAAC,EAAE;AAC5B,UAAM,YAAYA,cAAQ,SAAC,EAAE;AAC7B,UAAM,aAAaA,cAAQ,SAAC,EAAE;AAG9B,UAAM,qBAAqBD,cAAG,IAAC,IAAI;AACnC,UAAM,aAAaA,cAAG,IAAC,IAAI;AAC3B,UAAM,aAAaA,cAAG,IAAC,CAAC;AACxB,UAAM,WAAWA,cAAG,IAAC,IAAI;AAGzB,UAAM,cAAcA,cAAG,IAAC,IAAI;AAG5B,UAAM,eAAeE,cAAQ,SAAC,MAAM;AAClC,YAAM,WAAW,WAAW,SAAS,CAAC,oBAAoB;AAC1DC,oBAAAA,MAAA,MAAA,OAAA,qDAAY,aAAa;AAAA,QACvB,YAAY,WAAW;AAAA,QACvB,qBAAqB,oBAAoB;AAAA,QACzC;AAAA,MACJ,CAAG;AAED,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,UAAU;AAAA,MACX;AAAA,IACH,CAAC;AAGDC,kBAAK,MAAC,MAAM,MAAM,OAAO,CAAC,QAAQ;AAChC,cAAQ,QAAQ;AAAA,IAClB,GAAG,EAAE,WAAW,MAAM;AAEtBA,kBAAK,MAAC,MAAM,MAAM,MAAM,CAAC,QAAQ;AAC/B,UAAI,CAAC,KAAK;AACR,kBAAU,QAAQ;AAClB,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH,GAAG,EAAE,WAAW,MAAM;AAGtB,UAAM,eAAe,MAAM;AACzB,UAAI,CAAC,MAAM,QAAQC,2BAAAA,SAAS,KAAKA,2BAAS,UAAC,WAAW,GAAG;AACvDF,sBAAAA,MAAa,MAAA,QAAA,qDAAA,aAAa;AAC1B;AAAA,MACD;AAGD,aAAO,KAAK,QAAQ,EAAE,QAAQ,SAAO,OAAO,SAAS,GAAG,CAAC;AACzD,aAAO,KAAK,SAAS,EAAE,QAAQ,SAAO,OAAO,UAAU,GAAG,CAAC;AAC3D,aAAO,KAAK,UAAU,EAAE,QAAQ,SAAO,OAAO,WAAW,GAAG,CAAC;AAG7DE,iCAAS,UAAC,QAAQ,WAAS;AACzB,cAAM,MAAM,MAAM,UAAU,MAAM,OAAO,MAAM;AAC/C,cAAM,MAAM,MAAM,OAAO,MAAM;AAE/B,YAAI,CAAC,OAAO,CAAC;AAAK;AAElB,YAAI;AAEF,gBAAM,aAAa,mBAAmB,GAAG;AACzC,mBAAS,UAAU,IAAI;AAGvB,gBAAM,aAAa,IAAI,WAAW,GAAG,IAAI,MAAM,IAAI,GAAG;AACtD,oBAAU,UAAU,IAAI;AAGxB,+BAAqB,KAAK,UAAU;AAAA,QACrC,SAAQ,KAAK;AACZF,wBAAa,MAAA,MAAA,QAAA,qDAAA,aAAa,KAAK,GAAG;AAAA,QACnC;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,uBAAuB,CAAC,KAAK,eAAe;AAEhD,iBAAW,GAAG,IAAI;AAGlB,YAAM,WAAW,IAAI,MAAM,GAAG;AAC9B,YAAM,WAAW,SAAS,SAAS,SAAS,CAAC;AAC7C,UAAI,UAAU;AACZ,mBAAW,QAAQ,IAAI;AACvB,mBAAW,MAAM,QAAQ,IAAI;AAAA,MAC9B;AAGD,UAAI,SAAS,UAAU,GAAG;AACxB,mBAAW,SAAS,MAAM,EAAE,EAAE,KAAK,GAAG,CAAC,IAAI;AAAA,MAC5C;AACD,UAAI,SAAS,UAAU,GAAG;AACxB,mBAAW,SAAS,MAAM,EAAE,EAAE,KAAK,GAAG,CAAC,IAAI;AAAA,MAC5C;AAAA,IACH;AAGA,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAA,MAAA,OAAA,qDAAY,UAAU;AAStBA,oBAAAA,MAAI,oBAAqB,EACtB,GAAG,QAAQ,EACX,OAAO,gBAAgB,EACvB,QAAQ,CAAC,QAAQ;AAChBA,sBAAAA,wEAAY,eAAe,GAAG;AAC9B,YAAI,OAAO,IAAI,SAAS;AACtB,oBAAU,QAAQ,IAAI;AACtB,wBAAc,QAAQ;AACtBA,wBAAAA,MAAY,MAAA,OAAA,qDAAA,UAAU;AAGtB,cAAI,aAAa,OAAO;AACtB,2BAAgB;AAAA,UACjB;AAAA,QACT,OAAa;AACLA,wBAAAA,0EAAc,YAAY;AAE1B,cAAI,CAAC,WAAW;AAAO,uBAAW,QAAQ;AAC1C,cAAI,WAAW,QAAQ,GAAG;AACxB,uBAAW;AACX,uBAAW,QAAQ,WAAW,MAAM;AAClC,4BAAe;AAAA,YAC3B,GAAa,MAAM,WAAW,KAAK;AAAA,UACnC,OAAe;AACLA,0BAAAA,MAAA,MAAA,SAAA,qDAAc,oBAAoB;AAClC,iBAAK,eAAe,UAAU;AAAA,UAC/B;AAAA,QACF;AAAA,MACP,CAAK,EACA,KAAM;AAAA,IAEX;AAGA,UAAM,eAAe,MAAM;AACzB,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,YAAI,CAAC,UAAU,SAAS,CAAC,cAAc,OAAO;AAC5C,kBAAQ,IAAI;AACZ;AAAA,QACD;AAED,kBAAU,MAAM,aAAa;AAAA,UAC3B,SAAS,SAAO,QAAQ,GAAG;AAAA,UAC3B,MAAM,MAAM,QAAQ,IAAI;AAAA,QAC9B,CAAK;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,cAAc,CAAC,OAAO,QAAQ;AAClC,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,YAAI,CAAC,UAAU,SAAS,CAAC,cAAc,OAAO;AAC5CA,wBAAAA,0EAAc,WAAW;AAAA,YACvB,WAAW,CAAC,CAAC,UAAU;AAAA,YACvB,eAAe,cAAc;AAAA,UACrC,CAAO;AACD,kBAAQ,IAAI;AACZ;AAAA,QACD;AAED,cAAM,UAAU;AAAA,UACd,SAAS,SAAO;AACdA,0BAAAA,MAAA,MAAA,OAAA,qDAAY,kBAAkB,GAAG;AACjC,oBAAQ,GAAG;AAAA,UACZ;AAAA,UACD,MAAM,CAAC,QAAQ;AACbA,0BAAAA,MAAA,MAAA,SAAA,qDAAc,kBAAkB,GAAG;AACnC,oBAAQ,IAAI;AAAA,UACb;AAAA,QACF;AAED,YAAI,OAAO,UAAU,eAAe,OAAO,QAAQ,aAAa;AAC9D,kBAAQ,QAAQ;AAChB,kBAAQ,MAAM;AAAA,QACf;AAED,YAAI;AACF,oBAAU,MAAM,YAAY,OAAO;AAAA,QACpC,SAAQ,KAAK;AACZA,wBAAAA,MAAA,MAAA,SAAA,qDAAc,oBAAoB,GAAG;AACrC,kBAAQ,IAAI;AAAA,QACb;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,gBAAgB,OAAO,MAAM;AACjC,UAAI,CAAC,UAAU,SAAS,CAAC,cAAc,OAAO;AAC5C;AAAA,MACD;AAGD,UAAI,mBAAmB,OAAO;AAC5B,qBAAa,mBAAmB,KAAK;AAAA,MACtC;AAED,yBAAmB,QAAQ,WAAW,YAAY;AAChD,YAAI;AAEF,gBAAM,WAAW,MAAM,YAAa;AACpC,cAAI,CAAC;AAAU;AAGf,sBAAY,QAAQ,SAAS,QAAQ;AAGrC,gBAAM,WAAW,SAAS,QAAQ,SAAS,KAAK,SAAS,WAAW;AAGpE,gBAAM,YAAY,YAAY,QAAQ,YAAY,MAAM,QAAQ,OAAO,EAAE,EAAE,KAAM,IAAG;AACpF,gBAAM,iBAAiB,UAAU,SAAS;AAC1C,gBAAM,kBAAkB,CAAC,CAAC,aAAa;AACvC,qBAAW,QAAQ,kBAAkB,mBAAmB;AAExDA,wBAAAA,wEAAY,WAAW;AAAA,YACrB,cAAc,YAAY;AAAA,YAC1B;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,YAAY,WAAW;AAAA,UAC/B,CAAO;AAGD,gBAAM,gBAAgB,YAAY,MAAM,MAAM,EAAE;AAChD,cAAI,kBAAkB,OAAO,cAAc,UAAU,KAAK;AACxD,wBAAa;AAAA,UACd;AACD,wBAAc,QAAQ;AAGtB,cAAI,KAAK,EAAE,WAAW,EAAE,OAAO,YAAY,KAAK,EAAE,OAAO,YAAY,KAAK;AACxE,kBAAM,aAAc;AAAA,UACrB;AAAA,QACF,SAAQ,KAAK;AACZA,wBAAAA,0EAAc,gBAAgB,GAAG;AAAA,QAClC;AAAA,MACF,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,eAAe,YAAY;AAC/B,UAAI,CAAC,UAAU,SAAS,CAAC,cAAc;AAAO;AAE9C,UAAI;AAEF,cAAM,YAAY,MAAM,aAAc;AACtC,YAAI,CAAC;AAAW;AAEhB,cAAM,EAAE,OAAO,IAAG,IAAK;AAGvB,cAAM,WAAW,MAAM,YAAY,KAAK,IAAI,GAAG,MAAM,SAAS,CAAC,GAAG,IAAI,SAAS,CAAC;AAChF,YAAI,CAAC,YAAY,CAAC,SAAS;AAAO;AAElC,cAAM,WAAW,SAAS,MAAM;AAAA,UAAK,UACnC,KAAK,SAAS,SAAS,KAAK,SAAS,KAAK,MAAM,SAAS,KAAK,MAAM,MAAM,SAAS,WAAW;AAAA,QAC/F;AAED,YAAI,UAAU;AAEZ,oBAAU,MAAM,eAAe;AAAA,YAC7B,SAAS,MAAM;AAEb,4BAAe;AAAA,YAChB;AAAA,UACT,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,KAAK;AACZA,sBAAAA,MAAA,MAAA,SAAA,qDAAc,aAAa,GAAG;AAAA,MAC/B;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,cAAc,SAAS,CAAC,cAAc,SAAS,CAAC,eAAe,OAAO;AACzE,kBAAU,QAAQ;AAClB,YAAI,YAAY,OAAO;AACrB,sBAAY,MAAM,MAAO;AAAA,QAC1B;AAAA,MACF;AACD,oBAAc,QAAQ;AACtB,cAAQ,QAAQ;AAEhB,WAAK,OAAO;AAAA,IACd;AAGA,UAAM,aAAa,MAAM;AACvB,cAAQ,QAAQ;AAGhB,UAAI,CAAC,eAAe,SAAS,CAAC,cAAc,SAAS,CAAC,oBAAoB,OAAO;AAE/E,YAAI,UAAU,SAAS,cAAc,OAAO;AAC1C,oBAAU,MAAM,YAAY;AAAA,YAC1B,SAAS,SAAO;AACd,2BAAa,QAAQ;AAAA,YACtB;AAAA,UACT,CAAO;AAAA,QACF;AACD,aAAK,MAAM;AAAA,MACZ;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AACxB,oBAAc,QAAQ;AACtB,oBAAc,QAAQ;AAEtB,UAAI,eAAe,OAAO;AACxB,oBAAY,MAAM,MAAO;AACzB,uBAAe,QAAQ;AAAA,MACxB;AAED,iBAAW,MAAM;AACf,kBAAU,QAAQ,CAAC,UAAU;AAC7B,uBAAe,QAAQ,UAAU;AAGjC,YAAI,CAAC,UAAU,OAAO;AACpB,wBAAc,QAAQ;AACtB,wBAAc,QAAQ;AAAA,QACvB;AAAA,MACF,GAAE,GAAG;AAAA,IACR;AAGA,UAAM,cAAc,CAAC,UAAU;AAC7B,UAAI,CAAC,UAAU,SAAS,CAAC,cAAc,OAAO;AAC5CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,oBAAc,QAAQ;AACtB,oBAAc,QAAQ;AAEtB,YAAM,MAAM,MAAM,OAAO,MAAM;AAC/B,UAAI,CAAC;AAAK;AAGVA,oBAAAA,MAAY,MAAA,OAAA,qDAAA,SAAS;AAAA,QACnB;AAAA,QACA,QAAQ,MAAM,UAAU,MAAM,OAAO,MAAM;AAAA,QAC3C,SAAS,WAAW,GAAG;AAAA,MAC3B,CAAG;AAGD,UAAI,CAAC,WAAW,GAAG,GAAG;AACpB,cAAM,MAAM,MAAM,UAAU,MAAM,OAAO,MAAM,SAAS;AACxD,YAAI,KAAK;AACP,gBAAM,aAAa,IAAI,WAAW,GAAG,IAAI,MAAM,IAAI,GAAG;AACtD,qBAAW,GAAG,IAAI;AAGlB,gBAAM,WAAW,IAAI,MAAM,GAAG,EAAE,IAAK;AACrC,qBAAW,QAAQ,IAAI;AACvB,qBAAW,MAAM,QAAQ,IAAI;AAAA,QAC9B;AAAA,MACF;AAED,UAAI;AAEF,kBAAU,MAAM,YAAY;AAAA,UAC1B,KAAK;AAAA,UACL,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,SAAS,MAAM;AACb,0BAAe;AACf,uBAAW,MAAM;AACf,4BAAc,QAAQ;AAAA,YACvB,GAAE,GAAG;AAAA,UACP;AAAA,UACD,MAAM,CAAC,QAAQ;AACbA,0BAAAA,MAAc,MAAA,SAAA,qDAAA,WAAW,GAAG;AAC5BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACF,SAAQ,KAAK;AACZA,sBAAAA,0EAAc,WAAW,GAAG;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,YAAY,CAAC,QAAQ;AACzB,oBAAc,QAAQ;AAEtBA,oBAAAA,MAAY,MAAA,OAAA,qDAAA,YAAY,GAAG;AAE3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AACxB,UAAI,UAAU,OAAO;AACnB,kBAAU,MAAM,KAAM;AAAA,MACvB;AAAA,IACH;AAGA,UAAM,eAAe,CAAC,SAAS;AAC7B,YAAM,SAAS,IAAI,KAAK,QAAQ;AAEhC,UAAI,UAAU,SAAS,cAAc,OAAO;AAE1C,oBAAa;AAEb,kBAAU,MAAM,WAAW;AAAA,UACzB,MAAM;AAAA,UACN,SAAS,MAAM;AACb,0BAAe;AACf,yBAAc;AAGd,uBAAW,MAAM;AACf,kBAAI,UAAU,SAAS,cAAc,OAAO;AAC1C,0BAAU,MAAM,MAAO;AAAA,cACxB;AAAA,YACF,GAAE,GAAG;AAAA,UACP;AAAA,UACD,MAAM,CAAC,QAAQ;AACbA,0BAAAA,MAAc,MAAA,SAAA,qDAAA,YAAY,GAAG;AAE7B,2BAAgB;AAAA,UACjB;AAAA,QACP,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,oBAAoB,OAAO,SAAS;AACxC,UAAI;AACF,YAAI,CAAC;AAAM,iBAAO;AAElBA,sBAAAA,MAAA,MAAA,OAAA,qDAAY,aAAa,IAAI;AAG7B,cAAM,cAAc,KAAK,SAAS,WAAW;AAG7C,YAAI,CAAC,aAAa;AAEhB,cAAIG,QAAO,KAAK,QAAQ,YAAY,EAAE;AAGtC,UAAAA,QAAOA,MAAK,QAAQ,WAAW,GAAG,EAC/B,QAAQ,SAAS,GAAG,EACpB,QAAQ,SAAS,GAAG,EACpB,QAAQ,UAAU,GAAG,EACrB,QAAQ,WAAW,GAAG,EACtB,QAAQ,UAAU,GAAG;AAExBH,wBAAAA,MAAY,MAAA,OAAA,qDAAA,YAAYG,KAAI;AAC5B,iBAAOA,MAAK,KAAM;AAAA,QACnB;AAGD,cAAM,SAAS,CAAE;AACjB,cAAM,WAAW;AACjB,cAAM,WAAW;AAEjB,YAAI,OAAO,KAAK,QAAQ,UAAU,CAAC,WAAW;AAC5CH,wBAAAA,wEAAY,aAAa,MAAM;AAE/B,gBAAM,WAAW,OAAO,MAAM,QAAQ;AACtC,cAAI,CAAC,UAAU;AACb,mBAAO;AAAA,UACR;AAED,gBAAM,MAAM,SAAS,CAAC;AAGtB,cAAI,YAAY,WAAW,GAAG;AAE9B,cAAI,CAAC,WAAW;AAEd,kBAAM,QAAQE,2BAAAA,UAAU,KAAK,OAAK;AAChC,oBAAM,WAAW,EAAE,OAAO,EAAE,QAAQ;AACpC,qBAAO,QAAQ,YAAY,IAAI,SAAS,SAAS,MAAM,GAAG,EAAE,KAAK;AAAA,YAC3E,CAAS;AAED,gBAAI,OAAO;AACT,0BAAY,MAAM,UAAU,MAAM,OAAO,MAAM;AAAA,YACzD,OAAe;AACL,0BAAY;AAAA,YACb;AAAA,UACF;AAED,iBAAO,KAAK,SAAS;AACrB,iBAAO;AAAA,QACb,CAAK;AAGD,eAAO,KAAK,QAAQ,YAAY,EAAE;AAGlC,eAAO,KAAK,QAAQ,WAAW,GAAG,EAC/B,QAAQ,SAAS,GAAG,EACpB,QAAQ,SAAS,GAAG,EACpB,QAAQ,UAAU,GAAG,EACrB,QAAQ,WAAW,GAAG,EACtB,QAAQ,UAAU,GAAG;AAExBF,sBAAAA,MAAA,MAAA,OAAA,qDAAY,cAAc,IAAI;AAC9B,eAAO,KAAK,KAAI,KAAM,OAAO,KAAK,EAAE;AAAA,MAErC,SAAQ,KAAK;AACZA,sBAAAA,MAAA,MAAA,SAAA,qDAAc,aAAa,GAAG;AAE9B,cAAM,eAAe,KAAK,QAAQ,YAAY,EAAE,EAAE,KAAM;AACxD,eAAO,gBAAgB;AAAA,MACxB;AAAA,IACH;AAGA,UAAM,oBAAoB,CAAC,MAAM;AAC/BA,oBAAAA,wEAAY,oBAAoB;AAChCA,oBAAAA,MAAA,MAAA,OAAA,qDAAY,WAAW,CAAC;AACxBA,oBAAY,MAAA,MAAA,OAAA,qDAAA,SAAS,EAAE,IAAI;AAC3BA,oBAAAA,MAAA,MAAA,OAAA,qDAAY,WAAW;AAAA,QACrB,YAAY,WAAW;AAAA,QACvB,qBAAqB,oBAAoB;AAAA,QACzC,UAAU,MAAM;AAAA,QAChB,aAAa,cAAc;AAAA,QAC3B,WAAW,UAAU;AAAA,QACrB,SAAS,QAAQ;AAAA,MACrB,CAAG;AAGD,UAAI,KAAK,EAAE,iBAAiB;AAC1B,UAAE,gBAAiB;AAAA,MACpB;AACD,UAAI,KAAK,EAAE,gBAAgB;AACzB,UAAE,eAAgB;AAAA,MACnB;AAGD,UAAI,CAAC,WAAW,OAAO;AACrBA,sBAAAA,MAAY,MAAA,OAAA,qDAAA,aAAa;AACzB;AAAA,MACD;AAED,UAAI,oBAAoB,OAAO;AAC7BA,sBAAAA,wEAAY,cAAc;AAC1B;AAAA,MACD;AAGDA,oBAAAA,MAAA,MAAA,OAAA,qDAAY,aAAa;AACzB,wBAAmB;AAAA,IACrB;AAGA,UAAM,oBAAoB,YAAY;AACpCA,oBAAAA,MAAY,MAAA,OAAA,qDAAA,iBAAiB;AAC7BA,oBAAAA,MAAY,MAAA,OAAA,qDAAA,gBAAgB;AAC5BA,oBAAAA,MAAA,MAAA,OAAA,qDAAY,UAAU;AAAA,QACpB,WAAW,CAAC,CAAC,UAAU;AAAA,QACvB,eAAe,cAAc;AAAA,QAC7B,qBAAqB,oBAAoB;AAAA,MAC7C,CAAG;AAED,UAAI,CAAC,UAAU,SAAS,CAAC,cAAc,OAAO;AAC5CA,sBAAAA,MAAA,MAAA,SAAA,qDAAc,QAAQ;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,oBAAoB,OAAO;AAC7BA,sBAAAA,wEAAY,cAAc;AAC1B;AAAA,MACD;AAED,UAAI;AACFA,sBAAAA,wEAAY,cAAc;AAC1B,cAAM,WAAW,MAAM,YAAa;AACpCA,sBAAAA,MAAA,MAAA,OAAA,qDAAY,iBAAiB,QAAQ;AAErC,YAAI,CAAC,UAAU;AACbA,wBAAAA,0EAAc,WAAW;AACzBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AACD;AAAA,QACD;AAED,cAAM,EAAE,MAAM,KAAI,IAAK;AAGvB,cAAM,WAAW,QAAQ,KAAK,SAAS,WAAW;AAGlD,cAAM,YAAY,OAAO,KAAK,QAAQ,OAAO,EAAE,EAAE,KAAI,IAAK;AAC1D,cAAM,iBAAiB,UAAU,SAAS;AAC1C,cAAM,kBAAkB,CAAC,CAAC,aAAa;AAEvCA,sBAAAA,MAAA,MAAA,OAAA,qDAAY,WAAW;AAAA,UACrB,cAAc;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAY,UAAU;AAAA,UACtB,cAAc,aAAa;AAAA,QACjC,CAAK;AAED,YAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,UAAU;AACpDA,wBAAAA,MAAA,MAAA,OAAA,qDAAY,aAAa;AACzBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AACD;AAAA,QACD;AAEDA,sBAAAA,MAAY,MAAA,OAAA,qDAAA,aAAa;AACzB,4BAAoB,QAAQ;AAE5B,YAAI,eAAe;AAEnB,YAAI,gBAAgB;AAElB,cAAI,UAAU;AAEZA,0BAAAA,wEAAY,aAAa;AACzB,2BAAe,MAAM,kBAAkB,QAAQ,EAAE;AAAA,UACzD,OAAa;AAELA,0BAAAA,MAAA,MAAA,OAAA,qDAAY,SAAS;AACrB,2BAAe;AAAA,UAChB;AAAA,QACF,WAAU,UAAU;AAEnBA,wBAAAA,MAAY,MAAA,OAAA,qDAAA,SAAS;AACrB,yBAAe,MAAM,kBAAkB,QAAQ,EAAE;AAAA,QAClD;AAGD,YAAI,CAAC,aAAa,KAAM,KAAI,CAAC,iBAAiB;AAC5C,yBAAe;AAAA,QAChB;AAEDA,sBAAAA,MAAA,MAAA,OAAA,qDAAY,gBAAgB;AAC5BA,sBAAAA,MAAA,MAAA,OAAA,qDAAY,WAAW;AAAA,UACrB,SAAS;AAAA,UACT,OAAO,aAAa,SAAS;AAAA,UAC7B,eAAe,aAAa;AAAA,QAClC,CAAK;AAGD,aAAK,QAAQ;AAAA,UACX,SAAS,aAAa,KAAM;AAAA,UAC5B,OAAO,aAAa,SAAS;AAAA,QACnC,CAAK;AAEDA,sBAAAA,MAAA,MAAA,OAAA,qDAAY,SAAS;AAGrBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAGD,mBAAW,MAAM;AAEf,cAAI,UAAU,OAAO;AACnB,sBAAU,MAAM,MAAO;AAAA,UACxB;AACD,uBAAa,QAAQ;AACrB,oBAAU,QAAQ;AAClB,qBAAW,QAAQ;AACnB,uBAAa,QAAQ;AACrBA,wBAAAA,wEAAY,UAAU;AAAA,QACvB,GAAE,GAAG;AAAA,MAEP,SAAQ,KAAK;AACZA,sBAAAA,MAAA,MAAA,SAAA,qDAAc,gBAAgB;AAC9BA,sBAAAA,MAAc,MAAA,SAAA,qDAAA,SAAS,GAAG;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,mBAAW,MAAM;AACf,8BAAoB,QAAQ;AAC5BA,wBAAAA,MAAY,MAAA,OAAA,qDAAA,SAAS;AAAA,QACtB,GAAE,GAAG;AAAA,MACP;AAAA,IACH;AAGA,UAAM,QAAQ,MAAM;AAClB,UAAI,UAAU,OAAO;AACnB,kBAAU,MAAM,MAAO;AAAA,MACxB;AACD,mBAAa,QAAQ;AACrB,iBAAW,QAAQ;AACnB,mBAAa,QAAQ;AAAA,IACvB;AAGA,UAAM,aAAa,CAAC,SAAS;AAC3B,UAAI,UAAU,SAAS,MAAM;AAC3B,kBAAU,MAAM,YAAY;AAAA,UAC1B,MAAM;AAAA,UACN,SAAS,MAAM;AACb,0BAAe;AAAA,UAChB;AAAA,QACP,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,kBAAkB,MAAM;AAC5B,oBAAc,QAAQ;AAGtB,gBAAU,QAAQ;AAGlB,UAAI,YAAY,SAAS,WAAW,OAAO,iBAAiB,SAAS,SAAS,WAAW,OAAO,iBAAiB,MAAM,mBAAmB;AACxI,iBAAS,WAAW,OAAO,iBAAiB,MAAM;AAAA,UAAkB;AAAA;AAAA,UAElE,CAAC,QAAQ;AACPA,0BAAAA,MAAI,YAAa;AACjB,gBAAI,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC5B,2BAAa,QAAQ,IAAI,KAAK;AAC9B,yBAAW,QAAQ;AACnBA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACX,OAAe;AACLA,4BAAAA,MAAA,MAAA,SAAA,qDAAc,eAAe,GAAG;AAChCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAED,CAAC,QAAQ;AACPA,0BAAAA,MAAI,YAAa;AACjBA,0BAAAA,MAAA,MAAA,OAAA,qDAAY,cAAc,GAAG;AAAA,UAC9B;AAAA;AAAA,UAED,CAAC,QAAQ;AACP,gBAAI,OAAO,IAAI,KAAK,IAAI,GAAG;AACzBA,kCAAA,MAAA,OAAA,qDAAY,SAAS,IAAI,GAAG,KAAK,IAAI,CAAC;AAAA,YACvC;AAAA,UACF;AAAA,QACF;AAAA,MACL,OAAS;AAELA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,UAAU,CAAC,YAAY,YAAY;AAAA,UACnC,YAAY,CAAC,SAAS,QAAQ;AAAA,UAC9B,SAAS,CAAC,QAAQ;AAChB,kBAAM,eAAe,IAAI,cAAc,CAAC;AAGxCA,0BAAAA,MAAI,YAAY;AAAA,cACd,UAAU;AAAA,cACV,SAAS,CAAC,aAAa;AACrB,sBAAM,aAAa,SAAS,QAAQ,OAAO;AAC3C,oBAAI,aAAa,GAAG;AAClBA,gCAAAA,MAAI,UAAU;AAAA,oBACZ,OAAO;AAAA,oBACP,MAAM;AAAA,kBACtB,CAAe;AACD;AAAA,gBACD;AAGDA,8BAAAA,MAAI,YAAY;AAAA,kBACd,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAGDA,8BAAAA,MAAI,WAAW;AAAA,kBACb,KAAM,YAAY,SAAS,WAAW,OAAO,iBAAiB,QAAQ,SAAS,WAAW,OAAO,iBAAiB,KAAK,aAAc;AAAA,kBACrI,UAAU;AAAA,kBACV,MAAM;AAAA,kBACN,SAAS,CAAC,cAAc;AACtBA,kCAAAA,MAAI,YAAa;AACjB,wBAAI;AACF,4BAAM,OAAO,KAAK,MAAM,UAAU,IAAI;AACtC,0BAAI,KAAK,SAAS,OAAO,KAAK,QAAQ,KAAK,KAAK,KAAK;AACnD,qCAAa,QAAQ,KAAK,KAAK;AAC/B,mCAAW,QAAQ;AACnBA,sCAAAA,MAAI,UAAU;AAAA,0BACZ,OAAO;AAAA,0BACP,MAAM;AAAA,wBAC5B,CAAqB;AAAA,sBACrB,OAAyB;AACL,8BAAM,IAAI,MAAM,UAAU;AAAA,sBAC3B;AAAA,oBACF,SAAQ,GAAG;AACVA,oCAAAA,MAAA,MAAA,SAAA,qDAAc,aAAa,CAAC;AAC5BA,oCAAAA,MAAI,UAAU;AAAA,wBACZ,OAAO;AAAA,wBACP,MAAM;AAAA,sBAC1B,CAAmB;AAAA,oBACF;AAAA,kBACF;AAAA,kBACD,MAAM,CAAC,QAAQ;AACbA,kCAAAA,MAAI,YAAa;AACjBA,kCAAAA,MAAc,MAAA,SAAA,qDAAA,WAAW,GAAG;AAC5BA,kCAAAA,MAAI,UAAU;AAAA,sBACZ,OAAO;AAAA,sBACP,MAAM;AAAA,oBACxB,CAAiB;AAAA,kBACF;AAAA,gBACf,CAAa;AAAA,cACF;AAAA,cACD,MAAM,CAAC,QAAQ;AACbA,8BAAAA,MAAA,MAAA,SAAA,qDAAc,aAAa,GAAG;AAC9BA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAAA,cACF;AAAA,YACX,CAAS;AAAA,UACF;AAAA,QACP,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/B,mBAAa,QAAQ;AAAA,IACvB;AAGA,UAAM,eAAe,MAAM;AACzB,qBAAe,QAAQ;AACvB,qBAAe,QAAQ,UAAU;AACjC,kBAAY,MAAM,MAAO;AAGzB,qBAAgB;AAAA,IAClB;AAGA,UAAM,cAAc,MAAM;AACxB,oBAAc,QAAQ;AACtB,qBAAe,QAAQ;AAGvB,kBAAa;AAGb,UAAI,UAAU,OAAO;AACnB,kBAAU,QAAQ;AAClB,sBAAc,QAAQ;AAAA,MACvB;AAGD,kBAAY,MAAM,KAAM;AACxB,qBAAe,QAAQ;AAAA,IACzB;AAGA,UAAM,eAAe,MAAM;AACzB,UAAI,aAAa,OAAO;AACtBA,sBAAAA,MAAI,aAAa;AAAA,UACf,MAAM,CAAC,aAAa,KAAK;AAAA,UACzB,SAAS,aAAa;AAAA,QAC5B,CAAK;AAAA,MACF;AAAA,IACH;AA8EA,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,UAAU,SAAS,CAAC,cAAc;AAAO;AAE9C,gBAAU,MAAM,YAAY;AAAA,QAC1B,SAAS,SAAO;AACd,uBAAa,QAAQ;AAAA,QACtB;AAAA,QACD,MAAM,SAAO;AACXA,wBAAAA,MAAc,MAAA,SAAA,sDAAA,WAAW,GAAG;AAAA,QAC7B;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,UAAU,SAAS,CAAC,cAAc,SAAS,CAAC,aAAa;AAAO;AAErE,gBAAU,MAAM,YAAY;AAAA,QAC1B,MAAM,aAAa,MAAM;AAAA,QACzB,SAAS,MAAM;AACb,wBAAe;AAAA,QAChB;AAAA,QACD,MAAM,SAAO;AACXA,wBAAAA,MAAc,MAAA,SAAA,sDAAA,WAAW,GAAG;AAAA,QAC7B;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,UAAU,SAAS,CAAC,cAAc;AAAO;AAE9C,UAAI;AAWF,YAAI,OAAO,UAAU,MAAM,UAAU,YAAY;AAC/C,oBAAU,MAAM,MAAO;AAAA,QACxB;AAAA,MACF,SAAQ,KAAK;AACZA,sBAAAA,MAAA,MAAA,SAAA,sDAAc,cAAc,GAAG;AAAA,MAChC;AAAA,IACH;AAGA,UAAM,aAAa,MAAM;AACvB,kBAAY,QAAQ;AACpB,mBAAa,QAAQ;AACrB,gBAAU,QAAQ;AAClB,oBAAc,QAAQ;AACtB,qBAAe,QAAQ;AACvB,qBAAe,QAAQ;AACvB,oBAAc,QAAQ;AACtB,mBAAa,QAAQ;AACrB,oBAAc,QAAQ;AACtB,iBAAW,QAAQ;AAGnB,UAAI,mBAAmB,OAAO;AAC5B,qBAAa,mBAAmB,KAAK;AACrC,2BAAmB,QAAQ;AAAA,MAC5B;AACD,UAAI,WAAW,OAAO;AACpB,qBAAa,WAAW,KAAK;AAC7B,mBAAW,QAAQ;AAAA,MACpB;AAAA,IACH;AAGAI,kBAAAA,UAAU,MAAM;AACdJ,oBAAAA,yEAAY,oBAAoB;AAGhC,mBAAc;AAAA,IAShB,CAAC;AAEDK,kBAAAA,gBAAgB,MAAM;AAEpB,UAAI,mBAAmB,OAAO;AAC5B,qBAAa,mBAAmB,KAAK;AACrC,2BAAmB,QAAQ;AAAA,MAC5B;AACD,UAAI,WAAW,OAAO;AACpB,qBAAa,WAAW,KAAK;AAC7B,mBAAW,QAAQ;AAAA,MACpB;AAGD,UAAI,SAAS,OAAO;AAClB,iBAAS,MAAM,WAAY;AAC3B,iBAAS,QAAQ;AAAA,MAClB;AAGD,gBAAU,QAAQ;AAAA,IACpB,CAAC;AAGD,aAAa;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACptCD,GAAG,gBAAgBC,SAAS;"}