{"version": 3, "file": "circle.js", "sources": ["pages/center/circle.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2VudGVyL2NpcmNsZS52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <view class=\"nav-bar bfw\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\r\n      <view class=\"bar-box df\" :style=\"{'height': titleBarHeight + 'px', 'width': '100%'}\">\r\n        <view class=\"bar-back df\" @tap=\"navBack\">\r\n          <image src=\"/static/img/z.png\" style=\"width:34rpx;height:34rpx\"></image>\r\n        </view>\r\n        <view class=\"bar-title ohto\">圈子</view>\r\n      </view>\r\n      <view class=\"nav-box df\">\r\n        <view \r\n          v-for=\"(item, index) in barList\" \r\n          :key=\"index\" \r\n          class=\"nav-item df\" \r\n          @tap=\"barClick\" \r\n          :data-idx=\"index\">\r\n          <text :style=\"{'color': index == barIdx ? '#000' : '#999', 'font-size': index == barIdx ? '28rpx' : '26rpx'}\">\r\n            {{ item }}\r\n          </text>\r\n          <view :style=\"{'opacity': index == barIdx ? 1 : 0}\" class=\"nav-line\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <view class=\"content-box\" :style=\"{'margin-top': 'calc(' + (statusBarHeight + titleBarHeight) + 'px + 90rpx)'}\">\r\n      <!-- 自定义加载指示器 -->\r\n      <view v-if=\"showLoading\" class=\"loading-container\">\r\n        <view class=\"loading-indicator\"></view>\r\n      </view>\r\n      \r\n      <view v-if=\"isEmpty\" class=\"empty-box df\">\r\n        <image src=\"/static/img/empty.png\"/>\r\n        <view class=\"e1\">{{ barIdx == 0 ? '暂无推荐圈子' : '暂无加入圈子' }}</view>\r\n        <view class=\"e2\">空空如也，等待探索</view>\r\n      </view>\r\n      \r\n      <block v-else>\r\n        <view \r\n          v-for=\"(item, index) in list\" \r\n          :key=\"index\" \r\n          class=\"list-box df\" \r\n          :data-id=\"item.id\" \r\n          @tap=\"toCircle\">\r\n          <view class=\"list-avatar\">\r\n            <lazy-image :src=\"item.circle_avatar || item.avatar\" :br=\"'168rpx'\"></lazy-image>\r\n            <view v-if=\"item.is_official == 1\" class=\"tag\" style=\"background: url(/static/img/gf.png) 0% 0% / 100% 100%;\"></view>\r\n            <view v-else-if=\"item.is_hot == 1\" class=\"tag\" style=\"background: url(/static/img/tj.png) 0% 0% / 100% 100%;\"></view>\r\n          </view>\r\n          <view class=\"list-item\">\r\n            <view class=\"name ohto2\">{{ item.circle_name || item.name }}</view>\r\n            <view class=\"intro ohto2\">{{ item.circle_description || item.intro }}</view>\r\n            <view v-if=\"item.member_count > 0\" class=\"cu-img-group\">\r\n              <view v-for=\"(member, imgIndex) in item.recent_members\" :key=\"imgIndex\" class=\"cu-img\" v-if=\"imgIndex < 3\">\r\n                <image :src=\"member.avatar\" mode=\"aspectFill\"></image>\r\n              </view>\r\n              <view class=\"cu-txt\">{{ item.member_count }}人加入 · {{ item.dynamic_count || 0 }}篇笔记</view>\r\n              <view class=\"view-count\" v-if=\"item.view_count\">· 访问 {{ item.view_count }}</view>\r\n            </view>\r\n            <view class=\"recent-topics\" v-if=\"item.recent_topics && item.recent_topics.length\">\r\n              <view class=\"recent-title\">最近话题：</view>\r\n              <view class=\"topic-item\" v-for=\"(topic, topicIndex) in item.recent_topics\" :key=\"topicIndex\">\r\n                {{topic}}\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </block>\r\n      \r\n      <!-- 底部加载状态显示 -->\r\n      <view v-if=\"list.length > 0 && loadStatus === 'noMore'\" style=\"text-align: center; padding: 20rpx 0; color: #999; font-size: 24rpx;\">\r\n        没有更多数据了\r\n      </view>\r\n    </view>\r\n    \r\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\r\n      <view class=\"tips-box df\">\r\n        <view class=\"tips-item bfh\">{{ tipsTitle }}</view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport lazyImage from '@/components/lazyImage/lazyImage'\r\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\r\nimport { getCircleList, getMyCircles, getJoinedCircles } from '@/api/social'\r\n\r\nconst app = getApp();\r\n\r\nexport default {\r\n  components: {\r\n    lazyImage,\r\n    uniLoadMore\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\r\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\r\n      barList: ['全部', '我的'],\r\n      barIdx: 0,\r\n      list: [],\r\n      page: 1,\r\n      limit: 10,\r\n      totalCount: 0,\r\n      isEmpty: false,\r\n      isThrottling: true,\r\n      loadStatus: 'more',\r\n      tipsTitle: '',\r\n      showLoading: false,\r\n      loadingTimer: null,\r\n      debounceTimer: null\r\n    }\r\n  },\r\n  onLoad(option) {\r\n    if (option && option.type) {\r\n      this.barIdx = parseInt(option.type) || 0\r\n    }\r\n    this.getCircleData()\r\n  },\r\n  // 开启下拉刷新\r\n  onPullDownRefresh() {\r\n    if (this.loadStatus !== 'loading') {\r\n      this.page = 1;\r\n      this.getCircleData();\r\n    } else {\r\n      uni.stopPullDownRefresh();\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取圈子数据\r\n    getCircleData() {\r\n      let that = this;\r\n      that.isThrottling = false;\r\n      \r\n      // 设置为加载中状态\r\n      that.loadStatus = 'loading';\r\n      \r\n      // 延迟显示加载指示器，避免短时间内的闪烁\r\n      if (that.loadingTimer) {\r\n        clearTimeout(that.loadingTimer);\r\n      }\r\n      that.loadingTimer = setTimeout(() => {\r\n        if (that.loadStatus === 'loading') {\r\n          that.showLoading = true;\r\n        }\r\n      }, 300);\r\n      \r\n      // 请求参数\r\n      const params = {\r\n        page: that.page,\r\n        limit: that.limit\r\n      };\r\n      \r\n      // 根据标签选择API\r\n      let apiMethod;\r\n      if (that.barIdx === 0) {\r\n        // 全部圈子\r\n        apiMethod = getCircleList;\r\n      } else {\r\n        // 我的圈子 - 这里可以调用我创建的圈子或者我加入的圈子，根据实际需求调整\r\n        // 目前先使用加入的圈子，如果需要同时显示创建的和加入的，可以并发请求后合并\r\n        apiMethod = getJoinedCircles;\r\n      }\r\n      \r\n      // 调用API\r\n      apiMethod(params).then(res => {\r\n        that.isThrottling = true;\r\n        that.loadStatus = \"more\";\r\n        \r\n        // 清除加载定时器并隐藏加载指示器\r\n        if (that.loadingTimer) {\r\n          clearTimeout(that.loadingTimer);\r\n          that.loadingTimer = null;\r\n        }\r\n        that.showLoading = false;\r\n        \r\n        // 停止下拉刷新\r\n        uni.stopPullDownRefresh();\r\n        \r\n        if (res.status === 200 && res.data) {\r\n          // 处理返回的数据\r\n          const responseData = res.data;\r\n          \r\n          if (responseData.list && responseData.list.length > 0) {\r\n            if (that.page == 1) {\r\n              that.list = responseData.list;\r\n            } else {\r\n              that.list = that.list.concat(responseData.list);\r\n            }\r\n            \r\n            // 更新总记录数\r\n            if (responseData.count !== undefined) {\r\n              that.totalCount = responseData.count;\r\n            }\r\n            \r\n            that.isEmpty = false;\r\n          } else if (that.page == 1) {\r\n            that.isEmpty = true;\r\n            that.list = [];\r\n          }\r\n          \r\n          // 判断是否还有更多数据\r\n          if (that.list.length >= that.totalCount && that.list.length > 0) {\r\n            that.loadStatus = \"noMore\";\r\n          }\r\n        } else {\r\n          if (that.page == 1) {\r\n            that.isEmpty = true;\r\n            that.list = [];\r\n          }\r\n          that.opTipsPopup(res.msg || \"获取数据失败\");\r\n        }\r\n      }).catch(err => {\r\n        that.isThrottling = true;\r\n        that.loadStatus = \"more\";\r\n        \r\n        // 清除加载定时器并隐藏加载指示器\r\n        if (that.loadingTimer) {\r\n          clearTimeout(that.loadingTimer);\r\n          that.loadingTimer = null;\r\n        }\r\n        that.showLoading = false;\r\n        \r\n        // 停止下拉刷新\r\n        uni.stopPullDownRefresh();\r\n        \r\n        if (that.page == 1) {\r\n          that.isEmpty = true;\r\n          that.list = [];\r\n        }\r\n        \r\n        that.opTipsPopup(\"网络错误，请稍后重试\");\r\n        console.error('获取圈子列表失败:', err);\r\n      });\r\n    },\r\n    \r\n    // 标签切换\r\n    barClick(e) {\r\n      // 如果正在请求数据或节流状态为false，不处理点击\r\n      if (!this.isThrottling || this.loadStatus === 'loading') {\r\n        return;\r\n      }\r\n      \r\n      // 获取点击的标签索引\r\n      const clickIdx = parseInt(e.currentTarget.dataset.idx);\r\n      \r\n      // 如果点击的是当前选中的标签，不重复加载\r\n      if (clickIdx === this.barIdx) {\r\n        return;\r\n      }\r\n      \r\n      // 使用防抖处理，避免快速重复点击\r\n      if (this.debounceTimer) {\r\n        clearTimeout(this.debounceTimer);\r\n      }\r\n      \r\n      this.debounceTimer = setTimeout(() => {\r\n        this.isThrottling = false;\r\n        this.barIdx = clickIdx;\r\n        this.page = 1;\r\n        this.loadStatus = \"loading\";\r\n        \r\n        // 重置列表数据，立即显示空状态，提高用户体验\r\n        this.list = [];\r\n        \r\n        // 获取数据\r\n        this.getCircleData();\r\n      }, 100);\r\n    },\r\n    \r\n    // 跳转到圈子详情\r\n    toCircle(e) {\r\n      // 安全检查参数\r\n      if (!e || !e.currentTarget || !e.currentTarget.dataset || !e.currentTarget.dataset.id) {\r\n        this.opTipsPopup('圈子信息无效');\r\n        return;\r\n      }\r\n      \r\n      const id = parseInt(e.currentTarget.dataset.id);\r\n      \r\n      // 检查圈子ID是否有效\r\n      if (!id || id <= 0) {\r\n        this.opTipsPopup('圈子ID无效');\r\n        return;\r\n      }\r\n      \r\n      uni.navigateTo({\r\n        url: '/pages/note/circle?id=' + id,\r\n        fail: () => {\r\n          this.opTipsPopup('页面跳转失败');\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 返回上一页\r\n    navBack() {\r\n      const pages = getCurrentPages();\r\n      if (pages.length > 1) {\r\n        uni.navigateBack({\r\n          fail: () => {\r\n            uni.switchTab({\r\n              url: '/pages/user/index'\r\n            });\r\n          }\r\n        });\r\n      } else {\r\n        uni.switchTab({\r\n          url: '/pages/user/index'\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 显示提示信息\r\n    opTipsPopup(msg) {\r\n      this.tipsTitle = msg;\r\n      this.$refs.tipsPopup.open();\r\n      setTimeout(() => {\r\n        this.$refs.tipsPopup.close();\r\n      }, 2000);\r\n    }\r\n  },\r\n  \r\n  onReachBottom() {\r\n    // 如果当前状态是加载中，不触发加载更多\r\n    if (this.loadStatus === \"loading\") {\r\n      return;\r\n    }\r\n    \r\n    // 如果未到达最大数量，加载更多\r\n    if (this.isThrottling && this.list.length && this.list.length < this.totalCount) {\r\n      this.page = this.page + 1;\r\n      this.loadStatus = \"loading\";\r\n      this.getCircleData();\r\n    } else if (this.list.length >= this.totalCount && this.list.length > 0) {\r\n      this.loadStatus = \"noMore\";\r\n    }\r\n  },\r\n  \r\n  onUnload() {\r\n    // 组件卸载时清除定时器\r\n    if (this.loadingTimer) {\r\n      clearTimeout(this.loadingTimer);\r\n      this.loadingTimer = null;\r\n    }\r\n    if (this.debounceTimer) {\r\n      clearTimeout(this.debounceTimer);\r\n      this.debounceTimer = null;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.nav-bar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 99;\r\n  box-sizing: border-box;\r\n}\r\n.bar-box .bar-back {\r\n  padding: 0 30rpx;\r\n  width: 34rpx;\r\n  height: 100%;\r\n}\r\n.bar-box .bar-title {\r\n  max-width: 60%;\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n}\r\n.nav-box {\r\n  width: 100%;\r\n  height: 80rpx;\r\n}\r\n.nav-box .nav-item {\r\n  padding: 0 30rpx;\r\n  height: 100%;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n.nav-box .nav-item text {\r\n  font-weight: 700;\r\n  transition: all .3s ease-in-out;\r\n}\r\n.nav-box .nav-line {\r\n  position: absolute;\r\n  bottom: 12rpx;\r\n  width: 18rpx;\r\n  height: 6rpx;\r\n  border-radius: 6rpx;\r\n  background: #000;\r\n  transition: opacity .3s ease-in-out;\r\n}\r\n.content-box {\r\n  width: calc(100% - 60rpx);\r\n  padding: 30rpx;\r\n}\r\n\r\n/* 加载中状态样式 */\r\n.loading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 60rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n.loading-indicator {\r\n  width: 30rpx;\r\n  height: 30rpx;\r\n  border: 3rpx solid #f3f3f3;\r\n  border-top: 3rpx solid #000;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.list-box {\r\n  width: 100%;\r\n  padding-bottom: 30rpx;\r\n  justify-content: space-between;\r\n}\r\n.list-box .list-avatar {\r\n  width: 168rpx;\r\n  height: 168rpx;\r\n  border-radius: 50%;\r\n  background: #f8f8f8;\r\n  border: 1px solid #f8f8f8;\r\n  position: relative;\r\n}\r\n.list-avatar .tag {\r\n  position: absolute;\r\n  right: 0;\r\n  bottom: 0;\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  border-radius: 50%;\r\n  border: 6rpx solid #fff;\r\n}\r\n.list-box .list-item {\r\n  width: calc(100% - 198rpx);\r\n  margin-left: 30rpx;\r\n}\r\n.list-item .name {\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n}\r\n.list-item .intro {\r\n  margin: 10rpx 0 20rpx;\r\n  color: #999;\r\n  font-size: 24rpx;\r\n}\r\n.cu-img-group {\r\n  direction: ltr;\r\n  unicode-bidi: bidi-override;\r\n  display: inline-block;\r\n  margin-left: 16rpx;\r\n}\r\n.cu-img-group .cu-img {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  display: inline-flex;\r\n  position: relative;\r\n  margin-left: -16rpx;\r\n  border: 4rpx solid #fff;\r\n  background: #f8f8f8;\r\n  vertical-align: middle;\r\n  border-radius: 50%;\r\n}\r\n.cu-img-group .cu-img image {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n}\r\n.cu-img-group .cu-txt {\r\n  margin-left: 10rpx;\r\n  display: inline-flex;\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  font-weight: 700;\r\n}\r\n.view-count {\r\n  display: inline-flex;\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  font-weight: 500;\r\n  margin-left: 4rpx;\r\n}\r\n.df {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n.bfw {\r\n  backdrop-filter: blur(10px);\r\n  -webkit-backdrop-filter: blur(10px);\r\n  background: rgba(255,255,255,.8);\r\n}\r\n.bfh {\r\n  background: #000;\r\n  color: #fff;\r\n  padding: 20rpx 40rpx;\r\n  border-radius: 12rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n}\r\n.empty-box {\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n.empty-box image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n.empty-box .e1 {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 10rpx;\r\n}\r\n.empty-box .e2 {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n.tips-box {\r\n  justify-content: center;\r\n  width: 100%;\r\n}\r\n.ohto {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.ohto2 {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  word-break: break-all;\r\n}\r\n.heio {\r\n  width: 100%;\r\n  overflow: hidden;\r\n  transition: height 0.3s;\r\n  justify-content: center;\r\n}\r\n\r\n.tags-box {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: 6rpx 0;\r\n}\r\n\r\n.tag-item {\r\n  font-size: 20rpx;\r\n  color: #666;\r\n  margin-right: 12rpx;\r\n  background: #f5f5f5;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 10rpx;\r\n}\r\n\r\n.recent-topics {\r\n  margin-top: 8rpx;\r\n}\r\n\r\n.recent-title {\r\n  font-size: 20rpx;\r\n  color: #666;\r\n  margin-bottom: 4rpx;\r\n}\r\n\r\n.topic-item {\r\n  font-size: 20rpx;\r\n  color: #666;\r\n  background: #f8f8f8;\r\n  padding: 4rpx 12rpx;\r\n  border-radius: 8rpx;\r\n  margin-bottom: 4rpx;\r\n  margin-right: 8rpx;\r\n  display: inline-block;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/uniapp/vue3/pages/center/circle.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getCircleList", "getJoinedCircles"], "mappings": ";;;;AAmFA,kBAAkB,MAAW;AAC7B,MAAO,cAAa,MAAW;AAGnB,OAAQ;AAEpB,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB,KAAK,OAAO,MAAM,mBAAmB;AAAA,MACtD,gBAAgB,KAAK,OAAO,MAAM,kBAAkB;AAAA,MACpD,SAAS,CAAC,MAAM,IAAI;AAAA,MACpB,QAAQ;AAAA,MACR,MAAM,CAAE;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,aAAa;AAAA,MACb,cAAc;AAAA,MACd,eAAe;AAAA,IACjB;AAAA,EACD;AAAA,EACD,OAAO,QAAQ;AACb,QAAI,UAAU,OAAO,MAAM;AACzB,WAAK,SAAS,SAAS,OAAO,IAAI,KAAK;AAAA,IACzC;AACA,SAAK,cAAc;AAAA,EACpB;AAAA;AAAA,EAED,oBAAoB;AAClB,QAAI,KAAK,eAAe,WAAW;AACjC,WAAK,OAAO;AACZ,WAAK,cAAa;AAAA,WACb;AACLA,oBAAG,MAAC,oBAAmB;AAAA,IACzB;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,gBAAgB;AACd,UAAI,OAAO;AACX,WAAK,eAAe;AAGpB,WAAK,aAAa;AAGlB,UAAI,KAAK,cAAc;AACrB,qBAAa,KAAK,YAAY;AAAA,MAChC;AACA,WAAK,eAAe,WAAW,MAAM;AACnC,YAAI,KAAK,eAAe,WAAW;AACjC,eAAK,cAAc;AAAA,QACrB;AAAA,MACD,GAAE,GAAG;AAGN,YAAM,SAAS;AAAA,QACb,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA;AAId,UAAI;AACJ,UAAI,KAAK,WAAW,GAAG;AAErB,oBAAYC,WAAAA;AAAAA,aACP;AAGL,oBAAYC,WAAAA;AAAAA,MACd;AAGA,gBAAU,MAAM,EAAE,KAAK,SAAO;AAC5B,aAAK,eAAe;AACpB,aAAK,aAAa;AAGlB,YAAI,KAAK,cAAc;AACrB,uBAAa,KAAK,YAAY;AAC9B,eAAK,eAAe;AAAA,QACtB;AACA,aAAK,cAAc;AAGnBF,sBAAG,MAAC,oBAAmB;AAEvB,YAAI,IAAI,WAAW,OAAO,IAAI,MAAM;AAElC,gBAAM,eAAe,IAAI;AAEzB,cAAI,aAAa,QAAQ,aAAa,KAAK,SAAS,GAAG;AACrD,gBAAI,KAAK,QAAQ,GAAG;AAClB,mBAAK,OAAO,aAAa;AAAA,mBACpB;AACL,mBAAK,OAAO,KAAK,KAAK,OAAO,aAAa,IAAI;AAAA,YAChD;AAGA,gBAAI,aAAa,UAAU,QAAW;AACpC,mBAAK,aAAa,aAAa;AAAA,YACjC;AAEA,iBAAK,UAAU;AAAA,UACjB,WAAW,KAAK,QAAQ,GAAG;AACzB,iBAAK,UAAU;AACf,iBAAK,OAAO;UACd;AAGA,cAAI,KAAK,KAAK,UAAU,KAAK,cAAc,KAAK,KAAK,SAAS,GAAG;AAC/D,iBAAK,aAAa;AAAA,UACpB;AAAA,eACK;AACL,cAAI,KAAK,QAAQ,GAAG;AAClB,iBAAK,UAAU;AACf,iBAAK,OAAO;UACd;AACA,eAAK,YAAY,IAAI,OAAO,QAAQ;AAAA,QACtC;AAAA,MACF,CAAC,EAAE,MAAM,SAAO;AACd,aAAK,eAAe;AACpB,aAAK,aAAa;AAGlB,YAAI,KAAK,cAAc;AACrB,uBAAa,KAAK,YAAY;AAC9B,eAAK,eAAe;AAAA,QACtB;AACA,aAAK,cAAc;AAGnBA,sBAAG,MAAC,oBAAmB;AAEvB,YAAI,KAAK,QAAQ,GAAG;AAClB,eAAK,UAAU;AACf,eAAK,OAAO;QACd;AAEA,aAAK,YAAY,YAAY;AAC7BA,sBAAc,MAAA,MAAA,SAAA,kCAAA,aAAa,GAAG;AAAA,MAChC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,SAAS,GAAG;AAEV,UAAI,CAAC,KAAK,gBAAgB,KAAK,eAAe,WAAW;AACvD;AAAA,MACF;AAGA,YAAM,WAAW,SAAS,EAAE,cAAc,QAAQ,GAAG;AAGrD,UAAI,aAAa,KAAK,QAAQ;AAC5B;AAAA,MACF;AAGA,UAAI,KAAK,eAAe;AACtB,qBAAa,KAAK,aAAa;AAAA,MACjC;AAEA,WAAK,gBAAgB,WAAW,MAAM;AACpC,aAAK,eAAe;AACpB,aAAK,SAAS;AACd,aAAK,OAAO;AACZ,aAAK,aAAa;AAGlB,aAAK,OAAO;AAGZ,aAAK,cAAa;AAAA,MACnB,GAAE,GAAG;AAAA,IACP;AAAA;AAAA,IAGD,SAAS,GAAG;AAEV,UAAI,CAAC,KAAK,CAAC,EAAE,iBAAiB,CAAC,EAAE,cAAc,WAAW,CAAC,EAAE,cAAc,QAAQ,IAAI;AACrF,aAAK,YAAY,QAAQ;AACzB;AAAA,MACF;AAEA,YAAM,KAAK,SAAS,EAAE,cAAc,QAAQ,EAAE;AAG9C,UAAI,CAAC,MAAM,MAAM,GAAG;AAClB,aAAK,YAAY,QAAQ;AACzB;AAAA,MACF;AAEAA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,2BAA2B;AAAA,QAChC,MAAM,MAAM;AACV,eAAK,YAAY,QAAQ;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,UAAU;AACR,YAAM,QAAQ;AACd,UAAI,MAAM,SAAS,GAAG;AACpBA,sBAAAA,MAAI,aAAa;AAAA,UACf,MAAM,MAAM;AACVA,0BAAAA,MAAI,UAAU;AAAA,cACZ,KAAK;AAAA,YACP,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,aACI;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,QACP,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,KAAK;AACf,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU;AACrB,iBAAW,MAAM;AACf,aAAK,MAAM,UAAU;MACtB,GAAE,GAAI;AAAA,IACT;AAAA,EACD;AAAA,EAED,gBAAgB;AAEd,QAAI,KAAK,eAAe,WAAW;AACjC;AAAA,IACF;AAGA,QAAI,KAAK,gBAAgB,KAAK,KAAK,UAAU,KAAK,KAAK,SAAS,KAAK,YAAY;AAC/E,WAAK,OAAO,KAAK,OAAO;AACxB,WAAK,aAAa;AAClB,WAAK,cAAa;AAAA,IACpB,WAAW,KAAK,KAAK,UAAU,KAAK,cAAc,KAAK,KAAK,SAAS,GAAG;AACtE,WAAK,aAAa;AAAA,IACpB;AAAA,EACD;AAAA,EAED,WAAW;AAET,QAAI,KAAK,cAAc;AACrB,mBAAa,KAAK,YAAY;AAC9B,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,KAAK,eAAe;AACtB,mBAAa,KAAK,aAAa;AAC/B,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5VA,GAAG,WAAW,eAAe;"}