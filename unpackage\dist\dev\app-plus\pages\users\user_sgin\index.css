body[data-v-72e91ee0] {
  background-color: #f5f5f5 !important;
}
.sign-page[data-v-72e91ee0] {
  min-height: 100vh;
  background-color: #87CEEB;
}
.sign-page[data-v-72e91ee0]::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, #87CEEB 0%, #E0F6FF 50%, #FFFFFF 100%);
  z-index: -1;
}
.main-content[data-v-72e91ee0] {
  position: relative;
  padding: 1.25rem 0.9375rem;
  z-index: 1;
}
.header-bg[data-v-72e91ee0] {
  position: relative;
  height: 10rem;
  padding: 1.25rem 0;
  overflow: visible;
}

/* 连续签到天数 */
.continuous-days[data-v-72e91ee0] {
  position: absolute;
  left: 1.25rem;
  top: 1.25rem;
  width: 3.75rem;
  height: 3.75rem;
  text-align: center;
  background: linear-gradient(135deg, #FFE4E1 0%, #FFF0F5 100%);
  border-radius: 0.625rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 0.0625rem solid #FFB6C1;
  z-index: 2;
}
.continuous-days .days-label[data-v-72e91ee0] {
  font-size: 0.5625rem;
  color: #FF6B6B;
  font-weight: 500;
  margin-bottom: 0.125rem;
  letter-spacing: 0.03125rem;
}
.continuous-days .days-number[data-v-72e91ee0] {
  font-size: 1.375rem;
  font-weight: bold;
  color: #FF1744;
  line-height: 1;
  margin-bottom: 0.0625rem;
}
.continuous-days .days-unit[data-v-72e91ee0] {
  font-size: 0.5625rem;
  color: #FF6B6B;
  font-weight: 500;
  letter-spacing: 0.03125rem;
}

/* 中央日历图标 */
.calendar-icon[data-v-72e91ee0] {
  position: absolute;
  left: 50%;
  top: 0.625rem;
  margin-left: -3.125rem;
  width: 6.25rem;
  height: 6.25rem;
  z-index: 2;
}
.calendar-icon .calendar-header[data-v-72e91ee0] {
  position: relative;
  background-color: #FF6B6B;
  border-radius: 0.625rem 0.625rem 0.25rem 0.25rem;
  height: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.calendar-icon .calendar-header .calendar-pin[data-v-72e91ee0] {
  position: absolute;
  top: -0.3125rem;
  width: 0.5rem;
  height: 0.9375rem;
  background: #FFF;
  border-radius: 0.25rem;
}
.calendar-icon .calendar-header .calendar-pin.left-pin[data-v-72e91ee0] {
  left: 0.9375rem;
}
.calendar-icon .calendar-header .calendar-pin.right-pin[data-v-72e91ee0] {
  right: 0.9375rem;
}
.calendar-icon .calendar-header .calendar-title[data-v-72e91ee0] {
  font-size: 0.625rem;
  color: #FFF;
  font-weight: 500;
}
.calendar-icon .calendar-body[data-v-72e91ee0] {
  background: #FFF;
  border-radius: 0.25rem 0.25rem 0.625rem 0.625rem;
  height: 4.375rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 0.03125rem solid #f0f0f0;
}
.calendar-icon .calendar-body .calendar-number[data-v-72e91ee0] {
  font-size: 1.875rem;
  font-weight: bold;
  color: #FF6B6B;
  line-height: 1.2;
}
.calendar-icon .calendar-body .calendar-unit[data-v-72e91ee0] {
  font-size: 0.75rem;
  color: #666;
  margin-top: 0.25rem;
}

/* 右侧统计数据 */
.right-stats[data-v-72e91ee0] {
  position: absolute;
  right: 1.25rem;
  top: 1.25rem;
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
  z-index: 2;
}
.right-stats .stat-item[data-v-72e91ee0] {
  text-align: center;
}
.right-stats .stat-item .stat-number[data-v-72e91ee0] {
  width: 1.875rem;
  height: 1.875rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
  color: #FF6B6B;
  margin: 0 auto 0.25rem;
  border: 0.03125rem solid #f0f0f0;
}
.right-stats .stat-item .stat-label[data-v-72e91ee0] {
  font-size: 0.5625rem;
  color: #666;
  white-space: nowrap;
}

/* 查看日历按钮 */
.calendar-btn[data-v-72e91ee0] {
  position: absolute;
  left: 50%;
  bottom: 1.25rem;
  margin-left: -2.5rem;
  background: rgba(255, 255, 255, 0.9);
  padding: 0.5rem 1rem;
  border-radius: 1.25rem;
  font-size: 0.75rem;
  color: #666;
  border: 0.03125rem solid #e0e0e0;
  z-index: 2;
}

/* 签到提醒开关 */
.remind-switch[data-v-72e91ee0] {
  position: absolute;
  right: 0;
  top: 60%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #F8E8FF 0%, #E8F4FD 100%);
  padding: 0.46875rem 0.625rem;
  border-radius: 0.9375rem 0 0 0.9375rem;
  width: 3.125rem;
  height: 2.5rem;
  z-index: 10;
  border: 0.03125rem solid #e0e0e0;
  border-right: none;
  /* 自定义开关样式 */
}
.remind-switch .switch-label[data-v-72e91ee0] {
  font-size: 0.625rem;
  color: #D2691E;
  font-weight: 600;
  margin-bottom: 0.1875rem;
  text-align: center;
}
.remind-switch .custom-switch[data-v-72e91ee0] {
  position: relative;
  width: 1.5625rem;
  height: 0.8125rem;
  background: #E0E0E0;
  border-radius: 0.40625rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
}
.remind-switch .custom-switch.active[data-v-72e91ee0] {
  background: linear-gradient(135deg, #FF69B4, #FFB347);
}
.remind-switch .custom-switch .switch-handle[data-v-72e91ee0] {
  position: absolute;
  top: 0.0625rem;
  left: 0.0625rem;
  width: 0.6875rem;
  height: 0.6875rem;
  background: #FFF;
  border-radius: 50%;
  transition: all 0.3s ease;
  border: 0.03125rem solid #ddd;
}
.remind-switch .custom-switch.active .switch-handle[data-v-72e91ee0] {
  left: 0.8125rem;
}

/* 签到按钮 */
.sign-button-container[data-v-72e91ee0] {
  margin: 1.25rem 0 1.25rem;
  padding: 0 0.9375rem;
  display: flex;
  justify-content: center;
  z-index: 2;
  position: relative;
}
.sign-button-container .sign-button[data-v-72e91ee0] {
  width: 85%;
  max-width: 18.75rem;
  height: 3.125rem;
  background: linear-gradient(135deg, #FFB347 0%, #FF69B4 100%);
  border-radius: 1.5625rem;
  border: none;
  font-size: 1rem;
  font-weight: bold;
  color: #FFF;
  letter-spacing: 0.0625rem;
  box-shadow: 0 0.25rem 0.78125rem rgba(255, 105, 180, 0.4);
  transition: all 0.3s ease;
}
.sign-button-container .sign-button[data-v-72e91ee0]:active {
  transform: translateY(0.0625rem);
  box-shadow: 0 0.1875rem 0.625rem rgba(255, 105, 180, 0.3);
}
.sign-button-container .sign-button[data-v-72e91ee0]:disabled {
  opacity: 0.7;
  transform: none;
  /* 保持渐变色，只是降低透明度 */
}

/* 日期滑动区域 */
.date-slider[data-v-72e91ee0] {
  margin: 1.25rem 0;
  background: #FFF;
  border-radius: 0.625rem;
  padding: 0.625rem;
  border: 0.03125rem solid #f0f0f0;
}
.date-slider .date-scroll[data-v-72e91ee0] {
  height: 3.75rem;
  white-space: nowrap;
}
.date-slider .date-container[data-v-72e91ee0] {
  display: flex;
  align-items: center;
  padding: 0 0.625rem;
  height: 100%;
}
.date-slider .date-item[data-v-72e91ee0] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 3.125rem;
  flex-shrink: 0;
  margin-right: 0.625rem;
}
.date-slider .date-item[data-v-72e91ee0]:last-child {
  margin-right: 0;
}
.date-slider .date-item.signed .date-text[data-v-72e91ee0] {
  color: #FF6B6B;
  font-weight: bold;
}
.date-slider .date-item.today .date-text[data-v-72e91ee0] {
  color: #FF6B6B;
  font-weight: bold;
}
.date-slider .date-item .date-icon[data-v-72e91ee0] {
  width: 1.875rem;
  height: 1.875rem;
  margin-bottom: 0.25rem;
}
.date-slider .date-item .date-icon .sign-icon[data-v-72e91ee0] {
  width: 100%;
  height: 100%;
}
.date-slider .date-item .date-text[data-v-72e91ee0] {
  font-size: 0.625rem;
  color: #666;
  text-align: center;
}

/* 签到记录 */
.sign-records[data-v-72e91ee0] {
  background: #FFF;
  border-radius: 0.625rem;
  margin: 0.625rem 0.9375rem;
  padding: 0.9375rem;
  border: 0.03125rem solid #f0f0f0;
}
.sign-records .records-header[data-v-72e91ee0] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.9375rem;
}
.sign-records .records-header .records-title[data-v-72e91ee0] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.sign-records .records-header .records-more[data-v-72e91ee0] {
  font-size: 0.75rem;
  color: #999;
}
.sign-records .records-list .record-item[data-v-72e91ee0] {
  display: flex;
  align-items: center;
  padding: 0.625rem 0;
  border-bottom: 0.03125rem solid #F5F5F5;
}
.sign-records .records-list .record-item[data-v-72e91ee0]:last-child {
  border-bottom: none;
}
.sign-records .records-list .record-item .record-icon[data-v-72e91ee0] {
  width: 1.875rem;
  height: 1.875rem;
  margin-right: 0.625rem;
}
.sign-records .records-list .record-item .record-icon uni-image[data-v-72e91ee0] {
  width: 100%;
  height: 100%;
  border-radius: 0.25rem;
}
.sign-records .records-list .record-item .record-info[data-v-72e91ee0] {
  flex: 1;
}
.sign-records .records-list .record-item .record-info .record-title[data-v-72e91ee0] {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.25rem;
}
.sign-records .records-list .record-item .record-info .record-date[data-v-72e91ee0] {
  font-size: 0.75rem;
  color: #999;
}
.sign-records .records-list .record-item .record-reward[data-v-72e91ee0] {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  font-weight: bold;
  color: #FF6B6B;
}
.sign-records .records-list .record-item .record-reward uni-image[data-v-72e91ee0] {
  width: 1rem;
  height: 1rem;
  margin-left: 0.25rem;
}

/* 日历弹窗 */
.calendar-modal[data-v-72e91ee0] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  justify-content: center;
  align-items: center;
}
.calendar-modal .calendar-content[data-v-72e91ee0] {
  width: 100%;
  background: linear-gradient(135deg, #F8FFFE 0%, #F0F8FF 100%);
  border-radius: 0.9375rem;
  margin: 0 1.25rem;
  padding: 1.5625rem 0.9375rem 1.875rem;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 0.3125rem 1.25rem rgba(0, 0, 0, 0.15);
}
.calendar-modal .calendar-content .calendar-modal-header[data-v-72e91ee0] {
  text-align: center;
  margin-bottom: 1.25rem;
}
.calendar-modal .calendar-content .calendar-modal-header .modal-title[data-v-72e91ee0] {
  font-size: 1rem;
  font-weight: 500;
  color: #666;
}
.calendar-modal .calendar-content .calendar-modal-header .modal-title .highlight-number[data-v-72e91ee0] {
  color: #FF6B6B;
  font-weight: bold;
  font-size: 1.125rem;
}
.calendar-modal .calendar-content .calendar-nav[data-v-72e91ee0] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
}
.calendar-modal .calendar-content .calendar-nav .nav-btn[data-v-72e91ee0] {
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #FFE8E8, #FFF0F0);
  border-radius: 50%;
  border: 0.03125rem solid #FFD0D0;
}
.calendar-modal .calendar-content .calendar-nav .nav-btn .nav-arrow[data-v-72e91ee0] {
  font-size: 0.875rem;
  color: #FF6B6B;
  font-weight: bold;
}
.calendar-modal .calendar-content .calendar-nav .nav-title[data-v-72e91ee0] {
  font-size: 1.125rem;
  font-weight: bold;
  color: #FF6B6B;
}
.calendar-modal .calendar-content .calendar-grid .weekdays[data-v-72e91ee0] {
  display: flex;
  margin-bottom: 0.9375rem;
}
.calendar-modal .calendar-content .calendar-grid .weekdays .weekday[data-v-72e91ee0] {
  flex: 1;
  text-align: center;
  font-size: 0.8125rem;
  color: #999;
  padding: 0.625rem 0;
  font-weight: 500;
}
.calendar-modal .calendar-content .calendar-grid .calendar-dates[data-v-72e91ee0] {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.46875rem;
  padding: 0 0.3125rem;
}
.calendar-modal .calendar-content .calendar-grid .calendar-dates .calendar-date[data-v-72e91ee0] {
  height: 3.75rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 0.625rem;
}
.calendar-modal .calendar-content .calendar-grid .calendar-dates .calendar-date.today[data-v-72e91ee0] {
  background: #FF6B6B;
  border: 0.0625rem solid #FF6B6B;
  border-radius: 0.625rem;
  border: 0.0625rem solid #FF6B6B;
}
.calendar-modal .calendar-content .calendar-grid .calendar-dates .calendar-date.today .date-number[data-v-72e91ee0] {
  color: #FFF;
  font-weight: bold;
}
.calendar-modal .calendar-content .calendar-grid .calendar-dates .calendar-date.signed[data-v-72e91ee0] {
  background: linear-gradient(135deg, #FFE8E8, #FFF0F0);
  border: 0.0625rem solid #FFD0D0;
  border-radius: 0.625rem;
  border: 0.0625rem solid #FFD0D0;
}
.calendar-modal .calendar-content .calendar-grid .calendar-dates .calendar-date.signed .date-number[data-v-72e91ee0] {
  opacity: 0;
  visibility: hidden;
}
.calendar-modal .calendar-content .calendar-grid .calendar-dates .calendar-date.signed .sign-icon[data-v-72e91ee0] {
  position: absolute;
  top: 37%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1.5625rem;
  height: 1.5625rem;
  z-index: 2;
}
.calendar-modal .calendar-content .calendar-grid .calendar-dates .calendar-date.signed .status-text[data-v-72e91ee0] {
  font-size: 0.625rem;
  color: #FF6B6B;
  font-weight: 500;
  margin-top: 0.25rem;
}
.calendar-modal .calendar-content .calendar-grid .calendar-dates .calendar-date .date-number[data-v-72e91ee0] {
  font-size: 0.9375rem;
  color: #333;
  text-align: center;
  font-weight: 500;
}
.calendar-modal .calendar-content .calendar-grid .calendar-dates .calendar-date .date-status[data-v-72e91ee0] {
  position: absolute;
  bottom: 0.15625rem;
}
.calendar-modal .calendar-content .calendar-grid .calendar-dates .calendar-date .date-status .status-text[data-v-72e91ee0] {
  font-size: 0.5625rem;
  color: #FF6B6B;
  background: rgba(255, 107, 107, 0.1);
  padding: 0.0625rem 0.25rem;
  border-radius: 0.3125rem;
}

/* 签到成功弹窗 */
.sign-success-modal[data-v-72e91ee0] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}
.sign-success-modal .success-content[data-v-72e91ee0] {
  background: #FFF;
  border-radius: 0.625rem;
  padding: 1.875rem 1.25rem;
  text-align: center;
  margin: 0 1.875rem;
  border: 0.03125rem solid #e0e0e0;
}
.sign-success-modal .success-content .success-icon[data-v-72e91ee0] {
  margin-bottom: 0.9375rem;
}
.sign-success-modal .success-content .success-icon uni-image[data-v-72e91ee0] {
  width: 3.75rem;
  height: 3.75rem;
}
.sign-success-modal .success-content .success-text[data-v-72e91ee0] {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.625rem;
}
.sign-success-modal .success-content .success-reward[data-v-72e91ee0] {
  font-size: 0.875rem;
  color: #FF6B6B;
  margin-bottom: 1.25rem;
}
.sign-success-modal .success-content .success-btn[data-v-72e91ee0] {
  background-color: #FFB347;
  color: #FFF;
  font-size: 0.875rem;
  font-weight: bold;
  padding: 0.625rem 1.875rem;
  border-radius: 1.25rem;
  border: none;
}