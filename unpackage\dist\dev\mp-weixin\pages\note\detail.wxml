<view class="container"><view class="nav-bar bfw" style="{{'padding-top:' + e}}"><view class="bar-box df" style="{{'height:' + d + ';' + ('width:' + '100%')}}"><view class="bar-back df" bindtap="{{b}}"><image src="{{a}}" style="width:34rpx;height:34rpx"></image></view><view class="bar-title ohto">纸条详情</view><view class="nav-action df" bindtap="{{c}}"><text>⋯</text></view></view></view><view class="content-box" style="{{'margin-top:' + F}}"><view wx:if="{{f}}" class="loading-container"><view class="loading-indicator"></view></view><view wx:elif="{{g}}" class="content"><view class="box-card"><view class="user-info"><view class="avatar"><image src="{{h}}" class="avatar-img"></image></view><view class="user-details"><view class="username">{{i}}</view><view class="user-meta"><text class="gender-icon">{{j}}</text><text class="age">{{k}} · {{l}}</text></view></view><view class="{{['type-tag', o]}}"><text class="type-icon">{{m}}</text><text class="type-text">{{n}}</text></view></view><view class="box-content"><view wx:if="{{p}}" class="text-content"><text class="content-text">{{q}}</text></view><view wx:else class="voice-content"><view class="voice-player" bindtap="{{t}}"><text class="voice-icon">{{r}}</text><text class="voice-duration">{{s}}s</text></view></view></view><view class="time-info"><text class="time-text">{{v}}</text></view></view><view class="response-section"><view class="section-title"><text>回应 ({{w}})</text></view><view wx:if="{{x}}" class="privacy-notice"><text class="notice-icon">🔒</text><text class="notice-text">为保护用户隐私，除了您自己的回应外，其他回应者信息将显示为匿名</text></view><view class="response-list"><view wx:for="{{y}}" wx:for-item="item" wx:key="f" class="response-item"><view class="response-user"><image src="{{item.a}}" class="response-avatar"></image><text class="response-username">{{item.b}}</text><text class="response-time">{{item.c}}</text><text wx:if="{{item.d}}" class="my-response-tag">我的回应</text></view><view class="response-content"><text>{{item.e}}</text></view></view></view><view class="response-input"><block wx:if="{{r0}}"><textarea class="input-area" placeholder="写下你的回应..." maxlength="200" value="{{z}}" bindinput="{{A}}"/></block><view class="input-actions"><text class="char-count">{{B}}/200</text><view class="{{['send-btn', C && 'active']}}" bindtap="{{D}}"><text>发送</text></view></view></view></view></view><view wx:else class="error-state"><text class="error-icon">😕</text><text class="error-text">纸条不存在或已被删除</text><view class="error-btn" bindtap="{{E}}"><text>返回</text></view></view></view><uni-popup wx:if="{{I}}" class="r" u-s="{{['d']}}" u-r="tipsPopup" u-i="7c67bb18-0" bind:__l="__l" u-p="{{I}}"><view class="tips-box df"><view class="tips-item bfh">{{G}}</view></view></uni-popup></view>