{"version": 3, "file": "SendVerifyCode.js", "sources": ["mixins/SendVerifyCode.js"], "sourcesContent": ["// +----------------------------------------------------------------------\r\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\r\n// +----------------------------------------------------------------------\r\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\r\n// +----------------------------------------------------------------------\r\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\r\n// +----------------------------------------------------------------------\r\n// | Author: CRMEB Team <<EMAIL>>\r\n// +----------------------------------------------------------------------\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tdisabled: false,\r\n\t\t\ttext: this.$t('验证码'),\r\n\t\t\trunTime: undefined,\r\n\t\t\tcaptchaType: 'clickWord'\r\n\t\t};\r\n\t},\r\n\tmethods: {\r\n\t\tsendCode() {\r\n\t\t\tif (this.disabled) return;\r\n\t\t\tthis.disabled = true;\r\n\t\t\tlet n = 60;\r\n\t\t\tthis.text = this.$t('剩余') + n + \"s\";\r\n\t\t\tthis.runTime = setInterval(() => {\r\n\t\t\t\tn = n - 1;\r\n\t\t\t\tif (n < 0) {\r\n\t\t\t\t\tclearInterval(this.runTime);\r\n\t\t\t\t\tthis.disabled = false;\r\n\t\t\t\t\tthis.text = this.$t('重新获取');\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.text = this.$t('剩余') + n + \"s\";\r\n\t\t\t}, 1000);\r\n\t\t}\r\n\t},\r\n\tonHide() {\r\n\t\tclearInterval(this.runTime);\r\n\t}\r\n};\n"], "names": [], "mappings": ";AAUA,MAAe,iBAAA;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU;AAAA,MACV,MAAM,KAAK,GAAG,KAAK;AAAA,MACnB,SAAS;AAAA,MACT,aAAa;AAAA,IAChB;AAAA,EACE;AAAA,EACD,SAAS;AAAA,IACR,WAAW;AACV,UAAI,KAAK;AAAU;AACnB,WAAK,WAAW;AAChB,UAAI,IAAI;AACR,WAAK,OAAO,KAAK,GAAG,IAAI,IAAI,IAAI;AAChC,WAAK,UAAU,YAAY,MAAM;AAChC,YAAI,IAAI;AACR,YAAI,IAAI,GAAG;AACV,wBAAc,KAAK,OAAO;AAC1B,eAAK,WAAW;AAChB,eAAK,OAAO,KAAK,GAAG,MAAM;AAC1B;AAAA,QACA;AACD,aAAK,OAAO,KAAK,GAAG,IAAI,IAAI,IAAI;AAAA,MAChC,GAAE,GAAI;AAAA,IACP;AAAA,EACD;AAAA,EACD,SAAS;AACR,kBAAc,KAAK,OAAO;AAAA,EAC1B;AACF;;"}