{"version": 3, "file": "mp-html.js", "sources": ["uni_modules/mp-html/components/mp-html/mp-html.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvdW5pX21vZHVsZXMvbXAtaHRtbC9jb21wb25lbnRzL21wLWh0bWwvbXAtaHRtbC52dWU"], "sourcesContent": ["<template>\r\n  <view id=\"_root\" :class=\"(selectable?'_select ':'')+'_root'\" :style=\"containerStyle\">\r\n    <slot v-if=\"!nodes[0]\" />\r\n    <!-- #ifndef APP-PLUS-NVUE -->\r\n    <node v-else :childs=\"nodes\" :opts=\"[lazyLoad,loadingImg,errorImg,showImgMenu,selectable]\" name=\"span\" />\r\n    <!-- #endif -->\r\n    <!-- #ifdef APP-PLUS-NVUE -->\r\n    <web-view ref=\"web\" src=\"/uni_modules/mp-html/static/app-plus/mp-html/local.html\" :style=\"'margin-top:-2px;height:' + height + 'px'\" @onPostMessage=\"_onMessage\" />\r\n    <!-- #endif -->\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * mp-html v2.5.1\r\n * @description 富文本组件\r\n * @tutorial https://github.com/jin-yufeng/mp-html\r\n * @property {String} container-style 容器的样式\r\n * @property {String} content 用于渲染的 html 字符串\r\n * @property {Boolean} copy-link 是否允许外部链接被点击时自动复制\r\n * @property {String} domain 主域名，用于拼接链接\r\n * @property {String} error-img 图片出错时的占位图链接\r\n * @property {Boolean} lazy-load 是否开启图片懒加载\r\n * @property {string} loading-img 图片加载过程中的占位图链接\r\n * @property {Boolean} pause-video 是否在播放一个视频时自动暂停其他视频\r\n * @property {Boolean} preview-img 是否允许图片被点击时自动预览\r\n * @property {Boolean} scroll-table 是否给每个表格添加一个滚动层使其能单独横向滚动\r\n * @property {Boolean | String} selectable 是否开启长按复制\r\n * @property {Boolean} set-title 是否将 title 标签的内容设置到页面标题\r\n * @property {Boolean} show-img-menu 是否允许图片被长按时显示菜单\r\n * @property {Object} tag-style 标签的默认样式\r\n * @property {Boolean | Number} use-anchor 是否使用锚点链接\r\n * @event {Function} load dom 结构加载完毕时触发\r\n * @event {Function} ready 所有图片加载完毕时触发\r\n * @event {Function} imgtap 图片被点击时触发\r\n * @event {Function} linktap 链接被点击时触发\r\n * @event {Function} play 音视频播放时触发\r\n * @event {Function} error 媒体加载出错时触发\r\n */\r\n// #ifndef APP-PLUS-NVUE\r\nimport node from './node/node'\r\n// #endif\r\nimport Parser from './parser'\r\nconst plugins=[]\r\n// #ifdef APP-PLUS-NVUE\r\nconst dom = weex.requireModule('dom')\r\n// #endif\r\nexport default {\r\n  name: 'mp-html',\r\n  data () {\r\n    return {\r\n      nodes: [],\r\n      // #ifdef APP-PLUS-NVUE\r\n      height: 3\r\n      // #endif\r\n    }\r\n  },\r\n  props: {\r\n    containerStyle: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    content: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    copyLink: {\r\n      type: [Boolean, String],\r\n      default: true\r\n    },\r\n    domain: String,\r\n    errorImg: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    lazyLoad: {\r\n      type: [Boolean, String],\r\n      default: false\r\n    },\r\n    loadingImg: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    pauseVideo: {\r\n      type: [Boolean, String],\r\n      default: true\r\n    },\r\n    previewImg: {\r\n      type: [Boolean, String],\r\n      default: true\r\n    },\r\n    scrollTable: [Boolean, String],\r\n    selectable: [Boolean, String],\r\n    setTitle: {\r\n      type: [Boolean, String],\r\n      default: true\r\n    },\r\n    showImgMenu: {\r\n      type: [Boolean, String],\r\n      default: true\r\n    },\r\n    tagStyle: Object,\r\n    useAnchor: [Boolean, Number]\r\n  },\r\n  // #ifdef VUE3\r\n  emits: ['load', 'ready', 'imgtap', 'linktap', 'play', 'error'],\r\n  // #endif\r\n  // #ifndef APP-PLUS-NVUE\r\n  components: {\r\n    node\r\n  },\r\n  // #endif\r\n  watch: {\r\n    content (content) {\r\n      this.setContent(content)\r\n    }\r\n  },\r\n  created () {\r\n    this.plugins = []\r\n    for (let i = plugins.length; i--;) {\r\n      this.plugins.push(new plugins[i](this))\r\n    }\r\n  },\r\n  mounted () {\r\n    if (this.content && !this.nodes.length) {\r\n      this.setContent(this.content)\r\n    }\r\n  },\r\n  beforeDestroy () {\r\n    this._hook('onDetached')\r\n  },\r\n  methods: {\r\n    /**\r\n     * @description 将锚点跳转的范围限定在一个 scroll-view 内\r\n     * @param {Object} page scroll-view 所在页面的示例\r\n     * @param {String} selector scroll-view 的选择器\r\n     * @param {String} scrollTop scroll-view scroll-top 属性绑定的变量名\r\n     */\r\n    in (page, selector, scrollTop) {\r\n      // #ifndef APP-PLUS-NVUE\r\n      if (page && selector && scrollTop) {\r\n        this._in = {\r\n          page,\r\n          selector,\r\n          scrollTop\r\n        }\r\n      }\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * @description 锚点跳转\r\n     * @param {String} id 要跳转的锚点 id\r\n     * @param {Number} offset 跳转位置的偏移量\r\n     * @returns {Promise}\r\n     */\r\n    navigateTo (id, offset) {\r\n      return new Promise((resolve, reject) => {\r\n        if (!this.useAnchor) {\r\n          reject(Error('Anchor is disabled'))\r\n          return\r\n        }\r\n        offset = offset || parseInt(this.useAnchor) || 0\r\n        // #ifdef APP-PLUS-NVUE\r\n        if (!id) {\r\n          dom.scrollToElement(this.$refs.web, {\r\n            offset\r\n          })\r\n          resolve()\r\n        } else {\r\n          this._navigateTo = {\r\n            resolve,\r\n            reject,\r\n            offset\r\n          }\r\n          this.$refs.web.evalJs('uni.postMessage({data:{action:\"getOffset\",offset:(document.getElementById(' + id + ')||{}).offsetTop}})')\r\n        }\r\n        // #endif\r\n        // #ifndef APP-PLUS-NVUE\r\n        let deep = ' '\r\n        // #ifdef MP-WEIXIN || MP-QQ || MP-TOUTIAO\r\n        deep = '>>>'\r\n        // #endif\r\n        const selector = uni.createSelectorQuery()\r\n          // #ifndef MP-ALIPAY\r\n          .in(this._in ? this._in.page : this)\r\n          // #endif\r\n          .select((this._in ? this._in.selector : '._root') + (id ? `${deep}#${id}` : '')).boundingClientRect()\r\n        if (this._in) {\r\n          selector.select(this._in.selector).scrollOffset()\r\n            .select(this._in.selector).boundingClientRect()\r\n        } else {\r\n          // 获取 scroll-view 的位置和滚动距离\r\n          selector.selectViewport().scrollOffset() // 获取窗口的滚动距离\r\n        }\r\n        selector.exec(res => {\r\n          if (!res[0]) {\r\n            reject(Error('Label not found'))\r\n            return\r\n          }\r\n          const scrollTop = res[1].scrollTop + res[0].top - (res[2] ? res[2].top : 0) + offset\r\n          if (this._in) {\r\n            // scroll-view 跳转\r\n            this._in.page[this._in.scrollTop] = scrollTop\r\n          } else {\r\n            // 页面跳转\r\n            uni.pageScrollTo({\r\n              scrollTop,\r\n              duration: 300\r\n            })\r\n          }\r\n          resolve()\r\n        })\r\n        // #endif\r\n      })\r\n    },\r\n\r\n    /**\r\n     * @description 获取文本内容\r\n     * @return {String}\r\n     */\r\n    getText (nodes) {\r\n      let text = '';\r\n      (function traversal (nodes) {\r\n        for (let i = 0; i < nodes.length; i++) {\r\n          const node = nodes[i]\r\n          if (node.type === 'text') {\r\n            text += node.text.replace(/&amp;/g, '&')\r\n          } else if (node.name === 'br') {\r\n            text += '\\n'\r\n          } else {\r\n            // 块级标签前后加换行\r\n            const isBlock = node.name === 'p' || node.name === 'div' || node.name === 'tr' || node.name === 'li' || (node.name[0] === 'h' && node.name[1] > '0' && node.name[1] < '7')\r\n            if (isBlock && text && text[text.length - 1] !== '\\n') {\r\n              text += '\\n'\r\n            }\r\n            // 递归获取子节点的文本\r\n            if (node.children) {\r\n              traversal(node.children)\r\n            }\r\n            if (isBlock && text[text.length - 1] !== '\\n') {\r\n              text += '\\n'\r\n            } else if (node.name === 'td' || node.name === 'th') {\r\n              text += '\\t'\r\n            }\r\n          }\r\n        }\r\n      })(nodes || this.nodes)\r\n      return text\r\n    },\r\n\r\n    /**\r\n     * @description 获取内容大小和位置\r\n     * @return {Promise}\r\n     */\r\n    getRect () {\r\n      return new Promise((resolve, reject) => {\r\n        uni.createSelectorQuery()\r\n          // #ifndef MP-ALIPAY\r\n          .in(this)\r\n          // #endif\r\n          .select('#_root').boundingClientRect().exec(res => res[0] ? resolve(res[0]) : reject(Error('Root label not found')))\r\n      })\r\n    },\r\n\r\n    /**\r\n     * @description 暂停播放媒体\r\n     */\r\n    pauseMedia () {\r\n      for (let i = (this._videos || []).length; i--;) {\r\n        this._videos[i].pause()\r\n      }\r\n      // #ifdef APP-PLUS\r\n      const command = 'for(var e=document.getElementsByTagName(\"video\"),i=e.length;i--;)e[i].pause()'\r\n      // #ifndef APP-PLUS-NVUE\r\n      let page = this.$parent\r\n      while (!page.$scope) page = page.$parent\r\n      page.$scope.$getAppWebview().evalJS(command)\r\n      // #endif\r\n      // #ifdef APP-PLUS-NVUE\r\n      this.$refs.web.evalJs(command)\r\n      // #endif\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * @description 设置媒体播放速率\r\n     * @param {Number} rate 播放速率\r\n     */\r\n    setPlaybackRate (rate) {\r\n      this.playbackRate = rate\r\n      for (let i = (this._videos || []).length; i--;) {\r\n        this._videos[i].playbackRate(rate)\r\n      }\r\n      // #ifdef APP-PLUS\r\n      const command = 'for(var e=document.getElementsByTagName(\"video\"),i=e.length;i--;)e[i].playbackRate=' + rate\r\n      // #ifndef APP-PLUS-NVUE\r\n      let page = this.$parent\r\n      while (!page.$scope) page = page.$parent\r\n      page.$scope.$getAppWebview().evalJS(command)\r\n      // #endif\r\n      // #ifdef APP-PLUS-NVUE\r\n      this.$refs.web.evalJs(command)\r\n      // #endif\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * @description 设置内容\r\n     * @param {String} content html 内容\r\n     * @param {Boolean} append 是否在尾部追加\r\n     */\r\n    setContent (content, append) {\r\n      if (!append || !this.imgList) {\r\n        this.imgList = []\r\n      }\r\n      const nodes = new Parser(this).parse(content)\r\n      // #ifdef APP-PLUS-NVUE\r\n      if (this._ready) {\r\n        this._set(nodes, append)\r\n      }\r\n      // #endif\r\n      this.$set(this, 'nodes', append ? (this.nodes || []).concat(nodes) : nodes)\r\n\r\n      // #ifndef APP-PLUS-NVUE\r\n      this._videos = []\r\n      this.$nextTick(() => {\r\n        this._hook('onLoad')\r\n        this.$emit('load')\r\n      })\r\n\r\n      if (this.lazyLoad || this.imgList._unloadimgs < this.imgList.length / 2) {\r\n        // 设置懒加载，每 350ms 获取高度，不变则认为加载完毕\r\n        let height = 0\r\n        const callback = rect => {\r\n          if (!rect || !rect.height) rect = {}\r\n          // 350ms 总高度无变化就触发 ready 事件\r\n          if (rect.height === height) {\r\n            this.$emit('ready', rect)\r\n          } else {\r\n            height = rect.height\r\n            setTimeout(() => {\r\n              this.getRect().then(callback).catch(callback)\r\n            }, 350)\r\n          }\r\n        }\r\n        this.getRect().then(callback).catch(callback)\r\n      } else {\r\n        // 未设置懒加载，等待所有图片加载完毕\r\n        if (!this.imgList._unloadimgs) {\r\n          this.getRect().then(rect => {\r\n            this.$emit('ready', rect)\r\n          }).catch(() => {\r\n            this.$emit('ready', {})\r\n          })\r\n        }\r\n      }\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * @description 调用插件钩子函数\r\n     */\r\n    _hook (name) {\r\n      for (let i = plugins.length; i--;) {\r\n        if (this.plugins[i][name]) {\r\n          this.plugins[i][name]()\r\n        }\r\n      }\r\n    },\r\n\r\n    // #ifdef APP-PLUS-NVUE\r\n    /**\r\n     * @description 设置内容\r\n     */\r\n    _set (nodes, append) {\r\n      this.$refs.web.evalJs('setContent(' + JSON.stringify(nodes).replace(/%22/g, '') + ',' + JSON.stringify([this.containerStyle.replace(/(?:margin|padding)[^;]+/g, ''), this.errorImg, this.loadingImg, this.pauseVideo, this.scrollTable, this.selectable]) + ',' + append + ')')\r\n    },\r\n\r\n    /**\r\n     * @description 接收到 web-view 消息\r\n     */\r\n    _onMessage (e) {\r\n      const message = e.detail.data[0]\r\n      switch (message.action) {\r\n        // web-view 初始化完毕\r\n        case 'onJSBridgeReady':\r\n          this._ready = true\r\n          if (this.nodes) {\r\n            this._set(this.nodes)\r\n          }\r\n          break\r\n        // 内容 dom 加载完毕\r\n        case 'onLoad':\r\n          this.height = message.height\r\n          this._hook('onLoad')\r\n          this.$emit('load')\r\n          break\r\n        // 所有图片加载完毕\r\n        case 'onReady':\r\n          this.getRect().then(res => {\r\n            this.$emit('ready', res)\r\n          }).catch(() => {\r\n            this.$emit('ready', {})\r\n          })\r\n          break\r\n        // 总高度发生变化\r\n        case 'onHeightChange':\r\n          this.height = message.height\r\n          break\r\n        // 图片点击\r\n        case 'onImgTap':\r\n          this.$emit('imgtap', message.attrs)\r\n          if (this.previewImg) {\r\n            uni.previewImage({\r\n              current: parseInt(message.attrs.i),\r\n              urls: this.imgList\r\n            })\r\n          }\r\n          break\r\n        // 链接点击\r\n        case 'onLinkTap': {\r\n          const href = message.attrs.href\r\n          this.$emit('linktap', message.attrs)\r\n          if (href) {\r\n            // 锚点跳转\r\n            if (href[0] === '#') {\r\n              if (this.useAnchor) {\r\n                dom.scrollToElement(this.$refs.web, {\r\n                  offset: message.offset\r\n                })\r\n              }\r\n            } else if (href.includes('://')) {\r\n              // 打开外链\r\n              if (this.copyLink) {\r\n                plus.runtime.openWeb(href)\r\n              }\r\n            } else {\r\n              uni.navigateTo({\r\n                url: href,\r\n                fail () {\r\n                  uni.switchTab({\r\n                    url: href\r\n                  })\r\n                }\r\n              })\r\n            }\r\n          }\r\n          break\r\n        }\r\n        case 'onPlay':\r\n          this.$emit('play')\r\n          break\r\n        // 获取到锚点的偏移量\r\n        case 'getOffset':\r\n          if (typeof message.offset === 'number') {\r\n            dom.scrollToElement(this.$refs.web, {\r\n              offset: message.offset + this._navigateTo.offset\r\n            })\r\n            this._navigateTo.resolve()\r\n          } else {\r\n            this._navigateTo.reject(Error('Label not found'))\r\n          }\r\n          break\r\n        // 点击\r\n        case 'onClick':\r\n          this.$emit('tap')\r\n          this.$emit('click')\r\n          break\r\n        // 出错\r\n        case 'onError':\r\n          this.$emit('error', {\r\n            source: message.source,\r\n            attrs: message.attrs\r\n          })\r\n      }\r\n    }\r\n    // #endif\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* #ifndef APP-PLUS-NVUE */\r\n/* 根节点样式 */\r\n._root {\r\n  padding: 1px 0;\r\n  overflow-x: auto;\r\n  overflow-y: hidden;\r\n  -webkit-overflow-scrolling: touch;\r\n}\r\n\r\n/* 长按复制 */\r\n._select {\r\n  user-select: text;\r\n}\r\n/* #endif */\r\n</style>\r\n", "import Component from 'D:/uniapp/vue3/uni_modules/mp-html/components/mp-html/mp-html.vue'\nwx.createComponent(Component)"], "names": ["uni", "nodes", "node", "<PERSON><PERSON><PERSON>"], "mappings": ";;;AAwCA,MAAK,OAAQ,MAAW;AAGxB,MAAM,UAAQ,CAAC;AAIf,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAQ;AACN,WAAO;AAAA,MACL,OAAO,CAAE;AAAA,IAIX;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACL,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,UAAU;AAAA,MACR,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACV;AAAA,IACD,QAAQ;AAAA,IACR,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,UAAU;AAAA,MACR,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACV;AAAA,IACD,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,YAAY;AAAA,MACV,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACV;AAAA,IACD,YAAY;AAAA,MACV,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACV;AAAA,IACD,aAAa,CAAC,SAAS,MAAM;AAAA,IAC7B,YAAY,CAAC,SAAS,MAAM;AAAA,IAC5B,UAAU;AAAA,MACR,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACV;AAAA,IACD,aAAa;AAAA,MACX,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACV;AAAA,IACD,UAAU;AAAA,IACV,WAAW,CAAC,SAAS,MAAM;AAAA,EAC5B;AAAA,EAED,OAAO,CAAC,QAAQ,SAAS,UAAU,WAAW,QAAQ,OAAO;AAAA,EAG7D,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EAED,OAAO;AAAA,IACL,QAAS,SAAS;AAChB,WAAK,WAAW,OAAO;AAAA,IACzB;AAAA,EACD;AAAA,EACD,UAAW;AACT,SAAK,UAAU,CAAC;AAChB,aAAS,IAAI,QAAQ,QAAQ,OAAM;AACjC,WAAK,QAAQ,KAAK,IAAI,QAAQ,CAAC,EAAE,IAAI,CAAC;AAAA,IACxC;AAAA,EACD;AAAA,EACD,UAAW;AACT,QAAI,KAAK,WAAW,CAAC,KAAK,MAAM,QAAQ;AACtC,WAAK,WAAW,KAAK,OAAO;AAAA,IAC9B;AAAA,EACD;AAAA,EACD,gBAAiB;AACf,SAAK,MAAM,YAAY;AAAA,EACxB;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOP,GAAI,MAAM,UAAU,WAAW;AAE7B,UAAI,QAAQ,YAAY,WAAW;AACjC,aAAK,MAAM;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQD,WAAY,IAAI,QAAQ;AACtB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAI,CAAC,KAAK,WAAW;AACnB,iBAAO,MAAM,oBAAoB,CAAC;AAClC;AAAA,QACF;AACA,iBAAS,UAAU,SAAS,KAAK,SAAS,KAAK;AAiB/C,YAAI,OAAO;AAEX,eAAO;AAEP,cAAM,WAAWA,cAAG,MAAC,oBAAoB,EAEtC,GAAG,KAAK,MAAM,KAAK,IAAI,OAAO,IAAI,EAElC,QAAQ,KAAK,MAAM,KAAK,IAAI,WAAW,aAAa,KAAK,GAAG,IAAI,IAAI,EAAE,KAAK,GAAG,EAAE,mBAAmB;AACtG,YAAI,KAAK,KAAK;AACZ,mBAAS,OAAO,KAAK,IAAI,QAAQ,EAAE,aAAa,EAC7C,OAAO,KAAK,IAAI,QAAQ,EAAE,mBAAmB;AAAA,eAC3C;AAEL,mBAAS,eAAgB,EAAC,aAAe;AAAA,QAC3C;AACA,iBAAS,KAAK,SAAO;AACnB,cAAI,CAAC,IAAI,CAAC,GAAG;AACX,mBAAO,MAAM,iBAAiB,CAAC;AAC/B;AAAA,UACF;AACA,gBAAM,YAAY,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,MAAM,KAAK;AAC9E,cAAI,KAAK,KAAK;AAEZ,iBAAK,IAAI,KAAK,KAAK,IAAI,SAAS,IAAI;AAAA,iBAC/B;AAELA,0BAAAA,MAAI,aAAa;AAAA,cACf;AAAA,cACA,UAAU;AAAA,aACX;AAAA,UACH;AACA,kBAAQ;AAAA,SACT;AAAA,OAEF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,QAAS,OAAO;AACd,UAAI,OAAO;AACX,OAAC,SAAS,UAAWC,QAAO;AAC1B,iBAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,gBAAMC,QAAOD,OAAM,CAAC;AACpB,cAAIC,MAAK,SAAS,QAAQ;AACxB,oBAAQA,MAAK,KAAK,QAAQ,UAAU,GAAG;AAAA,qBAC9BA,MAAK,SAAS,MAAM;AAC7B,oBAAQ;AAAA,iBACH;AAEL,kBAAM,UAAUA,MAAK,SAAS,OAAOA,MAAK,SAAS,SAASA,MAAK,SAAS,QAAQA,MAAK,SAAS,QAASA,MAAK,KAAK,CAAC,MAAM,OAAOA,MAAK,KAAK,CAAC,IAAI,OAAOA,MAAK,KAAK,CAAC,IAAI;AACtK,gBAAI,WAAW,QAAQ,KAAK,KAAK,SAAS,CAAC,MAAM,MAAM;AACrD,sBAAQ;AAAA,YACV;AAEA,gBAAIA,MAAK,UAAU;AACjB,wBAAUA,MAAK,QAAQ;AAAA,YACzB;AACA,gBAAI,WAAW,KAAK,KAAK,SAAS,CAAC,MAAM,MAAM;AAC7C,sBAAQ;AAAA,YACV,WAAWA,MAAK,SAAS,QAAQA,MAAK,SAAS,MAAM;AACnD,sBAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAAA,MACF,GAAG,SAAS,KAAK,KAAK;AACtB,aAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,UAAW;AACT,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCF,sBAAAA,MAAI,oBAAoB,EAErB,GAAG,IAAI,EAEP,OAAO,QAAQ,EAAE,mBAAkB,EAAG,KAAK,SAAO,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,CAAC,IAAI,OAAO,MAAM,sBAAsB,CAAC,CAAC;AAAA,OACtH;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAKD,aAAc;AACZ,eAAS,KAAK,KAAK,WAAW,IAAI,QAAQ,OAAM;AAC9C,aAAK,QAAQ,CAAC,EAAE,MAAM;AAAA,MACxB;AAAA,IAYD;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,gBAAiB,MAAM;AACrB,WAAK,eAAe;AACpB,eAAS,KAAK,KAAK,WAAW,IAAI,QAAQ,OAAM;AAC9C,aAAK,QAAQ,CAAC,EAAE,aAAa,IAAI;AAAA,MACnC;AAAA,IAYD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOD,WAAY,SAAS,QAAQ;AAC3B,UAAI,CAAC,UAAU,CAAC,KAAK,SAAS;AAC5B,aAAK,UAAU,CAAC;AAAA,MAClB;AACA,YAAM,QAAQ,IAAIG,4CAAM,OAAC,IAAI,EAAE,MAAM,OAAO;AAM5C,WAAK,KAAK,MAAM,SAAS,UAAU,KAAK,SAAS,CAAA,GAAI,OAAO,KAAK,IAAI,KAAK;AAG1E,WAAK,UAAU,CAAC;AAChB,WAAK,UAAU,MAAM;AACnB,aAAK,MAAM,QAAQ;AACnB,aAAK,MAAM,MAAM;AAAA,OAClB;AAED,UAAI,KAAK,YAAY,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS,GAAG;AAEvE,YAAI,SAAS;AACb,cAAM,WAAW,UAAQ;AACvB,cAAI,CAAC,QAAQ,CAAC,KAAK;AAAQ,mBAAO,CAAC;AAEnC,cAAI,KAAK,WAAW,QAAQ;AAC1B,iBAAK,MAAM,SAAS,IAAI;AAAA,iBACnB;AACL,qBAAS,KAAK;AACd,uBAAW,MAAM;AACf,mBAAK,QAAO,EAAG,KAAK,QAAQ,EAAE,MAAM,QAAQ;AAAA,YAC7C,GAAE,GAAG;AAAA,UACR;AAAA,QACF;AACA,aAAK,QAAO,EAAG,KAAK,QAAQ,EAAE,MAAM,QAAQ;AAAA,aACvC;AAEL,YAAI,CAAC,KAAK,QAAQ,aAAa;AAC7B,eAAK,QAAO,EAAG,KAAK,UAAQ;AAC1B,iBAAK,MAAM,SAAS,IAAI;AAAA,WACzB,EAAE,MAAM,MAAM;AACb,iBAAK,MAAM,SAAS,EAAE;AAAA,WACvB;AAAA,QACH;AAAA,MACF;AAAA,IAED;AAAA;AAAA;AAAA;AAAA,IAKD,MAAO,MAAM;AACX,eAAS,IAAI,QAAQ,QAAQ,OAAM;AACjC,YAAI,KAAK,QAAQ,CAAC,EAAE,IAAI,GAAG;AACzB,eAAK,QAAQ,CAAC,EAAE,IAAI,EAAE;AAAA,QACxB;AAAA,MACF;AAAA,IACD;AAAA,EA6GH;AACF;;;;;;;;;;;;;;;;;;;;AC9dA,GAAG,gBAAgB,SAAS;"}