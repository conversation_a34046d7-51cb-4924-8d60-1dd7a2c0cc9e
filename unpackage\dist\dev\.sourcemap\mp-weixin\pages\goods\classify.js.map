{"version": 3, "file": "classify.js", "sources": ["pages/goods/classify.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZ29vZHMvY2xhc3NpZnkudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 导航栏 -->\r\n    <view class=\"nav-box\" :style=\"{'padding-top': statusBarHeight + 'px', 'background': 'rgba(255, 255, 255,' + navbarTrans + ')'}\">\r\n      <view class=\"bg-mk\"></view>\r\n      <view class=\"bg-mk2\"></view>\r\n      <view class=\"bg-img\">\r\n        <lazy-image v-if=\"classifyList[classifyIdx].img\" :src=\"classifyList[classifyIdx].img\" />\r\n        <lazy-image v-else :src=\"'/static/img/inset/map.jpg'\" />\r\n      </view>\r\n      <view class=\"nav-back df\" :style=\"{'height': titleBarHeight + 'px'}\" @tap=\"navBack\">\r\n        <image src=\"/static/img/back.png\" style=\"width:34rpx;height:34rpx\"></image>\r\n      </view>\r\n      \r\n      <!-- 一级分类滚动区域 -->\r\n      <scroll-view scroll-x=\"true\" class=\"classify-scroll\">\r\n        <view class=\"one-box df\">\r\n          <view \r\n            v-for=\"(item, index) in classifyList\" \r\n            :key=\"index\" \r\n            :class=\"['one-item', 'df', index == classifyIdx ? 'active' : '']\" \r\n            :data-idx=\"index\" \r\n            :data-type=\"1\" \r\n            @tap=\"classifyClick\">\r\n            {{ item.name }}\r\n          </view>\r\n          <view style=\"flex-shrink:0;width:15rpx;height:15rpx\"></view>\r\n        </view>\r\n      </scroll-view>\r\n      \r\n      <!-- 二级分类滚动区域 -->\r\n      <scroll-view \r\n        scroll-x=\"true\" \r\n        class=\"classify-scroll\" \r\n        :style=\"{'height': classifyList[classifyIdx].children.length ? '68rpx' : '0px'}\">\r\n        <view class=\"two-box df\">\r\n          <view \r\n            v-for=\"(item, index) in classifyList[classifyIdx].children\" \r\n            :key=\"index\" \r\n            class=\"two-item\" \r\n            :style=\"{'color': index == classifyChildrenIdx ? '#000' : '#999'}\" \r\n            :data-idx=\"index\" \r\n            :data-type=\"2\" \r\n            @tap=\"classifyClick\">\r\n            {{ item.name }}\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <view \r\n      class=\"content-box\" \r\n      :style=\"{\r\n        'padding-top': classifyList[classifyIdx].children.length \r\n          ? 'calc(' + (statusBarHeight + titleBarHeight) + 'px + 168rpx)' \r\n          : 'calc(' + (statusBarHeight + titleBarHeight) + 'px + 100rpx)'\r\n      }\">\r\n      <!-- 加载中状态 -->\r\n      <view class=\"heio df\" :style=\"{'height': isThrottling || loadStatus == 'loading' ? '0px' : '60rpx'}\">\r\n        <uni-load-more v-if=\"true\" status=\"loading\"></uni-load-more>\r\n      </view>\r\n      \r\n      <!-- 商品列表 -->\r\n      <view class=\"goods-box\">\r\n        <block v-if=\"!isEmpty\">\r\n          <view \r\n            v-for=\"(item, index) in list\" \r\n            :key=\"index\" \r\n            class=\"goods-item\" \r\n            :data-url=\"'goods/details?id=' + item.id\" \r\n            @tap=\"navigateToFun\">\r\n            <view class=\"goods-img\">\r\n              <view class=\"goods-img-item\">\r\n                <lazy-image :src=\"item.imgs[0]\"></lazy-image>\r\n              </view>\r\n            </view>\r\n            <view class=\"goods-name ohto2\">{{ item.name }}</view>\r\n            <view class=\"goods-price\">\r\n              <money :price=\"item.product.price\"></money>\r\n              <view class=\"price-h\" style=\"text-decoration:line-through\">¥{{ item.product.line_price }}</view>\r\n              <view class=\"price-h\">{{ item.buy ? item.buy + '人已买' : item.cart + item.browse + '人想买' }}</view>\r\n            </view>\r\n            <view class=\"goods-tag df\">\r\n              <view \r\n                v-for=\"(tag, tagIndex) in item.tags\" \r\n                :key=\"tagIndex\" \r\n                class=\"tag-item\">\r\n                {{ tag }}\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </block>\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <view v-if=\"isEmpty\" class=\"empty-box df\">\r\n        <image src=\"/static/img/empty.png\" />\r\n        <view class=\"e1\">暂无推荐商品</view>\r\n        <view class=\"e2\">正在为您制造更多美好的商品</view>\r\n      </view>\r\n      \r\n      <!-- 加载更多 -->\r\n      <uni-load-more :status=\"loadStatus\"></uni-load-more>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request.js'\r\nimport * as api from '@/config/api.js'\r\nimport lazyImage from '@/components/lazyImage/lazyImage.vue'\r\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\r\nimport money from '@/components/money/money.vue'\r\n\r\nconst app = getApp();\r\n\r\nexport default {\r\n  components: {\r\n    lazyImage,\r\n    uniLoadMore,\r\n    money\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\r\n      isOrder: false,\r\n      navbarTrans: 0,\r\n      classifyList: [\r\n        {\r\n          id: 1,\r\n          name: \"推荐\",\r\n          img: \"\",\r\n          children: []\r\n        }\r\n      ],\r\n      classifyIdx: 0,\r\n      classifyChildrenIdx: -1,\r\n      isThrottling: false,\r\n      list: [],\r\n      page: 1,\r\n      isEmpty: false,\r\n      loadStatus: \"more\",\r\n      // Mock数据\r\n      mockData: {\r\n        // 分类数据\r\n        classifyList: [\r\n          {\r\n            id: 1,\r\n            name: \"推荐\",\r\n            img: \"/static/img/avatar.png\",\r\n            children: []\r\n          },\r\n          {\r\n            id: 2,\r\n            name: \"女装\",\r\n            img: \"/static/img/avatar.png\",\r\n            children: [\r\n              { id: 21, name: \"T恤\" },\r\n              { id: 22, name: \"裙装\" },\r\n              { id: 23, name: \"裤装\" },\r\n              { id: 24, name: \"套装\" },\r\n              { id: 25, name: \"外套\" }\r\n            ]\r\n          },\r\n          {\r\n            id: 3,\r\n            name: \"男装\",\r\n            img: \"/static/img/avatar.png\",\r\n            children: [\r\n              { id: 31, name: \"T恤\" },\r\n              { id: 32, name: \"裤装\" },\r\n              { id: 33, name: \"衬衫\" },\r\n              { id: 34, name: \"外套\" }\r\n            ]\r\n          },\r\n          {\r\n            id: 4,\r\n            name: \"鞋包\",\r\n            img: \"/static/img/avatar.png\",\r\n            children: [\r\n              { id: 41, name: \"女鞋\" },\r\n              { id: 42, name: \"男鞋\" },\r\n              { id: 43, name: \"双肩包\" },\r\n              { id: 44, name: \"手提包\" }\r\n            ]\r\n          },\r\n          {\r\n            id: 5,\r\n            name: \"配饰\",\r\n            img: \"/static/img/avatar.png\",\r\n            children: [\r\n              { id: 51, name: \"项链\" },\r\n              { id: 52, name: \"手链\" },\r\n              { id: 53, name: \"耳饰\" },\r\n              { id: 54, name: \"帽子\" }\r\n            ]\r\n          }\r\n        ],\r\n        // 商品列表数据\r\n        goodsList: {\r\n          // 推荐商品\r\n          1: [\r\n            {\r\n              id: 101,\r\n              name: \"夏季新款连衣裙气质淑女温柔风碎花裙\",\r\n              imgs: [\"/static/img/avatar.png\"],\r\n              product: {\r\n                price: \"169.00\",\r\n                line_price: \"269.00\"\r\n              },\r\n              tags: [\"新品\", \"包邮\"],\r\n              buy: 156,\r\n              cart: 20,\r\n              browse: 358\r\n            },\r\n            {\r\n              id: 102,\r\n              name: \"高腰阔腿牛仔裤女夏季薄款宽松直筒\",\r\n              imgs: [\"/static/img/avatar.png\"],\r\n              product: {\r\n                price: \"128.00\",\r\n                line_price: \"198.00\"\r\n              },\r\n              tags: [\"热卖\", \"包邮\"],\r\n              buy: 239,\r\n              cart: 45,\r\n              browse: 520\r\n            },\r\n            {\r\n              id: 103,\r\n              name: \"小香风短袖t恤女2023新款夏季宽松上衣\",\r\n              imgs: [\"/static/img/avatar.png\"],\r\n              product: {\r\n                price: \"79.00\",\r\n                line_price: \"129.00\"\r\n              },\r\n              tags: [\"折扣\", \"包邮\"],\r\n              buy: 315,\r\n              cart: 67,\r\n              browse: 782\r\n            },\r\n            {\r\n              id: 104,\r\n              name: \"轻奢品牌小白鞋女夏季新款透气板鞋\",\r\n              imgs: [\"/static/img/avatar.png\"],\r\n              product: {\r\n                price: \"299.00\",\r\n                line_price: \"459.00\"\r\n              },\r\n              tags: [\"限量\", \"包邮\"],\r\n              buy: 98,\r\n              cart: 32,\r\n              browse: 256\r\n            }\r\n          ],\r\n          // 女装T恤\r\n          21: [\r\n            {\r\n              id: 201,\r\n              name: \"纯棉短袖T恤女装夏季薄款宽松圆领打底衫\",\r\n              imgs: [\"/static/img/avatar.png\"],\r\n              product: {\r\n                price: \"69.00\",\r\n                line_price: \"99.00\"\r\n              },\r\n              tags: [\"新款\", \"纯棉\"],\r\n              buy: 234,\r\n              cart: 56,\r\n              browse: 467\r\n            },\r\n            {\r\n              id: 202,\r\n              name: \"设计感小众短袖T恤女装夏季新款韩版上衣\",\r\n              imgs: [\"/static/img/avatar.png\"],\r\n              product: {\r\n                price: \"89.00\",\r\n                line_price: \"139.00\"\r\n              },\r\n              tags: [\"ins风\", \"宽松\"],\r\n              buy: 178,\r\n              cart: 43,\r\n              browse: 385\r\n            }\r\n          ],\r\n          // 男装T恤\r\n          31: [\r\n            {\r\n              id: 301,\r\n              name: \"夏季薄款男士短袖T恤纯棉圆领体恤衫\",\r\n              imgs: [\"/static/img/avatar.png\"],\r\n              product: {\r\n                price: \"79.00\",\r\n                line_price: \"119.00\"\r\n              },\r\n              tags: [\"纯棉\", \"透气\"],\r\n              buy: 256,\r\n              cart: 48,\r\n              browse: 512\r\n            }\r\n          ]\r\n        }\r\n      }\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.getClassifyList()\r\n  },\r\n  methods: {\r\n    getClassifyList() {\r\n      let that = this\r\n      \r\n      // 检查API是否可用\r\n      if (api.default && api.default.api && api.default.api.classifyListUrl) {\r\n        request(api.default.api.classifyListUrl).then(function(res) {\r\n          if (res.code == 200) {\r\n            that.classifyList = res.data\r\n            that.goodsList()\r\n          }\r\n        })\r\n      } else {\r\n        // 使用mock数据\r\n        setTimeout(() => {\r\n          that.classifyList = that.mockData.classifyList\r\n          that.goodsList()\r\n        }, 500)\r\n      }\r\n    },\r\n    \r\n    goodsList() {\r\n      let that = this\r\n      that.isEmpty = false\r\n      \r\n      let classifyId = that.classifyList[that.classifyIdx].id\r\n      if (that.classifyChildrenIdx != -1) {\r\n        classifyId = that.classifyList[that.classifyIdx].children[that.classifyChildrenIdx].id\r\n      }\r\n      \r\n      // 检查API是否可用\r\n      if (api.default && api.default.api && api.default.api.goodsListUrl) {\r\n        request(api.default.api.goodsListUrl, {\r\n          page: that.page,\r\n          classify_type: that.classifyChildrenIdx != -1 ? 1 : 0,\r\n          classify_id: classifyId\r\n        }).then(function(res) {\r\n          that.isThrottling = true\r\n          that.loadStatus = \"more\"\r\n          \r\n          if (res.data.data.length > 0) {\r\n            if (that.page == 1) {\r\n              that.list = res.data.data\r\n            } else {\r\n              that.list = that.list.concat(res.data.data)\r\n            }\r\n            that.page = res.data.current_page\r\n          } else if (that.page == 1) {\r\n            that.isEmpty = true\r\n          } else {\r\n            that.loadStatus = \"no-more\"\r\n          }\r\n        })\r\n      } else {\r\n        // 使用mock数据\r\n        setTimeout(() => {\r\n          that.isThrottling = true\r\n          that.loadStatus = \"more\"\r\n          \r\n          // 尝试获取当前分类的商品\r\n          let mockGoods = that.mockData.goodsList[classifyId] || []\r\n          \r\n          // 模拟分页\r\n          const pageSize = 4\r\n          const startIndex = (that.page - 1) * pageSize\r\n          const endIndex = startIndex + pageSize\r\n          const pageData = mockGoods.slice(startIndex, endIndex)\r\n          \r\n          if (pageData.length > 0) {\r\n            if (that.page == 1) {\r\n              that.list = pageData\r\n            } else {\r\n              that.list = that.list.concat(pageData)\r\n            }\r\n          } else if (that.page == 1) {\r\n            that.isEmpty = true\r\n          } else {\r\n            that.loadStatus = \"no-more\"\r\n          }\r\n        }, 500)\r\n      }\r\n    },\r\n    \r\n    classifyClick(e) {\r\n      let dataset = e.currentTarget.dataset\r\n      \r\n      if (dataset.type == 1) {\r\n        this.classifyIdx = dataset.idx\r\n        this.classifyChildrenIdx = -1\r\n      }\r\n      \r\n      if (dataset.type == 2) {\r\n        this.classifyChildrenIdx = dataset.idx\r\n      }\r\n      \r\n      this.isThrottling = false\r\n      this.page = 1\r\n      this.goodsList()\r\n    },\r\n    \r\n    navigateToFun(e) {\r\n      uni.navigateTo({\r\n        url: \"/pages/\" + e.currentTarget.dataset.url\r\n      })\r\n    },\r\n    \r\n    navBack() {\r\n      if (getCurrentPages().length > 1) {\r\n        uni.navigateBack()\r\n      } else {\r\n        uni.switchTab({\r\n          url: \"/pages/tabbar/shop\"\r\n        })\r\n      }\r\n    }\r\n  },\r\n  onPageScroll(e) {\r\n    var opacity = (e.scrollTop > 150 ? 150 : e.scrollTop) / 150\r\n    this.navbarTrans = opacity\r\n  },\r\n  onReachBottom() {\r\n    if (this.list.length) {\r\n      this.loadStatus = \"loading\"\r\n      this.page = this.page + 1\r\n      this.goodsList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\npage {\r\n  background: #f8f8f8;\r\n}\r\n\r\n.nav-box {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 99;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.nav-box .nav-back {\r\n  padding: 0 30rpx;\r\n}\r\n\r\n.bg-mk, .bg-mk2, .bg-img {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  top: 0;\r\n  left: 0;\r\n}\r\n\r\n.bg-mk {\r\n  z-index: -1;\r\n  background: linear-gradient(to left, rgba(255, 255, 255, .3), #fff);\r\n}\r\n\r\n.bg-mk2 {\r\n  z-index: -1;\r\n  background: linear-gradient(to bottom, rgba(255, 255, 255, .3), #fff, #fff);\r\n}\r\n\r\n.bg-img {\r\n  z-index: -2;\r\n}\r\n\r\n.nav-box .classify-scroll {\r\n  width: 100%;\r\n  white-space: nowrap;\r\n  transition: all .3s ease-in-out;\r\n  overflow: hidden;\r\n}\r\n\r\n.classify-scroll .one-box {\r\n  width: calc(100% - 30rpx);\r\n  padding: 0 15rpx;\r\n  height: 100rpx;\r\n}\r\n\r\n.one-box .one-item {\r\n  flex-shrink: 0;\r\n  margin: 0 15rpx;\r\n  padding: 0 30rpx;\r\n  height: 68rpx;\r\n  font-size: 20rpx;\r\n  font-weight: 700;\r\n  background: #f8f8f8;\r\n  border-radius: 68rpx;\r\n  justify-content: center;\r\n}\r\n\r\n.one-box .active {\r\n  color: #fff;\r\n  background: #000;\r\n}\r\n\r\n.classify-scroll .two-box {\r\n  width: 100%;\r\n  height: 68rpx;\r\n}\r\n\r\n.two-box .two-item {\r\n  flex-shrink: 0;\r\n  padding: 0 30rpx;\r\n  height: 68rpx;\r\n  line-height: 68rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 700;\r\n  transition: color .3s ease-in-out;\r\n}\r\n\r\n.content-box {\r\n  width: 100%;\r\n  transition: all .3s ease-in-out;\r\n}\r\n\r\n.goods-box {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.goods-box .goods-item {\r\n  width: calc(50% - 15rpx);\r\n  margin: 10rpx 0 0 10rpx;\r\n  background: #fff;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.goods-item .goods-img {\r\n  width: 100%;\r\n  padding-top: 100%;\r\n  position: relative;\r\n}\r\n\r\n.goods-img .goods-img-item {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.goods-item .goods-name {\r\n  width: calc(100% - 40rpx);\r\n  margin: 15rpx 20rpx;\r\n  font-size: 26rpx;\r\n  line-height: 36rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.goods-item .goods-price {\r\n  width: calc(100% - 30rpx);\r\n  margin: 0 20rpx 20rpx;\r\n  display: flex;\r\n  align-items: flex-end;\r\n}\r\n\r\n.goods-price .price-h {\r\n  margin-left: 15rpx;\r\n  color: #999;\r\n  font-size: 20rpx;\r\n  line-height: 20rpx;\r\n}\r\n\r\n.goods-item .goods-tag {\r\n  width: calc(100% - 30rpx);\r\n  margin: 0 15rpx 15rpx;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.goods-tag .tag-item {\r\n  margin: 0 5rpx 5rpx;\r\n  height: 40rpx;\r\n  padding: 0 12rpx;\r\n  line-height: 40rpx;\r\n  font-size: 18rpx;\r\n  font-weight: 500;\r\n  background: #f8f8f8;\r\n  border-radius: 8rpx;\r\n}\r\n\r\n/* 空状态样式 */\r\n.empty-box {\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n\r\n.empty-box image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.empty-box .e1 {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.empty-box .e2 {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 辅助类 */\r\n.df {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ohto2 {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n.heio {\r\n  width: 100%;\r\n  justify-content: center;\r\n  transition: all .3s ease-in-out;\r\n  overflow: hidden;\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/uniapp/vue3/pages/goods/classify.vue'\nwx.createPage(MiniProgramPage)"], "names": ["api.default", "request", "uni"], "mappings": ";;;;;AA+GA,MAAA,YAAA,MAAA;AACA,MAAA,cAAA,MAAA;AACA,MAAA,QAAA,MAAA;AAEA,OAAA;AAEA,MAAA,YAAA;AAAA;IAEI;AAAA;;;EAIF,OAAA;AACE,WAAA;AAAA;;;;;QAMI;AAAA;UAEE,MAAA;AAAA,UACA,KAAA;AAAA;QAEF;AAAA;;;MAIF,cAAA;AAAA,MACA,MAAA,CAAA;AAAA,MACA,MAAA;AAAA;MAEA,YAAA;AAAA;AAAA,MAEA,UAAA;AAAA;AAAA;UAGI;AAAA;YAEE,MAAA;AAAA;;;UAIF;AAAA;YAEE,MAAA;AAAA;YAEA,UAAA;AAAA,cACE,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,cACA,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,cACA,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,cACA,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,cACA,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,YACF;AAAA;UAEF;AAAA;YAEE,MAAA;AAAA;YAEA,UAAA;AAAA,cACE,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,cACA,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,cACA,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,cACA,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,YACF;AAAA;UAEF;AAAA;YAEE,MAAA;AAAA;YAEA,UAAA;AAAA,cACE,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,cACA,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,cACA,EAAA,IAAA,IAAA,MAAA,MAAA;AAAA,cACA,EAAA,IAAA,IAAA,MAAA,MAAA;AAAA,YACF;AAAA;UAEF;AAAA;YAEE,MAAA;AAAA;YAEA,UAAA;AAAA,cACE,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,cACA,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,cACA,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,cACA,EAAA,IAAA,IAAA,MAAA,KAAA;AAAA,YACF;AAAA,UACF;AAAA;;;;UAKA,GAAA;AAAA,YACE;AAAA,cACE,IAAA;AAAA;cAEA,MAAA,CAAA,wBAAA;AAAA,cACA,SAAA;AAAA,gBACE,OAAA;AAAA,gBACA,YAAA;AAAA;cAEF,MAAA,CAAA,MAAA,IAAA;AAAA,cACA,KAAA;AAAA,cACA,MAAA;AAAA,cACA,QAAA;AAAA;YAEF;AAAA,cACE,IAAA;AAAA;cAEA,MAAA,CAAA,wBAAA;AAAA,cACA,SAAA;AAAA,gBACE,OAAA;AAAA,gBACA,YAAA;AAAA;cAEF,MAAA,CAAA,MAAA,IAAA;AAAA,cACA,KAAA;AAAA,cACA,MAAA;AAAA,cACA,QAAA;AAAA;YAEF;AAAA,cACE,IAAA;AAAA;cAEA,MAAA,CAAA,wBAAA;AAAA,cACA,SAAA;AAAA;gBAEE,YAAA;AAAA;cAEF,MAAA,CAAA,MAAA,IAAA;AAAA,cACA,KAAA;AAAA,cACA,MAAA;AAAA,cACA,QAAA;AAAA;YAEF;AAAA,cACE,IAAA;AAAA;cAEA,MAAA,CAAA,wBAAA;AAAA,cACA,SAAA;AAAA,gBACE,OAAA;AAAA,gBACA,YAAA;AAAA;cAEF,MAAA,CAAA,MAAA,IAAA;AAAA,cACA,KAAA;AAAA,cACA,MAAA;AAAA,cACA,QAAA;AAAA,YACF;AAAA;;UAGF,IAAA;AAAA,YACE;AAAA,cACE,IAAA;AAAA;cAEA,MAAA,CAAA,wBAAA;AAAA,cACA,SAAA;AAAA;gBAEE,YAAA;AAAA;cAEF,MAAA,CAAA,MAAA,IAAA;AAAA,cACA,KAAA;AAAA,cACA,MAAA;AAAA,cACA,QAAA;AAAA;YAEF;AAAA,cACE,IAAA;AAAA;cAEA,MAAA,CAAA,wBAAA;AAAA,cACA,SAAA;AAAA;gBAEE,YAAA;AAAA;cAEF,MAAA,CAAA,QAAA,IAAA;AAAA,cACA,KAAA;AAAA,cACA,MAAA;AAAA,cACA,QAAA;AAAA,YACF;AAAA;;UAGF,IAAA;AAAA,YACE;AAAA,cACE,IAAA;AAAA;cAEA,MAAA,CAAA,wBAAA;AAAA,cACA,SAAA;AAAA;gBAEE,YAAA;AAAA;cAEF,MAAA,CAAA,MAAA,IAAA;AAAA,cACA,KAAA;AAAA,cACA,MAAA;AAAA,cACA,QAAA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;EAEF,SAAA;AACE,SAAA,gBAAA;AAAA;EAEF,SAAA;AAAA,IACE,kBAAA;;AAIE,UAAAA,WAAAA,SAAAA,WAAAA,MAAA,OAAAA,WAAAA,MAAA,IAAA,iBAAA;;AAEI,cAAA,IAAA,QAAA,KAAA;;AAEE,iBAAA,UAAA;AAAA,UACF;AAAA;;AAIF,mBAAA,MAAA;AACE,eAAA,eAAA,KAAA,SAAA;AACA,eAAA,UAAA;AAAA;MAEJ;AAAA;;;AAKA,WAAA,UAAA;;AAGA,UAAA,KAAA,uBAAA,IAAA;AACE,qBAAA,KAAA,aAAA,KAAA,WAAA,EAAA,SAAA,KAAA,mBAAA,EAAA;AAAA,MACF;AAGA,UAAAA,WAAAA,SAAAA,WAAAA,MAAA,OAAAA,WAAAA,MAAA,IAAA,cAAA;AACEC,8BAAAD,WAAAA,MAAA,IAAA,cAAA;AAAA,UACE,MAAA,KAAA;AAAA;UAEA,aAAA;AAAA,QACF,CAAA,EAAA,KAAA,SAAA,KAAA;AACE,eAAA,eAAA;AACA,eAAA,aAAA;;AAGE,gBAAA,KAAA,QAAA,GAAA;;;AAGE,mBAAA,OAAA,KAAA,KAAA,OAAA,IAAA,KAAA,IAAA;AAAA,YACF;AACA,iBAAA,OAAA,IAAA,KAAA;AAAA,UACF,WAAA,KAAA,QAAA,GAAA;AACE,iBAAA,UAAA;AAAA;;UAGF;AAAA;;AAIF,mBAAA,MAAA;AACE,eAAA,eAAA;AACA,eAAA,aAAA;;AAMA,gBAAA,WAAA;AACA,gBAAA,cAAA,KAAA,OAAA,KAAA;AACA,gBAAA,WAAA,aAAA;;AAGA,cAAA,SAAA,SAAA,GAAA;AACE,gBAAA,KAAA,QAAA,GAAA;AACE,mBAAA,OAAA;AAAA;AAEA,mBAAA,OAAA,KAAA,KAAA,OAAA,QAAA;AAAA,YACF;AAAA,UACF,WAAA,KAAA,QAAA,GAAA;AACE,iBAAA,UAAA;AAAA;;UAGF;AAAA;MAEJ;AAAA;IAGF,cAAA,GAAA;AACE,UAAA,UAAA,EAAA,cAAA;AAEA,UAAA,QAAA,QAAA,GAAA;;;MAGA;AAEA,UAAA,QAAA,QAAA,GAAA;AACE,aAAA,sBAAA,QAAA;AAAA,MACF;;;AAIA,WAAA,UAAA;AAAA;IAGF,cAAA,GAAA;AACEE,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,YAAA,EAAA,cAAA,QAAA;AAAA;;IAIJ,UAAA;AACE,UAAA,gBAAA,EAAA,SAAA,GAAA;AACEA,sBAAAA,MAAA,aAAA;AAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA;;MAGF;AAAA,IACF;AAAA;EAEF,aAAA,GAAA;;;;EAIA,gBAAA;AACE,QAAA,KAAA,KAAA,QAAA;;AAEE,WAAA,OAAA,KAAA,OAAA;AACA,WAAA,UAAA;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnbA,GAAG,WAAW,eAAe;"}