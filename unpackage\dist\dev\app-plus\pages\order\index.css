
.uni-load-more[data-v-9245e42c] {
  display: flex;
  flex-direction: row;
  height: 1.875rem;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-9245e42c] {
  font-size: 0.75rem;
  margin-left: 0.5rem;
}
.uni-load-more__img[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
}
.uni-load-more__img--nvue[data-v-9245e42c] {
  color: #999;
}
.uni-load-more__img--android[data-v-9245e42c],
.uni-load-more__img--ios[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
}
.uni-load-more__img--android[data-v-9245e42c] {
  animation: loading-ios 1s 0s linear infinite;
}
.uni-load-more__img--ios-H5[data-v-9245e42c] {
  position: relative;
  animation: loading-ios-H5-9245e42c 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-9245e42c] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-9245e42c {
0% {
    transform: rotate(0);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
to {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-9245e42c] {
  position: relative;
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 0.125rem transparent;
  border-top: solid 0.125rem #999;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(1) {
  animation: loading-android-MP-1-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(2) {
  animation: loading-android-MP-2-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(3) {
  animation: loading-android-MP-3-9245e42c 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(90deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(180deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(270deg);
}
to {
    transform: rotate(360deg);
}
}


.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.nav-bar[data-v-eaf4c2e5] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.nav-bar .navbar-item[data-v-eaf4c2e5] {
  width: 100%;
  display: flex;
  align-items: center;
}
.navbar-item .back-box[data-v-eaf4c2e5] {
  padding: 0 0.9375rem;
  display: flex;
  align-items: center;
}
.navbar-item .back-box uni-image[data-v-eaf4c2e5] {
  width: 1.0625rem;
  height: 1.0625rem;
}
.bfw[data-v-eaf4c2e5] {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,.8);
}
.bf8[data-v-eaf4c2e5] {
  background: #f8f8f8;
}


.lazy-image[data-v-337fa078]{position:relative;width:100%;height:100%;overflow:hidden;background:#f8f8f8;}
.lazy-image uni-image[data-v-337fa078]{width:100%;height:100%;}
.lazy-image .err[data-v-337fa078]{position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(248,248,248,0.7) url('../../static/img/load.png') no-repeat center/50%;}


.money-box[data-v-433ff6d7] {
  display: flex;
  align-items: flex-end;
  font-weight: bolder;
}
.money-box .sale[data-v-433ff6d7] {
  margin: 0 0.0625rem;
}
.money-box .unit[data-v-433ff6d7] {
  margin-bottom: 0.0625rem;
}


/* 基础样式 - 参考 details.vue 的 empty-box 样式 */
.empty-page[data-v-5cea664a] {
  width: 100%;
  padding: 3.125rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.empty-content[data-v-5cea664a] {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

/* 图片样式 - 参考 details.vue */
.empty-image[data-v-5cea664a] {
  width: 9.375rem;
  height: 9.375rem;
  margin-bottom: 0.9375rem;
  opacity: 0.8;
}

/* 标题样式 - 参考 details.vue 的 .e1 */
.empty-title[data-v-5cea664a] {
  font-size: 0.9375rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.3125rem;
  line-height: 1.4;
}

/* 描述样式 - 参考 details.vue 的 .e2 */
.empty-description[data-v-5cea664a] {
  margin-top: 0.3125rem;
  color: #999;
  font-size: 0.8125rem;
  line-height: 1.5;
  margin-bottom: 1.25rem;
  max-width: 15.625rem;
  text-align: center;
}

/* 按钮样式 - 参考 details.vue 的 retry-btn */
.empty-button[data-v-5cea664a] {
  margin-top: 1.25rem;
  width: 6.25rem;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 700;
  color: #fff;
  background: #007aff;
  border-radius: 1.25rem;
  transition: all 0.3s ease;
}
.empty-button[data-v-5cea664a]:active {
  background: #0056cc;
  transform: scale(0.95);
}

/* 工具类 */
.df[data-v-5cea664a] {
  display: flex;
  align-items: center;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
.empty-title[data-v-5cea664a] {
    color: #fff;
}
.empty-description[data-v-5cea664a] {
    color: #ccc;
}
.empty-page[data-v-5cea664a] {
    background-color: #1a1a1a;
}
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.empty-image[data-v-5cea664a] {
    width: 7.8125rem;
    height: 7.8125rem;
}
.empty-title[data-v-5cea664a] {
    font-size: 0.875rem;
}
.empty-description[data-v-5cea664a] {
    font-size: 0.75rem;
    padding: 0 1.25rem;
}
}

/* 小程序兼容性 */






/* H5 兼容性 */








body {
  background: #f8f8f8;
}

/* 导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.bar-box .bar-back {
  padding: 0 0.9375rem;
  width: 1.0625rem;
  height: 100%;
}
.bar-box .bar-title {
  font-size: 1rem;
  font-weight: 700;
}
.nav-box {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 99;
  margin-top: -1px;
  width: 100%;
  height: 2.5rem;
  justify-content: space-between;
}
.nav-box .nav-item {
  width: 16.66%;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.nav-box .nav-item uni-text {
  font-weight: 700;
  transition: all .3s ease-in-out;
}
.nav-box .nav-line {
  position: absolute;
  bottom: 0.375rem;
  width: 0.5625rem;
  height: 0.1875rem;
  border-radius: 0.1875rem;
  background: #000;
  transition: opacity .3s ease-in-out;
}

/* 内容区 */
.content {
  width: 100%;
  flex-direction: column;
}
.list-box {
  margin-top: 0.9375rem;
  width: calc(100% - 3.125rem);
  padding: 0.9375rem 0.625rem;
  border-radius: 0.9375rem;
  background: #fff;
}
.list-box .list-top {
  width: 100%;
  justify-content: space-between;
}
.list-top uni-text {
  color: #999;
  font-size: 0.8125rem;
  font-weight: 700;
}
.list-info {
  width: 100%;
  padding: 0.9375rem 0;
  justify-content: space-between;
}
.list-info .list-info-img {
  display: flex;
  align-items: center;
  position: relative;
}
.list-info .list-info-img uni-image,
.list-info .mask-img {
  width: 4.0625rem;
  height: 4.0625rem;
  border-radius: 0.25rem;
}
.list-info .list-info-img uni-image {
  margin-right: 0.25rem;
  animation: fadeIn .45s ease;
}
.list-info .mask-img {
  position: absolute;
  top: 0;
  right: 0.25rem;
  line-height: 4.0625rem;
  text-align: center;
  font-size: 0.75rem;
  font-weight: 700;
  color: #fff;
  background: rgba(0, 0, 0, .3);
}
.list-info .list-info-pn {
  display: flex;
  align-items: flex-end;
  flex-direction: column;
}
.list-info-pn .num {
  margin-top: 0.3125rem;
  color: #999;
  font-size: 0.625rem;
  font-weight: 500;
}
.list-wuliu {
  margin-bottom: 0.9375rem;
  z-index: 1;
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  color: #999;
  font-size: 0.75rem;
  background: #f8f8f8;
  border-radius: 0.25rem;
}
.list-wuliu uni-text {
  margin: 0 0.3125rem;
  color: #000;
  font-weight: 700;
}
.list-box .list-btn {
  justify-content: flex-end;
}
.list-btn uni-view {
  margin-left: 0.625rem;
  padding: 0 0.9375rem;
  height: 1.875rem;
  border-radius: 0.9375rem;
}
.list-btn uni-view uni-image {
  width: 0.8125rem;
  height: 0.8125rem;
  margin-right: 0.3125rem;
}
.list-btn uni-view uni-text {
  font-size: 0.625rem;
  line-height: 0.625rem;
  font-weight: 700;
}
.list-btn .btn1 {
  color: #999;
  border: 0.0625rem solid #F5F5F5;
}
.list-btn .btn2 {
  color: #fff;
  background: #000;
  border: 0.0625rem solid #000;
}

/* 评价弹窗 */
.note-box {
  padding: 0.46875rem;
  background: #fff;
  border-radius: 0.9375rem;
}
.note-box .note-add {
  margin: 0.9375rem;
  width: 12.5rem;
  height: 2.8125rem;
  font-size: 0.75rem;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 1.40625rem;
  justify-content: center;
}
.note-box .note-add uni-image {
  margin-right: 0.3125rem;
  width: 1.25rem;
  height: 1.25rem;
}

/* 空状态 */
.empty-box {
  margin-top: 3.75rem;
  width: 100%;
  flex-direction: column;
}
.empty-box uni-image {
  width: 7.5rem;
  height: 7.5rem;
}
.empty-box .e1 {
  margin-top: 0.9375rem;
  font-size: 1rem;
  font-weight: bold;
}
.empty-box .e2 {
  margin-top: 0.375rem;
  font-size: 0.8125rem;
  color: #999;
}

/* 提示框 */
.tips-box {
  width: 100%;
  justify-content: center;
}
.tips-item {
  background: #000;
  color: #fff;
  padding: 0.625rem 1.25rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 700;
}

/* 加载动画 */
.heio {
  justify-content: center;
}

/* 通用 */
.df {
  display: flex;
  align-items: center;
}
.bf8 {
  background: #fff;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
@keyframes fadeIn {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
