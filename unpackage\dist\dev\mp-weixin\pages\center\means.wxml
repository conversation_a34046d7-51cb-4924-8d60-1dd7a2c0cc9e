<view><form bindsubmit="{{br}}"><view class="container"><view class="album-section"><view class="album-title">我的照片📸 ({{a}}/6)</view><view class="album-desc">封面头像需上传你的清晰无遮挡照片，否则无法为你推荐优质的朋友！ <text class="change-avatar" bindtap="{{b}}">更换头像</text></view><view class="photo-grid" id="photoGrid"><view class="photo-item cover-photo" bindtap="{{f}}"><block wx:if="{{c}}"><image class="photo-image" src="{{d}}" mode="aspectFill"></image><view class="photo-tag">头像</view></block><block wx:else><view class="photo-placeholder"><image class="photo-icon" src="{{e}}"></image><view class="photo-type">我的头像</view><view class="photo-boost">+10%</view></view></block></view><view class="photo-item" bindtap="{{j}}"><block wx:if="{{g}}"><image class="photo-image" src="{{h}}" mode="aspectFill"></image></block><block wx:else><view class="photo-placeholder"><image class="photo-icon" src="{{i}}"></image><view class="photo-type">有趣的生活照</view><view class="photo-boost">+5%</view></view></block></view><view class="photo-item" bindtap="{{n}}"><block wx:if="{{k}}"><image class="photo-image" src="{{l}}" mode="aspectFill"></image></block><block wx:else><view class="photo-placeholder"><image class="photo-icon" src="{{m}}"></image><view class="photo-type">好看的旅行照</view><view class="photo-boost">+5%</view></view></block></view><view class="photo-item" bindtap="{{r}}"><block wx:if="{{o}}"><image class="photo-image" src="{{p}}" mode="aspectFill"></image></block><block wx:else><view class="photo-placeholder"><image class="photo-icon" src="{{q}}"></image><view class="photo-type">独一无二的才艺</view><view class="photo-boost">+5%</view></view></block></view><view class="photo-item" bindtap="{{w}}"><block wx:if="{{s}}"><image class="photo-image" src="{{t}}" mode="aspectFill"></image></block><block wx:else><view class="photo-placeholder"><image class="photo-icon" src="{{v}}"></image><view class="photo-type">美好的回忆瞬间</view><view class="photo-boost">+5%</view></view></block></view><view class="photo-item" bindtap="{{A}}"><block wx:if="{{x}}"><image class="photo-image" src="{{y}}" mode="aspectFill"></image></block><block wx:else><view class="photo-placeholder"><image class="photo-icon" src="{{z}}"></image><view class="photo-type">最爱的美食/宠物</view><view class="photo-boost">+5%</view></view></block></view></view></view><view class="df sp"><view class="title-label w50">昵称</view><view class="title-label w50">性别</view></view><view class="df sp"><input type="nickname" name="nickname" class="input-box w50" bindblur="{{B}}" maxlength="16" placeholder="怎么称呼你" value="{{C}}" bindinput="{{D}}"/><view class="input-box w50 df" bindtap="{{I}}"><view style="{{'color:' + G}}"><block wx:if="{{E}}">男</block><block wx:elif="{{F}}">女</block><block wx:else>未知</block></view><image src="{{H}}" style="width:24rpx;height:24rpx"></image></view></view><view class="title-label">关于我</view><block wx:if="{{r0}}"><textarea bindblur="{{J}}" class="textarea-box" show-confirm-bar="{{false}}" cursor-spacing="30" maxlength="100" placeholder="添加个人简介，让大家认识你..." auto-height value="{{K}}" bindinput="{{L}}"></textarea></block><view class="title-label">我的标签</view><view class="input-box df" bindtap="{{Q}}"><view style="{{'color:' + O}}"><block wx:if="{{M}}">{{N}}</block><block wx:else>点击选择标签</block></view><image src="{{P}}" style="width:24rpx;height:24rpx"></image></view><view class="df sp"><view class="title-label w50">生日</view><view class="title-label w50">星座</view></view><view class="df sp"><view class="input-box w50 df" bindtap="{{U}}"><view style="{{'color:' + S}}">{{R}}</view><image src="{{T}}" style="width:24rpx;height:24rpx"></image></view><view class="input-box w50 df"><view style="{{'color:' + W}}">{{V}}</view></view></view><view class="df sp"><view class="title-label w50">学校</view><view class="title-label w50">家乡</view></view><view class="df sp"><input class="input-box w50" maxlength="30" placeholder="填写你的学校" value="{{X}}" bindinput="{{Y}}"/><input class="input-box w50" maxlength="30" placeholder="填写你的家乡" value="{{Z}}" bindinput="{{aa}}"/></view><view class="title-label">职业</view><input class="input-box" maxlength="30" placeholder="填写你的职业" value="{{ab}}" bindinput="{{ac}}"/><view class="df sp"><view class="title-label w50">身高(cm)</view><view class="title-label w50">体重(kg)</view></view><view class="df sp"><input class="input-box w50" type="digit" maxlength="5" placeholder="填写身高" value="{{ad}}" bindinput="{{ae}}"/><input class="input-box w50" type="digit" maxlength="5" placeholder="填写体重" value="{{af}}" bindinput="{{ag}}"/></view><view class="title-label">手机号</view><view class="input-box df"><view>{{ah}}</view><button class="input-btn df" open-type="getPhoneNumber" bindgetphonenumber="{{ak}}"><image src="{{ai}}"></image><text>{{aj}}</text></button></view><view class="title-label">实名认证</view><view class="input-box df"><view>{{al}}</view><view class="input-btn df" bindtap="{{ao}}"><image src="{{am}}"></image><text>{{an}}</text></view></view><view class="title-label">IP属地</view><view class="input-box df"><view>{{ap}}</view><view class="input-btn df" bindtap="{{ar}}"><image src="{{aq}}"></image><text>刷新</text></view></view><view class="input-tips"> IP属地说明：为维护网络安全、保障良好生态和社区的真实性，根据网络运营商数据，展示用户IP属地信息。 </view><view class="footer-box bfw bUp"><view class="footer-item df"><view class="btn bg2" bindtap="{{as}}">保存</view></view></view><uni-popup wx:if="{{aD}}" class="r" u-s="{{['d']}}" u-r="birthdayPopup" u-i="2062139e-0" bind:__l="__l" u-p="{{aD}}"><view class="popup-box"><view class="popup-top df"><view class="popup-title"><view class="t1">选择生日</view><view class="t2">请选择您的出生日期</view></view><view class="popup-close df" bindtap="{{av}}"><image src="{{at}}" style="width:20rpx;height:20rpx"></image></view></view><block wx:if="{{r0}}"><picker-view class="birthday-picker" indicator-style="{{'height: 50px;'}}" value="{{az}}" bindchange="{{aA}}"><picker-view-column><view wx:for="{{aw}}" wx:for-item="item" wx:key="b" class="picker-item">{{item.a}}年</view></picker-view-column><picker-view-column><view wx:for="{{ax}}" wx:for-item="item" wx:key="b" class="picker-item">{{item.a}}月</view></picker-view-column><picker-view-column><view wx:for="{{ay}}" wx:for-item="item" wx:key="b" class="picker-item">{{item.a}}日</view></picker-view-column></picker-view></block><view class="popup-btn" bindtap="{{aB}}">确认保存</view></view></uni-popup><uni-popup wx:if="{{aN}}" class="r" u-s="{{['d']}}" u-r="agePopup" u-i="2062139e-1" bind:__l="__l" u-p="{{aN}}"><view class="popup-box"><view class="popup-top df"><view class="popup-title"><view class="t1">选择兴趣爱好</view><view class="t2">可选择多个标签 ({{aE}}/5)</view></view><view class="popup-close df" bindtap="{{aG}}"><image src="{{aF}}" style="width:20rpx;height:20rpx"></image></view></view><scroll-view class="tag-categories" scroll-x="true" show-scrollbar="false"><view wx:for="{{aH}}" wx:for-item="category" wx:key="d" class="{{['category-item', category.e]}}" bindtap="{{category.f}}" style="position:relative">{{category.a}} <text wx:if="{{category.b}}" style="font-size:20rpx;margin-left:6rpx;opacity:0.8">({{category.c}})</text></view></scroll-view><swiper class="tags-swiper" current="{{aJ}}" bindchange="{{aK}}"><swiper-item wx:for="{{aI}}" wx:for-item="category" wx:key="c"><scroll-view class="tags-scroll" scroll-y="true"><view class="tags-box"><view wx:if="{{category.a}}" class="no-tags"> 该分类暂无标签 </view><view wx:for="{{category.b}}" wx:for-item="item" wx:key="b" class="{{['tag-item', item.c]}}" bindtap="{{item.d}}">{{item.a}}</view></view></scroll-view></swiper-item></swiper><view class="popup-btn" bindtap="{{aL}}">确认保存</view></view></uni-popup><uni-popup wx:if="{{ba}}" class="r" u-s="{{['d']}}" u-r="genderPopup" u-i="2062139e-2" bind:__l="__l" u-p="{{ba}}"><view class="popup-box"><view class="popup-top df"><view class="popup-title"><view class="t1">选择性别</view><view class="t2">请选择您的性别</view></view><view class="popup-close df" bindtap="{{aP}}"><image src="{{aO}}" style="width:20rpx;height:20rpx"></image></view></view><view class="gender-box"><view bindtap="{{aR}}" class="{{['gender-item', 'df', aS]}}"><image src="{{aQ}}"></image><text>男</text></view><view bindtap="{{aU}}" class="{{['gender-item', 'df', aV]}}"><image src="{{aT}}"></image><text>女</text></view><view bindtap="{{aW}}" class="{{['gender-item', 'df', aX]}}"><text>未知</text></view></view><view class="popup-btn" bindtap="{{aY}}">确认保存</view></view></uni-popup><uni-popup wx:if="{{bd}}" class="r" u-s="{{['d']}}" u-r="tipsPopup" u-i="2062139e-3" bind:__l="__l" u-p="{{bd}}"><view class="tips-box df"><view class="tips-item">{{bb}}</view></view></uni-popup><view class="bottom-safe-area"></view><canvas wx:if="{{be}}" canvas-id="canvas" style="{{'width:' + bf + ';' + ('height:' + bg) + ';' + ('position:' + 'absolute') + ';' + ('left:' + '-100000px') + ';' + ('top:' + '-100000px')}}"></canvas><uni-popup wx:if="{{bq}}" class="r" u-s="{{['d']}}" u-r="newTagsPopup" u-i="2062139e-4" bind:__l="__l" u-p="{{bq}}"><view class="popup-box"><view class="popup-top df"><view class="popup-title"><view class="t1">选择我的标签</view><view class="t2">可选择多个标签 ({{bh}}/5)</view></view><view class="popup-close df" bindtap="{{bj}}"><image src="{{bi}}" style="width:20rpx;height:20rpx"></image></view></view><scroll-view class="tag-categories" scroll-x="true" show-scrollbar="false"><view wx:for="{{bk}}" wx:for-item="category" wx:key="d" class="{{['category-item', category.e]}}" bindtap="{{category.f}}" style="position:relative">{{category.a}} <text wx:if="{{category.b}}" style="font-size:20rpx;margin-left:6rpx;opacity:0.8">({{category.c}})</text></view></scroll-view><swiper class="tags-swiper" current="{{bm}}" bindchange="{{bn}}"><swiper-item wx:for="{{bl}}" wx:for-item="category" wx:key="c"><scroll-view class="tags-scroll" scroll-y="true"><view class="tags-box"><view wx:if="{{category.a}}" class="no-tags"> 该分类暂无标签 </view><view wx:for="{{category.b}}" wx:for-item="item" wx:key="b" class="{{['tag-item', item.c]}}" bindtap="{{item.d}}">{{item.a}}</view></view></scroll-view></swiper-item></swiper><view class="popup-btn" bindtap="{{bo}}">确认保存</view></view></uni-popup></view></form></view>