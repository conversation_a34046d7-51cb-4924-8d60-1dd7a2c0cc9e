"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_social = require("../../api/social.js");
if (!Math) {
  (emptyPage + lazyImage)();
}
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const emptyPage = () => "../../components/emptyPage/emptyPage.js";
const _sfc_main = {
  __name: "follow",
  setup(__props) {
    const store = common_vendor.useStore();
    const statusBarHeight = common_vendor.ref(store.state.statusBarHeight || 20);
    const titleBarHeight = common_vendor.ref(store.state.titleBarHeight || 44);
    const userId = common_vendor.ref(0);
    const myUserId = common_vendor.ref(0);
    const userName = common_vendor.ref("关注列表");
    const barList = common_vendor.ref(["关注", "粉丝"]);
    const barIdx = common_vendor.ref(0);
    const uiState = common_vendor.reactive({
      isEmpty: false,
      showLoading: false
    });
    const dataState = common_vendor.reactive({
      list: [],
      page: 1,
      limit: 10,
      totalCount: 0,
      loadStatus: "more"
    });
    const timers = common_vendor.reactive({
      loading: null,
      debounce: null
    });
    const formattedList = common_vendor.computed(() => {
      return dataState.list;
    });
    common_vendor.computed(() => {
      return dataState.loadStatus === "loading";
    });
    const hasMoreData = common_vendor.computed(() => {
      return dataState.list.length < dataState.totalCount && dataState.list.length > 0;
    });
    const getButtonClass = (item) => {
      const baseClass = ["list-btn", "bg1"];
      if (barIdx.value === 0 || barIdx.value === 1 && item.is_follow === 1) {
        baseClass.push("bg2");
      }
      return baseClass.join(" ");
    };
    const getButtonText = (item) => {
      if (item.is_mutual === 1) {
        return "互相关注";
      }
      if (barIdx.value === 0) {
        return "已关注";
      }
      if (barIdx.value === 1) {
        return item.is_follow === 1 ? "已关注" : "关注";
      }
      return "关注";
    };
    const debounce = (func, delay = 300) => {
      if (timers.debounce) {
        clearTimeout(timers.debounce);
      }
      timers.debounce = setTimeout(() => {
        func();
        timers.debounce = null;
      }, delay);
    };
    const handleError = (error, context = "操作", showToast = true) => {
      common_vendor.index.__f__("error", "at pages/center/follow.vue:178", `${context}失败:`, error);
      if (showToast) {
        let message = `${context}失败，请稍后重试`;
        if (error.message) {
          message = error.message;
        } else if (error.msg) {
          message = error.msg;
        }
        common_vendor.index.showToast({
          title: message,
          icon: "none",
          duration: 2e3
        });
      }
    };
    const clearTimers = () => {
      Object.keys(timers).forEach((key) => {
        if (timers[key]) {
          clearTimeout(timers[key]);
          timers[key] = null;
        }
      });
    };
    const getUserFollowList = () => {
      if (dataState.loadStatus === "loading") {
        return;
      }
      dataState.loadStatus = "loading";
      clearTimers();
      timers.loading = setTimeout(() => {
        if (dataState.loadStatus === "loading") {
          uiState.showLoading = true;
        }
      }, 300);
      const params = {
        page: dataState.page,
        limit: dataState.limit
      };
      if (userId.value > 0) {
        params.user_id = userId.value;
      }
      const apiMethod = barIdx.value === 0 ? api_social.getSocialFollowList : api_social.getSocialFansList;
      apiMethod(params).then((res) => {
        dataState.loadStatus = "more";
        clearTimers();
        uiState.showLoading = false;
        common_vendor.index.stopPullDownRefresh();
        if (res.status === 200 && res.data) {
          const responseData = res.data;
          if (responseData.list && responseData.list.length > 0) {
            if (dataState.page == 1) {
              dataState.list = responseData.list;
            } else {
              dataState.list = dataState.list.concat(responseData.list);
            }
            if (responseData.count !== void 0) {
              dataState.totalCount = responseData.count;
            }
            uiState.isEmpty = false;
          } else if (dataState.page == 1) {
            uiState.isEmpty = true;
            dataState.list = [];
          }
          if (dataState.list.length >= dataState.totalCount && dataState.list.length > 0) {
            dataState.loadStatus = "noMore";
          }
        } else {
          if (dataState.page == 1) {
            uiState.isEmpty = true;
            dataState.list = [];
          }
          handleError({ msg: res.msg || "获取数据失败" }, "获取数据");
        }
      }).catch((error) => {
        dataState.loadStatus = "more";
        clearTimers();
        uiState.showLoading = false;
        common_vendor.index.stopPullDownRefresh();
        if (dataState.page == 1) {
          uiState.isEmpty = true;
          dataState.list = [];
        }
        handleError(error, "网络请求");
      });
    };
    const followUser = (e) => {
      if (!e || !e.currentTarget || !e.currentTarget.dataset) {
        common_vendor.index.showToast({
          title: "操作失败，参数错误",
          icon: "none"
        });
        return;
      }
      const index = e.currentTarget.dataset.idx;
      if (index === void 0 || !formattedList.value[index]) {
        common_vendor.index.showToast({
          title: "数据错误，请刷新后重试",
          icon: "none"
        });
        return;
      }
      const item = formattedList.value[index];
      const isFollow = barIdx.value === 0 ? true : item.is_follow === 1;
      const targetUserId = parseInt(barIdx.value === 0 ? item.follow_uid : item.uid);
      if (!targetUserId || targetUserId <= 0) {
        common_vendor.index.showToast({
          title: "获取用户ID失败",
          icon: "none"
        });
        common_vendor.index.__f__("error", "at pages/center/follow.vue:334", "关注操作失败: 无效的用户ID", targetUserId);
        return;
      }
      const params = {
        follow_uid: targetUserId,
        type: isFollow ? 0 : 1
        // 0取消关注，1关注
      };
      common_vendor.index.showLoading({ title: "处理中...", mask: true });
      api_social.followUser(params).then((res) => {
        common_vendor.index.hideLoading();
        if (res.status === 200) {
          if (barIdx.value === 0 && isFollow) {
            dataState.list.splice(index, 1);
            if (dataState.list.length === 0) {
              uiState.isEmpty = true;
              dataState.loadStatus = "more";
            }
          } else if (barIdx.value === 1 && dataState.list[index]) {
            dataState.list[index].is_follow = isFollow ? 0 : 1;
            if (isFollow && dataState.list[index].is_mutual === 1) {
              dataState.list[index].is_mutual = 0;
            }
          }
          common_vendor.index.showToast({
            title: isFollow ? "已取消关注" : "关注成功",
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: res.msg || "操作失败，请重试",
            icon: "none"
          });
        }
      }).catch((error) => {
        common_vendor.index.hideLoading();
        handleError(error, "关注操作");
      });
    };
    const barClick = (e) => {
      if (dataState.loadStatus === "loading") {
        return;
      }
      const clickIdx = parseInt(e.currentTarget.dataset.idx);
      if (clickIdx === barIdx.value) {
        return;
      }
      debounce(() => {
        barIdx.value = clickIdx;
        dataState.page = 1;
        if (userId.value > 0) {
          userName.value = userName.value.replace(/(.*?)的(关注|粉丝)/, `$1的${clickIdx == 0 ? "关注" : "粉丝"}`);
        } else {
          userName.value = userName.value.replace(/(.*?)(关注|粉丝)/, `$1${clickIdx == 0 ? "关注" : "粉丝"}`);
        }
        dataState.list = [];
        uiState.isEmpty = false;
        getUserFollowList();
      }, 100);
    };
    const toUser = (e) => {
      if (!e || !e.currentTarget || !e.currentTarget.dataset || !e.currentTarget.dataset.id) {
        common_vendor.index.showToast({
          title: "用户信息无效",
          icon: "none"
        });
        return;
      }
      const userId2 = parseInt(e.currentTarget.dataset.id);
      if (!userId2 || userId2 <= 0) {
        common_vendor.index.showToast({
          title: "用户ID无效",
          icon: "none"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: "/pages/user/details?id=" + userId2
      });
    };
    const navBack = () => {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "/pages/user/index"
        });
      }
    };
    common_vendor.onLoad((options) => {
      var _a;
      if (options.type) {
        barIdx.value = parseInt(options.type);
      }
      myUserId.value = parseInt(((_a = store.state.app) == null ? void 0 : _a.uid) || 0);
      if (options.id) {
        userId.value = parseInt(options.id);
        userName.value = options.name ? options.name + "的" + (barIdx.value == 0 ? "关注" : "粉丝") : barIdx.value == 0 ? "关注" : "粉丝";
      } else {
        userId.value = 0;
        const USER_INFO = common_vendor.index.getStorageSync("USER_INFO");
        const nickname = (USER_INFO == null ? void 0 : USER_INFO.nickname) || "我";
        userName.value = nickname + "的" + (barIdx.value == 0 ? "关注" : "粉丝");
      }
      getUserFollowList();
    });
    common_vendor.onPullDownRefresh(() => {
      if (dataState.loadStatus !== "loading") {
        dataState.page = 1;
        getUserFollowList();
      } else {
        common_vendor.index.stopPullDownRefresh();
      }
    });
    common_vendor.onReachBottom(() => {
      if (dataState.loadStatus === "loading") {
        return;
      }
      if (hasMoreData.value) {
        dataState.page = dataState.page + 1;
        dataState.loadStatus = "loading";
        getUserFollowList();
      } else if (dataState.list.length >= dataState.totalCount && dataState.list.length > 0) {
        dataState.loadStatus = "noMore";
      }
    });
    common_vendor.onUnload(() => {
      clearTimers();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$7,
        b: common_vendor.o(navBack),
        c: common_vendor.t(userName.value),
        d: titleBarHeight.value + "px",
        e: common_vendor.f(barList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item),
            b: index == barIdx.value ? "#000" : "#999",
            c: index == barIdx.value ? "28rpx" : "26rpx",
            d: index == barIdx.value ? 1 : 0,
            e: index,
            f: common_vendor.o(barClick, index),
            g: index
          };
        }),
        f: statusBarHeight.value + "px",
        g: uiState.showLoading
      }, uiState.showLoading ? {} : {}, {
        h: uiState.isEmpty
      }, uiState.isEmpty ? {
        i: common_vendor.p({
          title: userId.value ? barIdx.value == 0 ? "TA还没有关注" : "TA还没有粉丝" : barIdx.value == 0 ? "你还没有关注任何人" : "还没有人关注你",
          description: userId.value ? "" : barIdx.value == 0 ? "赶快去关注感兴趣的小伙伴吧" : "发笔记，让大家可以关注你",
          image: "/static/img/empty.png"
        })
      } : {
        j: common_vendor.f(formattedList.value, (item, index, i0) => {
          return common_vendor.e({
            a: "127bb211-1-" + i0,
            b: common_vendor.p({
              src: barIdx.value === 0 ? item.follow_avatar : item.avatar
            }),
            c: common_vendor.t(barIdx.value === 0 ? item.follow_nickname : item.nickname),
            d: common_vendor.t(barIdx.value === 0 ? item.follow_about_me || "" : item.about_me || "")
          }, userId.value == 0 || userId.value == myUserId.value ? {
            e: common_vendor.t(getButtonText(item)),
            f: index,
            g: common_vendor.o(followUser, index),
            h: common_vendor.n(getButtonClass(item))
          } : {}, {
            i: index,
            j: common_vendor.o(toUser, index),
            k: barIdx.value === 0 ? item.follow_uid : item.uid
          });
        }),
        k: userId.value == 0 || userId.value == myUserId.value
      }, {
        l: formattedList.value.length > 0 && dataState.loadStatus === "noMore"
      }, formattedList.value.length > 0 && dataState.loadStatus === "noMore" ? {} : {}, {
        m: "calc(" + (statusBarHeight.value + titleBarHeight.value) + "px + 80rpx)"
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/center/follow.js.map
