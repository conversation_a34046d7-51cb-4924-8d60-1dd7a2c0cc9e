{"version": 3, "file": "index.js", "sources": ["pages/users/user_integral/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlcnMvdXNlcl9pbnRlZ3JhbC9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n\t<view>\r\n\t\t<view class='integral-details' :style=\"colorStyle\">\r\n\t\t\t<view class='header'>\r\n\t\t\t\t<view class='currentScore'>{{$t(`当前积分`)}}</view>\r\n\t\t\t\t<view class=\"scoreNum\">{{userInfo.integral}}</view>\r\n\t\t\t\t<view class='line'></view>\r\n\t\t\t\t<view class='nav acea-row'>\r\n\t\t\t\t\t<view class='item'>\r\n\t\t\t\t\t\t<view class='num'>{{userInfo.sum_integral}}</view>\r\n\t\t\t\t\t\t<view>{{$t(`累计积分`)}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item'>\r\n\t\t\t\t\t\t<view class='num'>{{userInfo.deduction_integral}}</view>\r\n\t\t\t\t\t\t<view>{{$t(`累计消费`)}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item'>\r\n\t\t\t\t\t\t<view class='num'>{{userInfo.frozen_integral}}</view>\r\n\t\t\t\t\t\t<view>{{$t(`冻结积分`)}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"apply\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<navigator url='/pages/users/privacy/index?type=6' hover-class=\"none\">\r\n\t\t\t\t\t\t\t<view>{{$t(`积分规则`)}}</view>\r\n\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='wrapper'>\r\n\t\t\t\t<view class='nav acea-row'>\r\n\t\t\t\t\t<view class='item acea-row row-center-wrapper' :class='current==index?\"on\":\"\"'\r\n\t\t\t\t\t\tv-for=\"(item,index) in navList\" :key='index' @click='nav(index)'><text class='iconfont'\r\n\t\t\t\t\t\t\t:class=\"item.icon\"></text>{{item.name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='list' :class=\"{'bag-white': integralList.length}\" :hidden='current!=0'>\r\n\t\t\t\t\t<view class='tip acea-row row-middle' v-if=\"!isTime\"><text\r\n\t\t\t\t\t\t\tclass='iconfont icon-shuoming'></text>{{$t(`提示：积分数值的高低会直接影响您的会员等级`)}}</view>\r\n\t\t\t\t\t<view class='tip acea-row row-middle' v-else><text\r\n\t\t\t\t\t\t\tclass='iconfont icon-shuoming'></text>{{$t(`提示：你有`)}}{{userInfo.clear_integral}}{{$t(`积分在`)}}{{ dateFormat(userInfo.clear_time) }}{{$t(`过期，请尽快使用`)}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper' v-for=\"(item,index) in integralList\" :key=\"index\">\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<view class='state'>{{$t(item.title)}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.add_time}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='num font-color' v-if=\"item.pm\">+{{item.number}}</view>\r\n\t\t\t\t\t\t<view class='num' v-else>-{{item.number}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='loadingicon acea-row row-center-wrapper' v-if=\"integralList.length>0\">\r\n\t\t\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"no-thing\" v-if=\"integralList.length == 0\">\r\n\t\t\t\t\t\t暂无积分记录哦～\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='list2' :hidden='current!=1'>\r\n\t\t\t\t\t<navigator class='item acea-row row-between-wrapper' hover-class='none' open-type=\"switchTab\"\r\n\t\t\t\t\t\turl='/pages/index/index'>\r\n\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t<image src='/static/score.png'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='name'>{{$t(`购买商品可获得积分奖励`)}}</view>\r\n\t\t\t\t\t\t<view class='earn'>{{$t(`赚积分`)}}</view>\r\n\t\t\t\t\t</navigator>\r\n\t\t\t\t\t<navigator class='item acea-row row-between-wrapper' hover-class='none'\r\n\t\t\t\t\t\turl='/pages/users/user_sgin/index'>\r\n\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t<image src='/static/score.png'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='name'>{{$t(`每日签到可获得积分奖励`)}}</view>\r\n\t\t\t\t\t\t<view class='earn'>{{$t(`赚积分`)}}</view>\r\n\t\t\t\t\t</navigator>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tpostSignUser,\r\n\t\tgetIntegralList\r\n\t} from '@/api/user.js';\r\n\t// import dayjs from 'dayjs'; // 暂时移除dayjs依赖\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport colors from '@/mixins/color'\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmixins: [colors],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tnavList: [{\r\n\t\t\t\t\t\t'name': this.$t(`分值明细`),\r\n\t\t\t\t\t\t'icon': 'icon-mingxi'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t'name': this.$t(`分值提升`),\r\n\t\t\t\t\t\t'icon': 'icon-tishengfenzhi'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tcurrent: 0,\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\tintegralList: [],\r\n\t\t\t\tuserInfo: {},\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadTitle: this.$t(`加载更多`),\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tisTime: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\twatch: {\r\n\t\t\tisLogin: {\r\n\t\t\t\thandler: function(newV, oldV) {\r\n\t\t\t\t\tif (newV) {\r\n\t\t\t\t\t\tthis.getUserInfo();\r\n\t\t\t\t\t\tthis.getIntegralList();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.getUserInfo();\r\n\t\t\t\tthis.getIntegralList();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 页面上拉触底事件的处理函数\r\n\t\t */\r\n\t\tonReachBottom: function() {\r\n\t\t\tthis.getIntegralList();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// Vue3: 过滤器已移除，改为方法\r\n\t\t\tdateFormat(value) {\r\n\t\t\t\tconst date = new Date(value * 1000);\r\n\t\t\t\tconst year = date.getFullYear();\r\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\r\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\r\n\t\t\t\treturn `${year}-${month}-${day}`;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 授权回调\r\n\t\t\t */\r\n\t\t\tonLoadFun: function() {\r\n\t\t\t\tthis.getUserInfo();\r\n\t\t\t\tthis.getIntegralList();\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\tgetUserInfo: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tpostSignUser({\r\n\t\t\t\t\tsign: 1,\r\n\t\t\t\t\tintegral: 1,\r\n\t\t\t\t\tall: 1\r\n\t\t\t\t}).then(function(res) {\r\n\t\t\t\t\tthat.$set(that, 'userInfo', res.data);\r\n\t\t\t\t\tlet clearTime = res.data.clear_time;\r\n\t\t\t\t\tlet showTime = clearTime - (86400 * 14);\r\n\t\t\t\t\tlet timestamp = Date.parse(new Date()) / 1000;\r\n\t\t\t\t\tif (showTime < timestamp) {\r\n\t\t\t\t\t\tthat.isTime = 1\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.isTime = 0\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 获取积分明细\r\n\t\t\t */\r\n\t\t\tgetIntegralList: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.loading) return;\r\n\t\t\t\tif (that.loadend) return;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.loadTitle = '';\r\n\t\t\t\tgetIntegralList({\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit\r\n\t\t\t\t}).then(function(res) {\r\n\t\t\t\t\tlet list = res.data,\r\n\t\t\t\t\t\tloadend = list.length < that.limit;\r\n\t\t\t\t\tthat.integralList = that.$util.SplitArray(list, that.integralList);\r\n\t\t\t\t\tthat.$set(that, 'integralList', that.integralList);\r\n\t\t\t\t\tthat.page = that.page + 1;\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\t\tthat.loadTitle = loadend ? that.$t(`我也是有底线的`) : that.$t(`加载更多`);\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tthat.loadTitle = that.$t(`加载更多`);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tnav: function(current) {\r\n\t\t\t\tthis.current = current;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.integral-details .header {\r\n\t\t// background-image: url('data:image/jpeg;base64,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');\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t\twidth: 100%;\r\n\t\theight: 460rpx;\r\n\t\tfont-size: 72rpx;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 31rpx 0 45rpx 0;\r\n\t\tbox-sizing: border-box;\r\n\t\ttext-align: center;\r\n\t\tfont-family: 'Guildford Pro';\r\n\t\tbackground-color: var(--view-theme);\r\n\t}\r\n\r\n\t.integral-details .header .currentScore {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 11rpx;\r\n\t}\r\n\r\n\t.integral-details .header .scoreNum {\r\n\t\tfont-family: \"Guildford Pro\";\r\n\t}\r\n\r\n\t.integral-details .header .line {\r\n\t\twidth: 60rpx;\r\n\t\theight: 3rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tmargin: 20rpx auto 0 auto;\r\n\t}\r\n\r\n\t.integral-details .header .nav {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\tflex: 1;\r\n\t\tmargin-top: 35rpx;\r\n\t}\r\n\r\n\t.integral-details .header .nav .item {\r\n\t\twidth: 33.33%;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.integral-details .header .nav .item .num {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 40rpx;\r\n\t\tmargin-bottom: 5rpx;\r\n\t\tfont-family: 'Guildford Pro';\r\n\t}\r\n\r\n\t.integral-details .wrapper .nav {\r\n\t\tflex: 1;\r\n\t\twidth: 690rpx;\r\n\t\tborder-radius: 20rpx 20rpx 0 0;\r\n\t\tmargin: -96rpx auto 0 auto;\r\n\t\tbackground-color: #f7f7f7;\r\n\t\theight: 96rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #bbb;\r\n\t}\r\n\r\n\t.integral-details .wrapper .nav .item {\r\n\t\ttext-align: center;\r\n\t\twidth: 50%;\r\n\t}\r\n\r\n\t.integral-details .wrapper .nav .item.on {\r\n\t\tbackground-color: #fff;\r\n\t\tcolor: var(--view-theme);\r\n\t\tfont-weight: bold;\r\n\t\tborder-radius: 20rpx 0 0 0;\r\n\t}\r\n\r\n\t.integral-details .wrapper .nav .item:nth-of-type(2).on {\r\n\t\tborder-radius: 0 20rpx 0 0;\r\n\t}\r\n\r\n\t.integral-details .wrapper .nav .item .iconfont {\r\n\t\tfont-size: 38rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.integral-details .wrapper .list {\r\n\t\tpadding: 24rpx 30rpx;\r\n\t}\r\n\r\n\t.integral-details .wrapper .list.bag-white {\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.integral-details .wrapper .list .tip {\r\n\t\tfont-size: 25rpx;\r\n\t\twidth: 690rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tbackground-color: #fff5e2;\r\n\t\tborder: 1rpx solid #ffeac1;\r\n\t\tcolor: #c8a86b;\r\n\t\tpadding: 0 20rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-bottom: 24rpx;\r\n\t}\r\n\r\n\t.integral-details .wrapper .list .tip .iconfont {\r\n\t\tfont-size: 35rpx;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.integral-details .wrapper .list .item {\r\n\t\theight: 124rpx;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.integral-details .wrapper .list .item .state {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.integral-details .wrapper .list .item .num {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-family: 'Guildford Pro';\r\n\t\tcolor: #16AC57;\r\n\t}\r\n\r\n\t.integral-details .wrapper .list .item .num.font-color {\r\n\t\tcolor: #E93323 !important;\r\n\t}\r\n\r\n\t.integral-details .wrapper .list2 {\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 24rpx 0;\r\n\t}\r\n\r\n\t.integral-details .wrapper .list2 .item {\r\n\t\tbackground-image: linear-gradient(to right, #fff7e7 0%, #fffdf9 100%);\r\n\t\twidth: 690rpx;\r\n\t\theight: 180rpx;\r\n\t\tposition: relative;\r\n\t\tborder-radius: 10rpx;\r\n\t\tmargin: 0 auto 20rpx auto;\r\n\t\tpadding: 0 25rpx 0 180rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.integral-details .wrapper .list2 .item .pictrue {\r\n\t\twidth: 90rpx;\r\n\t\theight: 150rpx;\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tleft: 45rpx;\r\n\t}\r\n\r\n\t.integral-details .wrapper .list2 .item .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.integral-details .wrapper .list2 .item .name {\r\n\t\twidth: 285rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #c8a86b;\r\n\t}\r\n\r\n\t.integral-details .wrapper .list2 .item .earn {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #c8a86b;\r\n\t\tborder: 2rpx solid #c8a86b;\r\n\t\ttext-align: center;\r\n\t\tline-height: 52rpx;\r\n\t\theight: 52rpx;\r\n\t\twidth: 160rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t}\r\n\r\n\t.apply {\r\n\t\ttop: 52rpx;\r\n\t\tright: 0;\r\n\t\tposition: absolute;\r\n\t\twidth: max-content;\r\n\t\theight: 56rpx;\r\n\t\tpadding: 0 14rpx;\r\n\t\tbackground-color: #fff1db;\r\n\t\tcolor: #a56a15;\r\n\t\tfont-size: 22rpx;\r\n\t\tborder-radius: 30rpx 0 0 30rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.empty-box {\r\n\t\tpadding-bottom: 300rpx;\r\n\t}\r\n</style>\r\n", "import MiniProgramPage from 'D:/uniapp/vue3/pages/users/user_integral/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["colors", "mapGetters", "<PERSON><PERSON><PERSON><PERSON>", "postSignUser", "getIntegralList"], "mappings": ";;;;;;AA+FC,MAAO,YAAW,MAAW;AAG7B,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IAEX;AAAA,EAEA;AAAA,EACD,QAAQ,CAACA,aAAAA,MAAM;AAAA,EACf,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,QAAC;AAAA,UACR,QAAQ,KAAK,GAAG,MAAM;AAAA,UACtB,QAAQ;AAAA,QACR;AAAA,QACD;AAAA,UACC,QAAQ,KAAK,GAAG,MAAM;AAAA,UACtB,QAAQ;AAAA,QACT;AAAA,MACA;AAAA,MACD,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc,CAAE;AAAA,MAChB,UAAU,CAAE;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,MACT,WAAW,KAAK,GAAG,MAAM;AAAA,MACzB,QAAQ;AAAA;AAAA,MACR,YAAY;AAAA;AAAA,MACZ,QAAQ;AAAA;EAET;AAAA,EACD,UAAUC,cAAAA,WAAW,CAAC,SAAS,CAAC;AAAA,EAChC,OAAO;AAAA,IACN,SAAS;AAAA,MACR,SAAS,SAAS,MAAM,MAAM;AAC7B,YAAI,MAAM;AACT,eAAK,YAAW;AAChB,eAAK,gBAAe;AAAA,QACrB;AAAA,MACA;AAAA,MACD,MAAM;AAAA,IACP;AAAA,EACA;AAAA,EACD,SAAS;AACR,QAAI,KAAK,SAAS;AACjB,WAAK,YAAW;AAChB,WAAK,gBAAe;AAAA,WACd;AACNC,iBAAAA;IACD;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAID,eAAe,WAAW;AACzB,SAAK,gBAAe;AAAA,EACpB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,WAAW,OAAO;AACjB,YAAM,OAAO,IAAI,KAAK,QAAQ,GAAI;AAClC,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA,IAID,WAAW,WAAW;AACrB,WAAK,YAAW;AAChB,WAAK,gBAAe;AAAA,IACpB;AAAA;AAAA,IAED,WAAW,SAAS,GAAG;AACtB,WAAK,aAAa;AAAA,IAClB;AAAA,IACD,aAAa,WAAW;AACvB,UAAI,OAAO;AACXC,4BAAa;AAAA,QACZ,MAAM;AAAA,QACN,UAAU;AAAA,QACV,KAAK;AAAA,MACN,CAAC,EAAE,KAAK,SAAS,KAAK;AACrB,aAAK,KAAK,MAAM,YAAY,IAAI,IAAI;AACpC,YAAI,YAAY,IAAI,KAAK;AACzB,YAAI,WAAW,YAAa,QAAQ;AACpC,YAAI,YAAY,KAAK,MAAM,oBAAI,KAAI,CAAE,IAAI;AACzC,YAAI,WAAW,WAAW;AACzB,eAAK,SAAS;AAAA,eACR;AACN,eAAK,SAAS;AAAA,QACf;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,iBAAiB,WAAW;AAC3B,UAAI,OAAO;AACX,UAAI,KAAK;AAAS;AAClB,UAAI,KAAK;AAAS;AAClB,WAAK,UAAU;AACf,WAAK,YAAY;AACjBC,+BAAgB;AAAA,QACf,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,MACb,CAAC,EAAE,KAAK,SAAS,KAAK;AACrB,YAAI,OAAO,IAAI,MACd,UAAU,KAAK,SAAS,KAAK;AAC9B,aAAK,eAAe,KAAK,MAAM,WAAW,MAAM,KAAK,YAAY;AACjE,aAAK,KAAK,MAAM,gBAAgB,KAAK,YAAY;AACjD,aAAK,OAAO,KAAK,OAAO;AACxB,aAAK,UAAU;AACf,aAAK,UAAU;AACf,aAAK,YAAY,UAAU,KAAK,GAAG,SAAS,IAAI,KAAK,GAAG,MAAM;AAAA,MAC9D,GAAE,SAAS,KAAK;AAChB,aAAK,UAAU;AACf,aAAK,YAAY,KAAK,GAAG,MAAM;AAAA,MAChC,CAAC;AAAA,IACD;AAAA,IACD,KAAK,SAAS,SAAS;AACtB,WAAK,UAAU;AAAA,IAChB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/ND,GAAG,WAAW,eAAe;"}