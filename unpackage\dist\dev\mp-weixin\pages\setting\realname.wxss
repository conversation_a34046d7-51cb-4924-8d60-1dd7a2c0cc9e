
.page-wrapper.data-v-a3319d4f {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background: #fff;
	position: relative;
}
.content-scroll.data-v-a3319d4f {
	flex: 1;
	overflow-y: auto;
}
.container.data-v-a3319d4f {
	width: calc(100% - 60rpx);
	padding: 0 30rpx;
	background: #fff;
}
.title-box.data-v-a3319d4f {
	padding: 20rpx 0;
	font-size: 40rpx;
	font-weight: 700;
}
.title-box view.data-v-a3319d4f:last-child {
	font-size: 24rpx;
	color: #999;
	font-weight: normal;
	margin-top: 10rpx;
}
.desc.data-v-a3319d4f {
	color: #888;
	font-size: 24rpx;
	margin-bottom: 30rpx;
	line-height: 1.4;
}
.form-section.data-v-a3319d4f {
	background: #fff;
	border-radius: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
	padding: 20rpx 0 10rpx 0;
	margin-bottom: 30rpx;
}
.title-label.data-v-a3319d4f {
	width: calc(100% - 48rpx);
	padding: 30rpx 24rpx 12rpx;
	color: #999;
	font-size: 24rpx;
	font-weight: 700;
}
.input-box.data-v-a3319d4f {
	width: calc(100% - 68rpx);
	padding: 0 30rpx;
	height: 90rpx;
	line-height: 90rpx;
	font-size: 28rpx;
	font-weight: 700;
	border: 4rpx solid #f5f5f5;
	border-radius: 24rpx;
	justify-content: space-between;
	margin-bottom: 20rpx;
}
.input-box[disabled].data-v-a3319d4f {
	background: #f8f8f8;
	color: #bbb;
}
.value.data-v-a3319d4f {
	color: #333;
	font-size: 28rpx;
}
.fail-reason.data-v-a3319d4f {
	color: #fa5150;
	font-size: 24rpx;
	margin: 10rpx 24rpx 20rpx;
	padding: 20rpx;
	background: rgba(250, 81, 80, 0.1);
	border-radius: 12rpx;
}
.success-msg.data-v-a3319d4f {
	color: #4cd964;
	font-size: 24rpx;
	margin: 10rpx 24rpx 20rpx;
	padding: 20rpx;
	background: rgba(76, 217, 100, 0.1);
	border-radius: 12rpx;
}
.pending-msg.data-v-a3319d4f {
	color: #faad14;
	font-size: 24rpx;
	margin: 10rpx 24rpx 20rpx;
	padding: 20rpx;
	background: rgba(250, 173, 20, 0.1);
	border-radius: 12rpx;
}
.protocol-row.data-v-a3319d4f {
	display: flex;
	align-items: center;
	margin: 20rpx 0;
	font-size: 24rpx;
	color: #999;
}
.protocol-text.data-v-a3319d4f {
	font-size: 24rpx;
	color: #999;
}
.protocol-link.data-v-a3319d4f {
	color: #576b95;
}

/* 底部按钮样式 */
.footer-box.data-v-a3319d4f {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	padding-bottom: env(safe-area-inset-bottom);
	background: #fff;
	z-index: 99;
}
.footer-box .footer-item.data-v-a3319d4f {
	width: calc(100% - 60rpx);
	padding: 20rpx 30rpx;
	justify-content: center;
	box-sizing: border-box;
}
.footer-item .btn.data-v-a3319d4f {
	width: calc(100% - 30rpx);
	height: 90rpx;
	line-height: 90rpx;
	text-align: center;
	font-size: 28rpx;
	font-weight: 700;
	border-radius: 45rpx;
}
.bg2.data-v-a3319d4f {
	color: #fff;
	background: #000;
}
.bg2[disabled].data-v-a3319d4f {
	background: #ccc !important;
	color: #fff !important;
	opacity: 0.6;
}
.btn-gray.data-v-a3319d4f {
	background: #ccc !important;
	color: #fff !important;
	opacity: 0.6;
	cursor: not-allowed;
}
button[disabled].data-v-a3319d4f {
	background: #ccc !important;
	color: #fff !important;
	opacity: 0.6;
}
.cancel-section.data-v-a3319d4f {
	margin: 20rpx 0 40rpx;
	width: 100%;
}
.cancel-btn.data-v-a3319d4f {
	color: #666;
	background: #f5f5f5;
	border: 2rpx solid #ddd;
}
.cancel-btn[disabled].data-v-a3319d4f {
	background: #f0f0f0;
	color: #ccc;
}
.bfw.data-v-a3319d4f {
	background: #fff;
}
.bUp.data-v-a3319d4f {
	box-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);
}

/* 提示弹窗样式 */
.tips-box.data-v-a3319d4f {
	padding: 20rpx 30rpx;
	border-radius: 12rpx;
	justify-content: center;
	margin-top: 40rpx;
}
.tips-box .tips-item.data-v-a3319d4f {
	color: #fff;
	font-size: 28rpx;
	font-weight: 600;
	text-align: center;
}
.df.data-v-a3319d4f {
	display: flex;
	align-items: center;
}

/* 底部安全区域 */
.bottom-safe-area.data-v-a3319d4f {
	height: 150rpx;
	width: 100%;
	margin-bottom: env(safe-area-inset-bottom);
}

/* 输入框样式优化 */
input.data-v-a3319d4f {
	border: none;
	background: transparent;
	outline: none;
	width: 100%;
}
input[disabled].data-v-a3319d4f {
	color: #bbb;
}

/* 复选框样式 */
checkbox.data-v-a3319d4f {
	transform: scale(0.8);
}

/* 手机号绑定按钮样式 */
.input-btn.data-v-a3319d4f {
	width: 90rpx;
	height: 90rpx;
	font-size: 24rpx;
	justify-content: space-between;
	margin: 0;
	padding: 0;
	background: #fff;
}
.input-btn image.data-v-a3319d4f {
	margin-right: 8rpx;
	width: 32rpx;
	height: 32rpx;
}
.input-tips.data-v-a3319d4f {
	color: #999;
	font-size: 24rpx;
	margin-bottom: 20rpx;
}

/* Adjust the protocol row spacing */
.protocol-row.data-v-a3319d4f {
	display: flex;
	align-items: center;
	margin: 20rpx 0;
	font-size: 24rpx;
	color: #999;
}

/* Ensure the footer button has proper spacing */
.footer-box .footer-item.data-v-a3319d4f {
	width: calc(100%);
	padding: 20rpx 30rpx;
	justify-content: center;
	box-sizing: border-box;
}

/* Add box shadow to make the footer stand out */
.bUp.data-v-a3319d4f {
	box-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);
}
