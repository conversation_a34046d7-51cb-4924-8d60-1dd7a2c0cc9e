
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 99;
  background: #fff;
  box-sizing: border-box;
}
.back-box .back-item {
  padding: 0 30rpx;
  width: 34rpx;
  height: 100%;
}
.nav-bar .nav-search {
  width: 100%;
  padding: 20rpx 0;
}
.nav-bar .nav-search input {
  margin-left: 30rpx;
  width: calc(100% - 200rpx);
  height: 80rpx;
  padding: 0 30rpx;
  font-size: 26rpx;
  font-weight: 700;
  border-radius: 8rpx;
  background: #f8f8f8;
  justify-content: space-between;
}
.nav-bar .nav-search .btn {
  width: 120rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 26rpx;
  font-weight: 700;
}
.nav-bar .nav-search .ph {
  color: #999;
}
.nav-bar .nav-search image {
  width: 38rpx;
  height: 38rpx;
}
.nav-bar .bar-box {
  width: 100%;
  height: 80rpx;
}
.bar-box .bar-item {
  padding: 0 30rpx;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.bar-box .bar-item text {
  font-weight: 700;
  transition: all .3s ease-in-out;
}
.bar-item .bar-line {
  position: absolute;
  bottom: 12rpx;
  width: 18rpx;
  height: 6rpx;
  border-radius: 6rpx;
  background: #000;
  transition: opacity .3s ease-in-out;
}
.content-box {
  width: 100%;
  padding-bottom: 30rpx;
}
.content-box .dynamic-box {
  width: calc(100% - 16rpx);
  padding: 22rpx 8rpx 0;
}
.goods {
  width: 100%;
  padding: 20rpx 0 0;
  display: flex;
  flex-wrap: wrap;
}
.goods .goods-item {
  width: calc(50% - 19rpx);
  margin: 10rpx 0 0 10rpx;
  background: #fff;
  border-radius: 8rpx;
  overflow: hidden;
  border: 2rpx solid #f5f5f5;
}
.goods-item .goods-img {
  width: 100%;
  padding-top: 100%;
  position: relative;
}
.goods-img .goods-img-item {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.goods-item .goods-name {
  width: calc(100% - 40rpx);
  margin: 15rpx 20rpx;
  font-size: 26rpx;
  line-height: 36rpx;
  font-weight: 500;
}
.goods-item .goods-price {
  width: calc(100% - 30rpx);
  margin: 0 20rpx 20rpx;
  display: flex;
  align-items: flex-end;
}
.goods-price .price-h {
  margin-left: 15rpx;
  color: #999;
  font-size: 20rpx;
  line-height: 20rpx;
}
.goods-item .goods-tag {
  width: calc(100% - 30rpx);
  margin: 0 15rpx 15rpx;
  display: flex;
  flex-wrap: wrap;
}
.goods-tag .tag-item {
  margin: 0 5rpx 5rpx;
  height: 40rpx;
  padding: 0 12rpx;
  line-height: 40rpx;
  font-size: 18rpx;
  font-weight: 500;
  background: #f8f8f8;
  border-radius: 8rpx;
}
.content-box .circle-box {
  width: calc(100% - 60rpx);
  padding: 30rpx 30rpx 10rpx;
  justify-content: space-between;
}
.circle-box .circle-avatar {
  width: 168rpx;
  height: 168rpx;
  border-radius: 50%;
  background: #f8f8f8;
  border: 1px solid #f8f8f8;
  position: relative;
}
.circle-avatar .tag {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 6rpx solid #fff;
}
.circle-box .circle-item {
  width: calc(100% - 198rpx);
  margin-left: 30rpx;
}
.circle-item .name {
  font-size: 32rpx;
  font-weight: 700;
}
.circle-item .intro {
  margin: 10rpx 0 20rpx;
  color: #999;
  font-size: 24rpx;
}
.cu-img-group {
  direction: ltr;
  unicode-bidi: bidi-override;
  display: inline-block;
  margin-left: 16rpx;
}
.cu-img-group .cu-img {
  width: 32rpx;
  height: 32rpx;
  display: inline-flex;
  position: relative;
  margin-left: -16rpx;
  border: 4rpx solid #fff;
  background: #f8f8f8;
  vertical-align: middle;
  border-radius: 50%;
}
.cu-img-group .cu-img image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.cu-img-group .cu-txt {
  margin-left: 10rpx;
  display: inline-flex;
  color: #999;
  font-size: 20rpx;
  font-weight: 700;
}
.content-box .user-box {
  width: calc(100% - 60rpx);
  padding: 30rpx;
  justify-content: space-between;
}
.user-box .user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 1px solid #f8f8f8;
  overflow: hidden;
}
.user-box .user-item {
  width: calc(100% - 120rpx - 2px);
  margin-left: 20rpx;
}
.user-item .name {
  font-size: 28rpx;
  font-weight: 700;
}
.user-item .unm {
  margin: 5rpx 0 10rpx;
  color: #999;
  font-size: 20rpx;
}
.user-tag .tag-item {
  margin-right: 16rpx;
  height: 44rpx;
  padding: 0 14rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
  font-weight: 500;
  font-size: 20rpx;
  justify-content: center;
}
.user-tag .tag-item image {
  width: 24rpx;
  height: 24rpx;
}
.activity-item {
  width: calc(100% - 60rpx);
  padding: 30rpx;
}
.activity-item .activity-img {
  width: 275rpx;
  height: 220rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
  position: relative;
  overflow: hidden;
}
.activity-img .activity-state {
  position: absolute;
  top: 15rpx;
  left: 15rpx;
  width: 68rpx;
  height: 40rpx;
  color: #fff;
  font-size: 16rpx;
  font-weight: 700;
  border-radius: 8rpx;
  background: rgba(0, 0, 0, .6);
  justify-content: center;
}
.activity-item .activity-data {
  padding-left: 20rpx;
  width: calc(100% - 295rpx);
  height: 220rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}
.activity-data .title {
  font-size: 28rpx;
  line-height: 28rpx;
  font-weight: 700;
  padding-bottom: 12rpx;
}
.activity-data .txt {
  margin-bottom: 4rpx;
}
.activity-data .txt image {
  margin-right: 8rpx;
  width: 20rpx;
  height: 20rpx;
}
.activity-data .txt view {
  width: calc(100% - 26rpx);
  color: #999;
  font-size: 20rpx;
  font-weight: 500;
}
.activity-data .cu-img-group {
  margin: 8rpx 0 16rpx 16rpx;
  direction: ltr;
  unicode-bidi: bidi-override;
  display: inline-block;
}
.cu-img-group .cu-tit {
  display: inline-flex;
  margin-left: 8rpx;
  color: #999;
  font-size: 20rpx;
  font-weight: 500;
}
.activity-data .cu-txt-group {
  margin: 8rpx 0 16rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: #999;
}
.activity-data .activity-btn {
  width: 100%;
  height: 60rpx;
  justify-content: space-between;
}
.activity-btn .btn-item {
  padding: 0;
  margin: 0;
  height: 60rpx;
  font-size: 20rpx;
  font-weight: 700;
  color: #000;
  background: #f8f8f8;
  border-radius: 8rpx;
  justify-content: center;
}
.activity-btn .btn-item .icon {
  margin-left: 10rpx;
  width: 20rpx;
  height: 20rpx;
}
.activity-btn .btn-item .img {
  width: 24rpx;
  height: 24rpx;
}
.w100 {
  width: 100%;
}
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-box image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-box .e1 {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.empty-box .e2 {
  font-size: 24rpx;
  color: #999;
}
.tips-box {
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
  justify-content: center;
}
.tips-box .tips-item {
  color: #fff;
  font-size: 28rpx;
  font-weight: 700;
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
