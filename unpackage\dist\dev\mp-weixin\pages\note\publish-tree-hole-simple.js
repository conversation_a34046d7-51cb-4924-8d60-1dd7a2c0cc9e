"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const libs_login = require("../../libs/login.js");
const _sfc_main = {
  data() {
    return {
      currentType: 1,
      content: "",
      isAnonymous: true,
      isSubmitting: false,
      typeList: [
        { value: 1, name: "问题咨询", icon: "❓" },
        { value: 2, name: "秘密", icon: "🤫" },
        { value: 3, name: "心愿", icon: "🌠" },
        { value: 4, name: "语音纸条", icon: "🎵" }
      ],
      // 调试信息
      debugInfo: {
        checkLoginResult: false,
        storeToken: "",
        localToken: "",
        userInfo: null,
        finalLoginStatus: false,
        apiStatus: "未调用",
        lastError: ""
      }
    };
  },
  onLoad() {
    this.printLoginDebugInfo();
    if (!this.checkLoginStatus()) {
      return;
    }
  },
  methods: {
    // 打印登录调试信息
    printLoginDebugInfo() {
      common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:104", "=== 登录状态调试信息 ===");
      const isLoginCheck = libs_login.checkLogin();
      this.debugInfo.checkLoginResult = isLoginCheck;
      common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:109", "checkLogin()结果:", isLoginCheck);
      let storeToken = null;
      try {
        if (this.$store && this.$store.state && this.$store.state.app) {
          storeToken = this.$store.state.app.token;
          this.debugInfo.storeToken = storeToken || "空";
          common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:117", "$store.state.app.token:", storeToken);
        } else {
          this.debugInfo.storeToken = "store不存在";
          common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:120", "$store.state.app 不存在");
        }
      } catch (e) {
        this.debugInfo.storeToken = "获取失败: " + e.message;
        common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:124", "获取store token失败:", e);
      }
      try {
        const localToken = common_vendor.index.getStorageSync("token");
        this.debugInfo.localToken = localToken || "空";
        common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:131", "本地存储token:", localToken);
      } catch (e) {
        this.debugInfo.localToken = "获取失败: " + e.message;
        common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:134", "获取本地token失败:", e);
      }
      try {
        const loginStatusToken = common_vendor.index.getStorageSync("LOGIN_STATUS_TOKEN");
        common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:140", "LOGIN_STATUS_TOKEN:", loginStatusToken);
      } catch (e) {
        common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:142", "获取LOGIN_STATUS_TOKEN失败:", e);
      }
      try {
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        this.debugInfo.userInfo = userInfo;
        common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:149", "本地用户信息:", userInfo);
      } catch (e) {
        this.debugInfo.userInfo = null;
        common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:152", "获取用户信息失败:", e);
      }
      const finalLoginStatus = isLoginCheck && storeToken;
      this.debugInfo.finalLoginStatus = finalLoginStatus;
      common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:158", "最终登录状态:", finalLoginStatus);
      common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:159", "=== 调试信息结束 ===");
    },
    checkLoginStatus() {
      const isLoggedIn = libs_login.checkLogin();
      if (!isLoggedIn) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请先登录后再发布纸条",
          confirmText: "去登录",
          cancelText: "返回",
          success: (res) => {
            if (res.confirm) {
              libs_login.toLogin();
            } else {
              common_vendor.index.navigateBack();
            }
          }
        });
        return false;
      }
      return true;
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    selectType(type) {
      this.currentType = type;
      this.content = "";
    },
    toggleAnonymous() {
      this.isAnonymous = !this.isAnonymous;
    },
    publishBox() {
      var _a, _b, _c;
      common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:199", "=== 发布纸条调试信息 ===");
      this.printLoginDebugInfo();
      const isLoginCheck = libs_login.checkLogin();
      const storeToken = (_c = (_b = (_a = this.$store) == null ? void 0 : _a.state) == null ? void 0 : _b.app) == null ? void 0 : _c.token;
      common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:207", "发布时登录状态:", isLoginCheck);
      common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:208", "发布时token:", storeToken);
      if (!this.validateContent()) {
        return;
      }
      this.isSubmitting = true;
      this.debugInfo.apiStatus = "准备调用API";
      const data = {
        type: this.currentType,
        content: this.currentType === 4 ? "" : this.content.trim(),
        voice_url: "",
        voice_duration: 0,
        is_anonymous: this.isAnonymous ? 1 : 0
      };
      common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:225", "发布数据:", data);
      common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:226", "调用API: publishTreeHoleBox");
      this.debugInfo.apiStatus = "正在调用API...";
      api_social.publishTreeHoleBox(data).then((res) => {
        common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:230", "API响应:", res);
        this.isSubmitting = false;
        this.debugInfo.apiStatus = `API成功: ${res.status}`;
        if (res.status === 200) {
          this.debugInfo.apiStatus = "API成功: 200";
          common_vendor.index.showToast({
            title: "发布成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          this.debugInfo.apiStatus = `API失败: ${res.status} - ${res.msg}`;
          common_vendor.index.showToast({
            title: res.msg || "发布失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        this.isSubmitting = false;
        common_vendor.index.__f__("error", "at pages/note/publish-tree-hole-simple.vue:253", "发布纸条失败:", err);
        common_vendor.index.__f__("log", "at pages/note/publish-tree-hole-simple.vue:254", "错误详情:", JSON.stringify(err));
        this.debugInfo.apiStatus = "API异常";
        this.debugInfo.lastError = err.message || err.msg || JSON.stringify(err);
        common_vendor.index.showToast({
          title: "网络错误，请稍后重试",
          icon: "none"
        });
      });
    },
    validateContent() {
      if (this.currentType === 4) {
        common_vendor.index.showToast({
          title: "语音功能开发中",
          icon: "none"
        });
        return false;
      }
      if (!this.content.trim()) {
        common_vendor.index.showToast({
          title: "请输入纸条内容",
          icon: "none"
        });
        return false;
      }
      if (this.content.trim().length < 5) {
        common_vendor.index.showToast({
          title: "内容太短，至少输入5个字符",
          icon: "none"
        });
        return false;
      }
      return true;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.t($data.isSubmitting ? "发布中..." : "发布"),
    c: common_vendor.o((...args) => $options.publishBox && $options.publishBox(...args)),
    d: common_vendor.f($data.typeList, (item, k0, i0) => {
      return {
        a: common_vendor.t(item.icon),
        b: common_vendor.t(item.name),
        c: item.value,
        d: $data.currentType === item.value ? 1 : "",
        e: common_vendor.o(($event) => $options.selectType(item.value), item.value)
      };
    }),
    e: $data.currentType !== 4
  }, $data.currentType !== 4 ? {
    f: $data.content,
    g: common_vendor.o(($event) => $data.content = $event.detail.value)
  } : {}, {
    h: $data.currentType !== 4
  }, $data.currentType !== 4 ? {
    i: common_vendor.t($data.content.length)
  } : {}, {
    j: $data.isAnonymous ? 1 : "",
    k: common_vendor.o((...args) => $options.toggleAnonymous && $options.toggleAnonymous(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-22b8a2a2"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/note/publish-tree-hole-simple.js.map
