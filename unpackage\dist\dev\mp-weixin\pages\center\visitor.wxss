
.container.data-v-971c42a7 {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 选项卡样式 */
.tab-bar.data-v-971c42a7 {
  display: flex;
  background: white;
  padding: 0 20rpx;
}
.tab-item.data-v-971c42a7 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
}
.tab-text.data-v-971c42a7 {
  font-size: 28rpx;
  color: #999;
  font-weight: 500;
  transition: all 0.3s ease;
}
.tab-item.active .tab-text.data-v-971c42a7 {
  color: #333;
  font-weight: bold;
}
.tab-line.data-v-971c42a7 {
  position: absolute;
  bottom: 0;
  width: 60rpx;
  height: 4rpx;
  background: #333;
  border-radius: 2rpx;
}

/* 访客统计 */
.visitor-stats.data-v-971c42a7 {
  display: flex;
  background: white;
  padding: 20rpx 30rpx;
  margin-bottom: 10rpx;
  align-items: center;
  justify-content: center;
}
.stats-item.data-v-971c42a7 {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.stats-number.data-v-971c42a7 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.2;
}
.stats-label.data-v-971c42a7 {
  font-size: 22rpx;
  color: #999;
  margin-top: 6rpx;
}
.stats-divider.data-v-971c42a7 {
  width: 1rpx;
  height: 40rpx;
  background-color: #eee;
}

/* 访客列表 */
.visitor-list.data-v-971c42a7 {
  flex: 1;
}

/* 空状态 */
.empty-state.data-v-971c42a7 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}
.empty-image.data-v-971c42a7 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text.data-v-971c42a7 {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.empty-desc.data-v-971c42a7 {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

/* 访客列表 */
.visitor-list-content.data-v-971c42a7 {
  background: white;
}
.visitor-item.data-v-971c42a7 {
  border-bottom: 1rpx solid #f0f0f0;
}
.visitor-item.data-v-971c42a7:last-child {
  border-bottom: none;
}

/* 访客行信息 */
.visitor-row.data-v-971c42a7 {
  display: flex;
  align-items: center;
  padding: 20rpx;
}
.avatar-wrapper.data-v-971c42a7 {
  position: relative;
  margin-right: 20rpx;
}
.visitor-avatar.data-v-971c42a7 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}
.vip-badge.data-v-971c42a7 {
  position: absolute;
  bottom: -3rpx;
  right: -3rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
}
.vip-icon.data-v-971c42a7 {
  width: 18rpx;
  height: 18rpx;
}
.visitor-info.data-v-971c42a7 {
  flex: 1;
}
.visitor-name-row.data-v-971c42a7 {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.visitor-name.data-v-971c42a7 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 8rpx;
}
.gender-icon.data-v-971c42a7,
.auth-icon.data-v-971c42a7 {
  width: 24rpx;
  height: 24rpx;
  margin-left: 6rpx;
}

/* 访问信息 */
.visit-info.data-v-971c42a7 {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.visit-time.data-v-971c42a7 {
  font-size: 24rpx;
  color: #666;
  margin-right: 20rpx;
}
.visit-count.data-v-971c42a7 {
  font-size: 24rpx;
  color: #1890ff;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.visitor-row.data-v-971c42a7 {
    padding: 15rpx 20rpx;
}
.visitor-avatar.data-v-971c42a7 {
    width: 70rpx;
    height: 70rpx;
}
.visitor-name.data-v-971c42a7 {
    font-size: 26rpx;
}
.visitor-stats.data-v-971c42a7 {
    padding: 15rpx 20rpx;
}
.stats-number.data-v-971c42a7 {
    font-size: 32rpx;
}
.tab-text.data-v-971c42a7 {
    font-size: 26rpx;
}
}
