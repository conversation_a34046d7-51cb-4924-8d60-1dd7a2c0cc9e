<view class="container"><view class="table">个人信息</view><view class="list-box"><button class="list df" data-url="center/means" bindtap="{{c}}"><image class="icon" src="{{a}}"></image><view class="list-item df bb1"><view class="title">个人资料</view><image src="{{b}}"></image></view></button><button class="list df"><image class="icon" src="{{d}}"></image><view class="list-item df bb1"><view class="title">手机号码</view><button wx:if="{{e}}" class="input-btn" open-type="getPhoneNumber" bindgetphonenumber="{{f}}"> 点击绑定手机号 <text class="iconfont icon-xiangyou"></text></button><view wx:else class="input acea-row row-between-wrapper"><view class=""></view><view class="acea-row row-middle"><text>{{g}}</text><text class="iconfont icon-suozi"></text></view></view></view></button><button wx:if="{{h}}" class="list df" data-url="users/user_phone/index?type=1" bindtap="{{k}}"><image class="icon" src="{{i}}"></image><view class="list-item df"><view class="title">更换手机号码</view><image src="{{j}}"></image></view></button></view><view wx:if="{{l}}" class="wrapper"><view class="title">账号切换</view><view class="wrapList"><view wx:for="{{m}}" wx:for-item="item" wx:key="f" class="{{['item', item.e]}}" bindtap="{{item.g}}"><view class="picTxt acea-row row-between-wrapper"><view class="pictrue acea-row row-center-wrapper"><image src="{{item.a}}" mode=""></image></view><view class="text"><view class="name line1">{{item.b}}</view><view class="phone">{{item.c}}</view></view></view><view wx:if="{{item.d}}" class="bnt acea-row row-center-wrapper">切换</view><view wx:else class="currentBnt acea-row row-center-wrapper">当前</view></view></view></view><view class="table">账号管理</view><view class="list-box"><button class="list df" data-url="center/address" bindtap="{{p}}"><image class="icon" src="{{n}}"></image><view class="list-item df bb1"><view class="title">收货地址</view><image src="{{o}}"></image></view></button><button class="list df" data-url="setting/realname" bindtap="{{s}}"><image class="icon" src="{{q}}"></image><view class="list-item df bb1"><view class="title">实名认证</view><image src="{{r}}"></image></view></button><button wx:if="{{t}}" class="list df" data-url="center/invoice" bindtap="{{x}}"><image class="icon" src="{{v}}"></image><view class="list-item df bb1"><view class="title">发票管理</view><image src="{{w}}"></image></view></button><button class="list df" data-url="setting/logout" bindtap="{{A}}"><image class="icon" src="{{y}}"></image><view class="list-item df"><view class="title">注销账号</view><image src="{{z}}"></image></view></button></view><view class="table">通用</view><view class="list-box"><view wx:if="{{B}}" class="list df"><image class="icon" src="{{C}}"></image><view class="list-item df bb1"><view class="title">语言切换</view><picker bindchange="{{E}}" range-key="name" value="{{F}}" range="{{G}}"><view class="picker-content"><text class="picker-text">{{D}}</text><text class="iconfont icon-xiangyou"></text></view></picker></view></view><button class="list df" open-type="contact"><image class="icon" src="{{H}}"></image><view class="list-item df bb1"><view class="title">在线客服</view><image src="{{I}}"></image></view></button><button class="list df" data-url="setting/privacy" bindtap="{{L}}"><image class="icon" src="{{J}}"></image><view class="list-item df bb1"><view class="title">隐私与显示设置</view><image src="{{K}}"></image></view></button></view><view class="table">关于</view><view class="list-box"><button class="list df" data-url="setting/xinxuan?type=3" bindtap="{{O}}"><image class="icon" src="{{M}}"></image><view class="list-item df bb1"><view class="title">隐私政策</view><image src="{{N}}"></image></view></button><button class="list df" data-url="setting/xinxuan?type=4" bindtap="{{R}}"><image class="icon" src="{{P}}"></image><view class="list-item df bb1"><view class="title">用户协议</view><image src="{{Q}}"></image></view></button></view><view class="logout-section"><button class="logout-btn" bindtap="{{T}}"><image class="logout-icon" src="{{S}}"></image><text class="logout-text">退出登录</text></button></view></view>