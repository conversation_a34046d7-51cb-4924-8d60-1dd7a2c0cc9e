
.pl-sty {
		color: #999999;
		font-size: 30rpx;
}

.product-window.on.data-v-6020d7bd {
  transform: translate3d(0, 0, 0);
}
.mask.data-v-6020d7bd {
  z-index: 99;
}
.product-window.data-v-6020d7bd {
  position: fixed;
  bottom: 0;
  width: 100%;
  left: 0;
  background-color: #fff;
  z-index: 1000;
  border-radius: 40rpx 40rpx 0 0;
  transform: translate3d(0, 100%, 0);
  transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
  padding: 64rpx 40rpx;
  padding-bottom: 38rpx;
  padding-bottom: calc(38rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(38rpx + env(safe-area-inset-bottom));
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
}
.product-window .icon-guanbi.data-v-6020d7bd {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  font-size: 24rpx;
  font-weight: bold;
  color: #999;
}
.product-window .mp-data.data-v-6020d7bd {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}
.product-window .mp-data .mp-name.data-v-6020d7bd {
  font-size: 34rpx;
  font-weight: 500;
  color: #333333;
  line-height: 48rpx;
}
.product-window .trip-msg.data-v-6020d7bd {
  padding-bottom: 32rpx;
}
.product-window .trip-msg .title.data-v-6020d7bd {
  font-size: 30rpx;
  font-weight: bold;
  color: #000;
  margin-bottom: 6rpx;
}
.product-window .trip-msg .trip.data-v-6020d7bd {
  color: #333333;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
}
.product-window .trip-title.data-v-6020d7bd {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}
.product-window .main-color.data-v-6020d7bd {
  font-size: 28rpx;
  font-weight: 400;
  color: var(--view-theme);
  margin-bottom: 40rpx;
}
.product-window .bottom.data-v-6020d7bd {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.product-window .bottom .save.data-v-6020d7bd,
.product-window .bottom .reject.data-v-6020d7bd {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 670rpx;
  height: 80rpx;
  border-radius: 80rpx;
  background-color: #F5F5F5;
  color: #333;
  font-size: 30rpx;
  font-weight: 500;
}
.product-window .bottom .save.data-v-6020d7bd {
  background-color: var(--view-theme);
  color: #fff;
  margin-bottom: 24rpx;
}