"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "lazyImage",
  props: {
    src: {
      type: String,
      default: "",
      required: false
    },
    br: {
      type: String,
      default: void 0,
      required: false
    }
  },
  data() {
    return {
      loaded: false,
      error: false
    };
  },
  watch: {
    src: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.resetLoadingState();
        }
      },
      immediate: false
    }
  },
  mounted() {
    this.resetLoadingState();
  },
  methods: {
    // 重置加载状态
    resetLoadingState() {
      if (!this.src) {
        this.loaded = true;
        this.error = false;
      } else {
        this.loaded = false;
        this.error = false;
      }
    },
    // 图片加载成功
    onImageLoad() {
      this.loaded = true;
      this.error = false;
    },
    // 图片加载失败
    onImageError() {
      this.error = true;
      this.loaded = true;
      common_vendor.index.__f__("log", "at components/lazyImage/lazyImage.vue:67", "Image load error:", this.src);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.src || "/static/img/load.png",
    b: $data.loaded ? 1 : 0,
    c: common_vendor.o((...args) => $options.onImageLoad && $options.onImageLoad(...args)),
    d: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args)),
    e: $data.error
  }, $data.error ? {} : {}, {
    f: $props.br
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-337fa078"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/lazyImage/lazyImage.js.map
