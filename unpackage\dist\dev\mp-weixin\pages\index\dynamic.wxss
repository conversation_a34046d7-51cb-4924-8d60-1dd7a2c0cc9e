
.nav-box{
  position:fixed;
  top:0;
  width:100%;
  z-index:99;
  box-sizing:border-box;
  background:#fff;
  /* 添加硬件加速 */
  transform: translateZ(0);
  will-change: transform;
}
.nav-box .bar-box{
  width:100%;
  position:relative
}
.bar-box .bar-title{
  padding:0 30rpx;
  font-size:32rpx;
  font-weight:700
}
.bar-box .bar-title image{
  margin-left:10rpx;
  width:15rpx;
  height:15rpx;
  transition:transform .3s ease-in-out
}
.bar-box .bar-search{
  padding:0 20rpx;
  width:180rpx;
  height:calc(100% - 24rpx);
  font-size:24rpx;
  font-weight:700;
  color:#999;
  background:#f8f8f8;
  border-radius:50rpx;
  border:1px solid #f5f5f5
}
.bar-box .bar-search image{
  margin-right:10rpx;
  width:30rpx;
  height:30rpx
}
.bar-box .bar-item{
  position:absolute;
  z-index:101;
  left:30rpx;
  padding:0 20rpx;
  flex-direction:column;
  justify-content:center;
  background:#fff;
  border-radius:24rpx;
  box-shadow:0 0 24rpx rgba(0,0,0,.06);
  transition:height .3s ease-in-out;
  overflow:hidden
}
.bar-box .bar-item view{
  padding:0 25rpx 0 15rpx;
  width:140rpx;
  height:70rpx;
  font-size:26rpx;
  font-weight:500;
  border-radius:12rpx;
  justify-content:space-between
}
.bar-box .bar-item image{
  width:24rpx;
  height:24rpx
}
.bar-box .bar-item view:hover{
  background:#f8f8f8
}
.bar-mask{
  position:fixed;
  z-index:100;
  top:0;
  right:0;
  bottom:0;
  left:0;
  width:100%;
  height:100%
}
.content-box{
  width:100%
}
.scroll-box{
  width:100%;
  white-space:nowrap;
  overflow:hidden;
  transition:height .45s ease-in-out
}
.content-box .circle-box{
  width:100%;
  display:flex;
  padding:30rpx 10rpx
}
.circle-box .circle-item{
  flex-shrink:0
}
.circle-item .circle-item-top{
  margin:0 20rpx;
  width:116rpx;
  height:116rpx;
  border-radius:50%;
  background:#f8f8f8;
  border:2rpx solid #f5f5f5;
  position:relative
}
.circle-item-top image{
  width:100%;
  height:100%;
  border-radius:50%
}
.circle-item-top .icon{
  margin:34rpx;
  width:48rpx;
  height:48rpx
}
.circle-item-top .circle-item-tag{
  position:absolute;
  right:0;
  bottom:0;
  width:24rpx;
  height:24rpx;
  border-radius:50%;
  border:6rpx solid #fff
}
.circle-item .circle-name{
  margin:20rpx 0 10rpx;
  width:160rpx;
  color:#000;
  font-weight:500;
  font-size:24rpx;
  line-height:24rpx;
  text-align:center
}
.circle-item .circle-tips{
  width:160rpx;
  color:#999;
  font-size:18rpx;
  line-height:18rpx;
  font-weight:300;
  text-align:center
}
.circle-item .circle-all{
  background:#f8f8f8
}
.content-box .home-title{
  width:calc(100% - 60rpx);
  padding:0 30rpx
}
.home-title .home-title-txt{
  font-weight:700;
  font-size:32rpx
}
.home-title .home-title-all{
  margin-left:12rpx;
  padding:0 12rpx;
  height:40rpx;
  line-height:40rpx;
  font-size:16rpx;
  font-weight:700;
  color:#999;
  background:#f8f8f8;
  border-radius:8rpx
}
.home-activity-item .home-activity-img,
.home-activity-item .home-activity-data{
  width:275rpx;
  height:220rpx;
  border-radius:8rpx;
  position:relative;
  overflow:hidden
}
.home-activity-item .home-activity-data{
  padding:0 30rpx 0 20rpx;
  position:relative
}
.home-activity-img .home-activity-state{
  position:absolute;
  top:16rpx;
  left:16rpx;
  width:68rpx;
  height:38rpx;
  color:#fff;
  font-size:16rpx;
  font-weight:700;
  background:rgba(0,0,0,.4);
  border:1px solid rgba(255,255,255,.16);
  border-radius:8rpx;
  justify-content:center
}
.home-activity-data .home-activity-title{
  font-size:28rpx;
  line-height:28rpx;
  font-weight:700;
  padding-bottom:12rpx
}
.home-activity-data .home-activity-tag view{
  width:calc(100% - 26rpx);
  color:#999;
  font-size:20rpx;
  font-weight:500
}
.home-activity-data .home-cu-img-group{
  margin:8rpx 0 16rpx 16rpx;
  direction:ltr;
  unicode-bidi:bidi-override;
  display:inline-block
}
.home-cu-img-group .home-cu-img{
  width:32rpx;
  height:32rpx;
  display:inline-flex;
  position:relative;
  margin-left:-16rpx;
  border:2rpx solid #fff;
  background:#eee;
  vertical-align:middle;
  border-radius:50%
}
.home-cu-img-group .home-cu-tit{
  display:inline-flex;
  margin-left:8rpx;
  color:#999;
  font-size:20rpx;
  font-weight:500
}
.home-activity-data .home-activity-btn{
  position:absolute;
  bottom:0;
  width:calc(100% - 50rpx);
  height:60rpx;
  font-size:20rpx;
  font-weight:700;
  color:#000;
  background:#f8f8f8;
  border-radius:8rpx;
  justify-content:center
}
.content-box .dynamic-box{
  width:calc(100% - 16rpx);
  padding:8rpx 8rpx 180rpx
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-box image {
  width: 320rpx;
  height: 320rpx;
  opacity: 0.8;
}
.empty-box .e1 {
  margin-top: 50rpx;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.empty-box .e2 {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}
.heio {
  justify-content: center;
}

/* 添加新的导航样式 */
.nav-scroll-box {
  flex: 1;
  overflow: hidden;
}
.nav-scroll {
  white-space: nowrap;
  width: 100%;
}
.nav-scroll-content {
  display: flex;
  padding: 0 20rpx;
}
.nav-item {
  position: relative;
  padding: 0 15rpx;
  font-size: 32rpx;
  display: inline-block;
  margin-right: 30rpx;
  /* 添加过渡效果 */
  transition: all 0.3s ease;
  text-align: center;
}
.nav-item.active {
  font-weight: 700;
  color: #000;
  transform: scale(1.05);
}
.nav-item:not(.active) {
  color: #999;
}
.active-line {
  position: absolute;
  bottom: -5rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 6rpx;
  background-color: #000;
  border-radius: 3rpx;
  /* 添加过渡效果 */
  transition: all 0.3s ease;
}
.content-swiper {
  width: 100%;
  will-change: transform;
  background: #fafafa;
  /* 启用硬件加速 */
  transform: translateZ(0);
}
.content-scroll {
  height: 100%;
  -webkit-overflow-scrolling: touch;
  background: #ffffff;
  /* 优化滚动性能 */
  contain: layout style paint;
  transform: translateZ(0);
}

/* 添加性能优化相关类 */
.hardware-accelerated {
  transform: translateZ(0);
  will-change: transform;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}

/* 优化动画效果 */
@keyframes fade-in {
from {
    opacity: 0;
    transform: translateY(30rpx) scale(0.95);
}
to {
    opacity: 1;
    transform: translateY(0) scale(1);
}
}
@keyframes slide-in-left {
from {
    opacity: 0;
    transform: translateX(-50rpx);
}
to {
    opacity: 1;
    transform: translateX(0);
}
}
@keyframes pulse {
0%, 100% {
    transform: scale(1);
}
50% {
    transform: scale(1.05);
}
}
.card-gg-item {
  animation: fade-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: opacity, transform;
  /* 优化渲染性能 */
  contain: layout style paint;
  transform: translateZ(0);
}
.circle-item {
  animation: fade-in 0.5s ease-out;
}
.login-btn:hover {
  animation: pulse 1s infinite;
}

/* 图片懒加载占位样式 */
.lazy-image-placeholder {
  background: #f0f0f0;
}

/* 虚拟列表相关样式 */
.virtual-list-container {
  position: relative;
  width: 100%;
}
.virtual-list-item {
  position: absolute;
  width: 100%;
  will-change: transform;
}

/* 提高渲染层级，减少重绘 */
.content-box {
  position: relative;
  z-index: 1;
  will-change: transform;
}

/* 优化图像渲染 */
image {
  will-change: transform;
}

/* 添加页面切换动画 */
.page-transition-enter-active,
.page-transition-leave-active {
  transition: opacity 0.3s;
}
.page-transition-enter,
.page-transition-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
.nav-item {
    font-size: 30rpx;
    padding: 0 12rpx;
    margin-right: 25rpx;
}
.empty-box {
    padding: 120rpx 30rpx 100rpx;
    margin: 30rpx 15rpx;
}
.empty-box image {
    width: 280rpx;
    height: 280rpx;
}
.login-btn {
    width: 260rpx;
    height: 80rpx;
    line-height: 80rpx;
    font-size: 30rpx;
}
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
.nav-box {
    background: #1a1a1a;
}
.nav-item.active {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
}
.nav-item:not(.active) {
    color: #ccc;
}
.content-swiper,
  .content-scroll {
    background: #121212;
}
.empty-box {
    background: #1e1e1e;
}
.empty-box .e1 {
    color: #fff;
}
.empty-box .e2 {
    color: #aaa;
}
}

/* 调试信息样式 */
.debug-info {
  position: fixed;
  top: 100rpx;
  right: 10rpx;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  border-radius: 12rpx;
  z-index: 999;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.swiper-box {
  flex: 1;
  width: 100%;
  height: 100%;
  position: relative;
}
.swiper-item {
  flex: 1;
  flex-direction: row;
  position: relative;
  overflow: visible;
}
.scroll-v {
  flex: 1;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: visible;
}

/* 修复页面高度和滚动问题 */
.content-box {
  width: 100%;
  height: 100%;
  overflow: visible;
  position: relative;
}

/* 优化登录按钮样式 */
.login-btn {
  margin-top: 50rpx;
  width: 280rpx;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(135deg, #000 0%, #333 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  border-radius: 44rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 20rpx;
  padding: 0;
  border: none;
  position: relative;
  overflow: hidden;
}
.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}
.login-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.3);
}
.login-btn:active::before {
  left: 100%;
}
.login-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30rpx;
  margin-bottom: 30rpx;
}
.login-tips {
  font-size: 26rpx;
  color: #888;
  margin-top: 16rpx;
  opacity: 0.8;
}

/* 圈子相关样式 */
.scroll-box{
  width:100%;
  white-space:nowrap;
  overflow:hidden;
  transition:height .45s ease-in-out
}
.circle-box{
  width:100%;
  display:flex;
  padding:30rpx 10rpx
}
.circle-box .circle-item{
  flex-shrink:0
}
.circle-item .circle-item-top{
  margin:0 20rpx;
  width:120rpx;
  height:120rpx;
  border-radius:50%;
  background: linear-gradient(135deg, #f8f8f8 0%, #f0f0f0 100%);
  border:3rpx solid #fff;
  position:relative;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}
.circle-item .circle-item-top:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.15);
}
.circle-item-top image{
  width:100%;
  height:100%;
  border-radius:50%;
  object-fit: cover;
}
.circle-item-top .icon{
  margin:36rpx;
  width:48rpx;
  height:48rpx;
  opacity: 0.7;
}
.circle-item-top .circle-item-tag{
  position:absolute;
  right:-2rpx;
  bottom:-2rpx;
  width:28rpx;
  height:28rpx;
  border-radius:50%;
  border:4rpx solid #fff;
  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.15);
}
.circle-item .circle-name{
  margin:24rpx 0 12rpx;
  width:160rpx;
  color:#333;
  font-weight:600;
  font-size:26rpx;
  line-height:28rpx;
  text-align:center;
}
.circle-item .circle-tips{
  width:160rpx;
  color:#888;
  font-size:20rpx;
  line-height:22rpx;
  font-weight:400;
  text-align:center;
  opacity: 0.8;
}
