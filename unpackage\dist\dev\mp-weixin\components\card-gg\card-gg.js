"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../lazyImage/lazyImage.js";
const VoteComponent = () => "../vote-component/vote-component.js";
const DYNAMIC_TYPES = {
  TEXT: 1,
  IMAGE: 2,
  VIDEO: 3,
  AUDIO: 4
};
const NAVIGATION_TYPES = {
  USER_PROFILE: 1,
  TOPIC: 2,
  PRODUCT: 3,
  COMMENT: 4,
  SHARE: 5,
  ACTIVITY: 6,
  CIRCLE: 7
};
const DEBOUNCE_DELAYS = {
  LIKE: 300,
  FOLLOW: 500
};
const _sfc_main = {
  name: "card-gg",
  components: {
    lazyImage,
    VoteComponent
  },
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    idx: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      isNoteVideo: typeof getApp === "function" && getApp() && getApp().globalData && getApp().globalData.isNoteVideo || true,
      userId: common_vendor.index.getStorageSync("userInfo") && common_vendor.index.getStorageSync("userInfo").uid || 0,
      isLiked: false,
      likeCount: 0,
      isProcessing: false
      // 防止重复点击
    };
  },
  created() {
    this.debouncedHandleLike = this.debounce(this.handleLikeAction, DEBOUNCE_DELAYS.LIKE);
    this.debouncedFollowUser = this.debounce(this.followUserAction, DEBOUNCE_DELAYS.FOLLOW);
    this.preloadImages();
  },
  mounted() {
  },
  computed: {
    // 基础数据获取 - 统一处理多种数据结构
    itemData() {
      var _a, _b;
      const userInfo = this.item.user_info || {};
      const userBase = this.item.user || {};
      const mergedUser = { ...userBase, ...userInfo };
      return {
        // 用户信息 - 合并user和user_info，确保VIP信息不丢失
        user: mergedUser,
        // 商品信息
        product: this.item.goods_info || this.item.product_info || {},
        // 活动信息
        activity: this.item.activity || {},
        // 话题信息
        topics: this.item.topic_info || this.item.topics || [],
        // 关注状态
        followStatus: {
          isFollow: this.item.is_follow === 1 || ((_a = this.item.user_info) == null ? void 0 : _a.is_follow) === 1,
          isMutual: this.item.is_mutual_follow === 1 || ((_b = this.item.user_info) == null ? void 0 : _b.is_mutual_follow) === 1
        }
      };
    },
    // 性别图标
    genderIcon() {
      return this.item.gender == 0 ? "/static/img/nv.png" : "/static/img/nan.png";
    },
    // 视频封面
    videoCoverSrc() {
      var _a;
      return this.item.video_cover || ((_a = this.item.video) == null ? void 0 : _a.cover) || "";
    },
    // 音频封面
    audioCoverSrc() {
      var _a;
      return this.item.audio_cover || ((_a = this.item.audio) == null ? void 0 : _a.cover) || "/static/img/audio_cover.png";
    },
    // 用户头像
    userAvatar() {
      return this.itemData.user.avatar || this.item.avatar || "/static/img/avatar.png";
    },
    // 用户名
    userName() {
      return this.itemData.user.name || this.itemData.user.nickname || this.item.nickname || "用户";
    },
    // 用户VIP信息
    userVipInfo() {
      const user = this.itemData.user;
      const isMoneyLevel = user.is_money_level > 0 || user.is_ever_level > 0;
      const svipOpen = user.svip_open > 0 || user.vip === true;
      const showVipByCenter = isMoneyLevel && svipOpen;
      const hasVip = user.vip || false;
      const vipStatus = user.vip_status || 2;
      const isValidVip = hasVip && (vipStatus === 1 || vipStatus === 3);
      const finalShowVip = showVipByCenter || isValidVip;
      return {
        showVip: finalShowVip,
        vipIcon: "/static/img/svip.gif",
        // 与center.vue保持一致，使用固定图标
        vipName: user.vip_name || "",
        vipStatus
      };
    },
    // 位置名称
    locationName() {
      return this.item.location_name || this.item.adds_name || "";
    },
    // 话题列表
    topicsList() {
      return this.itemData.topics;
    },
    // 商品信息
    productId() {
      return this.itemData.product.id || "";
    },
    productImage() {
      return this.itemData.product.image || "";
    },
    productName() {
      return this.itemData.product.store_name || "";
    },
    // 活动信息
    activityImage() {
      return this.item.activity_img || this.itemData.activity.img || "/static/img/activity.png";
    },
    activityName() {
      return this.item.activity_name || this.itemData.activity.name || "活动详情";
    },
    // 获取图片数组
    getImages() {
      var _a;
      if ((_a = this.item.imgs) == null ? void 0 : _a.length) {
        return this.item.imgs.map((img) => typeof img === "string" ? img : img.url);
      }
      if (this.item.images) {
        if (Array.isArray(this.item.images)) {
          return this.item.images;
        }
        if (typeof this.item.images === "string") {
          try {
            return JSON.parse(this.item.images);
          } catch (e) {
            return [this.item.images];
          }
        }
      }
      if (this.item.image) {
        return [this.item.image];
      }
      return [];
    },
    // 获取图片数量
    getImagesCount() {
      return this.getImages.length;
    },
    // 内容检查 - 统一处理各种内容类型的存在性判断
    contentChecks() {
      var _a;
      return {
        hasLocation: !!(this.item.location_name || this.item.adds_name),
        hasTopic: this.itemData.topics.length > 0,
        hasProduct: !!this.itemData.product.id,
        hasActivity: !!(this.item.activity_id || this.itemData.activity.id),
        hasCircle: this.circleInfo.id > 0,
        hasLatestComments: ((_a = this.item.latest_comments) == null ? void 0 : _a.length) > 0
      };
    },
    // 圈子信息 - 统一处理圈子相关数据
    circleInfo() {
      const circleData = this.item.circle_info || {};
      return {
        id: parseInt(circleData.circle_id || this.item.circle_id || 0),
        name: circleData.circle_name || this.item.circle_name || "",
        avatar: circleData.circle_avatar || this.item.circle_avatar || "/static/img/qz1.png"
      };
    },
    // 移除冗余的计算属性，直接在模板中使用 contentChecks 和 circleInfo
    // 获取视频宽高比
    getVideoRatio() {
      var _a, _b;
      if (this.item.video_width && this.item.video_height) {
        return this.item.video_width / this.item.video_height;
      }
      if (((_a = this.item.video) == null ? void 0 : _a.wide) && ((_b = this.item.video) == null ? void 0 : _b.high)) {
        return this.item.video.wide / this.item.video.high;
      }
      return 1;
    },
    // 获取图片宽高比
    getImageRatio() {
      var _a, _b, _c, _d;
      if (this.item.image_width && this.item.image_height) {
        return this.item.image_width / this.item.image_height;
      }
      if (this.getImages.length > 0 && ((_b = (_a = this.item.imgs) == null ? void 0 : _a[0]) == null ? void 0 : _b.wide) && ((_d = (_c = this.item.imgs) == null ? void 0 : _c[0]) == null ? void 0 : _d.high)) {
        return this.item.imgs[0].wide / this.item.imgs[0].high;
      }
      return 1;
    },
    // 关注按钮状态 - 统一处理关注相关逻辑
    followButtonState() {
      const { isFollow, isMutual } = this.itemData.followStatus;
      const isFollowed = isFollow || isMutual;
      return {
        show: this.item.uid != this.userId,
        isMutual,
        isFollowed,
        className: ["follow-btn", isMutual ? "mutual" : isFollowed ? "active" : ""],
        text: isMutual ? "互相关注" : isFollowed ? "已关注" : "＋关注"
      };
    },
    // 移除冗余的计算属性，直接在模板中使用 followButtonState
    // 媒体内容状态 - 统一处理媒体相关判断
    mediaState() {
      var _a;
      const type = this.item.type;
      const imageCount = this.getImagesCount;
      const videoRatio = this.getVideoRatio;
      const imageRatio = this.getImageRatio;
      return {
        showMedia: type > DYNAMIC_TYPES.TEXT || ((_a = this.item.images) == null ? void 0 : _a.length) > 0,
        isSingleImage: type === DYNAMIC_TYPES.IMAGE && imageCount === 1,
        isMultiImage: type === DYNAMIC_TYPES.IMAGE && imageCount > 1,
        isVideo: type === DYNAMIC_TYPES.VIDEO,
        isAudio: type === DYNAMIC_TYPES.AUDIO,
        showImageCount: imageCount > 3,
        singleImageClass: ["file-h", imageRatio > 1 ? "file-w" : ""],
        videoContainerClass: ["file-h", videoRatio > 1 ? "file-w" : ""],
        videoIconStyle: { "left": videoRatio > 1 ? "382rpx" : "282rpx" }
      };
    },
    // 显示状态 - 统一处理显示相关判断
    displayState() {
      var _a, _b;
      const checks = this.contentChecks;
      const authStatus = this.itemData.user.auth_status || ((_a = this.item.user_info) == null ? void 0 : _a.auth_status) || ((_b = this.item.user) == null ? void 0 : _b.auth_status) || 0;
      const isAuthenticated = authStatus === 2;
      return {
        contentClass: ["gg-item-content", "ohto2", this.item.type === DYNAMIC_TYPES.TEXT && !this.item.images ? "wlc8" : ""],
        showExtraInfo: checks.hasLocation || checks.hasTopic || checks.hasProduct || checks.hasCircle || this.item.activity_id,
        showAge: this.item.age && this.item.age !== "暂不展示",
        isAuthenticated
      };
    },
    // 音频信息
    audioInfo() {
      var _a, _b;
      return {
        title: this.item.audio_title || ((_a = this.item.audio) == null ? void 0 : _a.name) || "音频",
        intro: ((_b = this.item.audio) == null ? void 0 : _b.intro) || "点击播放音频"
      };
    },
    // 移除冗余的计算属性，直接在模板中使用对应的状态对象
    // 评论相关信息
    commentInfo() {
      var _a, _b, _c, _d;
      const latestComment = (_a = this.item.latest_comments) == null ? void 0 : _a[0];
      return {
        userName: ((_b = this.item.comment) == null ? void 0 : _b.user_name) || ((_d = (_c = this.item.comment) == null ? void 0 : _c.user) == null ? void 0 : _d.name) || "",
        latestUser: (latestComment == null ? void 0 : latestComment.nickname) || "匿名用户",
        latestContent: (latestComment == null ? void 0 : latestComment.content) || "无内容"
      };
    },
    // 点赞相关状态
    likeState() {
      const count = this.likeCount || "";
      return {
        textStyle: { "color": this.isLiked ? "#FA5150" : "#999" },
        countText: this.formatNumber(count, 1e4, "w")
      };
    },
    // 保留常用的显示数据计算属性
    viewsCount() {
      return this.item.browse || this.item.views || 0;
    },
    timeDisplay() {
      return this.item.create_time_str || this.item.create_time || "刚刚";
    },
    commentCount() {
      return this.item.comment_count || this.item.comments || "";
    }
  },
  watch: {
    item: {
      handler(newVal) {
        if (newVal) {
          const newIsLiked = newVal.is_like === 1;
          const newLikeCount = parseInt(newVal.likes || 0);
          if (this.isLiked !== newIsLiked || this.likeCount !== newLikeCount) {
            this.isLiked = newIsLiked;
            this.likeCount = newLikeCount;
          }
        }
      },
      immediate: true,
      deep: true
    },
    // 监听 isLiked 变化，确保视图更新（添加防抖避免无限循环）
    isLiked: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.$nextTick(() => {
            this.$forceUpdate();
          });
        }
      }
    }
  },
  methods: {
    // 防抖工具函数
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func.apply(this, args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },
    // 通用数字格式化方法
    formatNumber(num, threshold = 1e4, suffix = "w") {
      if (!num || num < threshold)
        return num.toString() || "";
      return (num / threshold).toFixed(1) + suffix;
    },
    // 图片预加载优化
    preloadImages() {
      try {
        const imagesToPreload = this.getImages.slice(0, 3);
        imagesToPreload.forEach((src) => {
          if (src) {
            const img = new Image();
            img.src = src;
          }
        });
        if (this.userAvatar) {
          const avatarImg = new Image();
          avatarImg.src = this.userAvatar;
        }
      } catch (e) {
      }
    },
    // 安全获取全局数据
    getGlobalData(key, defaultValue) {
      try {
        if (typeof getApp !== "function")
          return defaultValue;
        const app = getApp();
        if (!app || !app.globalData)
          return defaultValue;
        return app.globalData[key] !== void 0 ? app.globalData[key] : defaultValue;
      } catch (e) {
        return defaultValue;
      }
    },
    // 统一错误处理
    handleError(error, defaultMessage = "操作失败") {
      const message = (error == null ? void 0 : error.message) || (error == null ? void 0 : error.msg) || defaultMessage;
      common_vendor.index.showToast({
        title: message,
        icon: "none",
        duration: 2e3
      });
    },
    // 认证图标加载错误处理
    onAuthIconError() {
    },
    // 获取话题标题
    topicTitle(topic) {
      if (!topic)
        return "";
      return topic.title || topic.name || "";
    },
    // 获取笔记URL - 统一处理不同类型笔记的URL生成
    getNoteUrl(params = "") {
      const isMediaType = this.item.type == 2 || this.item.type == 3 || this.item.type == 4;
      const basePage = isMediaType ? "note/video" : "note/details";
      return `${basePage}?id=${this.item.id}${params}`;
    },
    /**
     * 处理点赞事件 - 防抖包装器
     */
    handleLike() {
      this.debouncedHandleLike();
    },
    /**
     * 实际的点赞处理逻辑
     */
    handleLikeAction() {
      if (!this.$store.getters.isLogin) {
        common_vendor.index.navigateTo({
          url: "/pages/login/index"
        });
        return;
      }
      if (this.isProcessing)
        return;
      this.isProcessing = true;
      const newLikeStatus = !this.isLiked;
      const oldLikeCount = this.likeCount;
      this.isLiked = newLikeStatus;
      this.likeCount = newLikeStatus ? oldLikeCount + 1 : Math.max(0, oldLikeCount - 1);
      api_social.likeDynamic({
        id: this.item.id,
        is_like: newLikeStatus ? 1 : 0
      }).then(() => {
        this.$emit("likeback", {
          index: this.idx,
          id: this.item.id,
          isLike: newLikeStatus
        });
      }).catch((error) => {
        this.isLiked = !newLikeStatus;
        this.likeCount = oldLikeCount;
        this.handleError(error, "点赞失败，请重试");
      }).finally(() => {
        this.isProcessing = false;
      });
    },
    /**
     * 处理不同类型的页面导航
     * @param {Object} e - 点击事件对象
     */
    toPaths(e) {
      if (!this.item || !this.item.id)
        return;
      let type = e && e.currentTarget ? parseInt(e.currentTarget.dataset.type) : void 0;
      let url;
      if (type === NAVIGATION_TYPES.USER_PROFILE) {
        if (this.item.uid) {
          url = "user/details?id=" + this.item.uid;
        }
      } else if (type === NAVIGATION_TYPES.TOPIC) {
        if (this.topicsList && this.topicsList.length > 0) {
          const topic = this.topicsList[0];
          const topicId = topic && topic.id ? topic.id : "";
          url = "topic/details?id=" + topicId;
        }
      } else if (type === NAVIGATION_TYPES.CIRCLE) {
        if (this.contentChecks.hasCircle) {
          url = "note/circle?id=" + this.circleInfo.id;
        }
      } else if (type === NAVIGATION_TYPES.PRODUCT) {
        let goodsId = e && e.currentTarget && e.currentTarget.dataset ? e.currentTarget.dataset.id : "";
        if (goodsId) {
          url = "goods/details?id=" + goodsId;
        }
      } else if (type === NAVIGATION_TYPES.COMMENT) {
        url = this.getNoteUrl("&comment=true");
      } else if (type === NAVIGATION_TYPES.SHARE) {
        url = this.getNoteUrl("&share=true");
      } else if (type === NAVIGATION_TYPES.ACTIVITY) {
        const activityId = this.item.activity_id || this.itemData.activity.id;
        if (activityId) {
          url = "activity/details?id=" + activityId;
        }
      } else {
        url = this.getNoteUrl();
      }
      if (url) {
        try {
          common_vendor.index.navigateTo({
            url: "/pages/" + url,
            fail: (err) => {
              common_vendor.index.__f__("error", "at components/card-gg/card-gg.vue:730", "页面导航失败:", err);
              common_vendor.index.switchTab({
                url: "/pages/index/index",
                fail: () => {
                  common_vendor.index.__f__("error", "at components/card-gg/card-gg.vue:735", "无法导航到页面");
                }
              });
            }
          });
        } catch (e2) {
          common_vendor.index.__f__("error", "at components/card-gg/card-gg.vue:741", "导航异常:", e2);
        }
      }
    },
    // 打开位置
    opLocationClick() {
      if (!this.item)
        return;
      let latitude = 0;
      try {
        if (this.item.latitude) {
          latitude = parseFloat(this.item.latitude);
        } else if (this.item.lat) {
          latitude = parseFloat(this.item.lat);
        }
        if (isNaN(latitude))
          latitude = 0;
      } catch (e) {
        common_vendor.index.__f__("error", "at components/card-gg/card-gg.vue:763", "解析纬度失败:", e);
        latitude = 0;
      }
      let longitude = 0;
      try {
        if (this.item.longitude) {
          longitude = parseFloat(this.item.longitude);
        } else if (this.item.lng) {
          longitude = parseFloat(this.item.lng);
        }
        if (isNaN(longitude))
          longitude = 0;
      } catch (e) {
        common_vendor.index.__f__("error", "at components/card-gg/card-gg.vue:779", "解析经度失败:", e);
        longitude = 0;
      }
      if (latitude === 0 && longitude === 0) {
        common_vendor.index.showToast({
          title: "位置信息不完整",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.openLocation({
          latitude,
          longitude,
          name: this.locationName || "位置",
          address: this.locationName || "位置",
          fail: (err) => {
            common_vendor.index.__f__("error", "at components/card-gg/card-gg.vue:800", "打开位置失败", err);
            common_vendor.index.showToast({
              title: "无法打开位置信息",
              icon: "none"
            });
          }
        });
      } catch (e) {
        common_vendor.index.__f__("error", "at components/card-gg/card-gg.vue:808", "打开位置异常:", e);
        common_vendor.index.showToast({
          title: "无法打开位置信息",
          icon: "none"
        });
      }
    },
    // 关注用户 - 防抖包装器
    followUser() {
      this.debouncedFollowUser();
    },
    // 实际的关注处理逻辑
    followUserAction() {
      if (!this.item || !this.item.uid) {
        return;
      }
      if (!this.$store.getters.isLogin) {
        common_vendor.index.navigateTo({
          url: "/pages/login/index"
        });
        return;
      }
      if (this.isProcessing)
        return;
      this.isProcessing = true;
      const targetUserId = parseInt(this.item.uid);
      if (!targetUserId || targetUserId <= 0) {
        common_vendor.index.showToast({
          title: "获取用户ID失败",
          icon: "none"
        });
        this.isProcessing = false;
        return;
      }
      const { isFollow, isMutual } = this.itemData.followStatus;
      const isFollowed = isFollow || isMutual;
      const params = {
        follow_uid: targetUserId,
        // 目标用户ID
        is_follow: isFollowed ? 0 : 1
        // 1:关注 0:取消关注
      };
      if (isFollowed) {
        this.item.is_follow = 0;
        this.item.is_mutual_follow = 0;
        if (this.item.user_info) {
          this.item.user_info.is_follow = 0;
          this.item.user_info.is_mutual_follow = 0;
        }
      } else {
        this.item.is_follow = 1;
        if (this.item.user_info) {
          this.item.user_info.is_follow = 1;
        }
      }
      common_vendor.index.showToast({
        title: isFollowed ? "取消关注中..." : "关注中...",
        icon: "none",
        duration: 500
      });
      api_social.followUser(params).then((res) => {
        var _a;
        if (res.status === 200) {
          if (res.data && res.data.is_mutual !== void 0) {
            const isMutual2 = res.data.is_mutual === 1;
            this.item.is_mutual_follow = isMutual2 ? 1 : 0;
            if (this.item.user_info) {
              this.item.user_info.is_mutual_follow = isMutual2 ? 1 : 0;
            }
          }
          this.$emit("followback", {
            idx: this.idx,
            uid: this.item.uid,
            is_follow: isFollowed ? 0 : 1,
            is_mutual: ((_a = res.data) == null ? void 0 : _a.is_mutual) || 0
          });
          common_vendor.index.showToast({
            title: isFollowed ? "已取消关注" : "关注成功",
            icon: "none",
            duration: 1500
          });
        } else {
          if (isFollowed) {
            this.item.is_follow = 1;
            if (isMutual) {
              this.item.is_mutual_follow = 1;
            }
            if (this.item.user_info) {
              this.item.user_info.is_follow = 1;
              if (isMutual) {
                this.item.user_info.is_mutual_follow = 1;
              }
            }
          } else {
            this.item.is_follow = 0;
            this.item.is_mutual_follow = 0;
            if (this.item.user_info) {
              this.item.user_info.is_follow = 0;
              this.item.user_info.is_mutual_follow = 0;
            }
          }
          common_vendor.index.showToast({
            title: res.msg || "操作失败，请重试",
            icon: "none"
          });
        }
      }).catch((error) => {
        if (isFollowed) {
          this.item.is_follow = 1;
          if (isMutual) {
            this.item.is_mutual_follow = 1;
          }
          if (this.item.user_info) {
            this.item.user_info.is_follow = 1;
            if (isMutual) {
              this.item.user_info.is_mutual_follow = 1;
            }
          }
        } else {
          this.item.is_follow = 0;
          this.item.is_mutual_follow = 0;
          if (this.item.user_info) {
            this.item.user_info.is_follow = 0;
            this.item.user_info.is_mutual_follow = 0;
          }
        }
        this.handleError(error, "网络错误，请稍后重试");
      }).finally(() => {
        this.isProcessing = false;
      });
    },
    // 跳转到视频页面
    toVideoPage() {
      this.navigateToPage(this.getNoteUrl());
    },
    // 跳转到详情页面
    toDetailsPage() {
      this.navigateToPage("note/details?id=" + this.item.id);
    },
    // 统一的页面导航方法
    navigateToPage(url) {
      if (!this.item || !this.item.id || !url)
        return;
      try {
        common_vendor.index.navigateTo({
          url: "/pages/" + url,
          fail: (err) => {
            common_vendor.index.__f__("error", "at components/card-gg/card-gg.vue:1011", "页面导航失败:", err);
            common_vendor.index.switchTab({
              url: "/pages/index/index",
              fail: () => {
                common_vendor.index.__f__("error", "at components/card-gg/card-gg.vue:1016", "无法导航到页面");
              }
            });
          }
        });
      } catch (e) {
        common_vendor.index.__f__("error", "at components/card-gg/card-gg.vue:1022", "导航异常:", e);
      }
    },
    // 处理投票成功事件
    handleVoteSuccess(data) {
      this.item.vote_info = data.voteInfo;
      this.$emit("update", {
        vote_info: data.voteInfo,
        idx: this.idx
      });
    }
  },
  mounted() {
    if (!this.item)
      return;
    if (this.item.type === void 0) {
      if (this.item.video || this.item.video_url) {
        this.$set(this.item, "type", 3);
      } else if (this.item.audio || this.item.audio_cover) {
        this.$set(this.item, "type", 4);
      } else if (this.getImages && this.getImages.length > 0) {
        this.$set(this.item, "type", 2);
      } else {
        this.$set(this.item, "type", 1);
      }
    }
  },
  beforeCreate() {
    if (this.$options.propsData && !this.$options.propsData.item) {
      this.$options.propsData.item = {};
    }
  },
  errorCaptured(err, _, info) {
    return false;
  }
};
if (!Array) {
  const _component_lazyImage = common_vendor.resolveComponent("lazyImage");
  const _component_VoteComponent = common_vendor.resolveComponent("VoteComponent");
  (_component_lazyImage + _component_VoteComponent)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      src: $options.userAvatar,
      br: "50%"
    }),
    b: $options.displayState.isAuthenticated
  }, $options.displayState.isAuthenticated ? {
    c: common_assets._imports_0$5,
    d: common_vendor.o((...args) => $options.onAuthIconError && $options.onAuthIconError(...args))
  } : {}, {
    e: common_vendor.o((...args) => $options.toPaths && $options.toPaths(...args)),
    f: common_vendor.t($options.userName),
    g: $options.userVipInfo.showVip
  }, $options.userVipInfo.showVip ? {
    h: $options.userVipInfo.vipIcon
  } : {}, {
    i: $options.followButtonState.show
  }, $options.followButtonState.show ? {
    j: common_vendor.t($options.followButtonState.text),
    k: common_vendor.n($options.followButtonState.className),
    l: common_vendor.o((...args) => $options.followUser && $options.followUser(...args))
  } : {}, {
    m: common_vendor.o((...args) => $options.toPaths && $options.toPaths(...args)),
    n: common_vendor.t($options.timeDisplay),
    o: common_vendor.t($props.item.user_info.residence_name || ""),
    p: common_vendor.t($options.viewsCount),
    q: common_vendor.t($props.item.content),
    r: common_vendor.n($options.displayState.contentClass),
    s: common_vendor.o((...args) => $options.toDetailsPage && $options.toDetailsPage(...args)),
    t: $options.mediaState.showMedia
  }, $options.mediaState.showMedia ? common_vendor.e({
    v: $options.mediaState.isSingleImage
  }, $options.mediaState.isSingleImage ? {
    w: common_vendor.p({
      src: $options.getImages[0]
    }),
    x: common_vendor.n($options.mediaState.singleImageClass),
    y: common_vendor.o((...args) => $options.toVideoPage && $options.toVideoPage(...args))
  } : {}, {
    z: $options.mediaState.isMultiImage
  }, $options.mediaState.isMultiImage ? common_vendor.e({
    A: common_vendor.f($options.getImages, (img, index, i0) => {
      return {
        a: "4c608c11-2-" + i0,
        b: common_vendor.p({
          src: img
        }),
        c: index,
        d: common_vendor.o((...args) => $options.toVideoPage && $options.toVideoPage(...args), index)
      };
    }),
    B: $options.mediaState.showImageCount
  }, $options.mediaState.showImageCount ? {
    C: common_assets._imports_0$22,
    D: common_vendor.t($options.getImagesCount)
  } : {}) : {}, {
    E: $options.mediaState.isVideo
  }, $options.mediaState.isVideo ? {
    F: common_vendor.p({
      src: $options.videoCoverSrc
    }),
    G: common_vendor.n($options.mediaState.videoContainerClass),
    H: common_vendor.o((...args) => $options.toVideoPage && $options.toVideoPage(...args)),
    I: common_assets._imports_1$22,
    J: common_vendor.s($options.mediaState.videoIconStyle)
  } : {}, {
    K: $options.mediaState.isAudio
  }, $options.mediaState.isAudio ? {
    L: $options.audioCoverSrc,
    M: common_vendor.p({
      src: $options.audioCoverSrc
    }),
    N: common_assets._imports_2$5,
    O: common_vendor.t($options.audioInfo.title),
    P: common_vendor.t($options.audioInfo.intro),
    Q: common_vendor.o((...args) => $options.toVideoPage && $options.toVideoPage(...args))
  } : {}) : {}, {
    R: $options.displayState.showExtraInfo
  }, $options.displayState.showExtraInfo ? common_vendor.e({
    S: $options.contentChecks.hasLocation
  }, $options.contentChecks.hasLocation ? {
    T: common_assets._imports_3$6,
    U: common_vendor.t($options.locationName),
    V: common_vendor.o((...args) => $options.opLocationClick && $options.opLocationClick(...args))
  } : {}, {
    W: $options.contentChecks.hasCircle
  }, $options.contentChecks.hasCircle ? {
    X: $options.circleInfo.avatar,
    Y: common_vendor.t($options.circleInfo.name),
    Z: common_vendor.o((...args) => $options.toPaths && $options.toPaths(...args))
  } : {}, {
    aa: $options.contentChecks.hasTopic
  }, $options.contentChecks.hasTopic ? {
    ab: $options.topicsList[0] && $options.topicsList[0].icon ? $options.topicsList[0].icon : "/static/img/topic_icon.png",
    ac: common_vendor.t($options.topicTitle($options.topicsList[0])),
    ad: common_vendor.o((...args) => $options.toPaths && $options.toPaths(...args))
  } : {}, {
    ae: $options.contentChecks.hasActivity
  }, $options.contentChecks.hasActivity ? {
    af: common_vendor.p({
      src: $options.activityImage
    }),
    ag: common_vendor.t($options.activityName),
    ah: common_vendor.o((...args) => $options.toPaths && $options.toPaths(...args))
  } : {}, {
    ai: $options.contentChecks.hasProduct
  }, $options.contentChecks.hasProduct ? {
    aj: common_vendor.p({
      src: $options.productImage
    }),
    ak: common_vendor.t($options.productName),
    al: $options.productId,
    am: common_vendor.o((...args) => $options.toPaths && $options.toPaths(...args))
  } : {}) : {}, {
    an: $props.item.vote_info
  }, $props.item.vote_info ? {
    ao: common_vendor.o($options.handleVoteSuccess),
    ap: common_vendor.p({
      voteInfo: $props.item.vote_info
    })
  } : {}, {
    aq: $options.contentChecks.hasLatestComments
  }, $options.contentChecks.hasLatestComments ? {
    ar: common_vendor.t($options.commentInfo.latestUser),
    as: common_vendor.t($options.commentInfo.latestContent),
    at: common_vendor.o((...args) => $options.toPaths && $options.toPaths(...args))
  } : {}, {
    av: common_assets._imports_5$2,
    aw: common_vendor.t($options.commentCount),
    ax: common_vendor.o((...args) => $options.toPaths && $options.toPaths(...args)),
    ay: $data.isLiked
  }, $data.isLiked ? {
    az: common_assets._imports_6$3
  } : {
    aA: common_assets._imports_7$2
  }, {
    aB: common_vendor.t($options.likeState.countText),
    aC: common_vendor.s($options.likeState.textStyle),
    aD: common_vendor.o((...args) => $options.handleLike && $options.handleLike(...args)),
    aE: common_assets._imports_8$1,
    aF: common_vendor.o((...args) => $options.toPaths && $options.toPaths(...args)),
    aG: common_vendor.o((...args) => $options.toPaths && $options.toPaths(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/card-gg/card-gg.js.map
