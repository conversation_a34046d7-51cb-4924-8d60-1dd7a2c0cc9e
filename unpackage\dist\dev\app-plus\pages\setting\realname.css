
.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.page-wrapper[data-v-a3319d4f] {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background: #fff;
	position: relative;
}
.content-scroll[data-v-a3319d4f] {
	flex: 1;
	overflow-y: auto;
}
.container[data-v-a3319d4f] {
	width: calc(100% - 1.875rem);
	padding: 0 0.9375rem;
	background: #fff;
}
.title-box[data-v-a3319d4f] {
	padding: 0.625rem 0;
	font-size: 1.25rem;
	font-weight: 700;
}
.title-box uni-view[data-v-a3319d4f]:last-child {
	font-size: 0.75rem;
	color: #999;
	font-weight: normal;
	margin-top: 0.3125rem;
}
.desc[data-v-a3319d4f] {
	color: #888;
	font-size: 0.75rem;
	margin-bottom: 0.9375rem;
	line-height: 1.4;
}
.form-section[data-v-a3319d4f] {
	background: #fff;
	border-radius: 0.625rem;
	box-shadow: 0 0.0625rem 0.25rem rgba(0,0,0,0.03);
	padding: 0.625rem 0 0.3125rem 0;
	margin-bottom: 0.9375rem;
}
.title-label[data-v-a3319d4f] {
	width: calc(100% - 1.5rem);
	padding: 0.9375rem 0.75rem 0.375rem;
	color: #999;
	font-size: 0.75rem;
	font-weight: 700;
}
.input-box[data-v-a3319d4f] {
	width: calc(100% - 2.125rem);
	padding: 0 0.9375rem;
	height: 2.8125rem;
	line-height: 2.8125rem;
	font-size: 0.875rem;
	font-weight: 700;
	border: 0.125rem solid #f5f5f5;
	border-radius: 0.75rem;
	justify-content: space-between;
	margin-bottom: 0.625rem;
}
.input-box[disabled][data-v-a3319d4f] {
	background: #f8f8f8;
	color: #bbb;
}
.value[data-v-a3319d4f] {
	color: #333;
	font-size: 0.875rem;
}
.fail-reason[data-v-a3319d4f] {
	color: #fa5150;
	font-size: 0.75rem;
	margin: 0.3125rem 0.75rem 0.625rem;
	padding: 0.625rem;
	background: rgba(250, 81, 80, 0.1);
	border-radius: 0.375rem;
}
.success-msg[data-v-a3319d4f] {
	color: #4cd964;
	font-size: 0.75rem;
	margin: 0.3125rem 0.75rem 0.625rem;
	padding: 0.625rem;
	background: rgba(76, 217, 100, 0.1);
	border-radius: 0.375rem;
}
.pending-msg[data-v-a3319d4f] {
	color: #faad14;
	font-size: 0.75rem;
	margin: 0.3125rem 0.75rem 0.625rem;
	padding: 0.625rem;
	background: rgba(250, 173, 20, 0.1);
	border-radius: 0.375rem;
}
.protocol-row[data-v-a3319d4f] {
	display: flex;
	align-items: center;
	margin: 0.625rem 0;
	font-size: 0.75rem;
	color: #999;
}
.protocol-text[data-v-a3319d4f] {
	font-size: 0.75rem;
	color: #999;
}
.protocol-link[data-v-a3319d4f] {
	color: #576b95;
}

/* 底部按钮样式 */
.footer-box[data-v-a3319d4f] {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	padding-bottom: env(safe-area-inset-bottom);
	background: #fff;
	z-index: 99;
}
.footer-box .footer-item[data-v-a3319d4f] {
	width: calc(100% - 1.875rem);
	padding: 0.625rem 0.9375rem;
	justify-content: center;
	box-sizing: border-box;
}
.footer-item .btn[data-v-a3319d4f] {
	width: calc(100% - 0.9375rem);
	height: 2.8125rem;
	line-height: 2.8125rem;
	text-align: center;
	font-size: 0.875rem;
	font-weight: 700;
	border-radius: 1.40625rem;
}
.bg2[data-v-a3319d4f] {
	color: #fff;
	background: #000;
}
.bg2[disabled][data-v-a3319d4f] {
	background: #ccc !important;
	color: #fff !important;
	opacity: 0.6;
}
.btn-gray[data-v-a3319d4f] {
	background: #ccc !important;
	color: #fff !important;
	opacity: 0.6;
	cursor: not-allowed;
}
uni-button[disabled][data-v-a3319d4f] {
	background: #ccc !important;
	color: #fff !important;
	opacity: 0.6;
}
.cancel-section[data-v-a3319d4f] {
	margin: 0.625rem 0 1.25rem;
	width: 100%;
}
.cancel-btn[data-v-a3319d4f] {
	color: #666;
	background: #f5f5f5;
	border: 0.0625rem solid #ddd;
}
.cancel-btn[disabled][data-v-a3319d4f] {
	background: #f0f0f0;
	color: #ccc;
}
.bfw[data-v-a3319d4f] {
	background: #fff;
}
.bUp[data-v-a3319d4f] {
	box-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);
}

/* 提示弹窗样式 */
.tips-box[data-v-a3319d4f] {
	padding: 0.625rem 0.9375rem;
	border-radius: 0.375rem;
	justify-content: center;
	margin-top: 1.25rem;
}
.tips-box .tips-item[data-v-a3319d4f] {
	color: #fff;
	font-size: 0.875rem;
	font-weight: 600;
	text-align: center;
}
.df[data-v-a3319d4f] {
	display: flex;
	align-items: center;
}

/* 底部安全区域 */
.bottom-safe-area[data-v-a3319d4f] {
	height: 4.6875rem;
	width: 100%;
	margin-bottom: env(safe-area-inset-bottom);
}

/* 输入框样式优化 */
uni-input[data-v-a3319d4f] {
	border: none;
	background: transparent;
	outline: none;
	width: 100%;
}
uni-input[disabled][data-v-a3319d4f] {
	color: #bbb;
}

/* 复选框样式 */
uni-checkbox[data-v-a3319d4f] {
	transform: scale(0.8);
}

/* 手机号绑定按钮样式 */
.input-btn[data-v-a3319d4f] {
	width: 2.8125rem;
	height: 2.8125rem;
	font-size: 0.75rem;
	justify-content: space-between;
	margin: 0;
	padding: 0;
	background: #fff;
}
.input-btn uni-image[data-v-a3319d4f] {
	margin-right: 0.25rem;
	width: 1rem;
	height: 1rem;
}
.input-tips[data-v-a3319d4f] {
	color: #999;
	font-size: 0.75rem;
	margin-bottom: 0.625rem;
}

/* Adjust the protocol row spacing */
.protocol-row[data-v-a3319d4f] {
	display: flex;
	align-items: center;
	margin: 0.625rem 0;
	font-size: 0.75rem;
	color: #999;
}

/* Ensure the footer button has proper spacing */
.footer-box .footer-item[data-v-a3319d4f] {
	width: calc(100%);
	padding: 0.625rem 0.9375rem;
	justify-content: center;
	box-sizing: border-box;
}

/* Add box shadow to make the footer stand out */
.bUp[data-v-a3319d4f] {
	box-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);
}
