
.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.lazy-image[data-v-337fa078]{position:relative;width:100%;height:100%;overflow:hidden;background:#f8f8f8;}
.lazy-image uni-image[data-v-337fa078]{width:100%;height:100%;}
.lazy-image .err[data-v-337fa078]{position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(248,248,248,0.7) url('../../static/img/load.png') no-repeat center/50%;}


.uni-load-more[data-v-9245e42c] {
  display: flex;
  flex-direction: row;
  height: 1.875rem;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-9245e42c] {
  font-size: 0.75rem;
  margin-left: 0.5rem;
}
.uni-load-more__img[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
}
.uni-load-more__img--nvue[data-v-9245e42c] {
  color: #999;
}
.uni-load-more__img--android[data-v-9245e42c],
.uni-load-more__img--ios[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
}
.uni-load-more__img--android[data-v-9245e42c] {
  animation: loading-ios 1s 0s linear infinite;
}
.uni-load-more__img--ios-H5[data-v-9245e42c] {
  position: relative;
  animation: loading-ios-H5-9245e42c 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-9245e42c] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-9245e42c {
0% {
    transform: rotate(0);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
to {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-9245e42c] {
  position: relative;
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 0.125rem transparent;
  border-top: solid 0.125rem #999;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(1) {
  animation: loading-android-MP-1-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(2) {
  animation: loading-android-MP-2-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(3) {
  animation: loading-android-MP-3-9245e42c 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(90deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(180deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(270deg);
}
to {
    transform: rotate(360deg);
}
}


.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.bar-box .bar-back {
  padding: 0 0.9375rem;
  width: 1.0625rem;
  height: 100%;
}
.bar-box .bar-title {
  max-width: 60%;
  font-size: 1rem;
  font-weight: 700;
}
.nav-box {
  width: 100%;
  height: 2.5rem;
}
.nav-box .nav-item {
  padding: 0 0.9375rem;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.nav-box .nav-item uni-text {
  font-weight: 700;
  transition: all .3s ease-in-out;
}
.nav-box .nav-line {
  position: absolute;
  bottom: 0.375rem;
  width: 0.5625rem;
  height: 0.1875rem;
  border-radius: 0.1875rem;
  background: #000;
  transition: opacity .3s ease-in-out;
}
.content-box {
  width: calc(100% - 1.875rem);
  padding: 0.9375rem;
}

/* 加载中状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 1.875rem;
  margin-bottom: 0.625rem;
}
.loading-indicator {
  width: 0.9375rem;
  height: 0.9375rem;
  border: 0.09375rem solid #f3f3f3;
  border-top: 0.09375rem solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.list-box {
  width: 100%;
  padding-bottom: 0.9375rem;
  justify-content: space-between;
}
.list-box .list-avatar {
  width: 5.25rem;
  height: 5.25rem;
  border-radius: 50%;
  background: #f8f8f8;
  border: 1px solid #f8f8f8;
  position: relative;
}
.list-avatar .tag {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 0.1875rem solid #fff;
}
.list-box .list-item {
  width: calc(100% - 6.1875rem);
  margin-left: 0.9375rem;
}
.list-item .name {
  font-size: 1rem;
  font-weight: 700;
}
.list-item .intro {
  margin: 0.3125rem 0 0.625rem;
  color: #999;
  font-size: 0.75rem;
}
.cu-img-group {
  direction: ltr;
  unicode-bidi: bidi-override;
  display: inline-block;
  margin-left: 0.5rem;
}
.cu-img-group .cu-img {
  width: 1rem;
  height: 1rem;
  display: inline-flex;
  position: relative;
  margin-left: -0.5rem;
  border: 0.125rem solid #fff;
  background: #f8f8f8;
  vertical-align: middle;
  border-radius: 50%;
}
.cu-img-group .cu-img uni-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.cu-img-group .cu-txt {
  margin-left: 0.3125rem;
  display: inline-flex;
  color: #999;
  font-size: 0.625rem;
  font-weight: 700;
}
.view-count {
  display: inline-flex;
  color: #999;
  font-size: 0.625rem;
  font-weight: 500;
  margin-left: 0.125rem;
}
.df {
  display: flex;
  align-items: center;
}
.bfw {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,.8);
}
.bfh {
  background: #000;
  color: #fff;
  padding: 0.625rem 1.25rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 700;
}
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-box uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.9375rem;
}
.empty-box .e1 {
  font-size: 0.875rem;
  font-weight: bold;
  margin-bottom: 0.3125rem;
}
.empty-box .e2 {
  font-size: 0.75rem;
  color: #999;
}
.tips-box {
  justify-content: center;
  width: 100%;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  word-break: break-all;
}
.heio {
  width: 100%;
  overflow: hidden;
  transition: height 0.3s;
  justify-content: center;
}
.tags-box {
  display: flex;
  flex-wrap: wrap;
  margin: 0.1875rem 0;
}
.tag-item {
  font-size: 0.625rem;
  color: #666;
  margin-right: 0.375rem;
  background: #f5f5f5;
  padding: 0.125rem 0.375rem;
  border-radius: 0.3125rem;
}
.recent-topics {
  margin-top: 0.25rem;
}
.recent-title {
  font-size: 0.625rem;
  color: #666;
  margin-bottom: 0.125rem;
}
.topic-item {
  font-size: 0.625rem;
  color: #666;
  background: #f8f8f8;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  margin-bottom: 0.125rem;
  margin-right: 0.25rem;
  display: inline-block;
}
