
/* 基础样式 */
.container {
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
  box-sizing: border-box;
}
.nav-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.nav-box .nav-back {
  padding: 0 30rpx;
  width: 34rpx;
  height: 100%;
}
.nav-box .nav-title {
  max-width: 60%;
  font-size: 32rpx;
  font-weight: 700;
}

/* 话题头部区域 */
.topic-header {
  position: relative;
  overflow: hidden;
}

/* 主背景容器 */
.images-box {
  width: 100%;
  flex-direction: column;
  position: relative;
  background: #f8f8f8;
  border-radius: 0 0 24rpx 24rpx;
  overflow: hidden;
}

/* 话题背景图片容器 */
.topic-image-container {
  position: relative;
  width: 100%;
  height: 400rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

/* 话题背景图片 */
.topic-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

/* 图片点击效果 */
.topic-image-container:active .topic-bg-image {
  transform: scale(0.98);
}

/* 背景遮罩层 */
.bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(to bottom,
      transparent 0%,
      rgba(0,0,0,0.1) 30%,
      rgba(0,0,0,0.4) 70%,
      rgba(0,0,0,0.8) 100%
    ),
    radial-gradient(circle at center bottom,
      rgba(0,0,0,0.2) 0%,
      rgba(0,0,0,0.6) 70%,
      rgba(0,0,0,0.9) 100%
    );

  backdrop-filter: blur(1px) saturate(180%);
  -webkit-backdrop-filter: blur(1px) saturate(180%);

  box-shadow:
    inset 0 -80rpx 120rpx -40rpx rgba(0,0,0,0.4),
    inset 0 80rpx 120rpx -40rpx rgba(0,0,0,0.1);

  transition: all 0.3s ease-out;
}

/* 话题信息叠加层 */
.topic-info-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 40rpx 30rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.topic-main-info {
  align-items: flex-start;
  margin-bottom: 20rpx;
  position: relative;
}

/* 话题图标 */
.topic-icon {
  margin-right: 20rpx;
}
.topic-icon-img {
  width: 60rpx;
  height: 60rpx;
  filter: brightness(0) invert(1);
}

/* 话题详情 */
.topic-details {
  flex: 1;
}
.topic-name {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}



/* 话题描述 */
.topic-description {
  color: rgba(255,255,255,0.9);
  font-size: 24rpx;
  line-height: 1.4;
  margin-bottom: 15rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

/* 话题分享按钮 */
.topic-share-btn {
  position: absolute;
  top: 0;
  right: 0;
}
.share-btn-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255,255,255,0.2);
  border: 2rpx solid rgba(255,255,255,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}
.share-btn-wrapper:active {
  transform: scale(0.95);
  background: rgba(255,255,255,0.3);
}
.share-icon {
  width: 36rpx;
  height: 36rpx;
  filter: brightness(0) invert(1);
}



/* 骨架屏样式 */
.skeleton-text {
  width: 200rpx;
  height: 36rpx;
  background: linear-gradient(90deg, rgba(255,255,255,0.3) 25%, rgba(255,255,255,0.6) 50%, rgba(255,255,255,0.3) 75%);
  background-size: 200% 100%;
  border-radius: 6rpx;
  animation: skeletonLoading 1.5s infinite;
}
.skeleton-stats {
  display: flex;
  gap: 16rpx;
}
.skeleton-item {
  width: 80rpx;
  height: 22rpx;
  background: linear-gradient(90deg, rgba(255,255,255,0.3) 25%, rgba(255,255,255,0.6) 50%, rgba(255,255,255,0.3) 75%);
  background-size: 200% 100%;
  border-radius: 4rpx;
  animation: skeletonLoading 1.5s infinite;
}
@keyframes skeletonLoading {
0% {
    background-position: -200% 0;
}
100% {
    background-position: 200% 0;
}
}

/* 分类标签 */
.bar-box {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 98;
  width: 100%;
  height: 80rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  margin-top: -24rpx;
  border-radius: 24rpx 24rpx 0 0;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;
  overflow: hidden;
}
.bar-left {
  flex: 1;
  overflow: hidden;
  min-width: 0;
}
.bar-box .bar-item {
  padding: 0 30rpx 0 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  min-width: 0;
}
.bar-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.bar-item-content text {
  font-weight: 700;
  transition: all 0.3s ease-in-out;
  margin-bottom: 8rpx;
}
.bar-line {
  width: 18rpx;
  height: 6rpx;
  border-radius: 6rpx;
  background: #000;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}
.bar-line.active {
  opacity: 1;
}

/* 话题统计信息 */
.topic-stats-right {
  color: #999;
  font-size: 24rpx;
  line-height: 1.4;
}
.topic-stats-right .stat-item {
  font-size: 24rpx;
  font-weight: 400;
}
.topic-stats-right .stat-divider {
  margin: 0 8rpx;
  font-size: 24rpx;
}

/* 内容区域 */
.content-area {
  min-height: 600rpx;
  background: #fff;
}
.dynamic-box {
  width: calc(100% - 16rpx);
  padding: 22rpx 8rpx 0;
}

/* 加载中状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60rpx;
  margin: 20rpx 0;
}
.loading-indicator {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}

/* 空状态 */
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-box image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-box .e1 {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.empty-box .e2 {
  font-size: 24rpx;
  color: #999;
}

/* 提示弹窗 */
.tips-box {
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
  justify-content: center;
}
.tips-box .tips-item {
  color: #fff;
  font-size: 28rpx;
  font-weight: 700;
}

/* 工具类 */
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
