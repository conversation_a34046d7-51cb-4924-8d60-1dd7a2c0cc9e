<view class="container"><view class="nav-box bfw df" style="{{'padding-top:' + d}}"><view class="nav-back df" style="{{'height:' + b}}" bindtap="{{c}}"><image src="{{a}}" style="width:34rpx;height:34rpx"></image></view><view class="nav-title">购物车</view></view><view class="content" style="{{'padding-top:' + r}}"><view wx:if="{{e}}" class="empty-box df"><image src="{{f}}"/><view class="e1">购物车空空的</view><view class="e2">去挑点喜欢的商品装满购物车</view></view><view wx:else class="list-box"><view wx:for="{{g}}" wx:for-item="item" wx:key="G" class="list-item df"><view class="choose df" bindtap="{{item.b}}" data-check="{{item.c}}" data-id="{{item.d}}"><image src="{{item.a}}"></image></view><view class="item-img df" bindtap="{{item.h}}" data-url="{{item.i}}" data-pay="false"><image src="{{item.e}}" mode="aspectFill"></image><view wx:if="{{item.f}}" class="sold-out df">{{item.g}}</view></view><view class="info"><view class="info-sp" style="height:calc(100% - 60rpx)"><view style="width:calc(100% - 84rpx)"><view class="txt ohto">{{item.j}}</view><view class="df" bindtap="{{item.l}}" data-id="{{item.m}}"><view class="txt-box">{{item.k}} <view class="dw df"><image src="{{h}}" style="width:20rpx;height:20rpx"></image></view></view></view></view><view class="more" bindtap="{{item.n}}" data-id="{{item.o}}"><image src="{{i}}" style="width:24rpx;height:24rpx"></image></view></view><view class="info-sp" style="align-items:flex-end"><money wx:if="{{item.q}}" u-i="{{item.p}}" bind:__l="__l" u-p="{{item.q}}"></money><view wx:if="{{item.r}}" class="quantity-box df"><view class="quantity-btn" style="{{'color:' + item.s}}" bindtap="{{item.t}}" data-id="{{item.v}}" data-type="0">－</view><input bindblur="{{item.w}}" data-id="{{item.x}}" data-type="2" type="number" maxlength="4" value="{{item.y}}" bindinput="{{item.z}}"/><view class="quantity-btn" style="{{'color:' + item.A}}" bindtap="{{item.B}}" data-id="{{item.C}}" data-type="1">＋</view></view><view wx:else class="quantity-box df" bindtap="{{item.E}}" data-id="{{item.F}}"><view style="width:80rpx;text-align:center">{{item.D}}</view></view></view></view></view><view class="list-count df" style="margin-top:30rpx"><text class="t1">实际商品总额</text><view class="df"><text class="t3">已选 {{j}} 件</text><money wx:if="{{k}}" u-i="6dc304c3-1" bind:__l="__l" u-p="{{k}}"></money></view></view><view class="list-count df"><text class="t1">优惠金额</text><view class="df"><text class="t3">立减</text><money wx:if="{{l}}" u-i="6dc304c3-2" bind:__l="__l" u-p="{{l}}"></money></view></view><view wx:if="{{m}}" class="list-count df"><text class="t1">卡券</text><view class="df"><text class="t3" style="{{'color:' + n}}">优惠</text><money wx:if="{{o}}" u-i="6dc304c3-3" bind:__l="__l" u-p="{{o}}"></money></view></view><view class="list-count df"><text class="t1">运费</text><view class="df"><text class="t3">包邮</text><money wx:if="{{p}}" u-i="6dc304c3-4" bind:__l="__l" u-p="{{p}}"></money></view></view><view class="list-count df"><text class="t2">总计</text><money wx:if="{{q}}" u-i="6dc304c3-5" bind:__l="__l" u-p="{{q}}"></money></view></view></view><view wx:if="{{s}}" class="footer-box bUp df"><view class="footer-all df" bindtap="{{v}}" data-check="{{w}}" data-id="-1"><image src="{{t}}"></image><text>全选</text></view><view class="{{['btn', 'not', z]}}" bindtap="{{A}}" data-url="order/settlement?type=0" data-pay="true"><text wx:if="{{x}}">¥ {{y}}</text> 结算 </view></view><uni-popup wx:if="{{K}}" class="r" u-s="{{['d']}}" u-r="goodsPopup" u-i="6dc304c3-6" bind:__l="__l" u-p="{{K}}"><view class="goods-box"><view class="goods-top df"><view class="popup-name">{{B}}</view><view class="popup-close df" bindtap="{{D}}"><image src="{{C}}" style="width:20rpx;height:20rpx"></image></view></view><view class="specs-title">规格</view><view class="specs-overflow"><view class="specs-box" style="display:flex;padding:0 10rpx"><view wx:for="{{E}}" wx:for-item="item" wx:key="f" class="specs-item" style="{{'border-color:' + item.g}}" bindtap="{{item.h}}" data-idx="{{item.i}}"><image class="img" src="{{item.a}}" mode="aspectFill"></image><view class="fd df" catchtap="{{item.b}}" data-url="{{item.c}}"><image src="{{F}}" style="width:22rpx;height:22rpx"></image></view><view class="name"><view>{{item.d}}</view><view style="margin-top:10rpx">¥ {{item.e}}</view></view></view><view style="flex-shrink:0;width:20rpx;height:20rpx"></view></view></view><view class="{{['btn', 'gtn', H]}}" bindtap="{{I}}">{{G}}</view></view></uni-popup><uni-popup wx:if="{{P}}" class="r" u-s="{{['d']}}" u-r="morePopup" u-i="6dc304c3-7" bind:__l="__l" u-p="{{P}}"><view class="popup-box"><view class="popup-item df" bindtap="{{M}}"><text style="color:#FA5150">删除</text><image src="{{L}}"></image></view><view class="popup-item" bindtap="{{N}}"><text style="color:#999">取消</text></view></view></uni-popup><uni-popup wx:if="{{S}}" class="r" u-s="{{['d']}}" u-r="tipsPopup" u-i="6dc304c3-8" bind:__l="__l" u-p="{{S}}"><view class="tips-box df"><view class="tips-item">{{Q}}</view></view></uni-popup></view>