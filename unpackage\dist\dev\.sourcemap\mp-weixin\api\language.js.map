{"version": 3, "file": "language.js", "sources": ["api/language.js"], "sourcesContent": ["import request from '@/utils/request'\n\n/**\n * 获取语言列表\n */\nexport function getLangList() {\n  return request({\n    url: '/api/lang/list',\n    method: 'GET'\n  })\n}\n\n/**\n * 获取语言包数据\n * @param {string} lang 语言代码\n */\nexport function getLangJson(lang) {\n  return request({\n    url: '/api/lang/json',\n    method: 'GET',\n    params: {\n      lang: lang || ''\n    }\n  })\n}\n\n/**\n * 设置用户语言偏好\n * @param {string} lang 语言代码\n */\nexport function setUserLang(lang) {\n  return request({\n    url: '/api/user/lang',\n    method: 'POST',\n    data: {\n      lang\n    }\n  })\n}\n"], "names": ["request"], "mappings": ";;AAKO,SAAS,cAAc;AAC5B,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,EACZ,CAAG;AACH;AAMO,SAAS,YAAY,MAAM;AAChC,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN,MAAM,QAAQ;AAAA,IACf;AAAA,EACL,CAAG;AACH;;;"}