
.waterfall-box.data-v-8e26fd7f {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 8rpx;
  box-sizing: border-box;
}
.waterfall-item.data-v-8e26fd7f {
  width: calc(50% - 4rpx);
}
.waterfall-note.data-v-8e26fd7f {
  margin-bottom: 8rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.03);
}
.waterfall-note .waterfall-note-top.data-v-8e26fd7f {
  width: 100%;
  position: relative;
  overflow: hidden;
  background: #f8f8f8;
}
.waterfall-note-top.text-only.data-v-8e26fd7f {
  background: #f8f8f8;
  padding: 16rpx;
  box-sizing: border-box;
  width: 100%;
  border-radius: 8rpx 8rpx 0 0;
  display: flex;
  flex-direction: column;
}
.text-content.data-v-8e26fd7f {
  margin: 0 !important;
  font-size: 26rpx;
  line-height: 36rpx;
  color: #333;
  word-break: break-word;
  white-space: pre-wrap;
}
.waterfall-note-top .xxiv.data-v-8e26fd7f,
.waterfall-note-top .xxa .xxa-icon.data-v-8e26fd7f {
  filter: drop-shadow(0 2rpx 2rpx rgba(0, 0, 0, 0.2));
}
.waterfall-note-top .xxiv.data-v-8e26fd7f {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 28rpx;
  height: 28rpx;
}
.waterfall-note-top .xxa.data-v-8e26fd7f {
  width: calc(100% - 64rpx);
  height: calc(100% - 64rpx);
  padding: 32rpx;
  position: relative;
  z-index: 1;
  color: #fff;
}
.waterfall-note-top .xxa .xxa-bg.data-v-8e26fd7f {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
  object-fit: cover;
}
.waterfall-note-top .xxa .xxa-mb.data-v-8e26fd7f {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  -webkit-backdrop-filter: saturate(150%) blur(25px);
  backdrop-filter: saturate(150%) blur(25px);
  background: rgba(0, 0, 0, 0.5);
}
.waterfall-note-top .xxa .xxa-top.data-v-8e26fd7f {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
}
.waterfall-note-top .xxa .xxa-top-img.data-v-8e26fd7f {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  object-fit: cover;
}
.waterfall-note-top .xxa .xxa-icon.data-v-8e26fd7f {
  position: absolute;
  top: 35rpx;
  right: 35rpx;
  bottom: 35rpx;
  left: 35rpx;
  width: 50rpx;
  height: 50rpx;
}
.waterfall-note-top .xxa .xxa-t.data-v-8e26fd7f {
  margin-top: 32rpx;
  font-size: 26rpx;
  font-weight: 700;
}
.waterfall-note-top .xxa .xxa-tt.data-v-8e26fd7f {
  margin: 8rpx 0 32rpx;
  opacity: 0.8;
  font-size: 20rpx;
}
.waterfall-note-top .xxa .xxa-play.data-v-8e26fd7f {
  width: 100%;
  height: 60rpx;
  font-size: 18rpx;
  font-weight: 700;
  border-radius: 60rpx;
  justify-content: center;
  background: rgba(255, 255, 255, 0.15);
}
.waterfall-note-top .xxa .xxa-play image.data-v-8e26fd7f {
  margin-right: 8rpx;
  width: 16rpx;
  height: 16rpx;
}
.waterfall-note-top .xxzd.data-v-8e26fd7f {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  width: 52rpx;
  height: 32rpx;
  color: #fff;
  font-size: 16rpx;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.16);
  border-radius: 8rpx;
}
.waterfall-note .waterfall-note-content.data-v-8e26fd7f {
  width: calc(100% - 32rpx);
  margin: 12rpx 16rpx;
  font-size: 26rpx;
  line-height: 36rpx;
  word-break: break-word;
  white-space: pre-line;
  color: #333;
}
.waterfall-note .waterfall-note-bottom.data-v-8e26fd7f {
  width: calc(100% - 32rpx);
  margin: 0 16rpx;
  height: 60rpx;
  justify-content: space-between;
}
.waterfall-note-bottom .waterfall-note-user.data-v-8e26fd7f {
  display: flex;
  align-items: center;
}
.waterfall-note-bottom .waterfall-note-user image.data-v-8e26fd7f {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
}
.waterfall-note-bottom .waterfall-note-like image.data-v-8e26fd7f {
  width: 28rpx;
  height: 28rpx;
}
.waterfall-note .waterfall-note-top.data-v-8e26fd7f,
.waterfall-note-bottom .waterfall-note-user image.data-v-8e26fd7f,
.waterfall-activity .waterfall-activity-item.data-v-8e26fd7f,
.waterfall-activity .big.data-v-8e26fd7f,
.wlc10.data-v-8e26fd7f {
  background: #f8f8f8;
}
.waterfall-note-bottom .waterfall-note-user view.data-v-8e26fd7f,
.waterfall-note-bottom .waterfall-note-like text.data-v-8e26fd7f {
  margin-left: 8rpx;
  line-height: 32rpx;
}
.waterfall-note-bottom .waterfall-note-user view.data-v-8e26fd7f {
  color: #333;
  max-width: 140rpx;
  font-size: 20rpx;
}
.waterfall-note-bottom .waterfall-note-like text.data-v-8e26fd7f {
  color: #999;
  font-size: 20rpx;
}
.wlc1.data-v-8e26fd7f {
  -webkit-line-clamp: 1 !important;
}
.ohto.data-v-8e26fd7f {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2.data-v-8e26fd7f {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.df.data-v-8e26fd7f {
  display: flex;
  align-items: center;
}

/* 活动区域样式调整 */
.waterfall-activity.data-v-8e26fd7f {
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}
.waterfall-activity-item.data-v-8e26fd7f {
  border-radius: 8rpx 8rpx 0 0;
  overflow: hidden;
}
.waterfall-activity-img.data-v-8e26fd7f {
  position: relative;
  border-radius: 8rpx 8rpx 0 0;
  overflow: hidden;
}
.waterfall-activity .big.data-v-8e26fd7f {
  height: 72rpx;
  border-radius: 0 0 8rpx 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
  justify-content: center;
}

/* 图片类卡片的图片样式 */
.lazy-image.data-v-8e26fd7f {
  border-radius: 8rpx 8rpx 0 0;
  overflow: hidden;
}

/* 活动区域更详细样式（从原始组件复制） */
.waterfall-activity .waterfall-activity-item.data-v-8e26fd7f {
  height: 470rpx;
  overflow: hidden;
}
.waterfall-activity-item .waterfall-activity-img.data-v-8e26fd7f {
  margin-bottom: 16rpx;
  width: 100%;
  height: 290rpx;
  position: relative;
}
.waterfall-activity-img .zt.data-v-8e26fd7f {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  width: 68rpx;
  height: 38rpx;
  color: #fff;
  font-size: 16rpx;
  font-weight: 700;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.16);
  border-radius: 8rpx;
  justify-content: center;
}
.waterfall-activity-img .xxbt.data-v-8e26fd7f {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 24rpx 0 8rpx;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
}
.waterfall-activity-img .waterfall-activity-name.data-v-8e26fd7f,
.waterfall-activity-item .waterfall-activity-tag.data-v-8e26fd7f {
  width: calc(100% - 32rpx);
  padding: 0 16rpx 8rpx;
}
.waterfall-activity-img .waterfall-activity-name.data-v-8e26fd7f {
  color: #fff;
  font-size: 24rpx;
  font-weight: 700;
}
.waterfall-activity-item .waterfall-activity-tag image.data-v-8e26fd7f,
.waterfall-activity .waterfall-activity-btn image.data-v-8e26fd7f {
  width: 20rpx;
  height: 20rpx;
}
.waterfall-activity-item .waterfall-activity-tag view.data-v-8e26fd7f {
  width: calc(100% - 28rpx);
  color: #999;
  font-size: 20rpx;
  font-weight: 500;
}
.waterfall-activity-group.data-v-8e26fd7f {
  margin-left: 31rpx;
  direction: ltr;
  unicode-bidi: bidi-override;
  display: inline-block;
}
.waterfall-activity-group .group-img.data-v-8e26fd7f {
  width: 32rpx;
  height: 32rpx;
  display: inline-flex;
  position: relative;
  margin-left: -16rpx;
  border: 2rpx solid #f8f8f8;
  background: #fff;
  vertical-align: middle;
  border-radius: 50%;
}
.waterfall-activity-group .group-img image.data-v-8e26fd7f {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.waterfall-activity-group .group-tit.data-v-8e26fd7f {
  display: inline-flex;
  margin-left: 8rpx;
  color: #999;
  font-size: 20rpx;
  font-weight: 500;
}
.waterfall-activity .waterfall-activity-btn.data-v-8e26fd7f {
  margin-top: 8rpx;
  font-weight: 700;
  justify-content: center;
}
.waterfall-activity .big.data-v-8e26fd7f {
  width: 100%;
  font-size: 22rpx;
  height: 70rpx;
}
.waterfall-activity .small.data-v-8e26fd7f {
  position: absolute;
  left: 16rpx;
  bottom: 16rpx;
  font-size: 20rpx;
  width: calc(100% - 32rpx);
  height: 60rpx;
  background: #fff;
}
