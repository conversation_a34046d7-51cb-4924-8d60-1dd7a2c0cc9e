{"version": 3, "file": "publish-tree-hole-simple.js", "sources": ["pages/note/publish-tree-hole-simple.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbm90ZS9wdWJsaXNoLXRyZWUtaG9sZS1zaW1wbGUudnVl"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 顶部导航 -->\n    <view class=\"nav-bar\">\n      <view class=\"nav-back\" @tap=\"goBack\">\n        <text>←</text>\n      </view>\n      <text class=\"nav-title\">放入纸条</text>\n      <view class=\"nav-submit\" @tap=\"publishBox\">\n        <text>{{isSubmitting ? '发布中...' : '发布'}}</text>\n      </view>\n    </view>\n    \n\n    <!-- 内容区域 -->\n    <view class=\"form-container\">\n      <!-- 类型选择 -->\n      <view class=\"form-item\">\n        <view class=\"form-label\">纸条类型</view>\n        <view class=\"type-list\">\n          <view \n            v-for=\"item in typeList\" \n            :key=\"item.value\"\n            class=\"type-item\"\n            :class=\"{'active': currentType === item.value}\"\n            @tap=\"selectType(item.value)\"\n          >\n            <text class=\"type-icon\">{{item.icon}}</text>\n            <text class=\"type-name\">{{item.name}}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 内容输入 -->\n      <view class=\"form-item\">\n        <view class=\"form-label\">纸条内容</view>\n        <textarea \n          v-if=\"currentType !== 4\"\n          class=\"form-textarea\" \n          placeholder=\"写下你想说的话...\" \n          maxlength=\"500\" \n          v-model=\"content\" \n        />\n        <view v-else class=\"voice-section\">\n          <text>语音功能开发中...</text>\n        </view>\n        <view v-if=\"currentType !== 4\" class=\"form-count\">{{content.length}}/500</view>\n      </view>\n      \n      <!-- 匿名设置 -->\n      <view class=\"form-item\">\n        <view class=\"form-label\">发布设置</view>\n        <view class=\"setting-row\" @tap=\"toggleAnonymous\">\n          <text class=\"setting-text\">匿名发布</text>\n          <view class=\"switch\" :class=\"{'active': isAnonymous}\">\n            <view class=\"switch-dot\"></view>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { publishTreeHoleBox } from '@/api/social'\nimport { checkLogin, toLogin } from '@/libs/login.js'\n\nexport default {\n  data() {\n    return {\n      currentType: 1,\n      content: '',\n      isAnonymous: true,\n      isSubmitting: false,\n      typeList: [\n        { value: 1, name: '问题咨询', icon: '❓' },\n        { value: 2, name: '秘密', icon: '🤫' },\n        { value: 3, name: '心愿', icon: '🌠' },\n        { value: 4, name: '语音纸条', icon: '🎵' }\n      ],\n      // 调试信息\n      debugInfo: {\n        checkLoginResult: false,\n        storeToken: '',\n        localToken: '',\n        userInfo: null,\n        finalLoginStatus: false,\n        apiStatus: '未调用',\n        lastError: ''\n      }\n    }\n  },\n  onLoad() {\n    // 打印登录状态和token调试信息\n    this.printLoginDebugInfo()\n\n    if (!this.checkLoginStatus()) {\n      return\n    }\n  },\n  methods: {\n    // 打印登录调试信息\n    printLoginDebugInfo() {\n      console.log('=== 登录状态调试信息 ===')\n\n      // 检查checkLogin函数\n      const isLoginCheck = checkLogin()\n      this.debugInfo.checkLoginResult = isLoginCheck\n      console.log('checkLogin()结果:', isLoginCheck)\n\n      // 检查store中的token\n      let storeToken = null\n      try {\n        if (this.$store && this.$store.state && this.$store.state.app) {\n          storeToken = this.$store.state.app.token\n          this.debugInfo.storeToken = storeToken || '空'\n          console.log('$store.state.app.token:', storeToken)\n        } else {\n          this.debugInfo.storeToken = 'store不存在'\n          console.log('$store.state.app 不存在')\n        }\n      } catch (e) {\n        this.debugInfo.storeToken = '获取失败: ' + e.message\n        console.log('获取store token失败:', e)\n      }\n\n      // 检查本地存储的token\n      try {\n        const localToken = uni.getStorageSync('token')\n        this.debugInfo.localToken = localToken || '空'\n        console.log('本地存储token:', localToken)\n      } catch (e) {\n        this.debugInfo.localToken = '获取失败: ' + e.message\n        console.log('获取本地token失败:', e)\n      }\n\n      // 检查LOGIN_STATUS_TOKEN\n      try {\n        const loginStatusToken = uni.getStorageSync('LOGIN_STATUS_TOKEN')\n        console.log('LOGIN_STATUS_TOKEN:', loginStatusToken)\n      } catch (e) {\n        console.log('获取LOGIN_STATUS_TOKEN失败:', e)\n      }\n\n      // 检查用户信息\n      try {\n        const userInfo = uni.getStorageSync('userInfo')\n        this.debugInfo.userInfo = userInfo\n        console.log('本地用户信息:', userInfo)\n      } catch (e) {\n        this.debugInfo.userInfo = null\n        console.log('获取用户信息失败:', e)\n      }\n\n      // 最终登录状态\n      const finalLoginStatus = isLoginCheck && storeToken\n      this.debugInfo.finalLoginStatus = finalLoginStatus\n      console.log('最终登录状态:', finalLoginStatus)\n      console.log('=== 调试信息结束 ===')\n    },\n\n    checkLoginStatus() {\n      const isLoggedIn = checkLogin()\n      \n      if (!isLoggedIn) {\n        uni.showModal({\n          title: '提示',\n          content: '请先登录后再发布纸条',\n          confirmText: '去登录',\n          cancelText: '返回',\n          success: (res) => {\n            if (res.confirm) {\n              toLogin()\n            } else {\n              uni.navigateBack()\n            }\n          }\n        })\n        return false\n      }\n      \n      return true\n    },\n    \n    goBack() {\n      uni.navigateBack()\n    },\n    \n    selectType(type) {\n      this.currentType = type\n      this.content = ''\n    },\n    \n    toggleAnonymous() {\n      this.isAnonymous = !this.isAnonymous\n    },\n    \n    publishBox() {\n      console.log('=== 发布纸条调试信息 ===')\n\n      // 更新调试信息\n      this.printLoginDebugInfo()\n\n      // 再次检查登录状态\n      const isLoginCheck = checkLogin()\n      const storeToken = this.$store?.state?.app?.token\n      console.log('发布时登录状态:', isLoginCheck)\n      console.log('发布时token:', storeToken)\n\n      if (!this.validateContent()) {\n        return\n      }\n\n      this.isSubmitting = true\n      this.debugInfo.apiStatus = '准备调用API'\n      \n      const data = {\n        type: this.currentType,\n        content: this.currentType === 4 ? '' : this.content.trim(),\n        voice_url: '',\n        voice_duration: 0,\n        is_anonymous: this.isAnonymous ? 1 : 0\n      }\n\n      console.log('发布数据:', data)\n      console.log('调用API: publishTreeHoleBox')\n      this.debugInfo.apiStatus = '正在调用API...'\n\n      publishTreeHoleBox(data).then(res => {\n        console.log('API响应:', res)\n        this.isSubmitting = false\n        this.debugInfo.apiStatus = `API成功: ${res.status}`\n\n        if (res.status === 200) {\n          this.debugInfo.apiStatus = 'API成功: 200'\n          uni.showToast({\n            title: '发布成功',\n            icon: 'success'\n          })\n\n          setTimeout(() => {\n            uni.navigateBack()\n          }, 1500)\n        } else {\n          this.debugInfo.apiStatus = `API失败: ${res.status} - ${res.msg}`\n          uni.showToast({\n            title: res.msg || '发布失败',\n            icon: 'none'\n          })\n        }\n      }).catch(err => {\n        this.isSubmitting = false\n        console.error('发布纸条失败:', err)\n        console.log('错误详情:', JSON.stringify(err))\n\n        // 更新调试信息\n        this.debugInfo.apiStatus = 'API异常'\n        this.debugInfo.lastError = err.message || err.msg || JSON.stringify(err)\n\n        uni.showToast({\n          title: '网络错误，请稍后重试',\n          icon: 'none'\n        })\n      })\n    },\n    \n    validateContent() {\n      if (this.currentType === 4) {\n        uni.showToast({\n          title: '语音功能开发中',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (!this.content.trim()) {\n        uni.showToast({\n          title: '请输入纸条内容',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      if (this.content.trim().length < 5) {\n        uni.showToast({\n          title: '内容太短，至少输入5个字符',\n          icon: 'none'\n        })\n        return false\n      }\n      \n      return true\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  min-height: 100vh;\n  background-color: #f8f8f8;\n}\n\n.nav-bar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  padding: 0 30rpx;\n  background-color: #fff;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.nav-back {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 36rpx;\n}\n\n.nav-title {\n  font-size: 32rpx;\n  font-weight: bold;\n}\n\n.nav-submit {\n  padding: 12rpx 24rpx;\n  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);\n  border-radius: 20rpx;\n  color: white;\n  font-size: 28rpx;\n}\n\n.form-container {\n  padding: 30rpx;\n}\n\n.form-item {\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n}\n\n.form-label {\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-bottom: 20rpx;\n}\n\n.type-list {\n  display: flex;\n  gap: 20rpx;\n  flex-wrap: wrap;\n}\n\n.type-item {\n  flex: 1;\n  min-width: 140rpx;\n  padding: 20rpx;\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  text-align: center;\n  border: 2rpx solid transparent;\n}\n\n.type-item.active {\n  background: #e6f0ff;\n  border-color: #3B82F6;\n}\n\n.type-icon {\n  display: block;\n  font-size: 32rpx;\n  margin-bottom: 8rpx;\n}\n\n.type-name {\n  font-size: 24rpx;\n  color: #666;\n}\n\n.form-textarea {\n  width: 100%;\n  min-height: 200rpx;\n  padding: 20rpx;\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  font-size: 28rpx;\n  line-height: 1.6;\n}\n\n.form-count {\n  text-align: right;\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 10rpx;\n}\n\n.voice-section {\n  padding: 60rpx;\n  text-align: center;\n  color: #999;\n}\n\n.setting-row {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.setting-text {\n  font-size: 28rpx;\n}\n\n.switch {\n  width: 80rpx;\n  height: 40rpx;\n  background: #e9ecef;\n  border-radius: 20rpx;\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.switch.active {\n  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);\n}\n\n.switch-dot {\n  width: 32rpx;\n  height: 32rpx;\n  background: #fff;\n  border-radius: 50%;\n  position: absolute;\n  top: 4rpx;\n  left: 4rpx;\n  transition: all 0.3s ease;\n}\n\n.switch.active .switch-dot {\n  left: 44rpx;\n}\n\n/* 调试信息样式 */\n.debug-info {\n  background: #fff3cd;\n  border: 2rpx solid #ffeaa7;\n  border-radius: 12rpx;\n  padding: 20rpx;\n  margin: 20rpx;\n}\n\n.debug-title {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #856404;\n  margin-bottom: 16rpx;\n  text-align: center;\n}\n\n.debug-item {\n  font-size: 24rpx;\n  color: #856404;\n  margin-bottom: 8rpx;\n  padding: 8rpx;\n  background: rgba(255, 234, 167, 0.3);\n  border-radius: 6rpx;\n  word-break: break-all;\n}\n</style>\n", "import MiniProgramPage from 'D:/uniapp/vue3/pages/note/publish-tree-hole-simple.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "checkLogin", "<PERSON><PERSON><PERSON><PERSON>", "publishTreeHoleBox"], "mappings": ";;;;AAmEA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,MACb,SAAS;AAAA,MACT,aAAa;AAAA,MACb,cAAc;AAAA,MACd,UAAU;AAAA,QACR,EAAE,OAAO,GAAG,MAAM,QAAQ,MAAM,IAAK;AAAA,QACrC,EAAE,OAAO,GAAG,MAAM,MAAM,MAAM,KAAM;AAAA,QACpC,EAAE,OAAO,GAAG,MAAM,MAAM,MAAM,KAAM;AAAA,QACpC,EAAE,OAAO,GAAG,MAAM,QAAQ,MAAM,KAAK;AAAA,MACtC;AAAA;AAAA,MAED,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAEP,SAAK,oBAAoB;AAEzB,QAAI,CAAC,KAAK,oBAAoB;AAC5B;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,sBAAsB;AACpBA,oBAAAA,qEAAY,kBAAkB;AAG9B,YAAM,eAAeC,WAAAA,WAAW;AAChC,WAAK,UAAU,mBAAmB;AAClCD,oBAAAA,qEAAY,mBAAmB,YAAY;AAG3C,UAAI,aAAa;AACjB,UAAI;AACF,YAAI,KAAK,UAAU,KAAK,OAAO,SAAS,KAAK,OAAO,MAAM,KAAK;AAC7D,uBAAa,KAAK,OAAO,MAAM,IAAI;AACnC,eAAK,UAAU,aAAa,cAAc;AAC1CA,wBAAAA,MAAA,MAAA,OAAA,kDAAY,2BAA2B,UAAU;AAAA,eAC5C;AACL,eAAK,UAAU,aAAa;AAC5BA,wBAAAA,MAAY,MAAA,OAAA,kDAAA,sBAAsB;AAAA,QACpC;AAAA,MACF,SAAS,GAAG;AACV,aAAK,UAAU,aAAa,WAAW,EAAE;AACzCA,sBAAAA,MAAA,MAAA,OAAA,kDAAY,oBAAoB,CAAC;AAAA,MACnC;AAGA,UAAI;AACF,cAAM,aAAaA,cAAAA,MAAI,eAAe,OAAO;AAC7C,aAAK,UAAU,aAAa,cAAc;AAC1CA,sBAAAA,MAAY,MAAA,OAAA,kDAAA,cAAc,UAAU;AAAA,MACtC,SAAS,GAAG;AACV,aAAK,UAAU,aAAa,WAAW,EAAE;AACzCA,sBAAAA,MAAA,MAAA,OAAA,kDAAY,gBAAgB,CAAC;AAAA,MAC/B;AAGA,UAAI;AACF,cAAM,mBAAmBA,cAAAA,MAAI,eAAe,oBAAoB;AAChEA,sBAAAA,qEAAY,uBAAuB,gBAAgB;AAAA,MACrD,SAAS,GAAG;AACVA,sBAAAA,MAAA,MAAA,OAAA,kDAAY,2BAA2B,CAAC;AAAA,MAC1C;AAGA,UAAI;AACF,cAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,aAAK,UAAU,WAAW;AAC1BA,sBAAAA,MAAY,MAAA,OAAA,kDAAA,WAAW,QAAQ;AAAA,MACjC,SAAS,GAAG;AACV,aAAK,UAAU,WAAW;AAC1BA,sBAAAA,MAAY,MAAA,OAAA,kDAAA,aAAa,CAAC;AAAA,MAC5B;AAGA,YAAM,mBAAmB,gBAAgB;AACzC,WAAK,UAAU,mBAAmB;AAClCA,oBAAAA,MAAY,MAAA,OAAA,kDAAA,WAAW,gBAAgB;AACvCA,oBAAAA,MAAY,MAAA,OAAA,kDAAA,gBAAgB;AAAA,IAC7B;AAAA,IAED,mBAAmB;AACjB,YAAM,aAAaC,WAAAA,WAAW;AAE9B,UAAI,CAAC,YAAY;AACfD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACfE,iCAAQ;AAAA,mBACH;AACLF,4BAAAA,MAAI,aAAa;AAAA,YACnB;AAAA,UACF;AAAA,SACD;AACD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAAA,IAED,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA,IAED,WAAW,MAAM;AACf,WAAK,cAAc;AACnB,WAAK,UAAU;AAAA,IAChB;AAAA,IAED,kBAAkB;AAChB,WAAK,cAAc,CAAC,KAAK;AAAA,IAC1B;AAAA,IAED,aAAa;;AACXA,oBAAAA,qEAAY,kBAAkB;AAG9B,WAAK,oBAAoB;AAGzB,YAAM,eAAeC,WAAAA,WAAW;AAChC,YAAM,cAAa,sBAAK,WAAL,mBAAa,UAAb,mBAAoB,QAApB,mBAAyB;AAC5CD,oBAAAA,MAAA,MAAA,OAAA,kDAAY,YAAY,YAAY;AACpCA,oBAAAA,MAAA,MAAA,OAAA,kDAAY,aAAa,UAAU;AAEnC,UAAI,CAAC,KAAK,mBAAmB;AAC3B;AAAA,MACF;AAEA,WAAK,eAAe;AACpB,WAAK,UAAU,YAAY;AAE3B,YAAM,OAAO;AAAA,QACX,MAAM,KAAK;AAAA,QACX,SAAS,KAAK,gBAAgB,IAAI,KAAK,KAAK,QAAQ,KAAM;AAAA,QAC1D,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,cAAc,KAAK,cAAc,IAAI;AAAA,MACvC;AAEAA,oBAAAA,MAAA,MAAA,OAAA,kDAAY,SAAS,IAAI;AACzBA,oBAAAA,MAAY,MAAA,OAAA,kDAAA,2BAA2B;AACvC,WAAK,UAAU,YAAY;AAE3BG,iBAAAA,mBAAmB,IAAI,EAAE,KAAK,SAAO;AACnCH,sBAAAA,qEAAY,UAAU,GAAG;AACzB,aAAK,eAAe;AACpB,aAAK,UAAU,YAAY,UAAU,IAAI,MAAM;AAE/C,YAAI,IAAI,WAAW,KAAK;AACtB,eAAK,UAAU,YAAY;AAC3BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAED,qBAAW,MAAM;AACfA,0BAAAA,MAAI,aAAa;AAAA,UAClB,GAAE,IAAI;AAAA,eACF;AACL,eAAK,UAAU,YAAY,UAAU,IAAI,MAAM,MAAM,IAAI,GAAG;AAC5DA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACF,CAAC,EAAE,MAAM,SAAO;AACd,aAAK,eAAe;AACpBA,sBAAAA,MAAc,MAAA,SAAA,kDAAA,WAAW,GAAG;AAC5BA,4BAAA,MAAA,OAAA,kDAAY,SAAS,KAAK,UAAU,GAAG,CAAC;AAGxC,aAAK,UAAU,YAAY;AAC3B,aAAK,UAAU,YAAY,IAAI,WAAW,IAAI,OAAO,KAAK,UAAU,GAAG;AAEvEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,OACF;AAAA,IACF;AAAA,IAED,kBAAkB;AAChB,UAAI,KAAK,gBAAgB,GAAG;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,QAAQ,QAAQ;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,QAAQ,KAAI,EAAG,SAAS,GAAG;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrSA,GAAG,WAAW,eAAe;"}