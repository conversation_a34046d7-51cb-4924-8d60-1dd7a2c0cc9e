{"version": 3, "file": "play.js", "sources": ["components/play/play.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy9wbGF5L3BsYXkudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"play df\">\r\n    <view class=\"play-chunk1\" :style=\"{'background': color}\"></view>\r\n    <view class=\"play-chunk2\" :style=\"{'background': color}\"></view>\r\n    <view class=\"play-chunk3\" :style=\"{'background': color}\"></view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'play',\r\n  props: {\r\n    color: {\r\n      type: String,\r\n      default: '#000'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.play {\r\n  height: 100%;\r\n  justify-content: space-between;\r\n}\r\n.play .play-chunk1,\r\n.play .play-chunk2,\r\n.play .play-chunk3 {\r\n  width: calc(33.33% - 4rpx);\r\n  border-radius: 50px;\r\n  animation: wave 1s linear infinite;\r\n}\r\n.play .play-chunk1 {\r\n  animation-delay: 0s;\r\n}\r\n.play .play-chunk2 {\r\n  animation-delay: 0.33s;\r\n}\r\n.play .play-chunk3 {\r\n  animation-delay: 0.66s;\r\n}\r\n@keyframes wave {\r\n  0% {\r\n    height: 20%;\r\n  }\r\n  50% {\r\n    height: 100%;\r\n  }\r\n  to {\r\n    height: 20%;\r\n  }\r\n}\r\n.df {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n</style> ", "import Component from 'D:/uniapp/vue3/components/play/play.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AASA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AACF;;;;;;;;;AChBA,GAAG,gBAAgB,SAAS;"}