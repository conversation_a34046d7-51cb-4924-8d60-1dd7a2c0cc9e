{"version": 3, "file": "uni-transition.js", "sources": ["uni_modules/uni-transition/components/uni-transition/uni-transition.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvdW5pX21vZHVsZXMvdW5pLXRyYW5zaXRpb24vY29tcG9uZW50cy91bmktdHJhbnNpdGlvbi91bmktdHJhbnNpdGlvbi52dWU"], "sourcesContent": ["<template>\r\n  <view \r\n    v-if=\"isShow\" \r\n    ref=\"ani\" \r\n    :animation=\"animationData\" \r\n    :class=\"customClass\" \r\n    :style=\"transformStyles\" \r\n    @tap=\"onClick\">\r\n    <slot></slot>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { createAnimation } from './createAnimation.js'\r\n\r\nexport default {\r\n  name: 'uniTransition',\r\n  emits: ['click', 'change'],\r\n  props: {\r\n    show: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    modeClass: {\r\n      type: [Array, String],\r\n      default () {\r\n        return 'fade'\r\n      }\r\n    },\r\n    duration: {\r\n      type: Number,\r\n      default: 300\r\n    },\r\n    styles: {\r\n      type: Object,\r\n      default () {\r\n        return {}\r\n      }\r\n    },\r\n    customClass: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    onceRender: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isShow: false,\r\n      transform: '',\r\n      opacity: 1,\r\n      animationData: {},\r\n      durationTime: 300,\r\n      config: {}\r\n    }\r\n  },\r\n  watch: {\r\n    show: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          this.open()\r\n        } else {\r\n          if (this.isShow) {\r\n            this.close()\r\n          }\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  computed: {\r\n    // 生成样式数据\r\n    stylesObject() {\r\n      let styles = {\r\n        ...this.styles,\r\n        'transition-duration': this.duration / 1000 + 's'\r\n      }\r\n      let transform = ''\r\n      for (let i in styles) {\r\n        let line = this.toLine(i)\r\n        transform += line + ':' + styles[i] + ';'\r\n      }\r\n      return transform\r\n    },\r\n    // 动画模式\r\n    transformStyles() {\r\n      return 'transform:' + this.transform + ';' + 'opacity:' + this.opacity + ';' + this.stylesObject\r\n    }\r\n  },\r\n  created() {\r\n    // 动画配置\r\n    this.config = {\r\n      duration: this.duration,\r\n      timingFunction: 'ease',\r\n      transformOrigin: '50% 50%',\r\n      delay: 0\r\n    }\r\n    this.durationTime = this.duration\r\n  },\r\n  methods: {\r\n    /**\r\n     * 初始化动画条件\r\n     */\r\n    init(obj = {}) {\r\n      if (obj.duration) {\r\n        this.durationTime = obj.duration\r\n      }\r\n      this.animation = createAnimation(Object.assign(this.config, obj), this)\r\n    },\r\n    /**\r\n     * 点击组件触发回调\r\n     */\r\n    onClick() {\r\n      this.$emit('click', {\r\n        detail: this.isShow\r\n      })\r\n    },\r\n    /**\r\n     * 修改动画样式\r\n     * @param {Object} styleObj\r\n     * @param {Object} step 动画步长\r\n     */\r\n    step(obj, config = {}) {\r\n      if (!this.animation) return\r\n      for (let i in obj) {\r\n        try {\r\n          if (typeof obj[i] === 'object') {\r\n            this.animation[i](...obj[i])\r\n          } else {\r\n            this.animation[i](obj[i])\r\n          }\r\n        } catch (e) {\r\n          console.error(`方法 ${i} 不存在`)\r\n        }\r\n      }\r\n      this.animation.step(config)\r\n      return this\r\n    },\r\n    /**\r\n     * 执行动画\r\n     * @param {Object} callback 可选，执行完成的回调\r\n     */\r\n    run(callback) {\r\n      if (!this.animation) return\r\n      this.animation.run(callback)\r\n    },\r\n    /**\r\n     * 开启动画\r\n     */\r\n    open() {\r\n      clearTimeout(this.timer)\r\n      this.transform = ''\r\n      this.isShow = true\r\n      let { opacity, transform } = this.styleInit(false)\r\n      if (typeof opacity !== 'undefined') {\r\n        this.opacity = opacity\r\n      }\r\n      this.transform = transform\r\n      // 确保动画起效\r\n      this.$nextTick(() => {\r\n        // TODO 定时器保证动画完全执行\r\n        this.timer = setTimeout(() => {\r\n          this.animation = createAnimation(this.config, this)\r\n          this.tranfromInit(false).step()\r\n          this.animation.run()\r\n          this.$emit('change', {\r\n            detail: this.isShow\r\n          })\r\n        }, 20)\r\n      })\r\n    },\r\n    /**\r\n     * 关闭动画\r\n     */\r\n    close(type) {\r\n      if (!this.animation) return\r\n      this.tranfromInit(true)\r\n        .step()\r\n        .run(() => {\r\n          this.isShow = false\r\n          this.animationData = null\r\n          this.animation = null\r\n          let { opacity, transform } = this.styleInit(false)\r\n          this.opacity = opacity || 1\r\n          this.transform = transform\r\n          this.$emit('change', {\r\n            detail: this.isShow\r\n          })\r\n        })\r\n    },\r\n    /**\r\n     * 处理动画开始前的样式\r\n     * @param {Boolean} type 不同类型的样式处理\r\n     */\r\n    styleInit(type) {\r\n      let styles = {\r\n        transform: ''\r\n      }\r\n      let buildStyle = (type, mode) => {\r\n        if (mode === 'fade') {\r\n          styles.opacity = this.animationType(type)[mode]\r\n        } else {\r\n          styles.transform += this.animationType(type)[mode] + ' '\r\n        }\r\n      }\r\n      if (typeof this.modeClass === 'string') {\r\n        buildStyle(type, this.modeClass)\r\n      } else {\r\n        this.modeClass.forEach(mode => {\r\n          buildStyle(type, mode)\r\n        })\r\n      }\r\n      return styles\r\n    },\r\n    /**\r\n     * 处理内置组合动画\r\n     * @param {Boolean} type 不同类型的样式处理\r\n     */\r\n    tranfromInit(type) {\r\n      let buildTranfrom = (type, mode) => {\r\n        let aniNum = null\r\n        if (mode === 'fade') {\r\n          aniNum = type ? 0 : 1\r\n        } else {\r\n          aniNum = type ? '-100%' : '0'\r\n          if (mode === 'zoom-in') {\r\n            aniNum = type ? 0.8 : 1\r\n          }\r\n          if (mode === 'zoom-out') {\r\n            aniNum = type ? 1.2 : 1\r\n          }\r\n          if (mode === 'slide-right') {\r\n            aniNum = type ? '100%' : '0'\r\n          }\r\n          if (mode === 'slide-bottom') {\r\n            aniNum = type ? '100%' : '0'\r\n          }\r\n        }\r\n        this.animation[this.animationMode()[mode]](aniNum)\r\n      }\r\n      if (typeof this.modeClass === 'string') {\r\n        buildTranfrom(type, this.modeClass)\r\n      } else {\r\n        this.modeClass.forEach(mode => {\r\n          buildTranfrom(type, mode)\r\n        })\r\n      }\r\n      return this.animation\r\n    },\r\n    /**\r\n     * 内置动画类型与实际动画对应字典\r\n     */\r\n    animationType(type) {\r\n      return {\r\n        fade: type ? 0 : 1,\r\n        'slide-top': `translateY(${type ? '0' : '-100%'})`,\r\n        'slide-right': `translateX(${type ? '0' : '100%'})`,\r\n        'slide-bottom': `translateY(${type ? '0' : '100%'})`,\r\n        'slide-left': `translateX(${type ? '0' : '-100%'})`,\r\n        'zoom-in': `scaleX(${type ? 1 : 0.8}) scaleY(${type ? 1 : 0.8})`,\r\n        'zoom-out': `scaleX(${type ? 1 : 1.2}) scaleY(${type ? 1 : 1.2})`\r\n      }\r\n    },\r\n    /**\r\n     * 内置动画模式与实际动画对应字典\r\n     */\r\n    animationMode() {\r\n      return {\r\n        fade: 'opacity',\r\n        'slide-top': 'translateY',\r\n        'slide-right': 'translateX',\r\n        'slide-bottom': 'translateY',\r\n        'slide-left': 'translateX',\r\n        'zoom-in': 'scale',\r\n        'zoom-out': 'scale'\r\n      }\r\n    },\r\n    /**\r\n     * 驼峰转中横线\r\n     */\r\n    toLine(name) {\r\n      return name.replace(/([A-Z])/g, '-$1').toLowerCase()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n</style> ", "import Component from 'D:/uniapp/vue3/uni_modules/uni-transition/components/uni-transition/uni-transition.vue'\nwx.createComponent(Component)"], "names": ["createAnimation", "uni", "type"], "mappings": ";;;AAeA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO,CAAC,SAAS,QAAQ;AAAA,EACzB,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,WAAW;AAAA,MACT,MAAM,CAAC,OAAO,MAAM;AAAA,MACpB,UAAW;AACT,eAAO;AAAA,MACT;AAAA,IACD;AAAA,IACD,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,UAAW;AACT,eAAO,CAAC;AAAA,MACV;AAAA,IACD;AAAA,IACD,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,SAAS;AAAA,MACT,eAAe,CAAE;AAAA,MACjB,cAAc;AAAA,MACd,QAAQ,CAAC;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,QAAQ,QAAQ;AACd,YAAI,QAAQ;AACV,eAAK,KAAK;AAAA,eACL;AACL,cAAI,KAAK,QAAQ;AACf,iBAAK,MAAM;AAAA,UACb;AAAA,QACF;AAAA,MACD;AAAA,MACD,WAAW;AAAA,IACb;AAAA,EACD;AAAA,EACD,UAAU;AAAA;AAAA,IAER,eAAe;AACb,UAAI,SAAS;AAAA,QACX,GAAG,KAAK;AAAA,QACR,uBAAuB,KAAK,WAAW,MAAO;AAAA,MAChD;AACA,UAAI,YAAY;AAChB,eAAS,KAAK,QAAQ;AACpB,YAAI,OAAO,KAAK,OAAO,CAAC;AACxB,qBAAa,OAAO,MAAM,OAAO,CAAC,IAAI;AAAA,MACxC;AACA,aAAO;AAAA,IACR;AAAA;AAAA,IAED,kBAAkB;AAChB,aAAO,eAAe,KAAK,YAAY,cAAmB,KAAK,UAAU,MAAM,KAAK;AAAA,IACtF;AAAA,EACD;AAAA,EACD,UAAU;AAER,SAAK,SAAS;AAAA,MACZ,UAAU,KAAK;AAAA,MACf,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,OAAO;AAAA,IACT;AACA,SAAK,eAAe,KAAK;AAAA,EAC1B;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAIP,KAAK,MAAM,IAAI;AACb,UAAI,IAAI,UAAU;AAChB,aAAK,eAAe,IAAI;AAAA,MAC1B;AACA,WAAK,YAAYA,mEAAe,gBAAC,OAAO,OAAO,KAAK,QAAQ,GAAG,GAAG,IAAI;AAAA,IACvE;AAAA;AAAA;AAAA;AAAA,IAID,UAAU;AACR,WAAK,MAAM,SAAS;AAAA,QAClB,QAAQ,KAAK;AAAA,OACd;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,KAAK,KAAK,SAAS,IAAI;AACrB,UAAI,CAAC,KAAK;AAAW;AACrB,eAAS,KAAK,KAAK;AACjB,YAAI;AACF,cAAI,OAAO,IAAI,CAAC,MAAM,UAAU;AAC9B,iBAAK,UAAU,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;AAAA,iBACtB;AACL,iBAAK,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF,SAAS,GAAG;AACVC,wBAAA,MAAA,MAAA,SAAA,kFAAc,MAAM,CAAC,MAAM;AAAA,QAC7B;AAAA,MACF;AACA,WAAK,UAAU,KAAK,MAAM;AAC1B,aAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,IAAI,UAAU;AACZ,UAAI,CAAC,KAAK;AAAW;AACrB,WAAK,UAAU,IAAI,QAAQ;AAAA,IAC5B;AAAA;AAAA;AAAA;AAAA,IAID,OAAO;AACL,mBAAa,KAAK,KAAK;AACvB,WAAK,YAAY;AACjB,WAAK,SAAS;AACd,UAAI,EAAE,SAAS,UAAQ,IAAM,KAAK,UAAU,KAAK;AACjD,UAAI,OAAO,YAAY,aAAa;AAClC,aAAK,UAAU;AAAA,MACjB;AACA,WAAK,YAAY;AAEjB,WAAK,UAAU,MAAM;AAEnB,aAAK,QAAQ,WAAW,MAAM;AAC5B,eAAK,YAAYD,mEAAAA,gBAAgB,KAAK,QAAQ,IAAI;AAClD,eAAK,aAAa,KAAK,EAAE,KAAK;AAC9B,eAAK,UAAU,IAAI;AACnB,eAAK,MAAM,UAAU;AAAA,YACnB,QAAQ,KAAK;AAAA,WACd;AAAA,QACF,GAAE,EAAE;AAAA,OACN;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAID,MAAM,MAAM;AACV,UAAI,CAAC,KAAK;AAAW;AACrB,WAAK,aAAa,IAAI,EACnB,KAAK,EACL,IAAI,MAAM;AACT,aAAK,SAAS;AACd,aAAK,gBAAgB;AACrB,aAAK,YAAY;AACjB,YAAI,EAAE,SAAS,UAAQ,IAAM,KAAK,UAAU,KAAK;AACjD,aAAK,UAAU,WAAW;AAC1B,aAAK,YAAY;AACjB,aAAK,MAAM,UAAU;AAAA,UACnB,QAAQ,KAAK;AAAA,SACd;AAAA,OACF;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,UAAU,MAAM;AACd,UAAI,SAAS;AAAA,QACX,WAAW;AAAA,MACb;AACA,UAAI,aAAa,CAACE,OAAM,SAAS;AAC/B,YAAI,SAAS,QAAQ;AACnB,iBAAO,UAAU,KAAK,cAAcA,KAAI,EAAE,IAAI;AAAA,eACzC;AACL,iBAAO,aAAa,KAAK,cAAcA,KAAI,EAAE,IAAI,IAAI;AAAA,QACvD;AAAA,MACF;AACA,UAAI,OAAO,KAAK,cAAc,UAAU;AACtC,mBAAW,MAAM,KAAK,SAAS;AAAA,aAC1B;AACL,aAAK,UAAU,QAAQ,UAAQ;AAC7B,qBAAW,MAAM,IAAI;AAAA,SACtB;AAAA,MACH;AACA,aAAO;AAAA,IACR;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,aAAa,MAAM;AACjB,UAAI,gBAAgB,CAACA,OAAM,SAAS;AAClC,YAAI,SAAS;AACb,YAAI,SAAS,QAAQ;AACnB,mBAASA,QAAO,IAAI;AAAA,eACf;AACL,mBAASA,QAAO,UAAU;AAC1B,cAAI,SAAS,WAAW;AACtB,qBAASA,QAAO,MAAM;AAAA,UACxB;AACA,cAAI,SAAS,YAAY;AACvB,qBAASA,QAAO,MAAM;AAAA,UACxB;AACA,cAAI,SAAS,eAAe;AAC1B,qBAASA,QAAO,SAAS;AAAA,UAC3B;AACA,cAAI,SAAS,gBAAgB;AAC3B,qBAASA,QAAO,SAAS;AAAA,UAC3B;AAAA,QACF;AACA,aAAK,UAAU,KAAK,cAAe,EAAC,IAAI,CAAC,EAAE,MAAM;AAAA,MACnD;AACA,UAAI,OAAO,KAAK,cAAc,UAAU;AACtC,sBAAc,MAAM,KAAK,SAAS;AAAA,aAC7B;AACL,aAAK,UAAU,QAAQ,UAAQ;AAC7B,wBAAc,MAAM,IAAI;AAAA,SACzB;AAAA,MACH;AACA,aAAO,KAAK;AAAA,IACb;AAAA;AAAA;AAAA;AAAA,IAID,cAAc,MAAM;AAClB,aAAO;AAAA,QACL,MAAM,OAAO,IAAI;AAAA,QACjB,aAAa,cAAc,OAAO,MAAM,OAAO;AAAA,QAC/C,eAAe,cAAc,OAAO,MAAM,MAAM;AAAA,QAChD,gBAAgB,cAAc,OAAO,MAAM,MAAM;AAAA,QACjD,cAAc,cAAc,OAAO,MAAM,OAAO;AAAA,QAChD,WAAW,UAAU,OAAO,IAAI,GAAG,YAAY,OAAO,IAAI,GAAG;AAAA,QAC7D,YAAY,UAAU,OAAO,IAAI,GAAG,YAAY,OAAO,IAAI,GAAG;AAAA,MAChE;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAID,gBAAgB;AACd,aAAO;AAAA,QACL,MAAM;AAAA,QACN,aAAa;AAAA,QACb,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,WAAW;AAAA,QACX,YAAY;AAAA,MACd;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAID,OAAO,MAAM;AACX,aAAO,KAAK,QAAQ,YAAY,KAAK,EAAE,YAAY;AAAA,IACrD;AAAA,EACF;AACF;;;;;;;;;;;;AC7RA,GAAG,gBAAgB,SAAS;"}