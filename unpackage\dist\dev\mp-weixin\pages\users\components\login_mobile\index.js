"use strict";
const common_vendor = require("../../../../common/vendor.js");
const mixins_SendVerifyCode = require("../../../../mixins/SendVerifyCode.js");
const libs_routine = require("../../../../libs/routine.js");
require("../../../../utils/cache.js");
const api_user = require("../../../../api/user.js");
const api_social = require("../../../../api/social.js");
require("../../../../utils/request.js");
const stores_user = require("../../../../stores/user.js");
const app = getApp();
const Verify = () => "../verify/index.js";
const _sfc_main = {
  name: "login_mobile",
  components: {
    Verify
  },
  props: {
    isUp: {
      type: <PERSON><PERSON>an,
      default: false
    },
    canClose: {
      type: Boolean,
      default: true
    },
    authKey: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      userStore: stores_user.useUserStore(),
      keyCode: "",
      account: "",
      codeNum: ""
    };
  },
  mixins: [mixins_SendVerifyCode.sendVerifyCode],
  mounted() {
    this.getCode();
  },
  methods: {
    success(data) {
      let that = this;
      this.$refs.verify.hide();
      api_user.getCodeApi().then((res) => {
        api_user.registerVerify({
          phone: that.account,
          key: res.data.key,
          captchaType: this.captchaType,
          captchaVerification: data.captchaVerification
        }).then((res2) => {
          that.$util.Tips({
            title: res2.msg
          });
          that.sendCode();
        }).catch((err) => {
          return that.$util.Tips({
            title: err
          });
        });
      });
    },
    // 获取验证码
    code() {
      let that = this;
      if (!that.account)
        return that.$util.Tips({
          title: that.$t(`请填写手机号码`)
        });
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account))
        return that.$util.Tips({
          title: that.$t(`请输入正确的手机号码`)
        });
      this.$refs.verify.show();
    },
    // 获取验证码api
    getCode() {
      let that = this;
      api_user.getCodeApi().then((res) => {
        that.keyCode = res.data.key;
      }).catch((res) => {
        that.$util.Tips({
          title: res
        });
      });
    },
    close(new_user) {
      if (this.canClose) {
        this.$emit("close", new_user);
      }
    },
    // 登录
    loginBtn() {
      let that = this;
      if (!that.account)
        return that.$util.Tips({
          title: that.$t(`请填写手机号码`)
        });
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.account))
        return that.$util.Tips({
          title: that.$t(`请输入正确的手机号码`)
        });
      if (!that.codeNum)
        return that.$util.Tips({
          title: that.$t(`请填写验证码`)
        });
      if (!/^[\w\d]+$/i.test(that.codeNum))
        return that.$util.Tips({
          title: that.$t(`请输入正确的验证码`)
        });
      common_vendor.index.showLoading({
        title: that.$t(`正在登录中`)
      });
      libs_routine.Routine.getCode().then((code) => {
        this.phoneSilenceAuth(code);
      }).catch((error) => {
        common_vendor.index.hideLoading();
      });
    },
    phoneAuth(key) {
      api_user.phoneWxSilenceAuth({
        spid: app.globalData.spid,
        spread: app.globalData.code,
        phone: this.account,
        captcha: this.codeNum,
        key
      }).then((res) => {
        this.userStore.setToken({
          token: res.data.token,
          time: res.data.expires_time - this.$Cache.time()
        });
        this.getUserInfo();
      }).catch((error) => {
        common_vendor.index.hideLoading();
        this.$util.Tips({
          title: error
        });
      });
    },
    phoneSilenceAuth(code) {
      let self = this;
      api_user.phoneSilenceAuth({
        code,
        spread_spid: app.globalData.spid,
        spread_code: app.globalData.code,
        phone: this.account,
        captcha: this.codeNum
      }).then((res) => {
        this.userStore.setToken({
          token: res.data.token,
          time: res.data.expires_time - this.$Cache.time()
        });
        this.getUserInfo(res.data.new_user);
      }).catch((error) => {
        self.$util.Tips({
          title: error
        });
      });
    },
    /**
     * 获取个人用户信息
     */
    getUserInfo: function(new_user) {
      let that = this;
      api_user.getUserInfo().then((res) => {
        that.userInfo = res.data;
        that.userStore.setUid(res.data.uid);
        api_social.getUserSocialInfo().then((socialRes) => {
          common_vendor.index.hideLoading();
          if (socialRes.data) {
            that.userStore.updateUserInfo(socialRes.data);
          }
          if (!new_user) {
            that.$util.Tips({
              title: that.$t(`登录成功`),
              icon: "success"
            }, {
              tab: 3
            });
          } else {
            that.$util.Tips({
              title: that.$t(`登录成功`),
              icon: "success"
            });
          }
          that.close(new_user || 0);
        }).catch(() => {
          common_vendor.index.hideLoading();
          if (!new_user) {
            that.$util.Tips({
              title: that.$t(`登录成功`),
              icon: "success"
            }, {
              tab: 3
            });
          } else {
            that.$util.Tips({
              title: that.$t(`登录成功`),
              icon: "success"
            });
          }
          that.close(new_user || 0);
        });
      });
    }
  }
};
if (!Array) {
  const _component_Verify = common_vendor.resolveComponent("Verify");
  _component_Verify();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.isUp
  }, $props.isUp ? {
    b: common_vendor.o((...args) => $options.close && $options.close(...args)),
    c: _ctx.$t(`输入手机号`),
    d: $data.account,
    e: common_vendor.o(($event) => $data.account = $event.detail.value),
    f: _ctx.$t(`输入验证码`),
    g: $data.codeNum,
    h: common_vendor.o(($event) => $data.codeNum = $event.detail.value),
    i: common_vendor.t(_ctx.text),
    j: _ctx.disabled,
    k: common_vendor.o((...args) => $options.code && $options.code(...args)),
    l: common_vendor.t(_ctx.$t(`立即登录`)),
    m: common_vendor.o((...args) => $options.loginBtn && $options.loginBtn(...args)),
    n: $props.isUp ? 1 : "",
    o: common_vendor.sr("verify", "8a2bf81a-0"),
    p: common_vendor.o($options.success),
    q: common_vendor.p({
      captchaType: _ctx.captchaType,
      imgSize: {
        width: "330px",
        height: "155px"
      }
    })
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages/users/components/login_mobile/index.js.map
