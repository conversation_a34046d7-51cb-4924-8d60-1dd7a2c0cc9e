{"version": 3, "file": "webview.js", "sources": ["pages/setting/webview.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc2V0dGluZy93ZWJ2aWV3LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view>\r\n    <web-view :src=\"url\"></web-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      url: \"\"\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    this.url = decodeURIComponent(options.url)\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n</style> ", "import MiniProgramPage from 'D:/uniapp/vue3/pages/setting/webview.vue'\nwx.createPage(MiniProgramPage)"], "names": [], "mappings": ";;AAOA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AACd,SAAK,MAAM,mBAAmB,QAAQ,GAAG;AAAA,EAC3C;AACF;;;;;;;ACfA,GAAG,WAAW,eAAe;"}