
.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.nav-bar[data-v-eaf4c2e5] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.nav-bar .navbar-item[data-v-eaf4c2e5] {
  width: 100%;
  display: flex;
  align-items: center;
}
.navbar-item .back-box[data-v-eaf4c2e5] {
  padding: 0 0.9375rem;
  display: flex;
  align-items: center;
}
.navbar-item .back-box uni-image[data-v-eaf4c2e5] {
  width: 1.0625rem;
  height: 1.0625rem;
}
.bfw[data-v-eaf4c2e5] {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,.8);
}
.bf8[data-v-eaf4c2e5] {
  background: #f8f8f8;
}


/* 保持原有的CSS类名，只添加性能优化 */
.container{
	width: calc(100% - 1.875rem);
	padding: 0 0.9375rem 1.875rem;
}
.title-box{
	padding: 0.625rem 0;
	font-size: 1.25rem;
	font-weight: 700;
}

/* 相册部分样式 */
.album-section {
	margin: 0.9375rem 0;
	width: 100%;
}
.album-title {
	font-size: 1rem;
	font-weight: bold;
	margin-bottom: 0.3125rem;
	color: #333;
}
.album-desc {
	font-size: 0.75rem;
	color: #999;
	margin-bottom: 0.625rem;
	line-height: 1.4;
}
.change-avatar {
	color: #FA5150;
	margin-left: 0.3125rem;
	font-weight: bold;
}
.photo-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-top: 0.625rem;
}
.photo-item {
	position: relative;
	width: 6.875rem;
	height: 6.875rem;
	margin-bottom: 0.625rem;
	border-radius: 0.375rem;
	overflow: hidden;
	border: 0.0625rem dashed #ddd;
}
.photo-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.photo-tag {
	position: absolute;
	top: 0.3125rem;
	left: 0.3125rem;
	background-color: rgba(0, 0, 0, 0.6);
	color: #fff;
	font-size: 0.625rem;
	padding: 0.125rem 0.375rem;
	border-radius: 0.3125rem;
	font-weight: normal;
}
.photo-boost {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: #FA5150;
	color: #fff;
	font-size: 0.6875rem;
	padding: 0.25rem 0;
	text-align: center;
}
.photo-placeholder {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100%;
	background-color: #f5f5f5;
}
.photo-icon {
	width: 1.875rem;
	height: 1.875rem;
	margin-bottom: 0.3125rem;
}
.photo-type {
	color: #666;
	font-size: 0.75rem;
	margin-bottom: 0.3125rem;
	text-align: center;
	padding: 0 0.3125rem;
	font-weight: normal;
}
.avatar-box{
	margin: 0.3125rem 0 0;
	width: 5.625rem;
	height: 5.625rem;
	padding: 0;
	background: none;
	position: relative;
}
.avatar-box .avatar{
	width: 100%;
	height: 100%;
	background: #f8f8f8;
	border-radius: 50%;
}
.avatar-box .icon{
	position: absolute;
	right: 0;
	bottom: 0;
	width: 1.5rem;
	height: 1.5rem;
	border-radius: 50%;
	justify-content: center;
	background: #000;
	border: 0.125rem solid #fff;
}
.title-label{
	width: calc(100% - 1.5rem);
	padding: 0.9375rem 0.75rem 0.375rem;
	color: #999;
	font-size: 0.75rem;
	font-weight: 700;
}
.subtitle {
	font-weight: normal;
	font-size: 0.625rem;
	margin-left: 0.3125rem;
}
.w50{
	width: calc(50% - 2.5rem) !important;
}
.w70{
	width: calc(70% - 0.46875rem) !important;
}
.w25{
	width: calc(25% - 0.46875rem) !important;
}
.sp{
	justify-content: space-between;
}
.age{
	width: calc(50% - 2.34375rem) !important;
}
.gender-box{
	padding: 0.625rem 0;
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}
.gender-box .active-1{
	border-color: #fa5150 !important;
	background: rgba(250, 81, 80, 0.125);
}
.gender-box .active-2{
	border-color: #4cd964 !important;
	background: rgba(76, 217, 100, 0.125);
}
.gender-box .gender-item{
	height: 2.8125rem;
	width: 5.625rem;
	border-radius: 0.75rem;
	border-width: 0.125rem;
	border-style: solid;
	border-color: #f5f5f5;
	justify-content: center;
	margin: 0.3125rem;
}
.gender-box .gender-item uni-image{
	width: 1.375rem;
	height: 1.375rem;
	margin-right: 0.3125rem;
}
.input-box{
	width: calc(100% - 2.125rem);
	padding: 0 0.9375rem;
	height: 2.8125rem;
	line-height: 2.8125rem;
	font-size: 0.875rem;
	font-weight: 700;
	border: 0.125rem solid #f5f5f5;
	border-radius: 0.75rem;
	justify-content: space-between;
}
.textarea-box{
	width: calc(100% - 2.125rem);
	padding: 0.625rem 0.9375rem;
	min-height: 2.8125rem;
	line-height: 1.5rem;
	color: #000;
	font-size: 0.875rem;
	font-weight: 700;
	border: 0.125rem solid #f5f5f5;
	border-radius: 0.75rem;
}
.input-btn{
	width: 2.8125rem;
	height: 2.8125rem;
	font-size: 0.75rem;
	justify-content: space-between;
	margin: 0;
	padding: 0;
	background: #fff;
}
.input-btn uni-image{
	margin-right: 0.25rem;
	width: 1rem;
	height: 1rem;
}
.input-tips{
	margin-top: 0.625rem;
	color: #999;
	font-size: 0.5625rem;
}
.popup-box{
	width: calc(100% - 1.25rem);
	padding: 0.625rem;
	background: #fff;
	border-radius: 0.9375rem 0.9375rem 0 0;
	position: relative;
	overflow: hidden;
}
.popup-box .popup-top{
	width: calc(100% - 0.625rem);
	padding: 0.3125rem;
	justify-content: space-between;
}
.popup-top .popup-title .t1{
	font-size: 1.1875rem;
	font-weight: 700;
}
.popup-top .popup-title .t2{
	color: #999;
	font-size: 0.625rem;
	font-weight: 300;
}
.popup-top .popup-close{
	width: 1.5rem;
	height: 1.5rem;
	border-radius: 50%;
	background: #f8f8f8;
	justify-content: center;
	transform: rotate(45deg);
}
.popup-box .popup-btn{
	margin: 1.25rem 0.3125rem;
	width: calc(100% - 0.625rem);
	height: 2.8125rem;
	line-height: 2.8125rem;
	text-align: center;
	font-size: 0.75rem;
	font-weight: 700;
	color: #fff;
	background: #000;
	border-radius: 2.8125rem;
}
.popup-box .age-box{
	padding: 0.625rem 0;
	display: flex;
	flex-wrap: wrap;
}
.age-box .age-item{
	margin: 0.3125rem;
	padding: 0.9375rem 1.25rem;
	color: #000;
	border-width: 0.125rem;
	border-style: solid;
	border-color: #f5f5f5;
	font-size: 0.75rem;
	font-weight: 700;
	border-radius: 0.9375rem;
}
.tips-box {
	padding: 0.625rem 0.9375rem;
	border-radius: 0.375rem;
	justify-content: center;
	margin-top: 1.25rem;
}
.tips-box .tips-item {
	color: #fff;
	font-size: 0.875rem;
	font-weight: 600;
	text-align: center;
}
.df{
	display: flex;
	align-items: center;
}

/* 底部按钮样式 */
.footer-box {
	position: fixed;
	z-index: 99;
	bottom: 0;
	left: 0;
	width: 100%;
	padding-bottom: env(safe-area-inset-bottom);
}
.footer-box .footer-item {
	width: calc(100% - 1.875rem);
	padding: 0.625rem 0.9375rem;
	justify-content: center;
}
.footer-item .btn {
	width: calc(100% - 0.9375rem);
	height: 2.8125rem;
	line-height: 2.8125rem;
	text-align: center;
	font-size: 0.875rem;
	font-weight: 700;
	border-radius: 1.40625rem;
}
.bg2 {
	color: #fff;
	background: #000;
}
.bfw {
	background: #fff;
}
.bUp {
	box-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);
}

/* 增加一些缺失的样式类 */
uni-button {
	padding: 0;
	background-color: transparent;
}
uni-button::after {
	border: none;
}
uni-image {
	max-width: 100%;
	max-height: 100%;
}

/* 生日选择器样式 */
.birthday-picker {
	width: 100%;
	height: 12.5rem;
	margin-top: 0.625rem;
}
.picker-item {
	height: 50px;
	line-height: 50px;
	text-align: center;
	font-size: 0.875rem;
}

/* 年龄标签样式 */
.age-tags {
	display: flex;
	flex-wrap: wrap;
	margin: 0.3125rem 0 0.625rem;
}
.age-tag {
	padding: 0.3125rem 0.9375rem;
	margin: 0.3125rem;
	background-color: #f5f5f5;
	border-radius: 1.25rem;
	font-size: 0.8125rem;
	color: #666;
}
.age-tag-active {
	background-color: #000;
	color: #fff;
}

/* 标签相关样式 */
.tags-box {
	padding: 0.625rem 0.3125rem;
	display: flex;
	flex-wrap: wrap;
	max-height: 15.625rem;
	overflow-y: auto;
}
.tag-item {
	padding: 0.5rem 0.9375rem;
	margin: 0.3125rem;
	background-color: #f5f5f5;
	border-radius: 1.25rem;
	font-size: 0.8125rem;
	color: #666;
}
.tagactive {
	background-color: #000;
	color: #fff;
}
.tag-categories {
	white-space: nowrap;
	padding: 0.625rem 0;
	border-bottom: 1px solid #f0f0f0;
}
.category-item {
	display: inline-block;
	padding: 0.5rem 0.9375rem;
	margin: 0 0.3125rem;
	background-color: #f5f5f5;
	border-radius: 1.25rem;
	font-size: 0.8125rem;
	color: #666;
}
.category-active {
	background-color: #000;
	color: #fff;
}

/* 标签内容区 */
.tags-swiper {
	height: 12.5rem;
	margin-top: 0.625rem;
}
.tags-scroll {
	height: 100%;
}
.no-tags {
	text-align: center;
	color: #999;
	font-size: 0.875rem;
	padding: 0.9375rem 0;
	width: 100%;
}

/* 底部安全区域 */
.bottom-safe-area {
	height: 4.6875rem;
	width: 100%;
}

/* 新增照片按钮样式 */
.add-photo {
	border: 0.0625rem dashed #ccc;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f9f9f9;
}
.add-icon {
	font-size: 1.875rem;
	color: #999;
	text-align: center;
	line-height: 1;
}
.add-text {
	font-size: 0.75rem;
	color: #999;
	margin-top: 0.3125rem;
}
.photo-button {
	width: 100%;
	height: 100%;
	padding: 0;
	margin: 0;
	background: none;
	border: none;
	line-height: normal;
	position: relative;
	display: block;
}
.photo-button::after {
	border: none;
}
.category-item {
	display: inline-block;
	padding: 0.5rem 0.9375rem;
	margin: 0 0.3125rem;
	background-color: #f5f5f5;
	border-radius: 1.25rem;
	font-size: 0.8125rem;
	color: #666;
}
.category-active {
	background-color: #000;
	color: #fff;
}
.category-item {
	display: inline-block;
	padding: 0.5rem 0.9375rem;
	margin: 0 0.3125rem;
	background-color: #f5f5f5;
	border-radius: 1.25rem;
	font-size: 0.8125rem;
	color: #666;
}
.category-active {
	background-color: #000;
	color: #fff;
}
.category-count {
	font-size: 0.625rem;
	margin-left: 0.1875rem;
	opacity: 0.8;
}
