{"version": 3, "file": "user.js", "sources": ["api/user.js"], "sourcesContent": ["// +----------------------------------------------------------------------\r\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\r\n// +----------------------------------------------------------------------\r\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\r\n// +----------------------------------------------------------------------\r\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\r\n// +----------------------------------------------------------------------\r\n// | Author: CRMEB Team <<EMAIL>>\r\n// +----------------------------------------------------------------------\r\n\r\nimport request from \"@/utils/request.js\";\r\n\r\n/**\r\n * 获取用户信息\r\n * \r\n */\r\nexport function getUserInfo() {\r\n\treturn request.get('user');\r\n}\r\n\r\n/**\r\n * 设置用户分享\r\n * \r\n */\r\nexport function userShare() {\r\n\treturn request.post('user/share');\r\n}\r\n\r\n/**\r\n * h5用户登录\r\n * @param data object 用户账号密码\r\n */\r\nexport function loginH5(data) {\r\n\treturn request.post(\"login\", data, {\r\n\t\tnoAuth: true\r\n\t});\r\n}\r\n\r\n/**\r\n * h5用户手机号登录\r\n * @param data object 用户手机号 也只能\r\n */\r\nexport function loginMobile(data) {\r\n\treturn request.post(\"login/mobile\", data, {\r\n\t\tnoAuth: true\r\n\t});\r\n}\r\n\r\n/**\r\n * 验证码key\r\n */\r\nexport function getCodeApi() {\r\n\treturn request.get(\"verify_code\", {}, {\r\n\t\tnoAuth: true\r\n\t});\r\n}\r\n\r\n/**\r\n * h5用户发送验证码\r\n * @param data object 用户手机号\r\n */\r\nexport function registerVerify(data) {\r\n\treturn request.post(\"register/verify\", data, {\r\n\t\tnoAuth: true\r\n\t});\r\n}\r\n\r\n/**\r\n * h5用户手机号注册\r\n * @param data object 用户手机号 验证码 密码\r\n */\r\nexport function register(data) {\r\n\treturn request.post(\"register\", data, {\r\n\t\tnoAuth: true\r\n\t});\r\n}\r\n\r\n/**\r\n * 用户手机号修改密码\r\n * @param data object 用户手机号 验证码 密码\r\n */\r\nexport function registerReset(data) {\r\n\treturn request.post(\"register/reset\", data, {\r\n\t\tnoAuth: true\r\n\t});\r\n}\r\n\r\n/**\r\n * 获取用户中心菜单\r\n *\r\n */\r\nexport function getMenuList() {\r\n\treturn request.get(\"menu/user\", {}, {\r\n\t\tnoAuth: true\r\n\t});\r\n}\r\n\r\n/*\r\n * 签到用户信息\r\n * */\r\nexport function postSignUser(sign) {\r\n\treturn request.post(\"sign/user\", sign);\r\n}\r\n\r\n/**\r\n * 获取签到配置\r\n * \r\n */\r\nexport function getSignConfig() {\r\n\treturn request.get('sign/config')\r\n}\r\n\r\n/**\r\n * 获取签到列表\r\n * @param object data\r\n */\r\nexport function getSignList(data) {\r\n\treturn request.get('sign/list', data);\r\n}\r\n\r\n/**\r\n * 用户签到\r\n */\r\nexport function setSignIntegral() {\r\n\treturn request.post('sign/integral')\r\n}\r\n\r\n/**\r\n * 签到列表(年月)\r\n * @param object data\r\n * \r\n */\r\nexport function getSignMonthList(data) {\r\n\treturn request.get('sign/month', data)\r\n}\r\n\r\n/**\r\n * 活动状态\r\n * \r\n */\r\nexport function userActivity() {\r\n\treturn request.get('user/activity');\r\n}\r\n\r\n/*\r\n * 资金明细（types|0=全部,1=消费,2=充值,3=返佣,4=提现）\r\n * */\r\nexport function getCommissionInfo(q, types) {\r\n\treturn request.get(\"spread/commission/\" + types, q);\r\n}\r\n\r\n/*\r\n * 积分记录\r\n * */\r\nexport function getIntegralList(q) {\r\n\treturn request.get(\"integral/list\", q);\r\n}\r\n\r\n/**\r\n * 获取分销海报图片\r\n * \r\n */\r\nexport function spreadBanner() {\r\n\t//#ifdef H5 || APP-PLUS\r\n\treturn request.get('spread/banner', {\r\n\t\ttype: 2\r\n\t});\r\n\t//#endif\r\n\t//#ifdef MP\r\n\treturn request.get('spread/banner', {\r\n\t\ttype: 1\r\n\t});\r\n\t//#endif\r\n\r\n}\r\n\r\n/**\r\n *\r\n * 获取推广用户一级和二级\r\n * @param object data\r\n */\r\nexport function spreadPeople(data) {\r\n\treturn request.post('spread/people', data);\r\n}\r\n\r\n/**\r\n * \r\n * 推广佣金/提现总和\r\n * @param int type\r\n */\r\nexport function spreadCount(type) {\r\n\treturn request.get('spread/count/' + type);\r\n}\r\n\r\n/*\r\n * 推广数据\r\n * */\r\nexport function getSpreadInfo() {\r\n\treturn request.get(\"commission\");\r\n}\r\n\r\n\r\n/**\r\n * \r\n * 推广订单\r\n * @param object data\r\n */\r\nexport function spreadOrder(data) {\r\n\treturn request.post('spread/order', data);\r\n}\r\n\r\n/**\r\n * \r\n * 事业部/推广订单\r\n * @param object data\r\n */\r\nexport function divisionOrder(data) {\r\n\treturn request.post('division/order', data);\r\n}\r\n\r\n/*\r\n * 获取推广人排行\r\n * */\r\nexport function getRankList(q) {\r\n\treturn request.get(\"rank\", q);\r\n}\r\n\r\n/*\r\n * 获取佣金排名\r\n * */\r\nexport function getBrokerageRank(q) {\r\n\treturn request.get(\"brokerage_rank\", q);\r\n}\r\n\r\n/**\r\n * 提现申请\r\n * @param object data\r\n */\r\nexport function extractCash(data) {\r\n\treturn request.post('extract/cash', data)\r\n}\r\n\r\n/**\r\n * 提现银行/提现最低金额\r\n * \r\n */\r\nexport function extractBank() {\r\n\treturn request.get('extract/bank');\r\n}\r\n\r\n/**\r\n * 会员等级列表\r\n * \r\n */\r\nexport function userLevelGrade() {\r\n\treturn request.get('user/level/grade');\r\n}\r\n\r\n/**\r\n * 获取某个等级任务\r\n * @param int id 任务id\r\n */\r\nexport function userLevelTask(id) {\r\n\treturn request.get('user/level/task/' + id);\r\n}\r\n\r\n\r\n/**\r\n * 检查用户是否可以成为会员\r\n * \r\n */\r\nexport function userLevelDetection() {\r\n\treturn request.get('user/level/detection');\r\n}\r\n\r\n/**\r\n * \r\n * 地址列表\r\n * @param object data\r\n */\r\nexport function getAddressList(data) {\r\n\treturn request.get('address/list', data);\r\n}\r\n\r\n/**\r\n * 设置默认地址\r\n * @param int id\r\n */\r\nexport function setAddressDefault(id) {\r\n\treturn request.post('address/default/set', {\r\n\t\tid: id\r\n\t})\r\n}\r\n\r\n/**\r\n * 修改 添加地址\r\n * @param object data\r\n */\r\nexport function editAddress(data) {\r\n\treturn request.post('address/edit', data);\r\n}\r\n\r\n/**\r\n * 删除地址\r\n * @param int id\r\n * \r\n */\r\nexport function delAddress(id) {\r\n\treturn request.post('address/del', {\r\n\t\tid: id\r\n\t})\r\n}\r\n\r\n/**\r\n * 获取单个地址\r\n * @param int id \r\n */\r\nexport function getAddressDetail(id) {\r\n\treturn request.get('address/detail/' + id);\r\n}\r\n\r\n/**\r\n * 修改用户信息\r\n * @param object\r\n */\r\nexport function userEdit(data) {\r\n\treturn request.post('user/edit', data);\r\n}\r\n\r\n/*\r\n * 退出登录\r\n * */\r\nexport function getLogout() {\r\n\treturn request.get(\"logout\");\r\n}\r\n/**\r\n * 小程序充值\r\n * \r\n */\r\nexport function rechargeRoutine(data) {\r\n\treturn request.post('recharge/routine', data)\r\n}\r\n/*\r\n * 公众号充值\r\n * \r\n */\r\nexport function rechargeWechat(data) {\r\n\treturn request.post(\"recharge/wechat\", data);\r\n}\r\n/*\r\n * 公众号充值\r\n * \r\n */\r\nexport function recharge(data) {\r\n\treturn request.post(\"recharge/recharge\", data);\r\n}\r\n/**\r\n * 获取默认地址\r\n * \r\n */\r\nexport function getAddressDefault() {\r\n\treturn request.get('address/default');\r\n}\r\n\r\n/**\r\n * 充值金额选择\r\n */\r\nexport function getRechargeApi() {\r\n\treturn request.get(\"recharge/index\");\r\n}\r\n\r\n/**\r\n * 登陆记录\r\n */\r\nexport function setVisit(data) {\r\n\treturn request.post('user/set_visit', {\r\n\t\t...data\r\n\t}, {\r\n\t\tnoAuth: true\r\n\t});\r\n}\r\n\r\n/**\r\n * 客服列表\r\n */\r\nexport function serviceList() {\r\n\treturn request.get(\"user/service/list\");\r\n}\r\n/**\r\n * 客服详情\r\n */\r\nexport function getChatRecord(data) {\r\n\treturn request.get(\"v2/user/service/record\", data);\r\n}\r\n\r\n/**\r\n * 静默绑定推广人\r\n * @param {Object} puid\r\n */\r\nexport function spread(puid) {\r\n\treturn request.post(\"user/spread\", puid);\r\n}\r\n\r\n/**\r\n * 会员详情\r\n */\r\nexport function getlevelInfo() {\r\n\treturn request.get(\"user/level/info\");\r\n}\r\n\r\n/**\r\n * 会员经验列表\r\n */\r\nexport function getlevelExpList(data) {\r\n\treturn request.get(\"user/level/expList\", data);\r\n}\r\n\r\n\r\n/**\r\n * 微信直接手机号登录\r\n */\r\nexport function phoneWxSilenceAuth(data) {\r\n\treturn request.post('v2/phone_wx_silence_auth', data, {\r\n\t\tnoAuth: true\r\n\t});\r\n}\r\n\r\n/**\r\n * 小程序直接手机号登录\r\n */\r\nexport function phoneSilenceAuth(data) {\r\n\treturn request.post('v2/phone_silence_auth', data, {\r\n\t\tnoAuth: true\r\n\t});\r\n}\r\n\r\n/**\r\n * 用户发票列表\r\n * @param {Object} data\r\n */\r\nexport function invoiceList(data) {\r\n\treturn request.get('v2/invoice', data, {\r\n\t\tnoAuth: true\r\n\t});\r\n}\r\n\r\n/**\r\n * 用户添加|修改发票\r\n * @param {Object} data\r\n */\r\nexport function invoiceSave(data) {\r\n\treturn request.post('v2/invoice/save', data, {\r\n\t\tnoAuth: true\r\n\t});\r\n}\r\n\r\n/**\r\n * 用户删除发票\r\n * @param {Object} data\r\n */\r\nexport function invoiceDelete(id) {\r\n\treturn request.get('v2/invoice/del/' + id);\r\n}\r\n\r\n/**\r\n * 获取用户默认发票\r\n * @param {Object} type\r\n */\r\nexport function invoiceDefault(type) {\r\n\treturn request.get('v2/invoice/get_default/' + type);\r\n}\r\n\r\n/**\r\n * 用户单个发票详情\r\n * @param {Object} id\r\n */\r\nexport function invoiceDetail(id) {\r\n\treturn request.get('v2/invoice/detail/' + id);\r\n}\r\n\r\n/**\r\n * 订单申请开票\r\n * @param {Object} id\r\n */\r\nexport function invoiceOrder(data) {\r\n\treturn request.post('v2/order/make_up_invoice', data);\r\n}\r\n\r\n/**\r\n * 订单详情中申请开票\r\n * @param {Object} id\r\n */\r\nexport function makeUpinvoice(data) {\r\n\treturn request.post('v2/order/make_up_invoice', data);\r\n}\r\n\r\n/**\r\n * 会员卡主界面\r\n */\r\nexport function memberCard() {\r\n\treturn request.get('user/member/card/index');\r\n}\r\n\r\n/**\r\n * 卡密领取会员卡\r\n * @param {Object} data\r\n */\r\nexport function memberCardDraw(data) {\r\n\treturn request.post('user/member/card/draw', data);\r\n}\r\n\r\n/**\r\n * 购买会员卡\r\n * @param {Object} data\r\n */\r\nexport function memberCardCreate(data) {\r\n\treturn request.post('user/member/card/create', data);\r\n}\r\n\r\n/**\r\n * 会员优惠券\r\n */\r\nexport function memberCouponsList() {\r\n\treturn request.get('user/member/coupons/list');\r\n}\r\n\r\n/**\r\n * svip推荐商品\r\n * @param {Object} id\r\n */\r\nexport function groomList(id, data) {\r\n\treturn request.get(`groom/list/${id}`, data);\r\n}\r\n\r\n/**\r\n * 付费会员结束\r\n * @param {Object} data\r\n */\r\nexport function memberOverdueTime(data) {\r\n\treturn request.get('user/member/overdue/time', data);\r\n}\r\n\r\n/**\r\n * 新版分享海报信息获取\r\n * \r\n */\r\nexport function spreadMsg() {\r\n\treturn request.get('user/spread_info');\r\n}\r\n\r\n\r\n/**\r\n * 图片链接转base64\r\n * \r\n */\r\nexport function imgToBase(data) {\r\n\treturn request.post('image_base64', data);\r\n}\r\n\r\n/**\r\n * 获取小程序二维码\r\n * \r\n */\r\nexport function routineCode(data) {\r\n\treturn request.get('user/routine_code', data);\r\n}\r\n\r\n/**\r\n * 消息中心\r\n */\r\nexport function serviceRecord(data) {\r\n\treturn request.get('user/record', data);\r\n}\r\n\r\n/**\r\n * 消息中心-站内信列表\r\n */\r\nexport function messageSystem(data) {\r\n\treturn request.get('user/message_system/list', data);\r\n}\r\n\r\n/**\r\n * 消息中心-站内信列表详情\r\n */\r\nexport function getMsgDetails(id) {\r\n\treturn request.get('user/message_system/detail/' + id);\r\n}\r\n\r\n/**\r\n * 消息中心-消息已读/删除\r\n */\r\nexport function msgLookDel(data) {\r\n\treturn request.get('user/message_system/edit_message', data);\r\n}\r\n\r\n/**\r\n * 苹果账号登录\r\n * @param {Object} data\r\n */\r\nexport function appleLogin(data) {\r\n\treturn request.post('apple_login', data, {\r\n\t\tnoAuth: true\r\n\t});\r\n}\r\n\r\n/*\r\n * 获取隐私协议\r\n * */\r\nexport function getUserAgreement(type) {\r\n\treturn request.get(`get_agreement/${type}`, {}, {\r\n\t\tnoAuth: true\r\n\t});\r\n}\r\n\r\n/**\r\n * 获取分销等级列表\r\n * @param int id 任务id\r\n */\r\nexport function agentLevelList() {\r\n\treturn request.get('v2/agent/level_list');\r\n}\r\n\r\n/**\r\n * 获取分销任务列表\r\n * @param int id 任务id\r\n */\r\nexport function agentLevelTaskList(id) {\r\n\treturn request.get('v2/agent/level_task_list?id=' + id);\r\n}\r\n\r\n/**\r\n * 获取代付详情\r\n * @param int id 任务id\r\n */\r\nexport function friendDetail(id) {\r\n\treturn request.get('order/friend_detail?order_id=' + id);\r\n}\r\n\r\n/**\r\n * 员工列表\r\n * @param object data\r\n * \r\n */\r\nexport function clerkPeople(data) {\r\n\treturn request.get('agent/get_staff_list', data)\r\n}\r\n\r\n/**\r\n * \r\n * 员工比例\r\n * @param object data\r\n */\r\nexport function setClerkPercent(data) {\r\n\treturn request.post('agent/set_staff_percent', data);\r\n}\r\n\r\n/**\r\n * \r\n * 删除员工\r\n * @param object data\r\n */\r\nexport function delClerkPercent(id) {\r\n\treturn request.get(`agent/del_staff/${id}`);\r\n}\r\n\r\n/**\r\n * 注销用户\r\n * @param int id\r\n * \r\n */\r\nexport function cancelUser() {\r\n\treturn request.get('user_cancel');\r\n}\r\n/**\r\n * 获取多语言类型\r\n */\r\n\r\nexport function getLangList() {\r\n\treturn request.get('get_lang_type_list', {}, {\r\n\t\tnoAuth: true\r\n\t})\r\n}\r\n\r\n/**\r\n * 获取多语言JSON\r\n */\r\n\r\nexport function getLangJson() {\r\n\treturn request.get('get_lang_json', {}, {\r\n\t\tnoAuth: true\r\n\t})\r\n}\r\n\r\n/**\r\n * 获取多语言是否切换\r\n */\r\n\r\nexport function getLangVersion() {\r\n\treturn request.get('lang_version', {}, {\r\n\t\tnoAuth: true\r\n\t})\r\n}\r\n\r\n/**\r\n * \r\n * 小程序绑定手机号\r\n * @param object data\r\n */\r\nexport function mpBindingPhone(data) {\r\n\treturn request.post('v2/routine/binding_phone', data);\r\n}\r\n\r\n/**\r\n *  签到提醒切换\r\n */\r\n\r\nexport function changeRemindStatus(status) {\r\n\treturn request.get(`sign/remind/${status}`, {}, {\r\n\t\tnoAuth: true\r\n\t})\r\n}\r\n\r\n\r\n/**\r\n * 绑定员工\r\n * \r\n */\r\nexport function spreadAgent(data) {\r\n\treturn request.post(`agent/spread`, data);\r\n}\r\n\r\n\r\n\r\n"], "names": ["request"], "mappings": ";;AAgBO,SAAS,cAAc;AAC7B,SAAOA,cAAO,QAAC,IAAI,MAAM;AAC1B;AAcO,SAAS,QAAQ,MAAM;AAC7B,SAAOA,sBAAQ,KAAK,SAAS,MAAM;AAAA,IAClC,QAAQ;AAAA,EACV,CAAE;AACF;AAMO,SAAS,YAAY,MAAM;AACjC,SAAOA,sBAAQ,KAAK,gBAAgB,MAAM;AAAA,IACzC,QAAQ;AAAA,EACV,CAAE;AACF;AAKO,SAAS,aAAa;AAC5B,SAAOA,sBAAQ,IAAI,eAAe,IAAI;AAAA,IACrC,QAAQ;AAAA,EACV,CAAE;AACF;AAMO,SAAS,eAAe,MAAM;AACpC,SAAOA,sBAAQ,KAAK,mBAAmB,MAAM;AAAA,IAC5C,QAAQ;AAAA,EACV,CAAE;AACF;AAMO,SAAS,SAAS,MAAM;AAC9B,SAAOA,sBAAQ,KAAK,YAAY,MAAM;AAAA,IACrC,QAAQ;AAAA,EACV,CAAE;AACF;AAgBO,SAAS,cAAc;AAC7B,SAAOA,sBAAQ,IAAI,aAAa,IAAI;AAAA,IACnC,QAAQ;AAAA,EACV,CAAE;AACF;AAKO,SAAS,aAAa,MAAM;AAClC,SAAOA,sBAAQ,KAAK,aAAa,IAAI;AACtC;AAMO,SAAS,gBAAgB;AAC/B,SAAOA,cAAO,QAAC,IAAI,aAAa;AACjC;AAMO,SAAS,YAAY,MAAM;AACjC,SAAOA,sBAAQ,IAAI,aAAa,IAAI;AACrC;AAKO,SAAS,kBAAkB;AACjC,SAAOA,cAAO,QAAC,KAAK,eAAe;AACpC;AAOO,SAAS,iBAAiB,MAAM;AACtC,SAAOA,sBAAQ,IAAI,cAAc,IAAI;AACtC;AAoBO,SAAS,gBAAgB,GAAG;AAClC,SAAOA,sBAAQ,IAAI,iBAAiB,CAAC;AACtC;AA4HO,SAAS,eAAe,MAAM;AACpC,SAAOA,sBAAQ,IAAI,gBAAgB,IAAI;AACxC;AAMO,SAAS,kBAAkB,IAAI;AACrC,SAAOA,cAAO,QAAC,KAAK,uBAAuB;AAAA,IAC1C;AAAA,EACF,CAAE;AACF;AAMO,SAAS,YAAY,MAAM;AACjC,SAAOA,sBAAQ,KAAK,gBAAgB,IAAI;AACzC;AAOO,SAAS,WAAW,IAAI;AAC9B,SAAOA,cAAO,QAAC,KAAK,eAAe;AAAA,IAClC;AAAA,EACF,CAAE;AACF;AAMO,SAAS,iBAAiB,IAAI;AACpC,SAAOA,sBAAQ,IAAI,oBAAoB,EAAE;AAC1C;AAMO,SAAS,SAAS,MAAM;AAC9B,SAAOA,sBAAQ,KAAK,aAAa,IAAI;AACtC;AAKO,SAAS,YAAY;AAC3B,SAAOA,cAAO,QAAC,IAAI,QAAQ;AAC5B;AAwCO,SAAS,SAAS,MAAM;AAC9B,SAAOA,cAAO,QAAC,KAAK,kBAAkB;AAAA,IACrC,GAAG;AAAA,EACL,GAAI;AAAA,IACF,QAAQ;AAAA,EACV,CAAE;AACF;AAmBO,SAAS,OAAO,MAAM;AAC5B,SAAOA,sBAAQ,KAAK,eAAe,IAAI;AACxC;AAoBO,SAAS,mBAAmB,MAAM;AACxC,SAAOA,sBAAQ,KAAK,4BAA4B,MAAM;AAAA,IACrD,QAAQ;AAAA,EACV,CAAE;AACF;AAKO,SAAS,iBAAiB,MAAM;AACtC,SAAOA,sBAAQ,KAAK,yBAAyB,MAAM;AAAA,IAClD,QAAQ;AAAA,EACV,CAAE;AACF;AAqKO,SAAS,WAAW,MAAM;AAChC,SAAOA,sBAAQ,KAAK,eAAe,MAAM;AAAA,IACxC,QAAQ;AAAA,EACV,CAAE;AACF;AAKO,SAAS,iBAAiB,MAAM;AACtC,SAAOA,cAAAA,QAAQ,IAAI,iBAAiB,IAAI,IAAI,IAAI;AAAA,IAC/C,QAAQ;AAAA,EACV,CAAE;AACF;AA0DO,SAAS,aAAa;AAC5B,SAAOA,cAAO,QAAC,IAAI,aAAa;AACjC;AAKO,SAAS,cAAc;AAC7B,SAAOA,sBAAQ,IAAI,sBAAsB,IAAI;AAAA,IAC5C,QAAQ;AAAA,EACV,CAAE;AACF;AAMO,SAAS,cAAc;AAC7B,SAAOA,sBAAQ,IAAI,iBAAiB,IAAI;AAAA,IACvC,QAAQ;AAAA,EACV,CAAE;AACF;AAMO,SAAS,iBAAiB;AAChC,SAAOA,sBAAQ,IAAI,gBAAgB,IAAI;AAAA,IACtC,QAAQ;AAAA,EACV,CAAE;AACF;AAOO,SAAS,eAAe,MAAM;AACpC,SAAOA,sBAAQ,KAAK,4BAA4B,IAAI;AACrD;AAMO,SAAS,mBAAmB,QAAQ;AAC1C,SAAOA,cAAAA,QAAQ,IAAI,eAAe,MAAM,IAAI,IAAI;AAAA,IAC/C,QAAQ;AAAA,EACV,CAAE;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}