{"version": 3, "file": "index.js", "sources": ["pages/users/user_phone/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlcnMvdXNlcl9waG9uZS9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"container\" :style=\"colorStyle\">\r\n\t\t<navbar></navbar>\r\n\t\t<view class=\"content-box\">\r\n\t\t\t<view class=\"logo-box\">\r\n\t\t\t\t<image src=\"/static/logo.png\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"login-box\">\r\n\t\t\t\t<view class=\"title\">绑定手机号</view>\r\n\t\t\t\t<view class=\"sub-title\">为了提供更好的服务，需要绑定您的手机号</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"input-item df\">\r\n\t\t\t\t\t<view class=\"icon-mobile\">\r\n\t\t\t\t\t\t<image src=\"/static/img/icon-mobile.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input type='number' :placeholder='$t(`填写手机号码`)' v-model=\"phone\" maxlength=\"11\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"input-item df\">\r\n\t\t\t\t\t<view class=\"icon-code\">\r\n\t\t\t\t\t\t<image src=\"/static/img/icon-code.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input type='number' :placeholder='$t(`填写验证码`)' maxlength=\"6\" v-model=\"captcha\"/>\r\n\t\t\t\t\t<view :class=\"['send-code', disabled ? 'active' : '']\" @tap=\"code\">{{text}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"tips-text\">* 绑定后，可使用手机号码直接登录</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"btn-submit df\" @tap=\"editPwd\">{{$t(`确认绑定`)}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<Verify @success=\"success\" :captchaType=\"captchaType\" :imgSize=\"{ width: '330px', height: '155px' }\" ref=\"verify\"></Verify>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport sendVerifyCode from \"@/mixins/SendVerifyCode\";\r\n\timport Verify from '../components/verify/index.vue';\r\n\timport {\r\n\t\tregisterVerify,\r\n\t\tbindingUserPhone,\r\n\t\tverifyCode,\r\n\t\tupdatePhone\r\n\t} from '@/api/api.js';\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport colors from '@/mixins/color.js';\r\n\texport default {\r\n\t\tmixins: [sendVerifyCode, colors],\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t\tVerify\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tphone: '',\r\n\t\t\t\tcaptcha: '',\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tkey: '',\r\n\t\t\t\tauthKey: '',\r\n\t\t\t\ttype: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\tonLoad(options) {\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tverifyCode().then(res => {\r\n\t\t\t\t\tthis.$set(this, 'key', res.data.key)\r\n\t\t\t\t});\r\n\t\t\t\tthis.authKey = options.key || '';\r\n\t\t\t\tthis.url = options.url || '';\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t\tthis.type = options.type || 0\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonLoadFun: function() {},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\teditPwd: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (!that.phone) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请填写手机号码`)\r\n\t\t\t\t});\r\n\t\t\t\tif (!(/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.phone))) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请输入正确的手机号码`)\r\n\t\t\t\t});\r\n\t\t\t\tif (!that.captcha) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请填写验证码`)\r\n\t\t\t\t});\r\n\t\t\t\tif (this.type == 0) {\r\n\t\t\t\t\tbindingUserPhone({\r\n\t\t\t\t\t\tphone: that.phone,\r\n\t\t\t\t\t\tcaptcha: that.captcha\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tif (res.data !== undefined && res.data.is_bind) {\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle: that.$t(`是否绑定账号`),\r\n\t\t\t\t\t\t\t\tcontent: res.msg,\r\n\t\t\t\t\t\t\t\tconfirmText: that.$t(`绑定`),\r\n\t\t\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\tbindingUserPhone({\r\n\t\t\t\t\t\t\t\t\t\t\tphone: that.phone,\r\n\t\t\t\t\t\t\t\t\t\t\tcaptcha: that.captcha,\r\n\t\t\t\t\t\t\t\t\t\t\tstep: 1\r\n\t\t\t\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\t\t\turl: '/pages/users/user_info/index'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: that.$t(`您已取消绑定！`)\r\n\t\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\t\turl: '/pages/users/user_info/index'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: that.$t(`绑定成功`),\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\turl: '/pages/users/user_info/index'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tupdatePhone({\r\n\t\t\t\t\t\tphone: that.phone,\r\n\t\t\t\t\t\tcaptcha: that.captcha,\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\turl: '/pages/users/user_info/index'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}).catch(error => {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: error,\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsuccess(data) {\r\n\t\t\t\tthis.$refs.verify.hide()\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tverifyCode().then(res => {\r\n\t\t\t\t\tregisterVerify(that.phone, 'reset', res.data.key, this.captchaType, data.captchaVerification)\r\n\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: res.msg\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tthat.sendCode();\r\n\t\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t});\r\n\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 发送验证码\r\n\t\t\t *\r\n\t\t\t */\r\n\t\t\tasync code() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (!that.phone) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请填写手机号码`)\r\n\t\t\t\t});\r\n\t\t\t\tif (!(/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.phone))) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请输入正确的手机号码`)\r\n\t\t\t\t});\r\n\t\t\t\tthis.$refs.verify.show();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.container {\r\n\twidth: 100%;\r\n\tbackground: #fff;\r\n\theight: 100vh;\r\n\toverflow: hidden;\r\n}\r\n\r\n.content-box {\r\n\twidth: 100%;\r\n\tmargin-top: 30px;\r\n}\r\n\r\n.logo-box {\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\t/* #ifdef APP-VUE */\r\n\tmargin-top: 50rpx;\r\n\t/* #endif */\r\n\t/* #ifndef APP-VUE */\r\n\tmargin-top: 200rpx;\r\n\t/* #endif */\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.logo-box image {\r\n\twidth: 240rpx;\r\n\theight: 240rpx;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.login-box {\r\n\twidth: calc(100% - 60rpx);\r\n\tmargin: 30rpx;\r\n}\r\n\r\n.login-box .title {\r\n\tfont-size: 40rpx;\r\n\tfont-weight: 700;\r\n\tcolor: #000;\r\n}\r\n\r\n.login-box .sub-title {\r\n\tmargin-top: 20rpx;\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.login-box .input-item {\r\n\tmargin-top: 60rpx;\r\n\tpadding: 0 30rpx;\r\n\twidth: calc(100% - 60rpx);\r\n\theight: 100rpx;\r\n\tborder-radius: 100rpx;\r\n\tbackground: #f8f8f8;\r\n\tposition: relative;\r\n}\r\n\r\n.input-item .icon-mobile,\r\n.input-item .icon-code {\r\n\tmargin-right: 20rpx;\r\n\twidth: 36rpx;\r\n\theight: 36rpx;\r\n}\r\n\r\n.input-item .icon-mobile image,\r\n.input-item .icon-code image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.input-item input {\r\n\twidth: calc(100% - 56rpx);\r\n\theight: 100%;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.input-item .send-code {\r\n\tposition: absolute;\r\n\tright: 30rpx;\r\n\twidth: 180rpx;\r\n\theight: 60rpx;\r\n\tline-height: 60rpx;\r\n\ttext-align: center;\r\n\tfont-size: 24rpx;\r\n\tfont-weight: 700;\r\n\tbackground: #000;\r\n\tcolor: #fff;\r\n\tborder-radius: 30rpx;\r\n}\r\n\r\n.input-item .send-code.active {\r\n\tbackground: #f5f5f5;\r\n\tcolor: #999;\r\n}\r\n\r\n.login-box .tips-text {\r\n\tmargin-top: 30rpx;\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.login-box .btn-submit {\r\n\tmargin-top: 60rpx;\r\n\twidth: 100%;\r\n\theight: 100rpx;\r\n\tline-height: 100rpx;\r\n\ttext-align: center;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 700;\r\n\tbackground: #000;\r\n\tcolor: #fff;\r\n\tborder-radius: 100rpx;\r\n\tjustify-content: center;\r\n}\r\n\r\n.df {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/uniapp/vue3/pages/users/user_phone/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["sendVerifyCode", "colors", "mapGetters", "verifyCode", "<PERSON><PERSON><PERSON><PERSON>", "bindingUserPhone", "uni", "res", "updatePhone", "registerVerify"], "mappings": ";;;;;;;AAsCC,eAAe,MAAW;AAc1B,MAAO,YAAW,MAAW;AAG7B,MAAK,YAAU;AAAA,EACd,QAAQ,CAACA,sBAAc,gBAAEC,mBAAM;AAAA,EAC/B,YAAY;AAAA,IAEX;AAAA,IAEA;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA;AAAA,MACR,YAAY;AAAA;AAAA,MACZ,KAAK;AAAA,MACL,SAAS;AAAA,MACT,MAAM;AAAA;EAEP;AAAA,EACD,UAAUC,cAAAA,WAAW,CAAC,SAAS,CAAC;AAAA,EAChC,OAAO,SAAS;AACf,QAAI,KAAK,SAAS;AACjBC,yBAAY,EAAC,KAAK,SAAO;AACxB,aAAK,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAAA,MACpC,CAAC;AACD,WAAK,UAAU,QAAQ,OAAO;AAC9B,WAAK,MAAM,QAAQ,OAAO;AAAA,WACpB;AACNC,iBAAAA;IACD;AACA,SAAK,OAAO,QAAQ,QAAQ;AAAA,EAC5B;AAAA,EACD,SAAS;AAAA,IACR,WAAW,WAAW;AAAA,IAAE;AAAA;AAAA,IAExB,WAAW,SAAS,GAAG;AACtB,WAAK,aAAa;AAAA,IAClB;AAAA,IACD,SAAS,WAAW;AACnB,UAAI,OAAO;AACX,UAAI,CAAC,KAAK;AAAO,eAAO,KAAK,MAAM,KAAK;AAAA,UACvC,OAAO,KAAK,GAAG,SAAS;AAAA,QACzB,CAAC;AACD,UAAI,CAAE,2BAA2B,KAAK,KAAK,KAAK;AAAI,eAAO,KAAK,MAAM,KAAK;AAAA,UAC1E,OAAO,KAAK,GAAG,YAAY;AAAA,QAC5B,CAAC;AACD,UAAI,CAAC,KAAK;AAAS,eAAO,KAAK,MAAM,KAAK;AAAA,UACzC,OAAO,KAAK,GAAG,QAAQ;AAAA,QACxB,CAAC;AACD,UAAI,KAAK,QAAQ,GAAG;AACnBC,iCAAiB;AAAA,UAChB,OAAO,KAAK;AAAA,UACZ,SAAS,KAAK;AAAA,SACd,EAAE,KAAK,SAAO;AACd,cAAI,IAAI,SAAS,UAAa,IAAI,KAAK,SAAS;AAC/CC,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,KAAK,GAAG,QAAQ;AAAA,cACvB,SAAS,IAAI;AAAA,cACb,aAAa,KAAK,GAAG,IAAI;AAAA,cACzB,QAAQC,MAAK;AACZ,oBAAIA,KAAI,SAAS;AAChBF,2CAAiB;AAAA,oBAChB,OAAO,KAAK;AAAA,oBACZ,SAAS,KAAK;AAAA,oBACd,MAAM;AAAA,mBACN,EAAE,KAAK,CAAAE,SAAO;AACd,2BAAO,KAAK,MAAM,KAAK;AAAA,sBACtB,OAAOA,KAAI;AAAA,sBACX,MAAM;AAAA,oBACP,GAAG;AAAA,sBACF,KAAK;AAAA,sBACL,KAAK;AAAA,oBACN,CAAC;AAAA,kBACF,CAAC,EAAE,MAAM,SAAO;AACf,2BAAO,KAAK,MAAM,KAAK;AAAA,sBACtB,OAAO;AAAA,oBACR,CAAC;AAAA,mBACD;AAAA,gBACF,WAAWA,KAAI,QAAQ;AACtB,yBAAO,KAAK,MAAM,KAAK;AAAA,oBACtB,OAAO,KAAK,GAAG,SAAS;AAAA,kBACzB,GAAG;AAAA,oBACF,KAAK;AAAA,oBACL,KAAK;AAAA,kBACN,CAAC;AAAA,gBACF;AAAA,cACD;AAAA,YACD,CAAC;AAAA,UACA;AACD,mBAAO,KAAK,MAAM,KAAK;AAAA,cACtB,OAAO,KAAK,GAAG,MAAM;AAAA,cACrB,MAAM;AAAA,YACP,GAAG;AAAA,cACF,KAAK;AAAA,cACL,KAAK;AAAA,YACN,CAAC;AAAA,QACH,CAAC,EAAE,MAAM,SAAO;AACf,iBAAO,KAAK,MAAM,KAAK;AAAA,YACtB,OAAO;AAAA,UACR,CAAC;AAAA,SACD;AAAA,aACK;AACNC,4BAAY;AAAA,UACX,OAAO,KAAK;AAAA,UACZ,SAAS,KAAK;AAAA,SACd,EAAE,KAAK,SAAO;AACd,iBAAO,KAAK,MAAM,KAAK;AAAA,YACtB,OAAO,IAAI;AAAA,YACX,MAAM;AAAA,UACP,GAAG;AAAA,YACF,KAAK;AAAA,YACL,KAAK;AAAA,UACN,CAAC;AAAA,QACF,CAAC,EAAE,MAAM,WAAS;AACjB,iBAAO,KAAK,MAAM,KAAK;AAAA,YACtB,OAAO;AAAA,UACR,CAAC;AAAA,SACD;AAAA,MACF;AAAA,IACA;AAAA,IACD,QAAQ,MAAM;AACb,WAAK,MAAM,OAAO,KAAK;AACvB,UAAI,OAAO;AACXL,yBAAY,EAAC,KAAK,SAAO;AACxBM,gBAAAA,eAAe,KAAK,OAAO,SAAS,IAAI,KAAK,KAAK,KAAK,aAAa,KAAK,mBAAmB,EAC1F,KAAK,CAAAF,SAAO;AACZ,eAAK,MAAM,KAAK;AAAA,YACf,OAAOA,KAAI;AAAA,UACZ,CAAC;AACD,eAAK,SAAQ;AAAA,QACd,CAAC,EAAE,MAAM,SAAO;AACf,iBAAO,KAAK,MAAM,KAAK;AAAA,YACtB,OAAO;AAAA,UACR,CAAC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IAED;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,OAAO;AACZ,UAAI,OAAO;AACX,UAAI,CAAC,KAAK;AAAO,eAAO,KAAK,MAAM,KAAK;AAAA,UACvC,OAAO,KAAK,GAAG,SAAS;AAAA,QACzB,CAAC;AACD,UAAI,CAAE,2BAA2B,KAAK,KAAK,KAAK;AAAI,eAAO,KAAK,MAAM,KAAK;AAAA,UAC1E,OAAO,KAAK,GAAG,YAAY;AAAA,QAC5B,CAAC;AACD,WAAK,MAAM,OAAO;AAClB;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChND,GAAG,WAAW,eAAe;"}