{"version": 3, "file": "index.js", "sources": ["components/eidtUserModal/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy9laWR0VXNlck1vZGFsL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view :style=\"colorStyle\">\n\t\t<view class=\"product-window\" :class=\"{'on':isShow}\">\n\t\t\t<view class=\"iconfont icon-guanbi\" @click=\"closeAttr\"></view>\n\t\t\t<view class=\"mp-data\">\n\t\t\t\t<image :src=\"mpData.siteLogo\" mode=\"\"></image>\n\t\t\t\t<text class=\"mp-name\">{{mpData.siteName}} 申请</text>\n\t\t\t</view>\n\t\t\t<view class=\"trip-msg\">\n\t\t\t\t<view class=\"title\">\n\t\t\t\t\t{{$t(`获取您的昵称、头像`)}}\n\t\t\t\t</view>\n\t\t\t\t<view class=\"trip\">\n\t\t\t\t\t{{$t(`提供具有辨识度的用户中心界面`)}}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<form @submit=\"formSubmit\">\n\t\t\t\t<view class=\"edit\">\n\t\t\t\t\t<view class=\"avatar edit-box\">\n\t\t\t\t\t\t<view class=\"left\">\n\t\t\t\t\t\t\t<view class=\"head\">{{$t(`头像`)}}</view>\n\t\t\t\t\t\t\t<!-- <image :src=\"userInfo.avatar || defaultAvatar\" mode=\"\"></image> -->\n\t\t\t\t\t\t\t<view class=\"avatar-box\" v-if=\"!mp_is_new\" @click.stop='uploadpic'>\n\t\t\t\t\t\t\t\t<image :src=\"userInfo.avatar || defHead\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<button v-else class=\"avatar-box\" open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\">\n\t\t\t\t\t\t\t\t<image :src=\"userInfo.avatar || defHead\"></image>\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- <view class=\"iconfont icon-xiangyou\"></view> -->\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"nickname edit-box\">\n\t\t\t\t\t\t<view class=\"left\">\n\t\t\t\t\t\t\t<view class=\"head\">{{$t(`昵称`)}}</view>\n\t\t\t\t\t\t\t<view class='input'><input type='nickname' placeholder-class=\"pl-sty\"\n\t\t\t\t\t\t\t\t\t:placeholder=\"$t(`请输入昵称`)\" name='nickname' :maxlength=\"16\"\n\t\t\t\t\t\t\t\t\t:value='userInfo.nickname'></input>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- <view class=\"iconfont icon-xiangyou\"></view> -->\n\t\t\t\t\t</view>\n\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"bottom\">\n\t\t\t\t\t<button class=\"save\" formType=\"submit\" :class=\"{'open': userInfo.avatar}\">\n\t\t\t\t\t\t{{$t(`保存`)}}\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</form>\n\t\t</view>\n\t\t<canvas canvas-id=\"canvas\" v-if=\"canvasStatus\"\n\t\t\t:style=\"{width: canvasWidth + 'px', height: canvasHeight + 'px',position: 'absolute',left:'-100000px',top:'-100000px'}\"></canvas>\n\t\t<view class=\"mask\" @touchmove.prevent v-if=\"isShow\" @click=\"closeAttr\"></view>\n\t</view>\n\n</template>\n\n<script>\n\timport colors from \"@/mixins/color\";\n\timport Cache from '@/utils/cache';\n\timport {\n\t\tuserEdit,\n\t} from '@/api/user.js';\n\texport default {\n\t\tmixins: [colors],\n\t\tprops: {\n\t\t\tisShow: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tdefHead: '/static/img/def_avatar.png',\n\t\t\t\tmp_is_new: this.$Cache.get('MP_VERSION_ISNEW') || false,\n\t\t\t\tuserInfo: {\n\t\t\t\t\tavatar: '',\n\t\t\t\t\tnickname: '',\n\t\t\t\t},\n\t\t\t\tmpData: uni.getStorageSync('copyRight'),\n\t\t\t\tcanvasStatus: false,\n\t\t\t};\n\t\t},\n\t\tmounted() {\n\n\t\t},\n\t\tmethods: {\n\t\t\t/**\n\t\t\t * 上传文件\n\t\t\t * \n\t\t\t */\n\t\t\tuploadpic: function() {\n\t\t\t\tlet that = this;\n\t\t\t\tthis.canvasStatus = true\n\t\t\t\tthat.$util.uploadImageChange('upload/image', (res) => {\n\t\t\t\t\tlet userInfo = that.userInfo;\n\t\t\t\t\tif (userInfo !== undefined) {\n\t\t\t\t\t\tthat.userInfo.avatar = res.data.url;\n\t\t\t\t\t}\n\t\t\t\t\tthis.canvasStatus = false\n\t\t\t\t}, (res) => {\n\t\t\t\t\tthis.canvasStatus = false\n\t\t\t\t}, (res) => {\n\t\t\t\t\tthis.canvasWidth = res.w\n\t\t\t\t\tthis.canvasHeight = res.h\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 微信头像获取\n\t\t\tonChooseAvatar(e) {\n\t\t\t\tconst {\n\t\t\t\t\tavatarUrl\n\t\t\t\t} = e.detail\n\t\t\t\tthis.$util.uploadImgs('upload/image', avatarUrl, (res) => {\n\t\t\t\t\tthis.userInfo.avatar = res.data.url\n\t\t\t\t}, (err) => {\n\t\t\t\t\tconsole.log(err)\n\t\t\t\t})\n\t\t\t},\n\t\t\tcloseAttr: function() {\n\t\t\t\tthis.$emit('closeEdit');\n\t\t\t},\n\t\t\t/**\n\t\t\t * 提交修改\n\t\t\t */\n\t\t\tformSubmit(e) {\n\t\t\t\tlet that = this\n\t\t\t\tif (!this.userInfo.avatar) return that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请上传头像`)\n\t\t\t\t});\n\t\t\t\tif (!e.detail.value.nickname) return that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请输入昵称`)\n\t\t\t\t});\n\t\t\t\tthis.userInfo.nickname = e.detail.value.nickname\n\t\t\t\tuserEdit(this.userInfo).then(res => {\n\t\t\t\t\tthis.$emit('editSuccess')\n\t\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\t\ttitle: res.msg,\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t}, {\n\t\t\t\t\t\ttab: 3\n\t\t\t\t\t});\n\t\t\t\t}).catch(msg => {\n\t\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\t\ttitle: msg || that.$t(`保存失败`)\n\t\t\t\t\t}, {\n\t\t\t\t\t\ttab: 3,\n\t\t\t\t\t\turl: 1\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n<style>\n\t.pl-sty {\n\t\tcolor: #999999;\n\t\tfont-size: 30rpx;\n\t}\n</style>\n<style scoped lang=\"scss\">\n\t.product-window.on {\n\t\ttransform: translate3d(0, 0, 0);\n\t}\n\n\t.mask {\n\t\tz-index: 99;\n\t}\n\n\t.product-window {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\twidth: 100%;\n\t\tleft: 0;\n\t\tbackground-color: #fff;\n\t\tz-index: 1000;\n\t\tborder-radius: 20rpx 20rpx 0 0;\n\t\ttransform: translate3d(0, 100%, 0);\n\t\ttransition: all .3s cubic-bezier(.25, .5, .5, .9);\n\t\tpadding: 38rpx 40rpx;\n\t\tpadding-bottom: 80rpx;\n\t\tpadding-bottom: calc(80rpx + constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/\n\t\tpadding-bottom: calc(80rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/\n\n\t\t.icon-guanbi {\n\t\t\tposition: absolute;\n\t\t\ttop: 40rpx;\n\t\t\tright: 40rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-weight: bold;\n\t\t\tcolor: #999;\n\t\t}\n\n\t\t.mp-data {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 30rpx;\n\n\t\t\t.mp-name {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #000000;\n\t\t\t}\n\n\t\t\timage {\n\t\t\t\twidth: 48rpx;\n\t\t\t\theight: 48rpx;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tmargin-right: 16rpx;\n\t\t\t}\n\t\t}\n\n\t\t.trip-msg {\n\t\t\tpadding-bottom: 32rpx;\n\t\t\tborder-bottom: 1px solid #F5F5F5;\n\n\t\t\t.title {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #000;\n\t\t\t\tmargin-bottom: 6rpx;\n\t\t\t}\n\n\t\t\t.trip {\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tcolor: #777777;\n\t\t\t}\n\t\t}\n\n\t\t.edit {\n\t\t\tborder-bottom: 1px solid #F5F5F5;\n\n\t\t\t.avatar {\n\t\t\t\tborder-bottom: 1px solid #F5F5F5;\n\t\t\t}\n\n\t\t\t.nickname {\n\t\t\t\t.input {\n\t\t\t\t\twidth: 100%;\n\n\t\t\t\t}\n\n\t\t\t\tinput {\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.edit-box {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tpadding: 22rpx 0;\n\n\t\t\t\t.left {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tflex: 1;\n\n\t\t\t\t\t.head {\n\t\t\t\t\t\tcolor: rgba(0, 0, 0, 0.9);\n\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\tmargin-right: 60rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\tbutton {\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\timage {\n\t\t\t\t\twidth: 80rpx;\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t\tborder-radius: 6rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.icon-xiangyou {\n\t\t\t\tcolor: #cfcfcf;\n\t\t\t}\n\t\t}\n\n\t\t.bottom {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\n\t\t\t.save {\n\t\t\t\tborder: 1px solid #F5F5F5;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\twidth: 368rpx;\n\t\t\t\theight: 80rpx;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tmargin-top: 52rpx;\n\t\t\t\tbackground-color: #F5F5F5;\n\t\t\t\tcolor: #ccc;\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t}\n\n\t\t\t.save.open {\n\t\t\t\tborder: 1px solid #fff;\n\t\t\t\tbackground-color: #07C160;\n\t\t\t\tcolor: #fff;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import Component from 'D:/uniapp/vue3/components/eidtUserModal/index.vue'\nwx.createComponent(Component)"], "names": ["colors", "uni", "userEdit"], "mappings": ";;;;;AAgEC,MAAK,YAAU;AAAA,EACd,QAAQ,CAACA,aAAAA,MAAM;AAAA,EACf,OAAO;AAAA,IACN,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,WAAW,KAAK,OAAO,IAAI,kBAAkB,KAAK;AAAA,MAClD,UAAU;AAAA,QACT,QAAQ;AAAA,QACR,UAAU;AAAA,MACV;AAAA,MACD,QAAQC,cAAAA,MAAI,eAAe,WAAW;AAAA,MACtC,cAAc;AAAA;EAEf;AAAA,EACD,UAAU;AAAA,EAET;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,IAKR,WAAW,WAAW;AACrB,UAAI,OAAO;AACX,WAAK,eAAe;AACpB,WAAK,MAAM,kBAAkB,gBAAgB,CAAC,QAAQ;AACrD,YAAI,WAAW,KAAK;AACpB,YAAI,aAAa,QAAW;AAC3B,eAAK,SAAS,SAAS,IAAI,KAAK;AAAA,QACjC;AACA,aAAK,eAAe;AAAA,MACpB,GAAE,CAAC,QAAQ;AACX,aAAK,eAAe;AAAA,MACpB,GAAE,CAAC,QAAQ;AACX,aAAK,cAAc,IAAI;AACvB,aAAK,eAAe,IAAI;AAAA,MACzB,CAAC;AAAA,IACD;AAAA;AAAA,IAED,eAAe,GAAG;AACjB,YAAM;AAAA,QACL;AAAA,MACD,IAAI,EAAE;AACN,WAAK,MAAM,WAAW,gBAAgB,WAAW,CAAC,QAAQ;AACzD,aAAK,SAAS,SAAS,IAAI,KAAK;AAAA,MAChC,GAAE,CAAC,QAAQ;AACXA,sBAAAA,gEAAY,GAAG;AAAA,OACf;AAAA,IACD;AAAA,IACD,WAAW,WAAW;AACrB,WAAK,MAAM,WAAW;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA,IAID,WAAW,GAAG;AACb,UAAI,OAAO;AACX,UAAI,CAAC,KAAK,SAAS;AAAQ,eAAO,KAAK,MAAM,KAAK;AAAA,UACjD,OAAO,KAAK,GAAG,OAAO;AAAA,QACvB,CAAC;AACD,UAAI,CAAC,EAAE,OAAO,MAAM;AAAU,eAAO,KAAK,MAAM,KAAK;AAAA,UACpD,OAAO,KAAK,GAAG,OAAO;AAAA,QACvB,CAAC;AACD,WAAK,SAAS,WAAW,EAAE,OAAO,MAAM;AACxCC,eAAAA,SAAS,KAAK,QAAQ,EAAE,KAAK,SAAO;AACnC,aAAK,MAAM,aAAa;AACxB,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,IAAI;AAAA,UACX,MAAM;AAAA,QACP,GAAG;AAAA,UACF,KAAK;AAAA,QACN,CAAC;AAAA,MACF,CAAC,EAAE,MAAM,SAAO;AACf,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,OAAO,KAAK,GAAG,MAAM;AAAA,QAC7B,GAAG;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AAAA,QACN,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvJD,GAAG,gBAAgB,SAAS;"}