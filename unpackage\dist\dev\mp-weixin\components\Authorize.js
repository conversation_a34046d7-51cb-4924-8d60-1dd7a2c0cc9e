"use strict";
const common_vendor = require("../common/vendor.js");
const utils_cache = require("../utils/cache.js");
const api_public = require("../api/public.js");
const config_cache = require("../config/cache.js");
const stores_user = require("../stores/user.js");
const libs_routine = require("../libs/routine.js");
const app = getApp();
const { LOGO_URL, EXPIRES_TIME, USER_INFO, STATE_R_KEY } = config_cache.cacheConfig;
const _sfc_main = {
  name: "Authorize",
  props: {
    isAuto: {
      type: Boolean,
      default: true
    },
    isGoIndex: {
      type: Boolean,
      default: true
    },
    isShowAuth: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      userStore: stores_user.useUserStore(),
      logoUrl: "",
      authKey: ""
    };
  },
  computed: {
    isLogin() {
      return this.userStore.isLoggedIn;
    },
    userInfo() {
      return this.userStore.userInfo;
    }
  },
  watch: {
    isLogin(n) {
      n === true && this.$emit("onLoadFun", this.userInfo);
    }
  },
  mounted() {
    this.getLogoUrl();
    if (!this.isLogin && !utils_cache.Cache.has(STATE_R_KEY)) {
      common_vendor.wx$1.login({
        success(res) {
          utils_cache.Cache.set(STATE_R_KEY, res.code, 10800);
          app.globalData.spid ? app.globalData.spid : "";
        }
      });
    } else {
      this.setAuthStatus();
    }
  },
  methods: {
    setAuthStatus() {
      libs_routine.Routine.authorize().then((res) => {
        if (res.islogin === false)
          this.setUserInfo();
        else
          this.$emit("onLoadFun", this.userInfo);
      }).catch((res) => {
        if (this.isAuto)
          this.$emit("authColse", true);
      });
    },
    getUserInfo(code) {
      libs_routine.Routine.getUserInfo().then((res) => {
        let userInfo = res.userInfo;
        userInfo.code = code;
        userInfo.spread_spid = app.globalData.spid;
        userInfo.spread_code = app.globalData.code;
        libs_routine.Routine.authUserInfo(userInfo).then((res2) => {
          common_vendor.index.hideLoading();
          this.$emit("authColse", false);
          this.$emit("onLoadFun", this.userInfo);
        }).catch((res2) => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: res2.msg,
            icon: "none",
            duration: 2e3
          });
        });
      }).catch((res) => {
        common_vendor.index.hideLoading();
      });
    },
    getUserPhoneNumber(encryptedData, iv, code) {
      api_public.routineBindingPhone({
        encryptedData,
        iv,
        code,
        spid: app.globalData.spid,
        spread: app.globalData.code
      }).then((res) => {
        this.userStore.setToken({
          token: res.data.token,
          time: res.data.expires_time - this.$Cache.time()
        });
        this.$emit("authColse", false);
        this.$emit("onLoadFun", res.data.userInfo);
        common_vendor.index.hideLoading();
      }).catch((res) => {
        common_vendor.index.hideLoading();
      });
    },
    setUserInfo(e) {
      common_vendor.index.showLoading({ title: "正在登录中" });
      libs_routine.Routine.getCode().then((code) => {
        this.getUserPhoneNumber(e.detail.encryptedData, e.detail.iv, code);
      }).catch((res) => {
        common_vendor.index.hideLoading();
      });
    },
    getLogoUrl() {
      let that = this;
      if (utils_cache.Cache.has(LOGO_URL)) {
        this.logoUrl = utils_cache.Cache.get(LOGO_URL);
        return;
      }
      api_public.getLogo().then((res) => {
        that.logoUrl = res.data.logo_url;
        utils_cache.Cache.set(LOGO_URL, that.logoUrl);
      });
    },
    close() {
      let pages = getCurrentPages();
      pages[pages.length - 1];
      if (this.isGoIndex) {
        common_vendor.index.navigateTo({ url: "/pages/index/index" });
      } else {
        this.$emit("authColse", false);
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.isShowAuth
  }, $props.isShowAuth ? {
    b: $data.logoUrl,
    c: common_vendor.o((...args) => $options.close && $options.close(...args)),
    d: common_vendor.o((...args) => $options.setUserInfo && $options.setUserInfo(...args))
  } : {}, {
    e: $props.isShowAuth
  }, $props.isShowAuth ? {
    f: common_vendor.o((...args) => $options.close && $options.close(...args))
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-127aa7e5"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/Authorize.js.map
