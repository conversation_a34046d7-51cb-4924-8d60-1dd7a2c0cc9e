
page {
  background: #f8f8f8;
}

/* 导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.bar-box .bar-back {
  padding: 0 30rpx;
  width: 34rpx;
  height: 100%;
}
.bar-box .bar-title {
  font-size: 32rpx;
  font-weight: 700;
}
.nav-box {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 99;
  margin-top: -1px;
  width: 100%;
  height: 80rpx;
  justify-content: space-between;
}
.nav-box .nav-item {
  width: 16.66%;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.nav-box .nav-item text {
  font-weight: 700;
  transition: all .3s ease-in-out;
}
.nav-box .nav-line {
  position: absolute;
  bottom: 12rpx;
  width: 18rpx;
  height: 6rpx;
  border-radius: 6rpx;
  background: #000;
  transition: opacity .3s ease-in-out;
}

/* 内容区 */
.content {
  width: 100%;
  flex-direction: column;
}
.list-box {
  margin-top: 30rpx;
  width: calc(100% - 100rpx);
  padding: 30rpx 20rpx;
  border-radius: 30rpx;
  background: #fff;
}
.list-box .list-top {
  width: 100%;
  justify-content: space-between;
}
.list-top text {
  color: #999;
  font-size: 26rpx;
  font-weight: 700;
}
.list-info {
  width: 100%;
  padding: 30rpx 0;
  justify-content: space-between;
}
.list-info .list-info-img {
  display: flex;
  align-items: center;
  position: relative;
}
.list-info .list-info-img image,
.list-info .mask-img {
  width: 130rpx;
  height: 130rpx;
  border-radius: 8rpx;
}
.list-info .list-info-img image {
  margin-right: 8rpx;
  animation: fadeIn .45s ease;
}
.list-info .mask-img {
  position: absolute;
  top: 0;
  right: 8rpx;
  line-height: 130rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 700;
  color: #fff;
  background: rgba(0, 0, 0, .3);
}
.list-info .list-info-pn {
  display: flex;
  align-items: flex-end;
  flex-direction: column;
}
.list-info-pn .num {
  margin-top: 10rpx;
  color: #999;
  font-size: 20rpx;
  font-weight: 500;
}
.list-wuliu {
  margin-bottom: 30rpx;
  z-index: 1;
  width: calc(100% - 40rpx);
  padding: 20rpx;
  color: #999;
  font-size: 24rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
}
.list-wuliu text {
  margin: 0 10rpx;
  color: #000;
  font-weight: 700;
}
.list-box .list-btn {
  justify-content: flex-end;
}
.list-btn view {
  margin-left: 20rpx;
  padding: 0 30rpx;
  height: 60rpx;
  border-radius: 30rpx;
}
.list-btn view image {
  width: 26rpx;
  height: 26rpx;
  margin-right: 10rpx;
}
.list-btn view text {
  font-size: 20rpx;
  line-height: 20rpx;
  font-weight: 700;
}
.list-btn .btn1 {
  color: #999;
  border: 2rpx solid #F5F5F5;
}
.list-btn .btn2 {
  color: #fff;
  background: #000;
  border: 2rpx solid #000;
}

/* 评价弹窗 */
.note-box {
  padding: 15rpx;
  background: #fff;
  border-radius: 30rpx;
}
.note-box .note-add {
  margin: 30rpx;
  width: 400rpx;
  height: 90rpx;
  font-size: 24rpx;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 45rpx;
  justify-content: center;
}
.note-box .note-add image {
  margin-right: 10rpx;
  width: 40rpx;
  height: 40rpx;
}

/* 空状态 */
.empty-box {
  margin-top: 120rpx;
  width: 100%;
  flex-direction: column;
}
.empty-box image {
  width: 240rpx;
  height: 240rpx;
}
.empty-box .e1 {
  margin-top: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
}
.empty-box .e2 {
  margin-top: 12rpx;
  font-size: 26rpx;
  color: #999;
}

/* 提示框 */
.tips-box {
  width: 100%;
  justify-content: center;
}
.tips-item {
  background: #000;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 700;
}

/* 加载动画 */
.heio {
  justify-content: center;
}

/* 通用 */
.df {
  display: flex;
  align-items: center;
}
.bf8 {
  background: #fff;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
@keyframes fadeIn {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
