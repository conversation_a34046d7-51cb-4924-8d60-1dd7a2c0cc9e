
page {
	background: #f8f8f8;
	padding-bottom: 100rpx;
}
.container {
	width: 100%;
}
.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 99;
	box-sizing: border-box;
}
.bar-box .bar-back {
	padding: 0 30rpx;
	width: 34rpx;
	height: 100%;
}
.bar-box .bar-title {
	max-width: 60%;
	font-size: 32rpx;
	font-weight: 700;
}
.nav-box {
	width: 100%;
	height: 80rpx;
}
.nav-box .nav-item {
	padding: 0 30rpx;
	height: 100%;
	flex-direction: column;
	justify-content: center;
	position: relative;
}
.nav-box .nav-item text {
	font-weight: 700;
	transition: all .3s ease-in-out;
}
.nav-box .nav-line {
	position: absolute;
	bottom: 12rpx;
	width: 18rpx;
	height: 6rpx;
	border-radius: 6rpx;
	background: #000;
	transition: opacity .3s ease-in-out;
}
.code-box {
	position: fixed;
	z-index: 99;
	left: 30rpx;
	bottom: max(env(safe-area-inset-bottom), 30rpx);
	width: calc(100% - 60rpx);
	height: 100rpx;
	color: #fff;
	font-size: 24rpx;
	font-weight: 700;
	background: #000;
	border-radius: 100rpx;
	justify-content: center;
}
.code-box image {
	margin-right: 8rpx;
	width: 34rpx;
	height: 34rpx;
}
.content {
	width: calc(100% - 60rpx);
	padding: 30rpx;
}
.coupon {
	margin-bottom: 30rpx;
	width: 100%;
	border-radius: 8rpx;
	background: #fff;
	position: relative;
	overflow: hidden;
}
.coupon .coupon-bg {
	position: absolute;
	z-index: 1;
	right: -90rpx;
	bottom: -120rpx;
	width: 380rpx;
	height: 380rpx;
}
.coupon .coupon-item {
	z-index: 2;
	width: calc(100% - 80rpx);
	padding: 50rpx 40rpx 20rpx;
	border-bottom: 2rpx dashed #f8f8f8;
	position: relative;
}
.coupon-item .corner-mark {
	position: absolute;
	z-index: 9;
	top: 0;
	left: 0;
	border-radius: 8rpx 0;
	padding: 0rpx 12rpx;
	height: 36rpx;
	line-height: 36rpx;
	text-align: center;
	font-size: 20rpx;
	color: #fa5150;
	background: rgba(250, 81, 80, .125);
}
.coupon-item .t1 {
	width: calc(100% - 200rpx);
	color: #000;
	font-size: 48rpx;
	font-weight: 700;
}
.coupon-item .t2 {
	width: calc(100% - 200rpx);
	margin: 8rpx 0;
	color: #444;
	font-size: 24rpx;
}
.coupon .coupon-btn {
	position: absolute;
	z-index: 9;
	top: calc(50% - 30rpx);
	right: 30rpx;
	width: 140rpx;
	height: 60rpx;
	line-height: 60rpx;
	text-align: center;
	font-size: 20rpx;
	font-weight: 700;
	border-radius: 50rpx;
	color: #fff;
	background: #000;
}
.coupon .validity {
	width: calc(100% - 80rpx);
	padding: 20rpx 40rpx;
	color: #999;
	font-size: 20rpx;
}
.coupon .coupon-err {
	position: absolute;
	z-index: 10;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	font-size: 28rpx;
	font-style: italic;
	font-weight: 700;
	justify-content: center;
	color: #ccc;
	background: rgba(255, 255, 255, .85);
}
.popup-box {
	width: calc(100% - 40rpx);
	padding: 20rpx;
	background: #fff;
	border-radius: 30rpx 30rpx 0 0;
	position: relative;
	overflow: hidden;
}
.popup-box .popup-top {
	width: calc(100% - 20rpx);
	padding: 10rpx;
	justify-content: space-between;
}
.popup-top .popup-title .t1 {
	font-size: 38rpx;
	font-weight: 700;
}
.popup-top .popup-title .t2 {
	color: #999;
	font-size: 20rpx;
	font-weight: 300;
}
.popup-top .popup-close {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: #f8f8f8;
	justify-content: center;
	transform: rotate(45deg);
}
.exchange-input {
	margin: 30rpx 10rpx;
	padding: 0 30rpx;
	width: calc(100% - 80rpx);
	height: 100rpx;
	font-size: 28rpx;
	font-weight: 700;
	text-align: center;
	border: 4rpx solid #f5f5f5;
	border-radius: 30rpx;
}
.exchange-item view {
	margin: 10rpx;
	width: calc(100% - 20rpx);
	color: #999;
	font-size: 20rpx;
}
.popup-box .popup-btn {
	margin: 40rpx 10rpx;
	width: calc(100% - 20rpx);
	height: 100rpx;
	line-height: 100rpx;
	text-align: center;
	font-size: 24rpx;
	font-weight: 700;
	color: #fff;
	background: #000;
	border-radius: 100rpx;
}
.empty-box {
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}
.empty-box image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}
.empty-box .e1 {
	font-size: 28rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}
.empty-box .e2 {
	font-size: 24rpx;
	color: #999;
}
.tips-box {
	padding: 20rpx 30rpx;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 12rpx;
	justify-content: center;
}
.tips-box .tips-item {
	color: #fff;
	font-size: 28rpx;
	font-weight: 700;
}
.df {
	display: flex;
	align-items: center;
}
.ohto {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.bf8 {
	background: #f8f8f8;
}
