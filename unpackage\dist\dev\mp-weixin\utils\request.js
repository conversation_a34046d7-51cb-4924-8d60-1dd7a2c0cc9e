"use strict";
const common_vendor = require("../common/vendor.js");
const config_app = require("../config/app.js");
const libs_login = require("../libs/login.js");
const store_index = require("../store/index.js");
const utils_lang = require("./lang.js");
const {
  HTTP_REQUEST_URL,
  HEADER,
  TOKENNAME,
  TIMEOUT
} = config_app.config;
function baseRequest(url, method, data, {
  noAuth = false,
  noVerify = false
}) {
  let Url = HTTP_REQUEST_URL, header = HEADER;
  if (!noAuth) {
    if (!store_index.store.state.app.token && !libs_login.checkLogin()) {
      libs_login.toLogin();
      return Promise.reject({
        msg: utils_lang.i18n.t(`未登录`)
      });
    }
  }
  if (store_index.store.state.app.token)
    header[TOKENNAME] = "Bearer " + store_index.store.state.app.token;
  return new Promise((reslove, reject) => {
    if (common_vendor.index.getStorageSync("locale")) {
      header["Cb-lang"] = common_vendor.index.getStorageSync("locale");
    }
    common_vendor.index.request({
      url: Url + "/api/" + url,
      method: method || "GET",
      header,
      data: data || {},
      timeout: TIMEOUT,
      success: (res) => {
        if (noVerify)
          reslove(res.data, res);
        else if (res.data.status == 200)
          reslove(res.data, res);
        else if ([110002, 110003, 110004].indexOf(res.data.status) !== -1) {
          libs_login.toLogin();
          reject(res.data);
        } else if (res.data.status == 100103) {
          common_vendor.index.showModal({
            title: utils_lang.i18n.t(`提示`),
            content: res.data.msg,
            showCancel: false,
            confirmText: utils_lang.i18n.t(`我知道了`)
          });
          reject(res.data);
        } else {
          const errorData = {
            status: res.data.status || 400,
            msg: res.data.msg || utils_lang.i18n.t(`系统错误`),
            data: res.data.data || null,
            code: res.data.code || res.data.status || 400
          };
          common_vendor.index.__f__("log", "at utils/request.js:82", "API错误响应:", {
            url,
            status: res.data.status,
            msg: res.data.msg,
            errorData
          });
          reject(errorData);
        }
      },
      fail: (error) => {
        const errorData = {
          status: 0,
          msg: utils_lang.i18n.t(`请求失败`),
          code: "NETWORK_ERROR",
          data: null,
          error
        };
        reject(errorData);
      }
    });
  });
}
const request = {};
["options", "get", "post", "put", "head", "delete", "trace", "connect"].forEach((method) => {
  request[method] = (api, data, opt) => baseRequest(api, method, data, opt || {});
});
exports.request = request;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/request.js.map
