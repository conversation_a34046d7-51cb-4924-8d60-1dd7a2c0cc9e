{"version": 3, "file": "new_chat.js", "sources": ["libs/new_chat.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport $store from \"@/store\";\nimport appConfig from \"@/config/app.js\";\nconst { HTTP_REQUEST_URL } = appConfig;\nimport {\n\tVUE_APP_WS_URL\n} from \"@/utils/index.js\";\nimport {\n\tgetServerType\n} from '@/api/api.js';\nconst Socket = function() {\n\n\t// this.ws.close(this.close.bind(this));\n};\n\n\n// #ifdef H5\nfunction wss(wsSocketUrl) {\n\tlet ishttps = document.location.protocol == 'https:';\n\tif (ishttps) {\n\t\treturn wsSocketUrl.replace('ws:', 'wss:');\n\t} else {\n\t\treturn wsSocketUrl.replace('wss:', 'ws:');\n\t}\n}\n// #endif\n\n\n\nSocket.prototype = {\n\t// close() {\n\t//   clearInterval(this.timer);\n\t//   this.ws.close();\n\t// },\n\tonSocketOpen: function(my) {\n\t\tuni.$emit('socketOpen', my)\n\t},\n\tinit: function() {\n\t\tvar that = this;\n\t\tthis.timer = setInterval(function() {\n\t\t\tthat.send({\n\t\t\t\ttype: \"ping\"\n\t\t\t});\n\t\t}, 10000);\n\t},\n\tsend: function(data) {\n\t\tlet datas = JSON.stringify(data)\n\t\treturn uni.sendSocketMessage({\n\t\t\tdata: datas\n\t\t});\n\t},\n\tonMessage: function(res) {\n\t\tconst {\n\t\t\ttype,\n\t\t\tdata = {}\n\t\t} = JSON.parse(res.data);\n\t\tuni.$emit(type, data)\n\t},\n\n\tonClose: function() {\n\t\tuni.closeSocket()\n\t\tclearInterval(this.timer);\n\t\tuni.$emit(\"socket_close\");\n\t},\n\tonError: function(e) {\n\t\tuni.$emit(\"socket_error\", e);\n\t},\n\tclose: function() {\n\t\tuni.closeSocket();\n\t},\n\tonStart: function(token, form_type) {\n\t\tlet wssUrl = `${VUE_APP_WS_URL}`\n\t\tthis.ws = uni.connectSocket({\n\t\t\turl: wssUrl + '?type=user&token=' + token + '&form_type=' + form_type,\n\t\t\theader: {\n\t\t\t\t'content-type': 'application/json'\n\t\t\t},\n\t\t\tmethod: 'GET',\n\t\t\tsuccess: (res) => {}\n\t\t});\n\t\tthis.ws.onOpen(this.onSocketOpen.bind(this))\n\t\tthis.ws.onError(this.onError.bind(this));\n\t\tthis.ws.onMessage(this.onMessage.bind(this))\n\t\tthis.ws.onClose(this.onClose.bind(this));\n\t}\n};\n\nSocket.prototype.constructor = Socket;\nexport default Socket;\n"], "names": ["uni", "VUE_APP_WS_URL"], "mappings": ";;;;;AAmBK,MAAC,SAAS,WAAW;AAG1B;AAgBA,OAAO,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlB,cAAc,SAAS,IAAI;AAC1BA,wBAAI,MAAM,cAAc,EAAE;AAAA,EAC1B;AAAA,EACD,MAAM,WAAW;AAChB,QAAI,OAAO;AACX,SAAK,QAAQ,YAAY,WAAW;AACnC,WAAK,KAAK;AAAA,QACT,MAAM;AAAA,MACV,CAAI;AAAA,IACD,GAAE,GAAK;AAAA,EACR;AAAA,EACD,MAAM,SAAS,MAAM;AACpB,QAAI,QAAQ,KAAK,UAAU,IAAI;AAC/B,WAAOA,cAAAA,MAAI,kBAAkB;AAAA,MAC5B,MAAM;AAAA,IACT,CAAG;AAAA,EACD;AAAA,EACD,WAAW,SAAS,KAAK;AACxB,UAAM;AAAA,MACL;AAAA,MACA,OAAO,CAAE;AAAA,IACT,IAAG,KAAK,MAAM,IAAI,IAAI;AACvBA,wBAAI,MAAM,MAAM,IAAI;AAAA,EACpB;AAAA,EAED,SAAS,WAAW;AACnBA,kBAAAA,MAAI,YAAa;AACjB,kBAAc,KAAK,KAAK;AACxBA,wBAAI,MAAM,cAAc;AAAA,EACxB;AAAA,EACD,SAAS,SAAS,GAAG;AACpBA,kBAAAA,MAAI,MAAM,gBAAgB,CAAC;AAAA,EAC3B;AAAA,EACD,OAAO,WAAW;AACjBA,kBAAG,MAAC,YAAW;AAAA,EACf;AAAA,EACD,SAAS,SAAS,OAAO,WAAW;AACnC,QAAI,SAAS,GAAGC,YAAAA,cAAc;AAC9B,SAAK,KAAKD,cAAG,MAAC,cAAc;AAAA,MAC3B,KAAK,SAAS,sBAAsB,QAAQ,gBAAgB;AAAA,MAC5D,QAAQ;AAAA,QACP,gBAAgB;AAAA,MAChB;AAAA,MACD,QAAQ;AAAA,MACR,SAAS,CAAC,QAAQ;AAAA,MAAE;AAAA,IACvB,CAAG;AACD,SAAK,GAAG,OAAO,KAAK,aAAa,KAAK,IAAI,CAAC;AAC3C,SAAK,GAAG,QAAQ,KAAK,QAAQ,KAAK,IAAI,CAAC;AACvC,SAAK,GAAG,UAAU,KAAK,UAAU,KAAK,IAAI,CAAC;AAC3C,SAAK,GAAG,QAAQ,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,EACvC;AACF;AAEA,OAAO,UAAU,cAAc;;"}