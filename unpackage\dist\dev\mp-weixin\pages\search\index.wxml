<view class="container"><view class="nav-bar" style="{{'padding-top:' + i}}"><view class="back-box df" style="{{'height:' + c + ';' + ('width:' + '100%')}}"><view class="back-item df" bindtap="{{b}}"><image src="{{a}}" style="width:34rpx;height:34rpx"></image></view></view><view class="nav-search df"><input bindconfirm="{{d}}" focus="true" confirm-type="search" placeholder="输入关键词搜索" placeholder-class="ph" value="{{e}}" bindinput="{{f}}"/><view class="btn" bindtap="{{g}}">搜索</view></view><view class="bar-box df"><view wx:for="{{h}}" wx:for-item="item" wx:key="e" class="bar-item df" bindtap="{{item.f}}" data-idx="{{item.g}}"><text style="{{'color:' + item.b + ';' + ('font-size:' + item.c)}}">{{item.a}}</text><view style="{{'opacity:' + item.d}}" class="bar-line"></view></view></view></view><view class="{{['content-box', y]}}" style="{{'margin-top:' + z}}"><empty-page wx:if="{{j}}" u-i="2d4a7a96-0" bind:__l="__l" u-p="{{k}}"/><block wx:else><view wx:if="{{l}}" class="{{[p]}}"><waterfall wx:if="{{m}}" u-i="2d4a7a96-1" bind:__l="__l" u-p="{{n}}"></waterfall><block wx:else><card-gg wx:for="{{o}}" wx:for-item="item" wx:key="a" bindlikeback="{{item.b}}" bindupdate="{{item.c}}" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"></card-gg></block></view><block wx:else><view wx:for="{{q}}" wx:for-item="item" wx:key="U" class="{{[w]}}" data-id="{{item.V}}" bindtap="{{item.W}}"><block wx:if="{{r}}"><view class="goods-img"><view class="goods-img-item"><lazy-image wx:if="{{item.b}}" u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}"></lazy-image></view></view><view class="goods-name ohto2">{{item.c}}</view><view class="goods-price"><money wx:if="{{item.e}}" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"></money><view class="price-h" style="text-decoration:line-through">¥{{item.f}}</view><view class="price-h">{{item.g}}</view></view><view class="goods-tag df"><view wx:for="{{item.h}}" wx:for-item="tag" wx:key="b" class="tag-item">{{tag.a}}</view></view></block><view wx:if="{{s}}" class="circle-box df"><view class="circle-avatar"><lazy-image wx:if="{{item.j}}" u-i="{{item.i}}" bind:__l="__l" u-p="{{item.j}}"></lazy-image><view wx:if="{{item.k}}" class="tag" style="background:#FA5150"></view><view wx:elif="{{item.l}}" class="tag" style="background:#4CD964"></view></view><view class="circle-item"><view class="name ohto2">{{item.m}}</view><view class="intro ohto2">{{item.n}}</view><view wx:if="{{item.o}}" class="cu-img-group"><view wx:for="{{item.p}}" wx:for-item="img" wx:key="b" class="cu-img"><image src="{{img.a}}" mode="aspectFill"></image></view><view class="cu-txt">{{item.q}}人加入 · {{item.r}}篇笔记 </view></view></view></view><view wx:if="{{t}}" class="user-box df"><view class="user-avatar"><lazy-image wx:if="{{item.t}}" u-i="{{item.s}}" bind:__l="__l" u-p="{{item.t}}"></lazy-image></view><view class="user-item"><view class="name ohto">{{item.v}}</view><view class="unm">笔记 · {{item.w}}｜粉丝 · {{item.x}}</view><view class="user-tag df" style="width:100%"><view wx:if="{{item.y}}" class="tag-item df"><image src="{{item.z}}"></image><text wx:if="{{item.A}}" style="margin-left:8rpx">{{item.B}}</text></view><view class="tag-item df">IP属地：{{item.C}}</view></view></view></view><view wx:if="{{v}}" class="activity-item df"><view class="activity-img"><lazy-image wx:if="{{item.E}}" u-i="{{item.D}}" bind:__l="__l" u-p="{{item.E}}"></lazy-image><view class="activity-state df">{{item.F}}</view></view><view class="activity-data"><view class="title ohto" data-id="{{item.H}}">{{item.G}}</view><view class="txt df" data-id="{{item.K}}"><image src="{{item.I}}"></image><view class="ohto">{{item.J}}</view></view><view class="txt df" data-id="{{item.N}}"><image src="{{item.L}}"></image><view class="ohto">{{item.M}}</view></view><view wx:if="{{item.O}}" class="cu-img-group"><view wx:for="{{item.P}}" wx:for-item="img" wx:key="b" class="cu-img"><image src="{{img.a}}" mode="aspectFill"></image></view><view class="cu-tit">{{item.Q}}人已参加</view></view><view wx:else class="cu-txt-group">{{item.R}}人想参加</view><view class="activity-btn df"><button class="btn-item df w100"><text>{{item.S}}</text><image class="effect icon" src="{{item.T}}"></image></button></view></view></view></view></block></block><view class="df" style="width:100%;justify-content:center"><uni-load-more wx:if="{{x}}" u-i="2d4a7a96-8" bind:__l="__l" u-p="{{x}}"></uni-load-more></view></view><uni-popup wx:if="{{C}}" class="r" u-s="{{['d']}}" u-r="tipsPopup" u-i="2d4a7a96-9" bind:__l="__l" u-p="{{C}}"><view class="tips-box df"><view class="tips-item">{{A}}</view></view></uni-popup></view>