"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../lazyImage/lazyImage.js";
const VoteComponent = () => "../vote-component/vote-component.js";
const _sfc_main = {
  name: "card-wd",
  components: {
    lazyImage,
    VoteComponent
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    idx: {
      type: Number,
      default: 0
    },
    bar: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      currentUid: common_vendor.index.getStorageSync("uid") || 0
    };
  },
  computed: {
    // 获取日期数字
    getDay() {
      if (!this.item.create_time)
        return "N/A";
      try {
        const date = new Date(typeof this.item.create_time === "string" ? this.item.create_time.replace(/-/g, "/") : this.item.create_time * 1e3);
        return date.getDate();
      } catch (e) {
        return "N/A";
      }
    },
    // 获取状态文本
    getStatusText() {
      const statusMap = {
        0: "待审核",
        1: "已通过",
        2: "未通过",
        3: "草稿"
      };
      return statusMap[this.item.status] || "";
    },
    // 格式化日期时间
    formatTime() {
      if (!this.item.create_time)
        return "";
      try {
        let timestamp;
        if (typeof this.item.create_time === "string") {
          timestamp = new Date(this.item.create_time.replace(/-/g, "/")).getTime();
        } else if (typeof this.item.create_time === "number") {
          timestamp = this.item.create_time * 1e3;
        } else {
          return "";
        }
        const now = (/* @__PURE__ */ new Date()).getTime();
        const diff = now - timestamp;
        if (diff < 60 * 1e3) {
          return "刚刚";
        } else if (diff < 60 * 60 * 1e3) {
          return Math.floor(diff / (60 * 1e3)) + "分钟前";
        } else if (diff < 24 * 60 * 60 * 1e3) {
          return Math.floor(diff / (60 * 60 * 1e3)) + "小时前";
        } else if (diff < 30 * 24 * 60 * 60 * 1e3) {
          return Math.floor(diff / (24 * 60 * 60 * 1e3)) + "天前";
        } else {
          const date = new Date(timestamp);
          return date.getMonth() + 1 + "月" + date.getDate() + "日";
        }
      } catch (e) {
        return "";
      }
    },
    // 格式化年月日
    formatDate() {
      if (!this.item.create_time)
        return "";
      try {
        let timestamp;
        if (typeof this.item.create_time === "string") {
          timestamp = new Date(this.item.create_time.replace(/-/g, "/")).getTime();
        } else if (typeof this.item.create_time === "number") {
          timestamp = this.item.create_time * 1e3;
        } else {
          return "";
        }
        const date = new Date(timestamp);
        return date.getFullYear() + "年" + (date.getMonth() + 1) + "月" + date.getDate() + "日";
      } catch (e) {
        return "";
      }
    },
    // 获取图片数组
    getImages() {
      if (!this.item.images)
        return [];
      if (typeof this.item.images === "string") {
        try {
          return JSON.parse(this.item.images);
        } catch (e) {
          return [];
        }
      }
      return Array.isArray(this.item.images) ? this.item.images : [];
    },
    // 获取图片数量
    getImagesCount() {
      return this.getImages.length;
    },
    // 检查是否有位置
    hasLocation() {
      return this.item.location_name && this.item.location_name.length > 0;
    },
    // 检查是否有话题
    hasTopic() {
      return this.item.topic_info && Array.isArray(this.item.topic_info) && this.item.topic_info.length > 0;
    },
    // 检查是否有商品
    hasProduct() {
      return this.item.product_info && Object.keys(this.item.product_info).length > 0;
    },
    // 获取图片宽高比（用于单图显示）
    getImageRatio() {
      if (this.item.image_width && this.item.image_height) {
        return this.item.image_width / this.item.image_height;
      }
      return 0.8;
    },
    // 单图容器类名
    getSingleImageClass() {
      return ["file-h", this.getImageRatio > 1.2 ? "file-w" : ""];
    }
  },
  methods: {
    toPaths(e) {
      let url = "";
      if (this.bar == 2) {
        url = "note/add?id=" + this.item.id;
      } else {
        let type = e && e.currentTarget ? e.currentTarget.dataset.type : 0;
        if (this.item.type == 2 || this.item.type == 3 || this.item.type == 4) {
          url = "note/video?id=" + this.item.id;
        } else {
          url = "note/details?id=" + this.item.id;
        }
        if (type == 1) {
          url = "user/details?id=" + this.item.uid;
        } else if (type == 2) {
          const topicId = e.currentTarget.dataset.id || "";
          url = "topic/details?id=" + topicId;
        } else if (type == 3) {
          const goodsId = e.currentTarget.dataset.id || "";
          url = "goods/details?id=" + goodsId;
        }
      }
      common_vendor.index.navigateTo({
        url: "/pages/" + url
      });
    },
    delNote() {
      this.$emit("delback", {
        idx: this.idx
      });
    },
    opLocationClick() {
      if (!this.item.latitude || !this.item.longitude)
        return;
      common_vendor.index.openLocation({
        latitude: parseFloat(this.item.latitude),
        longitude: parseFloat(this.item.longitude),
        name: this.item.location_name
      });
    },
    // 处理投票成功事件
    handleVoteSuccess(data) {
      this.item.vote_info = data.voteInfo;
      this.$emit("update", {
        vote_info: data.voteInfo,
        idx: this.idx
      });
    }
  }
};
if (!Array) {
  const _component_lazyImage = common_vendor.resolveComponent("lazyImage");
  const _component_VoteComponent = common_vendor.resolveComponent("VoteComponent");
  (_component_lazyImage + _component_VoteComponent)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.bar == 1
  }, $props.bar == 1 ? common_vendor.e({
    b: $props.item.avatar
  }, $props.item.avatar ? {
    c: common_vendor.p({
      src: $props.item.avatar,
      br: "50%"
    })
  } : {}, {
    d: common_vendor.o((...args) => $options.toPaths && $options.toPaths(...args))
  }) : {
    e: common_vendor.t($options.getDay)
  }, {
    f: $props.bar == 1
  }, $props.bar == 1 ? {
    g: common_vendor.t($props.item.nickname || "用户"),
    h: common_vendor.t($options.formatTime),
    i: common_vendor.t($props.item.province || "未知地区")
  } : common_vendor.e({
    j: $options.getStatusText
  }, $options.getStatusText ? {
    k: common_vendor.t($options.getStatusText)
  } : {}, {
    l: common_vendor.t($options.formatDate),
    m: common_vendor.t($props.item.province || "未知地区")
  }), {
    n: common_vendor.t($props.item.content || "无内容"),
    o: common_vendor.n($props.item.type == 1 ? "wlc8" : ""),
    p: $props.item.type > 0
  }, $props.item.type > 0 ? common_vendor.e({
    q: $props.item.type == 2 && $options.getImages.length == 1
  }, $props.item.type == 2 && $options.getImages.length == 1 ? {
    r: common_vendor.p({
      src: $options.getImages[0]
    }),
    s: common_vendor.n($options.getSingleImageClass)
  } : {}, {
    t: $props.item.type == 2 && $options.getImages.length > 1
  }, $props.item.type == 2 && $options.getImages.length > 1 ? common_vendor.e({
    v: common_vendor.f($options.getImages.slice(0, 3), (img, index, i0) => {
      return {
        a: "8c51542a-2-" + i0,
        b: common_vendor.p({
          src: img
        }),
        c: index
      };
    }),
    w: $options.getImagesCount > 3
  }, $options.getImagesCount > 3 ? {
    x: common_assets._imports_0$22,
    y: common_vendor.t($options.getImagesCount)
  } : {}) : {}, {
    z: $props.item.type == 3 && $props.item.video
  }, $props.item.type == 3 && $props.item.video ? {
    A: common_vendor.p({
      src: $props.item.video_cover || $options.getImages[0] || "/static/img/video_placeholder.png"
    }),
    B: common_assets._imports_1$22
  } : {}, {
    C: $props.item.type == 4 && $props.item.audio
  }, $props.item.type == 4 && $props.item.audio ? {
    D: $props.item.audio_cover || "/static/img/audio_cover.png",
    E: common_vendor.p({
      src: $props.item.audio_cover || "/static/img/audio_cover.png"
    }),
    F: common_assets._imports_2$5,
    G: common_vendor.t($props.item.audio_name || "音频"),
    H: common_vendor.t($props.item.audio_intro || "点击播放音频")
  } : {}) : {}, {
    I: $props.item.vote_info
  }, $props.item.vote_info ? {
    J: common_vendor.o($options.handleVoteSuccess),
    K: common_vendor.p({
      voteInfo: $props.item.vote_info
    })
  } : {}, {
    L: $options.hasLocation || $props.item.topic_info || $props.item.product_info
  }, $options.hasLocation || $props.item.topic_info || $props.item.product_info ? common_vendor.e({
    M: $options.hasLocation
  }, $options.hasLocation ? {
    N: common_assets._imports_3$6,
    O: common_vendor.t($props.item.location_name),
    P: common_vendor.o((...args) => $options.opLocationClick && $options.opLocationClick(...args))
  } : {}, {
    Q: $options.hasTopic
  }, $options.hasTopic ? {
    R: common_vendor.f($props.item.topic_info, (topic, index, i0) => {
      return {
        a: "8c51542a-6-" + i0,
        b: common_vendor.p({
          src: topic.image || "/static/img/topic.png",
          br: "50%"
        }),
        c: common_vendor.t(topic.title),
        d: "topic-" + index,
        e: common_vendor.o((...args) => $options.toPaths && $options.toPaths(...args), "topic-" + index)
      };
    })
  } : {}, {
    S: $options.hasProduct
  }, $options.hasProduct ? {
    T: common_vendor.p({
      src: $props.item.product_info.image
    }),
    U: common_vendor.t($props.item.product_info.store_name),
    V: $props.item.product_info.id,
    W: common_vendor.o((...args) => $options.toPaths && $options.toPaths(...args))
  } : {}) : {}, {
    X: common_vendor.t($props.item.comments || 0),
    Y: common_vendor.t($props.item.likes || 0),
    Z: common_vendor.t($props.item.views || 0),
    aa: $props.bar == 2 || $props.item.uid == $data.currentUid
  }, $props.bar == 2 || $props.item.uid == $data.currentUid ? {
    ab: common_vendor.o((...args) => $options.delNote && $options.delNote(...args))
  } : {}, {
    ac: common_vendor.o((...args) => $options.toPaths && $options.toPaths(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/card-wd/card-wd.js.map
