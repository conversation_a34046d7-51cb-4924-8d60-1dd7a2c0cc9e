{"version": 3, "file": "video.js", "sources": ["pages/note/video.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbm90ZS92aWRlby52dWU"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 导航栏 -->\n    <view class=\"nav-box\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\n      <view class=\"nav-item df\" :style=\"{'width': '100%', 'height': titleBarHeight + 'px', 'position': 'relative'}\">\n        <view class=\"nav-back df\" @tap=\"navBack\">\n          <image class=\"xwb\" src=\"/static/img/z.png\" style=\"width:34rpx;height:34rpx\"></image>\n        </view>\n        <!-- 图片计数器 -->\n        <view v-if=\"isImageNote && noteInfo.images && noteInfo.images.length > 1\" class=\"nav-counter\">\n          {{currentImageIndex + 1}}/{{noteInfo.images.length}}\n        </view>\n      </view>\n    </view>\n    \n    <!-- 视频播放区 - type=3 视频类型 -->\n    <view v-if=\"isVideoNote && noteInfo.video\" class=\"video-box df\" :style=\"{'height': isCommentPopup ? '30vh' : 'calc(100vh - ' + footerHeight + 'px)'}\">\n      <video \n        id=\"myVideo\"\n        :src=\"noteInfo.video\" \n        :poster=\"noteInfo.video_cover\"\n        object-fit=\"contain\"\n        autoplay\n        play-btn-position=\"center\" \n        :show-fullscreen-btn=\"false\">\n      </video>\n    </view>\n    \n    <!-- 图片显示区 - type=2 -->\n    <view v-if=\"isImageNote && noteInfo.images && noteInfo.images.length > 0\" \n          :class=\"[noteInfo.images.length == 1 ? 'image-box df' : 'multi-image-box']\" \n          :style=\"{'height': isCommentPopup ? '30vh' : 'calc(100vh - ' + footerHeight + 'px)'}\">\n      <!-- 单图显示 -->\n      <template v-if=\"noteInfo.images.length == 1\">\n      <image \n        :src=\"noteInfo.images[0]\" \n        mode=\"aspectFit\" \n        class=\"full-image\"\n        @tap=\"previewImage(noteInfo.images[0])\">\n      </image>\n      </template>\n    \n      <!-- 多图显示 -->\n      <swiper v-else\n        class=\"image-swiper\" \n        :indicator-dots=\"false\" \n        @change=\"onSwiperChange\" \n        circular>\n        <swiper-item v-for=\"(img, index) in noteInfo.images\" :key=\"index\">\n          <image \n            :src=\"img\" \n            mode=\"aspectFit\" \n            class=\"swiper-image\"\n            @tap=\"previewImage(img, index)\">\n          </image>\n        </swiper-item>\n      </swiper>\n    </view>\n    \n    <!-- 音频播放区 - type=4 -->\n    <view v-if=\"isAudioNote\" class=\"audio-player-container\" \n          :style=\"{'height': isCommentPopup ? '30vh' : 'calc(100vh - ' + footerHeight + 'px)'}\">\n      \n      <!-- 背景模糊层 -->\n      <view class=\"audio-bg-blur\">\n        <lazy-image :src=\"noteInfo.audio_cover || '/static/img/audio_icon.png'\" mode=\"aspectFill\"></lazy-image>\n      </view>\n      <view class=\"audio-bg-overlay\"></view>\n      \n      <!-- 音频播放内容 -->\n      <view class=\"audio-player-content\">\n        <!-- 外圈碟机效果 -->\n        <view class=\"vinyl-outer\" :class=\"{'rotating': bgAudioStatus}\">\n          <view class=\"vinyl-groove\"></view>\n          <view class=\"vinyl-groove-2\"></view>\n          <view class=\"vinyl-groove-3\"></view>\n          \n          <!-- 内圈唱片和封面 -->\n          <view class=\"vinyl-inner\" :class=\"{'rotating-slow': bgAudioStatus}\">\n            <view class=\"vinyl-center\">\n              <view class=\"album-cover\">\n                <lazy-image :src=\"noteInfo.audio_cover || '/static/img/audio_icon.png'\" mode=\"aspectFill\"></lazy-image>\n              </view>\n              <view class=\"vinyl-dot\"></view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 音频信息 -->\n        <view class=\"audio-info\">\n          <view v-if=\"noteInfo.audio_title\" class=\"audio-title\">{{noteInfo.audio_title}}</view>\n          <view v-if=\"noteInfo.user_info\" class=\"audio-artist\">{{noteInfo.user_info.nickname || '未知艺术家'}}</view>\n        </view>\n        \n        <!-- 播放控制 -->\n        <view class=\"audio-controls\">\n          <view class=\"audio-play-btn\" @tap=\"toggleAudioPlay\">\n            <image v-if=\"bgAudioStatus\" src=\"/static/img/pause.png\" mode=\"aspectFit\"></image>\n            <image v-else src=\"/static/img/play.png\" mode=\"aspectFit\"></image>\n          </view>\n          \n          <!-- 进度条 -->\n          <view class=\"audio-progress-container\">\n            <view class=\"audio-time-start\">{{formatTime(currentTime)}}</view>\n            <view class=\"audio-progress\">\n              <slider :value=\"audioProgress\" @change=\"onAudioProgressChange\" \n                      backgroundColor=\"rgba(255,255,255,0.3)\" \n                      activeColor=\"rgba(255,255,255,0.9)\" \n                      block-color=\"rgba(255,255,255,1)\"\n                      block-size=\"24\"></slider>\n            </view>\n            <view class=\"audio-time-end\">{{formatTime(duration)}}</view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view class=\"content-box\" :style=\"{'bottom': footerHeight + 'px'}\">\n      <view class=\"nav-user df\">\n        <view class=\"user-info df\" :data-url=\"'user/details?id='+noteInfo.uid\" @tap=\"navigateToFun\">\n          <image class=\"nav-user-avatar\" :src=\"noteInfo.user_info && noteInfo.user_info.avatar ? noteInfo.user_info.avatar : '/static/img/avatar_default.png'\" mode=\"aspectFill\"></image>\n          <view class=\"user-text\">\n            <view class=\"nav-user-name ohto\">{{noteInfo.user_info && noteInfo.user_info.nickname ? noteInfo.user_info.nickname : '用户'}}</view>\n            <view class=\"content-tips\">{{noteInfo.create_time}} · {{noteInfo.location_name || 'IP属地'}} · 浏览 {{noteInfo.views || 0}}</view>\n          </view>\n        </view>\n        <!-- 关注按钮 -->\n        <view v-if=\"noteInfo.uid && noteInfo.uid != userId\" \n              :class=\"['follow-btn', isFollowing ? 'active' : '']\" \n              @tap.stop=\"followUser\">\n          {{isFollowing ? '已关注' : '＋关注'}}\n        </view>\n      </view>\n            \n      <view class=\"content-item\">\n        <text :class=\"['content-text', isExpanded ? '' : 'ohto2']\">{{noteInfo.content}}</text>\n        <view v-if=\"isContentOverflow && !isExpanded\" class=\"expand-btn\" @tap=\"toggleContent\">展开</view>\n        <view v-if=\"isExpanded\" class=\"collapse-btn\" @tap=\"toggleContent\">收起</view>\n      </view>\n      \n      <!-- 标签区域 -->\n      <view v-if=\"noteInfo.topic_info && noteInfo.topic_info.length > 0 || noteInfo.goods_info || noteInfo.location_name || hasCircle()\" class=\"content-tag\">\n        <!-- 位置信息 -->\n        <view v-if=\"noteInfo.location_name\" class=\"tag-item df\" @tap.stop=\"openLocationClick\">\n          <image class=\"icon\" style=\"width:32rpx;height:32rpx\" src=\"/static/img/wz.png\"></image>\n          <text style=\"padding:0 8rpx\">{{noteInfo.location_name}}</text>\n        </view>\n       <!-- 圈子标签 -->\n                <view \n          v-if=\"hasCircle()\" \n          class=\"tag-item df\" \n          style=\"border-radius:40rpx\" \n          :data-url=\"'note/circle?id='+getCircleId()\" \n          @tap=\"navigateToFun\">\n          <image class=\"icon\" style=\"border-radius:50%\" :src=\"getCircleAvatar()\" mode=\"aspectFill\"></image>\n          <text>{{getCircleName()}}</text>\n        </view>\n        <!-- 话题标签 -->\n        <view \n          v-for=\"(topic, topicIndex) in noteInfo.topic_info\" \n          :key=\"'topic-'+topicIndex\"\n          class=\"tag-item df\" \n          style=\"border-radius:40rpx\" \n          :data-url=\"'topic/details?id='+topic.id\"\n          @tap=\"navigateToFun\">\n          <image class=\"icon\" style=\"border-radius:50%\" :src=\"topic.icon || '/static/img/topic_icon.png'\" mode=\"aspectFill\"></image>\n          <text>{{topic.title}}</text>\n        </view>\n        \n        <!-- 商品标签 -->\n        <view \n          v-if=\"noteInfo.goods_info\" \n          class=\"tag-item df\" \n          :data-url=\"'goods/details?id='+noteInfo.product_id\" \n          @tap=\"navigateToFun\">\n          <image class=\"icon\" :src=\"noteInfo.goods_info.image\" mode=\"aspectFill\"></image>\n          <text>{{noteInfo.goods_info.store_name}}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"footer-box bUp\">\n      <view class=\"footer-item df\">\n        <!-- 完善信息提示 -->\n        <view v-if=\"!isUser\" class=\"footer-means df\" data-url=\"center/means\" @tap=\"navigateToFun\">\n          <text>完善账号资料后即可评论</text>\n        </view>\n        <!-- 评论框 -->\n        <view v-else class=\"footer-comment ohto pl-str\" data-type=\"0\" data-idx=\"-1\" data-i=\"-1\" @tap=\"openComment\">\n          {{comtext ? comtext : comtips}}\n        </view>\n        \n        <!-- 操作按钮组 -->\n        <view class=\"df\">\n          <view class=\"footer-icon\" @tap=\"commentPopupClick(true)\">\n            <image src=\"/static/img/pl.png\"></image>\n            <text>{{noteInfo.comments ? noteInfo.comments : ''}} 评论</text>\n          </view>\n          \n          <view class=\"footer-icon\" @tap=\"likeDynamic\">\n            <image v-if=\"noteInfo.is_like == 1\" class=\"hi\" src=\"/static/img/dz1.png\"></image>\n            <image v-else class=\"hi\" src=\"/static/img/dz.png\"></image>\n            <text v-if=\"noteInfo.likes < 10000\">{{noteInfo.likes ? noteInfo.likes : ''}} 赞</text>\n            <text v-else>{{formatCount(noteInfo.likes)}} 赞</text>\n          </view>\n          \n          <view class=\"footer-icon\" @tap=\"openShare\">\n            <image src=\"/static/img/fx.png\"></image>\n          </view>\n          \n\n        </view>\n      </view>\n    </view>\n    \n    <!-- 评论蒙层 -->\n    <view v-if=\"isComment\" class=\"popup-comment-mask\" @tap=\"closeComment\"></view>\n    \n    <!-- 评论输入框 -->\n    <comment-input \n      :show=\"isComment\" \n      :placeholder=\"comtips\"\n      :focus=\"isFocus\"\n      ref=\"commentInput\"\n      @blur=\"closeComment\"\n      @send=\"handleCommentSubmit\"\n    ></comment-input>\n    \n\n    \n    <!-- 分享组件 -->\n    <SharePanel \n      :show=\"isShareVisible\" \n      :noteInfo=\"noteInfo\"\n      :userId=\"userId\"\n      @close=\"closeShare\"\n      @edit=\"handleEdit\"\n      @delete=\"handleDelete\"\n      @report=\"handleReport\"\n      @dislike=\"handleDislike\"\n    ></SharePanel>\n    \n    <!-- 提示弹窗 -->\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\n      <view class=\"tips-box df\">\n        <view class=\"tips-item\">{{tipsTitle}}</view>\n      </view>\n    </uni-popup>\n    \n    <!-- 表情预览弹窗 -->\n    <view v-if=\"previewEmojiData\" class=\"emoji-preview-popup\" @tap=\"previewEmojiData = null\">\n      <image \n        class=\"emoji-preview-image\" \n        :src=\"previewEmojiData.url\" \n        mode=\"aspectFit\"\n      ></image>\n    </view>\n    \n    <!-- 评论弹窗 -->\n    <uni-popup ref=\"commentPopup\" type=\"bottom\" @maskClick=\"commentPopupClick(false)\" :safe-area=\"false\">\n      <view class=\"comment-box\">\n        <view class=\"comment-top df\">\n          <view class=\"top-title\">{{noteInfo.comments > 0 ? '评论 ' + noteInfo.comments : '暂无评论'}}</view>\n          \n          <!-- 评论排序切换 -->\n          <view class=\"top-btn df\">\n            <view class=\"btn-active\" :style=\"{'left': cType == 0 ? '6rpx' : '74rpx'}\"></view>\n            <view class=\"btn-item\" :style=\"{'color': cType == 0 ? '#000' : '#999'}\" @tap=\"commentClick(0)\">默认</view>\n            <view class=\"btn-item\" :style=\"{'color': cType == 1 ? '#000' : '#999'}\" @tap=\"commentClick(1)\">最新</view>\n          </view>\n          \n          <!-- 关闭按钮 -->\n          <view class=\"top-close df\" @tap=\"commentPopupClick(false)\">\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:18rpx;height:18rpx\"></image>\n          </view>\n        </view>\n      \n        <!-- 评论列表 -->\n        <scroll-view class=\"comment-scroll\" scroll-y @scrolltolower=\"commentReachBottom\">\n          <!-- 内容详情 -->\n          <view v-if=\"isCommentContent\" class=\"comment-item df\">\n            <view class=\"comment-avatar\" :data-url=\"'user/details?id='+noteInfo.uid\" @tap=\"navigateToFun\">\n              <lazy-image :src=\"noteInfo.user_info && noteInfo.user_info.avatar ? noteInfo.user_info.avatar : '/static/img/avatar_default.png'\"></lazy-image>\n            </view>\n            <view class=\"comment-info\">\n              <view class=\"comment-info-top df\" :data-url=\"'user/details?id='+noteInfo.uid\" @tap=\"navigateToFun\">\n                <view class=\"user-info-left\">\n                  <view class=\"zz\">作者</view> \n                  {{noteInfo.user_info && noteInfo.user_info.nickname ? noteInfo.user_info.nickname : '用户'}}\n                </view>\n              </view>\n              <view class=\"comment-info-content\">\n                <text user-select=\"true\">{{noteInfo.content}}</text>\n              </view>\n              <view class=\"comment-info-bottom df\">\n                <text>{{noteInfo.create_time}} · {{noteInfo.location_name || 'IP属地'}} · 浏览 {{noteInfo.views || 0}}</text>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 空态 -->\n          <view v-if=\"isEmpty\" class=\"empty-box df\">\n            <image src=\"/static/img/empty.png\"></image>\n            <view class=\"e1\">期待你的评论</view>\n            <view class=\"e2\">发条评论表达你的想法吧</view>\n          </view>\n          \n          <!-- 评论列表 -->\n          <block v-else>\n            <view v-for=\"(item, index) in commentList\" :key=\"index\" class=\"comment-item df\">\n              <!-- 评论头像 -->\n              <view class=\"comment-avatar\" :data-url=\"'user/details?id='+item.uid\" @tap=\"navigateToFun\">\n                <lazy-image :src=\"item.user_info && item.user_info.avatar ? item.user_info.avatar : '/static/img/avatar_default.png'\"></lazy-image>\n              </view>\n              \n              <!-- 评论内容 -->\n              <view class=\"comment-info\">\n                <view class=\"comment-info-top df\" :data-url=\"'user/details?id='+item.uid\" @tap=\"navigateToFun\">\n                  <view class=\"user-info-left\">\n                    <view v-if=\"noteInfo.uid == item.uid\" class=\"zz\">作者</view>\n                    <view v-else-if=\"userId == item.uid\" class=\"wo\">我</view> \n                    {{item.user_info && item.user_info.nickname ? item.user_info.nickname : '用户'}}\n                  </view>\n                  <!-- 点赞图标按钮 -->\n                  <view v-if=\"!item.delete_time\" class=\"like-icon\" @tap.stop=\"toggleCommentLike(item.id, item.is_like)\">\n                    <image :src=\"item.is_like == 1 ? '/static/img/dz1.png' : '/static/img/dz.png'\" mode=\"aspectFill\"></image>\n                    <text v-if=\"item.like_count > 0 || item.likes > 0\">{{item.like_count || item.likes}}</text>\n                  </view>\n                </view>\n                \n                <view :class=\"['comment-info-content', (item.status != 5 || item.delete_time) && 'db']\" @tap=\"openComment\" \n                      data-type=\"1\" :data-uid=\"item.uid\" :data-cid=\"item.id\" \n                      :data-name=\"item.user_info && item.user_info.nickname ? item.user_info.nickname : '用户'\" :data-idx=\"index\" data-i=\"-1\">\n                  <rich-text \n                    v-if=\"!item.delete_time\" \n                    :nodes=\"parseEmojiContentForRichText(item.content)\" \n                    user-select=\"true\"\n                    @itemclick=\"onEmojiClick\"\n                    class=\"comment-rich-text\"\n                  ></rich-text>\n                  <text v-else class=\"deleted-comment\">(该评论已被删除)</text>\n                  <!-- 显示评论图片 -->\n                  <image \n                    v-if=\"item.image && !item.delete_time\" \n                    class=\"comment-image\" \n                    mode=\"widthFix\" \n                    :src=\"item.image\" \n                    @tap.stop=\"previewCommentImage(item.image)\"\n                    lazy-load\n                  ></image>\n                </view>\n                \n                <view class=\"comment-info-bottom df\">\n                  <text>{{item.create_time}} {{item.province || ''}}</text>\n                  <!-- 回复按钮 -->\n                  <view v-if=\"!item.delete_time\" @tap=\"openComment\" data-type=\"1\" :data-uid=\"item.uid\" \n                        :data-cid=\"item.id\" :data-name=\"item.user_info && item.user_info.nickname ? item.user_info.nickname : '用户'\" :data-idx=\"index\" data-i=\"-1\">\n                    回复\n                  </view>\n                  <view v-if=\"userId == item.uid && item.status == 5 && !item.delete_time\" \n                        @tap=\"delComment\" :data-id=\"item.id\" :data-idx=\"index\" data-i=\"-1\">\n                    删除\n                  </view>\n                </view>\n                \n                <!-- 回复列表 - 直接嵌套在评论内部 -->\n                <template v-if=\"item.replies && item.replies.length > 0\">\n                  <view v-for=\"(v, i) in sortRepliesByTime(item.replies)\" :key=\"i\" class=\"comment-item df\">\n                    <view class=\"comment-avatar-z\" :data-url=\"'user/details?id='+v.uid\" @tap=\"navigateToFun\">\n                      <lazy-image :src=\"v.user_info && v.user_info.avatar ? v.user_info.avatar : '/static/img/avatar_default.png'\"></lazy-image>\n                    </view>\n                    \n                    <view class=\"comment-info\" style=\"width:calc(100% - 68rpx)\">\n                      <view class=\"comment-info-top-z df\">\n                        <view class=\"user-info-left\">\n                          <view v-if=\"noteInfo.uid == v.uid\" class=\"zz\">作者</view>\n                          <view v-else-if=\"userId == v.uid\" class=\"wo\">我</view>\n                          <view class=\"nn ohto\" :data-url=\"'user/details?id='+v.uid\" @tap=\"navigateToFun\">\n                            {{v.user_info && v.user_info.nickname ? v.user_info.nickname : '用户'}}\n                          </view>\n                          <!-- 显示回复关系 -->\n                          <template v-if=\"v.reply_uid && v.reply_uid !== item.uid\">\n                            <text> ▶ </text>\n                            <view class=\"nn ohto\" :data-url=\"'user/details?id='+v.reply_uid\" @tap=\"navigateToFun\">\n                              {{v.reply_nickname || '用户'}}\n                            </view>\n                          </template>\n                          <text>: </text>\n                        </view>\n                        <!-- 点赞图标按钮 -->\n                        <view v-if=\"!v.delete_time\" class=\"like-icon\" @tap.stop=\"toggleCommentLike(v.id, v.is_like)\">\n                          <image :src=\"v.is_like == 1 ? '/static/img/dz1.png' : '/static/img/dz.png'\" mode=\"aspectFill\"></image>\n                          <text v-if=\"v.like_count > 0 || v.likes > 0\">{{v.like_count || v.likes}}</text>\n                        </view>\n                      </view>\n                      \n                      <view :class=\"['comment-info-content', (v.status != 5 || v.delete_time) && 'db']\" @tap=\"openComment\" \n                            data-type=\"1\" :data-uid=\"v.uid\" \n                            :data-cid=\"item.id\" \n                            :data-name=\"v.user_info && v.user_info.nickname ? v.user_info.nickname : '用户'\" :data-idx=\"index\" :data-i=\"getReplyIndex(item.replies, v.id)\">\n                        <rich-text \n                          v-if=\"!v.delete_time\" \n                          :nodes=\"parseEmojiContentForRichText(v.content)\" \n                          user-select=\"true\"\n                          @itemclick=\"onEmojiClick\"\n                          class=\"comment-rich-text reply-rich-text\"\n                        ></rich-text>\n                        <text v-else class=\"deleted-comment\">(该评论已被删除)</text>\n                        <!-- 显示评论图片 -->\n                        <image \n                          v-if=\"v.image && !v.delete_time\" \n                          class=\"comment-image reply-comment-image\" \n                          mode=\"widthFix\" \n                          :src=\"v.image\" \n                          @tap.stop=\"previewCommentImage(v.image)\"\n                          lazy-load\n                        ></image>\n                      </view>\n                      \n                      <view class=\"comment-info-bottom df\">\n                        <text>{{v.create_time}} {{v.province || ''}}</text>\n                        <!-- 回复按钮 -->\n                        <view v-if=\"!v.delete_time\" @tap=\"openComment\" data-type=\"1\" :data-uid=\"v.uid\" \n                              :data-cid=\"item.id\" :data-name=\"v.user_info && v.user_info.nickname ? v.user_info.nickname : '用户'\" :data-idx=\"index\" :data-i=\"getReplyIndex(item.replies, v.id)\">\n                          回复\n                        </view>\n                        <view v-if=\"userId == v.uid && v.status == 5 && !v.delete_time\" \n                              @tap=\"delComment\" :data-id=\"v.id\" :data-idx=\"index\" :data-i=\"getReplyIndex(item.replies, v.id)\">\n                          删除\n                        </view>\n                      </view>\n                    </view>\n                  </view>\n                </template>\n                \n                <!-- 展开更多回复 -->\n                <view v-if=\"item.reply_count > (item.replies ? item.replies.length : 0)\" class=\"unfold\" \n                      :data-id=\"item.id\" :data-idx=\"index\" @tap=\"sonComment\">\n                  <view v-if=\"item.loading_replies\" class=\"loading-text\">\n                    <image class=\"loading-icon\" src=\"/static/img/loading.gif\"></image>\n                    加载中...\n                  </view>\n                  <text v-else>\n                    <template v-if=\"!item.replies || item.replies.length === 0\">\n                      查看 {{item.reply_count}} 条回复\n                    </template>\n                    <template v-else-if=\"item.has_more_replies\">\n                      加载更多回复 ({{item.replies.length}}/{{item.reply_count}})\n                    </template>\n                    <template v-else>\n                      已加载全部回复\n                    </template>\n                  </text>\n                </view>\n              </view>\n            </view>\n          </block>\n          \n          <!-- 加载状态 -->\n          <uni-load-more v-if=\"loadStatus != 'no-more'\" :status=\"loadStatus\"></uni-load-more>\n          <view v-else class=\"no-more\">- THE END -</view>\n        </scroll-view>\n      \n        <!-- 评论输入框 -->\n        <view class=\"comment-btn df\" data-type=\"0\" data-idx=\"-1\" data-i=\"-1\" @tap=\"openComment\">\n          <image :src=\"userAvatar || '/static/img/avatar_default.png'\" mode=\"aspectFill\"></image>\n          <view class=\"pl-str ohto\" style=\"width:calc(100% - 120rpx)\">{{comtext ? comtext : comtips}}</view>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport lazyImage from '@/components/lazyImage/lazyImage'\nimport play from '@/components/play/play'\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\nimport SharePanel from '@/components/share/index.vue'\nimport CommentInput from '@/components/comment-input/comment-input.vue'\nimport sinaEmoji from '@/components/emoji-panel/sina.js'\nimport {\n  getDynamicDetail,\n  likeDynamic,\n  deleteDynamic,\n  getCommentsList,\n  getCommentReplies,\n  addComment,\n  deleteComment,\n  likeComment,\n  unlikeComment,\n  followUser,\n  reportDynamic\n} from '@/api/social.js'\n\nexport default {\n  name: 'VideoDetails',\n  components: {\n    lazyImage,\n    play,\n    uniLoadMore,\n    SharePanel,\n    CommentInput\n  },\n  computed: {\n    // 空状态判断\n    showEmptyState() {\n      return this.isEmpty && this.page === 1;\n    },\n    \n    // 处理头像展示\n    formattedUserAvatar() {\n      return this.userAvatar || '/static/img/avatar_default.png';\n    },\n    \n    // 发送按钮图标\n    sendButtonSrc() {\n      return this.comtext.length ? '/static/img/fs1.png' : '/static/img/fs.png';\n    },\n    \n    // 是否为音频动态\n    isAudioNote() {\n      return this.noteInfo.type === 4;\n    },\n    \n    // 是否为视频动态\n    isVideoNote() {\n      return this.noteInfo.type === 3;\n    },\n    \n    // 是否为图片动态\n    isImageNote() {\n      return this.noteInfo.type === 1 || this.noteInfo.type === 2;\n    },\n    \n    // 表情缓存统计（用于性能监控）\n    emojiCacheInfo() {\n      return {\n        emojiMapSize: this.emojiMap.size,\n        parsedContentCacheSize: this.parsedContentCache.size,\n        cacheHitRate: this.parsedContentCache.size > 0 ? \n          (this.parsedContentCache.size / (this.parsedContentCache.size + 10)).toFixed(2) : '0.00'\n      };\n    },\n    \n    // 页面性能统计\n    pagePerformanceInfo() {\n      return {\n        commentCacheSize: Object.keys(this.commentCache).length,\n        replyIndicesSize: this.replyIndices.size,\n        isPageActive: this.isPageActive\n      };\n    }\n  },\n  data() {\n    return {\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\n      keyboardHeight: 0,\n      footerHeight: 0,\n      userId: 0,           // 用户ID (uid)\n      userNickname: \"\",    // 用户昵称 (nickname)\n      userAvatar: \"/static/img/avatar_default.png\", // 设置默认头像\n      isUser: false,       // 用户是否完善资料 (根据userId和phone判断)\n      isFollowing: false,  // 是否已关注动态作者\n      followChecked: false, // 关注状态是否已检查\n      actionInProgress: false, // 操作进行中防重复\n      likeThrottling: false,   // 点赞防抖\n      bgAudioStatus: false,    // 音频播放状态\n      bgAudioManager: null,    // 音频管理器\n      audioPlayingId: '',      // 当前播放音频ID\n      audioProgress: 0,        // 音频播放进度\n      currentTime: 0,          // 当前播放时间\n      duration: 0,             // 音频总时长\n      // 添加加载状态控制\n      isLoadingDetail: false,   // 是否正在加载详情\n      isLoadingComments: false, // 是否正在加载评论\n      isSubmittingComment: false, // 是否正在提交评论\n      isDeletingComment: false,   // 是否正在删除评论\n      isLoadingReplies: false,    // 是否正在加载回复\n      // 缓存相关\n      commentsCache: {},          // 评论列表缓存\n      commentCache: {},           // 回复列表缓存\n      replyIndices: new Map(),    // 回复索引映射\n      noteInfo: {\n        id: 0,\n        uid: 0,\n        user_info: {\n          uid: 0,\n          nickname: \"昵称加载中\",\n          avatar: \"/static/img/avatar_default.png\" // 设置默认头像\n        },\n        content: \"内容加载中\",\n        type: 0,\n        comments: 0,\n        likes: 0,\n        views: 0,\n        shares: 0,\n        is_like: false,\n        create_time: \"日期\",\n        location_name: \"IP属地\",\n        latitude: \"0\",\n        longitude: \"0\",\n        topic_id: \"\",\n        topic_info: [],\n        product_id: 0,\n        goods_info: null,\n        images: [],\n        video: \"\",\n        video_cover: \"\",\n        audio: \"\",\n        audio_title: \"\",\n        audio_cover: \"\"\n      },\n      isCommentContent: false,\n      isEmpty: false,\n      commentView: false,\n      shareView: false,\n      commentList: [],\n      cType: 0,\n      isThrottling: true,\n      cIdx: -1,\n      cI: -1,\n      page: 1,\n      sonPage: 1,\n      limit: 10, // 每页评论数量\n      loadStatus: \"loading\",\n      cCId: 0,\n      cUId: 0,\n      isComment: false,\n      isFocus: false,\n      comtips: \"说点什么...\",\n      comtext: \"\",\n      isCommentPopup: false,\n      isFocusPopup: false,\n      tipsTitle: \"\",\n      isContentOverflow: false, // 内容是否超出两行\n      isExpanded: false,        // 内容是否已展开\n      currentImageIndex: 0,      // 当前显示的图片索引\n      isShareVisible: false,        // 分享面板显示状态\n      debug: true, // 是否开启调试日志\n      \n      // 表情相关优化\n      emojiMap: new Map(), // 表情映射缓存，提高查找性能\n      parsedContentCache: new Map(), // 解析内容缓存\n      emojiClickTimer: null, // 表情点击防抖定时器\n      isEmojiLoading: false, // 表情加载状态\n      previewEmojiData: null, // 预览表情数据\n      maxCacheSize: 200, // 最大缓存条目数\n      \n      // 性能优化相关\n      isPageActive: true, // 页面是否活跃\n      performanceTimer: null, // 性能监控定时器\n      lazyLoadObserver: null, // 懒加载观察器\n    }\n  },\n  async onLoad(options) {\n    console.log('页面加载参数:', options);\n    \n    // 标记页面为活跃状态\n    this.isPageActive = true;\n    \n    // 判断平台兼容性，只在支持分享菜单的平台调用\n    if (uni.showShareMenu && typeof uni.showShareMenu === 'function') {\n      uni.showShareMenu();\n    }\n    \n    // 等待小程序初始化完成\n    await this.$onLaunched;\n    console.log('小程序初始化完成');\n    \n    // 初始化表情映射缓存\n    this.initEmojiMap();\n    \n    // 加载最近使用的表情\n    this.loadRecentEmojis();\n    \n    // 首先获取用户信息\n    this.userInfoHandle();\n    \n    // 设置动态ID并获取详情\n    if (options.id) {\n      this.noteInfo.id = options.id;\n      this.debugLog('获取到动态ID', this.noteInfo.id);\n      \n      // 获取动态详情\n      this.dynamicDetails();\n    } else {\n      console.error('未提供动态ID');\n      uni.showToast({\n        title: '参数错误',\n        icon: 'none'\n      });\n      setTimeout(() => {\n        uni.navigateBack();\n      }, 1500);\n      return;\n    }\n    \n    // 设置底部安全区域高度\n    this.safeAreaBottom = (uni.getSystemInfoSync().safeAreaInsets && uni.getSystemInfoSync().safeAreaInsets.bottom) || 0;\n    \n    // 计算底部高度\n    this.$nextTick(() => {\n      uni.createSelectorQuery().in(this)\n        .select('.footer-box')\n        .boundingClientRect(data => {\n          if (data) {\n            this.footerHeight = parseInt(data.height);\n          }\n        })\n        .exec();\n    });\n  },\n  \n  onShow() {\n    // 标记页面为活跃状态\n    this.isPageActive = true;\n    \n    // 更新用户信息（静默更新，避免重复请求）\n    this.userInfoHandle();\n    \n    // 如果是音频动态且之前正在播放，恢复播放状态检查\n    if (this.isAudioNote && this.bgAudioManager) {\n      this.checkAudioStatus();\n    }\n  },\n  \n  onHide() {\n    // 标记页面为非活跃状态\n    this.isPageActive = false;\n    \n    // 只有音频类型才处理音频暂停\n    if (this.isAudioNote && this.bgAudioManager && this.bgAudioStatus) {\n      this.pauseAudio();\n    }\n  },\n  \n  mounted() {\n    // 判断平台兼容性，只在支持页面滚动监听的平台添加监听\n    if (uni.onPageScroll && typeof uni.onPageScroll === 'function') {\n      uni.onPageScroll(this.handlePageScroll);\n    }\n    \n    // 初始化懒加载\n    this.initLazyLoad();\n  },\n  \n  beforeUnmount() { // Vue3: beforeDestroy改为beforeUnmount\n    // 标记页面为非活跃状态\n    this.isPageActive = false;\n\n    // 清理所有资源\n    this.cleanupResources();\n  },\n  \n  methods: {\n    // 添加调试日志函数\n    debugLog(...args) {\n      if (this.debug) {\n        console.log('[Video]', ...args);\n      }\n    },\n    \n    // 获取用户信息\n    userInfoHandle() {\n      try {\n        // 统一从本地存储获取用户信息，保持一致性\n        let userInfo = uni.getStorageSync(\"USER_INFO\") || {};\n        console.log('本地存储用户数据类型:', typeof userInfo);\n        \n        // 确保userInfo是对象类型\n        if (typeof userInfo === 'string') {\n          try {\n            userInfo = JSON.parse(userInfo);\n            console.log('已将字符串解析为对象');\n          } catch (e) {\n            console.error('解析用户数据失败:', e);\n            userInfo = {};\n          }\n        }\n        \n        // 从Vuex获取用户ID作为备用\n        const storeUid = this.$store.state.app?.uid || 0;\n        \n        // 优先使用本地缓存中的用户信息，Vuex作为备用\n        this.userId = userInfo.uid || userInfo.id || storeUid || 0;\n        this.userAvatar = userInfo.avatar || '';\n        this.userNickname = userInfo.nickname || '';\n        \n        // 判断用户是否完善资料 - 需要有用户ID和手机号\n        const hasUserId = this.userId > 0;\n        const hasPhone = !!userInfo.phone;\n        this.isUser = hasUserId && hasPhone;\n        \n        this.debugLog('用户信息处理完成', {\n          userId: this.userId,\n          userAvatar: this.userAvatar,\n          userNickname: this.userNickname,\n          hasUserId: hasUserId,\n          hasPhone: hasPhone,\n          isUser: this.isUser\n        });\n      } catch (error) {\n        console.error('获取用户信息失败:', error);\n        // 设置默认值\n        this.userId = 0;\n        this.userAvatar = '';\n        this.userNickname = '';\n        this.isUser = false;\n      }\n    },\n    \n    // 处理媒体数据\n    processMediaData() {\n      try {\n        this.debugLog('开始处理媒体数据，笔记类型:', this.noteInfo.type);\n        \n        // 根据类型处理不同的媒体数据\n        switch (this.noteInfo.type) {\n          case 1: // 单图\n          case 2: // 多图\n            this.processImageData();\n            break;\n          case 3: // 视频\n            this.processVideoData();\n            break;\n          case 4: // 音频\n            this.processAudioData();\n            break;\n          default:\n            this.debugLog('未知的媒体类型:', this.noteInfo.type);\n            // 尝试自动判断类型\n            if (this.noteInfo.video) {\n              this.noteInfo.type = 3;\n              this.processVideoData();\n            } else if (this.noteInfo.audio) {\n              this.noteInfo.type = 4;\n              this.processAudioData();\n            } else if (this.noteInfo.images && this.noteInfo.images.length) {\n              this.noteInfo.type = this.noteInfo.images.length > 1 ? 2 : 1;\n              this.processImageData();\n            }\n        }\n      } catch (error) {\n        this.debugLog('处理媒体数据失败:', error);\n      }\n    },\n    \n    // 处理图片数据\n    processImageData() {\n      this.debugLog('处理图片数据');\n      \n      try {\n        // 确保images是数组\n        let images = this.noteInfo.images;\n        if (!Array.isArray(images)) {\n          // 如果是字符串，尝试解析JSON\n          if (typeof images === 'string' && images.startsWith('[')) {\n            try {\n              images = JSON.parse(images);\n            } catch (e) {\n              images = [images];\n            }\n          } else if (images) {\n            // 非数组且非空，转为数组\n            images = [images];\n          } else {\n            // 为空，初始化为空数组\n            images = [];\n          }\n        }\n        \n        // 处理每张图片\n        this.noteInfo.images = images.map(img => {\n          // 如果是对象，提取URL\n          if (img && typeof img === 'object') {\n            return img.url || img.src || img.path || img.image || '';\n          }\n          // 如果是字符串，直接返回\n          return img || '';\n        }).filter(url => !!url); // 过滤空URL\n        \n        // 确保所有图片URL都是字符串类型\n        this.noteInfo.images = this.noteInfo.images.map(url => {\n          return typeof url === 'string' ? url : String(url);\n        });\n        \n        this.debugLog('图片数据处理完成，数量:', this.noteInfo.images.length);\n      } catch (e) {\n        this.debugLog('处理图片数据失败:', e);\n        this.noteInfo.images = [];\n      }\n    },\n    \n    // 处理视频数据\n    processVideoData() {\n      console.log('处理视频数据:', this.noteInfo.video);\n      \n      // 确保视频封面存在\n      if (!this.noteInfo.video_cover && this.noteInfo.video && this.noteInfo.video.cover) {\n        this.noteInfo.video_cover = this.noteInfo.video.cover;\n      }\n      \n      // 确保视频URL存在并且是字符串类型\n      if (this.noteInfo.video && typeof this.noteInfo.video === 'object') {\n        // 如果video是对象，尝试获取其中的URL\n        if (this.noteInfo.video.url) {\n          this.noteInfo.video = this.noteInfo.video.url;\n        } else if (this.noteInfo.video.src) {\n          this.noteInfo.video = this.noteInfo.video.src;\n        } else if (this.noteInfo.video.path) {\n          this.noteInfo.video = this.noteInfo.video.path;\n        }\n      }\n      \n      // 确保视频URL存在\n      if (!this.noteInfo.video && this.noteInfo.video_url) {\n        this.noteInfo.video = this.noteInfo.video_url;\n      }\n      \n      // 确保视频URL是字符串类型\n      if (this.noteInfo.video && typeof this.noteInfo.video !== 'string') {\n        this.noteInfo.video = String(this.noteInfo.video);\n      }\n      \n      // 确保video_cover是字符串\n      if (this.noteInfo.video_cover && typeof this.noteInfo.video_cover === 'object') {\n        if (this.noteInfo.video_cover.url) {\n          this.noteInfo.video_cover = this.noteInfo.video_cover.url;\n        }\n      }\n      \n      // 确保video_cover是字符串类型\n      if (this.noteInfo.video_cover && typeof this.noteInfo.video_cover !== 'string') {\n        this.noteInfo.video_cover = String(this.noteInfo.video_cover);\n      }\n    },\n    \n    // 处理音频数据 - 只有音频动态时才执行\n    processAudioData() {\n      if (!this.isAudioNote) {\n        console.log('非音频动态，跳过音频数据处理');\n        return;\n      }\n      \n      console.log('处理音频数据:', this.noteInfo.audio);\n      \n      // 确保音频URL是字符串\n      if (this.noteInfo.audio && typeof this.noteInfo.audio === 'object') {\n        if (this.noteInfo.audio.url) {\n          this.noteInfo.audio = this.noteInfo.audio.url;\n        } else if (this.noteInfo.audio.src) {\n          this.noteInfo.audio = this.noteInfo.audio.src;\n        } else if (this.noteInfo.audio.path) {\n          this.noteInfo.audio = this.noteInfo.audio.path;\n        }\n      }\n      \n      // 确保音频URL是字符串类型\n      if (this.noteInfo.audio && typeof this.noteInfo.audio !== 'string') {\n        this.noteInfo.audio = String(this.noteInfo.audio);\n      }\n      \n      // 确保封面图存在且是字符串\n      if (!this.noteInfo.audio_cover) {\n        this.noteInfo.audio_cover = '/static/img/audio_default_cover.png';\n      } else if (typeof this.noteInfo.audio_cover === 'object') {\n        if (this.noteInfo.audio_cover.url) {\n          this.noteInfo.audio_cover = this.noteInfo.audio_cover.url;\n        }\n      }\n      \n      // 确保封面是字符串类型\n      if (this.noteInfo.audio_cover && typeof this.noteInfo.audio_cover !== 'string') {\n        this.noteInfo.audio_cover = String(this.noteInfo.audio_cover);\n      }\n      \n      // 确保标题存在\n      if (!this.noteInfo.audio_title) {\n        this.noteInfo.audio_title = '音频';\n      }\n      \n      // 初始化音频状态\n      this.initAudioState();\n    },\n    \n    // 初始化音频状态 - 只有音频动态时才调用\n    initAudioState() {\n      if (!this.isAudioNote) return;\n      \n      console.log('初始化音频状态');\n      \n      // 重置音频相关状态\n      this.bgAudioStatus = false;\n      this.bgAudioManager = null;\n      this.audioRetryCount = 0;\n      this.audioPlayingId = '';\n    },\n    \n    // 标准化图片数组\n    normalizeImageArray(originalImages) {\n      // 确保图片字段是数组\n      if (!originalImages) {\n        console.log('图片数据为空，初始化为空数组');\n        return [];\n      } \n      \n      // 解析JSON字符串\n      if (typeof originalImages === 'string') {\n        console.log('图片数据是字符串，尝试解析JSON');\n        if (originalImages.startsWith('[')) {\n          try {\n            originalImages = JSON.parse(originalImages);\n            console.log('JSON解析成功:', originalImages);\n          } catch (parseErr) {\n            console.error('JSON解析失败:', parseErr);\n            originalImages = [originalImages];\n          }\n        } else {\n          console.log('图片数据是单个字符串，转换为数组');\n          originalImages = [originalImages];\n        }\n      }\n      \n      // 确保是数组类型并标准化每个图片对象\n      if (Array.isArray(originalImages)) {\n        console.log('处理图片数组，数量:', originalImages.length);\n        return originalImages.map((img, index) => {\n          console.log(`处理第${index+1}张图片:`, img);\n          if (typeof img === 'string') {\n            return { url: img, wide: 750, high: 750 };\n          } else if (img && typeof img === 'object') {\n            // 确保即使对象中缺少某些属性，也能正常工作\n            const imgObj = {\n              url: img.url || img.path || img.src || img.image || '',\n              wide: parseInt(img.wide || img.width || 750),\n              high: parseInt(img.high || img.height || 750)\n            };\n            console.log(`图片${index+1}处理结果:`, imgObj);\n            return imgObj;\n          }\n          return { url: '', wide: 750, high: 750 };\n        }).filter(img => !!img.url); // 过滤掉没有URL的图片\n      } else {\n        console.log('图片数据不是数组，初始化为空数组');\n        return [];\n      }\n    },\n    \n    // 处理通用数据\n    processCommonData() {\n      // 确保user_info对象存在\n      if (!this.noteInfo.user_info) {\n        this.noteInfo.user_info = {\n          nickname: \"用户\",\n          avatar: \"/static/img/avatar_default.png\"\n        };\n      }\n      \n      // 确保avatar字段存在\n      if (!this.noteInfo.user_info.avatar) {\n        this.noteInfo.user_info.avatar = \"/static/img/avatar_default.png\";\n      }\n      \n      // 确保comments字段存在且为数字\n      if (this.noteInfo.comments === undefined || this.noteInfo.comments === null) {\n        this.noteInfo.comments = 0;\n      } else if (typeof this.noteInfo.comments === 'string') {\n        this.noteInfo.comments = parseInt(this.noteInfo.comments) || 0;\n      }\n      \n      // 确保uid字段存在\n      if (!this.noteInfo.uid && this.noteInfo.user_id) {\n        this.noteInfo.uid = this.noteInfo.user_id;\n      }\n      \n      console.log('通用数据处理完成');\n    },\n    \n    // 检查是否有圈子\n    hasCircle() {\n      // 新的 circle_info 结构\n      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_id > 0) {\n        return true;\n      }\n      // 兼容旧的直接字段结构\n      if (this.noteInfo.circle_id && this.noteInfo.circle_id > 0) {\n        return true;\n      }\n      return false;\n    },\n    \n    // 获取圈子ID\n    getCircleId() {\n      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_id) {\n        return this.noteInfo.circle_info.circle_id;\n      }\n      return this.noteInfo.circle_id || 0;\n    },\n    \n    // 获取圈子名称\n    getCircleName() {\n      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_name) {\n        return this.noteInfo.circle_info.circle_name;\n      }\n      return this.noteInfo.circle_name || '';\n    },\n    \n    // 获取圈子头像\n    getCircleAvatar() {\n      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_avatar) {\n        return this.noteInfo.circle_info.circle_avatar;\n      }\n      return this.noteInfo.circle_avatar || '/static/img/qz1.png';\n    },\n    \n    // 初始化表情映射缓存\n    initEmojiMap() {\n      if (this.emojiMap.size > 0) return; // 避免重复初始化\n      \n      try {\n        // 将表情数组转换为Map，提高查找性能\n        sinaEmoji.forEach(emoji => {\n          if (emoji && emoji.phrase) {\n            this.emojiMap.set(emoji.phrase, emoji);\n          }\n        });\n        console.log('表情映射缓存初始化完成，共', this.emojiMap.size, '个表情');\n      } catch (error) {\n        console.error('初始化表情映射失败:', error);\n      }\n    },\n    \n    // 加载最近使用的表情\n    loadRecentEmojis() {\n      try {\n        const recentEmojisStr = uni.getStorageSync('recent_emojis');\n        if (recentEmojisStr) {\n          this.recentEmojis = JSON.parse(recentEmojisStr);\n        }\n      } catch (e) {\n        console.error('加载表情记录失败', e);\n      }\n    },\n    \n    // 初始化懒加载\n    initLazyLoad() {\n      // 这里可以添加图片懒加载逻辑\n      console.log('初始化懒加载');\n    },\n    \n    // 清理所有资源\n    cleanupResources() {\n      console.log('清理页面资源');\n      \n      // 判断平台兼容性，只在支持页面滚动监听的平台移除监听\n      if (uni.offPageScroll && typeof uni.offPageScroll === 'function') {\n        uni.offPageScroll(this.handlePageScroll);\n      }\n      \n      // 销毁音频实例（只有音频动态时才执行）\n      if (this.isAudioNote) {\n        this.destroyAudioInstance();\n      }\n      \n      // 清除所有定时器\n      this.clearAllTimers();\n      \n      // 清除表情相关定时器和缓存\n      this.cleanupEmojiResources();\n      \n      // 清理懒加载观察器\n      if (this.lazyLoadObserver) {\n        this.lazyLoadObserver.disconnect();\n        this.lazyLoadObserver = null;\n      }\n    },\n    \n    // 清除所有定时器\n    clearAllTimers() {\n      if (this.debounceTimer) {\n        clearTimeout(this.debounceTimer);\n        this.debounceTimer = null;\n      }\n      \n      if (this.commentBlurTimer) {\n        clearTimeout(this.commentBlurTimer);\n        this.commentBlurTimer = null;\n      }\n      \n      if (this.performanceTimer) {\n        clearTimeout(this.performanceTimer);\n        this.performanceTimer = null;\n      }\n      \n      if (this.imageCheckInterval) {\n        clearInterval(this.imageCheckInterval);\n        this.imageCheckInterval = null;\n      }\n    },\n    \n    // 清理表情相关资源\n    cleanupEmojiResources() {\n      if (this.emojiClickTimer) {\n        clearTimeout(this.emojiClickTimer);\n        this.emojiClickTimer = null;\n      }\n      \n      // 清理表情缓存以释放内存\n      this.clearEmojiCache();\n    },\n    \n    // 清理表情缓存\n    clearEmojiCache() {\n      this.parsedContentCache.clear();\n      console.log('表情缓存已清理');\n    },\n    \n    // 获取表情缓存统计\n    getEmojiCacheStats() {\n      return {\n        emojiMapSize: this.emojiMap.size,\n        parsedContentCacheSize: this.parsedContentCache.size,\n        maxCacheSize: this.maxCacheSize\n      };\n    },\n    \n    // 强制应用表情样式（运行时修复）\n    forceApplyEmojiStyles() {\n      this.$nextTick(() => {\n        try {\n          // 获取所有表情图片元素\n          const emojiImages = uni.createSelectorQuery().in(this)\n            .selectAll('image[data-emoji], img[data-emoji]')\n            .exec((res) => {\n              if (res && res[0]) {\n                res[0].forEach((node, index) => {\n                  // 通过选择器强制设置样式\n                  uni.createSelectorQuery().in(this)\n                    .select(`image[data-emoji]:nth-child(${index + 1}), img[data-emoji]:nth-child(${index + 1})`)\n                    .fields({\n                      node: true,\n                      size: true\n                    })\n                    .exec((nodeRes) => {\n                      if (nodeRes && nodeRes[0] && nodeRes[0].node) {\n                        const node = nodeRes[0].node;\n                        // 强制设置样式\n                        node.style.width = '32rpx';\n                        node.style.height = '32rpx';\n                        node.style.maxWidth = '32rpx';\n                        node.style.maxHeight = '32rpx';\n                        node.style.minWidth = '32rpx';\n                        node.style.minHeight = '32rpx';\n                        node.style.objectFit = 'cover';\n                      }\n                    });\n                });\n              }\n            });\n        } catch (error) {\n          console.warn('强制应用表情样式失败:', error);\n        }\n      });\n    },\n    \n    // 返回上一页\n    navBack() {\n      if (getCurrentPages().length > 1) {\n        uni.navigateBack()\n      } else {\n        uni.switchTab({\n          url: \"/pages/index/index\"\n        })\n      }\n    },\n    \n    // 显示提示\n    opTipsPopup(msg, back = false) {\n      let self = this\n      \n      console.log(\"显示提示\", msg, back);\n      \n      self.tipsTitle = msg\n      self.$refs.tipsPopup.open()\n      \n      setTimeout(() => {\n        self.$refs.tipsPopup.close()\n        if (back) {\n          self.navBack()\n        }\n      }, 2000)\n    },\n    \n    // 页面跳转\n    navigateToFun(e) {\n      uni.navigateTo({\n        url: \"/pages/\" + e.currentTarget.dataset.url\n      })\n    },\n    \n    // 打开位置信息\n    openLocationClick() {\n      if (!this.noteInfo.latitude || !this.noteInfo.longitude) return;\n      \n      uni.openLocation({\n        latitude: parseFloat(this.noteInfo.latitude),\n        longitude: parseFloat(this.noteInfo.longitude),\n        name: this.noteInfo.location_name\n      })\n    },\n    \n    // 打开分享面板\n    openShare() {\n      this.isShareVisible = true;\n    },\n    \n    // 关闭分享面板\n    closeShare() {\n      this.isShareVisible = false;\n    },\n    \n    // 处理编辑笔记\n    handleEdit(noteId) {\n      uni.navigateTo({\n        url: `/pages/note/add?id=${noteId}`\n      });\n    },\n    \n    // 处理删除笔记\n    handleDelete(noteId) {\n      this.delDynamic();\n    },\n    \n    // 处理举报笔记\n    handleReport(reportData) {\n      this.reasonClick(reportData.reason);\n    },\n    \n    // 处理不感兴趣\n    handleDislike(noteId) {\n      console.log('标记不感兴趣:', noteId);\n      // 这里可以添加具体的不感兴趣处理逻辑\n    },\n    \n    // 点赞笔记\n    likeDynamic() {\n      if (this.actionInProgress) return;\n      this.actionInProgress = true;\n      \n      // 更新点赞状态 - 使用0和1替代布尔值，与后端一致\n      const currentLikeState = this.noteInfo.is_like ? 1 : 0;\n      const newLikeState = currentLikeState ? 0 : 1;\n      const oldLikes = this.noteInfo.likes;\n      \n      // 立即更新UI - 保存为0或1\n      this.noteInfo.is_like = newLikeState;\n      this.noteInfo.likes = newLikeState ? oldLikes + 1 : oldLikes - 1;\n      \n      // API调用\n      likeDynamic({\n        id: this.noteInfo.id,\n        is_like: newLikeState // 直接使用0或1\n      })\n      .then(res => {\n        // 成功，无需处理\n      })\n      .catch(err => {\n        // 失败，恢复状态\n        this.noteInfo.is_like = currentLikeState;\n        this.noteInfo.likes = oldLikes;\n        this.opTipsPopup('操作失败，请重试');\n      })\n      .finally(() => {\n        // 设置短时间防抖\n        setTimeout(() => {\n          this.actionInProgress = false;\n        }, 500);\n      });\n    },\n    \n    // 关注/取消关注用户\n    followUser() {\n      if (!this.noteInfo.uid || this.actionInProgress) return;\n      \n      this.actionInProgress = true;\n      \n      // 立即更新UI状态\n      const newFollowState = !this.isFollowing;\n      const oldFollowState = this.isFollowing;\n      \n      this.isFollowing = newFollowState;\n      \n      // 同步更新用户信息\n      if (this.noteInfo.user_info) {\n        this.noteInfo.user_info.is_follow = newFollowState;\n      }\n      \n      // API调用\n      followUser({\n        follow_uid: this.noteInfo.uid,\n        is_follow: newFollowState ? 1 : 0\n      })\n      .then(res => {\n        // 成功无需处理\n      })\n      .catch(err => {\n        // 失败恢复状态\n        this.isFollowing = oldFollowState;\n        if (this.noteInfo.user_info) {\n          this.noteInfo.user_info.is_follow = oldFollowState;\n        }\n        this.opTipsPopup(\"操作失败，请重试\");\n      })\n      .finally(() => {\n        // 设置短时间防抖\n        setTimeout(() => {\n          this.actionInProgress = false;\n        }, 500);\n      });\n    },\n    \n    // 删除笔记\n    delDynamic() {\n      let self = this;\n      \n      uni.showModal({\n        content: \"确认要永久删除这篇笔记吗？\",\n        confirmColor: \"#FA5150\",\n        success: function(res) {\n          if (res.confirm) {\n            uni.showLoading({\n              mask: true\n            });\n            \n            // 使用导入的API方法\n            deleteDynamic(self.noteInfo.id).then(res => {\n              uni.hideLoading();\n              getApp().globalData.isCenterPage = true;\n              \n              if (res.status === 200) {\n                self.opTipsPopup(\"删除成功\", true);\n              } else {\n                self.opTipsPopup(res.msg || \"删除失败\");\n              }\n            }).catch(err => {\n              uni.hideLoading();\n              self.opTipsPopup(\"删除失败\");\n            });\n          }\n        }\n      });\n    },\n    \n    // 举报笔记\n    reasonClick(reason) {\n      let self = this;\n      \n      uni.showLoading({\n        mask: true\n      });\n      \n      // 获取合适的图片URL\n      let imageUrl = '';\n      if (self.noteInfo.type == 2 && self.noteInfo.images && self.noteInfo.images.length > 0) {\n        imageUrl = self.noteInfo.images[0].url;\n      } else if (self.noteInfo.type == 3 && self.noteInfo.video && self.noteInfo.video_cover) {\n        imageUrl = self.noteInfo.video_cover;\n      } else if (self.noteInfo.type == 4 && self.noteInfo.audio && self.noteInfo.audio.cover) {\n        imageUrl = self.noteInfo.audio.cover;\n      }\n      \n      // 使用新的API调用方式\n      reportDynamic(\n        reason,\n        self.noteInfo.id,\n        self.noteInfo.uid,\n        self.noteInfo.content,\n        imageUrl\n      ).then(res => {\n        uni.hideLoading();\n        self.opTipsPopup(res.msg || \"举报成功\");\n        self.menuPopupClick(false);\n      }).catch(err => {\n        uni.hideLoading();\n        self.opTipsPopup('举报失败');\n      });\n    },\n    \n    // 分享设置\n    onShareAppMessage: function() {\n      return {\n        title: this.noteInfo.content,\n        imageUrl: this.getShareImageUrl(),\n        path: \"/pages/note/video?id=\" + this.noteInfo.id\n      };\n    },\n    \n    // 分享到朋友圈\n    onShareTimeline() {\n      return {\n        title: this.noteInfo.content,\n        imageUrl: this.getShareImageUrl(),\n        query: \"id=\" + this.noteInfo.id\n      };\n    },\n    \n    // 获取分享图片URL\n    getShareImageUrl() {\n      if (this.noteInfo.type == 2) {\n        if (this.noteInfo.images && this.noteInfo.images.length > 0) {\n          const firstImage = this.noteInfo.images[0];\n          if (typeof firstImage === 'string') {\n            return firstImage;\n          } else if (firstImage.url) {\n            return firstImage.url;\n          }\n        }\n        return '';\n      } else if (this.noteInfo.type == 3) {\n        return this.noteInfo.video_cover || '';\n      } else if (this.noteInfo.type == 4) {\n        return this.noteInfo.audio_cover || '';\n      }\n      return '';\n    },\n    \n    // 格式化数字显示\n    formatCount(count) {\n      if (!count || count < 1000) return count;\n      if (count < 10000) return (count / 1000).toFixed(1) + 'k';\n      return (count / 10000).toFixed(1) + 'w';\n    },\n    \n    // 格式化时间\n    formatTime(seconds) {\n      if (!seconds || isNaN(seconds)) return '00:00';\n      \n      const mins = Math.floor(seconds / 60);\n      const secs = Math.floor(seconds % 60);\n      \n      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    },\n    \n    // 格式化日期为字符串\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      \n      return `${year}-${month}-${day} ${hours}:${minutes}`;\n    },\n    \n    // 获取评论列表 - 优化版本\n    getCommentList() {\n      // 防止重复加载\n      if (this.isLoadingComments) {\n        this.debugLog('正在加载评论，跳过重复请求');\n        return;\n      }\n      \n      // 如果已经加载完所有评论，不再请求\n      if (this.loadStatus === \"no-more\" && this.page > 1) {\n        this.debugLog('已无更多评论，跳过请求');\n        return;\n      }\n      \n      this.isLoadingComments = true;\n      \n      // 第一页加载时显示加载状态，滚动加载更多时不显示全屏加载\n      if (this.page === 1) {\n        uni.showLoading({\n          title: '加载中',\n          mask: true\n        });\n      } else {\n        // 设置加载中状态\n        this.loadStatus = \"loading\";\n      }\n      \n      // 准备请求参数 - 与details.vue保持一致\n      const params = {\n        type: 0, // 动态评论类型\n        page: this.page || 1,\n        sort_type: this.cType || 0 // 0-默认，1-最新\n      };\n      \n      this.debugLog('获取评论列表', {\n        动态ID: this.noteInfo.id,\n        页码: params.page,\n        排序方式: params.sort_type === 0 ? '默认' : '最新'\n      });\n      \n      // 调用评论列表API\n      getCommentsList(this.noteInfo.id, params)\n        .then(res => {\n          if (this.page === 1) {\n            uni.hideLoading();\n          }\n          \n          this.isLoadingComments = false;\n          \n          if (res.status === 200) {\n            this.debugLog('评论列表获取成功', res.data);\n            \n            const list = res.data.list || [];\n            \n            // 更新评论总数\n            if (res.data.total !== undefined && res.data.total !== null) {\n              this.noteInfo.comments = parseInt(res.data.total) || 0;\n              this.debugLog('从API获取总评论数', this.noteInfo.comments);\n            } else if (res.data.count !== undefined && res.data.count !== null) {\n              this.noteInfo.comments = parseInt(res.data.count) || 0;\n              this.debugLog('从API获取总评论数(count字段)', this.noteInfo.comments);\n            }\n            \n            // 处理评论列表数据 - 与details.vue保持一致的字段名称\n            const processedList = list.map(item => {\n              // 确保用户信息字段存在\n              const avatar = item.avatar || \n                (item.user_info && item.user_info.avatar) || \n                '/static/img/avatar_default.png';\n                \n              const nickname = item.nickname || \n                (item.user_info && item.user_info.nickname) || \n                '用户';\n                \n              const uid = item.uid || \n                (item.user_info && item.user_info.uid) || \n                0;\n              \n              return {\n                ...item,\n                // 确保基本字段存在\n                id: item.id || 0,\n                uid: uid,\n                nickname: nickname,\n                avatar: avatar,\n                // 保存原始用户信息对象，同时确保字段一致性\n                user_info: {\n                  uid: uid,\n                  nickname: nickname,\n                  avatar: avatar\n                },\n                reply_count: parseInt(item.reply_count) || 0,\n                like_count: parseInt(item.like_count) || parseInt(item.likes) || 0,\n                likes: parseInt(item.likes) || parseInt(item.like_count) || 0,\n                is_like: !!item.is_like,\n                create_time: item.create_time || item.add_time || '',\n                content: item.content || '',\n                image: item.image || '',\n                status: item.status || 5,\n                delete_time: item.delete_time || null,\n                // 处理回复列表\n                replies: Array.isArray(item.replies) ? item.replies.map(reply => {\n                  // 确保回复的用户信息字段存在\n                  const replyAvatar = reply.avatar || \n                    (reply.user_info && reply.user_info.avatar) || \n                    '/static/img/avatar_default.png';\n                    \n                  const replyNickname = reply.nickname || \n                    (reply.user_info && reply.user_info.nickname) || \n                    '用户';\n                    \n                  const replyUid = reply.uid || \n                    (reply.user_info && reply.user_info.uid) || \n                    0;\n                  \n                  return {\n                    ...reply,\n                    id: reply.id || 0,\n                    uid: replyUid,\n                    nickname: replyNickname,\n                    avatar: replyAvatar,\n                    user_info: {\n                      uid: replyUid,\n                      nickname: replyNickname,\n                      avatar: replyAvatar\n                    },\n                    reply_uid: reply.reply_uid || reply.to_uid || 0,\n                    reply_nickname: reply.reply_nickname || reply.to_nickname || '',\n                    like_count: parseInt(reply.like_count) || parseInt(reply.likes) || 0,\n                    likes: parseInt(reply.likes) || parseInt(reply.like_count) || 0,\n                    is_like: !!reply.is_like,\n                    create_time: reply.create_time || reply.add_time || '',\n                    content: reply.content || '',\n                    image: reply.image || '',\n                    status: reply.status || 5,\n                    delete_time: reply.delete_time || null\n                  };\n                }) : [],\n                // 添加回复分页相关字段\n                replyPage: 1,\n                has_more_replies: item.reply_count > (item.replies ? item.replies.length : 0)\n              };\n            });\n            \n            // 更新评论列表\n            if (this.page === 1) {\n              // 第一页：直接替换列表\n              this.commentList = processedList;\n              // 设置是否为空状态\n              this.isEmpty = processedList.length === 0;\n            } else {\n              // 加载更多：追加到列表\n              this.commentList = [...this.commentList, ...processedList];\n            }\n            \n            // 更新分页状态\n            if (list.length < 10) { // 本页不足10条，说明没有更多了\n              this.loadStatus = \"no-more\";\n            } else {\n              this.loadStatus = \"more\";\n            }\n            \n            // 缓存第一页数据\n            if (this.page === 1) {\n              const cacheKey = `comments_${this.noteInfo.id}_${this.cType}_${this.page}`;\n              this.commentsCache[cacheKey] = {\n                list: processedList,\n                isEmpty: this.isEmpty,\n                loadStatus: this.loadStatus,\n                totalComments: this.noteInfo.comments\n              };\n            }\n            \n            // 更新回复索引映射\n            this.updateReplyIndices();\n            \n            this.debugLog('评论列表更新完成', {\n              当前页码: this.page,\n              评论总数: this.commentList.length,\n              加载状态: this.loadStatus,\n              是否为空: this.isEmpty\n            });\n          } else {\n            this.debugLog('评论列表获取失败', res);\n            this.loadStatus = \"no-more\";\n            uni.showToast({\n              title: res.msg || '获取评论失败',\n              icon: 'none'\n            });\n          }\n        })\n        .catch(err => {\n          if (this.page === 1) {\n            uni.hideLoading();\n          }\n          \n          this.isLoadingComments = false;\n          this.loadStatus = \"no-more\";\n          this.debugLog('获取评论列表异常', err);\n          uni.showToast({\n            title: '获取评论失败',\n            icon: 'none'\n          });\n        });\n    },\n    \n    // 切换评论排序\n    commentClick(type) {\n      if (!this.isThrottling || this.actionInProgress) return;\n      \n      // 如果切换到相同类型，不重复加载\n      if (this.cType === type) return;\n      \n      this.isThrottling = false;\n      this.cType = type;\n      this.page = 1;\n      this.loadStatus = \"loading\";\n      \n      // 重置评论列表\n      this.commentList = [];\n      this.isEmpty = false;\n      \n      this.getCommentList();\n      \n      // 设置延时，允许在一段时间后再次切换\n      setTimeout(() => {\n        this.isThrottling = true;\n      }, 500);\n    },\n    \n    // 打开评论框\n    openComment(e) {\n      // 如果没有事件对象，创建一个空对象模拟\n      e = e || { currentTarget: { dataset: { type: 0 } } };\n      \n      // 阻止事件冒泡\n      e.stopPropagation && e.stopPropagation();\n      \n      console.log('尝试打开评论框，用户状态:', {\n        isUser: this.isUser,\n        userId: this.userId,\n        userInfo: {\n          avatar: this.userAvatar,\n          nickname: this.userNickname\n        }\n      });\n      \n      if (!this.isUser) {\n        console.log('用户未完善资料，无法评论');\n        this.opTipsPopup(\"完善账号资料后即可评论！\")\n        setTimeout(() => {\n          uni.navigateTo({\n            url: \"/pages/center/means\"\n          })\n        }, 1000)\n        return\n      }\n      \n      let dataset = e.currentTarget.dataset || {};\n      let type = dataset.type || 0;\n      let uid = dataset.uid || 0;\n      let cid = dataset.cid || 0;\n      let name = dataset.name || \"\";\n      \n      this.cIdx = dataset.idx !== undefined ? dataset.idx : -1;\n      this.cI = dataset.i !== undefined ? dataset.i : -1;\n      \n      // 强制关闭再打开，解决偶尔无法弹出的问题\n      this.isComment = false;\n      \n      // 重置状态变量\n      this.isSubmittingComment = false;\n      \n      // 设置评论目标信息\n      if (type == 1) {\n        this.cCId = cid;\n        this.cUId = uid;\n        this.comtips = \"回复：\" + name;\n      } else {\n        this.cCId = 0;\n        this.cUId = 0;\n        this.comtips = \"说点什么...\";\n      }\n      \n      // 使用nextTick确保DOM已更新\n      this.$nextTick(() => {\n        // 先显示评论框，再延迟聚焦\n        this.isComment = true;\n        \n        // 延迟聚焦，确保键盘能弹出\n        setTimeout(() => {\n          this.isFocus = true;\n          \n          // 再次确认评论框已显示\n          if (!this.isComment) {\n            this.isComment = true;\n          }\n        }, 150);\n      });\n    },\n    \n    // 关闭评论框\n    closeComment(e) {\n      // 阻止事件冒泡\n      e && e.stopPropagation && e.stopPropagation();\n      \n      console.log('手动关闭评论框');\n      \n      // 如果正在提交评论，不关闭\n      if (this.isSubmittingComment) {\n        return;\n      }\n      \n      // 清除blur定时器\n      if (this.commentBlurTimer) {\n        clearTimeout(this.commentBlurTimer);\n        this.commentBlurTimer = null;\n      }\n      \n      // 设置为非评论操作状态\n      this.commentActioning = false;\n      \n      // 关闭评论框和焦点\n      this.isComment = false;\n      this.isFocus = false;\n      \n      // 清空输入内容\n      this.comtext = \"\";\n    },\n    \n    // 处理评论提交\n    handleCommentSubmit(commentData) {\n      if (this.isSubmittingComment) return;\n      this.isSubmittingComment = true;\n      \n      // 获取评论内容和图片\n      const content = commentData.content;\n      const image = commentData.image;\n      \n      // 如果没有内容和图片，不提交\n      if (!content && !image) {\n        this.isSubmittingComment = false;\n        return this.opTipsPopup(\"表达你的态度再评论吧！\");\n      }\n      \n      // 显示加载状态\n      uni.showLoading({\n        title: '发布中',\n        mask: true\n      });\n      \n      // 先关闭评论框，避免延迟\n      this.isComment = false;\n      this.isFocus = false;\n      this.showEmoji = false;\n      \n      // 准备评论数据\n      const params = {\n        dynamic_id: this.noteInfo.id,\n        content: content,\n        pid: this.cCId || 0,\n        to_uid: this.cUId || 0\n      };\n      \n      // 如果有图片，添加到参数\n      if (image) {\n        params.image = image;\n      }\n      \n      // 提交评论\n      addComment(params)\n        .then(res => {\n          uni.hideLoading();\n          \n          if (res.status === 200) {\n            // 处理评论数据\n            const commentData = res.data || this.createDefaultCommentData(content, image);\n            \n            // 更新评论计数和列表\n            this.processCommentSuccess(commentData);\n            \n            // 显示成功提示\n            this.opTipsPopup('评论成功');\n            \n            // 如果评论列表为空，刷新页面\n            if (this.isEmpty) {\n              this.isEmpty = false;\n            }\n          } else {\n            this.opTipsPopup(res.msg || '评论失败');\n          }\n        })\n        .catch(err => {\n          uni.hideLoading();\n          this.opTipsPopup('评论失败，请重试');\n          console.error('评论请求异常', err);\n        })\n        .finally(() => {\n          // 重置状态\n          this.isSubmittingComment = false;\n          \n          // 重置评论目标信息\n          this.cCId = 0;\n          this.cUId = 0;\n          this.comtips = \"说点什么...\";\n          this.comtext = \"\";\n        });\n    },\n    \n    // 创建默认评论数据（当API返回为空时）\n    createDefaultCommentData(content, imageUrl) {\n      // 生成更安全的临时ID，避免冲突\n      const tempId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n      \n      // 获取用户地理位置信息（如果有的话）\n      const userProvince = this.noteInfo?.province || this.$store.state.app?.userInfo?.province || '';\n      \n      // 创建完整的评论数据结构\n      const commentData = {\n        id: tempId, // 使用更安全的临时ID\n        uid: this.userId,\n        nickname: this.userNickname || '用户',\n        avatar: this.userAvatar || '/static/img/avatar_default.png',\n        content: content || '',\n        image: imageUrl || '', // 评论图片URL\n        create_time: this.formatDate(new Date()),\n        likes: 0,\n        like_count: 0, // 兼容不同字段名\n        is_like: false,\n        status: 5, // 正常状态：5\n        province: userProvince,\n        delete_time: null, // 删除时间，用于标记是否被删除\n        replies: [], // 初始化回复数组\n        reply_count: 0, // 回复数量\n        has_more_replies: false, // 是否有更多回复\n        replyPage: 1, // 回复页码\n        loading_replies: false // 是否正在加载回复\n      };\n      \n      console.log('创建默认评论数据:', commentData);\n      return commentData;\n    },\n    \n    // 处理评论成功\n    processCommentSuccess(commentData) {\n      console.log(\"处理评论成功\", {\n        commentData,\n        cIdx: this.cIdx,\n        cI: this.cI,\n        noteInfoComments: this.noteInfo.comments\n      });\n      \n      // 确保commentData存在\n      if (!commentData) {\n        console.error(\"评论数据为空，无法处理\");\n        return;\n      }\n      \n      // 增加评论总数\n      this.noteInfo.comments = (this.noteInfo.comments || 0) + 1;\n      \n      // 处理回复评论\n      if (this.cIdx >= 0) {\n        console.log(\"处理回复评论\", this.cIdx);\n        \n        if (!this.commentList[this.cIdx].replies) {\n          this.commentList[this.cIdx].replies = [];\n        }\n        if (this.commentList[this.cIdx].reply_count === undefined) {\n          this.commentList[this.cIdx].reply_count = 0;\n        }\n        \n        // 设置回复目标信息\n        if (this.cUId) {\n          // 获取被回复用户的昵称\n          const nickname = this.comtips.replace(\"回复：\", \"\");\n          commentData.reply_uid = this.cUId;\n          commentData.reply_nickname = nickname;\n        }\n        \n        // 添加回复\n        this.commentList[this.cIdx].replies.push(commentData);\n        this.commentList[this.cIdx].reply_count++;\n        \n        // 更新回复索引映射\n        this.replyIndices.set(commentData.id, this.commentList[this.cIdx].replies.length - 1);\n        \n        // 强制更新视图\n        this.$forceUpdate();\n        \n        console.log(\"回复评论处理完成\", {\n          回复数量: this.commentList[this.cIdx].reply_count,\n          回复列表: this.commentList[this.cIdx].replies.length\n        });\n      }\n      // 处理新评论\n      else {\n        console.log(\"处理新评论\");\n        \n        if (this.isEmpty || this.commentList.length === 0) {\n          this.isEmpty = false;\n          this.commentList = [];\n          console.log(\"重置评论列表\");\n        }\n        \n        // 初始化回复数据\n        commentData.replies = [];\n        commentData.reply_count = 0;\n        \n        // 添加到列表头部\n        this.commentList.unshift(commentData);\n        \n        // 强制更新视图\n        this.$forceUpdate();\n        \n        console.log(\"新评论处理完成\", {\n          评论列表长度: this.commentList.length,\n          评论总数: this.noteInfo.comments\n        });\n      }\n    },\n    \n    // 删除评论\n    delComment(e) {\n      let self = this;\n      let idx = e.currentTarget.dataset.idx;\n      let i = e.currentTarget.dataset.i;\n      let commentId = e.currentTarget.dataset.id;\n      \n      // 防止重复点击\n      if (self.isDeletingComment) return;\n      \n      uni.showModal({\n        content: \"确定要永久删除该评论吗？\",\n        confirmColor: \"#FA5150\",\n        success: function(res) {\n          if (res.confirm) {\n            self.isDeletingComment = true;\n            \n            uni.showLoading({\n              title: '删除中',\n              mask: true\n            });\n            \n            // 使用新的删除评论API接口\n            deleteComment(commentId).then(res => {\n              uni.hideLoading();\n              self.isDeletingComment = false;\n              \n              if (res.status === 200) {\n                // 减少评论总数\n                if (self.noteInfo.comments > 0) {\n                  self.noteInfo.comments--;\n                }\n                \n                // 更新评论状态\n                if (i == -1) {\n                  // 主评论被删除\n                  self.commentList[idx].delete_time = new Date().toISOString(); // 设置删除时间\n                  self.commentList[idx].status = 0; // 设置删除状态\n                } else {\n                  // 确保replies数组存在\n                  if (self.commentList[idx].replies && self.commentList[idx].replies.length > i) {\n                    self.commentList[idx].replies[i].delete_time = new Date().toISOString(); // 设置删除时间\n                    self.commentList[idx].replies[i].status = 0; // 设置删除状态\n                    \n                    // 子评论被删除，也要减少对应的计数\n                    if (self.commentList[idx].reply_count > 0) {\n                      // 减少回复计数\n                      self.commentList[idx].reply_count--;\n                    }\n                  }\n                }\n                self.opTipsPopup(\"删除成功\");\n              } else {\n                self.opTipsPopup(res.msg || \"删除失败\");\n              }\n            }).catch(err => {\n              uni.hideLoading();\n              self.isDeletingComment = false;\n              self.opTipsPopup(\"删除失败\");\n            });\n          }\n        }\n      });\n    },\n    \n    // 点赞/取消点赞评论\n    toggleCommentLike(commentId, isCurrentlyLiked) {\n      if (!this.isUser) {\n        this.opTipsPopup(\"请先完善账号资料\");\n        return;\n      }\n      \n      // 防止频繁点击\n      if (this.likeThrottling) return;\n      this.likeThrottling = true;\n      \n      // 500ms后恢复\n      setTimeout(() => {\n        this.likeThrottling = false;\n      }, 500);\n      \n      // 将布尔值转换为0/1格式\n      const currentLikeState = isCurrentlyLiked ? 1 : 0;\n      const newLikeState = currentLikeState ? 0 : 1;\n      \n      // 优先更新UI状态，让用户感觉响应迅速\n      // 遍历更新评论列表中的点赞状态\n      for (let i = 0; i < this.commentList.length; i++) {\n        const comment = this.commentList[i];\n        \n        // 更新主评论\n        if (comment.id === commentId) {\n          comment.is_like = newLikeState;\n          if (newLikeState) {\n            // 处理不同的字段名称\n            if (comment.like_count !== undefined) {\n              comment.like_count = (comment.like_count || 0) + 1;\n            }\n            if (comment.likes !== undefined) {\n              comment.likes = (comment.likes || 0) + 1;\n            }\n          } else {\n            // 处理不同的字段名称\n            if (comment.like_count !== undefined && comment.like_count > 0) {\n              comment.like_count--;\n            }\n            if (comment.likes !== undefined && comment.likes > 0) {\n              comment.likes--;\n            }\n          }\n          break; // 找到了主评论，可以跳出循环\n        }\n        \n        // 更新回复评论\n        if (comment.replies && comment.replies.length > 0) {\n          for (let j = 0; j < comment.replies.length; j++) {\n            const reply = comment.replies[j];\n            if (reply.id === commentId) {\n              reply.is_like = newLikeState;\n              if (newLikeState) {\n                // 处理不同的字段名称\n                if (reply.like_count !== undefined) {\n                  reply.like_count = (reply.like_count || 0) + 1;\n                }\n                if (reply.likes !== undefined) {\n                  reply.likes = (reply.likes || 0) + 1;\n                }\n              } else {\n                // 处理不同的字段名称\n                if (reply.like_count !== undefined && reply.like_count > 0) {\n                  reply.like_count--;\n                }\n                if (reply.likes !== undefined && reply.likes > 0) {\n                  reply.likes--;\n                }\n              }\n              break; // 找到了回复，可以跳出内部循环\n            }\n          }\n        }\n      }\n      \n      // 后台执行API调用\n      if (currentLikeState) {\n        // 如果当前已点赞，则取消点赞\n        unlikeComment(commentId)\n          .then(res => {\n            // 点赞成功，不需要操作\n          })\n          .catch(err => {\n            // 恢复状态\n            this.restoreCommentLikeStatus(commentId, currentLikeState);\n            this.opTipsPopup(\"操作失败，请重试\");\n          });\n      } else {\n        // 如果当前未点赞，则点赞\n        likeComment(commentId)\n          .then(res => {\n            // 点赞成功，不需要操作\n          })\n          .catch(err => {\n            // 恢复状态\n            this.restoreCommentLikeStatus(commentId, currentLikeState);\n            this.opTipsPopup(\"操作失败，请重试\");\n          });\n      }\n    },\n    \n    // 恢复评论点赞状态（操作失败时）\n    restoreCommentLikeStatus(commentId, originalLikeState) {\n      // 操作失败时恢复状态\n      for (let i = 0; i < this.commentList.length; i++) {\n        const comment = this.commentList[i];\n        \n        // 还原主评论\n        if (comment.id === commentId) {\n          comment.is_like = originalLikeState;\n          if (originalLikeState === 1) {\n            // 处理不同的字段名称\n            if (comment.like_count !== undefined && comment.like_count > 0) {\n              comment.like_count--;\n            }\n            if (comment.likes !== undefined && comment.likes > 0) {\n              comment.likes--;\n            }\n          } else {\n            // 处理不同的字段名称\n            if (comment.like_count !== undefined) {\n              comment.like_count = (comment.like_count || 0) + 1;\n            }\n            if (comment.likes !== undefined) {\n              comment.likes = (comment.likes || 0) + 1;\n            }\n          }\n          break; // 找到后跳出循环\n        }\n        \n        // 还原回复评论\n        if (comment.replies && comment.replies.length > 0) {\n          for (let j = 0; j < comment.replies.length; j++) {\n            const reply = comment.replies[j];\n            if (reply.id === commentId) {\n              reply.is_like = originalLikeState;\n              if (originalLikeState === 1) {\n                // 处理不同的字段名称\n                if (reply.like_count !== undefined && reply.like_count > 0) {\n                  reply.like_count--;\n                }\n                if (reply.likes !== undefined && reply.likes > 0) {\n                  reply.likes--;\n                }\n              } else {\n                // 处理不同的字段名称\n                if (reply.like_count !== undefined) {\n                  reply.like_count = (reply.like_count || 0) + 1;\n                }\n                if (reply.likes !== undefined) {\n                  reply.likes = (reply.likes || 0) + 1;\n                }\n              }\n              break; // 找到后跳出循环\n            }\n          }\n        }\n      }\n    },\n    \n    // 递归更新回复索引映射\n    updateReplyIndices() {\n      if (!this.commentList || !this.commentList.length) return;\n      \n      this.replyIndices = new Map();\n      \n      this.commentList.forEach((comment, commentIndex) => {\n        if (comment.replies && comment.replies.length) {\n          comment.replies.forEach((reply, replyIndex) => {\n            this.replyIndices.set(reply.id, replyIndex);\n          });\n        }\n      });\n      \n      this.debugLog('更新回复索引映射完成', {\n        索引数量: this.replyIndices.size\n      });\n    },\n    \n    // 加载评论回复 - 与details.vue保持一致\n    sonComment(e) {\n      let id = parseInt(e.currentTarget.dataset.id) || 0;\n      let idx = parseInt(e.currentTarget.dataset.idx) || 0;\n      \n      this.debugLog('加载评论回复:', {\n        commentId: id,\n        commentIndex: idx\n      });\n      \n      // 防止重复点击\n      if (this.isLoadingReplies) return;\n      this.isLoadingReplies = true;\n      \n      // 显示加载中状态\n      const commentItem = this.commentList[idx];\n      if (commentItem) {\n        this.$set(commentItem, 'loading_replies', true);\n      }\n      \n      // 获取当前评论的回复页码\n      const currentPage = parseInt(commentItem.replyPage) || 1;\n      \n      // 生成缓存键\n      const cacheKey = `replies_${id}_${currentPage}`;\n      \n      // 检查缓存\n      if (this.commentCache && this.commentCache[cacheKey]) {\n        this.debugLog('使用缓存中的回复数据');\n        this.handleAllRepliesData(this.commentCache[cacheKey], idx, currentPage);\n        return;\n      }\n      \n      // 请求参数 - 与details.vue保持一致\n      const params = {\n        parent_id: id,           // 父评论ID\n        page: currentPage,       // 页码\n        limit: 10,               // 每页数量\n        sort_type: 1             // 排序类型：1=最新(创建时间)\n      };\n      \n      this.debugLog('回复请求参数:', params);\n      \n      // 发起请求\n      getCommentReplies(params)\n        .then(res => {\n          if (res.status === 200) {\n            this.debugLog('获取到回复数据:', res.data);\n            \n            // 缓存结果\n            if (!this.commentCache) this.commentCache = {};\n            this.commentCache[cacheKey] = res.data;\n            \n            // 处理回复数据\n            this.handleAllRepliesData(res.data, idx, currentPage);\n          } else {\n            // 移除加载状态\n            if (commentItem) {\n              this.$set(commentItem, 'loading_replies', false);\n            }\n            this.isLoadingReplies = false;\n            \n            uni.showToast({\n              title: res.msg || '获取回复失败',\n              icon: 'none'\n            });\n          }\n        })\n        .catch(err => {\n          this.debugLog('获取回复失败:', err);\n          \n          // 移除加载状态\n          if (commentItem) {\n            this.$set(commentItem, 'loading_replies', false);\n          }\n          this.isLoadingReplies = false;\n          \n          uni.showToast({\n            title: '获取回复失败',\n            icon: 'none'\n          });\n        });\n    },\n    \n    // 处理回复数据（分页方式）- 与details.vue保持一致\n    handleAllRepliesData(data, idx, page) {\n      if (this.commentList[idx]) {\n        const commentItem = this.commentList[idx];\n        \n        // 处理不同的数据结构：有些API返回data.list，有些直接返回data作为列表\n        let replies = [];\n        if (data.list && Array.isArray(data.list)) {\n          replies = data.list;\n        } else if (Array.isArray(data)) {\n          replies = data;\n        } else if (data.data && Array.isArray(data.data)) {\n          replies = data.data;\n        } else {\n          this.debugLog('无法识别的回复数据结构', data);\n          replies = [];\n        }\n        \n        this.debugLog('获取到回复数据:', replies);\n        \n        // 标准化回复数据\n        replies = replies.map(reply => {\n          // 确保回复的用户信息字段存在\n          const replyAvatar = reply.avatar || \n            (reply.user_info && reply.user_info.avatar) || \n            '/static/img/avatar_default.png';\n            \n          const replyNickname = reply.nickname || \n            (reply.user_info && reply.user_info.nickname) || \n            '用户';\n            \n          const replyUid = reply.uid || \n            (reply.user_info && reply.user_info.uid) || \n            0;\n          \n          return {\n            ...reply,\n            id: reply.id || 0,\n            uid: replyUid,\n            nickname: replyNickname,\n            avatar: replyAvatar,\n            user_info: {\n              uid: replyUid,\n              nickname: replyNickname,\n              avatar: replyAvatar\n            },\n            reply_uid: reply.reply_uid || reply.to_uid || 0,\n            reply_nickname: reply.reply_nickname || reply.to_nickname || '',\n            like_count: parseInt(reply.like_count) || parseInt(reply.likes) || 0,\n            likes: parseInt(reply.likes) || parseInt(reply.like_count) || 0,\n            is_like: !!reply.is_like,\n            create_time: reply.create_time || reply.add_time || '',\n            content: reply.content || '',\n            image: reply.image || '',\n            status: reply.status || 5,\n            delete_time: reply.delete_time || null\n          };\n        });\n        \n        // 如果是第一页，直接替换现有回复列表\n        if (page === 1) {\n          this.$set(commentItem, 'replies', replies);\n        } else {\n          // 否则追加到现有列表\n          // 避免重复添加已加载的回复\n          const existingIds = (commentItem.replies || []).map(r => r.id);\n          const newReplies = replies.filter(r => !existingIds.includes(r.id));\n          \n          this.$set(commentItem, 'replies', [...(commentItem.replies || []), ...newReplies]);\n        }\n        \n        // 更新回复页码\n        this.$set(commentItem, 'replyPage', page + 1);\n        \n        // 获取回复总数\n        let replyCount = 0;\n        if (data.count !== undefined) {\n          replyCount = parseInt(data.count) || 0;\n        } else if (data.total !== undefined) {\n          replyCount = parseInt(data.total) || 0;\n        } else {\n          replyCount = commentItem.reply_count || 0;\n        }\n        \n        // 标记是否有更多回复\n        // 如果已加载的回复数量等于或超过总回复数，设置为无更多回复\n        const currentLoadedCount = commentItem.replies ? commentItem.replies.length : 0;\n        \n        // 判断是否已加载全部回复\n        const hasNoMoreReplies = replies.length < 10 || currentLoadedCount >= replyCount;\n        this.$set(commentItem, 'has_more_replies', !hasNoMoreReplies);\n        \n        // 更新总回复数\n        this.$set(commentItem, 'reply_count', Math.max(replyCount, currentLoadedCount));\n        \n        // 更新回复索引映射\n        this.updateReplyIndices();\n        \n        // 移除加载状态\n        this.$set(commentItem, 'loading_replies', false);\n        this.isLoadingReplies = false;\n      }\n    },\n    \n    // 评论加载更多 - 优化版本\n    commentReachBottom() {\n      // 防止重复触发\n      if (this.isLoadingComments) {\n        this.debugLog('正在加载评论，跳过触底加载');\n        return;\n      }\n      \n      if (!this.isEmpty && this.commentList.length && this.loadStatus !== \"no-more\") {\n        this.debugLog('触底加载更多评论', {\n          当前页码: this.page,\n          评论总数: this.commentList.length\n        });\n        \n        this.page = this.page + 1;\n        this.loadStatus = \"loading\";\n        this.getCommentList();\n      } else {\n        this.debugLog('无更多评论可加载', {\n          isEmpty: this.isEmpty,\n          评论数量: this.commentList.length,\n          loadStatus: this.loadStatus\n        });\n      }\n    },\n    \n    // 优化页面滚动事件处理\n    handlePageScroll(e) {\n      // 保存滚动位置以优化性能\n      if (!e || !this.isPageActive) return; // 确保有事件对象且页面活跃\n      \n      const scrollTop = e.scrollTop;\n      const direction = scrollTop > this.lastScrollTop ? 'down' : 'up';\n      this.lastScrollTop = scrollTop;\n      \n      // 只在滚动方向为向下且不在加载状态时预加载评论\n      if (direction === 'down' && !this.actionInProgress && \n          scrollTop > 300 && this.loadStatus === 'more') {\n        this.preloadComments();\n      }\n    },\n    \n    // 预加载评论 - 性能优化\n    preloadComments() {\n      // 使用防抖减少请求频率\n      if (this.debounceTimer) clearTimeout(this.debounceTimer);\n      \n      this.debounceTimer = setTimeout(() => {\n        // 判断是否需要加载更多\n        if (this.page > 1 && !this.commentCache[this.page + 1]) {\n          // 预加载下一页评论但不显示\n          this.fetchCommentsForPage(this.page + 1, true);\n        }\n      }, 300);\n    },\n    \n    // 处理音频URL\n    formatAudioUrl(url) {\n      if (!url) return '';\n      \n      // 如果URL不是以http开头，尝试添加协议\n      if (!url.startsWith('http')) {\n        // 如果以//开头，添加https:\n        if (url.startsWith('//')) {\n          return 'https:' + url;\n        }\n        // 如果以/开头，可能是相对路径，尝试添加完整域名\n        if (url.startsWith('/')) {\n          return 'https://yourdomain.com' + url; // 替换为实际的域名\n        }\n        // 其他情况，假设是相对路径\n        return 'https://yourdomain.com/' + url; // 替换为实际的域名\n      }\n      \n      return url;\n    },\n    \n    // 音频播放 - 只有音频动态时才执行\n    audioBgClick() {\n      // 确保是音频类型\n      if (!this.isAudioNote) {\n        console.log('非音频动态，不执行音频播放逻辑');\n        return;\n      }\n      \n      try {\n        // 处理音频播放状态\n        if (this.bgAudioStatus) {\n          // 当前正在播放，需要暂停\n          this.pauseAudio();\n        } else {\n          // 当前已暂停，需要播放\n          this.playAudio();\n        }\n      } catch (e) {\n        console.error('音频控制异常:', e);\n        this.handleAudioError();\n      }\n    },\n    \n    // 暂停音频\n    pauseAudio() {\n      if (!this.isAudioNote || !this.bgAudioManager) return;\n      \n      try {\n        this.bgAudioManager.pause();\n        this.bgAudioStatus = false;\n        console.log('音频已暂停');\n      } catch (e) {\n        console.error('暂停音频失败:', e);\n        this.handleAudioError();\n      }\n    },\n    \n    // 播放音频 - 只有音频动态时才执行\n    playAudio() {\n      // 确保是音频类型才执行播放逻辑\n      if (!this.isAudioNote) {\n        console.log('非音频动态，不执行音频播放逻辑');\n        return;\n      }\n      \n      // 检查音频URL是否存在\n      if (!this.noteInfo.audio) {\n        return this.opTipsPopup(\"音频资源不可用\");\n      }\n      \n      // 如果已有实例，尝试继续播放\n      if (this.bgAudioManager) {\n        try {\n          console.log('继续播放现有音频');\n          this.bgAudioManager.play();\n          this.bgAudioStatus = true;\n          return;\n        } catch (e) {\n          console.error('播放现有音频失败，重新创建:', e);\n          // 播放失败则重新创建\n          this.createAudioInstance();\n        }\n      } else {\n        // 创建新的音频实例\n        this.createAudioInstance();\n      }\n    },\n    \n    // 创建音频实例 - 只有音频动态时才执行\n    createAudioInstance() {\n      if (!this.isAudioNote) return;\n      \n      try {\n        // 显示加载中提示\n        uni.showToast({\n          title: '加载音频中...',\n          icon: 'loading',\n          mask: true\n        });\n        \n        // 根据平台使用不同的音频播放方式\n        // #ifdef APP-PLUS || MP\n        // 获取全局唯一的背景音频管理器（仅小程序和APP支持）\n        this.bgAudioManager = uni.getBackgroundAudioManager();\n        \n        // 设置音频属性（这些属性是backgroundAudioManager必须设置的）\n        this.bgAudioManager.title = this.noteInfo.audio_title || '音频';\n        this.bgAudioManager.singer = this.noteInfo.user_info.nickname || '未知作者';\n        this.bgAudioManager.coverImgUrl = this.noteInfo.audio_cover || '/static/img/audio_default_cover.png';\n        \n        // 可选属性\n        this.bgAudioManager.epname = '笔记音频';\n        // #endif\n        \n        // #ifdef H5\n        // H5平台使用普通音频上下文\n        this.bgAudioManager = uni.createInnerAudioContext();\n        this.bgAudioManager.autoplay = true;\n        // #endif\n        \n        // 记录当前播放的音频ID，用于对比检查\n        this.audioPlayingId = this.noteInfo.id + '_' + Date.now();\n        const currentAudioId = this.audioPlayingId;\n        \n        // 设置事件监听\n        this.setupAudioListeners(currentAudioId);\n        \n        // 设置音频源必须放在最后，因为设置src后会自动开始播放\n        const audioUrl = this.formatAudioUrl(this.noteInfo.audio);\n        this.bgAudioManager.src = audioUrl;\n        \n      } catch (e) {\n        console.error('创建音频实例异常:', e);\n        this.handleAudioError();\n      }\n    },\n    \n    // 设置音频事件监听 - 只有音频动态时才执行\n    setupAudioListeners(currentAudioId) {\n      if (!this.isAudioNote || !this.bgAudioManager) return;\n      \n      try {\n        this.bgAudioManager.onPlay(() => {\n          // 检查是否仍是当前音频\n          if (this.audioPlayingId !== currentAudioId) return;\n          \n          uni.hideToast();\n          this.bgAudioStatus = true;\n          console.log('音频开始播放');\n        });\n        \n        this.bgAudioManager.onError((err) => {\n          // 检查是否仍是当前音频\n          if (this.audioPlayingId !== currentAudioId) return;\n          \n          console.error('音频播放错误:', err);\n          this.handleAudioError(err);\n        });\n        \n        this.bgAudioManager.onEnded(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频播放结束');\n          this.bgAudioStatus = false;\n        });\n        \n        this.bgAudioManager.onStop(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频播放停止');\n          this.bgAudioStatus = false;\n        });\n        \n        this.bgAudioManager.onPause(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频播放暂停');\n          this.bgAudioStatus = false;\n        });\n        \n        this.bgAudioManager.onWaiting(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频加载中');\n        });\n        \n        this.bgAudioManager.onCanplay(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频可以播放');\n          uni.hideToast();\n        });\n        \n      } catch (e) {\n        console.error('设置音频监听器失败:', e);\n        this.handleAudioError();\n      }\n    },\n    \n    // 处理音频错误\n    handleAudioError(err = null) {\n      if (!this.isAudioNote) return;\n      \n      uni.hideToast();\n      this.bgAudioStatus = false;\n      \n      // 根据错误码显示不同提示\n      let errorMsg = \"音频播放失败，请稍后重试\";\n      if (err && err.errCode) {\n        switch(err.errCode) {\n          case 10001: errorMsg = \"系统错误，请重启应用\"; break;\n          case 10002: errorMsg = \"网络错误，请检查网络连接\"; break;\n          case 10003: errorMsg = \"音频文件错误，请更换音频\"; break;\n          case 10004: errorMsg = \"音频格式不支持\"; break;\n          default: errorMsg = \"音频播放失败，错误码: \" + err.errCode;\n        }\n      }\n      this.opTipsPopup(errorMsg);\n      \n      // 重置音频相关状态\n      this.bgAudioManager = null;\n      this.audioPlayingId = '';\n    },\n    \n    // 检查音频状态 - 只有音频动态时才执行\n    checkAudioStatus() {\n      if (!this.isAudioNote || !this.bgAudioManager) return;\n      \n      try {\n        // 检查音频管理器状态并同步到页面状态\n        // 这里可以添加具体的状态检查逻辑\n        console.log('检查音频状态');\n      } catch (e) {\n        console.error('检查音频状态失败:', e);\n      }\n    },\n    \n    // 销毁音频实例 - 只有音频动态时才执行\n    destroyAudioInstance() {\n      if (!this.isAudioNote) {\n        console.log('非音频动态，不执行音频销毁逻辑');\n        return;\n      }\n      \n      console.log('销毁音频实例');\n      \n      if (this.bgAudioManager) {\n        try {\n          // 停止音频播放\n          if (this.bgAudioStatus) {\n            this.bgAudioManager.stop();\n          }\n          \n          // #ifdef H5\n          // H5平台需要销毁音频实例\n          if (typeof this.bgAudioManager.destroy === 'function') {\n            this.bgAudioManager.destroy();\n          }\n          // #endif\n          \n          // #ifdef MP-WEIXIN\n          // 微信小程序需要先取消监听事件再销毁\n          try {\n            if (this.bgAudioManager.offPlay) {\n              this.bgAudioManager.offPlay();\n              this.bgAudioManager.offPause();\n              this.bgAudioManager.offStop();\n              this.bgAudioManager.offEnded();\n              this.bgAudioManager.offTimeUpdate();\n              this.bgAudioManager.offWaiting();\n              this.bgAudioManager.offCanplay();\n              this.bgAudioManager.offError();\n            }\n          } catch (e) {\n            console.error('微信小程序取消音频事件监听失败:', e);\n          }\n          // #endif\n          \n          // 将引用置为null\n          this.bgAudioManager = null;\n          this.bgAudioStatus = false;\n          this.audioPlayingId = '';\n          \n          console.log('音频实例销毁完成');\n        } catch (e) {\n          console.error('处理音频实例销毁过程中出错:', e);\n          this.bgAudioManager = null;\n          this.bgAudioStatus = false;\n          this.audioPlayingId = '';\n        }\n      }\n    },\n    \n    // 音频进度变化\n    onAudioProgressChange(e) {\n      // 确保是音频类型才处理进度变化\n      if (!this.isAudioNote) {\n        console.log('非音频动态，不处理音频进度变化');\n        return;\n      }\n      \n      if (!this.bgAudioManager || !this.duration) return;\n      \n      const seekTime = (e.detail.value / 100) * this.duration;\n      this.bgAudioManager.seek(seekTime);\n    },\n    \n    // 格式化时间显示\n    formatTime(seconds) {\n      if (!seconds || isNaN(seconds)) return '00:00';\n      \n      const mins = Math.floor(seconds / 60);\n      const secs = Math.floor(seconds % 60);\n      \n      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    },\n    \n    // 格式化日期为字符串\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      \n      return `${year}-${month}-${day} ${hours}:${minutes}`;\n    },\n    \n    // 获取评论列表 - 优化版本\n    getCommentList() {\n      // 防止重复加载\n      if (this.isLoadingComments) {\n        this.debugLog('正在加载评论，跳过重复请求');\n        return;\n      }\n      \n      // 如果已经加载完所有评论，不再请求\n      if (this.loadStatus === \"no-more\" && this.page > 1) {\n        this.debugLog('已无更多评论，跳过请求');\n        return;\n      }\n      \n      this.isLoadingComments = true;\n      \n      // 第一页加载时显示加载状态，滚动加载更多时不显示全屏加载\n      if (this.page === 1) {\n        uni.showLoading({\n          title: '加载中',\n          mask: true\n        });\n      } else {\n        // 设置加载中状态\n        this.loadStatus = \"loading\";\n      }\n      \n      // 准备请求参数 - 与details.vue保持一致\n      const params = {\n        type: 0, // 动态评论类型\n        page: this.page || 1,\n        sort_type: this.cType || 0 // 0-默认，1-最新\n      };\n      \n      this.debugLog('获取评论列表', {\n        动态ID: this.noteInfo.id,\n        页码: params.page,\n        排序方式: params.sort_type === 0 ? '默认' : '最新'\n      });\n      \n      // 调用评论列表API\n      getCommentsList(this.noteInfo.id, params)\n        .then(res => {\n          if (this.page === 1) {\n            uni.hideLoading();\n          }\n          \n          this.isLoadingComments = false;\n          \n          if (res.status === 200) {\n            this.debugLog('评论列表获取成功', res.data);\n            \n            const list = res.data.list || [];\n            \n            // 更新评论总数\n            if (res.data.total !== undefined && res.data.total !== null) {\n              this.noteInfo.comments = parseInt(res.data.total) || 0;\n              this.debugLog('从API获取总评论数', this.noteInfo.comments);\n            } else if (res.data.count !== undefined && res.data.count !== null) {\n              this.noteInfo.comments = parseInt(res.data.count) || 0;\n              this.debugLog('从API获取总评论数(count字段)', this.noteInfo.comments);\n            }\n            \n            // 处理评论列表数据 - 与details.vue保持一致的字段名称\n            const processedList = list.map(item => {\n              // 确保用户信息字段存在\n              const avatar = item.avatar || \n                (item.user_info && item.user_info.avatar) || \n                '/static/img/avatar_default.png';\n                \n              const nickname = item.nickname || \n                (item.user_info && item.user_info.nickname) || \n                '用户';\n                \n              const uid = item.uid || \n                (item.user_info && item.user_info.uid) || \n                0;\n              \n              return {\n                ...item,\n                // 确保基本字段存在\n                id: item.id || 0,\n                uid: uid,\n                nickname: nickname,\n                avatar: avatar,\n                // 保存原始用户信息对象，同时确保字段一致性\n                user_info: {\n                  uid: uid,\n                  nickname: nickname,\n                  avatar: avatar\n                },\n                reply_count: parseInt(item.reply_count) || 0,\n                like_count: parseInt(item.like_count) || parseInt(item.likes) || 0,\n                likes: parseInt(item.likes) || parseInt(item.like_count) || 0,\n                is_like: !!item.is_like,\n                create_time: item.create_time || item.add_time || '',\n                content: item.content || '',\n                image: item.image || '',\n                status: item.status || 5,\n                delete_time: item.delete_time || null,\n                // 处理回复列表\n                replies: Array.isArray(item.replies) ? item.replies.map(reply => {\n                  // 确保回复的用户信息字段存在\n                  const replyAvatar = reply.avatar || \n                    (reply.user_info && reply.user_info.avatar) || \n                    '/static/img/avatar_default.png';\n                    \n                  const replyNickname = reply.nickname || \n                    (reply.user_info && reply.user_info.nickname) || \n                    '用户';\n                    \n                  const replyUid = reply.uid || \n                    (reply.user_info && reply.user_info.uid) || \n                    0;\n                  \n                  return {\n                    ...reply,\n                    id: reply.id || 0,\n                    uid: replyUid,\n                    nickname: replyNickname,\n                    avatar: replyAvatar,\n                    user_info: {\n                      uid: replyUid,\n                      nickname: replyNickname,\n                      avatar: replyAvatar\n                    },\n                    reply_uid: reply.reply_uid || reply.to_uid || 0,\n                    reply_nickname: reply.reply_nickname || reply.to_nickname || '',\n                    like_count: parseInt(reply.like_count) || parseInt(reply.likes) || 0,\n                    likes: parseInt(reply.likes) || parseInt(reply.like_count) || 0,\n                    is_like: !!reply.is_like,\n                    create_time: reply.create_time || reply.add_time || '',\n                    content: reply.content || '',\n                    image: reply.image || '',\n                    status: reply.status || 5,\n                    delete_time: reply.delete_time || null\n                  };\n                }) : [],\n                // 添加回复分页相关字段\n                replyPage: 1,\n                has_more_replies: item.reply_count > (item.replies ? item.replies.length : 0)\n              };\n            });\n            \n            // 更新评论列表\n            if (this.page === 1) {\n              // 第一页：直接替换列表\n              this.commentList = processedList;\n              // 设置是否为空状态\n              this.isEmpty = processedList.length === 0;\n            } else {\n              // 加载更多：追加到列表\n              this.commentList = [...this.commentList, ...processedList];\n            }\n            \n            // 更新分页状态\n            if (list.length < 10) { // 本页不足10条，说明没有更多了\n              this.loadStatus = \"no-more\";\n            } else {\n              this.loadStatus = \"more\";\n            }\n            \n            // 缓存第一页数据\n            if (this.page === 1) {\n              const cacheKey = `comments_${this.noteInfo.id}_${this.cType}_${this.page}`;\n              this.commentsCache[cacheKey] = {\n                list: processedList,\n                isEmpty: this.isEmpty,\n                loadStatus: this.loadStatus,\n                totalComments: this.noteInfo.comments\n              };\n            }\n            \n            // 更新回复索引映射\n            this.updateReplyIndices();\n            \n            this.debugLog('评论列表更新完成', {\n              当前页码: this.page,\n              评论总数: this.commentList.length,\n              加载状态: this.loadStatus,\n              是否为空: this.isEmpty\n            });\n          } else {\n            this.debugLog('评论列表获取失败', res);\n            this.loadStatus = \"no-more\";\n            uni.showToast({\n              title: res.msg || '获取评论失败',\n              icon: 'none'\n            });\n          }\n        })\n        .catch(err => {\n          if (this.page === 1) {\n            uni.hideLoading();\n          }\n          \n          this.isLoadingComments = false;\n          this.loadStatus = \"no-more\";\n          this.debugLog('获取评论列表异常', err);\n          uni.showToast({\n            title: '获取评论失败',\n            icon: 'none'\n          });\n        });\n    },\n    \n    // 切换评论排序\n    commentClick(type) {\n      if (!this.isThrottling || this.actionInProgress) return;\n      \n      // 如果切换到相同类型，不重复加载\n      if (this.cType === type) return;\n      \n      this.isThrottling = false;\n      this.cType = type;\n      this.page = 1;\n      this.loadStatus = \"loading\";\n      \n      // 重置评论列表\n      this.commentList = [];\n      this.isEmpty = false;\n      \n      this.getCommentList();\n      \n      // 设置延时，允许在一段时间后再次切换\n      setTimeout(() => {\n        this.isThrottling = true;\n      }, 500);\n    },\n    \n    // 打开评论框\n    openComment(e) {\n      // 如果没有事件对象，创建一个空对象模拟\n      e = e || { currentTarget: { dataset: { type: 0 } } };\n      \n      // 阻止事件冒泡\n      e.stopPropagation && e.stopPropagation();\n      \n      console.log('尝试打开评论框，用户状态:', {\n        isUser: this.isUser,\n        userId: this.userId,\n        userInfo: {\n          avatar: this.userAvatar,\n          nickname: this.userNickname\n        }\n      });\n      \n      if (!this.isUser) {\n        console.log('用户未完善资料，无法评论');\n        this.opTipsPopup(\"完善账号资料后即可评论！\")\n        setTimeout(() => {\n          uni.navigateTo({\n            url: \"/pages/center/means\"\n          })\n        }, 1000)\n        return\n      }\n      \n      let dataset = e.currentTarget.dataset || {};\n      let type = dataset.type || 0;\n      let uid = dataset.uid || 0;\n      let cid = dataset.cid || 0;\n      let name = dataset.name || \"\";\n      \n      this.cIdx = dataset.idx !== undefined ? dataset.idx : -1;\n      this.cI = dataset.i !== undefined ? dataset.i : -1;\n      \n      // 强制关闭再打开，解决偶尔无法弹出的问题\n      this.isComment = false;\n      \n      // 重置状态变量\n      this.isSubmittingComment = false;\n      \n      // 设置评论目标信息\n      if (type == 1) {\n        this.cCId = cid;\n        this.cUId = uid;\n        this.comtips = \"回复：\" + name;\n      } else {\n        this.cCId = 0;\n        this.cUId = 0;\n        this.comtips = \"说点什么...\";\n      }\n      \n      // 使用nextTick确保DOM已更新\n      this.$nextTick(() => {\n        // 先显示评论框，再延迟聚焦\n        this.isComment = true;\n        \n        // 延迟聚焦，确保键盘能弹出\n        setTimeout(() => {\n          this.isFocus = true;\n          \n          // 再次确认评论框已显示\n          if (!this.isComment) {\n            this.isComment = true;\n          }\n        }, 150);\n      });\n    },\n    \n    // 关闭评论框\n    closeComment(e) {\n      // 阻止事件冒泡\n      e && e.stopPropagation && e.stopPropagation();\n      \n      console.log('手动关闭评论框');\n      \n      // 如果正在提交评论，不关闭\n      if (this.isSubmittingComment) {\n        return;\n      }\n      \n      // 清除blur定时器\n      if (this.commentBlurTimer) {\n        clearTimeout(this.commentBlurTimer);\n        this.commentBlurTimer = null;\n      }\n      \n      // 设置为非评论操作状态\n      this.commentActioning = false;\n      \n      // 关闭评论框和焦点\n      this.isComment = false;\n      this.isFocus = false;\n      \n      // 清空输入内容\n      this.comtext = \"\";\n    },\n    \n    // 处理评论提交\n    handleCommentSubmit(commentData) {\n      if (this.isSubmittingComment) return;\n      this.isSubmittingComment = true;\n      \n      // 获取评论内容和图片\n      const content = commentData.content;\n      const image = commentData.image;\n      \n      // 如果没有内容和图片，不提交\n      if (!content && !image) {\n        this.isSubmittingComment = false;\n        return this.opTipsPopup(\"表达你的态度再评论吧！\");\n      }\n      \n      // 显示加载状态\n      uni.showLoading({\n        title: '发布中',\n        mask: true\n      });\n      \n      // 先关闭评论框，避免延迟\n      this.isComment = false;\n      this.isFocus = false;\n      this.showEmoji = false;\n      \n      // 准备评论数据\n      const params = {\n        dynamic_id: this.noteInfo.id,\n        content: content,\n        pid: this.cCId || 0,\n        to_uid: this.cUId || 0\n      };\n      \n      // 如果有图片，添加到参数\n      if (image) {\n        params.image = image;\n      }\n      \n      // 提交评论\n      addComment(params)\n        .then(res => {\n          uni.hideLoading();\n          \n          if (res.status === 200) {\n            // 处理评论数据\n            const commentData = res.data || this.createDefaultCommentData(content, image);\n            \n            // 更新评论计数和列表\n            this.processCommentSuccess(commentData);\n            \n            // 显示成功提示\n            this.opTipsPopup('评论成功');\n            \n            // 如果评论列表为空，刷新页面\n            if (this.isEmpty) {\n              this.isEmpty = false;\n            }\n          } else {\n            this.opTipsPopup(res.msg || '评论失败');\n          }\n        })\n        .catch(err => {\n          uni.hideLoading();\n          this.opTipsPopup('评论失败，请重试');\n          console.error('评论请求异常', err);\n        })\n        .finally(() => {\n          // 重置状态\n          this.isSubmittingComment = false;\n          \n          // 重置评论目标信息\n          this.cCId = 0;\n          this.cUId = 0;\n          this.comtips = \"说点什么...\";\n          this.comtext = \"\";\n        });\n    },\n    \n    // 创建默认评论数据（当API返回为空时）\n    createDefaultCommentData(content, imageUrl) {\n      // 生成更安全的临时ID，避免冲突\n      const tempId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n      \n      // 获取用户地理位置信息（如果有的话）\n      const userProvince = this.noteInfo?.province || this.$store.state.app?.userInfo?.province || '';\n      \n      // 创建完整的评论数据结构\n      const commentData = {\n        id: tempId, // 使用更安全的临时ID\n        uid: this.userId,\n        nickname: this.userNickname || '用户',\n        avatar: this.userAvatar || '/static/img/avatar_default.png',\n        content: content || '',\n        image: imageUrl || '', // 评论图片URL\n        create_time: this.formatDate(new Date()),\n        likes: 0,\n        like_count: 0, // 兼容不同字段名\n        is_like: false,\n        status: 5, // 正常状态：5\n        province: userProvince,\n        delete_time: null, // 删除时间，用于标记是否被删除\n        replies: [], // 初始化回复数组\n        reply_count: 0, // 回复数量\n        has_more_replies: false, // 是否有更多回复\n        replyPage: 1, // 回复页码\n        loading_replies: false // 是否正在加载回复\n      };\n      \n      console.log('创建默认评论数据:', commentData);\n      return commentData;\n    },\n    \n    // 处理评论成功\n    processCommentSuccess(commentData) {\n      console.log(\"处理评论成功\", {\n        commentData,\n        cIdx: this.cIdx,\n        cI: this.cI,\n        noteInfoComments: this.noteInfo.comments\n      });\n      \n      // 确保commentData存在\n      if (!commentData) {\n        console.error(\"评论数据为空，无法处理\");\n        return;\n      }\n      \n      // 增加评论总数\n      this.noteInfo.comments = (this.noteInfo.comments || 0) + 1;\n      \n      // 处理回复评论\n      if (this.cIdx >= 0) {\n        console.log(\"处理回复评论\", this.cIdx);\n        \n        if (!this.commentList[this.cIdx].replies) {\n          this.commentList[this.cIdx].replies = [];\n        }\n        if (this.commentList[this.cIdx].reply_count === undefined) {\n          this.commentList[this.cIdx].reply_count = 0;\n        }\n        \n        // 设置回复目标信息\n        if (this.cUId) {\n          // 获取被回复用户的昵称\n          const nickname = this.comtips.replace(\"回复：\", \"\");\n          commentData.reply_uid = this.cUId;\n          commentData.reply_nickname = nickname;\n        }\n        \n        // 添加回复\n        this.commentList[this.cIdx].replies.push(commentData);\n        this.commentList[this.cIdx].reply_count++;\n        \n        // 更新回复索引映射\n        this.replyIndices.set(commentData.id, this.commentList[this.cIdx].replies.length - 1);\n        \n        // 强制更新视图\n        this.$forceUpdate();\n        \n        console.log(\"回复评论处理完成\", {\n          回复数量: this.commentList[this.cIdx].reply_count,\n          回复列表: this.commentList[this.cIdx].replies.length\n        });\n      }\n      // 处理新评论\n      else {\n        console.log(\"处理新评论\");\n        \n        if (this.isEmpty || this.commentList.length === 0) {\n          this.isEmpty = false;\n          this.commentList = [];\n          console.log(\"重置评论列表\");\n        }\n        \n        // 初始化回复数据\n        commentData.replies = [];\n        commentData.reply_count = 0;\n        \n        // 添加到列表头部\n        this.commentList.unshift(commentData);\n        \n        // 强制更新视图\n        this.$forceUpdate();\n        \n        console.log(\"新评论处理完成\", {\n          评论列表长度: this.commentList.length,\n          评论总数: this.noteInfo.comments\n        });\n      }\n    },\n    \n    // 删除评论\n    delComment(e) {\n      let self = this;\n      let idx = e.currentTarget.dataset.idx;\n      let i = e.currentTarget.dataset.i;\n      let commentId = e.currentTarget.dataset.id;\n      \n      // 防止重复点击\n      if (self.isDeletingComment) return;\n      \n      uni.showModal({\n        content: \"确定要永久删除该评论吗？\",\n        confirmColor: \"#FA5150\",\n        success: function(res) {\n          if (res.confirm) {\n            self.isDeletingComment = true;\n            \n            uni.showLoading({\n              title: '删除中',\n              mask: true\n            });\n            \n            // 使用新的删除评论API接口\n            deleteComment(commentId).then(res => {\n              uni.hideLoading();\n              self.isDeletingComment = false;\n              \n              if (res.status === 200) {\n                // 减少评论总数\n                if (self.noteInfo.comments > 0) {\n                  self.noteInfo.comments--;\n                }\n                \n                // 更新评论状态\n                if (i == -1) {\n                  // 主评论被删除\n                  self.commentList[idx].delete_time = new Date().toISOString(); // 设置删除时间\n                  self.commentList[idx].status = 0; // 设置删除状态\n                } else {\n                  // 确保replies数组存在\n                  if (self.commentList[idx].replies && self.commentList[idx].replies.length > i) {\n                    self.commentList[idx].replies[i].delete_time = new Date().toISOString(); // 设置删除时间\n                    self.commentList[idx].replies[i].status = 0; // 设置删除状态\n                    \n                    // 子评论被删除，也要减少对应的计数\n                    if (self.commentList[idx].reply_count > 0) {\n                      // 减少回复计数\n                      self.commentList[idx].reply_count--;\n                    }\n                  }\n                }\n                self.opTipsPopup(\"删除成功\");\n              } else {\n                self.opTipsPopup(res.msg || \"删除失败\");\n              }\n            }).catch(err => {\n              uni.hideLoading();\n              self.isDeletingComment = false;\n              self.opTipsPopup(\"删除失败\");\n            });\n          }\n        }\n      });\n    },\n    \n    // 点赞/取消点赞评论\n    toggleCommentLike(commentId, isCurrentlyLiked) {\n      if (!this.isUser) {\n        this.opTipsPopup(\"请先完善账号资料\");\n        return;\n      }\n      \n      // 防止频繁点击\n      if (this.likeThrottling) return;\n      this.likeThrottling = true;\n      \n      // 500ms后恢复\n      setTimeout(() => {\n        this.likeThrottling = false;\n      }, 500);\n      \n      // 将布尔值转换为0/1格式\n      const currentLikeState = isCurrentlyLiked ? 1 : 0;\n      const newLikeState = currentLikeState ? 0 : 1;\n      \n      // 优先更新UI状态，让用户感觉响应迅速\n      // 遍历更新评论列表中的点赞状态\n      for (let i = 0; i < this.commentList.length; i++) {\n        const comment = this.commentList[i];\n        \n        // 更新主评论\n        if (comment.id === commentId) {\n          comment.is_like = newLikeState;\n          if (newLikeState) {\n            // 处理不同的字段名称\n            if (comment.like_count !== undefined) {\n              comment.like_count = (comment.like_count || 0) + 1;\n            }\n            if (comment.likes !== undefined) {\n              comment.likes = (comment.likes || 0) + 1;\n            }\n          } else {\n            // 处理不同的字段名称\n            if (comment.like_count !== undefined && comment.like_count > 0) {\n              comment.like_count--;\n            }\n            if (comment.likes !== undefined && comment.likes > 0) {\n              comment.likes--;\n            }\n          }\n          break; // 找到了主评论，可以跳出循环\n        }\n        \n        // 更新回复评论\n        if (comment.replies && comment.replies.length > 0) {\n          for (let j = 0; j < comment.replies.length; j++) {\n            const reply = comment.replies[j];\n            if (reply.id === commentId) {\n              reply.is_like = newLikeState;\n              if (newLikeState) {\n                // 处理不同的字段名称\n                if (reply.like_count !== undefined) {\n                  reply.like_count = (reply.like_count || 0) + 1;\n                }\n                if (reply.likes !== undefined) {\n                  reply.likes = (reply.likes || 0) + 1;\n                }\n              } else {\n                // 处理不同的字段名称\n                if (reply.like_count !== undefined && reply.like_count > 0) {\n                  reply.like_count--;\n                }\n                if (reply.likes !== undefined && reply.likes > 0) {\n                  reply.likes--;\n                }\n              }\n              break; // 找到了回复，可以跳出内部循环\n            }\n          }\n        }\n      }\n      \n      // 后台执行API调用\n      if (currentLikeState) {\n        // 如果当前已点赞，则取消点赞\n        unlikeComment(commentId)\n          .then(res => {\n            // 点赞成功，不需要操作\n          })\n          .catch(err => {\n            // 恢复状态\n            this.restoreCommentLikeStatus(commentId, currentLikeState);\n            this.opTipsPopup(\"操作失败，请重试\");\n          });\n      } else {\n        // 如果当前未点赞，则点赞\n        likeComment(commentId)\n          .then(res => {\n            // 点赞成功，不需要操作\n          })\n          .catch(err => {\n            // 恢复状态\n            this.restoreCommentLikeStatus(commentId, currentLikeState);\n            this.opTipsPopup(\"操作失败，请重试\");\n          });\n      }\n    },\n    \n    // 恢复评论点赞状态（操作失败时）\n    restoreCommentLikeStatus(commentId, originalLikeState) {\n      // 操作失败时恢复状态\n      for (let i = 0; i < this.commentList.length; i++) {\n        const comment = this.commentList[i];\n        \n        // 还原主评论\n        if (comment.id === commentId) {\n          comment.is_like = originalLikeState;\n          if (originalLikeState === 1) {\n            // 处理不同的字段名称\n            if (comment.like_count !== undefined && comment.like_count > 0) {\n              comment.like_count--;\n            }\n            if (comment.likes !== undefined && comment.likes > 0) {\n              comment.likes--;\n            }\n          } else {\n            // 处理不同的字段名称\n            if (comment.like_count !== undefined) {\n              comment.like_count = (comment.like_count || 0) + 1;\n            }\n            if (comment.likes !== undefined) {\n              comment.likes = (comment.likes || 0) + 1;\n            }\n          }\n          break; // 找到后跳出循环\n        }\n        \n        // 还原回复评论\n        if (comment.replies && comment.replies.length > 0) {\n          for (let j = 0; j < comment.replies.length; j++) {\n            const reply = comment.replies[j];\n            if (reply.id === commentId) {\n              reply.is_like = originalLikeState;\n              if (originalLikeState === 1) {\n                // 处理不同的字段名称\n                if (reply.like_count !== undefined && reply.like_count > 0) {\n                  reply.like_count--;\n                }\n                if (reply.likes !== undefined && reply.likes > 0) {\n                  reply.likes--;\n                }\n              } else {\n                // 处理不同的字段名称\n                if (reply.like_count !== undefined) {\n                  reply.like_count = (reply.like_count || 0) + 1;\n                }\n                if (reply.likes !== undefined) {\n                  reply.likes = (reply.likes || 0) + 1;\n                }\n              }\n              break; // 找到后跳出循环\n            }\n          }\n        }\n      }\n    },\n    \n    // 递归更新回复索引映射\n    updateReplyIndices() {\n      if (!this.commentList || !this.commentList.length) return;\n      \n      this.replyIndices = new Map();\n      \n      this.commentList.forEach((comment, commentIndex) => {\n        if (comment.replies && comment.replies.length) {\n          comment.replies.forEach((reply, replyIndex) => {\n            this.replyIndices.set(reply.id, replyIndex);\n          });\n        }\n      });\n      \n      this.debugLog('更新回复索引映射完成', {\n        索引数量: this.replyIndices.size\n      });\n    },\n    \n    // 加载评论回复 - 与details.vue保持一致\n    sonComment(e) {\n      let id = parseInt(e.currentTarget.dataset.id) || 0;\n      let idx = parseInt(e.currentTarget.dataset.idx) || 0;\n      \n      this.debugLog('加载评论回复:', {\n        commentId: id,\n        commentIndex: idx\n      });\n      \n      // 防止重复点击\n      if (this.isLoadingReplies) return;\n      this.isLoadingReplies = true;\n      \n      // 显示加载中状态\n      const commentItem = this.commentList[idx];\n      if (commentItem) {\n        this.$set(commentItem, 'loading_replies', true);\n      }\n      \n      // 获取当前评论的回复页码\n      const currentPage = parseInt(commentItem.replyPage) || 1;\n      \n      // 生成缓存键\n      const cacheKey = `replies_${id}_${currentPage}`;\n      \n      // 检查缓存\n      if (this.commentCache && this.commentCache[cacheKey]) {\n        this.debugLog('使用缓存中的回复数据');\n        this.handleAllRepliesData(this.commentCache[cacheKey], idx, currentPage);\n        return;\n      }\n      \n      // 请求参数 - 与details.vue保持一致\n      const params = {\n        parent_id: id,           // 父评论ID\n        page: currentPage,       // 页码\n        limit: 10,               // 每页数量\n        sort_type: 1             // 排序类型：1=最新(创建时间)\n      };\n      \n      this.debugLog('回复请求参数:', params);\n      \n      // 发起请求\n      getCommentReplies(params)\n        .then(res => {\n          if (res.status === 200) {\n            this.debugLog('获取到回复数据:', res.data);\n            \n            // 缓存结果\n            if (!this.commentCache) this.commentCache = {};\n            this.commentCache[cacheKey] = res.data;\n            \n            // 处理回复数据\n            this.handleAllRepliesData(res.data, idx, currentPage);\n          } else {\n            // 移除加载状态\n            if (commentItem) {\n              this.$set(commentItem, 'loading_replies', false);\n            }\n            this.isLoadingReplies = false;\n            \n            uni.showToast({\n              title: res.msg || '获取回复失败',\n              icon: 'none'\n            });\n          }\n        })\n        .catch(err => {\n          this.debugLog('获取回复失败:', err);\n          \n          // 移除加载状态\n          if (commentItem) {\n            this.$set(commentItem, 'loading_replies', false);\n          }\n          this.isLoadingReplies = false;\n          \n          uni.showToast({\n            title: '获取回复失败',\n            icon: 'none'\n          });\n        });\n    },\n    \n    // 处理回复数据（分页方式）- 与details.vue保持一致\n    handleAllRepliesData(data, idx, page) {\n      if (this.commentList[idx]) {\n        const commentItem = this.commentList[idx];\n        \n        // 处理不同的数据结构：有些API返回data.list，有些直接返回data作为列表\n        let replies = [];\n        if (data.list && Array.isArray(data.list)) {\n          replies = data.list;\n        } else if (Array.isArray(data)) {\n          replies = data;\n        } else if (data.data && Array.isArray(data.data)) {\n          replies = data.data;\n        } else {\n          this.debugLog('无法识别的回复数据结构', data);\n          replies = [];\n        }\n        \n        this.debugLog('获取到回复数据:', replies);\n        \n        // 标准化回复数据\n        replies = replies.map(reply => {\n          // 确保回复的用户信息字段存在\n          const replyAvatar = reply.avatar || \n            (reply.user_info && reply.user_info.avatar) || \n            '/static/img/avatar_default.png';\n            \n          const replyNickname = reply.nickname || \n            (reply.user_info && reply.user_info.nickname) || \n            '用户';\n            \n          const replyUid = reply.uid || \n            (reply.user_info && reply.user_info.uid) || \n            0;\n          \n          return {\n            ...reply,\n            id: reply.id || 0,\n            uid: replyUid,\n            nickname: replyNickname,\n            avatar: replyAvatar,\n            user_info: {\n              uid: replyUid,\n              nickname: replyNickname,\n              avatar: replyAvatar\n            },\n            reply_uid: reply.reply_uid || reply.to_uid || 0,\n            reply_nickname: reply.reply_nickname || reply.to_nickname || '',\n            like_count: parseInt(reply.like_count) || parseInt(reply.likes) || 0,\n            likes: parseInt(reply.likes) || parseInt(reply.like_count) || 0,\n            is_like: !!reply.is_like,\n            create_time: reply.create_time || reply.add_time || '',\n            content: reply.content || '',\n            image: reply.image || '',\n            status: reply.status || 5,\n            delete_time: reply.delete_time || null\n          };\n        });\n        \n        // 如果是第一页，直接替换现有回复列表\n        if (page === 1) {\n          this.$set(commentItem, 'replies', replies);\n        } else {\n          // 否则追加到现有列表\n          // 避免重复添加已加载的回复\n          const existingIds = (commentItem.replies || []).map(r => r.id);\n          const newReplies = replies.filter(r => !existingIds.includes(r.id));\n          \n          this.$set(commentItem, 'replies', [...(commentItem.replies || []), ...newReplies]);\n        }\n        \n        // 更新回复页码\n        this.$set(commentItem, 'replyPage', page + 1);\n        \n        // 获取回复总数\n        let replyCount = 0;\n        if (data.count !== undefined) {\n          replyCount = parseInt(data.count) || 0;\n        } else if (data.total !== undefined) {\n          replyCount = parseInt(data.total) || 0;\n        } else {\n          replyCount = commentItem.reply_count || 0;\n        }\n        \n        // 标记是否有更多回复\n        // 如果已加载的回复数量等于或超过总回复数，设置为无更多回复\n        const currentLoadedCount = commentItem.replies ? commentItem.replies.length : 0;\n        \n        // 判断是否已加载全部回复\n        const hasNoMoreReplies = replies.length < 10 || currentLoadedCount >= replyCount;\n        this.$set(commentItem, 'has_more_replies', !hasNoMoreReplies);\n        \n        // 更新总回复数\n        this.$set(commentItem, 'reply_count', Math.max(replyCount, currentLoadedCount));\n        \n        // 更新回复索引映射\n        this.updateReplyIndices();\n        \n        // 移除加载状态\n        this.$set(commentItem, 'loading_replies', false);\n        this.isLoadingReplies = false;\n      }\n    },\n    \n    // 评论加载更多 - 优化版本\n    commentReachBottom() {\n      // 防止重复触发\n      if (this.isLoadingComments) {\n        this.debugLog('正在加载评论，跳过触底加载');\n        return;\n      }\n      \n      if (!this.isEmpty && this.commentList.length && this.loadStatus !== \"no-more\") {\n        this.debugLog('触底加载更多评论', {\n          当前页码: this.page,\n          评论总数: this.commentList.length\n        });\n        \n        this.page = this.page + 1;\n        this.loadStatus = \"loading\";\n        this.getCommentList();\n      } else {\n        this.debugLog('无更多评论可加载', {\n          isEmpty: this.isEmpty,\n          评论数量: this.commentList.length,\n          loadStatus: this.loadStatus\n        });\n      }\n    },\n    \n    // 优化页面滚动事件处理\n    handlePageScroll(e) {\n      // 保存滚动位置以优化性能\n      if (!e || !this.isPageActive) return; // 确保有事件对象且页面活跃\n      \n      const scrollTop = e.scrollTop;\n      const direction = scrollTop > this.lastScrollTop ? 'down' : 'up';\n      this.lastScrollTop = scrollTop;\n      \n      // 只在滚动方向为向下且不在加载状态时预加载评论\n      if (direction === 'down' && !this.actionInProgress && \n          scrollTop > 300 && this.loadStatus === 'more') {\n        this.preloadComments();\n      }\n    },\n    \n    // 预加载评论 - 性能优化\n    preloadComments() {\n      // 使用防抖减少请求频率\n      if (this.debounceTimer) clearTimeout(this.debounceTimer);\n      \n      this.debounceTimer = setTimeout(() => {\n        // 判断是否需要加载更多\n        if (this.page > 1 && !this.commentCache[this.page + 1]) {\n          // 预加载下一页评论但不显示\n          this.fetchCommentsForPage(this.page + 1, true);\n        }\n      }, 300);\n    },\n    \n    // 处理音频URL\n    formatAudioUrl(url) {\n      if (!url) return '';\n      \n      // 如果URL不是以http开头，尝试添加协议\n      if (!url.startsWith('http')) {\n        // 如果以//开头，添加https:\n        if (url.startsWith('//')) {\n          return 'https:' + url;\n        }\n        // 如果以/开头，可能是相对路径，尝试添加完整域名\n        if (url.startsWith('/')) {\n          return 'https://yourdomain.com' + url; // 替换为实际的域名\n        }\n        // 其他情况，假设是相对路径\n        return 'https://yourdomain.com/' + url; // 替换为实际的域名\n      }\n      \n      return url;\n    },\n    \n    // 音频播放 - 只有音频动态时才执行\n    audioBgClick() {\n      // 确保是音频类型\n      if (!this.isAudioNote) {\n        console.log('非音频动态，不执行音频播放逻辑');\n        return;\n      }\n      \n      try {\n        // 处理音频播放状态\n        if (this.bgAudioStatus) {\n          // 当前正在播放，需要暂停\n          this.pauseAudio();\n        } else {\n          // 当前已暂停，需要播放\n          this.playAudio();\n        }\n      } catch (e) {\n        console.error('音频控制异常:', e);\n        this.handleAudioError();\n      }\n    },\n    \n    // 暂停音频\n    pauseAudio() {\n      if (!this.isAudioNote || !this.bgAudioManager) return;\n      \n      try {\n        this.bgAudioManager.pause();\n        this.bgAudioStatus = false;\n        console.log('音频已暂停');\n      } catch (e) {\n        console.error('暂停音频失败:', e);\n        this.handleAudioError();\n      }\n    },\n    \n    // 播放音频 - 只有音频动态时才执行\n    playAudio() {\n      // 确保是音频类型才执行播放逻辑\n      if (!this.isAudioNote) {\n        console.log('非音频动态，不执行音频播放逻辑');\n        return;\n      }\n      \n      // 检查音频URL是否存在\n      if (!this.noteInfo.audio) {\n        return this.opTipsPopup(\"音频资源不可用\");\n      }\n      \n      // 如果已有实例，尝试继续播放\n      if (this.bgAudioManager) {\n        try {\n          console.log('继续播放现有音频');\n          this.bgAudioManager.play();\n          this.bgAudioStatus = true;\n          return;\n        } catch (e) {\n          console.error('播放现有音频失败，重新创建:', e);\n          // 播放失败则重新创建\n          this.createAudioInstance();\n        }\n      } else {\n        // 创建新的音频实例\n        this.createAudioInstance();\n      }\n    },\n    \n    // 创建音频实例 - 只有音频动态时才执行\n    createAudioInstance() {\n      if (!this.isAudioNote) return;\n      \n      try {\n        // 显示加载中提示\n        uni.showToast({\n          title: '加载音频中...',\n          icon: 'loading',\n          mask: true\n        });\n        \n        // 根据平台使用不同的音频播放方式\n        // #ifdef APP-PLUS || MP\n        // 获取全局唯一的背景音频管理器（仅小程序和APP支持）\n        this.bgAudioManager = uni.getBackgroundAudioManager();\n        \n        // 设置音频属性（这些属性是backgroundAudioManager必须设置的）\n        this.bgAudioManager.title = this.noteInfo.audio_title || '音频';\n        this.bgAudioManager.singer = this.noteInfo.user_info.nickname || '未知作者';\n        this.bgAudioManager.coverImgUrl = this.noteInfo.audio_cover || '/static/img/audio_default_cover.png';\n        \n        // 可选属性\n        this.bgAudioManager.epname = '笔记音频';\n        // #endif\n        \n        // #ifdef H5\n        // H5平台使用普通音频上下文\n        this.bgAudioManager = uni.createInnerAudioContext();\n        this.bgAudioManager.autoplay = true;\n        // #endif\n        \n        // 记录当前播放的音频ID，用于对比检查\n        this.audioPlayingId = this.noteInfo.id + '_' + Date.now();\n        const currentAudioId = this.audioPlayingId;\n        \n        // 设置事件监听\n        this.setupAudioListeners(currentAudioId);\n        \n        // 设置音频源必须放在最后，因为设置src后会自动开始播放\n        const audioUrl = this.formatAudioUrl(this.noteInfo.audio);\n        this.bgAudioManager.src = audioUrl;\n        \n      } catch (e) {\n        console.error('创建音频实例异常:', e);\n        this.handleAudioError();\n      }\n    },\n    \n    // 设置音频事件监听 - 只有音频动态时才执行\n    setupAudioListeners(currentAudioId) {\n      if (!this.isAudioNote || !this.bgAudioManager) return;\n      \n      try {\n        this.bgAudioManager.onPlay(() => {\n          // 检查是否仍是当前音频\n          if (this.audioPlayingId !== currentAudioId) return;\n          \n          uni.hideToast();\n          this.bgAudioStatus = true;\n          console.log('音频开始播放');\n        });\n        \n        this.bgAudioManager.onError((err) => {\n          // 检查是否仍是当前音频\n          if (this.audioPlayingId !== currentAudioId) return;\n          \n          console.error('音频播放错误:', err);\n          this.handleAudioError(err);\n        });\n        \n        this.bgAudioManager.onEnded(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频播放结束');\n          this.bgAudioStatus = false;\n        });\n        \n        this.bgAudioManager.onStop(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频播放停止');\n          this.bgAudioStatus = false;\n        });\n        \n        this.bgAudioManager.onPause(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频播放暂停');\n          this.bgAudioStatus = false;\n        });\n        \n        this.bgAudioManager.onWaiting(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频加载中');\n        });\n        \n        this.bgAudioManager.onCanplay(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频可以播放');\n          uni.hideToast();\n        });\n        \n      } catch (e) {\n        console.error('设置音频监听器失败:', e);\n        this.handleAudioError();\n      }\n    },\n    \n    // 处理音频错误\n    handleAudioError(err = null) {\n      if (!this.isAudioNote) return;\n      \n      uni.hideToast();\n      this.bgAudioStatus = false;\n      \n      // 根据错误码显示不同提示\n      let errorMsg = \"音频播放失败，请稍后重试\";\n      if (err && err.errCode) {\n        switch(err.errCode) {\n          case 10001: errorMsg = \"系统错误，请重启应用\"; break;\n          case 10002: errorMsg = \"网络错误，请检查网络连接\"; break;\n          case 10003: errorMsg = \"音频文件错误，请更换音频\"; break;\n          case 10004: errorMsg = \"音频格式不支持\"; break;\n          default: errorMsg = \"音频播放失败，错误码: \" + err.errCode;\n        }\n      }\n      this.opTipsPopup(errorMsg);\n      \n      // 重置音频相关状态\n      this.bgAudioManager = null;\n      this.audioPlayingId = '';\n    },\n    \n    // 检查音频状态 - 只有音频动态时才执行\n    checkAudioStatus() {\n      if (!this.isAudioNote || !this.bgAudioManager) return;\n      \n      try {\n        // 检查音频管理器状态并同步到页面状态\n        // 这里可以添加具体的状态检查逻辑\n        console.log('检查音频状态');\n      } catch (e) {\n        console.error('检查音频状态失败:', e);\n      }\n    },\n    \n    // 销毁音频实例 - 只有音频动态时才执行\n    destroyAudioInstance() {\n      if (!this.isAudioNote) {\n        console.log('非音频动态，不执行音频销毁逻辑');\n        return;\n      }\n      \n      console.log('销毁音频实例');\n      \n      if (this.bgAudioManager) {\n        try {\n          // 停止音频播放\n          if (this.bgAudioStatus) {\n            this.bgAudioManager.stop();\n          }\n          \n          // #ifdef H5\n          // H5平台需要销毁音频实例\n          if (typeof this.bgAudioManager.destroy === 'function') {\n            this.bgAudioManager.destroy();\n          }\n          // #endif\n          \n          // #ifdef MP-WEIXIN\n          // 微信小程序需要先取消监听事件再销毁\n          try {\n            if (this.bgAudioManager.offPlay) {\n              this.bgAudioManager.offPlay();\n              this.bgAudioManager.offPause();\n              this.bgAudioManager.offStop();\n              this.bgAudioManager.offEnded();\n              this.bgAudioManager.offTimeUpdate();\n              this.bgAudioManager.offWaiting();\n              this.bgAudioManager.offCanplay();\n              this.bgAudioManager.offError();\n            }\n          } catch (e) {\n            console.error('微信小程序取消音频事件监听失败:', e);\n          }\n          // #endif\n          \n          // 将引用置为null\n          this.bgAudioManager = null;\n          this.bgAudioStatus = false;\n          this.audioPlayingId = '';\n          \n          console.log('音频实例销毁完成');\n        } catch (e) {\n          console.error('处理音频实例销毁过程中出错:', e);\n          this.bgAudioManager = null;\n          this.bgAudioStatus = false;\n          this.audioPlayingId = '';\n        }\n      }\n    },\n    \n    // 音频进度变化\n    onAudioProgressChange(e) {\n      // 确保是音频类型才处理进度变化\n      if (!this.isAudioNote) {\n        console.log('非音频动态，不处理音频进度变化');\n        return;\n      }\n      \n      if (!this.bgAudioManager || !this.duration) return;\n      \n      const seekTime = (e.detail.value / 100) * this.duration;\n      this.bgAudioManager.seek(seekTime);\n    },\n    \n    // 格式化时间显示\n    formatTime(seconds) {\n      if (!seconds || isNaN(seconds)) return '00:00';\n      \n      const mins = Math.floor(seconds / 60);\n      const secs = Math.floor(seconds % 60);\n      \n      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    },\n    \n    // 优化后的表情内容解析\n    parseEmojiContent(text) {\n      if (!text || typeof text !== 'string') return [];\n      \n      // 检查缓存\n      const cacheKey = text;\n      if (this.parsedContentCache.has(cacheKey)) {\n        return this.parsedContentCache.get(cacheKey);\n      }\n      \n      const nodes = [];\n      let lastIndex = 0;\n      \n      // 优化后的正则表达式，更精确匹配表情格式\n      const regex = /\\[([^\\[\\]]{1,10})\\]/g;\n      let match;\n      \n      try {\n        while ((match = regex.exec(text)) !== null) {\n          // 添加表情前的文本\n          if (match.index > lastIndex) {\n            const textContent = text.substring(lastIndex, match.index);\n            if (textContent) {\n              nodes.push({\n                type: 'text',\n                text: textContent\n              });\n            }\n          }\n          \n          // 使用Map快速查找表情\n          const emojiPhrase = match[0];\n          const emoji = this.emojiMap.get(emojiPhrase);\n          \n          if (emoji && emoji.url) {\n            // 添加表情图片，强制固定尺寸\n            nodes.push({\n              type: 'image',\n              attrs: {\n                src: emoji.url,\n                class: 'emoji-img',\n                style: 'width: 32rpx !important; height: 32rpx !important; max-width: 32rpx !important; max-height: 32rpx !important; min-width: 32rpx !important; min-height: 32rpx !important; vertical-align: middle; margin: 0 4rpx; display: inline-block; border-radius: 4rpx; object-fit: cover;',\n                'data-emoji': emojiPhrase,\n                'data-url': emoji.url\n              }\n            });\n          } else {\n            // 如果没找到对应表情，保留原文本\n            nodes.push({\n              type: 'text',\n              text: emojiPhrase\n            });\n          }\n          \n          lastIndex = regex.lastIndex;\n        }\n        \n        // 添加剩余的文本\n        if (lastIndex < text.length) {\n          const remainingText = text.substring(lastIndex);\n          if (remainingText) {\n            nodes.push({\n              type: 'text',\n              text: remainingText\n            });\n          }\n        }\n        \n        // 缓存解析结果，控制缓存大小\n        this.cacheEmojiParseResult(cacheKey, nodes);\n        \n        return nodes;\n      } catch (error) {\n        console.error('解析表情内容出错:', error);\n        // 出错时返回纯文本\n        return [{\n          type: 'text',\n          text: text\n        }];\n      }\n    },\n    \n    // 缓存表情解析结果\n    cacheEmojiParseResult(cacheKey, nodes) {\n      if (this.parsedContentCache.size >= this.maxCacheSize) {\n        // 删除最旧的缓存项\n        const firstKey = this.parsedContentCache.keys().next().value;\n        this.parsedContentCache.delete(firstKey);\n      }\n      this.parsedContentCache.set(cacheKey, nodes);\n    },\n    \n    // 优化后的表情内容解析（用于rich-text组件）\n    parseEmojiContentForRichText(text) {\n      if (!text || typeof text !== 'string') return text;\n      \n      // 检查缓存\n      const cacheKey = `richtext_${text}`;\n      if (this.parsedContentCache.has(cacheKey)) {\n        return this.parsedContentCache.get(cacheKey);\n      }\n      \n      try {\n        // 直接替换文本中的表情标记为HTML图片标签\n        let processedText = text.replace(/\\[([^\\[\\]]{1,10})\\]/g, (match) => {\n          const emoji = sinaEmoji.find(e => e.phrase === match);\n          if (emoji && emoji.url) {\n            return `<img src=\"${emoji.url}\" class=\"emoji-img-inline\" style=\"width: 32rpx !important; height: 32rpx !important; max-width: 32rpx !important; max-height: 32rpx !important; min-width: 32rpx !important; min-height: 32rpx !important; vertical-align: middle; margin: 0 4rpx; display: inline-block; border-radius: 4rpx; object-fit: cover;\" data-emoji=\"${match}\" />`;\n          }\n          return match;\n        });\n        \n        // 缓存结果\n        if (this.parsedContentCache.size >= this.maxCacheSize) {\n          const firstKey = this.parsedContentCache.keys().next().value;\n          this.parsedContentCache.delete(firstKey);\n        }\n        this.parsedContentCache.set(cacheKey, processedText);\n        \n        return processedText;\n      } catch (error) {\n        console.error('解析表情内容出错:', error);\n        return text;\n      }\n    },\n    \n    // 对回复按时间正序排序（从早到晚）\n    sortRepliesByTime(replies) {\n      if (!replies || !Array.isArray(replies)) return [];\n      \n      // 创建副本以避免修改原始数组\n      const sortedReplies = [...replies];\n      \n      // 按创建时间排序（早->晚）\n      return sortedReplies.sort((a, b) => {\n        // 处理不同的时间格式\n        // 后端改为datetime格式，不需要替换-为/\n        const timeA = new Date(a.create_time);\n        const timeB = new Date(b.create_time);\n        \n        return timeA - timeB; // 升序（从早到晚）\n      });\n    },\n    \n    // 获取回复在原始数组中的索引\n    getReplyIndex(replies, replyId) {\n      if (!replies || !Array.isArray(replies)) return -1;\n      \n      for (let i = 0; i < replies.length; i++) {\n        if (replies[i].id === replyId) {\n          return i;\n        }\n      }\n      \n      return -1;\n    },\n    \n    // 表情点击预览（可选功能）\n    onEmojiClick(event) {\n      // 防抖处理\n      if (this.emojiClickTimer) {\n        clearTimeout(this.emojiClickTimer);\n      }\n      \n      this.emojiClickTimer = setTimeout(() => {\n        const target = event.target || event.currentTarget;\n        const emojiPhrase = target.getAttribute('data-emoji');\n        const emojiUrl = target.getAttribute('data-url');\n        \n        if (emojiPhrase && emojiUrl) {\n          // 显示表情预览或复制功能\n          this.showEmojiPreview(emojiPhrase, emojiUrl);\n        }\n      }, 300);\n    },\n    \n    // 显示表情预览\n    showEmojiPreview(phrase, url) {\n      this.previewEmojiData = {\n        phrase,\n        url,\n        timestamp: Date.now()\n      };\n      \n      // 可以实现长按复制表情等功能\n      uni.showActionSheet({\n        itemList: ['复制表情', '查看大图'],\n        success: (res) => {\n          if (res.tapIndex === 0) {\n            // 复制表情文字\n            uni.setClipboardData({\n              data: phrase,\n              success: () => {\n                uni.showToast({\n                  title: '表情已复制',\n                  icon: 'success'\n                });\n              }\n            });\n          } else if (res.tapIndex === 1) {\n            // 预览表情大图\n            uni.previewImage({\n              urls: [url],\n              current: url\n            });\n          }\n        }\n      });\n    },\n    \n    // 清理表情缓存\n    clearEmojiCache() {\n      this.parsedContentCache.clear();\n      console.log('表情缓存已清理');\n    },\n    \n    // 获取表情缓存统计\n    getEmojiCacheStats() {\n      return {\n        emojiMapSize: this.emojiMap.size,\n        parsedContentCacheSize: this.parsedContentCache.size,\n        maxCacheSize: this.maxCacheSize\n      };\n    },\n    \n    // 动态详情获取\n    dynamicDetails() {\n      // 防止重复加载\n      if (this.isLoadingDetail) {\n        this.debugLog('正在加载详情，跳过重复请求');\n        return;\n      }\n      \n      this.isLoadingDetail = true;\n      \n      uni.showLoading({\n        title: '加载中',\n        mask: true\n      });\n      \n      // 获取动态详情\n      getDynamicDetail(this.noteInfo.id)\n        .then(res => {\n          uni.hideLoading();\n          this.isLoadingDetail = false;\n          \n          if (res.status === 200) {\n            this.debugLog('动态详情获取成功', res.data);\n            \n            // 修正：从res.data.detail中获取笔记数据，而不是直接从res.data中获取\n            const noteData = res.data.detail || res.data;\n            \n            // 更新笔记信息\n            this.noteInfo = {\n              ...this.noteInfo,\n              ...noteData,\n              user_info: noteData.user_info || {\n                uid: noteData.uid || 0,\n                nickname: noteData.nickname || '用户',\n                avatar: noteData.avatar || '/static/img/avatar_default.png'\n              },\n              comments: parseInt(noteData.comments || 0),\n              likes: parseInt(noteData.likes || 0),\n              views: parseInt(noteData.views || 0),\n              shares: parseInt(noteData.shares || 0),\n              is_like: !!noteData.is_like\n            };\n            \n            // 处理不同类型的媒体数据\n            this.processCommonData();\n            this.processMediaData();\n            \n            // 设置关注状态\n            if (noteData.user_info && noteData.user_info.is_follow !== undefined) {\n              this.isFollowing = !!noteData.user_info.is_follow;\n              this.followChecked = true;\n            }\n            \n            // 检查内容是否需要展开按钮\n            this.$nextTick(() => {\n              this.checkContentOverflow();\n            });\n            \n            // 加载评论列表\n            this.getCommentList();\n          } else {\n            this.opTipsPopup(res.msg || '获取动态详情失败', true);\n          }\n        })\n        .catch(err => {\n          uni.hideLoading();\n          this.isLoadingDetail = false;\n          this.opTipsPopup('获取动态详情失败', true);\n          this.debugLog('获取动态详情异常', err);\n        });\n    },\n    \n    // 检查内容是否超出两行需要显示展开按钮\n    checkContentOverflow() {\n      try {\n        if (!this.noteInfo.content) return;\n        \n        uni.createSelectorQuery()\n          .in(this)\n          .select('.content-text')\n          .boundingClientRect(rect => {\n            if (rect) {\n              // 获取文本内容高度\n              const lineHeight = parseInt(uni.getSystemInfoSync().fontSizeSetting) * 1.4;\n              const maxHeight = lineHeight * 2; // 两行文本的高度\n              \n              this.isContentOverflow = rect.height > maxHeight;\n              this.debugLog('内容高度检查', {\n                实际高度: rect.height,\n                最大高度: maxHeight,\n                是否超出: this.isContentOverflow\n              });\n            }\n          })\n          .exec();\n      } catch (e) {\n        this.debugLog('检查内容溢出异常', e);\n      }\n    },\n    \n    // 切换内容展开/收起状态\n    toggleContent() {\n      this.isExpanded = !this.isExpanded;\n    },\n    \n    // 预览图片\n    previewImage(currentImage, index) {\n      if (!currentImage) return;\n      \n      let images = [];\n      \n      // 获取所有图片URL\n      if (this.noteInfo.images && this.noteInfo.images.length > 0) {\n        images = this.noteInfo.images.map(img => {\n          if (typeof img === 'string') return img;\n          return img.url || '';\n        }).filter(Boolean);\n      }\n      \n      // 如果没有多张图片，使用当前图片\n      if (images.length === 0 && currentImage) {\n        images = [currentImage];\n      }\n      \n      uni.previewImage({\n        urls: images,\n        current: currentImage,\n        longPressActions: {\n          itemList: ['保存图片', '收藏', '分享'],\n          success: (data) => {\n            // 处理长按操作\n            if (data.tapIndex === 0) {\n              // 保存图片\n              uni.saveImageToPhotosAlbum({\n                filePath: images[data.index],\n                success: () => {\n                  uni.showToast({ title: '保存成功', icon: 'success' });\n                },\n                fail: () => {\n                  uni.showToast({ title: '保存失败', icon: 'none' });\n                }\n              });\n            }\n          }\n        }\n      });\n    },\n    \n    // 预览评论图片\n    previewCommentImage(imageUrl) {\n      if (!imageUrl) return;\n      \n      uni.previewImage({\n        urls: [imageUrl],\n        current: imageUrl,\n        longPressActions: {\n          itemList: ['保存图片'],\n          success: (data) => {\n            if (data.tapIndex === 0) {\n              // 保存图片\n              uni.saveImageToPhotosAlbum({\n                filePath: imageUrl,\n                success: () => {\n                  uni.showToast({ title: '保存成功', icon: 'success' });\n                },\n                fail: () => {\n                  uni.showToast({ title: '保存失败', icon: 'none' });\n                }\n              });\n            }\n          }\n        }\n      });\n    },\n    \n    // 轮播图切换\n    onSwiperChange(e) {\n      this.currentImageIndex = e.detail.current;\n    },\n    \n    // 切换音频播放状态\n    toggleAudioPlay() {\n      // 调用音频控制函数\n      this.audioBgClick();\n    },\n    \n    // 打开评论弹窗\n    commentPopupClick(isShow) {\n      this.isCommentPopup = isShow;\n      \n      if (isShow) {\n        // 当打开评论弹窗时加载评论列表\n        if (this.commentList.length === 0) {\n          console.log('加载评论列表');\n          this.page = 1;\n          this.getCommentList();\n        }\n        \n        // 打开评论弹窗\n        this.$refs.commentPopup.open();\n      } else {\n        // 关闭评论弹窗\n        this.$refs.commentPopup.close();\n      }\n    },\n    \n    // 强制应用表情样式（运行时修复）\n    forceApplyEmojiStyles() {\n      this.$nextTick(() => {\n        try {\n          // 获取所有表情图片元素\n          const emojiImages = uni.createSelectorQuery().in(this)\n            .selectAll('image[data-emoji], img[data-emoji]')\n            .exec((res) => {\n              if (res && res[0]) {\n                res[0].forEach((node, index) => {\n                  // 通过选择器强制设置样式\n                  uni.createSelectorQuery().in(this)\n                    .select(`image[data-emoji]:nth-child(${index + 1}), img[data-emoji]:nth-child(${index + 1})`)\n                    .fields({\n                      node: true,\n                      size: true\n                    })\n                    .exec((nodeRes) => {\n                      if (nodeRes && nodeRes[0] && nodeRes[0].node) {\n                        const node = nodeRes[0].node;\n                        // 强制设置样式\n                        node.style.width = '32rpx';\n                        node.style.height = '32rpx';\n                        node.style.maxWidth = '32rpx';\n                        node.style.maxHeight = '32rpx';\n                        node.style.minWidth = '32rpx';\n                        node.style.minHeight = '32rpx';\n                        node.style.objectFit = 'cover';\n                      }\n                    });\n                });\n              }\n            });\n        } catch (error) {\n          console.warn('强制应用表情样式失败:', error);\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style>\npage{\n  background:#000;\n}\n.nav-box{\n  position: fixed;\n  z-index: 99;\n  top: 0;\n  left: 0;\n  right: 0;\n  width: 100%;\n  box-sizing: border-box;\n}\n.nav-item .nav-back{\n  padding: 0 30rpx;\n  width: 34rpx;\n  height: 100%;\n  border-radius: 50%;\n}\n.video-box{\n  width: 100%;\n  transition: height .45s ease-in-out;\n}\n.video-box video{\n  width: 100%;\n  height: 100%;\n}\n.content-box{\n  position: fixed;\n  z-index: 99;\n  left: 0;\n  right: 0;\n  width: calc(100% - 60rpx);\n  margin: 20rpx 30rpx;\n  color: #fff;\n}\n.content-box .nav-user{\n  width: 100%;\n  justify-content: space-between;\n}\n.content-box .user-info{\n  display: flex;\n  align-items: center;\n}\n.content-box .nav-user .nav-user-avatar{\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 50%;\n  border: 2rpx solid rgba(255,255,255,.8);\n}\n.content-box .nav-user .nav-user-name{\n  margin-left: 20rpx;\n  font-size: 26rpx;\n  font-weight: 700;\n  opacity: .8;\n}\n/* 关注按钮样式 */\n.content-box .nav-user .follow-btn{\n  padding: 0 20rpx;\n  height: 48rpx;\n  line-height: 48rpx;\n  font-size: 20rpx;\n  font-weight: 700;\n  color: #fff;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 8rpx;\n  text-align: center;\n}\n.content-box .nav-user .follow-btn.active{\n  color: rgba(255, 255, 255, 0.6);\n  background: rgba(255, 255, 255, 0.1);\n}\n.content-box .content-item{\n  width: 100%;\n  margin: 20rpx 0;\n  word-break: break-word;\n}\n.content-box .content-item text{\n  font-size: 28rpx;\n  font-weight: 400;\n}\n.content-box .content-tag{\n  margin-bottom: 10rpx;\n  width: 100%;\n  display: flex;\n  flex-wrap: wrap;\n}\n.content-tag .tag-item{\n  margin: 0 10rpx 10rpx 0;\n  padding: 8rpx;\n  height: 40rpx;\n  border-radius: 8rpx;\n  background: rgba(255, 255, 255, 0.1);\n}\n.content-tag .tag-item .icon{\n  width: 40rpx;\n  height: 40rpx;\n  border-radius: 4rpx;\n}\n.content-tag .tag-item text{\n  font-size: 20rpx;\n  padding: 0 8rpx 0 12rpx;\n}\n.content-box .content-tips{\n  margin-top: 8rpx;\n  opacity: .6;\n  font-size: 20rpx;\n}\n.comment-box{\n  width: calc(100% - 60rpx);\n  padding: 30rpx;\n  background: #fff;\n  border-radius: 30rpx 30rpx 0 0;\n}\n.comment-box .comment-top{\n  width: 100%;\n  height: 56rpx;\n  justify-content: space-between;\n}\n.comment-top .top-title{\n  font-size: 26rpx;\n  font-weight: 700;\n}\n.comment-top .top-btn{\n  width: 136rpx;\n  padding: 6rpx;\n  background: #f8f8f8;\n  border-radius: 8rpx;\n  position: relative;\n}\n.comment-top .top-btn .btn-item, .comment-top .top-btn .btn-active{\n  width: 68rpx;\n  height: 44rpx;\n}\n.comment-top .top-btn .btn-item{\n  z-index: 2;\n  line-height: 44rpx;\n  text-align: center;\n  font-size: 20rpx;\n  font-weight: 500;\n  transition: color .3s;\n}\n.comment-top .top-btn .btn-active{\n  position: absolute;\n  z-index: 1;\n  background: #fff;\n  border-radius: 4rpx;\n  transition: left .3s;\n}\n.comment-top .top-close{\n  width: 48rpx;\n  height: 48rpx;\n  background: #f5f5f5;\n  border-radius: 50%;\n  transform: rotate(45deg);\n  justify-content: center;\n}\n.comment-scroll{\n  width: 100%;\n  height: calc(70vh - 196rpx);\n}\n.comment-box .comment-item{\n  width: 100%;\n  margin-top: 30rpx;\n  justify-content: space-between;\n  align-items: flex-start!important;\n}\n.comment-item .comment-avatar, .comment-item .comment-avatar-z{\n  background: #f8f8f8;\n  border: 1px solid #f5f5f5;\n  border-radius: 50%;\n  overflow: hidden;\n}\n.comment-item .comment-avatar{\n  width: 64rpx;\n  height: 64rpx;\n}\n.comment-item .comment-avatar-z{\n  width: 44rpx;\n  height: 44rpx;\n}\n.comment-item .comment-info{\n  width: calc(100% - 88rpx);\n}\n.unfold{\n  padding: 20rpx 68rpx;\n  color: #999;\n  font-size: 20rpx;\n  font-weight: 700;\n}\n.comment-info .comment-info-top{\n  font-size: 24rpx;\n  color: #999;\n}\n.comment-info .comment-info-top-z view{\n  max-width: 230rpx;\n  font-size: 22rpx;\n  color: #999;\n}\n.comment-info .comment-info-top-z text{\n  margin-right: 8rpx;\n  color: #333;\n  font-size: 22rpx;\n  font-weight: 500;\n}\n.comment-info .nn, .comment-info .zz, .comment-info .wo{\n  margin-right: 8rpx;\n}\n.comment-info .zz{\n  color: #FA5150!important;\n  font-weight: 700;\n}\n.comment-info .wo{\n  color: #000!important;\n  font-weight: 700;\n}\n.comment-info .db{\n  color: #ccc!important;\n}\n.comment-info .comment-info-content{\n  word-break: break-word;\n  white-space: pre-line;\n}\n.comment-info .comment-info-content text{\n  color: #333;\n  font-size: 26rpx;\n  font-weight: 400;\n}\n.comment-info .comment-info-bottom{\n  margin-top: 15rpx;\n  color: #999;\n  font-size: 20rpx;\n}\n.comment-info .comment-info-bottom view{\n  margin-left: 30rpx;\n  font-weight: 700;\n}\n.comment-box .no-more{\n  width: 100%;\n  height: 90rpx;\n  line-height: 90rpx;\n  text-align: center;\n  color: #999;\n  font-size: 20rpx;\n}\n.comment-box .comment-btn{\n  width: 100%;\n  height: 80rpx;\n  background: #f8f8f8;\n  border-radius: 40rpx;\n}\n.comment-box .comment-btn image{\n  margin: 0 20rpx 0 10rpx;\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n}\n.footer-box{\n  position: fixed;\n  z-index: 99;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  width: calc(100% - 30rpx);\n  padding: 20rpx 15rpx;\n  background: #000;\n  padding-bottom: max(env(safe-area-inset-bottom), 20rpx);\n}\n.footer-box .footer-item{\n  width: 100%;\n  height: 80rpx;\n  justify-content: space-between;\n}\n.footer-means{\n  margin-left: 15rpx;\n  padding: 0 30rpx;\n  height: 80rpx;\n  font-size: 20rpx;\n  font-weight: 700;\n  color: #fff;\n  background: #181818;\n  border-radius: 40rpx;\n}\n.footer-means image{\n  margin-left: 10rpx;\n  width: 20rpx;\n  height: 20rpx;\n}\n.footer-comment{\n  margin-left: 20rpx;\n  padding: 0 30rpx;\n  width: 200rpx;\n  height: 80rpx;\n  background: #181818;\n  border-radius: 40rpx;\n}\n.pl-str{\n  line-height: 80rpx;\n  color: #999;\n  font-size: 24rpx;\n  font-weight: 400;\n  word-break: break-word;\n  white-space: pre-line;\n  display: block;\n  display: -webkit-box;\n  overflow: hidden;\n  -webkit-line-clamp: 1;\n  text-overflow: ellipsis;\n  -webkit-box-orient: vertical;\n}\n.footer-item .footer-icon{\n  padding: 16rpx 15rpx;\n  display: flex;\n}\n.footer-item .footer-icon image{\n  width: 48rpx;\n  height: 48rpx;\n}\n.footer-item .footer-icon text{\n  margin-left: 8rpx;\n  color: #999;\n  font-size: 18rpx;\n  font-weight: 700;\n}\n.popup-comment-mask{\n  position: fixed;\n  z-index: 99998;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  width: 100%;\n  height: 100vh;\n}\n.popup-comment{\n  position: fixed;\n  z-index: 99999;\n  left: 0;\n  width: calc(100% - 60rpx);\n  padding: 30rpx;\n  background: #fff;\n  border-top: 1px solid #f8f8f8;\n  display: flex;\n  align-items: flex-end;\n}\n.popup-comment .send{\n  margin: 0 0 15rpx 30rpx;\n  width: 48rpx;\n  height: 48rpx;\n}\n.popup-comment .comment-textarea{\n  width: calc(100% - 98rpx);\n  padding: 10rpx 20rpx;\n  background: #f8f8f8;\n  border-radius: 30rpx;\n}\n.popup-comment .comment-textarea textarea{\n  width: 100%;\n  line-height: 32rpx;\n  min-height: 96rpx;\n  max-height: 320rpx;\n  font-size: 26rpx;\n}\n.popup-comment .comment-icon{\n  width: calc(100% - 20rpx);\n  padding: 30rpx 10rpx;\n}\n\n\n.empty-box{\n  width: 100%;\n  padding: 100rpx 0;\n  flex-direction: column;\n}\n.empty-box image{\n  width: 300rpx;\n  height: 300rpx;\n  margin-bottom: 30rpx;\n}\n.empty-box .e1{\n  font-size: 30rpx;\n  font-weight: 700;\n}\n.empty-box .e2{\n  margin-top: 10rpx;\n  color: #999;\n  font-size: 26rpx;\n}\n.tips-box{\n  justify-content: center;\n  width: 100%;\n}\n.tips-box .tips-item{\n  background: #000;\n  color: #fff;\n  padding: 20rpx 40rpx;\n  border-radius: 12rpx;\n  font-size: 24rpx;\n  font-weight: 700;\n}\n.df{\n  display: flex;\n  align-items: center;\n}\n.ohto{\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.ohto2{\n  display: -webkit-box;\n  word-break: break-all;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\n.xwb{\n  filter: invert(1);\n}\n\n/* 单图样式 */\n.image-box {\n  width: 100%;\n  background: #000;\n  transition: height .45s ease-in-out;\n  justify-content: center;\n  align-items: center;\n}\n\n.image-box .full-image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 多图样式 */\n.multi-image-box {\n  width: 100%;\n  background: #000;\n  transition: height .45s ease-in-out;\n}\n\n.multi-image-box .image-swiper {\n  width: 100%;\n  height: 100%;\n}\n\n.multi-image-box .swiper-image {\n  width: 100%;\n  height: 100%;\n}\n\n.multi-image-box .image-counter {\n  position: absolute;\n  right: 20rpx;\n  bottom: 20rpx;\n  background: rgba(0, 0, 0, 0.5);\n  color: #fff;\n  padding: 4rpx 20rpx;\n  border-radius: 30rpx;\n  font-size: 20rpx;\n}\n\n/* 内容区域调整 */\n.nav-counter{\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(0, 0, 0, 0.5);\n  color: #fff;\n  font-size: 24rpx;\n  padding: 6rpx 20rpx;\n  border-radius: 30rpx;\n}\n\n/* 音频播放器样式 */\n.audio-player-container {\n  position: relative;\n  width: 100%;\n  background: #000;\n  transition: height .45s ease-in-out;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  overflow: hidden;\n}\n\n/* 背景模糊层 */\n.audio-bg-blur {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n}\n\n.audio-bg-blur image {\n  width: 100%;\n  height: 100%;\n}\n\n.audio-bg-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 2;\n  -webkit-backdrop-filter: saturate(150%) blur(25px);\n  backdrop-filter: saturate(150%) blur(25px);\n  background: rgba(0, 0, 0, 0.5);\n}\n\n/* 音频播放内容 */\n.audio-player-content {\n  position: relative;\n  z-index: 3;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 40rpx;\n  width: 100%;\n  max-width: 700rpx;\n}\n\n/* 碟机外圈 */\n.vinyl-outer {\n  position: relative;\n  width: 400rpx;\n  height: 400rpx;\n  border: 3rpx solid rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-bottom: 40rpx;\n}\n\n/* 外圈刻槽 */\n.vinyl-groove {\n  position: absolute;\n  width: 350rpx;\n  height: 350rpx;\n  border: 2rpx solid rgba(255, 255, 255, 0.2);\n  border-radius: 50%;\n  border-style: dashed;\n}\n\n.vinyl-groove-2 {\n  position: absolute;\n  width: 320rpx;\n  height: 320rpx;\n  border: 1rpx solid rgba(255, 255, 255, 0.15);\n  border-radius: 50%;\n  border-style: dotted;\n}\n\n.vinyl-groove-3 {\n  position: absolute;\n  width: 290rpx;\n  height: 290rpx;\n  border: 1rpx solid rgba(255, 255, 255, 0.1);\n  border-radius: 50%;\n}\n\n/* 内圈唱片 */\n.vinyl-inner {\n  position: relative;\n  width: 260rpx;\n  height: 260rpx;\n  background: rgba(20, 20, 20, 0.8);\n  border-radius: 50%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  box-shadow: inset 0 0 30rpx rgba(0, 0, 0, 0.5);\n}\n\n.vinyl-center {\n  position: relative;\n  width: 200rpx;\n  height: 200rpx;\n  border-radius: 50%;\n  overflow: hidden;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n/* 专辑封面 */\n.album-cover {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  overflow: hidden;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);\n}\n\n.album-cover image {\n  width: 100%;\n  height: 100%;\n}\n\n/* 中心圆点 */\n.vinyl-dot {\n  position: absolute;\n  width: 16rpx;\n  height: 16rpx;\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 50%;\n  z-index: 2;\n}\n\n/* 音频信息 */\n.audio-info {\n  text-align: center;\n  margin-bottom: 40rpx;\n  width: 100%;\n}\n\n.audio-title {\n  font-size: 32rpx;\n  color: #fff;\n  font-weight: 600;\n  margin-bottom: 10rpx;\n  max-width: 100%;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.audio-artist {\n  font-size: 24rpx;\n  color: rgba(255, 255, 255, 0.7);\n  font-weight: 400;\n}\n\n/* 播放控制 */\n.audio-controls {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.audio-play-btn {\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.15);\n  backdrop-filter: blur(10rpx);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-bottom: 30rpx;\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\n  transition: all 0.3s ease;\n}\n\n.audio-play-btn:active {\n  background: rgba(255, 255, 255, 0.25);\n  transform: scale(0.95);\n}\n\n.audio-play-btn image {\n  width: 50rpx;\n  height: 50rpx;\n}\n\n/* 进度条容器 */\n.audio-progress-container {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.audio-time-start,\n.audio-time-end {\n  font-size: 20rpx;\n  color: rgba(255, 255, 255, 0.7);\n  min-width: 80rpx;\n}\n\n.audio-progress {\n  flex: 1;\n  margin: 0 20rpx;\n}\n\n/* 旋转动画 */\n.rotating {\n  animation: rotate 10s linear infinite;\n}\n\n.rotating-slow {\n  animation: rotate 30s linear infinite;\n}\n\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.loading-text {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #999;\n  font-size: 20rpx;\n}\n\n.loading-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 10rpx;\n}\n\n.comment-info-bottom .delete-btn{\n  margin-left: 24rpx;\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 评论点赞按钮样式 */\n.comment-info .user-info-left {\n  display: flex;\n  align-items: center;\n}\n\n.comment-info .like-icon {\n  display: flex;\n  align-items: center;\n  margin-left: auto;\n}\n\n.comment-info .like-icon image {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 4rpx;\n}\n\n.comment-info .like-icon text {\n  font-size: 20rpx;\n  color: #999;\n}\n\n/* 评论图片样式 */\n.comment-image {\n  margin-top: 10rpx;\n  max-width: 300rpx;\n  max-height: 400rpx;\n  border-radius: 8rpx;\n}\n\n.reply-comment-image {\n  max-width: 200rpx;\n  max-height: 300rpx;\n}\n\n/* 已删除评论样式 */\n.deleted-comment {\n  color: #ccc;\n  font-style: italic;\n  font-size: 24rpx;\n}\n\n/* 评论富文本样式 */\n.comment-rich-text {\n  word-break: break-word;\n  white-space: pre-wrap;\n}\n\n.reply-rich-text {\n  font-size: 24rpx;\n}\n\n/* 系统消息样式 */\n.system-message {\n  color: #999;\n  font-style: italic;\n  font-size: 24rpx;\n  background-color: #f8f8f8;\n  padding: 6rpx 12rpx;\n  border-radius: 6rpx;\n}\n\n/* 表情预览弹窗 */\n.emoji-preview-popup {\n  position: fixed;\n  z-index: 999;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100vh;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.emoji-preview-image {\n  width: 60%;\n  height: auto;\n  max-width: 400rpx;\n  max-height: 400rpx;\n}\n\n.emoji-img-inline {\n  display: inline-block;\n  width: 32rpx !important;\n  height: 32rpx !important;\n  vertical-align: middle;\n  margin: 0 4rpx;\n}\n</style> ", "import MiniProgramPage from 'D:/uniapp/vue3/pages/note/video.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "deleteDynamic", "res", "getCommentsList", "addComment", "commentData", "deleteComment", "likeComment", "getDynamicDetail"], "mappings": ";;;;;AA4dA,MAAA,YAAA,MAAA;AACA,MAAA,OAAA,MAAA;AACA,MAAA,cAAA,MAAA;AACA,MAAA,aAAA,MAAA;AACA,MAAA,eAAA,MAAA;AAgBA,MAAA,YAAA;AAAA,EACE,MAAA;AAAA;IAEE;AAAA;;;;;EAMF,UAAA;AAAA;AAAA,IAEE,iBAAA;AACE,aAAA,KAAA,WAAA,KAAA,SAAA;AAAA;;IAIF,sBAAA;;;;IAKA,gBAAA;AACE,aAAA,KAAA,QAAA,SAAA,wBAAA;AAAA;;;;;;;;;;;;;;IAmBF,iBAAA;AACE,aAAA;AAAA,QACE,cAAA,KAAA,SAAA;AAAA;QAEA,cAAA,KAAA,mBAAA,OAAA,KACE,KAAA,mBAAA,QAAA,KAAA,mBAAA,OAAA,KAAA,QAAA,CAAA,IAAA;AAAA;;;IAKN,sBAAA;AACE,aAAA;AAAA;QAEE,kBAAA,KAAA,aAAA;AAAA;;IAGJ;AAAA;EAEF,OAAA;AACE,WAAA;AAAA;;MAGE,gBAAA;AAAA,MACA,cAAA;AAAA,MACA,QAAA;AAAA;AAAA,MACA,cAAA;AAAA;AAAA;;MAEA,QAAA;AAAA;AAAA;;MAEA,eAAA;AAAA;AAAA,MACA,kBAAA;AAAA;AAAA;;;;;;;;;;MAMA,aAAA;AAAA;AAAA,MACA,UAAA;AAAA;AAAA;AAAA,MAEA,iBAAA;AAAA;AAAA,MACA,mBAAA;AAAA;AAAA,MACA,qBAAA;AAAA;AAAA,MACA,mBAAA;AAAA;AAAA,MACA,kBAAA;AAAA;AAAA;AAAA;;;;;;MAKA,UAAA;AAAA;;;;UAKI,UAAA;AAAA;;;QAGF,SAAA;AAAA,QACA,MAAA;AAAA;QAEA,OAAA;AAAA,QACA,OAAA;AAAA,QACA,QAAA;AAAA;QAEA,aAAA;AAAA,QACA,eAAA;AAAA;;;;;QAMA,YAAA;AAAA,QACA,QAAA,CAAA;AAAA,QACA,OAAA;AAAA,QACA,aAAA;AAAA,QACA,OAAA;AAAA,QACA,aAAA;AAAA;;;;MAKF,aAAA;AAAA,MACA,WAAA;AAAA,MACA,aAAA,CAAA;AAAA,MACA,OAAA;AAAA,MACA,cAAA;AAAA,MACA,MAAA;AAAA;MAEA,MAAA;AAAA,MACA,SAAA;AAAA,MACA,OAAA;AAAA;AAAA,MACA,YAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA,MACA,WAAA;AAAA;MAEA,SAAA;AAAA;MAEA,gBAAA;AAAA,MACA,cAAA;AAAA;MAEA,mBAAA;AAAA;AAAA;;MAEA,mBAAA;AAAA;AAAA,MACA,gBAAA;AAAA;AAAA,MACA,OAAA;AAAA;AAAA;AAAA,MAGA,UAAA,oBAAA,IAAA;AAAA;AAAA,MACA,oBAAA,oBAAA,IAAA;AAAA;AAAA,MACA,iBAAA;AAAA;AAAA;;;;;;;;;MAOA,kBAAA;AAAA;AAAA;;IAEF;AAAA;EAEF,MAAA,OAAA,SAAA;AACEA,kBAAA,MAAA,MAAA,OAAA,+BAAA,WAAA,OAAA;;;AAOEA,oBAAA,MAAA,cAAA;AAAA,IACF;AAGA,UAAA,KAAA;;AAIA,SAAA,aAAA;;AAMA,SAAA,eAAA;AAGA,QAAA,QAAA,IAAA;;AAEE,WAAA,SAAA,WAAA,KAAA,SAAA,EAAA;AAGA,WAAA,eAAA;AAAA;;AAGAA,oBAAAA,MAAA,UAAA;AAAA;;MAGA,CAAA;AACA,iBAAA,MAAA;AACEA,sBAAA,MAAA,aAAA;AAAA,MACF,GAAA,IAAA;AACA;AAAA,IACF;;AAMA,SAAA,UAAA,MAAA;AACEA,oBAAAA,MAAA,oBAAA,EAAA,GAAA,IAAA,EACE,OAAA,aAAA;AAEE,YAAA,MAAA;AACE,eAAA,eAAA,SAAA,KAAA,MAAA;AAAA,QACF;AAAA,SAEF;IACJ,CAAA;AAAA;EAGF,SAAA;;AAKE,SAAA,eAAA;AAGA,QAAA,KAAA,eAAA,KAAA,gBAAA;;IAEA;AAAA;EAGF,SAAA;;;AAMI,WAAA,WAAA;AAAA,IACF;AAAA;EAGF,UAAA;;AAGIA,oBAAAA,MAAA,aAAA,KAAA,gBAAA;AAAA,IACF;AAGA,SAAA,aAAA;AAAA;;;;;EAWF,SAAA;AAAA;AAAA,IAEE,YAAA,MAAA;AACE,UAAA,KAAA,OAAA;AACEA,sBAAA,MAAA,MAAA,OAAA,+BAAA,WAAA,GAAA,IAAA;AAAA,MACF;AAAA;;IAIF,iBAAA;;AACE,UAAA;;AAGEA,sBAAA,MAAA,MAAA,OAAA,+BAAA,eAAA,OAAA,QAAA;;AAIE,cAAA;;;UAGA,SAAA,GAAA;;;UAGA;AAAA,QACF;AAGA,cAAA,aAAA,UAAA,OAAA,MAAA,QAAA,mBAAA,QAAA;;AAIA,aAAA,aAAA,SAAA,UAAA;AACA,aAAA,eAAA,SAAA,YAAA;;AAIA,cAAA,WAAA,CAAA,CAAA,SAAA;AACA,aAAA,SAAA,aAAA;;UAGE,QAAA,KAAA;AAAA;UAEA,cAAA,KAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA,QAAA,KAAA;AAAA,QACF,CAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,+BAAA,aAAA,KAAA;;AAGA,aAAA,aAAA;AACA,aAAA,eAAA;AACA,aAAA,SAAA;AAAA,MACF;AAAA;;IAIF,mBAAA;AACE,UAAA;;;UAKI,KAAA;AAAA,UACA,KAAA;;;UAGA,KAAA;;;UAGA,KAAA;;;UAGA;AACE,iBAAA,SAAA,YAAA,KAAA,SAAA,IAAA;;AAGE,mBAAA,SAAA,OAAA;;;AAGA,mBAAA,SAAA,OAAA;;;;;YAKF;AAAA,QACJ;AAAA;AAEA,aAAA,SAAA,aAAA,KAAA;AAAA,MACF;AAAA;;IAIF,mBAAA;;AAGE,UAAA;AAEE,YAAA,SAAA,KAAA,SAAA;;;AAII,gBAAA;;YAEA,SAAA,GAAA;AACE,uBAAA,CAAA,MAAA;AAAA,YACF;AAAA,UACF,WAAA,QAAA;AAEE,qBAAA,CAAA,MAAA;AAAA;AAGA,qBAAA,CAAA;AAAA,UACF;AAAA,QACF;AAGA,aAAA,SAAA,SAAA,OAAA,IAAA,SAAA;;;UAIE;;;;AAOA,iBAAA,OAAA,QAAA,WAAA,MAAA,OAAA,GAAA;AAAA,QACF,CAAA;;MAGF,SAAA,GAAA;;;MAGA;AAAA;;IAIF,mBAAA;AACEA,0BAAA,MAAA,OAAA,+BAAA,WAAA,KAAA,SAAA,KAAA;AAGA,UAAA,CAAA,KAAA,SAAA,eAAA,KAAA,SAAA,SAAA,KAAA,SAAA,MAAA,OAAA;;MAEA;;;AAMI,eAAA,SAAA,QAAA,KAAA,SAAA,MAAA;AAAA,QACF,WAAA,KAAA,SAAA,MAAA,KAAA;AACE,eAAA,SAAA,QAAA,KAAA,SAAA,MAAA;AAAA,QACF,WAAA,KAAA,SAAA,MAAA,MAAA;AACE,eAAA,SAAA,QAAA,KAAA,SAAA,MAAA;AAAA,QACF;AAAA,MACF;;AAIE,aAAA,SAAA,QAAA,KAAA,SAAA;AAAA,MACF;;;MAKA;AAGA,UAAA,KAAA,SAAA,eAAA,OAAA,KAAA,SAAA,gBAAA,UAAA;AACE,YAAA,KAAA,SAAA,YAAA,KAAA;;QAEA;AAAA,MACF;AAGA,UAAA,KAAA,SAAA,eAAA,OAAA,KAAA,SAAA,gBAAA,UAAA;;MAEA;AAAA;;IAIF,mBAAA;AACE,UAAA,CAAA,KAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,+BAAA,gBAAA;AACA;AAAA,MACF;AAEAA,0BAAA,MAAA,OAAA,+BAAA,WAAA,KAAA,SAAA,KAAA;;;AAKI,eAAA,SAAA,QAAA,KAAA,SAAA,MAAA;AAAA,QACF,WAAA,KAAA,SAAA,MAAA,KAAA;AACE,eAAA,SAAA,QAAA,KAAA,SAAA,MAAA;AAAA,QACF,WAAA,KAAA,SAAA,MAAA,MAAA;AACE,eAAA,SAAA,QAAA,KAAA,SAAA,MAAA;AAAA,QACF;AAAA,MACF;;;MAKA;AAGA,UAAA,CAAA,KAAA,SAAA,aAAA;AACE,aAAA,SAAA,cAAA;AAAA;AAEA,YAAA,KAAA,SAAA,YAAA,KAAA;;QAEA;AAAA,MACF;AAGA,UAAA,KAAA,SAAA,eAAA,OAAA,KAAA,SAAA,gBAAA,UAAA;;MAEA;AAGA,UAAA,CAAA,KAAA,SAAA,aAAA;AACE,aAAA,SAAA,cAAA;AAAA,MACF;AAGA,WAAA,eAAA;AAAA;;IAIF,iBAAA;;;;;;;;;;IAaA,oBAAA,gBAAA;AAEE,UAAA,CAAA,gBAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,gBAAA;AACA,eAAA;MACF;AAGA,UAAA,OAAA,mBAAA,UAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,mBAAA;AACA,YAAA,eAAA,WAAA,GAAA,GAAA;AACE,cAAA;AACE,6BAAA,KAAA,MAAA,cAAA;AACAA,0BAAA,MAAA,MAAA,OAAA,gCAAA,aAAA,cAAA;AAAA,UACF,SAAA,UAAA;AACEA,0BAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,QAAA;AACA,6BAAA,CAAA,cAAA;AAAA,UACF;AAAA;AAEAA,wBAAAA,MAAA,MAAA,OAAA,gCAAA,kBAAA;AACA,2BAAA,CAAA,cAAA;AAAA,QACF;AAAA,MACF;AAGA,UAAA,MAAA,QAAA,cAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,OAAA,gCAAA,cAAA,eAAA,MAAA;AACA,eAAA,eAAA,IAAA,CAAA,KAAA,UAAA;AACEA,wBAAAA,MAAA,MAAA,OAAA,gCAAA,MAAA,QAAA,CAAA,QAAA,GAAA;;AAEE,mBAAA,EAAA,KAAA,KAAA,MAAA,KAAA,MAAA;UACF,WAAA,OAAA,OAAA,QAAA,UAAA;;cAGI,KAAA,IAAA,OAAA,IAAA,QAAA,IAAA,OAAA,IAAA,SAAA;AAAA,cACA,MAAA,SAAA,IAAA,QAAA,IAAA,SAAA,GAAA;AAAA,cACA,MAAA,SAAA,IAAA,QAAA,IAAA,UAAA,GAAA;AAAA;AAEFA,0BAAAA,MAAA,MAAA,OAAA,gCAAA,KAAA,QAAA,CAAA,SAAA,MAAA;;UAEF;AACA,iBAAA,EAAA,KAAA,IAAA,MAAA,KAAA,MAAA;QACF,CAAA,EAAA,OAAA,SAAA,CAAA,CAAA,IAAA,GAAA;AAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,kBAAA;AACA,eAAA;MACF;AAAA;;IAIF,oBAAA;;;;UAKM,QAAA;AAAA;MAEJ;AAGA,UAAA,CAAA,KAAA,SAAA,UAAA,QAAA;AACE,aAAA,SAAA,UAAA,SAAA;AAAA,MACF;AAGA,UAAA,KAAA,SAAA,aAAA,UAAA,KAAA,SAAA,aAAA,MAAA;;;;MAIA;AAGA,UAAA,CAAA,KAAA,SAAA,OAAA,KAAA,SAAA,SAAA;AACE,aAAA,SAAA,MAAA,KAAA,SAAA;AAAA,MACF;;;;;AAQA,UAAA,KAAA,SAAA,eAAA,KAAA,SAAA,YAAA,YAAA,GAAA;;MAEA;;;MAIA;;;;;AAMA,UAAA,KAAA,SAAA,eAAA,KAAA,SAAA,YAAA,WAAA;AACE,eAAA,KAAA,SAAA,YAAA;AAAA,MACF;AACA,aAAA,KAAA,SAAA,aAAA;AAAA;;IAIF,gBAAA;AACE,UAAA,KAAA,SAAA,eAAA,KAAA,SAAA,YAAA,aAAA;AACE,eAAA,KAAA,SAAA,YAAA;AAAA,MACF;AACA,aAAA,KAAA,SAAA,eAAA;AAAA;;IAIF,kBAAA;AACE,UAAA,KAAA,SAAA,eAAA,KAAA,SAAA,YAAA,eAAA;AACE,eAAA,KAAA,SAAA,YAAA;AAAA,MACF;;;;IAKF,eAAA;AACE,UAAA,KAAA,SAAA,OAAA;AAAA;AAEA,UAAA;;;AAIM,iBAAA,SAAA,IAAA,MAAA,QAAA,KAAA;AAAA,UACF;AAAA,QACF,CAAA;AACAA,4BAAA,MAAA,OAAA,gCAAA,iBAAA,KAAA,SAAA,MAAA,KAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,gCAAA,cAAA,KAAA;AAAA,MACF;AAAA;;IAIF,mBAAA;AACE,UAAA;;AAEE,YAAA,iBAAA;AACE,eAAA,eAAA,KAAA,MAAA,eAAA;AAAA,QACF;AAAA,MACF,SAAA,GAAA;;MAEA;AAAA;;IAIF,eAAA;;;;IAMA,mBAAA;;;AAKIA,sBAAAA,MAAA,cAAA,KAAA,gBAAA;AAAA,MACF;AAGA,UAAA,KAAA,aAAA;;MAEA;AAGA,WAAA,eAAA;;;AAOE,aAAA,iBAAA;;MAEF;AAAA;;IAIF,iBAAA;;AAEI,qBAAA,KAAA,aAAA;;MAEF;;AAGE,qBAAA,KAAA,gBAAA;;MAEF;;AAGE,qBAAA,KAAA,gBAAA;;MAEF;;AAGE,sBAAA,KAAA,kBAAA;;MAEF;AAAA;;;;AAME,qBAAA,KAAA,eAAA;;MAEF;;;;IAOF,kBAAA;AACE,WAAA,mBAAA;;;;IAKF,qBAAA;AACE,aAAA;AAAA,QACE,cAAA,KAAA,SAAA;AAAA;;;;;;AAQF,WAAA,UAAA,MAAA;AACE,YAAA;;AAKM,gBAAA,OAAA,IAAA,CAAA,GAAA;;AAGIA,8BAAAA,MAAA,oBAAA,EAAA,GAAA,IAAA,EACE,OAAA,+BAAA,QAAA,CAAA,gCAAA,QAAA,CAAA,GAAA,EACA,OAAA;AAAA,kBACE,MAAA;AAAA,kBACA,MAAA;AAAA,mBAEF,KAAA,CAAA,YAAA;AACE,sBAAA,WAAA,QAAA,CAAA,KAAA,QAAA,CAAA,EAAA,MAAA;;;;;;;;;kBAUA;AAAA,gBACF,CAAA;AAAA,cACJ,CAAA;AAAA,YACF;AAAA,UACF,CAAA;AAAA;AAEFA,wBAAA,MAAA,MAAA,QAAA,gCAAA,eAAA,KAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,UAAA;AACE,UAAA,gBAAA,EAAA,SAAA,GAAA;AACEA,sBAAAA,MAAA,aAAA;AAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA;;MAGF;AAAA;;;;AAOAA,oBAAA,MAAA,MAAA,OAAA,gCAAA,QAAA,KAAA,IAAA;AAEA,WAAA,YAAA;;AAGA,iBAAA,MAAA;;AAEE,YAAA,MAAA;;QAEA;AAAA,MACF,GAAA,GAAA;AAAA;;IAIF,cAAA,GAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,YAAA,EAAA,cAAA,QAAA;AAAA;;;IAKJ,oBAAA;;;AAGEA,oBAAAA,MAAA,aAAA;AAAA,QACE,UAAA,WAAA,KAAA,SAAA,QAAA;AAAA,QACA,WAAA,WAAA,KAAA,SAAA,SAAA;AAAA,QACA,MAAA,KAAA,SAAA;AAAA;;;;;;;;;;;IAeJ,WAAA,QAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,sBAAA,MAAA;AAAA,MACF,CAAA;AAAA;;IAIF,aAAA,QAAA;AACE,WAAA,WAAA;AAAA;;;AAKA,WAAA,YAAA,WAAA,MAAA;AAAA;;IAIF,cAAA,QAAA;AACEA,oBAAA,MAAA,MAAA,OAAA,gCAAA,WAAA,MAAA;AAAA;;;AAMA,UAAA,KAAA;AAAA;;;AAKA,YAAA,eAAA,mBAAA,IAAA;AACA,YAAA,WAAA,KAAA,SAAA;AAGA,WAAA,SAAA,UAAA;;;QAKE,IAAA,KAAA,SAAA;AAAA;;;;AAQA,aAAA,SAAA,UAAA;;;;AAMA,mBAAA,MAAA;;QAEA,GAAA,GAAA;AAAA,MACF,CAAA;AAAA;;;;;;AAUA,YAAA,iBAAA,CAAA,KAAA;AACA,YAAA,iBAAA,KAAA;AAEA,WAAA,cAAA;;;MAKA;;;;;;AAYE,aAAA,cAAA;;;QAGA;;;AAKA,mBAAA,MAAA;;QAEA,GAAA,GAAA;AAAA,MACF,CAAA;AAAA;;;;AAOAA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,SAAA,SAAA,KAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA;AAAA,cACE,MAAA;AAAA,YACF,CAAA;AAGAC,uBAAA,cAAA,KAAA,SAAA,EAAA,EAAA,KAAA,CAAAC,SAAA;AACEF,4BAAA,MAAA,YAAA;AACA,uBAAA,WAAA,eAAA;AAEA,kBAAAE,KAAA,WAAA,KAAA;;;AAGE,qBAAA,YAAAA,KAAA,OAAA,MAAA;AAAA,cACF;AAAA,YACF,CAAA,EAAA,MAAA,SAAA;AACEF,4BAAA,MAAA,YAAA;;YAEF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,YAAA,QAAA;;AAGEA,oBAAAA,MAAA,YAAA;AAAA,QACE,MAAA;AAAA,MACF,CAAA;AAGA,UAAA,WAAA;AACA,UAAA,KAAA,SAAA,QAAA,KAAA,KAAA,SAAA,UAAA,KAAA,SAAA,OAAA,SAAA,GAAA;AACE,mBAAA,KAAA,SAAA,OAAA,CAAA,EAAA;AAAA,MACF,WAAA,KAAA,SAAA,QAAA,KAAA,KAAA,SAAA,SAAA,KAAA,SAAA,aAAA;AACE,mBAAA,KAAA,SAAA;AAAA,MACF,WAAA,KAAA,SAAA,QAAA,KAAA,KAAA,SAAA,SAAA,KAAA,SAAA,MAAA,OAAA;AACE,mBAAA,KAAA,SAAA,MAAA;AAAA,MACF;;QAIE;AAAA,QACA,KAAA,SAAA;AAAA,QACA,KAAA,SAAA;AAAA,QACA,KAAA,SAAA;AAAA,QACA;AAAA;AAEAA,sBAAA,MAAA,YAAA;AACA,aAAA,YAAA,IAAA,OAAA,MAAA;;MAEF,CAAA,EAAA,MAAA,SAAA;AACEA,sBAAA,MAAA,YAAA;;MAEF,CAAA;AAAA;;;AAKA,aAAA;AAAA;QAEE,UAAA,KAAA,iBAAA;AAAA,QACA,MAAA,0BAAA,KAAA,SAAA;AAAA;;;IAKJ,kBAAA;AACE,aAAA;AAAA;QAEE,UAAA,KAAA,iBAAA;AAAA;;;;IAMJ,mBAAA;;;AAGM,gBAAA,aAAA,KAAA,SAAA,OAAA,CAAA;AACA,cAAA,OAAA,eAAA,UAAA;AACE,mBAAA;AAAA;AAEA,mBAAA,WAAA;AAAA,UACF;AAAA,QACF;AACA,eAAA;AAAA,MACF,WAAA,KAAA,SAAA,QAAA,GAAA;AACE,eAAA,KAAA,SAAA,eAAA;AAAA,MACF,WAAA,KAAA,SAAA,QAAA,GAAA;AACE,eAAA,KAAA,SAAA,eAAA;AAAA,MACF;AACA,aAAA;AAAA;;IAIF,YAAA,OAAA;AACE,UAAA,CAAA,SAAA,QAAA;AAAA,eAAA;;;AAEA,cAAA,QAAA,KAAA,QAAA,CAAA,IAAA;AAAA;;IAIF,WAAA,SAAA;AACE,UAAA,CAAA,WAAA,MAAA,OAAA;AAAA,eAAA;AAEA,YAAA,OAAA,KAAA,MAAA,UAAA,EAAA;AACA,YAAA,OAAA,KAAA,MAAA,UAAA,EAAA;AAEA,aAAA,GAAA,KAAA,SAAA,EAAA,SAAA,GAAA,GAAA,CAAA,IAAA,KAAA,SAAA,EAAA,SAAA,GAAA,GAAA,CAAA;AAAA;;IAIF,WAAA,MAAA;;;;;;;;;IAWA,iBAAA;;AAGI,aAAA,SAAA,eAAA;AACA;AAAA,MACF;AAGA,UAAA,KAAA,eAAA,aAAA,KAAA,OAAA,GAAA;;AAEE;AAAA,MACF;;AAKA,UAAA,KAAA,SAAA,GAAA;AACEA,sBAAAA,MAAA,YAAA;AAAA;UAEE,MAAA;AAAA,QACF,CAAA;AAAA;;MAIF;;QAIE,MAAA;AAAA;AAAA,QACA,MAAA,KAAA,QAAA;AAAA,QACA,WAAA,KAAA,SAAA;AAAA;AAAA;;QAIA,MAAA,KAAA,SAAA;AAAA,QACA,IAAA,OAAA;AAAA,QACA,MAAA,OAAA,cAAA,IAAA,OAAA;AAAA,MACF,CAAA;AAGAG,iBAAAA,gBAAA,KAAA,SAAA,IAAA,MAAA;AAEI,YAAA,KAAA,SAAA,GAAA;AACEH,wBAAA,MAAA,YAAA;AAAA,QACF;;AAIA,YAAA,IAAA,WAAA,KAAA;AACE,eAAA,SAAA,YAAA,IAAA,IAAA;;;;;;;;UAWA;AAGA,gBAAA,gBAAA,KAAA,IAAA,UAAA;0CAGI,KAAA,aAAA,KAAA,UAAA,UACA;8CAGA,KAAA,aAAA,KAAA,UAAA;AAGF,kBAAA,MAAA,KAAA,OACE,KAAA,aAAA,KAAA,UAAA;AAGF,mBAAA;AAAA,cACE,GAAA;AAAA;AAAA;cAGA;AAAA,cACA;AAAA;;;gBAIE;AAAA,gBACA;AAAA;;cAGF,aAAA,SAAA,KAAA,WAAA,KAAA;AAAA;;;;;cAMA,OAAA,KAAA,SAAA;AAAA,cACA,QAAA,KAAA,UAAA;AAAA,cACA,aAAA,KAAA,eAAA;AAAA;AAAA;AAIE,sBAAA,cAAA,MAAA,UACE,MAAA,aAAA,MAAA,UAAA,UACA;AAEF,sBAAA,gBAAA,MAAA,YACE,MAAA,aAAA,MAAA,UAAA;8CAIA,MAAA,aAAA,MAAA,UAAA;AAGF,uBAAA;AAAA,kBACE,GAAA;AAAA,kBACA,IAAA,MAAA,MAAA;AAAA;;kBAGA,QAAA;AAAA;;;oBAIE,QAAA;AAAA;kBAEF,WAAA,MAAA,aAAA,MAAA,UAAA;AAAA;kBAEA,YAAA,SAAA,MAAA,UAAA,KAAA,SAAA,MAAA,KAAA,KAAA;AAAA;;;;kBAKA,OAAA,MAAA,SAAA;AAAA;kBAEA,aAAA,MAAA,eAAA;AAAA;;;;cAKJ,kBAAA,KAAA,eAAA,KAAA,UAAA,KAAA,QAAA,SAAA;AAAA;UAEJ,CAAA;AAGA,cAAA,KAAA,SAAA,GAAA;AAEE,iBAAA,cAAA;AAEA,iBAAA,UAAA,cAAA,WAAA;AAAA;;UAIF;AAGA,cAAA,KAAA,SAAA,IAAA;;;;UAIA;AAGA,cAAA,KAAA,SAAA,GAAA;AACE,kBAAA,WAAA,YAAA,KAAA,SAAA,EAAA,IAAA,KAAA,KAAA,IAAA,KAAA,IAAA;;cAEE,MAAA;AAAA,cACA,SAAA,KAAA;AAAA;cAEA,eAAA,KAAA,SAAA;AAAA;UAEJ;;;YAME,MAAA,KAAA;AAAA;YAEA,MAAA,KAAA;AAAA,YACA,MAAA,KAAA;AAAA,UACF,CAAA;AAAA;;;AAIAA,wBAAAA,MAAA,UAAA;AAAA;;UAGA,CAAA;AAAA,QACF;AAAA;AAGA,YAAA,KAAA,SAAA,GAAA;AACEA,wBAAA,MAAA,YAAA;AAAA,QACF;;;;AAKAA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;QAEF,CAAA;AAAA,MACF,CAAA;AAAA;;IAIJ,aAAA,MAAA;;;;;;AAOE,WAAA,QAAA;;;AAKA,WAAA,cAAA;AACA,WAAA,UAAA;AAEA,WAAA,eAAA;AAGA,iBAAA,MAAA;;MAEA,GAAA,GAAA;AAAA;;IAIF,YAAA,GAAA;AAEE,UAAA,KAAA,EAAA,eAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA;AAGA,QAAA,mBAAA,EAAA;AAEAA,oBAAAA,MAAA,MAAA,OAAA,gCAAA,iBAAA;AAAA,QACE,QAAA,KAAA;AAAA,QACA,QAAA,KAAA;AAAA,QACA,UAAA;AAAA;;QAGA;AAAA,MACF,CAAA;AAEA,UAAA,CAAA,KAAA,QAAA;;AAEE,aAAA,YAAA,cAAA;AACA,mBAAA,MAAA;AACEA,wBAAAA,MAAA,WAAA;AAAA;;QAGF,GAAA,GAAA;;MAEF;AAEA,UAAA,UAAA,EAAA,cAAA,WAAA,CAAA;;AAEA,UAAA,MAAA,QAAA,OAAA;AACA,UAAA,MAAA,QAAA,OAAA;;;AAIA,WAAA,KAAA,QAAA,MAAA,SAAA,QAAA,IAAA;AAGA,WAAA,YAAA;AAGA,WAAA,sBAAA;;;;;;;;;MAWA;AAGA,WAAA,UAAA,MAAA;AAEE,aAAA,YAAA;AAGA,mBAAA,MAAA;AACE,eAAA,UAAA;AAGA,cAAA,CAAA,KAAA,WAAA;AACE,iBAAA,YAAA;AAAA,UACF;AAAA,QACF,GAAA,GAAA;AAAA,MACF,CAAA;AAAA;;IAIF,aAAA,GAAA;AAEE,WAAA,EAAA,mBAAA,EAAA,gBAAA;;;AAME;AAAA,MACF;;AAIE,qBAAA,KAAA,gBAAA;;MAEF;;AAMA,WAAA,YAAA;AACA,WAAA,UAAA;AAGA,WAAA,UAAA;AAAA;;IAIF,oBAAA,aAAA;AACE,UAAA,KAAA;AAAA;;AAIA,YAAA,UAAA,YAAA;;AAIA,UAAA,CAAA,WAAA,CAAA,OAAA;AACE,aAAA,sBAAA;AACA,eAAA,KAAA,YAAA,aAAA;AAAA,MACF;AAGAA,oBAAAA,MAAA,YAAA;AAAA;QAEE,MAAA;AAAA,MACF,CAAA;AAGA,WAAA,YAAA;AACA,WAAA,UAAA;AACA,WAAA,YAAA;;;QAKE;AAAA,QACA,KAAA,KAAA,QAAA;AAAA,QACA,QAAA,KAAA,QAAA;AAAA;AAIF,UAAA,OAAA;AACE,eAAA,QAAA;AAAA,MACF;AAGAI,iBAAAA,WAAA,MAAA;AAEIJ,sBAAA,MAAA,YAAA;AAEA,YAAA,IAAA,WAAA,KAAA;AAEE,gBAAAK,eAAA,IAAA,QAAA,KAAA,yBAAA,SAAA,KAAA;AAGA,eAAA,sBAAAA,YAAA;;AAMA,cAAA,KAAA,SAAA;AACE,iBAAA,UAAA;AAAA,UACF;AAAA;AAEA,eAAA,YAAA,IAAA,OAAA,MAAA;AAAA,QACF;AAAA;AAGAL,sBAAA,MAAA,YAAA;;;;AAMA,aAAA,sBAAA;;;;AAMA,aAAA,UAAA;AAAA,MACF,CAAA;AAAA;;IAIJ,yBAAA,SAAA,UAAA;;AAEE,YAAA,SAAA,QAAA,KAAA,IAAA,CAAA,IAAA,KAAA,OAAA,EAAA,SAAA,EAAA,EAAA,OAAA,GAAA,CAAA,CAAA;AAGA,YAAA,iBAAA,UAAA,aAAA,mBAAA,eAAA,gBAAA,OAAA,MAAA,QAAA,mBAAA,aAAA,mBAAA,aAAA;AAGA,YAAA,cAAA;AAAA,QACE,IAAA;AAAA;AAAA,QACA,KAAA,KAAA;AAAA,QACA,UAAA,KAAA,gBAAA;AAAA;QAEA,SAAA,WAAA;AAAA;;QAEA,aAAA,KAAA,WAAA,oBAAA,KAAA,CAAA;AAAA,QACA,OAAA;AAAA,QACA,YAAA;AAAA;AAAA;QAEA,QAAA;AAAA;AAAA,QACA,UAAA;AAAA,QACA,aAAA;AAAA;AAAA,QACA,SAAA,CAAA;AAAA;AAAA,QACA,aAAA;AAAA;AAAA,QACA,kBAAA;AAAA;AAAA,QACA,WAAA;AAAA;AAAA,QACA,iBAAA;AAAA;AAAA;AAGFA,oBAAA,MAAA,MAAA,OAAA,gCAAA,aAAA,WAAA;AACA,aAAA;AAAA;;IAIF,sBAAA,aAAA;;;QAGI,MAAA,KAAA;AAAA;QAEA,kBAAA,KAAA,SAAA;AAAA,MACF,CAAA;AAGA,UAAA,CAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,SAAA,gCAAA,aAAA;AACA;AAAA,MACF;;AAMA,UAAA,KAAA,QAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,OAAA,gCAAA,UAAA,KAAA,IAAA;AAEA,YAAA,CAAA,KAAA,YAAA,KAAA,IAAA,EAAA,SAAA;AACE,eAAA,YAAA,KAAA,IAAA,EAAA,UAAA,CAAA;AAAA,QACF;;AAEE,eAAA,YAAA,KAAA,IAAA,EAAA,cAAA;AAAA,QACF;;AAKE,gBAAA,WAAA,KAAA,QAAA,QAAA,OAAA,EAAA;AACA,sBAAA,YAAA,KAAA;AACA,sBAAA,iBAAA;AAAA,QACF;;AAIA,aAAA,YAAA,KAAA,IAAA,EAAA;AAGA,aAAA,aAAA,IAAA,YAAA,IAAA,KAAA,YAAA,KAAA,IAAA,EAAA,QAAA,SAAA,CAAA;AAGA,aAAA,aAAA;;UAGE,MAAA,KAAA,YAAA,KAAA,IAAA,EAAA;AAAA;QAEF,CAAA;AAAA,MACF;;AAKE,YAAA,KAAA,WAAA,KAAA,YAAA,WAAA,GAAA;AACE,eAAA,UAAA;AACA,eAAA,cAAA;;QAEF;;;AAOA,aAAA,YAAA,QAAA,WAAA;AAGA,aAAA,aAAA;;UAGE,QAAA,KAAA,YAAA;AAAA;QAEF,CAAA;AAAA,MACF;AAAA;;;;AAMA,UAAA,MAAA,EAAA,cAAA,QAAA;AACA,UAAA,IAAA,EAAA,cAAA,QAAA;AACA,UAAA,YAAA,EAAA,cAAA,QAAA;AAGA,UAAA,KAAA;AAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,SAAA,SAAA,KAAA;AACE,cAAA,IAAA,SAAA;;AAGEA,0BAAAA,MAAA,YAAA;AAAA;cAEE,MAAA;AAAA,YACF,CAAA;AAGAM,uBAAAA,cAAA,SAAA,EAAA,KAAA,CAAAJ,SAAA;AACEF,4BAAA,MAAA,YAAA;;AAGA,kBAAAE,KAAA,WAAA,KAAA;;;gBAIE;AAGA,oBAAA,KAAA,IAAA;AAEE,uBAAA,YAAA,GAAA,EAAA,eAAA,oBAAA,KAAA,GAAA;AACA,uBAAA,YAAA,GAAA,EAAA,SAAA;AAAA;AAGA,sBAAA,KAAA,YAAA,GAAA,EAAA,WAAA,KAAA,YAAA,GAAA,EAAA,QAAA,SAAA,GAAA;AACE,yBAAA,YAAA,GAAA,EAAA,QAAA,CAAA,EAAA,eAAA,oBAAA,QAAA;;AAIA,wBAAA,KAAA,YAAA,GAAA,EAAA,cAAA,GAAA;AAEE,2BAAA,YAAA,GAAA,EAAA;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;;;AAGA,qBAAA,YAAAA,KAAA,OAAA,MAAA;AAAA,cACF;AAAA,YACF,CAAA,EAAA,MAAA,SAAA;AACEF,4BAAA,MAAA,YAAA;;;YAGF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,kBAAA,WAAA,kBAAA;AACE,UAAA,CAAA,KAAA,QAAA;;AAEE;AAAA,MACF;;;;AAOA,iBAAA,MAAA;;MAEA,GAAA,GAAA;AAGA,YAAA,mBAAA,mBAAA,IAAA;AACA,YAAA,eAAA,mBAAA,IAAA;AAIA,eAAA,IAAA,GAAA,IAAA,KAAA,YAAA,QAAA,KAAA;AACE,cAAA,UAAA,KAAA,YAAA,CAAA;;;AAKE,cAAA,cAAA;AAEE,gBAAA,QAAA,eAAA,QAAA;AACE,sBAAA,cAAA,QAAA,cAAA,KAAA;AAAA,YACF;;AAEE,sBAAA,SAAA,QAAA,SAAA,KAAA;AAAA,YACF;AAAA;;AAIE,sBAAA;AAAA,YACF;;AAEE,sBAAA;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AAGA,YAAA,QAAA,WAAA,QAAA,QAAA,SAAA,GAAA;AACE,mBAAA,IAAA,GAAA,IAAA,QAAA,QAAA,QAAA,KAAA;;;;AAII,kBAAA,cAAA;AAEE,oBAAA,MAAA,eAAA,QAAA;AACE,wBAAA,cAAA,MAAA,cAAA,KAAA;AAAA,gBACF;;AAEE,wBAAA,SAAA,MAAA,SAAA,KAAA;AAAA,gBACF;AAAA;;AAIE,wBAAA;AAAA,gBACF;AACA,oBAAA,MAAA,UAAA,UAAA,MAAA,QAAA,GAAA;;gBAEA;AAAA,cACF;AACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,UAAA,kBAAA;;;;;QAUI,CAAA;AAAA;AAGFO,mBAAAA,YAAA,SAAA;;;;QAQE,CAAA;AAAA,MACJ;AAAA;;;AAMA,eAAA,IAAA,GAAA,IAAA,KAAA,YAAA,QAAA,KAAA;AACE,cAAA,UAAA,KAAA,YAAA,CAAA;;AAIE,kBAAA,UAAA;;;AAII,sBAAA;AAAA,YACF;;AAEE,sBAAA;AAAA,YACF;AAAA;AAGA,gBAAA,QAAA,eAAA,QAAA;AACE,sBAAA,cAAA,QAAA,cAAA,KAAA;AAAA,YACF;;AAEE,sBAAA,SAAA,QAAA,SAAA,KAAA;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AAGA,YAAA,QAAA,WAAA,QAAA,QAAA,SAAA,GAAA;AACE,mBAAA,IAAA,GAAA,IAAA,QAAA,QAAA,QAAA,KAAA;;;AAGI,oBAAA,UAAA;;;AAII,wBAAA;AAAA,gBACF;AACA,oBAAA,MAAA,UAAA,UAAA,MAAA,QAAA,GAAA;;gBAEA;AAAA;AAGA,oBAAA,MAAA,eAAA,QAAA;AACE,wBAAA,cAAA,MAAA,cAAA,KAAA;AAAA,gBACF;;AAEE,wBAAA,SAAA,MAAA,SAAA,KAAA;AAAA,gBACF;AAAA,cACF;AACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;;IAIF,qBAAA;;;;;AAMI,YAAA,QAAA,WAAA,QAAA,QAAA,QAAA;AACE,kBAAA,QAAA,QAAA,CAAA,OAAA,eAAA;AACE,iBAAA,aAAA,IAAA,MAAA,IAAA,UAAA;AAAA,UACF,CAAA;AAAA,QACF;AAAA,MACF,CAAA;;;MAIA,CAAA;AAAA;;;AAKA,UAAA,KAAA,SAAA,EAAA,cAAA,QAAA,EAAA,KAAA;;;;QAKE,cAAA;AAAA,MACF,CAAA;AAGA,UAAA,KAAA;AAAA;;AAIA,YAAA,cAAA,KAAA,YAAA,GAAA;AACA,UAAA,aAAA;AACE,aAAA,KAAA,aAAA,mBAAA,IAAA;AAAA,MACF;;AAMA,YAAA,WAAA,WAAA,EAAA,IAAA,WAAA;;;AAKE,aAAA,qBAAA,KAAA,aAAA,QAAA,GAAA,KAAA,WAAA;AACA;AAAA,MACF;;QAIE,WAAA;AAAA;AAAA,QACA,MAAA;AAAA;AAAA,QACA,OAAA;AAAA;AAAA;;;AAIF,WAAA,SAAA,WAAA,MAAA;;AAKI,YAAA,IAAA,WAAA,KAAA;AACE,eAAA,SAAA,YAAA,IAAA,IAAA;AAGA,cAAA,CAAA,KAAA;AAAA,iBAAA,eAAA,CAAA;AACA,eAAA,aAAA,QAAA,IAAA,IAAA;;;AAMA,cAAA,aAAA;;UAEA;;AAGAP,wBAAAA,MAAA,UAAA;AAAA;;UAGA,CAAA;AAAA,QACF;AAAA;;AAMA,YAAA,aAAA;;QAEA;;AAGAA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;QAEF,CAAA;AAAA,MACF,CAAA;AAAA;;IAIJ,qBAAA,MAAA,KAAA,MAAA;;AAEI,cAAA,cAAA,KAAA,YAAA,GAAA;;AAIA,YAAA,KAAA,QAAA,MAAA,QAAA,KAAA,IAAA,GAAA;AACE,oBAAA,KAAA;AAAA;;QAGF,WAAA,KAAA,QAAA,MAAA,QAAA,KAAA,IAAA,GAAA;AACE,oBAAA,KAAA;AAAA;AAEA,eAAA,SAAA,eAAA,IAAA;;QAEF;AAEA,aAAA,SAAA,YAAA,OAAA;;AAKE,gBAAA,cAAA,MAAA,UACE,MAAA,aAAA,MAAA,UAAA,UACA;AAEF,gBAAA,gBAAA,MAAA,YACE,MAAA,aAAA,MAAA,UAAA;wCAIA,MAAA,aAAA,MAAA,UAAA;AAGF,iBAAA;AAAA,YACE,GAAA;AAAA,YACA,IAAA,MAAA,MAAA;AAAA;;YAGA,QAAA;AAAA;;;cAIE,QAAA;AAAA;YAEF,WAAA,MAAA,aAAA,MAAA,UAAA;AAAA;YAEA,YAAA,SAAA,MAAA,UAAA,KAAA,SAAA,MAAA,KAAA,KAAA;AAAA;;;;YAKA,OAAA,MAAA,SAAA;AAAA;YAEA,aAAA,MAAA,eAAA;AAAA;QAEJ,CAAA;;AAIE,eAAA,KAAA,aAAA,WAAA,OAAA;AAAA;;AAKA,gBAAA,aAAA,QAAA,OAAA,OAAA,CAAA,YAAA,SAAA,EAAA,EAAA,CAAA;AAEA,eAAA,KAAA,aAAA,WAAA,CAAA,GAAA,YAAA,WAAA,CAAA,GAAA,GAAA,UAAA,CAAA;AAAA,QACF;AAGA,aAAA,KAAA,aAAA,aAAA,OAAA,CAAA;AAGA,YAAA,aAAA;;AAEE,uBAAA,SAAA,KAAA,KAAA,KAAA;AAAA,QACF,WAAA,KAAA,UAAA,QAAA;AACE,uBAAA,SAAA,KAAA,KAAA,KAAA;AAAA;AAEA,uBAAA,YAAA,eAAA;AAAA,QACF;AAIA,cAAA,qBAAA,YAAA,UAAA,YAAA,QAAA,SAAA;AAGA,cAAA,mBAAA,QAAA,SAAA,MAAA,sBAAA;;AAIA,aAAA,KAAA,aAAA,eAAA,KAAA,IAAA,YAAA,kBAAA,CAAA;;;;MAQF;AAAA;;IAIF,qBAAA;;AAGI,aAAA,SAAA,eAAA;AACA;AAAA,MACF;AAEA,UAAA,CAAA,KAAA,WAAA,KAAA,YAAA,UAAA,KAAA,eAAA,WAAA;;UAEI,MAAA,KAAA;AAAA;QAEF,CAAA;AAEA,aAAA,OAAA,KAAA,OAAA;;AAEA,aAAA,eAAA;AAAA;;UAGE,SAAA,KAAA;AAAA;;QAGF,CAAA;AAAA,MACF;AAAA;;IAIF,iBAAA,GAAA;;;;;;4DAUM,YAAA,OAAA,KAAA,eAAA,QAAA;;MAEJ;AAAA;;IAIF,kBAAA;;;AAIE,WAAA,gBAAA,WAAA,MAAA;;AAII,eAAA,qBAAA,KAAA,OAAA,GAAA,IAAA;AAAA,QACF;AAAA,MACF,GAAA,GAAA;AAAA;;IAIF,eAAA,KAAA;AACE,UAAA,CAAA;AAAA,eAAA;;;AAMI,iBAAA,WAAA;AAAA,QACF;;AAGE,iBAAA,2BAAA;AAAA,QACF;AAEA,eAAA,4BAAA;AAAA,MACF;AAEA,aAAA;AAAA;;IAIF,eAAA;AAEE,UAAA,CAAA,KAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,iBAAA;AACA;AAAA,MACF;AAEA,UAAA;;AAII,eAAA,WAAA;AAAA;AAGA,eAAA,UAAA;AAAA,QACF;AAAA,MACF,SAAA,GAAA;;;MAGA;AAAA;;;;;AAOA,UAAA;;;;MAIA,SAAA,GAAA;;;MAGA;AAAA;;;AAMA,UAAA,CAAA,KAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,iBAAA;AACA;AAAA,MACF;;AAIE,eAAA,KAAA,YAAA,SAAA;AAAA,MACF;;AAIE,YAAA;;;;AAIE;AAAA,QACF,SAAA,GAAA;AACEA,wBAAA,MAAA,MAAA,SAAA,gCAAA,kBAAA,CAAA;;QAGF;AAAA;;MAIF;AAAA;;IAIF,sBAAA;;;AAGE,UAAA;AAEEA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA,UACA,MAAA;AAAA,UACA,MAAA;AAAA,QACF,CAAA;;;AASA,aAAA,eAAA,SAAA,KAAA,SAAA,UAAA,YAAA;;AAIA,aAAA,eAAA,SAAA;;AAWA,cAAA,iBAAA,KAAA;AAGA,aAAA,oBAAA,cAAA;;AAIA,aAAA,eAAA,MAAA;AAAA,MAEF,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,CAAA;;MAEF;AAAA;;IAIF,oBAAA,gBAAA;;;AAGE,UAAA;AACE,aAAA,eAAA,OAAA,MAAA;;;AAIEA,wBAAA,MAAA,UAAA;;;QAGF,CAAA;AAEA,aAAA,eAAA,QAAA,CAAA,QAAA;;;AAIEA,wBAAA,MAAA,MAAA,SAAA,gCAAA,WAAA,GAAA;;QAEF,CAAA;AAEA,aAAA,eAAA,QAAA,MAAA;;;;;QAIA,CAAA;AAEA,aAAA,eAAA,OAAA,MAAA;;;;;QAIA,CAAA;AAEA,aAAA,eAAA,QAAA,MAAA;;;;;QAIA,CAAA;AAEA,aAAA,eAAA,UAAA,MAAA;;;;QAGA,CAAA;AAEA,aAAA,eAAA,UAAA,MAAA;;;;AAGEA,wBAAA,MAAA,UAAA;AAAA,QACF,CAAA;AAAA,MAEF,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,gCAAA,cAAA,CAAA;;MAEF;AAAA;;;;;AAOAA,oBAAA,MAAA,UAAA;;;AAKA,UAAA,OAAA,IAAA,SAAA;AACE,gBAAA,IAAA,SAAA;AAAA,UACE,KAAA;AAAA,uBAAA;AAAA;AAAA,UACA,KAAA;AAAA,uBAAA;AAAA;AAAA,UACA,KAAA;AAAA,uBAAA;AAAA;AAAA,UACA,KAAA;AAAA,uBAAA;AAAA;AAAA,UACA;AAAA,uBAAA,iBAAA,IAAA;AAAA,QACF;AAAA,MACF;;;;;;IASF,mBAAA;;;AAGE,UAAA;;MAIA,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,CAAA;AAAA,MACF;AAAA;;;AAKA,UAAA,CAAA,KAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,iBAAA;AACA;AAAA,MACF;;;AAKE,YAAA;;;UAIE;AAWA,cAAA;AACE,gBAAA,KAAA,eAAA,SAAA;;AAEE,mBAAA,eAAA;;AAEA,mBAAA,eAAA;AACA,mBAAA,eAAA;AACA,mBAAA,eAAA;AACA,mBAAA,eAAA;AACA,mBAAA,eAAA;YACF;AAAA,UACF,SAAA,GAAA;AACEA,0BAAA,MAAA,MAAA,SAAA,gCAAA,oBAAA,CAAA;AAAA,UACF;;;;;QASF,SAAA,GAAA;AACEA,wBAAA,MAAA,MAAA,SAAA,gCAAA,kBAAA,CAAA;;;;QAIF;AAAA,MACF;AAAA;;;AAMA,UAAA,CAAA,KAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,iBAAA;AACA;AAAA,MACF;;;;AAKA,WAAA,eAAA,KAAA,QAAA;AAAA;;IAIF,WAAA,SAAA;AACE,UAAA,CAAA,WAAA,MAAA,OAAA;AAAA,eAAA;AAEA,YAAA,OAAA,KAAA,MAAA,UAAA,EAAA;AACA,YAAA,OAAA,KAAA,MAAA,UAAA,EAAA;AAEA,aAAA,GAAA,KAAA,SAAA,EAAA,SAAA,GAAA,GAAA,CAAA,IAAA,KAAA,SAAA,EAAA,SAAA,GAAA,GAAA,CAAA;AAAA;;IAIF,WAAA,MAAA;;;;;;;;;IAWA,iBAAA;;AAGI,aAAA,SAAA,eAAA;AACA;AAAA,MACF;AAGA,UAAA,KAAA,eAAA,aAAA,KAAA,OAAA,GAAA;;AAEE;AAAA,MACF;;AAKA,UAAA,KAAA,SAAA,GAAA;AACEA,sBAAAA,MAAA,YAAA;AAAA;UAEE,MAAA;AAAA,QACF,CAAA;AAAA;;MAIF;;QAIE,MAAA;AAAA;AAAA,QACA,MAAA,KAAA,QAAA;AAAA,QACA,WAAA,KAAA,SAAA;AAAA;AAAA;;QAIA,MAAA,KAAA,SAAA;AAAA,QACA,IAAA,OAAA;AAAA,QACA,MAAA,OAAA,cAAA,IAAA,OAAA;AAAA,MACF,CAAA;AAGAG,iBAAAA,gBAAA,KAAA,SAAA,IAAA,MAAA;AAEI,YAAA,KAAA,SAAA,GAAA;AACEH,wBAAA,MAAA,YAAA;AAAA,QACF;;AAIA,YAAA,IAAA,WAAA,KAAA;AACE,eAAA,SAAA,YAAA,IAAA,IAAA;;;;;;;;UAWA;AAGA,gBAAA,gBAAA,KAAA,IAAA,UAAA;0CAGI,KAAA,aAAA,KAAA,UAAA,UACA;8CAGA,KAAA,aAAA,KAAA,UAAA;AAGF,kBAAA,MAAA,KAAA,OACE,KAAA,aAAA,KAAA,UAAA;AAGF,mBAAA;AAAA,cACE,GAAA;AAAA;AAAA;cAGA;AAAA,cACA;AAAA;;;gBAIE;AAAA,gBACA;AAAA;;cAGF,aAAA,SAAA,KAAA,WAAA,KAAA;AAAA;;;;;cAMA,OAAA,KAAA,SAAA;AAAA,cACA,QAAA,KAAA,UAAA;AAAA,cACA,aAAA,KAAA,eAAA;AAAA;AAAA;AAIE,sBAAA,cAAA,MAAA,UACE,MAAA,aAAA,MAAA,UAAA,UACA;AAEF,sBAAA,gBAAA,MAAA,YACE,MAAA,aAAA,MAAA,UAAA;8CAIA,MAAA,aAAA,MAAA,UAAA;AAGF,uBAAA;AAAA,kBACE,GAAA;AAAA,kBACA,IAAA,MAAA,MAAA;AAAA;;kBAGA,QAAA;AAAA;;;oBAIE,QAAA;AAAA;kBAEF,WAAA,MAAA,aAAA,MAAA,UAAA;AAAA;kBAEA,YAAA,SAAA,MAAA,UAAA,KAAA,SAAA,MAAA,KAAA,KAAA;AAAA;;;;kBAKA,OAAA,MAAA,SAAA;AAAA;kBAEA,aAAA,MAAA,eAAA;AAAA;;;;cAKJ,kBAAA,KAAA,eAAA,KAAA,UAAA,KAAA,QAAA,SAAA;AAAA;UAEJ,CAAA;AAGA,cAAA,KAAA,SAAA,GAAA;AAEE,iBAAA,cAAA;AAEA,iBAAA,UAAA,cAAA,WAAA;AAAA;;UAIF;AAGA,cAAA,KAAA,SAAA,IAAA;;;;UAIA;AAGA,cAAA,KAAA,SAAA,GAAA;AACE,kBAAA,WAAA,YAAA,KAAA,SAAA,EAAA,IAAA,KAAA,KAAA,IAAA,KAAA,IAAA;;cAEE,MAAA;AAAA,cACA,SAAA,KAAA;AAAA;cAEA,eAAA,KAAA,SAAA;AAAA;UAEJ;;;YAME,MAAA,KAAA;AAAA;YAEA,MAAA,KAAA;AAAA,YACA,MAAA,KAAA;AAAA,UACF,CAAA;AAAA;;;AAIAA,wBAAAA,MAAA,UAAA;AAAA;;UAGA,CAAA;AAAA,QACF;AAAA;AAGA,YAAA,KAAA,SAAA,GAAA;AACEA,wBAAA,MAAA,YAAA;AAAA,QACF;;;;AAKAA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;QAEF,CAAA;AAAA,MACF,CAAA;AAAA;;IAIJ,aAAA,MAAA;;;;;;AAOE,WAAA,QAAA;;;AAKA,WAAA,cAAA;AACA,WAAA,UAAA;AAEA,WAAA,eAAA;AAGA,iBAAA,MAAA;;MAEA,GAAA,GAAA;AAAA;;IAIF,YAAA,GAAA;AAEE,UAAA,KAAA,EAAA,eAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA;AAGA,QAAA,mBAAA,EAAA;AAEAA,oBAAAA,MAAA,MAAA,OAAA,gCAAA,iBAAA;AAAA,QACE,QAAA,KAAA;AAAA,QACA,QAAA,KAAA;AAAA,QACA,UAAA;AAAA;;QAGA;AAAA,MACF,CAAA;AAEA,UAAA,CAAA,KAAA,QAAA;;AAEE,aAAA,YAAA,cAAA;AACA,mBAAA,MAAA;AACEA,wBAAAA,MAAA,WAAA;AAAA;;QAGF,GAAA,GAAA;;MAEF;AAEA,UAAA,UAAA,EAAA,cAAA,WAAA,CAAA;;AAEA,UAAA,MAAA,QAAA,OAAA;AACA,UAAA,MAAA,QAAA,OAAA;;;AAIA,WAAA,KAAA,QAAA,MAAA,SAAA,QAAA,IAAA;AAGA,WAAA,YAAA;AAGA,WAAA,sBAAA;;;;;;;;;MAWA;AAGA,WAAA,UAAA,MAAA;AAEE,aAAA,YAAA;AAGA,mBAAA,MAAA;AACE,eAAA,UAAA;AAGA,cAAA,CAAA,KAAA,WAAA;AACE,iBAAA,YAAA;AAAA,UACF;AAAA,QACF,GAAA,GAAA;AAAA,MACF,CAAA;AAAA;;IAIF,aAAA,GAAA;AAEE,WAAA,EAAA,mBAAA,EAAA,gBAAA;;;AAME;AAAA,MACF;;AAIE,qBAAA,KAAA,gBAAA;;MAEF;;AAMA,WAAA,YAAA;AACA,WAAA,UAAA;AAGA,WAAA,UAAA;AAAA;;IAIF,oBAAA,aAAA;AACE,UAAA,KAAA;AAAA;;AAIA,YAAA,UAAA,YAAA;;AAIA,UAAA,CAAA,WAAA,CAAA,OAAA;AACE,aAAA,sBAAA;AACA,eAAA,KAAA,YAAA,aAAA;AAAA,MACF;AAGAA,oBAAAA,MAAA,YAAA;AAAA;QAEE,MAAA;AAAA,MACF,CAAA;AAGA,WAAA,YAAA;AACA,WAAA,UAAA;AACA,WAAA,YAAA;;;QAKE;AAAA,QACA,KAAA,KAAA,QAAA;AAAA,QACA,QAAA,KAAA,QAAA;AAAA;AAIF,UAAA,OAAA;AACE,eAAA,QAAA;AAAA,MACF;AAGAI,iBAAAA,WAAA,MAAA;AAEIJ,sBAAA,MAAA,YAAA;AAEA,YAAA,IAAA,WAAA,KAAA;AAEE,gBAAAK,eAAA,IAAA,QAAA,KAAA,yBAAA,SAAA,KAAA;AAGA,eAAA,sBAAAA,YAAA;;AAMA,cAAA,KAAA,SAAA;AACE,iBAAA,UAAA;AAAA,UACF;AAAA;AAEA,eAAA,YAAA,IAAA,OAAA,MAAA;AAAA,QACF;AAAA;AAGAL,sBAAA,MAAA,YAAA;;;;AAMA,aAAA,sBAAA;;;;AAMA,aAAA,UAAA;AAAA,MACF,CAAA;AAAA;;IAIJ,yBAAA,SAAA,UAAA;;AAEE,YAAA,SAAA,QAAA,KAAA,IAAA,CAAA,IAAA,KAAA,OAAA,EAAA,SAAA,EAAA,EAAA,OAAA,GAAA,CAAA,CAAA;AAGA,YAAA,iBAAA,UAAA,aAAA,mBAAA,eAAA,gBAAA,OAAA,MAAA,QAAA,mBAAA,aAAA,mBAAA,aAAA;AAGA,YAAA,cAAA;AAAA,QACE,IAAA;AAAA;AAAA,QACA,KAAA,KAAA;AAAA,QACA,UAAA,KAAA,gBAAA;AAAA;QAEA,SAAA,WAAA;AAAA;;QAEA,aAAA,KAAA,WAAA,oBAAA,KAAA,CAAA;AAAA,QACA,OAAA;AAAA,QACA,YAAA;AAAA;AAAA;QAEA,QAAA;AAAA;AAAA,QACA,UAAA;AAAA,QACA,aAAA;AAAA;AAAA,QACA,SAAA,CAAA;AAAA;AAAA,QACA,aAAA;AAAA;AAAA,QACA,kBAAA;AAAA;AAAA,QACA,WAAA;AAAA;AAAA,QACA,iBAAA;AAAA;AAAA;AAGFA,oBAAA,MAAA,MAAA,OAAA,gCAAA,aAAA,WAAA;AACA,aAAA;AAAA;;IAIF,sBAAA,aAAA;;;QAGI,MAAA,KAAA;AAAA;QAEA,kBAAA,KAAA,SAAA;AAAA,MACF,CAAA;AAGA,UAAA,CAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,SAAA,gCAAA,aAAA;AACA;AAAA,MACF;;AAMA,UAAA,KAAA,QAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,OAAA,gCAAA,UAAA,KAAA,IAAA;AAEA,YAAA,CAAA,KAAA,YAAA,KAAA,IAAA,EAAA,SAAA;AACE,eAAA,YAAA,KAAA,IAAA,EAAA,UAAA,CAAA;AAAA,QACF;;AAEE,eAAA,YAAA,KAAA,IAAA,EAAA,cAAA;AAAA,QACF;;AAKE,gBAAA,WAAA,KAAA,QAAA,QAAA,OAAA,EAAA;AACA,sBAAA,YAAA,KAAA;AACA,sBAAA,iBAAA;AAAA,QACF;;AAIA,aAAA,YAAA,KAAA,IAAA,EAAA;AAGA,aAAA,aAAA,IAAA,YAAA,IAAA,KAAA,YAAA,KAAA,IAAA,EAAA,QAAA,SAAA,CAAA;AAGA,aAAA,aAAA;;UAGE,MAAA,KAAA,YAAA,KAAA,IAAA,EAAA;AAAA;QAEF,CAAA;AAAA,MACF;;AAKE,YAAA,KAAA,WAAA,KAAA,YAAA,WAAA,GAAA;AACE,eAAA,UAAA;AACA,eAAA,cAAA;;QAEF;;;AAOA,aAAA,YAAA,QAAA,WAAA;AAGA,aAAA,aAAA;;UAGE,QAAA,KAAA,YAAA;AAAA;QAEF,CAAA;AAAA,MACF;AAAA;;;;AAMA,UAAA,MAAA,EAAA,cAAA,QAAA;AACA,UAAA,IAAA,EAAA,cAAA,QAAA;AACA,UAAA,YAAA,EAAA,cAAA,QAAA;AAGA,UAAA,KAAA;AAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,SAAA,SAAA,KAAA;AACE,cAAA,IAAA,SAAA;;AAGEA,0BAAAA,MAAA,YAAA;AAAA;cAEE,MAAA;AAAA,YACF,CAAA;AAGAM,uBAAAA,cAAA,SAAA,EAAA,KAAA,CAAAJ,SAAA;AACEF,4BAAA,MAAA,YAAA;;AAGA,kBAAAE,KAAA,WAAA,KAAA;;;gBAIE;AAGA,oBAAA,KAAA,IAAA;AAEE,uBAAA,YAAA,GAAA,EAAA,eAAA,oBAAA,KAAA,GAAA;AACA,uBAAA,YAAA,GAAA,EAAA,SAAA;AAAA;AAGA,sBAAA,KAAA,YAAA,GAAA,EAAA,WAAA,KAAA,YAAA,GAAA,EAAA,QAAA,SAAA,GAAA;AACE,yBAAA,YAAA,GAAA,EAAA,QAAA,CAAA,EAAA,eAAA,oBAAA,QAAA;;AAIA,wBAAA,KAAA,YAAA,GAAA,EAAA,cAAA,GAAA;AAEE,2BAAA,YAAA,GAAA,EAAA;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;;;AAGA,qBAAA,YAAAA,KAAA,OAAA,MAAA;AAAA,cACF;AAAA,YACF,CAAA,EAAA,MAAA,SAAA;AACEF,4BAAA,MAAA,YAAA;;;YAGF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,kBAAA,WAAA,kBAAA;AACE,UAAA,CAAA,KAAA,QAAA;;AAEE;AAAA,MACF;;;;AAOA,iBAAA,MAAA;;MAEA,GAAA,GAAA;AAGA,YAAA,mBAAA,mBAAA,IAAA;AACA,YAAA,eAAA,mBAAA,IAAA;AAIA,eAAA,IAAA,GAAA,IAAA,KAAA,YAAA,QAAA,KAAA;AACE,cAAA,UAAA,KAAA,YAAA,CAAA;;;AAKE,cAAA,cAAA;AAEE,gBAAA,QAAA,eAAA,QAAA;AACE,sBAAA,cAAA,QAAA,cAAA,KAAA;AAAA,YACF;;AAEE,sBAAA,SAAA,QAAA,SAAA,KAAA;AAAA,YACF;AAAA;;AAIE,sBAAA;AAAA,YACF;;AAEE,sBAAA;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AAGA,YAAA,QAAA,WAAA,QAAA,QAAA,SAAA,GAAA;AACE,mBAAA,IAAA,GAAA,IAAA,QAAA,QAAA,QAAA,KAAA;;;;AAII,kBAAA,cAAA;AAEE,oBAAA,MAAA,eAAA,QAAA;AACE,wBAAA,cAAA,MAAA,cAAA,KAAA;AAAA,gBACF;;AAEE,wBAAA,SAAA,MAAA,SAAA,KAAA;AAAA,gBACF;AAAA;;AAIE,wBAAA;AAAA,gBACF;AACA,oBAAA,MAAA,UAAA,UAAA,MAAA,QAAA,GAAA;;gBAEA;AAAA,cACF;AACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,UAAA,kBAAA;;;;;QAUI,CAAA;AAAA;AAGFO,mBAAAA,YAAA,SAAA;;;;QAQE,CAAA;AAAA,MACJ;AAAA;;;AAMA,eAAA,IAAA,GAAA,IAAA,KAAA,YAAA,QAAA,KAAA;AACE,cAAA,UAAA,KAAA,YAAA,CAAA;;AAIE,kBAAA,UAAA;;;AAII,sBAAA;AAAA,YACF;;AAEE,sBAAA;AAAA,YACF;AAAA;AAGA,gBAAA,QAAA,eAAA,QAAA;AACE,sBAAA,cAAA,QAAA,cAAA,KAAA;AAAA,YACF;;AAEE,sBAAA,SAAA,QAAA,SAAA,KAAA;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AAGA,YAAA,QAAA,WAAA,QAAA,QAAA,SAAA,GAAA;AACE,mBAAA,IAAA,GAAA,IAAA,QAAA,QAAA,QAAA,KAAA;;;AAGI,oBAAA,UAAA;;;AAII,wBAAA;AAAA,gBACF;AACA,oBAAA,MAAA,UAAA,UAAA,MAAA,QAAA,GAAA;;gBAEA;AAAA;AAGA,oBAAA,MAAA,eAAA,QAAA;AACE,wBAAA,cAAA,MAAA,cAAA,KAAA;AAAA,gBACF;;AAEE,wBAAA,SAAA,MAAA,SAAA,KAAA;AAAA,gBACF;AAAA,cACF;AACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;;IAIF,qBAAA;;;;;AAMI,YAAA,QAAA,WAAA,QAAA,QAAA,QAAA;AACE,kBAAA,QAAA,QAAA,CAAA,OAAA,eAAA;AACE,iBAAA,aAAA,IAAA,MAAA,IAAA,UAAA;AAAA,UACF,CAAA;AAAA,QACF;AAAA,MACF,CAAA;;;MAIA,CAAA;AAAA;;;AAKA,UAAA,KAAA,SAAA,EAAA,cAAA,QAAA,EAAA,KAAA;;;;QAKE,cAAA;AAAA,MACF,CAAA;AAGA,UAAA,KAAA;AAAA;;AAIA,YAAA,cAAA,KAAA,YAAA,GAAA;AACA,UAAA,aAAA;AACE,aAAA,KAAA,aAAA,mBAAA,IAAA;AAAA,MACF;;AAMA,YAAA,WAAA,WAAA,EAAA,IAAA,WAAA;;;AAKE,aAAA,qBAAA,KAAA,aAAA,QAAA,GAAA,KAAA,WAAA;AACA;AAAA,MACF;;QAIE,WAAA;AAAA;AAAA,QACA,MAAA;AAAA;AAAA,QACA,OAAA;AAAA;AAAA;;;AAIF,WAAA,SAAA,WAAA,MAAA;;AAKI,YAAA,IAAA,WAAA,KAAA;AACE,eAAA,SAAA,YAAA,IAAA,IAAA;AAGA,cAAA,CAAA,KAAA;AAAA,iBAAA,eAAA,CAAA;AACA,eAAA,aAAA,QAAA,IAAA,IAAA;;;AAMA,cAAA,aAAA;;UAEA;;AAGAP,wBAAAA,MAAA,UAAA;AAAA;;UAGA,CAAA;AAAA,QACF;AAAA;;AAMA,YAAA,aAAA;;QAEA;;AAGAA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;QAEF,CAAA;AAAA,MACF,CAAA;AAAA;;IAIJ,qBAAA,MAAA,KAAA,MAAA;;AAEI,cAAA,cAAA,KAAA,YAAA,GAAA;;AAIA,YAAA,KAAA,QAAA,MAAA,QAAA,KAAA,IAAA,GAAA;AACE,oBAAA,KAAA;AAAA;;QAGF,WAAA,KAAA,QAAA,MAAA,QAAA,KAAA,IAAA,GAAA;AACE,oBAAA,KAAA;AAAA;AAEA,eAAA,SAAA,eAAA,IAAA;;QAEF;AAEA,aAAA,SAAA,YAAA,OAAA;;AAKE,gBAAA,cAAA,MAAA,UACE,MAAA,aAAA,MAAA,UAAA,UACA;AAEF,gBAAA,gBAAA,MAAA,YACE,MAAA,aAAA,MAAA,UAAA;wCAIA,MAAA,aAAA,MAAA,UAAA;AAGF,iBAAA;AAAA,YACE,GAAA;AAAA,YACA,IAAA,MAAA,MAAA;AAAA;;YAGA,QAAA;AAAA;;;cAIE,QAAA;AAAA;YAEF,WAAA,MAAA,aAAA,MAAA,UAAA;AAAA;YAEA,YAAA,SAAA,MAAA,UAAA,KAAA,SAAA,MAAA,KAAA,KAAA;AAAA;;;;YAKA,OAAA,MAAA,SAAA;AAAA;YAEA,aAAA,MAAA,eAAA;AAAA;QAEJ,CAAA;;AAIE,eAAA,KAAA,aAAA,WAAA,OAAA;AAAA;;AAKA,gBAAA,aAAA,QAAA,OAAA,OAAA,CAAA,YAAA,SAAA,EAAA,EAAA,CAAA;AAEA,eAAA,KAAA,aAAA,WAAA,CAAA,GAAA,YAAA,WAAA,CAAA,GAAA,GAAA,UAAA,CAAA;AAAA,QACF;AAGA,aAAA,KAAA,aAAA,aAAA,OAAA,CAAA;AAGA,YAAA,aAAA;;AAEE,uBAAA,SAAA,KAAA,KAAA,KAAA;AAAA,QACF,WAAA,KAAA,UAAA,QAAA;AACE,uBAAA,SAAA,KAAA,KAAA,KAAA;AAAA;AAEA,uBAAA,YAAA,eAAA;AAAA,QACF;AAIA,cAAA,qBAAA,YAAA,UAAA,YAAA,QAAA,SAAA;AAGA,cAAA,mBAAA,QAAA,SAAA,MAAA,sBAAA;;AAIA,aAAA,KAAA,aAAA,eAAA,KAAA,IAAA,YAAA,kBAAA,CAAA;;;;MAQF;AAAA;;IAIF,qBAAA;;AAGI,aAAA,SAAA,eAAA;AACA;AAAA,MACF;AAEA,UAAA,CAAA,KAAA,WAAA,KAAA,YAAA,UAAA,KAAA,eAAA,WAAA;;UAEI,MAAA,KAAA;AAAA;QAEF,CAAA;AAEA,aAAA,OAAA,KAAA,OAAA;;AAEA,aAAA,eAAA;AAAA;;UAGE,SAAA,KAAA;AAAA;;QAGF,CAAA;AAAA,MACF;AAAA;;IAIF,iBAAA,GAAA;;;;;;4DAUM,YAAA,OAAA,KAAA,eAAA,QAAA;;MAEJ;AAAA;;IAIF,kBAAA;;;AAIE,WAAA,gBAAA,WAAA,MAAA;;AAII,eAAA,qBAAA,KAAA,OAAA,GAAA,IAAA;AAAA,QACF;AAAA,MACF,GAAA,GAAA;AAAA;;IAIF,eAAA,KAAA;AACE,UAAA,CAAA;AAAA,eAAA;;;AAMI,iBAAA,WAAA;AAAA,QACF;;AAGE,iBAAA,2BAAA;AAAA,QACF;AAEA,eAAA,4BAAA;AAAA,MACF;AAEA,aAAA;AAAA;;IAIF,eAAA;AAEE,UAAA,CAAA,KAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,iBAAA;AACA;AAAA,MACF;AAEA,UAAA;;AAII,eAAA,WAAA;AAAA;AAGA,eAAA,UAAA;AAAA,QACF;AAAA,MACF,SAAA,GAAA;;;MAGA;AAAA;;;;;AAOA,UAAA;;;;MAIA,SAAA,GAAA;;;MAGA;AAAA;;;AAMA,UAAA,CAAA,KAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,iBAAA;AACA;AAAA,MACF;;AAIE,eAAA,KAAA,YAAA,SAAA;AAAA,MACF;;AAIE,YAAA;;;;AAIE;AAAA,QACF,SAAA,GAAA;AACEA,wBAAA,MAAA,MAAA,SAAA,gCAAA,kBAAA,CAAA;;QAGF;AAAA;;MAIF;AAAA;;IAIF,sBAAA;;;AAGE,UAAA;AAEEA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA,UACA,MAAA;AAAA,UACA,MAAA;AAAA,QACF,CAAA;;;AASA,aAAA,eAAA,SAAA,KAAA,SAAA,UAAA,YAAA;;AAIA,aAAA,eAAA,SAAA;;AAWA,cAAA,iBAAA,KAAA;AAGA,aAAA,oBAAA,cAAA;;AAIA,aAAA,eAAA,MAAA;AAAA,MAEF,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,CAAA;;MAEF;AAAA;;IAIF,oBAAA,gBAAA;;;AAGE,UAAA;AACE,aAAA,eAAA,OAAA,MAAA;;;AAIEA,wBAAA,MAAA,UAAA;;;QAGF,CAAA;AAEA,aAAA,eAAA,QAAA,CAAA,QAAA;;;AAIEA,wBAAA,MAAA,MAAA,SAAA,gCAAA,WAAA,GAAA;;QAEF,CAAA;AAEA,aAAA,eAAA,QAAA,MAAA;;;;;QAIA,CAAA;AAEA,aAAA,eAAA,OAAA,MAAA;;;;;QAIA,CAAA;AAEA,aAAA,eAAA,QAAA,MAAA;;;;;QAIA,CAAA;AAEA,aAAA,eAAA,UAAA,MAAA;;;;QAGA,CAAA;AAEA,aAAA,eAAA,UAAA,MAAA;;;;AAGEA,wBAAA,MAAA,UAAA;AAAA,QACF,CAAA;AAAA,MAEF,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,gCAAA,cAAA,CAAA;;MAEF;AAAA;;;;;AAOAA,oBAAA,MAAA,UAAA;;;AAKA,UAAA,OAAA,IAAA,SAAA;AACE,gBAAA,IAAA,SAAA;AAAA,UACE,KAAA;AAAA,uBAAA;AAAA;AAAA,UACA,KAAA;AAAA,uBAAA;AAAA;AAAA,UACA,KAAA;AAAA,uBAAA;AAAA;AAAA,UACA,KAAA;AAAA,uBAAA;AAAA;AAAA,UACA;AAAA,uBAAA,iBAAA,IAAA;AAAA,QACF;AAAA,MACF;;;;;;IASF,mBAAA;;;AAGE,UAAA;;MAIA,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,CAAA;AAAA,MACF;AAAA;;;AAKA,UAAA,CAAA,KAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,iBAAA;AACA;AAAA,MACF;;;AAKE,YAAA;;;UAIE;AAWA,cAAA;AACE,gBAAA,KAAA,eAAA,SAAA;;AAEE,mBAAA,eAAA;;AAEA,mBAAA,eAAA;AACA,mBAAA,eAAA;AACA,mBAAA,eAAA;AACA,mBAAA,eAAA;AACA,mBAAA,eAAA;YACF;AAAA,UACF,SAAA,GAAA;AACEA,0BAAA,MAAA,MAAA,SAAA,gCAAA,oBAAA,CAAA;AAAA,UACF;;;;;QASF,SAAA,GAAA;AACEA,wBAAA,MAAA,MAAA,SAAA,gCAAA,kBAAA,CAAA;;;;QAIF;AAAA,MACF;AAAA;;;AAMA,UAAA,CAAA,KAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,iBAAA;AACA;AAAA,MACF;;;;AAKA,WAAA,eAAA,KAAA,QAAA;AAAA;;IAIF,WAAA,SAAA;AACE,UAAA,CAAA,WAAA,MAAA,OAAA;AAAA,eAAA;AAEA,YAAA,OAAA,KAAA,MAAA,UAAA,EAAA;AACA,YAAA,OAAA,KAAA,MAAA,UAAA,EAAA;AAEA,aAAA,GAAA,KAAA,SAAA,EAAA,SAAA,GAAA,GAAA,CAAA,IAAA,KAAA,SAAA,EAAA,SAAA,GAAA,GAAA,CAAA;AAAA;;;AAKA,UAAA,CAAA,QAAA,OAAA,SAAA;AAAA,eAAA,CAAA;AAGA,YAAA,WAAA;AACA,UAAA,KAAA,mBAAA,IAAA,QAAA,GAAA;AACE,eAAA,KAAA,mBAAA,IAAA,QAAA;AAAA,MACF;;AAGA,UAAA,YAAA;AAGA,YAAA,QAAA;AACA,UAAA;AAEA,UAAA;AACE,gBAAA,QAAA,MAAA,KAAA,IAAA,OAAA,MAAA;;;AAII,gBAAA,aAAA;;;gBAGI,MAAA;AAAA,cACF,CAAA;AAAA,YACF;AAAA,UACF;;AAIA,gBAAA,QAAA,KAAA,SAAA,IAAA,WAAA;AAEA,cAAA,SAAA,MAAA,KAAA;;;cAII,OAAA;AAAA;gBAEE,OAAA;AAAA;;gBAGA,YAAA,MAAA;AAAA,cACF;AAAA,YACF,CAAA;AAAA;;;cAKE,MAAA;AAAA,YACF,CAAA;AAAA,UACF;;QAGF;;AAIE,gBAAA,gBAAA,KAAA,UAAA,SAAA;AACA,cAAA,eAAA;;;cAGI,MAAA;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAGA,aAAA,sBAAA,UAAA,KAAA;;;AAIAA,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,KAAA;AAEA,eAAA,CAAA;AAAA;UAEE;AAAA,QACF,CAAA;AAAA,MACF;AAAA;;IAIF,sBAAA,UAAA,OAAA;;;AAII,aAAA,mBAAA,OAAA,QAAA;AAAA,MACF;AACA,WAAA,mBAAA,IAAA,UAAA,KAAA;AAAA;;IAIF,6BAAA,MAAA;AACE,UAAA,CAAA,QAAA,OAAA,SAAA;AAAA,eAAA;AAGA,YAAA,WAAA,YAAA,IAAA;AACA,UAAA,KAAA,mBAAA,IAAA,QAAA,GAAA;AACE,eAAA,KAAA,mBAAA,IAAA,QAAA;AAAA,MACF;AAEA,UAAA;AAEE,YAAA,gBAAA,KAAA,QAAA,wBAAA,CAAA,UAAA;;AAEE,cAAA,SAAA,MAAA,KAAA;AACE,mBAAA,aAAA,MAAA,GAAA,kUAAA,KAAA;AAAA,UACF;;QAEF,CAAA;;;AAKE,eAAA,mBAAA,OAAA,QAAA;AAAA,QACF;;AAGA,eAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,KAAA;;MAEF;AAAA;;;AAKA,UAAA,CAAA,WAAA,CAAA,MAAA,QAAA,OAAA;AAAA,eAAA;AAGA,YAAA,gBAAA,CAAA,GAAA,OAAA;AAGA,aAAA,cAAA,KAAA,CAAA,GAAA,MAAA;AAGE,cAAA,QAAA,IAAA,KAAA,EAAA,WAAA;AACA,cAAA,QAAA,IAAA,KAAA,EAAA,WAAA;;MAGF,CAAA;AAAA;;IAIF,cAAA,SAAA,SAAA;AACE,UAAA,CAAA,WAAA,CAAA,MAAA,QAAA,OAAA;AAAA,eAAA;AAEA,eAAA,IAAA,GAAA,IAAA,QAAA,QAAA,KAAA;;AAEI,iBAAA;AAAA,QACF;AAAA,MACF;AAEA,aAAA;AAAA;;IAIF,aAAA,OAAA;;AAGI,qBAAA,KAAA,eAAA;AAAA,MACF;AAEA,WAAA,kBAAA,WAAA,MAAA;AACE,cAAA,SAAA,MAAA,UAAA,MAAA;;AAEA,cAAA,WAAA,OAAA,aAAA,UAAA;;AAIE,eAAA,iBAAA,aAAA,QAAA;AAAA,QACF;AAAA,MACF,GAAA,GAAA;AAAA;;;;QAME;AAAA,QACA;AAAA,QACA,WAAA,KAAA,IAAA;AAAA;AAIFA,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,aAAA,GAAA;AAEEA,0BAAAA,MAAA,iBAAA;AAAA;;AAGIA,8BAAAA,MAAA,UAAA;AAAA;;gBAGA,CAAA;AAAA,cACF;AAAA,YACF,CAAA;AAAA;AAGAA,0BAAAA,MAAA,aAAA;AAAA;;YAGA,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,kBAAA;AACE,WAAA,mBAAA;;;;IAKF,qBAAA;AACE,aAAA;AAAA,QACE,cAAA,KAAA,SAAA;AAAA;;;;;IAOJ,iBAAA;;AAGI,aAAA,SAAA,eAAA;AACA;AAAA,MACF;;AAIAA,oBAAAA,MAAA,YAAA;AAAA;QAEE,MAAA;AAAA,MACF,CAAA;AAGAQ,kCAAA,KAAA,SAAA,EAAA;AAEIR,sBAAA,MAAA,YAAA;;AAGA,YAAA,IAAA,WAAA,KAAA;AACE,eAAA,SAAA,YAAA,IAAA,IAAA;AAGA,gBAAA,WAAA,IAAA,KAAA,UAAA,IAAA;AAGA,eAAA,WAAA;AAAA,YACE,GAAA,KAAA;AAAA;YAEA,WAAA,SAAA,aAAA;AAAA,cACE,KAAA,SAAA,OAAA;AAAA,cACA,UAAA,SAAA,YAAA;AAAA;;YAGF,UAAA,SAAA,SAAA,YAAA,CAAA;AAAA,YACA,OAAA,SAAA,SAAA,SAAA,CAAA;AAAA,YACA,OAAA,SAAA,SAAA,SAAA,CAAA;AAAA,YACA,QAAA,SAAA,SAAA,UAAA,CAAA;AAAA;;;;AASF,cAAA,SAAA,aAAA,SAAA,UAAA,cAAA,QAAA;;;UAGA;AAGA,eAAA,UAAA,MAAA;;UAEA,CAAA;AAGA,eAAA,eAAA;AAAA;AAEA,eAAA,YAAA,IAAA,OAAA,YAAA,IAAA;AAAA,QACF;AAAA;AAGAA,sBAAA,MAAA,YAAA;;AAEA,aAAA,YAAA,YAAA,IAAA;;MAEF,CAAA;AAAA;;;AAKF,UAAA;AACE,YAAA,CAAA,KAAA,SAAA;AAAA;kDAGE,GAAA,IAAA;AAGE,cAAA,MAAA;AAEE,kBAAA,aAAA,SAAAA,cAAA,MAAA,kBAAA,EAAA,eAAA,IAAA;AACA,kBAAA,YAAA,aAAA;AAEA,iBAAA,oBAAA,KAAA,SAAA;;cAEE,MAAA,KAAA;AAAA,cACA,MAAA;AAAA;YAEF,CAAA;AAAA,UACF;AAAA,WAEF;MACJ,SAAA,GAAA;;MAEA;AAAA;;IAIF,gBAAA;AACE,WAAA,aAAA,CAAA,KAAA;AAAA;;IAIF,aAAA,cAAA,OAAA;;;;;AAOI,iBAAA,KAAA,SAAA,OAAA,IAAA,SAAA;AACE,cAAA,OAAA,QAAA;AAAA,mBAAA;AACA,iBAAA,IAAA,OAAA;AAAA,QACF,CAAA,EAAA,OAAA,OAAA;AAAA,MACF;AAGA,UAAA,OAAA,WAAA,KAAA,cAAA;AACE,iBAAA,CAAA,YAAA;AAAA,MACF;AAEAA,oBAAAA,MAAA,aAAA;AAAA;QAEE,SAAA;AAAA,QACA,kBAAA;AAAA;UAEE,SAAA,CAAA,SAAA;AAEE,gBAAA,KAAA,aAAA,GAAA;;;;AAKMA,gCAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,UAAA,CAAA;AAAA;gBAEF,MAAA,MAAA;AACEA,gCAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,OAAA,CAAA;AAAA,gBACF;AAAA,cACF,CAAA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKA,UAAA,CAAA;AAAA;AAEAA,oBAAAA,MAAA,aAAA;AAAA,QACE,MAAA,CAAA,QAAA;AAAA,QACA,SAAA;AAAA,QACA,kBAAA;AAAA,UACE,UAAA,CAAA,MAAA;AAAA,UACA,SAAA,CAAA,SAAA;AACE,gBAAA,KAAA,aAAA,GAAA;;gBAGI,UAAA;AAAA;AAEEA,gCAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,UAAA,CAAA;AAAA;gBAEF,MAAA,MAAA;AACEA,gCAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,OAAA,CAAA;AAAA,gBACF;AAAA,cACF,CAAA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,eAAA,GAAA;AACE,WAAA,oBAAA,EAAA,OAAA;AAAA;;IAIF,kBAAA;AAEE,WAAA,aAAA;AAAA;;;;;AASE,YAAA,KAAA,YAAA,WAAA,GAAA;;;AAGE,eAAA,eAAA;AAAA,QACF;AAGA,aAAA,MAAA,aAAA;;AAGA,aAAA,MAAA,aAAA;MACF;AAAA;;;AAKA,WAAA,UAAA,MAAA;AACE,YAAA;;AAKM,gBAAA,OAAA,IAAA,CAAA,GAAA;;AAGIA,8BAAAA,MAAA,oBAAA,EAAA,GAAA,IAAA,EACE,OAAA,+BAAA,QAAA,CAAA,gCAAA,QAAA,CAAA,GAAA,EACA,OAAA;AAAA,kBACE,MAAA;AAAA,kBACA,MAAA;AAAA,mBAEF,KAAA,CAAA,YAAA;AACE,sBAAA,WAAA,QAAA,CAAA,KAAA,QAAA,CAAA,EAAA,MAAA;;;;;;;;;kBAUA;AAAA,gBACF,CAAA;AAAA,cACJ,CAAA;AAAA,YACF;AAAA,UACF,CAAA;AAAA;AAEFA,wBAAA,MAAA,MAAA,QAAA,gCAAA,eAAA,KAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/kJA,GAAG,WAAW,eAAe;"}