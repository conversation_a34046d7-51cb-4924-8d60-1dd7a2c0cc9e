<view wx:if="{{a}}"><view class="mobile-bg" bindtap="{{b}}"></view><view class="{{['mobile-mask', 'animated', n && 'slideInUp']}}"><view class="input-item"><input type="text" placeholder="{{c}}" maxlength="11" value="{{d}}" bindinput="{{e}}"/></view><view class="input-item"><input type="text" placeholder="{{f}}" maxlength="6" value="{{g}}" bindinput="{{h}}"/><button class="code" disabled="{{j}}" bindtap="{{k}}">{{i}}</button></view><view class="sub_btn" bindtap="{{m}}">{{l}}</view></view><verify wx:if="{{q}}" class="r" bindsuccess="{{p}}" u-r="verify" u-i="8a2bf81a-0" bind:__l="__l" u-p="{{q}}"></verify></view>