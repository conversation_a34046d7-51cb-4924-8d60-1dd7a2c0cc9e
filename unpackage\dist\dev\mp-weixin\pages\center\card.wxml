<view class="container"><view class="nav-bar bf8" style="{{'padding-top:' + e}}"><view class="bar-box df" style="{{'height:' + c + ';' + ('width:' + '100%')}}"><view class="bar-back df" bindtap="{{b}}"><image src="{{a}}" style="width:34rpx;height:34rpx"></image></view><view class="bar-title ohto">卡券</view></view><view class="nav-box df"><view wx:for="{{d}}" wx:for-item="item" wx:key="e" class="nav-item df" bindtap="{{item.f}}" data-idx="{{item.g}}"><text style="{{'color:' + item.b + ';' + ('font-size:' + item.c)}}">{{item.a}}</text><view style="{{'opacity:' + item.d}}" class="nav-line"></view></view></view></view><view class="content" style="{{'margin-top:' + n}}"><view wx:if="{{f}}" class="empty-box df"><image src="{{g}}" mode="aspectFill"/><view class="e1">暂无卡券</view><view class="e2">空空如也，等待探索</view></view><block wx:else><view wx:for="{{h}}" wx:for-item="item" wx:key="j" class="coupon"><image class="coupon-bg" src="{{i}}"></image><view class="coupon-item"><view class="corner-mark">{{item.a}}</view><view class="t1">{{item.b}} 元</view><view class="t2">{{item.c}}</view></view><view wx:if="{{j}}" class="validity">领取后 {{item.d}} 天内可用</view><view wx:if="{{k}}" class="validity">有效期：{{item.e}} - {{item.f}}</view><view wx:if="{{l}}" class="coupon-btn" bindtap="{{item.g}}" data-type="{{1}}" data-id="{{item.h}}">立即领取</view><view wx:if="{{item.i}}" class="coupon-err df">已使用或已失效</view></view></block><uni-load-more wx:if="{{m}}" u-i="d456c820-0" bind:__l="__l" u-p="{{m}}"></uni-load-more></view><view class="code-box df" bindtap="{{p}}"><image src="{{o}}"></image><text>兑换卡券</text></view><uni-popup wx:if="{{x}}" class="r" u-s="{{['d']}}" u-r="exchangePopup" u-i="d456c820-1" bind:__l="__l" u-p="{{x}}"><view class="popup-box"><view class="popup-top df"><view class="popup-title"><view class="t1">兑换卡券</view><view class="t2">一张兑换码仅可使用一次，兑换成功后失效</view></view><view class="popup-close df" bindtap="{{r}}"><image src="{{q}}" style="width:20rpx;height:20rpx"></image></view></view><input class="exchange-input" placeholder="请输入兑换码" cursor-spacing="30px" value="{{s}}" bindinput="{{t}}"/><view class="exchange-item"><view>1.使用兑换码请注意检查兑换码是否在有效期（客服告知或实体卡卡面为准）内，到期未使用则自动作废；</view><view>2.兑换码确认兑换后，系统会自动为您添加对应优惠券，过程不可逆；</view></view><view class="popup-btn" bindtap="{{v}}" data-type="{{2}}" data-id="{{0}}">确认兑换</view></view></uni-popup><uni-popup wx:if="{{A}}" class="r" u-s="{{['d']}}" u-r="tipsPopup" u-i="d456c820-2" bind:__l="__l" u-p="{{A}}"><view class="tips-box df"><view class="tips-item">{{y}}</view></view></uni-popup></view>