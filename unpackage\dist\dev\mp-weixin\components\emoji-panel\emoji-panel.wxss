.emoji-panel {
  width: 100%;
  height: 400rpx;
  background-color: #fff;
  border-top: 1rpx solid #f1f1f1;
  z-index: 999;
  position: relative;
}
.emoji-scroll {
  height: 100%;
}
.emoji-container {
  padding: 20rpx;
}
.recent-section {
  margin-bottom: 20rpx;
}
.section-title {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 15rpx;
  padding-left: 10rpx;
}
.recent-emojis {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}
.section-divider {
  height: 1rpx;
  background-color: #f1f1f1;
  margin: 20rpx 0;
}
.all-emojis {
  display: flex;
  flex-wrap: wrap;
  gap: 5rpx;
}
.emoji-item {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  transition: background-color 0.2s ease;
}
.emoji-item:active {
  background-color: #f5f5f5;
}
.emoji-image {
  width: 50rpx;
  height: 50rpx;
  object-fit: contain;
}

/* 预览弹窗样式 */
.emoji-preview {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease;
}
.preview-content {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx;
  margin: 40rpx;
  max-width: 500rpx;
  text-align: center;
  animation: slideUp 0.3s ease;
}
.preview-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}
.preview-name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 40rpx;
}
.preview-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

/* 小程序兼容：将SCSS嵌套语法改为普通CSS */
.preview-btn {
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  text-align: center;
  transition: all 0.2s ease;
}
.preview-btn:not(.secondary) {
  background-color: #ff4d6a;
  color: #fff;
}
.preview-btn:not(.secondary):active {
  background-color: #e6445e;
}
.preview-btn.secondary {
  background-color: #f5f5f5;
  color: #666;
}
.preview-btn.secondary:active {
  background-color: #e8e8e8;
}

/* 动画 */
@keyframes fadeIn {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes slideUp {
from {
    opacity: 0;
    transform: translateY(50rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
@media (prefers-color-scheme: dark) {
.emoji-panel {
    background-color: #1a1a1a;
    border-top-color: #333;
}
.section-title {
    color: #666;
}
.section-divider {
    background-color: #333;
}
.emoji-item:active {
    background-color: #333;
}
.preview-content {
    background-color: #333;
}
.preview-name {
    color: #fff;
}
}