
page {
  background: #f8f8f8;
  padding: 0 30rpx;
  box-sizing: border-box;
  overflow-x: hidden;
}
.title-box {
	padding: 20rpx 0;
	font-size: 40rpx;
	font-weight: bold;
}
.table {
	padding: 30rpx 0;
	color: #999;
	font-size: 24rpx;
	font-weight: 500;
}
.list-box {
  width: 100%;
  border-radius: 24rpx;
  overflow: hidden;
  box-sizing: border-box; 
  margin-bottom: 20rpx;
}
.list-box .list {
	margin: 0;
	padding: 0;
	width: 100%;
	background: #fff!important;
	border-radius: 0;
}
.list-box .list:last-child {
  border-bottom: none;
}
.list-box .list .icon {
  margin: 0 30rpx;
  width: 38rpx;
  height: 38rpx;
}
.list-box .list-item {
  width: calc(100% - 98rpx);
  padding: 30rpx 30rpx 30rpx 0;
  justify-content: space-between;
  border-bottom: 1px solid #f8f8f8;
}
.list-item .title {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.list-item .title .t1 {
  font-size: 24rpx;
  font-weight: 500;
  line-height: 48rpx;
}
.list-item .title .t2 {
  color: #999;
  font-size: 18rpx;
  font-weight: 300;
  line-height: 18rpx;
}
.list-box .list-item image {
  width: 24rpx;
  height: 24rpx;
  transform: rotate(-90deg);
}
.description-box {
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.description-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.description-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}
.version {
  padding: 60rpx 0;
  flex-direction: column;
  justify-content: center;
}
.version image {
  margin-right: 10rpx;
  width: 20rpx;
  height: 20rpx;
}
.version text {
  color: #999;
  font-size: 18rpx;
}
.tips-box {
  padding: 20rpx 30rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  justify-content: center;
}
.tips-box .tips-item {
  color: #fff;
  font-size: 28rpx;
  font-weight: 700;
}
.df {
  display: flex;
  align-items: center;
}
