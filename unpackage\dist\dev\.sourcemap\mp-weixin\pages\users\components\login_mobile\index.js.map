{"version": 3, "file": "index.js", "sources": ["pages/users/components/login_mobile/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvcGFnZXMvdXNlcnMvY29tcG9uZW50cy9sb2dpbl9tb2JpbGUvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n\t<view v-if=\"isUp\">\r\n\t\t<view class=\"mobile-bg\" @click=\"close\"></view>\r\n\t\t<view class=\"mobile-mask animated\" :class=\"{slideInUp:isUp}\">\r\n\t\t\t<view class=\"input-item\">\r\n\t\t\t\t<input type=\"text\" v-model=\"account\" :placeholder=\"$t(`输入手机号`)\" maxlength=\"11\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"input-item\">\r\n\t\t\t\t<input type=\"text\" v-model=\"codeNum\" :placeholder=\"$t(`输入验证码`)\" maxlength=\"6\" />\r\n\t\t\t\t<button class=\"code\" :disabled=\"disabled\" @click=\"code\">{{text}}</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"sub_btn\" @click=\"loginBtn\">{{$t(`立即登录`)}}</view>\r\n\t\t</view>\r\n\r\n\t\t<Verify @success=\"success\" :captchaType=\"captchaType\" :imgSize=\"{ width: '330px', height: '155px' }\"\r\n\t\t\tref=\"verify\"></Verify>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst app = getApp();\r\n\timport sendVerifyCode from \"@/mixins/SendVerifyCode\";\r\n\timport Routine from '@/libs/routine';\r\n\timport Verify from '../verify/index.vue';\r\n\timport Cache from '@/utils/cache';\r\n\timport {\r\n\tloginMobile,\r\n\tregisterVerify,\r\n\tgetCodeApi,\r\n\tgetUserInfo,\r\n\tphoneSilenceAuth,\r\n\tphoneWxSilenceAuth\r\n} from \"@/api/user\";\r\nimport {\r\n\tgetUserSocialInfo\r\n} from '@/api/social.js';\r\n\timport {\r\n\t\tbindingPhone\r\n\t} from '@/api/api.js'\r\n\timport {\r\n\t\tsilenceAuth\r\n\t} from '@/api/public.js'\r\n\timport { useUserStore } from '@/stores/user.js'\r\n\texport default {\r\n\t\tname: 'login_mobile',\r\n\t\tcomponents: {\r\n\t\t\tVerify\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tisUp: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false,\r\n\t\t\t},\r\n\t\t\tcanClose: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true,\r\n\t\t\t},\r\n\t\t\tauthKey: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '',\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuserStore: useUserStore(),\r\n\t\t\t\tkeyCode: '',\r\n\t\t\t\taccount: '',\r\n\t\t\t\tcodeNum: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tmixins: [sendVerifyCode],\r\n\t\tmounted() {\r\n\t\t\tthis.getCode();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsuccess(data) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthis.$refs.verify.hide()\r\n\t\t\t\tgetCodeApi().then(res => {\r\n\t\t\t\t\tregisterVerify({\r\n\t\t\t\t\t\tphone: that.account,\r\n\t\t\t\t\t\tkey: res.data.key,\r\n\t\t\t\t\t\tcaptchaType: this.captchaType,\r\n\t\t\t\t\t\tcaptchaVerification: data.captchaVerification\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: res.msg\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.sendCode();\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 获取验证码\r\n\t\t\tcode() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (!that.account) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请填写手机号码`)\r\n\t\t\t\t});\r\n\t\t\t\tif (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.account)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请输入正确的手机号码`)\r\n\t\t\t\t});\r\n\t\t\t\tthis.$refs.verify.show();\r\n\t\t\t},\r\n\t\t\t// 获取验证码api\r\n\t\t\tgetCode() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tgetCodeApi().then(res => {\r\n\t\t\t\t\tthat.keyCode = res.data.key;\r\n\t\t\t\t}).catch(res => {\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tclose(new_user) {\r\n\t\t\t\tif (this.canClose) {\r\n\t\t\t\t\tthis.$emit('close', new_user)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 登录\r\n\t\t\tloginBtn() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tif (!that.account) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请填写手机号码`)\r\n\t\t\t\t});\r\n\t\t\t\tif (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.account)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请输入正确的手机号码`)\r\n\t\t\t\t});\r\n\t\t\t\tif (!that.codeNum) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请填写验证码`)\r\n\t\t\t\t});\r\n\t\t\t\tif (!/^[\\w\\d]+$/i.test(that.codeNum)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请输入正确的验证码`)\r\n\t\t\t\t});\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: that.$t(`正在登录中`)\r\n\t\t\t\t});\r\n\t\t\t\tRoutine.getCode()\r\n\t\t\t\t\t.then(code => {\r\n\t\t\t\t\t\tthis.phoneSilenceAuth(code);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(error => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (!that.account) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请填写手机号码`)\r\n\t\t\t\t});\r\n\t\t\t\tif (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.account)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请输入正确的手机号码`)\r\n\t\t\t\t});\r\n\t\t\t\tif (!that.codeNum) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请填写验证码`)\r\n\t\t\t\t});\r\n\t\t\t\tif (!/^[\\w\\d]+$/i.test(that.codeNum)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请输入正确的验证码`)\r\n\t\t\t\t});\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: that.$t(`正在登录中`)\r\n\t\t\t\t});\r\n\t\t\t\tif (!this.authKey) {\r\n\t\t\t\t\tlet key = this.$Cache.get('snsapiKey');\r\n\t\t\t\t\tthis.phoneAuth(key)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.phoneAuth(this.authKey)\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tphoneAuth(key) {\r\n\t\t\t\tphoneWxSilenceAuth({\r\n\t\t\t\t\tspid: app.globalData.spid,\r\n\t\t\t\t\tspread: app.globalData.code,\r\n\t\t\t\t\tphone: this.account,\r\n\t\t\t\t\tcaptcha: this.codeNum,\r\n\t\t\t\t\tkey\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\t// 使用Pinia更新登录状态\r\n\t\t\t\t\tthis.userStore.setToken({\r\n\t\t\t\t\t\ttoken: res.data.token,\r\n\t\t\t\t\t\ttime: res.data.expires_time - this.$Cache.time()\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.getUserInfo();\r\n\t\t\t\t}).catch(error => {\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\ttitle: error\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// #ifdef MP\r\n\t\t\tphoneSilenceAuth(code) {\r\n\t\t\t\tlet self = this\r\n\t\t\t\tphoneSilenceAuth({\r\n\t\t\t\t\tcode: code,\r\n\t\t\t\t\tspread_spid: app.globalData.spid,\r\n\t\t\t\t\tspread_code: app.globalData.code,\r\n\t\t\t\t\tphone: this.account,\r\n\t\t\t\t\tcaptcha: this.codeNum\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\t// 使用Pinia更新登录状态\r\n\t\t\t\t\tthis.userStore.setToken({\r\n\t\t\t\t\t\ttoken: res.data.token,\r\n\t\t\t\t\t\ttime: res.data.expires_time - this.$Cache.time()\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.getUserInfo(res.data.new_user);\r\n\t\t\t\t}).catch(error => {\r\n\t\t\t\t\tself.$util.Tips({\r\n\t\t\t\t\t\ttitle: error\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t/**\r\n\t\t\t * 获取个人用户信息\r\n\t\t\t */\r\n\t\t\tgetUserInfo: function(new_user) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\t// 先获取基础用户信息\r\n\t\t\t\tgetUserInfo().then(res => {\r\n\t\t\t\t\tthat.userInfo = res.data\r\n\t\t\t\t\tthat.userStore.setUid(res.data.uid);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 再获取用户社交信息（拓展信息）\r\n\t\t\t\t\tgetUserSocialInfo()\r\n\t\t\t\t\t\t.then((socialRes) => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t// 更新用户社交信息到Pinia\r\n\t\t\t\t\t\t\tif (socialRes.data) {\r\n\t\t\t\t\t\t\t\tthat.userStore.updateUserInfo(socialRes.data);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\t\t\tif (!new_user) {\r\n\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: that.$t(`登录成功`),\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\ttab: 3\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: that.$t(`登录成功`),\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.close(new_user || 0)\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\t\tthat.$emit('wechatPhone', true)\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(() => {\r\n\t\t\t\t\t\t\t// 即使社交信息获取失败，仍然继续\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\t\t\tif (!new_user) {\r\n\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: that.$t(`登录成功`),\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\ttab: 3\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: that.$t(`登录成功`),\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthat.close(new_user || 0)\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\t\tthat.$emit('wechatPhone', true)\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"stylus\">\r\n\t.mobile-bg {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: rgba(0, 0, 0, 0.5);\r\n\t}\r\n\r\n\t.mobile-mask {\r\n\t\tz-index: 20;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\twidth: 100%;\r\n\t\tpadding: 67rpx 30rpx;\r\n\t\tbackground: #fff;\r\n\r\n\t\t.input-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 86rpx;\r\n\t\t\tmargin-bottom: 38rpx;\r\n\r\n\t\t\tinput {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tpadding-left: 40rpx;\r\n\t\t\t\tborder-radius: 43rpx;\r\n\t\t\t\tborder: 1px solid #DCDCDC;\r\n\t\t\t}\r\n\r\n\t\t\t.code {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\twidth: 220rpx;\r\n\t\t\t\theight: 86rpx;\r\n\t\t\t\tmargin-left: 30rpx;\r\n\t\t\t\tbackground: var(--view-minorColorT);\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: var(--view-theme);\r\n\t\t\t\tborder-radius: 43rpx;\r\n\r\n\t\t\t\t&[disabled] {\r\n\t\t\t\t\tbackground: rgba(0, 0, 0, 0.05);\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.sub_btn {\r\n\t\t\twidth: 690rpx;\r\n\t\t\theight: 86rpx;\r\n\t\t\tline-height: 86rpx;\r\n\t\t\tmargin-top: 60rpx;\r\n\t\t\tbackground: var(--view-theme);\r\n\t\t\tborder-radius: 43rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\t}\r\n\r\n\t.animated {\r\n\t\tanimation-duration: .4s\r\n\t}\r\n</style>\r\n", "import Component from 'D:/uniapp/vue3/pages/users/components/login_mobile/index.vue'\nwx.createComponent(Component)"], "names": ["useUserStore", "sendVerifyCode", "getCodeApi", "registerVerify", "res", "uni", "Routine", "phoneWxSilenceAuth", "phoneSilenceAuth", "getUserInfo", "getUserSocialInfo"], "mappings": ";;;;;;;;;AAoBC,MAAM,MAAM,OAAM;AAGlB,eAAe,MAAW;AAoB1B,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,WAAWA,YAAAA,aAAc;AAAA,MACzB,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,QAAQ,CAACC,sBAAAA,cAAc;AAAA,EACvB,UAAU;AACT,SAAK,QAAO;AAAA,EACZ;AAAA,EACD,SAAS;AAAA,IACR,QAAQ,MAAM;AACb,UAAI,OAAO;AACX,WAAK,MAAM,OAAO,KAAK;AACvBC,0BAAY,EAAC,KAAK,SAAO;AACxBC,gCAAe;AAAA,UACd,OAAO,KAAK;AAAA,UACZ,KAAK,IAAI,KAAK;AAAA,UACd,aAAa,KAAK;AAAA,UAClB,qBAAqB,KAAK;AAAA,SAC1B,EAAE,KAAK,CAAAC,SAAO;AACd,eAAK,MAAM,KAAK;AAAA,YACf,OAAOA,KAAI;AAAA,UACZ,CAAC;AACD,eAAK,SAAQ;AAAA,QACd,CAAC,EAAE,MAAM,SAAO;AACf,iBAAO,KAAK,MAAM,KAAK;AAAA,YACtB,OAAO;AAAA,WACP;AAAA,SACD;AAAA,OACD;AAAA,IACD;AAAA;AAAA,IAED,OAAO;AACN,UAAI,OAAO;AACX,UAAI,CAAC,KAAK;AAAS,eAAO,KAAK,MAAM,KAAK;AAAA,UACzC,OAAO,KAAK,GAAG,SAAS;AAAA,QACzB,CAAC;AACD,UAAI,CAAC,2BAA2B,KAAK,KAAK,OAAO;AAAG,eAAO,KAAK,MAAM,KAAK;AAAA,UAC1E,OAAO,KAAK,GAAG,YAAY;AAAA,QAC5B,CAAC;AACD,WAAK,MAAM,OAAO;IAClB;AAAA;AAAA,IAED,UAAU;AACT,UAAI,OAAO;AACXF,0BAAY,EAAC,KAAK,SAAO;AACxB,aAAK,UAAU,IAAI,KAAK;AAAA,MACzB,CAAC,EAAE,MAAM,SAAO;AACf,aAAK,MAAM,KAAK;AAAA,UACf,OAAO;AAAA,QACR,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA,IACD,MAAM,UAAU;AACf,UAAI,KAAK,UAAU;AAClB,aAAK,MAAM,SAAS,QAAQ;AAAA,MAC7B;AAAA,IACA;AAAA;AAAA,IAED,WAAW;AACV,UAAI,OAAO;AAEX,UAAI,CAAC,KAAK;AAAS,eAAO,KAAK,MAAM,KAAK;AAAA,UACzC,OAAO,KAAK,GAAG,SAAS;AAAA,QACzB,CAAC;AACD,UAAI,CAAC,2BAA2B,KAAK,KAAK,OAAO;AAAG,eAAO,KAAK,MAAM,KAAK;AAAA,UAC1E,OAAO,KAAK,GAAG,YAAY;AAAA,QAC5B,CAAC;AACD,UAAI,CAAC,KAAK;AAAS,eAAO,KAAK,MAAM,KAAK;AAAA,UACzC,OAAO,KAAK,GAAG,QAAQ;AAAA,QACxB,CAAC;AACD,UAAI,CAAC,aAAa,KAAK,KAAK,OAAO;AAAG,eAAO,KAAK,MAAM,KAAK;AAAA,UAC5D,OAAO,KAAK,GAAG,WAAW;AAAA,QAC3B,CAAC;AACDG,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,KAAK,GAAG,OAAO;AAAA,MACvB,CAAC;AACDC,mBAAAA,QAAQ,QAAQ,EACd,KAAK,UAAQ;AACb,aAAK,iBAAiB,IAAI;AAAA,OAC1B,EACA,MAAM,WAAS;AACfD,sBAAG,MAAC,YAAW;AAAA,MAChB,CAAC;AAAA,IAyBF;AAAA,IACD,UAAU,KAAK;AACdE,kCAAmB;AAAA,QAClB,MAAM,IAAI,WAAW;AAAA,QACrB,QAAQ,IAAI,WAAW;AAAA,QACvB,OAAO,KAAK;AAAA,QACZ,SAAS,KAAK;AAAA,QACd;AAAA,OACA,EAAE,KAAK,SAAO;AAEd,aAAK,UAAU,SAAS;AAAA,UACvB,OAAO,IAAI,KAAK;AAAA,UAChB,MAAM,IAAI,KAAK,eAAe,KAAK,OAAO,KAAK;AAAA,QAChD,CAAC;AACD,aAAK,YAAW;AAAA,MACjB,CAAC,EAAE,MAAM,WAAS;AACjBF,sBAAAA,MAAI,YAAY;AAChB,aAAK,MAAM,KAAK;AAAA,UACf,OAAO;AAAA,SACP;AAAA,OACD;AAAA,IACD;AAAA,IAED,iBAAiB,MAAM;AACtB,UAAI,OAAO;AACXG,gCAAiB;AAAA,QAChB;AAAA,QACA,aAAa,IAAI,WAAW;AAAA,QAC5B,aAAa,IAAI,WAAW;AAAA,QAC5B,OAAO,KAAK;AAAA,QACZ,SAAS,KAAK;AAAA,OACd,EAAE,KAAK,SAAO;AAEd,aAAK,UAAU,SAAS;AAAA,UACvB,OAAO,IAAI,KAAK;AAAA,UAChB,MAAM,IAAI,KAAK,eAAe,KAAK,OAAO,KAAK;AAAA,QAChD,CAAC;AACD,aAAK,YAAY,IAAI,KAAK,QAAQ;AAAA,MACnC,CAAC,EAAE,MAAM,WAAS;AACjB,aAAK,MAAM,KAAK;AAAA,UACf,OAAO;AAAA,SACP;AAAA,OACD;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,aAAa,SAAS,UAAU;AAC/B,UAAI,OAAO;AAEXC,2BAAa,EAAC,KAAK,SAAO;AACzB,aAAK,WAAW,IAAI;AACpB,aAAK,UAAU,OAAO,IAAI,KAAK,GAAG;AAGlCC,qCAAkB,EAChB,KAAK,CAAC,cAAc;AACpBL,wBAAG,MAAC,YAAW;AAEf,cAAI,UAAU,MAAM;AACnB,iBAAK,UAAU,eAAe,UAAU,IAAI;AAAA,UAC7C;AAGA,cAAI,CAAC,UAAU;AACd,iBAAK,MAAM,KAAK;AAAA,cACf,OAAO,KAAK,GAAG,MAAM;AAAA,cACrB,MAAM;AAAA,YACP,GAAG;AAAA,cACF,KAAK;AAAA,aACL;AAAA,iBACK;AACN,iBAAK,MAAM,KAAK;AAAA,cACf,OAAO,KAAK,GAAG,MAAM;AAAA,cACrB,MAAM;AAAA,aACN;AAAA,UACF;AACA,eAAK,MAAM,YAAY,CAAC;AAAA,SAKxB,EACA,MAAM,MAAM;AAEZA,wBAAG,MAAC,YAAW;AAEf,cAAI,CAAC,UAAU;AACd,iBAAK,MAAM,KAAK;AAAA,cACf,OAAO,KAAK,GAAG,MAAM;AAAA,cACrB,MAAM;AAAA,YACP,GAAG;AAAA,cACF,KAAK;AAAA,aACL;AAAA,iBACK;AACN,iBAAK,MAAM,KAAK;AAAA,cACf,OAAO,KAAK,GAAG,MAAM;AAAA,cACrB,MAAM;AAAA,aACN;AAAA,UACF;AACA,eAAK,MAAM,YAAY,CAAC;AAAA,QAKzB,CAAC;AAAA,MACH,CAAC;AAAA,IACD;AAAA,EACF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1RD,GAAG,gBAAgB,SAAS;"}