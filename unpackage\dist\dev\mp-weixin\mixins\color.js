"use strict";
const common_vendor = require("../common/vendor.js");
const colors = {
  data() {
    return {
      colorStyle: "",
      colorStatus: ""
    };
  },
  created() {
    this.colorStyle = common_vendor.index.getStorageSync("viewColor");
    common_vendor.index.$on("ok", (data, status) => {
      this.colorStyle = data;
      this.colorStatus = status;
    });
  },
  methods: {}
};
exports.colors = colors;
//# sourceMappingURL=../../.sourcemap/mp-weixin/mixins/color.js.map
