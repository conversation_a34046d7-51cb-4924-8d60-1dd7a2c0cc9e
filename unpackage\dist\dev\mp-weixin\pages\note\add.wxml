<view class="container"><view class="nav-box" style="{{'padding-top:' + h}}"><view class="nav-item df" style="{{'width:' + '100%' + ';' + ('height:' + g)}}"><view class="nav-back df" bindtap="{{b}}"><image src="{{a}}" style="width:34rpx;height:34rpx"></image></view><view class="nav-publish" style="{{f}}"><view class="{{['publish-btn', d && 'publish-btn-disabled']}}" bindtap="{{e}}">{{c}}</view></view></view></view><view class="content-box" style="{{'margin-top:' + ay}}"><view class="textarea-container"><block wx:if="{{r0}}"><textarea class="textarea-input" show-confirm-bar="{{false}}" maxlength="2000" cursor-spacing="30" auto-height placeholder="填写笔记内容" bindinput="{{i}}" bindfocus="{{j}}" bindblur="{{k}}" focus="{{l}}" value="{{m}}">
        </textarea></block><view class="{{['text-overlay', p && 'overlay-focused']}}"><view class="formatted-content"><text wx:for="{{n}}" wx:for-item="segment" wx:key="b" class="{{segment.c}}">{{segment.a}}</text><text wx:if="{{o}}" class="placeholder-text">填写笔记内容</text></view></view><view wx:if="{{q}}" class="mention-tags"><view wx:for="{{r}}" wx:for-item="user" wx:key="c" class="mention-tag" bindtap="{{user.d}}"><image src="{{user.a}}" class="mention-avatar" mode="aspectFill"></image><text class="mention-nickname">@{{user.b}}</text><text class="mention-delete">×</text></view></view></view><scroll-view wx:if="{{s}}" scroll-x="true" class="scroll-box"><view class="scroll-item"><block wx:if="{{t}}"><view wx:for="{{v}}" wx:for-item="item" wx:key="i" class="img-box"><image src="{{item.a}}" mode="aspectFill" bindtap="{{item.b}}" data-i="{{item.c}}"></image><view class="del df" bindtap="{{item.d}}"><image src="{{w}}"></image></view><view wx:if="{{x}}" class="sort df"><view class="sort-item df" style="{{'color:' + item.e}}" bindtap="{{item.f}}"> ← </view><view class="sort-item df" style="{{'color:' + item.g}}" bindtap="{{item.h}}"> → </view></view></view><view class="img-box file-add df" bindtap="{{z}}"><image src="{{y}}"></image><text>选择图片</text></view><view style="flex-shrink:0;width:20rpx;height:20rpx"></view></block><view wx:if="{{A}}" class="video-box df"><view wx:if="{{B}}" class="video-item file-add df" bindtap="{{D}}"><image src="{{C}}"></image><text>选择视频，建议时长</text><text>60秒以内</text></view><block wx:else><view class="video-item"><video src="{{E}}" custom-cache="{{false}}" controls="{{true}}" show-center-play-btn="{{true}}" show-play-btn="{{true}}" show-fullscreen-btn="{{true}}" show-progress="{{true}}" enable-progress-gesture="{{true}}" object-fit="{{'contain'}}" binderror="{{F}}" bindload="{{G}}" style="width:100%;height:100%;border-radius:8rpx"></video><view class="del df" bindtap="{{I}}"><image src="{{H}}"></image></view></view><view wx:if="{{J}}" class="video-item"><image src="{{K}}" mode="aspectFill"></image><view class="del df" bindtap="{{M}}"><image src="{{L}}"></image></view></view><view wx:else class="video-item file-add df" bindtap="{{O}}"><image src="{{N}}"></image><text>添加封面</text></view></block></view><block wx:if="{{P}}"><view wx:if="{{Q}}" class="audio-box file-add df" bindtap="{{S}}"><image src="{{R}}"></image><text>上传音频，支持格式mp3和m4a</text></view><view wx:else class="file-audio df"><image class="audio-bg" src="{{T}}"></image><view class="audio-mb"></view><view wx:if="{{U}}" class="audio-left"><image src="{{V}}"></image><image class="icon xwb" src="{{W}}"></image><view class="del df" bindtap="{{Y}}"><image src="{{X}}"></image></view></view><view wx:else class="audio-left file-add df" bindtap="{{aa}}"><image src="{{Z}}"></image><text>上传封面</text></view><view style="width:calc(100% - 268rpx)"><input class="audio-t1" placeholder="点击填写音频名称" placeholder-class="aph" value="{{ab}}" bindinput="{{ac}}"/><input class="audio-t2" placeholder="点击填写音频信息" placeholder-class="aph" value="{{ad}}" bindinput="{{ae}}"/></view><view class="del df" bindtap="{{ag}}"><image src="{{af}}"></image></view></view></block></view></scroll-view><vote-component wx:if="{{ah}}" bindvoteDelete="{{ai}}" u-i="5e5002e4-0" bind:__l="__l" u-p="{{aj}}"/><view class="content-item df"><view class="{{['tag-item', 'df', ap]}}" style="border-radius:64rpx;margin-right:16rpx" bindtap="{{aq}}"><image class="icon location-icon" src="{{ak}}" style="{{'border-radius:' + '50%' + ';' + ('background:' + '#f8f8f8') + ';' + ('opacity:' + al)}}"></image><text style="{{'color:' + an}}" title="{{ao}}" class="location-text">{{am}}</text></view><view class="{{['tag-item', 'df', aw]}}" style="border-radius:64rpx" bindtap="{{ax}}"><image class="icon" src="{{ar}}" style="{{'border-radius:' + '50%' + ';' + ('background:' + '#f8f8f8') + ';' + ('opacity:' + as)}}"></image><text style="{{'color:' + av}}">{{at}}</text></view></view></view><view wx:if="{{az}}" class="tags-box"><view wx:if="{{aA}}" class="tag-item df" bindtap="{{aD}}" style="border-radius:64rpx"><image class="icon" src="{{aB}}" style="border-radius:50%;background:#f8f8f8"></image><text style="color:#000">{{aC}}</text></view><view wx:for="{{aE}}" wx:for-item="v" wx:key="c" class="tag-item df" style="border-radius:64rpx"><image class="icon" src="{{v.a}}" mode="aspectFill" style="border-radius:50%;background:#f8f8f8"></image><text style="color:#000">{{v.b}}</text></view><view wx:for="{{aF}}" wx:for-item="topic" wx:key="d" class="tag-item df" style="border-radius:64rpx"><image class="icon" src="{{topic.a}}" mode="aspectFill" style="border-radius:50%;background:#f8f8f8"></image><text style="color:#000">#{{topic.b}}#</text><view class="tag-delete" bindtap="{{topic.c}}"><text>×</text></view></view></view><view style="{{'bottom:' + br}}" class="{{['media-toolbar-fixed', bs && 'keyboard-active']}}"><view class="toolbar-box"><view class="toolbar-item" bindtap="{{aH}}"><image src="{{aG}}" mode="aspectFit"></image></view><view class="toolbar-item" bindtap="{{aJ}}"><image src="{{aI}}" mode="aspectFit"></image></view><view class="toolbar-item" bindtap="{{aL}}"><image src="{{aK}}" mode="aspectFit"></image></view><view class="toolbar-item" bindtap="{{aN}}"><image src="{{aM}}" mode="aspectFit"></image></view><view class="{{['toolbar-item', aP]}}" bindtap="{{aQ}}"><image src="{{aO}}" mode="aspectFit"></image></view><view class="toolbar-item" bindtap="{{aS}}"><image src="{{aR}}" mode="aspectFit"></image></view></view><view class="{{['more-options-panel', bc && 'expanded']}}"><view class="more-options-row"><view class="toolbar-item" bindtap="{{aU}}"><image src="{{aT}}" mode="aspectFit"></image><text class="toolbar-label">音频</text></view><view class="toolbar-item" bindtap="{{aW}}"><image src="{{aV}}" mode="aspectFit"></image><text class="toolbar-label">投票</text></view><view class="toolbar-item" bindtap="{{aY}}"><image src="{{aX}}" mode="aspectFit"></image><text class="toolbar-label">活动</text></view><view wx:if="{{aZ}}" class="toolbar-item" bindtap="{{bb}}"><image src="{{ba}}" mode="aspectFit"></image><text class="toolbar-label">商品</text></view></view></view><emoji-panel wx:if="{{bd}}" bindselect="{{be}}" bindselectGif="{{bf}}" binddelete="{{bg}}" bindsend="{{bh}}" u-i="5e5002e4-1" bind:__l="__l" u-p="{{bi}}"></emoji-panel><view wx:if="{{bj}}" class="mention-user-panel" style="{{'bottom:' + bq}}"><view class="popup-close df" bindtap="{{bk}}"><text>×</text></view><view wx:if="{{bl}}" class="empty-mention-users"><text wx:if="{{bm}}">暂无此用户，请核对</text><text wx:else>暂无关注用户</text></view><view wx:if="{{bn}}" class="loading-mention-users"><text>加载中...</text></view><scroll-view wx:if="{{bo}}" class="mention-user-scroll" scroll-x="true" show-scrollbar="{{false}}"><view class="mention-user-list-horizontal"><view wx:for="{{bp}}" wx:for-item="user" wx:key="c" class="mention-user-card" bindtap="{{user.d}}"><image src="{{user.a}}" class="mention-user-avatar" mode="aspectFill"></image><text class="mention-user-nickname">{{user.b}}</text></view></view></scroll-view></view></view><uni-popup wx:if="{{bF}}" class="r" u-s="{{['d']}}" u-r="shopPopup" u-i="5e5002e4-2" bind:__l="__l" u-p="{{bF}}"><view class="popup-box"><view class="popup-top df"><view class="popup-title"><view class="t1">选择商品</view><view class="t2">轻触下方名称以选择相应商品</view></view><view class="popup-close df" bindtap="{{bv}}"><image src="{{bt}}" style="width:20rpx;height:20rpx"></image></view></view><view class="popup-search df"><input bindconfirm="{{bw}}" focus="true" confirm-type="search" placeholder="输入关键词搜索商品" placeholder-class="search-ph" value="{{bx}}" bindinput="{{by}}"/><view class="search-btn" bindtap="{{bz}}">搜索</view></view><view class="popup-scroll"><view wx:if="{{bA}}" class="empty-box df"><image src="{{bB}}"/><view class="e1">暂无商品</view><view class="e2">正在为您制造更多美好的商品</view></view><view class="circle-box"><view wx:for="{{bC}}" wx:for-item="item" wx:key="c" class="{{['circle-item', 'df', item.d]}}" style="border-radius:16rpx" data-idx="{{item.e}}" bindtap="{{item.f}}"><image src="{{item.a}}" style="border-radius:8rpx" mode="aspectFill"></image><text>{{item.b}}</text></view></view></view><view class="popup-btn" bindtap="{{bD}}">确认保存</view></view></uni-popup><uni-popup wx:if="{{bN}}" class="r" u-s="{{['d']}}" u-r="upPopup" u-i="5e5002e4-3" bind:__l="__l" u-p="{{bN}}"><view class="popup-box"><view class="popup-top df"><view class="popup-title"><view class="t1">选择一个圈子</view><view class="t2">轻触下方名称以选择相应圈子</view></view><view class="popup-close df" bindtap="{{bH}}"><image src="{{bG}}" style="width:20rpx;height:20rpx"></image></view></view><view class="popup-scroll"><view wx:if="{{bI}}" class="empty-box df"><image src="{{bJ}}"/><view class="e1">暂无圈子</view><view class="e2">还没有加入任何圈子，加入后即可发布</view></view><view class="circle-box"><view wx:for="{{bK}}" wx:for-item="item" wx:key="c" class="{{['circle-item', 'df', item.d]}}" data-idx="{{item.e}}" bindtap="{{item.f}}"><image src="{{item.a}}" mode="aspectFill"></image><text>{{item.b}}</text></view></view></view><view class="popup-btn" bindtap="{{bL}}">确认保存</view></view></uni-popup><uni-popup wx:if="{{bV}}" class="r" u-s="{{['d']}}" u-r="activityPopup" u-i="5e5002e4-4" bind:__l="__l" u-p="{{bV}}"><view class="popup-box"><view class="popup-top df"><view class="popup-title"><view class="t1">选择一个活动</view><view class="t2">轻触下方名称以选择相应活动</view></view><view class="popup-close df" bindtap="{{bP}}"><image src="{{bO}}" style="width:20rpx;height:20rpx"></image></view></view><view class="popup-scroll"><view wx:if="{{bQ}}" class="empty-box df"><image src="{{bR}}"/><view class="e1">暂无活动</view><view class="e2">还没有可参与的活动</view></view><view class="circle-box"><view wx:for="{{bS}}" wx:for-item="item" wx:key="c" class="{{['circle-item', 'df', item.d]}}" data-idx="{{item.e}}" bindtap="{{item.f}}"><image src="{{item.a}}" mode="aspectFill"></image><text>{{item.b}}</text></view></view></view><view class="popup-btn" bindtap="{{bT}}">确认保存</view></view></uni-popup><uni-popup wx:if="{{cg}}" class="r" u-s="{{['d']}}" u-r="topicPopup" u-i="5e5002e4-5" bind:__l="__l" u-p="{{cg}}"><view class="popup-box"><view class="popup-top df"><view class="popup-title"><view class="t1">选择一个话题</view><view class="t2">轻触下方名称以选择相应话题</view></view><view class="popup-close df" bindtap="{{bX}}"><image src="{{bW}}" style="width:20rpx;height:20rpx"></image></view></view><view class="popup-search df"><input bindconfirm="{{bY}}" focus="true" confirm-type="search" placeholder="输入关键词搜索话题" placeholder-class="search-ph" value="{{bZ}}" bindinput="{{ca}}"/><view class="search-btn" bindtap="{{cb}}">搜索</view></view><view class="popup-scroll"><empty-page wx:if="{{cc}}" u-i="5e5002e4-6,5e5002e4-5" bind:__l="__l" u-p="{{cd}}"/><view class="topic-box"><view wx:for="{{ce}}" wx:for-item="item" wx:key="c" class="topic-item df" bindtap="{{item.d}}"><view class="topic-tag">#</view><view class="topic-content"><view class="topic-name">{{item.a}}</view><view class="topic-desc">{{item.b}}</view></view></view></view></view></view></uni-popup><uni-popup wx:if="{{cr}}" class="r" u-s="{{['d']}}" u-r="userPopup" u-i="5e5002e4-7" bind:__l="__l" u-p="{{cr}}"><view class="popup-box"><view class="popup-top df"><view class="popup-title"><view class="t1">选择要@的好友</view><view class="t2">轻触下方名称以选择相应好友</view></view><view class="popup-close df" bindtap="{{ci}}"><image src="{{ch}}" style="width:20rpx;height:20rpx"></image></view></view><view class="popup-search df"><input bindconfirm="{{cj}}" focus="true" confirm-type="search" placeholder="输入关键词搜索好友" placeholder-class="search-ph" value="{{ck}}" bindinput="{{cl}}"/><view class="search-btn" bindtap="{{cm}}">搜索</view></view><view class="popup-scroll"><view wx:if="{{cn}}" class="empty-box df"><image src="{{co}}"/><view class="e1">暂无好友</view><view class="e2">还没有添加任何好友</view></view><view class="circle-box"><view wx:for="{{cp}}" wx:for-item="item" wx:key="c" class="circle-item df" data-idx="{{item.d}}" bindtap="{{item.e}}"><image src="{{item.a}}" mode="aspectFill"></image><text>{{item.b}}</text></view></view></view></view></uni-popup><uni-popup wx:if="{{cz}}" class="r" u-s="{{['d']}}" u-r="recordPopup" u-i="5e5002e4-8" bind:__l="__l" u-p="{{cz}}"><jc-record wx:if="{{cx}}" class="r" u-s="{{['d']}}" u-r="jcRecord" bindokClick="{{ct}}" bindshow="{{cv}}" bindclose="{{cw}}" u-i="5e5002e4-9,5e5002e4-8" bind:__l="__l" u-p="{{cx}}"><view class="centerwz">录制语音</view></jc-record></uni-popup><uni-popup wx:if="{{cC}}" class="r" u-s="{{['d']}}" u-r="tipsPopup" u-i="5e5002e4-10" bind:__l="__l" u-p="{{cC}}"><view class="tips-box df"><view class="tips-item">{{cA}}</view></view></uni-popup><canvas wx:if="{{cD}}" canvas-id="canvas" style="{{'width:' + cE + ';' + ('height:' + cF) + ';' + ('position:' + 'absolute') + ';' + ('left:' + '-100000px') + ';' + ('top:' + '-100000px')}}"></canvas><uni-popup wx:if="{{cP}}" class="r" u-s="{{['d']}}" u-r="votePopup" u-i="5e5002e4-11" bind:__l="__l" u-p="{{cP}}"><view class="popup-box vote-popup"><view class="popup-top df"><view class="popup-title"><view class="t1">投票</view></view><view class="popup-close df" bindtap="{{cH}}"><image src="{{cG}}" style="width:20rpx;height:20rpx"></image></view></view><view class="vote-title-input"><input placeholder="添加标题（20个汉字内）" maxlength="20" class="vote-input" value="{{cI}}" bindinput="{{cJ}}"/></view><view class="vote-options"><view wx:for="{{cK}}" wx:for-item="option" wx:key="e" class="vote-option-item"><input placeholder="{{option.a}}" maxlength="10" class="vote-option-input" value="{{option.b}}" bindinput="{{option.c}}"/><view class="vote-option-delete" bindtap="{{option.d}}"><view class="delete-circle"><view class="delete-line"></view></view></view></view></view><view class="vote-add-option" bindtap="{{cL}}"><text class="vote-add-icon">+</text><text class="vote-add-text">添加一个选项</text></view><view class="vote-bottom-btns"><view class="vote-cancel-btn" bindtap="{{cM}}">取消</view><view class="vote-confirm-btn" bindtap="{{cN}}">确定</view></view></view></uni-popup></view>