"use strict";
const utils_request = require("../utils/request.js");
function getAjcaptcha(data) {
  return utils_request.request.get("ajcaptcha", data, {
    noAuth: true
  });
}
function ajcaptchaCheck(data) {
  return utils_request.request.post("ajcheck", data, {
    noAuth: true
  });
}
function verifyCode() {
  return utils_request.request.get("verify_code", {}, {
    noAuth: true
  });
}
function registerVerify(phone, reset, key, captchaType, captchaVerification) {
  return utils_request.request.post("register/verify", {
    phone,
    type: reset === void 0 ? "reset" : reset,
    key,
    captchaType,
    captchaVerification
  }, {
    noAuth: true
  });
}
function phoneRegisterReset(data) {
  return utils_request.request.post("register/reset", data, {
    noAuth: true
  });
}
function switchH5Login() {
  return utils_request.request.post("switch_h5", {
    "from": "routine"
  });
}
function bindingUserPhone(data) {
  return utils_request.request.post("user/binding", data);
}
function getCity() {
  return utils_request.request.get("city_list", {}, {
    noAuth: true
  });
}
function colorChange(name) {
  return utils_request.request.get("v2/diy/color_change/" + name, {}, {
    noAuth: true
  });
}
function updatePhone(data) {
  return utils_request.request.post("user/updatePhone", data, {
    noAuth: true
  });
}
function siteConfig(data) {
  return utils_request.request.get("site_config", data, {
    noAuth: true
  });
}
function getCrmebCopyRight() {
  return utils_request.request.get("copyright", {}, {
    noAuth: true
  });
}
exports.ajcaptchaCheck = ajcaptchaCheck;
exports.bindingUserPhone = bindingUserPhone;
exports.colorChange = colorChange;
exports.getAjcaptcha = getAjcaptcha;
exports.getCity = getCity;
exports.getCrmebCopyRight = getCrmebCopyRight;
exports.phoneRegisterReset = phoneRegisterReset;
exports.registerVerify = registerVerify;
exports.siteConfig = siteConfig;
exports.switchH5Login = switchH5Login;
exports.updatePhone = updatePhone;
exports.verifyCode = verifyCode;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/api.js.map
