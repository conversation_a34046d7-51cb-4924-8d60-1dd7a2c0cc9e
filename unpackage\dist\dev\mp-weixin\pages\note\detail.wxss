
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.bar-box .bar-back {
  padding: 0 30rpx;
  width: 34rpx;
  height: 100%;
}
.bar-box .bar-title {
  max-width: 60%;
  font-size: 32rpx;
  font-weight: 700;
  flex: 1;
  text-align: center;
}
.nav-action {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  padding: 0 30rpx;
}
.content-box {
  width: calc(100% - 60rpx);
  padding: 30rpx;
}

/* 加载中状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60rpx;
  margin-bottom: 20rpx;
}
.loading-indicator {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}
.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}
.error-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}
.error-btn {
  padding: 20rpx 40rpx;
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  border-radius: 25rpx;
  color: white;
}
.content {
  width: 100%;
}
.box-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}
.avatar-img {
  width: 100%;
  height: 100%;
}
.user-details {
  flex: 1;
}
.username {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.user-meta {
  font-size: 24rpx;
  color: #999;
}
.type-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.type-1 { background: #e3f2fd; color: #1976d2;
}
.type-2 { background: #fce4ec; color: #c2185b;
}
.type-3 { background: #f3e5f5; color: #7b1fa2;
}
.type-4 { background: #e8f5e8; color: #388e3c;
}
.box-content {
  margin-bottom: 20rpx;
}
.content-text {
  font-size: 30rpx;
  line-height: 1.6;
  color: #333;
}
.voice-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
}
.voice-player {
  display: flex;
  align-items: center;
  gap: 20rpx;
  font-size: 28rpx;
}
.time-info {
  text-align: right;
}
.time-text {
  font-size: 24rpx;
  color: #999;
}
.response-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

/* 隐私保护提示 */
.privacy-notice {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}
.notice-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  color: #6c757d;
}
.notice-text {
  font-size: 24rpx;
  color: #6c757d;
  line-height: 1.4;
}
.response-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.response-user {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}
.response-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}
.response-username {
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 20rpx;
}
.response-time {
  font-size: 24rpx;
  color: #999;
}
.my-response-tag {
  font-size: 20rpx;
  color: #007aff;
  background: #e3f2fd;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-left: 12rpx;
}
.response-content {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}
.response-input {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}
.input-area {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}
.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.char-count {
  font-size: 24rpx;
  color: #999;
}
.send-btn {
  padding: 16rpx 32rpx;
  background: #e9ecef;
  border-radius: 20rpx;
  color: #999;
  font-size: 28rpx;
}
.send-btn.active {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  color: white;
}
.df {
  display: flex;
  align-items: center;
}
.bfw {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,.8);
}
.bfh {
  background: #000;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 700;
}
.tips-box {
  justify-content: center;
  width: 100%;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
