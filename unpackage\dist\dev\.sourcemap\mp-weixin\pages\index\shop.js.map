{"version": 3, "file": "shop.js", "sources": ["pages/index/shop.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvc2hvcC52dWU"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"nav-box\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\n      <view class=\"nav-bar df\" :style=\"{'height': titleBarHeight + 'px'}\">\n        <view class=\"nav-icon df\" data-url=\"search/index?bar=1\" @tap=\"navigateToFun\">\n          <image src=\"/static/img/s.png\"></image>\n        </view>\n        <view v-if=\"classifyList.length > 1\" class=\"nav-icon df\" data-url=\"goods/classify\" @tap=\"navigateToFun\">\n          <image src=\"/static/img/fl.png\"></image>\n        </view>\n        <view class=\"nav-icon df\" data-url=\"order/index\" @tap=\"navigateToFun\">\n          <image src=\"/static/img/dd1.png\"></image>\n          <view v-if=\"isOrder\" class=\"microlabel\" style=\"top:12rpx;right:8rpx\"></view>\n        </view>\n        <view v-if=\"appCard || isCard\" :class=\"['nav-icon', 'df', isCard ? 'animate' : '']\" data-url=\"center/card\" @tap=\"navigateToFun\">\n          <image src=\"/static/img/kq1.png\"></image>\n          <view v-if=\"isCard\" class=\"microlabel\" style=\"top:12rpx;right:8rpx\"></view>\n        </view>\n        <view class=\"nav-icon df\" data-url=\"goods/cart\" @tap=\"navigateToFun\">\n          <image src=\"/static/img/gwc1.png\"></image>\n          <view v-if=\"cartCount > 0\" class=\"msg\">{{cartCount}}</view>\n        </view>\n      </view>\n      <view class=\"nav-classify\" :style=\"{'height': classifyList.length > 1 && classifyScroll ? '68rpx' : '0'}\">\n        <scroll-view scroll-x=\"true\" class=\"classify-scroll\">\n          <view class=\"classify-box df\">\n            <view v-for=\"(item, index) in classifyList\" :key=\"index\" class=\"classify-item\" \n                  :style=\"{'color': index == classifyIdx ? '#000' : '#999', 'font-weight': index == classifyIdx ? 'bold' : '400'}\" \n                  :data-idx=\"index\" \n                  @tap=\"classifyClick\">\n              {{item.name}}\n            </view>\n            <view style=\"flex-shrink:0;width:120rpx;height:68rpx\"></view>\n          </view>\n        </scroll-view>\n        <view class=\"classify-all df\" data-url=\"goods/classify\" @tap=\"navigateToFun\">\n          <image src=\"/static/img/fl.png\"></image>\n          <text>分类</text>\n        </view>\n      </view>\n    </view>\n    <view class=\"content-box\" :style=\"{'padding-top': classifyList.length > 1 ? 'calc(' + (statusBarHeight + titleBarHeight) + 'px + 68rpx)' : 'calc(' + (statusBarHeight + titleBarHeight) + 'px)'}\">\n      <view class=\"heio df\" :style=\"{'height': isThrottling || loadStatus == 'loading' ? '0px' : '60rpx'}\">\n        <uni-load-more v-if=\"true\" status=\"loading\"></uni-load-more>\n      </view>\n      <view class=\"goods-box\">\n        <view v-if=\"bannerList.length > 0 && 0 == classifyIdx\" class=\"goods-banner\">\n          <view class=\"banner-swiper\">\n            <swiper class=\"banner-swiper\" circular autoplay @change=\"swiperChange\">\n              <swiper-item v-for=\"(item, index) in bannerList\" :key=\"index\" :data-url=\"item.type_url\" @tap=\"bannerClick\">\n                <lazy-image :src=\"item.cover\"></lazy-image>\n              </swiper-item>\n            </swiper>\n            <view class=\"swiper-idor df\">\n              <view v-for=\"(item, index) in bannerList.length\" :key=\"index\" :class=\"['idor-item', swiperIdx == index ? 'active' : '']\"></view>\n            </view>\n          </view>\n        </view>\n        <block v-if=\"!isEmpty\">\n          <view v-for=\"(item, index) in list\" :key=\"index\" class=\"goods-item\" :data-url=\"'goods/details?id=' + item.id\" @tap=\"navigateToFun\">\n            <view class=\"goods-img\">\n              <view class=\"goods-img-item\">\n                <lazy-image :src=\"item.imgs[0]\"></lazy-image>\n              </view>\n            </view>\n            <view class=\"goods-name ohto2\">{{item.name}}</view>\n            <view class=\"goods-price\">\n              <money :type=\"1\" :price=\"item.product.price\"></money>\n              <view class=\"price-h\" style=\"text-decoration:line-through\">¥{{item.product.line_price}}</view>\n              <view class=\"price-h\">{{item.buy ? item.buy + '人已买' : item.cart + item.browse + '人想买'}}</view>\n            </view>\n            <view class=\"goods-tag df\">\n              <view v-for=\"(tag, tagIndex) in item.tags\" :key=\"tagIndex\" class=\"tag-item\">{{tag}}</view>\n            </view>\n          </view>\n        </block>\n      </view>\n      <view v-if=\"isEmpty\" class=\"empty-box df\">\n        <image src=\"/static/img//null.png\"/>\n        <view class=\"e1\">暂无推荐商品</view>\n        <view class=\"e2\">正在为您制造更多美好的商品</view>\n      </view>\n      <uni-load-more v-if=\"true\" :status=\"loadStatus\"></uni-load-more>\n    </view>\n    <tabbar v-if=\"true\" :currentPage=\"1\" :currentMsg=\"currentMsg\"></tabbar>\n  </view>\n</template>\n\n<script>\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more'\nimport lazyImage from '@/components/lazyImage/lazyImage'\nimport money from '@/components/money/money'\nimport tabbar from '@/components/tabbar/tabbar'\nimport { getUserSocialInfo } from '@/api/social.js'\n\n// Mock数据 - 用于开发测试\nconst mockData = {\n  // 商品分类数据\n  classifyList: [\n    { id: 0, name: '推荐' },\n    { id: 1, name: '服饰' },\n    { id: 2, name: '美妆' },\n    { id: 3, name: '数码' },\n    { id: 4, name: '家居' },\n    { id: 5, name: '食品' },\n    { id: 6, name: '母婴' },\n    { id: 7, name: '运动' }\n  ],\n  \n  // 轮播图数据\n  bannerList: [\n    {\n      id: 1,\n      cover: '/static/img/avatar.png',\n      type: 1, // 普通链接\n      type_url: 'goods/details?id=1'\n    },\n    {\n      id: 2,\n      cover: '/static/img/avatar.png',\n      type: 2, // 商品\n      type_url: 'goods/details?id=2'\n    },\n  ],\n  \n\n  // 状态信息\n  statusInfo: {\n    is_order: true,\n    is_card: true,\n    cart_count: 5\n  },\n  \n  // 商品列表 - 按分类\n  goodsList: {\n    // 推荐商品\n    0: [\n      {\n        id: 1,\n        name: '2023新款连衣裙夏季气质淑女雪纺长裙显瘦温柔风长款仙女裙',\n        imgs: ['/static/img/avatar.png', '/static/img/avatar.png'],\n        product: {\n          price: 199.00,\n          line_price: 298.00\n        },\n        buy: 328,\n        cart: 56,\n        browse: 1245,\n        tags: ['夏季热卖', '气质淑女']\n      },\n      {\n        id: 2,\n        name: '轻奢品牌真皮小方包2023新款潮女包单肩斜挎包时尚百搭链条包',\n        imgs: ['/static/img/avatar.png', '/static/img/avatar.png'],\n        product: {\n          price: 398.00,\n          line_price: 658.00\n        },\n        buy: 156,\n        cart: 43,\n        browse: 876,\n        tags: ['轻奢', '真皮']\n      },\n      {\n        id: 3,\n        name: '超薄智能手表男士多功能运动防水电子表学生特种兵机械男表',\n        imgs: ['/static/img/avatar.png', '/static/img/avatar.png'],\n        product: {\n          price: 299.00,\n          line_price: 399.00\n        },\n        buy: 268,\n        cart: 35,\n        browse: 1024,\n        tags: ['智能手表', '防水']\n      },\n      {\n        id: 4,\n        name: '原创设计师品牌女装小香风外套春秋季新款高端复古港风小个子西装',\n        imgs: ['/static/img/avatar.png', '/static/img/avatar.png'],\n        product: {\n          price: 458.00,\n          line_price: 698.00\n        },\n        buy: 126,\n        cart: 28,\n        browse: 756,\n        tags: ['设计师品牌', '小香风']\n      }\n    ],\n    \n    // 服饰类商品\n    1: [\n      {\n        id: 11,\n        name: '法式复古裙山本风连衣裙女春秋宽松长袖气质长裙温柔风碎花裙子',\n        imgs: ['/static/img/avatar.png', '/static/img/avatar.png'],\n        product: {\n          price: 189.00,\n          line_price: 289.00\n        },\n        buy: 213,\n        cart: 45,\n        browse: 980,\n        tags: ['法式复古', '碎花裙']\n      },\n      {\n        id: 12,\n        name: '小众设计感西装外套女2023春装新款韩版宽松休闲西服上衣',\n        imgs: ['/static/img/avatar.png', '/static/img/avatar.png'],\n        product: {\n          price: 258.00,\n          line_price: 428.00\n        },\n        buy: 109,\n        cart: 32,\n        browse: 765,\n        tags: ['小众设计', '西装外套']\n      }\n    ],\n    \n    // 美妆类商品\n    2: [\n      {\n        id: 21,\n        name: '日本进口资生堂红腰子精华保湿补水抗皱紧致淡斑修护精华液',\n        imgs: ['/static/img/avatar.png', '/static/img/avatar.png'],\n        product: {\n          price: 699.00,\n          line_price: 998.00\n        },\n        buy: 567,\n        cart: 89,\n        browse: 2310,\n        tags: ['日本进口', '抗皱']\n      }\n    ]\n  }\n}\n\nexport default {\n  components: {\n    uniLoadMore,\n    lazyImage,\n    money,\n    tabbar\n  },\n  data() {\n    return {\n      statusBarHeight: 20,\n      titleBarHeight: 44,\n      appCard: false,\n      scrollTop: 0,\n      currentMsg: 0,\n      isOrder: false,\n      isCard: false,\n      cartCount: 0,\n      classifyList: [],\n      classifyIdx: 0,\n      classifyScroll: true,\n      bannerList: [],\n      swiperIdx: 0,\n      isThrottling: false,\n      list: [],\n      page: 1,\n      isEmpty: false,\n      loadStatus: 'more',\n      useMockData: process.env.NODE_ENV === 'development' // 开发环境下使用mock数据\n    }\n  },\n  async onLoad() {\n    // 开启分享功能 - 添加平台兼容性检查\n    try {\n      // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ\n      if (typeof uni.showShareMenu === 'function') {\n        uni.showShareMenu({\n          withShareTicket: true,\n          menus: ['shareAppMessage', 'shareTimeline']\n        })\n      }\n      // #endif\n    } catch (e) {\n      console.warn('showShareMenu not supported on this platform:', e)\n    }\n\n    this.$store.commit('SET_CURRENT_MSG', false)\n\n    // 等待应用初始化完成\n    if (this.$onLaunched) {\n      await this.$onLaunched\n    }\n\n    this.$store.commit('SET_CURRENT_MSG', true)\n    \n    // 初始化数据\n    this.getUserSocialData()\n    \n    if (this.$store.state.isClassify) {\n      this.getClassify()\n    }\n    \n    \n    this.getBanner()\n    this.goodsList()\n  },\n  onShow() {\n    // 自定义tabbar，不需要隐藏原生tabBar\n    // uni.hideTabBar(); // 注释掉，因为使用自定义tabbar\n    if (this.$store.state.isCurrentMsg) {\n      this.userAvatar = uni.getStorageSync('userInfo')?.avatar || '/static/img/tabbar/4-hl.png'\n      this.getUserSocialData()\n      this.getBanner()\n    }\n  },\n  onPullDownRefresh() {\n    this.refreshPath()\n  },\n  onPageScroll(e) {\n    let that = this\n    let scrollTop = parseInt(e.scrollTop)\n    \n    if (that.scrollTop > scrollTop) {\n      that.classifyScroll = true\n    } else if (that.scrollTop <= scrollTop && scrollTop > 0) {\n      that.classifyScroll = false\n    }\n    \n    setTimeout(function() {\n      that.scrollTop = scrollTop\n    }, 300)\n  },\n  onReachBottom() {\n    if (this.list && this.list.length) {\n      this.loadStatus = 'loading'\n      this.page = this.page + 1\n      this.goodsList()\n    }\n  },\n  onShareAppMessage() {\n    return {\n      title: this.$store.state.appXx?.[1] || '小程序示例',\n      imageUrl: this.$store.state.appXx?.[2] || '/static/img/avatar.png'\n    }\n  },\n  onShareTimeline() {\n    return {\n      title: this.$store.state.appXx?.[1] || '小程序示例',\n      imageUrl: this.$store.state.appXx?.[2] || '/static/img/avatar.png'\n    }\n  },\n  methods: {\n    // 刷新页面数据\n    refreshPath() {\n      uni.pageScrollTo({\n        scrollTop: 0,\n        duration: 300\n      })\n      \n      this.isThrottling = false\n      \n      if (this.$store.state.isClassify) {\n        this.getClassify()\n      }\n      \n      this.getBanner()\n      this.page = 1\n      this.goodsList()\n      \n      uni.stopPullDownRefresh()\n    },\n    \n    // 获取轮播图和状态信息\n    getBanner() {\n      let that = this\n      \n      // 使用Mock数据\n      if (this.useMockData) {\n        setTimeout(() => {\n          that.bannerList = mockData.bannerList\n          that.isOrder = mockData.statusInfo.is_order\n          that.isCard = mockData.statusInfo.is_card\n          that.cartCount = mockData.statusInfo.cart_count\n        }, 300)\n        return\n      }\n      \n      this.$util.request(this.$api.bannerUrl).then(function(res) {\n        if (res && res.code == 200) {\n          that.bannerList = res.data.banner || []\n          that.isOrder = res.data.is_order || false\n          that.isCard = res.data.is_card || false\n          that.cartCount = res.data.cart_count || 0\n        }\n      }).catch(function(err) {\n        console.log('获取轮播图失败', err)\n        // 请求失败时使用Mock数据\n        that.bannerList = mockData.bannerList\n        that.isOrder = mockData.statusInfo.is_order\n        that.isCard = mockData.statusInfo.is_card\n        that.cartCount = mockData.statusInfo.cart_count\n      })\n    },\n    \n    // 获取商品分类\n    getClassify() {\n      let that = this\n      \n      // 使用Mock数据\n      if (this.useMockData) {\n        setTimeout(() => {\n          that.classifyList = mockData.classifyList\n        }, 300)\n        return\n      }\n      \n      this.$util.request(this.$api.classifyHomeUrl).then(function(res) {\n        if (res && res.code == 200) {\n          that.classifyList = res.data || []\n        }\n      }).catch(function(err) {\n        console.log('获取商品分类失败', err)\n        // 请求失败时使用Mock数据\n        that.classifyList = mockData.classifyList\n      })\n    },\n    \n    // 获取商品列表\n    goodsList() {\n      let that = this\n      that.isEmpty = false\n      \n      // 使用Mock数据\n      if (this.useMockData) {\n        setTimeout(() => {\n          // 获取当前分类的商品列表\n          const categoryId = that.classifyIdx\n          \n          // 获取对应分类的商品，如果没有则使用推荐商品\n          let mockResult = mockData.goodsList[categoryId] || mockData.goodsList[0]\n          \n          // 模拟分页加载 - 第二页加载部分数据，第三页无数据\n          if (that.page > 1) {\n            // 截取前1-2个商品作为第二页\n            if (that.page === 2 && mockResult.length > 2) {\n              mockResult = mockResult.slice(0, 2)\n            } else {\n              mockResult = [] // 第三页及以后无数据\n            }\n          }\n          \n          // 更新列表数据\n          if (mockResult.length > 0) {\n            if (that.page === 1) {\n              that.list = mockResult\n            } else {\n              that.list = that.list.concat(mockResult)\n            }\n            that.isEmpty = false\n          } else if (that.page === 1) {\n            that.isEmpty = true\n            that.list = []\n          } else {\n            that.loadStatus = 'no-more'\n          }\n          \n          that.isThrottling = true\n        }, 500)\n        return\n      }\n      \n      // 获取当前分类ID\n      let classifyId = that.classifyList.length ? that.classifyList[that.classifyIdx].id : 0\n      \n      // 请求参数\n      let params = {\n        page: that.page || 1,\n        classify_id: classifyId\n      }\n      \n      this.$util.request(this.$api.goodsListUrl, params).then(function(res) {\n        that.isThrottling = true\n        that.loadStatus = 'more'\n        \n        if (res && res.data && res.data.data && res.data.data.length > 0) {\n          if (that.page == 1) {\n            that.list = res.data.data\n          } else {\n            that.list = that.list.concat(res.data.data)\n          }\n          that.page = res.data.current_page || that.page\n        } else if (that.page == 1) {\n          that.isEmpty = true\n          that.list = []\n        } else {\n          that.loadStatus = 'no-more'\n        }\n      }).catch(function(err) {\n        console.log('获取商品列表失败', err)\n        that.isThrottling = true\n        \n        // 获取当前分类的商品，失败时使用mock数据\n        const categoryId = that.classifyIdx\n        const mockResult = mockData.goodsList[categoryId] || mockData.goodsList[0]\n        \n        if (that.page === 1) {\n          that.list = mockResult\n          that.isEmpty = mockResult.length === 0\n        } else {\n          that.loadStatus = 'no-more'\n        }\n      })\n    },\n    \n    // 分类切换\n    classifyClick(e) {\n      if (!e || !e.currentTarget || !e.currentTarget.dataset) return\n      \n      this.isThrottling = false\n      this.classifyIdx = parseInt(e.currentTarget.dataset.idx) || 0\n      this.page = 1\n      this.goodsList()\n      \n      if (this.classifyIdx == 0) {\n        this.getBanner()\n        if (this.$store.state.isClassify) {\n          this.getClassify()\n        }\n      }\n    },\n    \n    \n    // 轮播图点击\n    bannerClick(e) {\n      if (!e || !e.currentTarget || !e.currentTarget.dataset) return\n      \n      let url = e.currentTarget.dataset.url\n      if (url) {\n        uni.navigateTo({\n          url: url\n        })\n      }\n    },\n    \n    // 轮播图切换\n    swiperChange(e) {\n      this.swiperIdx = e.detail.current || 0\n    },\n    \n    // 页面跳转\n    navigateToFun(e) {\n      if (!e || !e.currentTarget || !e.currentTarget.dataset || !e.currentTarget.dataset.url) return\n      \n      uni.navigateTo({\n        url: '/pages/' + e.currentTarget.dataset.url\n      })\n    },\n    \n    // 获取通知消息数量\n    getUserSocialData() {\n      let that = this\n      \n      // 使用Mock数据\n      if (this.useMockData) {\n        setTimeout(() => {\n          that.currentMsg = 3 // 模拟有3条未读消息\n        }, 300)\n        return\n      }\n      \n      // 使用getUserSocialInfo获取service_num\n      getUserSocialInfo().then(function(res) {\n        if (res && (res.code == 200 || res.status == 200) && res.data) {\n          // 从返回的数据中获取service_num\n          if (res.data.service_num !== undefined) {\n            that.currentMsg = res.data.service_num;\n          }\n        }\n      }).catch(function(err) {\n        console.error('获取消息数量失败', err)\n        that.currentMsg = 0\n      })\n    }\n  }\n}\n</script>\n\n<style>\npage{\n  background:#f8f8f8\n}\n.nav-box{\n  position:fixed;\n  top:0;\n  width:100%;\n  z-index:99;\n  box-sizing:border-box;\n  background:#fff\n}\n.nav-box .nav-bar{\n  width:calc(100% - 30rpx);\n  padding:0 15rpx\n}\n.nav-bar .nav-icon{\n  padding:0 15rpx;\n  height:100%;\n  position:relative\n}\n.nav-icon .msg{\n  position:absolute;\n  top:0;\n  right:0;\n  left:calc(50% + 8rpx);\n  min-width:30rpx;\n  height:30rpx;\n  line-height:30rpx;\n  text-align:center;\n  font-size:18rpx;\n  font-weight:700;\n  color:#fff;\n  background:#000;\n  border-radius:30rpx;\n  border:4rpx solid #fff\n}\n.nav-icon image{\n  width:46rpx;\n  height:46rpx\n}\n.nav-box .nav-classify{\n  width:100%;\n  overflow:hidden;\n  position:relative;\n  transition:height .3s ease-in-out\n}\n.nav-box .classify-scroll{\n  width:100%;\n  white-space:nowrap\n}\n.classify-scroll .classify-box{\n  width:100%;\n  height:68rpx\n}\n.classify-box .classify-item{\n  flex-shrink:0;\n  padding:0 30rpx;\n  height:68rpx;\n  line-height:68rpx;\n  font-size:24rpx;\n  transition:all .3s ease-in-out\n}\n.nav-classify .classify-all{\n  position:absolute;\n  top:0;\n  bottom:0;\n  right:0;\n  height:68rpx;\n  padding:0 30rpx;\n  font-size:20rpx;\n  font-weight:700;\n  background:linear-gradient(to right,rgba(255,255,255,0),#fff 20%);\n  justify-content:center\n}\n.classify-all image{\n  margin-right:8rpx;\n  width:26rpx;\n  height:26rpx\n}\n.content-box{\n  width:100%;\n  padding-bottom:180rpx\n}\n.goods-box{\n  width:100%;\n  display:flex;\n  flex-wrap:wrap\n}\n.goods-box .goods-banner,\n.goods-box .goods-item{\n  width:calc(50% - 15rpx);\n  margin:10rpx 0 0 10rpx;\n  background:#fff;\n  border-radius:8rpx;\n  overflow:hidden\n}\n.goods-box .goods-banner{\n  padding-top:calc(66.67% - 13.33rpx);\n  position:relative\n}\n.goods-banner .banner-swiper{\n  position:absolute;\n  top:0;\n  right:0;\n  bottom:0;\n  left:0;\n  width:100%;\n  height:100%\n}\n.goods-banner .swiper-idor{\n  position:absolute;\n  width:100%;\n  bottom:20rpx;\n  justify-content:center\n}\n.swiper-idor .idor-item{\n  margin:0 6rpx;\n  width:8rpx;\n  height:8rpx;\n  border-radius:8rpx;\n  background:rgba(255,255,255,.3);\n  transition:all .3s\n}\n.swiper-idor .active{\n  width:24rpx;\n  background:rgba(255,255,255,.9)\n}\n\n.goods-item .goods-img{\n  width:100%;\n  padding-top:100%;\n  position:relative\n}\n.goods-img .goods-img-item{\n  position:absolute;\n  top:0;\n  right:0;\n  bottom:0;\n  left:0;\n  width:100%;\n  height:100%\n}\n.goods-item .goods-name{\n  width:calc(100% - 40rpx);\n  margin:15rpx 20rpx;\n  font-size:26rpx;\n  line-height:36rpx;\n  font-weight:500\n}\n.goods-item .goods-price{\n  width:calc(100% - 30rpx);\n  margin:0 20rpx 20rpx;\n  display:flex;\n  align-items:flex-end\n}\n.goods-price .price-h{\n  margin-left:15rpx;\n  color:#999;\n  font-size:20rpx;\n  line-height:20rpx\n}\n.goods-item .goods-tag{\n  width:calc(100% - 30rpx);\n  margin:0 15rpx 15rpx;\n  display:flex;\n  flex-wrap:wrap\n}\n.goods-tag .tag-item{\n  margin:0 5rpx 5rpx;\n  height:40rpx;\n  padding:0 12rpx;\n  line-height:40rpx;\n  font-size:18rpx;\n  font-weight:500;\n  background:#f8f8f8;\n  border-radius:8rpx\n}\n.df {\n  display: flex;\n  align-items: center;\n}\n.ohto {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.ohto2 {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n.empty-box {\n  flex-direction: column;\n  padding: 120rpx 0;\n}\n.empty-box image {\n  width: 280rpx;\n  height: 280rpx;\n}\n.empty-box .e1 {\n  margin-top: 40rpx;\n  font-size: 32rpx;\n  font-weight: bold;\n}\n.empty-box .e2 {\n  margin-top: 20rpx;\n  font-size: 24rpx;\n  color: #999;\n}\n.heio {\n  justify-content: center;\n}\n.microlabel {\n  position: absolute;\n  width: 18rpx;\n  height: 18rpx;\n  background: #fa5150;\n  border-radius: 50%;\n  border: 4rpx solid #fff;\n}\n</style>\n", "import MiniProgramPage from 'D:/uniapp/vue3/pages/index/shop.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getUserSocialInfo"], "mappings": ";;;;AAyFA,MAAA,cAAA,MAAA;AACA,MAAA,YAAA,MAAA;AACA,MAAA,QAAA,MAAA;AACA,MAAA,SAAA,MAAA;AAIA,MAAA,WAAA;AAAA;AAAA,EAAiB,cAAA;AAAA,IAED,EAAA,IAAA,GAAA,MAAA,KAAA;AAAA,IACQ,EAAA,IAAA,GAAA,MAAA,KAAA;AAAA,IACA,EAAA,IAAA,GAAA,MAAA,KAAA;AAAA,IACA,EAAA,IAAA,GAAA,MAAA,KAAA;AAAA,IACA,EAAA,IAAA,GAAA,MAAA,KAAA;AAAA,IACA,EAAA,IAAA,GAAA,MAAA,KAAA;AAAA,IACA,EAAA,IAAA,GAAA,MAAA,KAAA;AAAA,IACA,EAAA,IAAA,GAAA,MAAA,KAAA;AAAA,EACA;AAAA;AAAA,EACtB,YAAA;AAAA,IAGY;AAAA,MACV,IAAA;AAAA,MACM,OAAA;AAAA,MACG,MAAA;AAAA;AAAA,MACD,UAAA;AAAA,IACI;AAAA,IACZ;AAAA,MACA,IAAA;AAAA,MACM,OAAA;AAAA,MACG,MAAA;AAAA;AAAA,MACD,UAAA;AAAA,IACI;AAAA,EACZ;AAAA;AAAA,EACF,YAAA;AAAA,IAIY,UAAA;AAAA,IACA,SAAA;AAAA,IACD,YAAA;AAAA,EACG;AAAA;AAAA,EACd,WAAA;AAAA;AAAA,IAGW,GAAA;AAAA,MAEN;AAAA,QACD,IAAA;AAAA,QACM,MAAA;AAAA,QACE,MAAA,CAAA,0BAAA,wBAAA;AAAA,QACmD,SAAA;AAAA,UAChD,OAAA;AAAA,UACA,YAAA;AAAA,QACK;AAAA,QACd,KAAA;AAAA,QACK,MAAA;AAAA,QACC,QAAA;AAAA,QACE,MAAA,CAAA,QAAA,MAAA;AAAA,MACa;AAAA,MACvB;AAAA,QACA,IAAA;AAAA,QACM,MAAA;AAAA,QACE,MAAA,CAAA,0BAAA,wBAAA;AAAA,QACmD,SAAA;AAAA,UAChD,OAAA;AAAA,UACA,YAAA;AAAA,QACK;AAAA,QACd,KAAA;AAAA,QACK,MAAA;AAAA,QACC,QAAA;AAAA,QACE,MAAA,CAAA,MAAA,IAAA;AAAA,MACS;AAAA,MACnB;AAAA,QACA,IAAA;AAAA,QACM,MAAA;AAAA,QACE,MAAA,CAAA,0BAAA,wBAAA;AAAA,QACmD,SAAA;AAAA,UAChD,OAAA;AAAA,UACA,YAAA;AAAA,QACK;AAAA,QACd,KAAA;AAAA,QACK,MAAA;AAAA,QACC,QAAA;AAAA,QACE,MAAA,CAAA,QAAA,IAAA;AAAA,MACW;AAAA,MACrB;AAAA,QACA,IAAA;AAAA,QACM,MAAA;AAAA,QACE,MAAA,CAAA,0BAAA,wBAAA;AAAA,QACmD,SAAA;AAAA,UAChD,OAAA;AAAA,UACA,YAAA;AAAA,QACK;AAAA,QACd,KAAA;AAAA,QACK,MAAA;AAAA,QACC,QAAA;AAAA,QACE,MAAA,CAAA,SAAA,KAAA;AAAA,MACa;AAAA,IACvB;AAAA;AAAA,IACF,GAAA;AAAA,MAGG;AAAA,QACD,IAAA;AAAA,QACM,MAAA;AAAA,QACE,MAAA,CAAA,0BAAA,wBAAA;AAAA,QACmD,SAAA;AAAA,UAChD,OAAA;AAAA,UACA,YAAA;AAAA,QACK;AAAA,QACd,KAAA;AAAA,QACK,MAAA;AAAA,QACC,QAAA;AAAA,QACE,MAAA,CAAA,QAAA,KAAA;AAAA,MACY;AAAA,MACtB;AAAA,QACA,IAAA;AAAA,QACM,MAAA;AAAA,QACE,MAAA,CAAA,0BAAA,wBAAA;AAAA,QACmD,SAAA;AAAA,UAChD,OAAA;AAAA,UACA,YAAA;AAAA,QACK;AAAA,QACd,KAAA;AAAA,QACK,MAAA;AAAA,QACC,QAAA;AAAA,QACE,MAAA,CAAA,QAAA,MAAA;AAAA,MACa;AAAA,IACvB;AAAA;AAAA,IACF,GAAA;AAAA,MAGG;AAAA,QACD,IAAA;AAAA,QACM,MAAA;AAAA,QACE,MAAA,CAAA,0BAAA,wBAAA;AAAA,QACmD,SAAA;AAAA,UAChD,OAAA;AAAA,UACA,YAAA;AAAA,QACK;AAAA,QACd,KAAA;AAAA,QACK,MAAA;AAAA,QACC,QAAA;AAAA,QACE,MAAA,CAAA,QAAA,IAAA;AAAA,MACW;AAAA,IACrB;AAAA,EACF;AAEJ;AAEA,MAAA,YAAA;AAAA,EAAe,YAAA;AAAA,IACD;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACA;AAAA,EACF,OAAA;AAEE,WAAA;AAAA,MAAO,iBAAA;AAAA,MACY,gBAAA;AAAA,MACD,SAAA;AAAA,MACP,WAAA;AAAA,MACE,YAAA;AAAA,MACC,SAAA;AAAA,MACH,QAAA;AAAA,MACD,WAAA;AAAA,MACG,cAAA,CAAA;AAAA,MACI,aAAA;AAAA,MACF,gBAAA;AAAA,MACG,YAAA,CAAA;AAAA,MACH,WAAA;AAAA,MACF,cAAA;AAAA,MACG,MAAA,CAAA;AAAA,MACP,MAAA;AAAA,MACD,SAAA;AAAA,MACG,YAAA;AAAA,MACG,aAAA;AAAA;AAAA,IACC;AAAA,EACf;AAAA,EACF,MAAA,SAAA;AAGE,QAAA;AAEE,UAAA,OAAAA,cAAAA,MAAA,kBAAA,YAAA;AACEA,sBAAAA,MAAA,cAAA;AAAA,UAAkB,iBAAA;AAAA,UACC,OAAA,CAAA,mBAAA,eAAA;AAAA,QACyB,CAAA;AAAA,MAC3C;AAAA,IACH,SAAA,GAAA;AAGAA,oBAAA,MAAA,MAAA,QAAA,+BAAA,iDAAA,CAAA;AAAA,IAA+D;AAGjE,SAAA,OAAA,OAAA,mBAAA,KAAA;AAGA,QAAA,KAAA,aAAA;AACE,YAAA,KAAA;AAAA,IAAW;AAGb,SAAA,OAAA,OAAA,mBAAA,IAAA;AAGA,SAAA,kBAAA;AAEA,QAAA,KAAA,OAAA,MAAA,YAAA;AACE,WAAA,YAAA;AAAA,IAAiB;AAInB,SAAA,UAAA;AACA,SAAA,UAAA;AAAA,EAAe;AAAA,EACjB,SAAA;;AAIE,QAAA,KAAA,OAAA,MAAA,cAAA;AACE,WAAA,eAAAA,yBAAA,eAAA,UAAA,MAAAA,mBAAA,WAAA;AACA,WAAA,kBAAA;AACA,WAAA,UAAA;AAAA,IAAe;AAAA,EACjB;AAAA,EACF,oBAAA;AAEE,SAAA,YAAA;AAAA,EAAiB;AAAA,EACnB,aAAA,GAAA;AAEE,QAAA,OAAA;AACA,QAAA,YAAA,SAAA,EAAA,SAAA;AAEA,QAAA,KAAA,YAAA,WAAA;AACE,WAAA,iBAAA;AAAA,IAAsB,WAAA,KAAA,aAAA,aAAA,YAAA,GAAA;AAEtB,WAAA,iBAAA;AAAA,IAAsB;AAGxB,eAAA,WAAA;AACE,WAAA,YAAA;AAAA,IAAiB,GAAA,GAAA;AAAA,EACb;AAAA,EACR,gBAAA;AAEE,QAAA,KAAA,QAAA,KAAA,KAAA,QAAA;AACE,WAAA,aAAA;AACA,WAAA,OAAA,KAAA,OAAA;AACA,WAAA,UAAA;AAAA,IAAe;AAAA,EACjB;AAAA,EACF,oBAAA;;AAEE,WAAA;AAAA,MAAO,SAAA,UAAA,OAAA,MAAA,UAAA,mBAAA,OAAA;AAAA,MACkC,YAAA,UAAA,OAAA,MAAA,UAAA,mBAAA,OAAA;AAAA,IACG;AAAA,EAC5C;AAAA,EACF,kBAAA;;AAEE,WAAA;AAAA,MAAO,SAAA,UAAA,OAAA,MAAA,UAAA,mBAAA,OAAA;AAAA,MACkC,YAAA,UAAA,OAAA,MAAA,UAAA,mBAAA,OAAA;AAAA,IACG;AAAA,EAC5C;AAAA,EACF,SAAA;AAAA;AAAA,IACS,cAAA;AAGLA,oBAAAA,MAAA,aAAA;AAAA,QAAiB,WAAA;AAAA,QACJ,UAAA;AAAA,MACD,CAAA;AAGZ,WAAA,eAAA;AAEA,UAAA,KAAA,OAAA,MAAA,YAAA;AACE,aAAA,YAAA;AAAA,MAAiB;AAGnB,WAAA,UAAA;AACA,WAAA,OAAA;AACA,WAAA,UAAA;AAEAA,oBAAA,MAAA,oBAAA;AAAA,IAAwB;AAAA;AAAA,IAC1B,YAAA;AAIE,UAAA,OAAA;AAGA,UAAA,KAAA,aAAA;AACE,mBAAA,MAAA;AACE,eAAA,aAAA,SAAA;AACA,eAAA,UAAA,SAAA,WAAA;AACA,eAAA,SAAA,SAAA,WAAA;AACA,eAAA,YAAA,SAAA,WAAA;AAAA,QAAqC,GAAA,GAAA;AAEvC;AAAA,MAAA;AAGF,WAAA,MAAA,QAAA,KAAA,KAAA,SAAA,EAAA,KAAA,SAAA,KAAA;AACE,YAAA,OAAA,IAAA,QAAA,KAAA;AACE,eAAA,aAAA,IAAA,KAAA,UAAA,CAAA;AACA,eAAA,UAAA,IAAA,KAAA,YAAA;AACA,eAAA,SAAA,IAAA,KAAA,WAAA;AACA,eAAA,YAAA,IAAA,KAAA,cAAA;AAAA,QAAwC;AAAA,MAC1C,CAAA,EAAA,MAAA,SAAA,KAAA;AAEAA,sBAAA,MAAA,MAAA,OAAA,+BAAA,WAAA,GAAA;AAEA,aAAA,aAAA,SAAA;AACA,aAAA,UAAA,SAAA,WAAA;AACA,aAAA,SAAA,SAAA,WAAA;AACA,aAAA,YAAA,SAAA,WAAA;AAAA,MAAqC,CAAA;AAAA,IACtC;AAAA;AAAA,IACH,cAAA;AAIE,UAAA,OAAA;AAGA,UAAA,KAAA,aAAA;AACE,mBAAA,MAAA;AACE,eAAA,eAAA,SAAA;AAAA,QAA6B,GAAA,GAAA;AAE/B;AAAA,MAAA;AAGF,WAAA,MAAA,QAAA,KAAA,KAAA,eAAA,EAAA,KAAA,SAAA,KAAA;AACE,YAAA,OAAA,IAAA,QAAA,KAAA;AACE,eAAA,eAAA,IAAA,QAAA,CAAA;AAAA,QAAiC;AAAA,MACnC,CAAA,EAAA,MAAA,SAAA,KAAA;AAEAA,sBAAA,MAAA,MAAA,OAAA,+BAAA,YAAA,GAAA;AAEA,aAAA,eAAA,SAAA;AAAA,MAA6B,CAAA;AAAA,IAC9B;AAAA;AAAA,IACH,YAAA;AAIE,UAAA,OAAA;AACA,WAAA,UAAA;AAGA,UAAA,KAAA,aAAA;AACE,mBAAA,MAAA;AAEE,gBAAA,aAAA,KAAA;AAGA,cAAA,aAAA,SAAA,UAAA,UAAA,KAAA,SAAA,UAAA,CAAA;AAGA,cAAA,KAAA,OAAA,GAAA;AAEE,gBAAA,KAAA,SAAA,KAAA,WAAA,SAAA,GAAA;AACE,2BAAA,WAAA,MAAA,GAAA,CAAA;AAAA,YAAkC,OAAA;AAElC,2BAAA,CAAA;AAAA,YAAc;AAAA,UAChB;AAIF,cAAA,WAAA,SAAA,GAAA;AACE,gBAAA,KAAA,SAAA,GAAA;AACE,mBAAA,OAAA;AAAA,YAAY,OAAA;AAEZ,mBAAA,OAAA,KAAA,KAAA,OAAA,UAAA;AAAA,YAAuC;AAEzC,iBAAA,UAAA;AAAA,UAAe,WAAA,KAAA,SAAA,GAAA;AAEf,iBAAA,UAAA;AACA,iBAAA,OAAA;UAAa,OAAA;AAEb,iBAAA,aAAA;AAAA,UAAkB;AAGpB,eAAA,eAAA;AAAA,QAAoB,GAAA,GAAA;AAEtB;AAAA,MAAA;AAIF,UAAA,aAAA,KAAA,aAAA,SAAA,KAAA,aAAA,KAAA,WAAA,EAAA,KAAA;AAGA,UAAA,SAAA;AAAA,QAAa,MAAA,KAAA,QAAA;AAAA,QACQ,aAAA;AAAA,MACN;AAGf,WAAA,MAAA,QAAA,KAAA,KAAA,cAAA,MAAA,EAAA,KAAA,SAAA,KAAA;AACE,aAAA,eAAA;AACA,aAAA,aAAA;AAEA,YAAA,OAAA,IAAA,QAAA,IAAA,KAAA,QAAA,IAAA,KAAA,KAAA,SAAA,GAAA;AACE,cAAA,KAAA,QAAA,GAAA;AACE,iBAAA,OAAA,IAAA,KAAA;AAAA,UAAqB,OAAA;AAErB,iBAAA,OAAA,KAAA,KAAA,OAAA,IAAA,KAAA,IAAA;AAAA,UAA0C;AAE5C,eAAA,OAAA,IAAA,KAAA,gBAAA,KAAA;AAAA,QAA0C,WAAA,KAAA,QAAA,GAAA;AAE1C,eAAA,UAAA;AACA,eAAA,OAAA;QAAa,OAAA;AAEb,eAAA,aAAA;AAAA,QAAkB;AAAA,MACpB,CAAA,EAAA,MAAA,SAAA,KAAA;AAEAA,sBAAA,MAAA,MAAA,OAAA,+BAAA,YAAA,GAAA;AACA,aAAA,eAAA;AAGA,cAAA,aAAA,KAAA;AACA,cAAA,aAAA,SAAA,UAAA,UAAA,KAAA,SAAA,UAAA,CAAA;AAEA,YAAA,KAAA,SAAA,GAAA;AACE,eAAA,OAAA;AACA,eAAA,UAAA,WAAA,WAAA;AAAA,QAAqC,OAAA;AAErC,eAAA,aAAA;AAAA,QAAkB;AAAA,MACpB,CAAA;AAAA,IACD;AAAA;AAAA,IACH,cAAA,GAAA;AAIE,UAAA,CAAA,KAAA,CAAA,EAAA,iBAAA,CAAA,EAAA,cAAA;AAAwD;AAExD,WAAA,eAAA;AACA,WAAA,cAAA,SAAA,EAAA,cAAA,QAAA,GAAA,KAAA;AACA,WAAA,OAAA;AACA,WAAA,UAAA;AAEA,UAAA,KAAA,eAAA,GAAA;AACE,aAAA,UAAA;AACA,YAAA,KAAA,OAAA,MAAA,YAAA;AACE,eAAA,YAAA;AAAA,QAAiB;AAAA,MACnB;AAAA,IACF;AAAA;AAAA,IACF,YAAA,GAAA;AAKE,UAAA,CAAA,KAAA,CAAA,EAAA,iBAAA,CAAA,EAAA,cAAA;AAAwD;AAExD,UAAA,MAAA,EAAA,cAAA,QAAA;AACA,UAAA,KAAA;AACEA,sBAAAA,MAAA,WAAA;AAAA,UAAe;AAAA,QACb,CAAA;AAAA,MACD;AAAA,IACH;AAAA;AAAA,IACF,aAAA,GAAA;AAIE,WAAA,YAAA,EAAA,OAAA,WAAA;AAAA,IAAqC;AAAA;AAAA,IACvC,cAAA,GAAA;AAIE,UAAA,CAAA,KAAA,CAAA,EAAA,iBAAA,CAAA,EAAA,cAAA,WAAA,CAAA,EAAA,cAAA,QAAA;AAAwF;AAExFA,oBAAAA,MAAA,WAAA;AAAA,QAAe,KAAA,YAAA,EAAA,cAAA,QAAA;AAAA,MAC4B,CAAA;AAAA,IAC1C;AAAA;AAAA,IACH,oBAAA;AAIE,UAAA,OAAA;AAGA,UAAA,KAAA,aAAA;AACE,mBAAA,MAAA;AACE,eAAA,aAAA;AAAA,QAAkB,GAAA,GAAA;AAEpB;AAAA,MAAA;AAIFC,mCAAA,EAAA,KAAA,SAAA,KAAA;AACE,YAAA,QAAA,IAAA,QAAA,OAAA,IAAA,UAAA,QAAA,IAAA,MAAA;AAEE,cAAA,IAAA,KAAA,gBAAA,QAAA;AACE,iBAAA,aAAA,IAAA,KAAA;AAAA,UAA2B;AAAA,QAC7B;AAAA,MACF,CAAA,EAAA,MAAA,SAAA,KAAA;AAEAD,sBAAA,MAAA,MAAA,SAAA,+BAAA,YAAA,GAAA;AACA,aAAA,aAAA;AAAA,MAAkB,CAAA;AAAA,IACnB;AAAA,EACH;AAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtkBA,GAAG,WAAW,eAAe;"}