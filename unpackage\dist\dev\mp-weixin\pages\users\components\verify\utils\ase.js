"use strict";
function simpleAESEncrypt(text, key = "XwKsGlMcdPMEhR1B") {
  let result = "";
  for (let i = 0; i < text.length; i++) {
    const char = text.charCodeAt(i);
    const keyChar = key.charCodeAt(i % key.length);
    result += String.fromCharCode(char ^ keyChar);
  }
  return btoa(result);
}
function aesEncrypt(word, keyWord = "XwKsGlMcdPMEhR1B") {
  return simpleAESEncrypt(word, keyWord);
}
exports.aesEncrypt = aesEncrypt;
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/pages/users/components/verify/utils/ase.js.map
