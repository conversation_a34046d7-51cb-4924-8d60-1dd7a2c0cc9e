"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const libs_login = require("../../libs/login.js");
const common_assets = require("../../common/assets.js");
const emptyPage = () => "../../components/emptyPage/emptyPage.js";
const _sfc_main = {
  name: "MyTreeHole",
  components: {
    emptyPage
  },
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      barList: ["我的纸条", "抽到的纸条"],
      currentTab: 1,
      // 0-我的纸条 1-抽到的纸条
      myPapers: [],
      // 我发布的纸条
      drawnPapers: [],
      // 我抽到的纸条
      loading: false,
      isEmpty: false,
      loadStatus: "more",
      tipsTitle: "",
      paperList: [
        // 模拟数据，用于测试
        {
          id: 1,
          type: 4,
          // 语音纸条
          content: "",
          nickname: "幽默的麻雀",
          avatar: "",
          sex: 1,
          age: 19,
          location: "Sub",
          response_count: 1,
          create_time: "2024-01-20 21:09",
          status: "received",
          // received-已收到 sent-已发送
          voice_duration: 23
        },
        {
          id: 2,
          type: 1,
          // 问题
          content: "又爱违者的暂哥滴落我哦",
          nickname: "优雅的巨蜥",
          avatar: "",
          sex: 1,
          age: 24,
          location: "Sub",
          response_count: 7,
          create_time: "2024-01-20 16:55",
          status: "received",
          received_count: 53
        },
        {
          id: 3,
          type: 1,
          // 问题
          content: "有没有资深玩家，做过提高身体阅值的系统脱敏训练，我过相关经验的留言，请简单描述，谢谢！",
          nickname: "忧伤的鲨鱼",
          avatar: "",
          sex: 1,
          age: 37,
          location: "Dom",
          response_count: 6,
          create_time: "2024-01-20 15:54",
          status: "received",
          received_count: 91
        }
      ]
    };
  },
  computed: {
    // 当前显示的纸条列表
    currentPaperList() {
      if (this.currentTab === 0) {
        return this.myPapers.length > 0 ? this.myPapers : [];
      } else {
        return this.drawnPapers.length > 0 ? this.drawnPapers : [];
      }
    }
  },
  onLoad() {
    if (!this.checkLoginStatus()) {
      return;
    }
    this.loadData();
  },
  onShow() {
    if (!this.checkLoginStatus()) {
      return;
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    async loadData() {
      if (this.currentTab === 0) {
        await this.loadMyPapers();
      } else {
        await this.loadDrawnPapers();
      }
    },
    async loadMyPapers() {
      common_vendor.index.__f__("log", "at pages/note/my-tree-hole.vue:213", "=== 加载我的纸条 ===");
      this.loading = true;
      try {
        const params = {
          page: 1,
          limit: 20
        };
        common_vendor.index.__f__("log", "at pages/note/my-tree-hole.vue:221", "请求参数:", params);
        const result = await api_social.getMyTreeHoleBoxList(params);
        common_vendor.index.__f__("log", "at pages/note/my-tree-hole.vue:224", "我的纸条API响应:", result);
        if (result.status === 200 && result.data) {
          this.myPapers = result.data.list || [];
          this.isEmpty = this.myPapers.length === 0;
          common_vendor.index.__f__("log", "at pages/note/my-tree-hole.vue:229", "我的纸条数据:", this.myPapers);
          if (this.myPapers.length > 0) {
            common_vendor.index.showToast({
              title: `加载了${this.myPapers.length}条纸条`,
              icon: "none"
            });
          }
        } else {
          this.isEmpty = true;
          throw new Error(result.msg || "获取我的纸条失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/my-tree-hole.vue:242", "加载我的纸条失败:", error);
        common_vendor.index.showToast({
          title: error.message || "加载失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    async loadDrawnPapers() {
      common_vendor.index.__f__("log", "at pages/note/my-tree-hole.vue:253", "=== 加载抽到的纸条 ===");
      this.loading = true;
      try {
        const params = {
          page: 1,
          limit: 20
        };
        common_vendor.index.__f__("log", "at pages/note/my-tree-hole.vue:261", "请求参数:", params);
        const result = await api_social.getMyDrawnBoxList(params);
        common_vendor.index.__f__("log", "at pages/note/my-tree-hole.vue:264", "抽到的纸条API响应:", result);
        if (result.status === 200 && result.data) {
          this.drawnPapers = result.data.list || [];
          this.isEmpty = this.drawnPapers.length === 0;
          common_vendor.index.__f__("log", "at pages/note/my-tree-hole.vue:269", "抽到的纸条数据:", this.drawnPapers);
          if (this.drawnPapers.length > 0) {
            common_vendor.index.showToast({
              title: `加载了${this.drawnPapers.length}条纸条`,
              icon: "none"
            });
          }
        } else {
          this.isEmpty = true;
          throw new Error(result.msg || "获取抽到的纸条失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/my-tree-hole.vue:282", "加载抽到的纸条失败:", error);
        common_vendor.index.showToast({
          title: error.message || "加载失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    async switchTab(e) {
      const clickIdx = parseInt(e.currentTarget.dataset.idx);
      if (clickIdx === this.currentTab) {
        return;
      }
      this.currentTab = clickIdx;
      this.isEmpty = false;
      await this.loadData();
    },
    getGenderIcon(sex) {
      return sex === 1 ? "♂" : sex === 2 ? "♀" : "⚪";
    },
    getTypeIcon(type) {
      const icons = {
        1: "❓",
        2: "🤫",
        3: "🌠",
        4: "🎵"
      };
      return icons[type] || "📝";
    },
    getTypeText(type) {
      const texts = {
        1: "问题咨询",
        2: "秘密",
        3: "心愿",
        4: "语音纸条"
      };
      return texts[type] || "纸条";
    },
    getStatusText(item) {
      if (this.currentTab === 0) {
        return `已被${item.received_count || 0}人抽取`;
      } else {
        return `已被${item.received_count || 0}人抽取`;
      }
    },
    formatTime(timeStr) {
      const now = /* @__PURE__ */ new Date();
      const time = new Date(timeStr);
      const diff = now - time;
      const minutes = Math.floor(diff / (1e3 * 60));
      if (minutes < 60) {
        return `${minutes}分钟前`;
      } else if (minutes < 1440) {
        return `${Math.floor(minutes / 60)}小时前`;
      } else {
        return `${Math.floor(minutes / 1440)}天前`;
      }
    },
    // 统一错误处理
    handleError(error, defaultMessage = "操作失败") {
      var _a, _b, _c;
      common_vendor.index.__f__("error", "at pages/note/my-tree-hole.vue:357", "错误处理:", error);
      let message = defaultMessage;
      if (typeof error === "string") {
        message = error;
      } else if (error && typeof error === "object") {
        if (error.code === "NETWORK_ERROR" || ((_a = error.message) == null ? void 0 : _a.includes("Network"))) {
          message = "网络连接异常，请检查网络设置";
        } else if (error.code === "TIMEOUT" || ((_b = error.message) == null ? void 0 : _b.includes("timeout"))) {
          message = "请求超时，请稍后重试";
        } else {
          message = error.msg || error.message || ((_c = error.data) == null ? void 0 : _c.msg) || defaultMessage;
        }
      }
      common_vendor.index.showToast({
        title: message,
        icon: "none",
        duration: 2e3
      });
      return message;
    },
    // 检查登录状态
    checkLoginStatus() {
      const isLoggedIn = libs_login.checkLogin() && this.$store.state.app.token;
      if (!isLoggedIn) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请先登录后查看纸条",
          confirmText: "去登录",
          cancelText: "返回",
          success: (res) => {
            if (res.confirm) {
              libs_login.toLogin();
            } else {
              common_vendor.index.navigateBack();
            }
          }
        });
        return false;
      }
      return true;
    },
    viewPaperDetail(item) {
      common_vendor.index.__f__("log", "at pages/note/my-tree-hole.vue:407", "查看纸条详情:", item);
      common_vendor.index.__f__("log", "at pages/note/my-tree-hole.vue:408", "当前tab:", this.currentTab === 0 ? "我的纸条" : "抽到的纸条");
      if (!item.id) {
        common_vendor.index.showToast({
          title: "纸条ID不存在",
          icon: "none"
        });
        return;
      }
      common_vendor.index.__f__("log", "at pages/note/my-tree-hole.vue:418", "跳转到详情页面，ID:", item.id);
      common_vendor.index.navigateTo({
        url: `/pages/note/detail?id=${item.id}`
      });
    },
    viewResponses(item) {
      common_vendor.index.navigateTo({
        url: `/pages/note/paper-detail?id=${item.id}&type=${item.type}`
      });
    },
    quickReply(item) {
      common_vendor.index.navigateTo({
        url: `/pages/note/tree-hole-detail?id=${item.id}&type=${item.type}`
      });
    },
    goToMain() {
      common_vendor.index.navigateTo({
        url: "/pages/note/manghe"
      });
    },
    // 显示提示信息
    opTipsPopup(msg) {
      this.tipsTitle = msg;
      this.$refs.tipsPopup.open();
      setTimeout(() => {
        this.$refs.tipsPopup.close();
      }, 2e3);
    }
  }
};
if (!Array) {
  const _component_emptyPage = common_vendor.resolveComponent("emptyPage");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_emptyPage + _easycom_uni_popup2)();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_2$4,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: $data.titleBarHeight + "px",
    d: common_vendor.f($data.barList, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index == $data.currentTab ? "#000" : "#999",
        c: index == $data.currentTab ? "28rpx" : "26rpx",
        d: index == $data.currentTab ? 1 : 0,
        e: index,
        f: common_vendor.o((...args) => $options.switchTab && $options.switchTab(...args), index),
        g: index
      };
    }),
    e: $data.statusBarHeight + "px",
    f: $data.loading
  }, $data.loading ? {} : {}, {
    g: $data.isEmpty
  }, $data.isEmpty ? {
    h: common_vendor.o($options.goToMain),
    i: common_vendor.p({
      title: $data.currentTab == 0 ? "还没有发布过纸条" : "还没有抽到纸条",
      description: "空空如也，等待探索",
      image: "/static/img/empty.png",
      buttonText: $data.currentTab == 0 ? "去发布纸条" : "去抽取纸条"
    })
  } : {
    j: common_vendor.f($options.currentPaperList, (item, k0, i0) => {
      return {
        a: item.avatar || "/static/default-avatar.png",
        b: common_vendor.t(item.nickname),
        c: common_vendor.t($options.getGenderIcon(item.sex)),
        d: common_vendor.t(item.age),
        e: common_vendor.t(item.location),
        f: common_vendor.t($options.getTypeIcon(item.type)),
        g: common_vendor.t($options.getTypeText(item.type)),
        h: common_vendor.n("type-" + item.type),
        i: common_vendor.t(item.content),
        j: common_vendor.t($options.getStatusText(item)),
        k: common_vendor.t($options.formatTime(item.create_time)),
        l: common_vendor.t(item.response_count),
        m: common_vendor.o(($event) => $options.viewResponses(item), item.id),
        n: common_vendor.o(($event) => $options.quickReply(item), item.id),
        o: item.id,
        p: common_vendor.o(($event) => $options.viewPaperDetail(item), item.id)
      };
    })
  }, {
    k: $options.currentPaperList.length > 0 && $data.loadStatus === "noMore"
  }, $options.currentPaperList.length > 0 && $data.loadStatus === "noMore" ? {} : {}, {
    l: "calc(" + ($data.statusBarHeight + $data.titleBarHeight) + "px + 90rpx)",
    m: common_vendor.t($data.tipsTitle),
    n: common_vendor.sr("tipsPopup", "9189c3de-1"),
    o: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/note/my-tree-hole.js.map
