
page {
  background: #f8f8f8;
  padding: 0 30rpx;
  box-sizing: border-box;
  overflow-x: hidden;
}
.title-box {
	padding: 20rpx 0;
	font-size: 40rpx;
	font-weight: bold;
}
.table {
	padding: 30rpx 0;
	color: #999;
	font-size: 24rpx;
	font-weight: 500;
}
.list-box {
  width: 100%;
  border-radius: 24rpx;
  overflow: hidden;
  box-sizing: border-box;
}
.list-box .list {
	margin: 0;
	padding: 0;
	width: 100%;
	background: #fff!important;
	border-radius: 0;
}
.list-box .list:last-child {
	border-bottom: none;
}
.list-box .list .icon {
	margin: 0 30rpx;
	width: 38rpx;
	height: 38rpx;
}
.list-box .list-item {
	width: calc(100% - 98rpx);
	padding: 30rpx 30rpx 30rpx 0;
	justify-content: space-between;
}
.list-box .list-item .title {
	font-size: 24rpx;
	font-weight: 500;
	min-width: 120rpx;
}
.list-box .list-item image {
	width: 24rpx;
	height: 24rpx;
	transform: rotate(-90deg);
}

/* 按钮样式 */
.input-btn {
	background: transparent !important;
	border: none !important;
	color: #868686 !important;
	font-size: 24rpx !important;
	text-align: right;
	padding: 0 !important;
	margin: 0 !important;
	line-height: normal !important;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}
.input-btn::after {
	border: none !important;
}

/* 数值显示样式 */
.list-box .list-item .value {
	color: #868686;
	font-size: 24rpx;
	margin-right: 10rpx;
}

/* 退出登录按钮样式 */
.logout-section {
  margin-top: 30rpx;
}
.logout-btn {
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 690rpx;
  height: 90rpx;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}
.logout-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);
}
.logout-btn::after {
  border: none;
}
.logout-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  filter: brightness(0) invert(1);
}
.logout-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}
.version {
	padding: 60rpx 0;
	flex-direction: column;
	justify-content: center;
}
.version image {
	margin-right: 10rpx;
	width: 20rpx;
	height: 20rpx;
}
.version text {
	color: #999;
	font-size: 18rpx;
}
.df {
  display: flex;
  align-items: center;
}
.acea-row {
  display: flex;
  flex-direction: row;
}
.row-center-wrapper {
  justify-content: center;
}
.row-between-wrapper {
  justify-content: space-between;
}
.row-middle {
  align-items: center;
}
.bb1 {
  border-bottom: 1px solid #f8f8f8;
}

/* 图标样式 */
.iconfont {
	font-size: 24rpx;
	color: #ccc;
}
.icon-xiangyou::before {
	content: '>';
}
.icon-suozi::before {
	content: '🔒';
}

/* 列表项间距调整 */
.list-box .list:not(:last-child) .list-item {
	border-bottom: 1px solid #f8f8f8;
}
.list-box .list:last-child .list-item {
	border-bottom: none;
}

/* 账号切换样式 */
.wrapper {
  margin: 10rpx 0;
  background-color: #fff;
  padding: 36rpx 30rpx 13rpx 30rpx;
  border-radius: 16rpx;
}
.wrapper .title {
  margin-bottom: 30rpx;
  font-size: 32rpx;
  color: #282828;
  font-weight: bold;
}
.wrapper .wrapList .item {
  width: 690rpx;
  height: 160rpx;
  background-color: #f8f8f8;
  border-radius: 20rpx;
  margin-bottom: 22rpx;
  padding: 0 30rpx;
  position: relative;
  border: 2rpx solid #f8f8f8;
  box-sizing: border-box;
}
.wrapper .wrapList .item.on {
  border-color: var(--view-theme);
  border-radius: 20rpx;
  background-color: #fff9f9;
}
.wrapper .wrapList .item .picTxt {
  width: 445rpx;
}
.wrapper .wrapList .item .picTxt .pictrue {
  width: 96rpx;
  height: 96rpx;
  position: relative;
}
.wrapper .wrapList .item .picTxt .pictrue image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.wrapper .wrapList .item .picTxt .text {
  width: 325rpx;
}
.wrapper .wrapList .item .picTxt .text .name {
  width: 100%;
  font-size: 30rpx;
  color: #282828;
}
.wrapper .wrapList .item .picTxt .text .phone {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.wrapper .wrapList .item .bnt {
  font-size: 24rpx;
  border-radius: 27rpx;
  width: 140rpx;
  height: 54rpx;
  border: 2rpx solid var(--view-theme);
  color: var(--view-theme);
  background-color: #fff;
}
.wrapper .wrapList .item .currentBnt {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 26rpx;
  background-color: rgba(233, 51, 35, 0.1);
  width: 140rpx;
  height: 48rpx;
  border-radius: 0 20rpx 0 20rpx;
  color: var(--view-theme);
}

/* 输入框焦点样式 */
.list-box .list-item .input input:focus {
	color: #333;
}

/* 禁用状态样式 */
.list-box .list-item .input input[disabled] {
	color: #ccc;
}

/* 表单区域样式 */
form {
	margin-bottom: 20rpx;
}

/* 针对不同平台的样式适配 */
button {
  background-color: transparent !important;
}















/* 语言切换选择器样式 */
.picker-content {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: #868686;
  font-size: 24rpx;
}
.picker-text {
  margin-right: 10rpx;
}
.picker-content .iconfont {
  font-size: 24rpx;
  color: #ccc;
}
