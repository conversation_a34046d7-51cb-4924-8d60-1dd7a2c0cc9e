{"version": 3, "file": "money.js", "sources": ["components/money/money.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy9tb25leS9tb25leS52dWU"], "sourcesContent": ["<template>\n  <view class=\"money-box\">\n    <view class=\"unit\" :style=\"{'font-size': ts, 'line-height': ts, 'color': cor}\">¥</view>\n    <view class=\"sale\" :style=\"{'font-size': qs, 'line-height': qs, 'color': cor}\">{{ qsHandle(price) }}</view>\n    <view class=\"unit\" :style=\"{'font-size': ts, 'line-height': ts, 'color': cor}\">{{ tsHandle(price) }}</view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'money',\n  props: {\n    type: {\n      type: Number,\n      default: 0\n    },\n    price: {\n      type: [String, Number],\n      default: '0.00'\n    },\n    qs: {\n      type: String,\n      default: '38rpx'\n    },\n    ts: {\n      type: String,\n      default: '22rpx'\n    },\n    cor: {\n      type: String,\n      default: '#000'\n    }\n  },\n  methods: {\n    qsHandle(price) {\n      return parseInt(String(price));\n    },\n    tsHandle(price) {\n      var decimalPart = parseFloat(String(price)).toFixed(2).slice(-2);\n      \n      if (this.type == 1 && decimalPart === '00') {\n        return '';\n      } else if (this.type == 1 && decimalPart[1] === '0') {\n        return '.' + decimalPart[0];\n      } else {\n        return '.' + decimalPart;\n      }\n    }\n  }\n}\n</script>\n\n<style>\n.money-box {\n  display: flex;\n  align-items: flex-end;\n  font-weight: bolder;\n}\n\n.money-box .sale {\n  margin: 0 2rpx;\n}\n\n.money-box .unit {\n  margin-bottom: 2rpx;\n}\n</style> ", "import Component from 'D:/uniapp/vue3/components/money/money.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AASA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,OAAO;AAAA,MACL,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACV;AAAA,IACD,IAAI;AAAA,MACF,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,IAAI;AAAA,MACF,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,SAAS,OAAO;AACd,aAAO,SAAS,OAAO,KAAK,CAAC;AAAA,IAC9B;AAAA,IACD,SAAS,OAAO;AACd,UAAI,cAAc,WAAW,OAAO,KAAK,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM,EAAE;AAE/D,UAAI,KAAK,QAAQ,KAAK,gBAAgB,MAAM;AAC1C,eAAO;AAAA,MACT,WAAW,KAAK,QAAQ,KAAK,YAAY,CAAC,MAAM,KAAK;AACnD,eAAO,MAAM,YAAY,CAAC;AAAA,aACrB;AACL,eAAO,MAAM;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;AChDA,GAAG,gBAAgB,SAAS;"}