{"version": 3, "file": "public.js", "sources": ["api/public.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport request from \"@/utils/request.js\";\nimport wechat from \"@/libs/wechat.js\";\n\n/**\n * 获取微信sdk配置\n * @returns {*}\n */\nexport function getWechatConfig() {\n\treturn request.get(\n\t\t\"wechat/config\", {\n\t\t\turl: wechat.signLink()\n\t\t}, {\n\t\t\tnoAuth: true\n\t\t}\n\t);\n}\n\n/**\n * 获取微信sdk配置\n * @returns {*}\n */\nexport function wechatAuth(code, spread, login_type) {\n\treturn request.get(\n\t\t\"wechat/auth\", {\n\t\t\tcode,\n\t\t\tspread,\n\t\t\tlogin_type\n\t\t}, {\n\t\t\tnoAuth: true\n\t\t}\n\t);\n}\n\n/**\n * 获取登录授权login\n * \n */\nexport function getLogo() {\n\treturn request.get('wechat/get_logo', {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 小程序用户登录\n * @param data object 小程序用户登陆信息\n */\nexport function login(data) {\n\treturn request.post(\"wechat/mp_auth\", data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 静默授权\n * @param {Object} data\n */\nexport function silenceAuth(data) {\n\t//#ifdef MP\n\treturn request.get(\"v2/wechat/silence_auth\", data, {\n\t\tnoAuth: true\n\t});\n\t//#endif\n\t//#ifdef H5\n\treturn request.get(\"v2/wechat/auth_type\", data, {\n\t\tnoAuth: true\n\t});\n\t//#endif\n}\n\n/**\n * 分享\n * @returns {*}\n */\nexport function getShare() {\n\treturn request.get(\"share\", {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 公众号登录\n * @returns {*}\n */\nexport function wechatAuthLogin(data) {\n\treturn request.get(\"v2/wechat/auth_login\", data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取关注海报\n * @returns {*}\n */\nexport function follow() {\n\treturn request.get(\"wechat/follow\", {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * code生成用户\n * @returns {*}\n */\nexport function authType(data) {\n\treturn request.get(\"v2/routine/auth_type\", data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 授权登录\n * @returns {*}\n */\nexport function authLogin(data) {\n\treturn request.get(\"v2/routine/auth_login\", data, {\n\t\tnoAuth: true\n\t});\n}\n\n\n/**\n * 获取图片base64\n * @retins {*}\n * */\nexport function imageBase64(image, code) {\n\treturn request.post(\n\t\t\"image_base64\", {\n\t\t\timage: image,\n\t\t\tcode: code\n\t\t}, {\n\t\t\tnoAuth: true\n\t\t}\n\t);\n}\n\n/**\n * 自动复制口令功能\n * @returns {*}\n */\nexport function copyWords() {\n\treturn request.get(\"copy_words\", {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取商城是否强制绑定手机号\n */\nexport function getShopConfig() {\n\treturn request.get('v2/bind_status', {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 小程序绑定手机号\n * @param {Object} data\n */\nexport function routineBindingPhone(data) {\n\treturn request.post('v2/routine/auth_binding_phone', data, {\n\t\tnoAuth: true\n\t});\n}\n/**\n * 小程序绑定手机号\n * @param {Object} data\n */\nexport function wechatBindingPhone(data) {\n\treturn request.post('v2/wechat/auth_binding_phone', data, {\n\t\tnoAuth: true\n\t});\n}\n/**\n * 小程序手机号登录\n * @param {Object} data\n */\nexport function phoneLogin(data) {\n\treturn request.post('v2/routine/phone_login', data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 小程序用户登录\n * @param data object 小程序用户登陆信息\n */\nexport function routineLogin(data) {\n\treturn request.get(\"v2/wechat/routine_auth\", data, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取微信sdk配置\n * @returns {*}\n */\nexport function wechatAuthV2(code, spread) {\n\treturn request.get(\n\t\t\"v2/wechat/auth\", {\n\t\t\tcode,\n\t\t\tspread\n\t\t}, {\n\t\t\tnoAuth: true\n\t\t}\n\t);\n}\n\n/**\n * 获取组件底部菜单\n * @param data object 获取组件底部菜单\n */\nexport function getNavigation(data) {\n\treturn request.get(\"navigation\", data, {\n\t\tnoAuth: true\n\t});\n}\nexport function getSubscribe() {\n\treturn request.get(\"subscribe\", {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取版本信息\n * @param 系统类型\n */\nexport function getUpdateInfo(type) {\n\treturn request.get(\"get_new_app/\" + type, {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 获取首页DIY数据版本号\n * \n */\nexport function getVersion(name) {\n\treturn request.get(`v2/diy/get_version/${name}`, {}, {\n\t\tnoAuth: true\n\t});\n}\n/**\n * 获取商品分类版本号\n * \n */\nexport function getCategoryVersion(name) {\n\treturn request.get(`category_version`, {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * 配置信息\n * \n */\nexport function basicConfig(name) {\n\treturn request.get(`basic_config`, {}, {\n\t\tnoAuth: true\n\t});\n}\n/**\n * 后台版本信息\n * \n */\nexport function getSystemVersion() {\n\treturn request.get(`version`, {}, {\n\t\tnoAuth: true\n\t});\n}\n\n/**\n * iframe登录\n * \n */\nexport function remoteRegister(data) {\n\treturn request.get(`remote_register`, data, {\n\t\tnoAuth: true\n\t});\n}\n"], "names": ["request"], "mappings": ";;;AA+CO,SAAS,UAAU;AACzB,SAAOA,sBAAQ,IAAI,mBAAmB,IAAI;AAAA,IACzC,QAAQ;AAAA,EACV,CAAE;AACF;AA2CO,SAAS,gBAAgB,MAAM;AACrC,SAAOA,sBAAQ,IAAI,wBAAwB,MAAM;AAAA,IAChD,QAAQ;AAAA,EACV,CAAE;AACF;AAgBO,SAAS,SAAS,MAAM;AAC9B,SAAOA,sBAAQ,IAAI,wBAAwB,MAAM;AAAA,IAChD,QAAQ;AAAA,EACV,CAAE;AACF;AAMO,SAAS,UAAU,MAAM;AAC/B,SAAOA,sBAAQ,IAAI,yBAAyB,MAAM;AAAA,IACjD,QAAQ;AAAA,EACV,CAAE;AACF;AAyCO,SAAS,oBAAoB,MAAM;AACzC,SAAOA,sBAAQ,KAAK,iCAAiC,MAAM;AAAA,IAC1D,QAAQ;AAAA,EACV,CAAE;AACF;AAcO,SAAS,WAAW,MAAM;AAChC,SAAOA,sBAAQ,KAAK,0BAA0B,MAAM;AAAA,IACnD,QAAQ;AAAA,EACV,CAAE;AACF;AAMO,SAAS,aAAa,MAAM;AAClC,SAAOA,sBAAQ,IAAI,0BAA0B,MAAM;AAAA,IAClD,QAAQ;AAAA,EACV,CAAE;AACF;AAiEO,SAAS,YAAY,MAAM;AACjC,SAAOA,cAAO,QAAC,IAAI,gBAAgB,CAAA,GAAI;AAAA,IACtC,QAAQ;AAAA,EACV,CAAE;AACF;AAeO,SAAS,eAAe,MAAM;AACpC,SAAOA,cAAO,QAAC,IAAI,mBAAmB,MAAM;AAAA,IAC3C,QAAQ;AAAA,EACV,CAAE;AACF;;;;;;;;;;"}