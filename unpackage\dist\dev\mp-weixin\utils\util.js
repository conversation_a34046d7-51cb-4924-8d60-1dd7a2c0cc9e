"use strict";
const common_vendor = require("../common/vendor.js");
const store_index = require("../store/index.js");
const config_app = require("../config/app.js");
const { HTTP_REQUEST_URL, TOKENNAME } = config_app.config;
const util = {
  /**
   * 提示信息
   * @param {Object} options 配置项
   */
  Tips(options) {
    common_vendor.index.showToast({
      title: options.title,
      icon: options.icon || "none",
      duration: options.duration || 2e3
    });
  },
  /**
   * 图片上传方法（简化版）
   * @param {Object} opt 配置项
   * @param {Function} successCallback 成功回调
   * @param {Function} errorCallback 失败回调
   */
  uploadImageChange: function(opt, successCallback, errorCallback, sizeCallback) {
    let that = this;
    if (typeof opt === "string") {
      let url = opt;
      opt = {};
      opt.url = url;
    }
    let count = opt.count || 1, sizeType = opt.sizeType || ["compressed"], sourceType = opt.sourceType || ["album", "camera"], uploadUrl = opt.url || "", inputName = opt.name || "pics";
    opt.fileType || "image";
    common_vendor.index.chooseImage({
      count,
      sizeType,
      sourceType,
      success: function(res) {
        common_vendor.index.showLoading({
          title: "图片上传中..."
        });
        let totalCount = res.tempFilePaths.length;
        let uploadedImages = [];
        function uploadNext(index) {
          if (index >= totalCount) {
            common_vendor.index.hideLoading();
            if (successCallback) {
              if (uploadedImages.length === 1) {
                successCallback({
                  status: 200,
                  data: uploadedImages[0]
                });
              } else {
                successCallback({
                  status: 200,
                  data: uploadedImages
                });
              }
            }
            return;
          }
          let filePath = res.tempFilePaths[index];
          common_vendor.index.uploadFile({
            url: HTTP_REQUEST_URL + "/api/" + uploadUrl,
            filePath,
            name: inputName,
            formData: {
              "filename": inputName
            },
            header: {
              [TOKENNAME]: "Bearer " + store_index.store.state.app.token
            },
            success: function(uploadRes) {
              try {
                let data = JSON.parse(uploadRes.data);
                common_vendor.index.__f__("log", "at utils/util.js:90", "上传响应数据:", data);
                if (data.status === 200) {
                  let imageUrl = "";
                  let imageWide = 0;
                  let imageHigh = 0;
                  if (data.data && data.data.url) {
                    imageUrl = data.data.url;
                    imageWide = data.data.wide || data.data.w || 0;
                    imageHigh = data.data.high || data.data.h || 0;
                  } else if (data.data && data.data.src) {
                    imageUrl = data.data.src;
                    imageWide = data.data.wide || data.data.w || 0;
                    imageHigh = data.data.high || data.data.h || 0;
                  } else if (data.url) {
                    imageUrl = data.url;
                    imageWide = data.wide || data.w || 0;
                    imageHigh = data.high || data.h || 0;
                  } else if (data.src) {
                    imageUrl = data.src;
                    imageWide = data.wide || data.w || 0;
                    imageHigh = data.high || data.h || 0;
                  }
                  if (imageUrl) {
                    uploadedImages.push({
                      url: imageUrl,
                      wide: imageWide,
                      high: imageHigh
                    });
                  } else {
                    common_vendor.index.__f__("error", "at utils/util.js:124", "无法从响应中获取图片URL:", data);
                  }
                } else {
                  common_vendor.index.__f__("error", "at utils/util.js:127", "上传响应状态错误:", data);
                }
              } catch (e) {
                common_vendor.index.__f__("error", "at utils/util.js:130", "解析上传响应失败:", e, uploadRes.data);
              }
              uploadNext(index + 1);
            },
            fail: function(err) {
              common_vendor.index.__f__("error", "at utils/util.js:137", "图片上传失败:", err);
              uploadNext(index + 1);
            }
          });
        }
        uploadNext(0);
      },
      fail: function(err) {
        that.Tips({
          title: err.errMsg || "选择图片失败"
        });
      }
    });
  },
  /**
   * 简单视频上传方法
   * @param {Object} opt 配置项
   * @param {Function} successCallback 成功回调
   * @param {Function} errorCallback 失败回调
   */
  uploadVideoSimple: function(opt, successCallback, errorCallback) {
    let that = this;
    if (typeof opt === "string") {
      let url = opt;
      opt = {};
      opt.url = url;
    }
    let count = opt.count || 1, sourceType = opt.sourceType || ["album", "camera"], maxDuration = opt.maxDuration || 60, camera = opt.camera || "back", uploadUrl = opt.url || "upload/video", inputName = opt.name || "file";
    common_vendor.index.chooseVideo({
      count,
      sourceType,
      maxDuration,
      camera,
      success: function(res) {
        if (res.size && res.size > 100 * 1024 * 1024) {
          that.Tips({
            title: "视频大小不能超过100MB"
          });
          return;
        }
        if (res.duration && res.duration > 60) {
          that.Tips({
            title: "视频时长不能超过60秒"
          });
          return;
        }
        common_vendor.index.showLoading({
          title: "视频上传中...",
          mask: true
        });
        common_vendor.index.uploadFile({
          url: HTTP_REQUEST_URL + "/api/" + uploadUrl,
          filePath: res.tempFilePath,
          name: inputName,
          formData: {
            chunkNumber: 1,
            currentChunkSize: res.size || 0,
            chunkSize: res.size || 0,
            totalChunks: 1,
            filename: `video_${Date.now()}.mp4`,
            md5: ""
          },
          header: {
            [TOKENNAME]: "Bearer " + store_index.store.state.app.token
          },
          success: function(uploadRes) {
            common_vendor.index.hideLoading();
            try {
              let data = JSON.parse(uploadRes.data);
              if (data.status === 200 && data.data) {
                let result = {
                  status: 200,
                  data: {
                    url: data.data.url || data.data.file_path || data.data.src || data.url || data.file_path || data.src,
                    high: res.height || 720,
                    wide: res.width || 1280,
                    size: res.size || 0,
                    duration: res.duration || 0
                  }
                };
                if (successCallback)
                  successCallback(result);
              } else {
                common_vendor.index.__f__("error", "at utils/util.js:236", "视频上传响应格式错误:", data);
                that.Tips({
                  title: data.msg || "上传失败"
                });
                if (errorCallback)
                  errorCallback(data);
              }
            } catch (e) {
              common_vendor.index.__f__("error", "at utils/util.js:243", "解析视频上传响应失败:", e);
              that.Tips({
                title: "上传失败，请重试"
              });
              if (errorCallback)
                errorCallback(e);
            }
          },
          fail: function(err) {
            common_vendor.index.hideLoading();
            that.Tips({
              title: "上传失败，请重试"
            });
            if (errorCallback)
              errorCallback(err);
          }
        });
      },
      fail: function(err) {
        that.Tips({
          title: err.errMsg || "选择视频失败"
        });
      }
    });
  },
  /**
   * 保存视频数据
   * @param {String} videoName 视频名称
   * @param {String} videoUrl 视频URL
   * @param {Function} successCallback 成功回调
   * @param {Function} errorCallback 失败回调
   */
  saveVideoData: function(videoName, videoUrl, successCallback, errorCallback) {
    let that = this;
    common_vendor.index.request({
      url: HTTP_REQUEST_URL + "/api/upload/video_data_save",
      method: "POST",
      data: {
        pid: 0,
        video_name: videoName,
        video_path: videoUrl
      },
      header: {
        [TOKENNAME]: "Bearer " + store_index.store.state.app.token
      },
      success: function(res) {
        common_vendor.index.hideLoading();
        if (res.statusCode === 200 && res.data.status === 200) {
          if (successCallback)
            successCallback({
              status: 200,
              data: {
                url: videoUrl,
                name: videoName
              }
            });
        } else {
          that.Tips({
            title: res.data.msg || "保存失败"
          });
          if (errorCallback)
            errorCallback(res.data);
        }
      },
      fail: function(err) {
        common_vendor.index.hideLoading();
        that.Tips({
          title: "保存失败，请重试"
        });
        if (errorCallback)
          errorCallback(err);
      }
    });
  },
  /**
   * 音频上传方法
   * @param {Object} opt 配置项
   * @param {Function} successCallback 成功回调
   * @param {Function} errorCallback 失败回调
   */
  uploadAudio: function(opt, successCallback, errorCallback) {
    let that = this;
    if (typeof opt === "string") {
      let url = opt;
      opt = {};
      opt.url = url;
    }
    let count = opt.count || 1;
    opt.sourceType || ["album"];
    opt.maxDuration || 300;
    let uploadUrl = opt.url || "upload/audio", inputName = opt.name || "file";
    common_vendor.index.chooseFile({
      count,
      type: "all",
      extension: ["mp3", "wav", "ogg", "aac", "m4a", "flac"],
      success: function(res) {
        if (res.tempFiles && res.tempFiles[0] && res.tempFiles[0].size > 50 * 1024 * 1024) {
          that.Tips({
            title: "音频大小不能超过50MB"
          });
          return;
        }
        common_vendor.index.showLoading({
          title: "音频上传中...",
          mask: true
        });
        common_vendor.index.uploadFile({
          url: HTTP_REQUEST_URL + "/api/" + uploadUrl,
          filePath: res.tempFilePaths[0],
          name: inputName,
          formData: {
            chunkNumber: 1,
            currentChunkSize: res.tempFiles[0].size || 0,
            chunkSize: res.tempFiles[0].size || 0,
            totalChunks: 1,
            filename: res.tempFiles[0].name || `audio_${Date.now()}.mp3`,
            md5: ""
          },
          header: {
            [TOKENNAME]: "Bearer " + store_index.store.state.app.token
          },
          success: function(uploadRes) {
            common_vendor.index.hideLoading();
            try {
              let data = JSON.parse(uploadRes.data);
              if (data.status === 200 && data.data) {
                let result = {
                  status: 200,
                  data: {
                    url: data.data.url || data.data.file_path || data.data.src || data.url || data.file_path || data.src,
                    name: res.tempFiles[0].name || "音频文件",
                    size: res.tempFiles[0].size || 0
                  }
                };
                if (successCallback)
                  successCallback(result);
              } else {
                common_vendor.index.__f__("error", "at utils/util.js:385", "音频上传响应格式错误:", data);
                that.Tips({
                  title: data.msg || "上传失败"
                });
                if (errorCallback)
                  errorCallback(data);
              }
            } catch (e) {
              common_vendor.index.__f__("error", "at utils/util.js:392", "解析音频上传响应失败:", e);
              that.Tips({
                title: "上传失败，请重试"
              });
              if (errorCallback)
                errorCallback(e);
            }
          },
          fail: function(err) {
            common_vendor.index.hideLoading();
            that.Tips({
              title: "上传失败，请重试"
            });
            if (errorCallback)
              errorCallback(err);
          }
        });
      },
      fail: function(err) {
        that.Tips({
          title: err.errMsg || "选择音频失败"
        });
      }
    });
  }
};
exports.util = util;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/util.js.map
