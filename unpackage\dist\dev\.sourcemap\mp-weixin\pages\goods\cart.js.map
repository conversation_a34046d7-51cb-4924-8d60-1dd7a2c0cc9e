{"version": 3, "file": "cart.js", "sources": ["pages/goods/cart.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZ29vZHMvY2FydC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 顶部导航栏 -->\r\n    <view class=\"nav-box bfw df\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\r\n      <view class=\"nav-back df\" :style=\"{'height': titleBarHeight + 'px'}\" @tap=\"navBack\">\r\n        <image src=\"/static/img/back.png\" style=\"width:34rpx;height:34rpx\"></image>\r\n      </view>\r\n      <view class=\"nav-title\">购物车</view>\r\n    </view>\r\n    \r\n    <view class=\"content\" :style=\"{'padding-top': statusBarHeight + titleBarHeight + 'px'}\">\r\n      <!-- 购物车为空时的展示 -->\r\n      <view v-if=\"isEmpty\" class=\"empty-box df\">\r\n        <image src=\"/static/img/inset/null.png\" />\r\n        <view class=\"e1\">购物车空空的</view>\r\n        <view class=\"e2\">去挑点喜欢的商品装满购物车</view>\r\n      </view>\r\n      \r\n      <!-- 购物车有商品时的展示 -->\r\n      <view v-else class=\"list-box\">\r\n        <view class=\"list-item df\" v-for=\"(item, index) in list\" :key=\"index\">\r\n          <!-- 商品选择 -->\r\n          <view class=\"choose df\" @tap=\"cartCheck\" :data-check=\"item.check\" :data-id=\"item.id\">\r\n            <image :src=\"item.check == 1 ? '/static/img/c1.png' : '/static/img/c.png'\"></image>\r\n          </view>\r\n          \r\n          <!-- 商品图片 -->\r\n          <view class=\"item-img df\" @tap=\"navigateToFun\" :data-url=\"'goods/details?id='+item.goods_id\" data-pay=\"false\">\r\n            <image :src=\"item.product.img\" mode=\"aspectFill\"></image>\r\n            <view v-if=\"item.status_str\" class=\"sold-out df\">{{item.status_str}}</view>\r\n          </view>\r\n          \r\n          <!-- 商品信息 -->\r\n          <view class=\"info\">\r\n            <view class=\"info-sp\" style=\"height:calc(100% - 60rpx)\">\r\n              <view style=\"width:calc(100% - 84rpx)\">\r\n                <view class=\"txt ohto\">{{item.goods_name}}</view>\r\n                <view class=\"df\" @tap=\"openProductClick\" :data-id=\"item.id\">\r\n                  <view class=\"txt-box\">\r\n                    {{item.product.name}}\r\n                    <view class=\"dw df\">\r\n                      <image src=\"/static/img/x.png\" style=\"width:20rpx;height:20rpx\"></image>\r\n                    </view>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n              <view class=\"more\" @tap=\"openMore\" :data-id=\"item.id\">\r\n                <image src=\"/static/img/gd.png\" style=\"width:24rpx;height:24rpx\"></image>\r\n              </view>\r\n            </view>\r\n            \r\n            <view class=\"info-sp\" style=\"align-items:flex-end\">\r\n              <money :price=\"item.product.price\"></money>\r\n              \r\n              <!-- 数量控制区域 -->\r\n              <view v-if=\"item.is_quantity\" class=\"quantity-box df\">\r\n                <view class=\"quantity-btn\" \r\n                      :style=\"{'color': quantity > 1 ? '#000' : '#ccc'}\" \r\n                      @tap=\"quantityBtn\" \r\n                      :data-id=\"item.id\" \r\n                      data-type=\"0\">－</view>\r\n                <input @blur=\"quantityBtn\" \r\n                       :data-id=\"item.id\" \r\n                       data-type=\"2\" \r\n                       type=\"number\" \r\n                       maxlength=\"4\" \r\n                       v-model=\"quantity\" />\r\n                <view class=\"quantity-btn\" \r\n                      :style=\"{'color': quantity < item.product.stock ? '#000' : '#ccc'}\" \r\n                      @tap=\"quantityBtn\" \r\n                      :data-id=\"item.id\" \r\n                      data-type=\"1\">＋</view>\r\n              </view>\r\n              <view v-else class=\"quantity-box df\" @tap=\"openQuantity\" :data-id=\"item.id\">\r\n                <view style=\"width:80rpx;text-align:center\">{{item.quantity}}</view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 订单金额汇总 -->\r\n        <view class=\"list-count df\" style=\"margin-top:30rpx\">\r\n          <text class=\"t1\">实际商品总额</text>\r\n          <view class=\"df\">\r\n            <text class=\"t3\">已选 {{sumCount}} 件</text>\r\n            <money :price=\"goodsAmount\" :qs=\"'28rpx'\" :ts=\"'18rpx'\"></money>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"list-count df\">\r\n          <text class=\"t1\">优惠金额</text>\r\n          <view class=\"df\">\r\n            <text class=\"t3\">立减</text>\r\n            <money :price=\"discountAmount\" :cor=\"'#999'\" :qs=\"'28rpx'\" :ts=\"'18rpx'\"></money>\r\n          </view>\r\n        </view>\r\n        \r\n        <view v-if=\"appCard\" class=\"list-count df\">\r\n          <text class=\"t1\">卡券</text>\r\n          <view class=\"df\">\r\n            <text class=\"t3\" :style=\"{'color': cardAmount ? '#FA5150' : '#999'}\">优惠</text>\r\n            <money :price=\"cardAmount\" :cor=\"cardAmount ? '#FA5150' : '#999'\" :qs=\"'28rpx'\" :ts=\"'18rpx'\"></money>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"list-count df\">\r\n          <text class=\"t1\">运费</text>\r\n          <view class=\"df\">\r\n            <text class=\"t3\">包邮</text>\r\n            <money :price=\"0.00\" :qs=\"'28rpx'\" :ts=\"'18rpx'\"></money>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"list-count df\">\r\n          <text class=\"t2\">总计</text>\r\n          <money :price=\"orderAmount\" :qs=\"'28rpx'\" :ts=\"'18rpx'\"></money>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部操作栏 -->\r\n    <view v-if=\"!isEmpty\" class=\"footer-box bUp df\">\r\n      <view class=\"footer-all df\" @tap=\"cartCheck\" :data-check=\"allCheck\" data-id=\"-1\">\r\n        <image :src=\"allCheck == 1 ? '/static/img/c1.png' : '/static/img/c.png'\"></image>\r\n        <text>全选</text>\r\n      </view>\r\n      \r\n      <view :class=\"['btn', 'not', checkCount > 0 && 'act']\" @tap=\"navigateToFun\" data-url=\"order/settlement?type=0\" data-pay=\"true\">\r\n        <text v-if=\"orderAmount > 0\">¥ {{orderAmount}}</text> 结算 \r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 规格选择弹窗 -->\r\n    <uni-popup ref=\"goodsPopup\" type=\"bottom\" :safe-area=\"false\">\r\n      <view class=\"goods-box\">\r\n        <view class=\"goods-top df\">\r\n          <view class=\"popup-name\">{{goodsName}}</view>\r\n          <view class=\"popup-close df\" @tap=\"closePopupClick(1)\">\r\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"specs-title\">规格</view>\r\n        <view class=\"specs-overflow\">\r\n          <view class=\"specs-box\" style=\"display:flex;padding:0 10rpx\">\r\n            <view \r\n              v-for=\"(item, index) in productList\" \r\n              :key=\"index\" \r\n              class=\"specs-item\" \r\n              :style=\"{'border-color': productIdx == index ? '#000' : '#f8f8f8'}\"\r\n              @tap=\"paramClick\" \r\n              :data-idx=\"index\">\r\n              <image class=\"img\" :src=\"item.img\" mode=\"aspectFill\"></image>\r\n              <view class=\"fd df\" @tap.stop=\"imgParamTap\" :data-url=\"item.img\">\r\n                <image src=\"/static/img/fd.png\" style=\"width:22rpx;height:22rpx\"></image>\r\n              </view>\r\n              <view class=\"name\">\r\n                <view>{{item.name}}</view>\r\n                <view style=\"margin-top:10rpx\">¥ {{item.price}}</view>\r\n              </view>\r\n            </view>\r\n            <view style=\"flex-shrink:0;width:20rpx;height:20rpx\"></view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view :class=\"['btn', 'gtn', productList[productIdx].stock > 0 && 'act']\" @tap=\"cartProductClick\">\r\n          {{productList[productIdx].stock > 0 ? '确定' : '暂无库存'}}\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n    \r\n    <!-- 删除弹窗 -->\r\n    <uni-popup ref=\"morePopup\" type=\"bottom\" :safe-area=\"false\">\r\n      <view class=\"popup-box\">\r\n        <view class=\"popup-item df\" @tap=\"cartDelClick\">\r\n          <text style=\"color:#FA5150\">删除</text>\r\n          <image src=\"/static/img/sc.png\"></image>\r\n        </view>\r\n        <view class=\"popup-item\" @tap=\"closePopupClick(2)\">\r\n          <text style=\"color:#999\">取消</text>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n    \r\n    <!-- 提示弹窗 -->\r\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\r\n      <view class=\"tips-box df\">\r\n        <view class=\"tips-item\">{{tipsTitle}}</view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request.js'\r\nimport * as api from '@/config/api.js'\r\nimport money from '@/components/money/money.vue'\r\n\r\nconst app = getApp();\r\n\r\nexport default {\r\n  components: {\r\n    money\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\r\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\r\n      appCard: app.globalData.isCard || false,\r\n      list: [],\r\n      listIdx: -1,\r\n      quantity: 0,\r\n      goodsAmount: \"0.00\",\r\n      discountAmount: \"0.00\",\r\n      orderAmount: \"0.00\",\r\n      cardAmount: 0,\r\n      allCheck: 0,\r\n      checkCount: 0,\r\n      sumCount: 0,\r\n      listProductIdx: 0,\r\n      productList: [{stock: 1}],\r\n      productIdx: 0,\r\n      goodsName: \"\",\r\n      isEmpty: false,\r\n      tipsTitle: \"\",\r\n      mockData: {\r\n        cartItems: [\r\n          {\r\n            id: 1,\r\n            goods_id: 101,\r\n            goods_name: \"轻奢真皮小白鞋\",\r\n            goods_product_id: 1001,\r\n            check: 1,\r\n            quantity: 2,\r\n            status: 1,\r\n            status_str: \"\",\r\n            is_quantity: false,\r\n            product: {\r\n              id: 1001,\r\n              name: \"白色 39码\",\r\n              img: \"/static/img/avatar.png\",\r\n              price: \"299.00\",\r\n              line_price: \"399.00\",\r\n              stock: 10\r\n            }\r\n          },\r\n          {\r\n            id: 2,\r\n            goods_id: 102,\r\n            goods_name: \"夏季薄款T恤\",\r\n            goods_product_id: 1002,\r\n            check: 1,\r\n            quantity: 1,\r\n            status: 1,\r\n            status_str: \"\",\r\n            is_quantity: false,\r\n            product: {\r\n              id: 1002,\r\n              name: \"蓝色 L码\",\r\n              img: \"/static/img/avatar.png\",\r\n              price: \"89.00\",\r\n              line_price: \"129.00\",\r\n              stock: 5\r\n            }\r\n          },\r\n          {\r\n            id: 3,\r\n            goods_id: 103,\r\n            goods_name: \"轻便双肩包\",\r\n            goods_product_id: 1003,\r\n            check: 0,\r\n            quantity: 1,\r\n            status: 1,\r\n            status_str: \"\",\r\n            is_quantity: false,\r\n            product: {\r\n              id: 1003,\r\n              name: \"黑色 标准款\",\r\n              img: \"/static/img/avatar.png\",\r\n              price: \"199.00\",\r\n              line_price: \"259.00\",\r\n              stock: 8\r\n            }\r\n          },\r\n          {\r\n            id: 4,\r\n            goods_id: 104,\r\n            goods_name: \"经典牛仔裤\",\r\n            goods_product_id: 1004,\r\n            check: 0,\r\n            quantity: 1,\r\n            status: 2,\r\n            status_str: \"已售罄\",\r\n            is_quantity: false,\r\n            product: {\r\n              id: 1004,\r\n              name: \"深蓝 30码\",\r\n              img: \"/static/img/avatar.png\",\r\n              price: \"159.00\",\r\n              line_price: \"199.00\",\r\n              stock: 0\r\n            }\r\n          }\r\n        ],\r\n        productVariants: {\r\n          101: [\r\n            {\r\n              id: 1001,\r\n              name: \"白色 39码\",\r\n              img: \"/static/img/avatar.png\",\r\n              price: \"299.00\",\r\n              stock: 10\r\n            },\r\n            {\r\n              id: 1005,\r\n              name: \"白色 40码\",\r\n              img: \"/static/img/avatar.png\", \r\n              price: \"299.00\",\r\n              stock: 8\r\n            },\r\n            {\r\n              id: 1006,\r\n              name: \"黑色 39码\",\r\n              img: \"/static/img/avatar.png\",\r\n              price: \"319.00\",\r\n              stock: 5\r\n            }\r\n          ]\r\n        },\r\n        cards: [\r\n          {\r\n            card: {\r\n              price: \"30.00\",\r\n              name: \"满300减30券\"\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.goodsCartList()\r\n  },\r\n  onShow() {\r\n    if (app.globalData.isCenterPage) {\r\n      this.goodsCartList()\r\n      app.globalData.isCenterPage = false\r\n    }\r\n  },\r\n  methods: {\r\n    goodsCartList() {\r\n      let that = this\r\n      that.isEmpty = false\r\n      \r\n      if (api.default && api.default.api && api.default.api.goodsCartListUrl) {\r\n        request(api.default.api.goodsCartListUrl).then(function(res) {\r\n          if (res.code == 200 && res.data.length) {\r\n            that.list = res.data\r\n            that.calculate()\r\n          } else {\r\n            that.isEmpty = true\r\n          }\r\n        })\r\n      } else {\r\n        setTimeout(() => {\r\n          if (that.mockData.cartItems.length) {\r\n            that.list = JSON.parse(JSON.stringify(that.mockData.cartItems))\r\n            that.calculate()\r\n          } else {\r\n            that.isEmpty = true\r\n          }\r\n        }, 500)\r\n      }\r\n    },\r\n    \r\n    openProductClick(e) {\r\n      let that = this\r\n      let index = this.getIndex(e.currentTarget.dataset.id)\r\n      that.listIdx = index\r\n      that.goodsName = that.list[index].goods_name\r\n      \r\n      uni.showLoading({\r\n        mask: true\r\n      })\r\n      \r\n      if (api.default && api.default.api && api.default.api.goodsProductUrl) {\r\n        request(api.default.api.goodsProductUrl, {\r\n          id: that.list[index].goods_id\r\n        }).then(function(res) {\r\n          uni.hideLoading()\r\n          that.productList = res.data\r\n          \r\n          for (let i in that.productList) {\r\n            if (that.productList[i].id == that.list[index].goods_product_id) {\r\n              that.listProductIdx = parseInt(i)\r\n              that.productIdx = parseInt(i)\r\n              break\r\n            }\r\n          }\r\n          \r\n          that.$refs.goodsPopup.open()\r\n        })\r\n      } else {\r\n        setTimeout(() => {\r\n          uni.hideLoading()\r\n          const goodsId = that.list[index].goods_id\r\n          \r\n          if (that.mockData.productVariants[goodsId]) {\r\n            that.productList = that.mockData.productVariants[goodsId]\r\n          } else {\r\n            that.productList = [that.list[index].product]\r\n          }\r\n          \r\n          for (let i in that.productList) {\r\n            if (that.productList[i].id == that.list[index].goods_product_id) {\r\n              that.listProductIdx = parseInt(i)\r\n              that.productIdx = parseInt(i)\r\n              break\r\n            }\r\n          }\r\n          \r\n          that.$refs.goodsPopup.open()\r\n        }, 300)\r\n      }\r\n    },\r\n    \r\n    cartProductClick() {\r\n      let that = this\r\n      \r\n      if (that.listProductIdx != that.productIdx) {\r\n        let product = that.productList[that.productIdx]\r\n        \r\n        if (product.stock <= 0) {\r\n          return that.opTipsPopup(\"该款式已售罄暂时无法购买！\")\r\n        }\r\n        \r\n        uni.showLoading({\r\n          mask: true\r\n        })\r\n        \r\n        if (api.default && api.default.api && api.default.api.goodsSaveCartUrl) {\r\n          request(api.default.api.goodsSaveCartUrl, {\r\n            type: 4,\r\n            id: that.list[that.listIdx].id,\r\n            goods_product_id: product.id,\r\n            quantity: that.list[that.listIdx].quantity\r\n          }, \"POST\").then(function(res) {\r\n            uni.hideLoading()\r\n            app.globalData.isCenterPage = true\r\n            that.goodsCartList()\r\n            that.$refs.goodsPopup.close()\r\n          })\r\n        } else {\r\n          setTimeout(() => {\r\n            uni.hideLoading()\r\n            \r\n            that.list[that.listIdx].product = product\r\n            that.list[that.listIdx].goods_product_id = product.id\r\n            \r\n            app.globalData.isCenterPage = true\r\n            that.calculate()\r\n            that.$refs.goodsPopup.close()\r\n          }, 300)\r\n        }\r\n      } else {\r\n        that.$refs.goodsPopup.close()\r\n      }\r\n    },\r\n    \r\n    cartCheck(e) {\r\n      let that = this\r\n      let index = this.getIndex(e.currentTarget.dataset.id)\r\n      let check = e.currentTarget.dataset.check\r\n      \r\n      if (index >= 0 && that.list[index].status == 2) {\r\n        return that.opTipsPopup(\"该款式已售罄暂时无法购买！\")\r\n      } else if (index >= 0 && that.list[index].status == 3) {\r\n        return that.opTipsPopup(\"该款式已下架无法购买！\")\r\n      }\r\n      \r\n      if (api.default && api.default.api && api.default.api.goodsSaveCartUrl) {\r\n        request(api.default.api.goodsSaveCartUrl, {\r\n          type: index >= 0 ? 1 : 2,\r\n          id: index >= 0 ? that.list[index].id : 0,\r\n          check: check\r\n        }, \"POST\").then(function(res) {\r\n          if (index >= 0) {\r\n            that.list[index].check = that.list[index].check == 1 ? 0 : 1\r\n          } else {\r\n            that.allCheck = that.allCheck == 1 ? 0 : 1\r\n            \r\n            for (let i in that.list) {\r\n              if (that.list[i].status == 1) {\r\n                that.list[i].check = that.allCheck\r\n              }\r\n            }\r\n          }\r\n          \r\n          that.calculate()\r\n        })\r\n      } else {\r\n        setTimeout(() => {\r\n          if (index >= 0) {\r\n            that.list[index].check = that.list[index].check == 1 ? 0 : 1\r\n          } else {\r\n            that.allCheck = that.allCheck == 1 ? 0 : 1\r\n            \r\n            for (let i in that.list) {\r\n              if (that.list[i].status == 1) {\r\n                that.list[i].check = that.allCheck\r\n              }\r\n            }\r\n          }\r\n          \r\n          that.calculate()\r\n        }, 200)\r\n      }\r\n    },\r\n    \r\n    quantityBtn(e) {\r\n      let that = this\r\n      let index = this.getIndex(e.currentTarget.dataset.id)\r\n      let type = e.currentTarget.dataset.type\r\n      let isNeedUpdate = false\r\n      \r\n      if (that.list[index].status == 2) {\r\n        return that.opTipsPopup(\"该款式已售罄暂时无法编辑数量！\")\r\n      } else if (that.list[index].status == 3) {\r\n        return that.opTipsPopup(\"该款式已下架无法编辑数量！\")\r\n      } else if (type == 0 && parseInt(that.quantity) <= 1) {\r\n        return that.opTipsPopup(\"就一件了，数量不能再少啦！\")\r\n      }\r\n      \r\n      if (parseInt(that.quantity) > parseInt(that.list[index].product.stock)) {\r\n        isNeedUpdate = true\r\n        that.quantity = parseInt(that.list[index].product.stock)\r\n        that.opTipsPopup(\"购买数量已达到最大库存上限！\")\r\n      } else if (that.list[index].status == 1 && that.quantity && that.quantity != 0) {\r\n        isNeedUpdate = true\r\n        \r\n        if (type == 0) {\r\n          that.quantity = parseInt(that.quantity) - 1\r\n        }\r\n        \r\n        if (type == 1 && parseInt(that.quantity) < parseInt(that.list[index].product.stock)) {\r\n          that.quantity = parseInt(that.quantity) + 1\r\n        }\r\n      }\r\n      \r\n      if (isNeedUpdate) {\r\n        if (api.default && api.default.api && api.default.api.goodsSaveCartUrl) {\r\n          request(api.default.api.goodsSaveCartUrl, {\r\n            type: 3,\r\n            id: that.list[index].id,\r\n            quantity: parseInt(that.quantity)\r\n          }, \"POST\").then(function(res) {\r\n            that.list[index].quantity = parseInt(that.quantity)\r\n            that.calculate()\r\n          })\r\n        } else {\r\n          setTimeout(() => {\r\n            that.list[index].quantity = parseInt(that.quantity)\r\n            that.calculate()\r\n          }, 200)\r\n        }\r\n      }\r\n    },\r\n    \r\n    cartDelClick() {\r\n      let that = this\r\n      \r\n      uni.showLoading({\r\n        mask: true\r\n      })\r\n      \r\n      if (api.default && api.default.api && api.default.api.goodsCartDelUrl) {\r\n        request(api.default.api.goodsCartDelUrl, {\r\n          id: that.list[that.listIdx].id\r\n        }, \"POST\").then(function(res) {\r\n          uni.hideLoading()\r\n          app.globalData.isCenterPage = true\r\n          \r\n          that.list.splice(that.listIdx, 1)\r\n          \r\n          if (that.list.length > 0) {\r\n            that.calculate()\r\n          } else {\r\n            that.isEmpty = true\r\n          }\r\n          \r\n          that.$refs.morePopup.close()\r\n        })\r\n      } else {\r\n        setTimeout(() => {\r\n          uni.hideLoading()\r\n          app.globalData.isCenterPage = true\r\n          \r\n          that.list.splice(that.listIdx, 1)\r\n          \r\n          if (that.list.length > 0) {\r\n            that.calculate()\r\n          } else {\r\n            that.isEmpty = true\r\n          }\r\n          \r\n          that.$refs.morePopup.close()\r\n        }, 300)\r\n      }\r\n    },\r\n    \r\n    calculate() {\r\n      let that = this\r\n      let goodsAmount = 0\r\n      let discountAmount = 0\r\n      let orderAmount = 0\r\n      let checkCount = 0\r\n      let sumCount = 0\r\n      let invalidCount = 0\r\n      \r\n      for (let item of that.list) {\r\n        if (item.check && item.status == 1) {\r\n          checkCount += 1\r\n          sumCount += parseInt(item.quantity)\r\n          goodsAmount += parseFloat(item.product.line_price * item.quantity)\r\n          discountAmount += parseFloat((item.product.line_price - item.product.price) * item.quantity)\r\n          orderAmount += parseFloat(item.product.price * item.quantity)\r\n        } else if (item.status == 2 || item.status == 3) {\r\n          invalidCount += 1\r\n        }\r\n      }\r\n      \r\n      that.goodsAmount = goodsAmount.toFixed(2)\r\n      that.discountAmount = discountAmount.toFixed(2)\r\n      that.orderAmount = orderAmount.toFixed(2)\r\n      that.checkCount = checkCount\r\n      that.sumCount = sumCount\r\n      \r\n      if (that.list.length == checkCount + invalidCount && checkCount != 0) {\r\n        that.allCheck = 1\r\n      } else {\r\n        that.allCheck = 0\r\n      }\r\n      \r\n      that.useCard()\r\n    },\r\n    \r\n    useCard() {\r\n      let that = this\r\n      \r\n      if (api.default && api.default.api && api.default.api.useCardUrl) {\r\n        request(api.default.api.useCardUrl, {\r\n          product_id: 0,\r\n          amount: that.orderAmount\r\n        }, \"POST\").then(function(res) {\r\n          if (res.code == 200 && res.data[0]) {\r\n            that.cardAmount = res.data[0].card.price\r\n            let newOrderAmount = that.orderAmount - res.data[0].card.price\r\n            that.orderAmount = newOrderAmount.toFixed(2)\r\n          }\r\n        })\r\n      } else {\r\n        setTimeout(() => {\r\n          if (parseFloat(that.orderAmount) >= 300) {\r\n            that.cardAmount = that.mockData.cards[0].card.price\r\n            let newOrderAmount = parseFloat(that.orderAmount) - parseFloat(that.cardAmount)\r\n            that.orderAmount = newOrderAmount.toFixed(2)\r\n          } else {\r\n            that.cardAmount = 0\r\n          }\r\n        }, 200)\r\n      }\r\n    },\r\n    \r\n    openQuantity(e) {\r\n      let index = this.getIndex(e.currentTarget.dataset.id)\r\n      \r\n      for (let i in this.list) {\r\n        if (index != i) {\r\n          this.list[i].is_quantity = false\r\n        }\r\n      }\r\n      \r\n      this.quantity = this.list[index].quantity\r\n      this.list[index].is_quantity = true\r\n    },\r\n    \r\n    paramClick(e) {\r\n      this.productIdx = e.currentTarget.dataset.idx\r\n    },\r\n    \r\n    openMore(e) {\r\n      this.listIdx = this.getIndex(e.currentTarget.dataset.id)\r\n      this.$refs.morePopup.open()\r\n    },\r\n    \r\n    closePopupClick(type) {\r\n      if (type == 1) {\r\n        this.$refs.goodsPopup.close()\r\n      } else if (type == 2) {\r\n        this.$refs.morePopup.close()\r\n      }\r\n    },\r\n    \r\n    getIndex(id) {\r\n      for (let i in this.list) {\r\n        if (this.list[i].id == id) {\r\n          return i\r\n        }\r\n      }\r\n      return -1\r\n    },\r\n    \r\n    imgParamTap(e) {\r\n      let url = e.currentTarget.dataset.url\r\n      \r\n      uni.previewImage({\r\n        current: url,\r\n        urls: [url]\r\n      })\r\n    },\r\n    \r\n    navigateToFun(e) {\r\n      let url = e.currentTarget.dataset.url\r\n      let pay = e.currentTarget.dataset.pay\r\n      \r\n      if (pay && this.checkCount <= 0) {\r\n        return this.opTipsPopup(\"没有可结算的商品！\")\r\n      }\r\n      \r\n      if (pay && this.orderAmount <= 0) {\r\n        return this.opTipsPopup(\"结算金额不能小于0.01！\")\r\n      }\r\n      \r\n      uni.navigateTo({\r\n        url: \"/pages/\" + url\r\n      })\r\n    },\r\n    \r\n    navBack() {\r\n      if (getCurrentPages().length > 1) {\r\n        uni.navigateBack()\r\n      } else {\r\n        uni.switchTab({\r\n          url: \"/pages/tabbar/shop\"\r\n        })\r\n      }\r\n    },\r\n    \r\n    opTipsPopup(msg) {\r\n      let that = this\r\n      that.tipsTitle = msg\r\n      that.$refs.tipsPopup.open()\r\n      \r\n      setTimeout(function() {\r\n        that.$refs.tipsPopup.close()\r\n      }, 2000)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container{padding-bottom:240rpx;}\r\n.btn{height:100rpx;line-height:100rpx;text-align:center;font-size:24rpx;font-weight:bolder;border-radius:50rpx;}\r\n.nav-box{position:fixed;top:0;left:0;width:100%;z-index:99;box-sizing:border-box;}\r\n.nav-box .nav-back{padding:0 30rpx;width:34rpx;height:100%;}\r\n.nav-box .nav-title{font-size:32rpx;font-weight:700;}\r\n.content-box,.list-box{width:100%;}\r\n.list-box .list-item{border-top:1px solid #f8f8f8;width:100%;height:240rpx;position:relative;}\r\n.list-item .choose{width:100rpx;height:100%;justify-content:center;}\r\n.list-item .choose image,.footer-box .footer-all image{width:38rpx;height:38rpx;}\r\n.list-item .item-img{width:180rpx;height:180rpx;justify-content:center;background:#f8f8f8;border-radius:8rpx;position:relative;overflow:hidden;}\r\n.list-item .item-img image{position:absolute;width:100%;height:100%;}\r\n.list-item .item-img .sold-out{position:absolute;z-index:1;padding:0 30rpx;height:40rpx;color:#fff;font-size:20rpx;font-weight:700;border-radius:40rpx;background:rgba(0,0,0,.4);}\r\n.list-item .info{width:calc(100% - 310rpx);height:180rpx;margin-left:30rpx;display:flex;flex-direction:column;justify-content:space-between;}\r\n.list-item .info .info-sp{display:flex;justify-content:space-between;}\r\n.info .info-sp .txt{font-size:24rpx;color:#000;font-weight:700;}\r\n.info .info-sp .txt-box{margin-top:10rpx;padding:0 50rpx 0 20rpx;color:#999;font-size:20rpx;font-weight:700;height:40rpx;line-height:40rpx;border-radius:20rpx;background:#f8f8f8;position:relative;}\r\n.info .info-sp .txt-box .dw{position:absolute;top:0;right:0;width:40rpx;height:40rpx;}\r\n.info .info-sp .more{padding:0 30rpx;z-index:2;}\r\n.info .info-sp .quantity-box{margin-right:30rpx;height:60rpx;line-height:60rpx;border-radius:30rpx;border:1px solid #f8f8f8;font-size:22rpx;font-weight:700;text-align:center;}\r\n.info .info-sp .quantity-box input{width:40rpx;height:60rpx;line-height:60rpx;color:#000;}\r\n.info .info-sp .quantity-box .quantity-btn{width:60rpx;height:60rpx;line-height:60rpx;}\r\n.list-count{width:calc(100% - 60rpx);padding:15rpx 30rpx;justify-content:space-between;font-size:24rpx;}\r\n.list-count .t1{color:#999;}\r\n.list-count .t2{color:#000;}\r\n.list-count .t3{color:#999;font-weight:700;margin-right:10rpx;}\r\n.footer-box{position:fixed;z-index:99;bottom:0;width:calc(100% - 60rpx);padding:30rpx;justify-content:space-between;border-top:1px solid #f8f8f8;background:rgba(255,255,255,.95);padding-bottom:max(env(safe-area-inset-bottom),30rpx);}\r\n.footer-box .footer-all{flex-direction:column;justify-content:center;}\r\n.footer-box .footer-all text{margin-top:10rpx;color:#000;font-size:20rpx;}\r\n.footer-box .not{min-width:150rpx;padding:0 50rpx;color:#999;background:#f8f8f8;}\r\n.footer-box .not text{margin-right:10rpx;}\r\n.act{color:#fff!important;background:#000!important;}\r\n.popup-box{width:100%;background:#fff;border-radius:30rpx 30rpx 0 0;overflow:hidden;}\r\n.popup-box .popup-item{width:calc(100% - 60rpx);padding:30rpx;color:#000;font-size:26rpx;font-weight:700;justify-content:space-between;}\r\n.popup-box .popup-item image{width:36rpx;height:36rpx;}\r\n.popup-box .popup-item:first-child{padding-top:40rpx;}\r\n.popup-box .popup-item:last-child{padding-bottom:80rpx;}\r\n.popup-box .popup-item:hover{background:#f8f8f8;}\r\n.goods-box{width:100%;padding:30rpx 0;background:#fff;border-radius:30rpx 30rpx 0 0;padding-bottom:max(env(safe-area-inset-bottom),30rpx);}\r\n.goods-box .gtn{margin:0 30rpx;width:calc(100% - 60rpx);color:#999;background:#f8f8f8;}\r\n.goods-box .goods-top{width:calc(100% - 60rpx);height:48rpx;padding:0 30rpx;justify-content:space-between;}\r\n.goods-top .popup-name{width:calc(100% - 68rpx);color:#000;font-size:32rpx;font-weight:700;}\r\n.goods-top .popup-close{width:48rpx;height:48rpx;border-radius:50%;background:#f8f8f8;justify-content:center;transform:rotate(45deg);}\r\n.goods-box .specs-title{padding:30rpx;color:#999;font-size:24rpx;font-weight:700;}\r\n.goods-box .specs-overflow{overflow-x:auto;width:100%;padding-bottom:60rpx;}\r\n.specs-box .specs-item{flex-shrink:0;margin-left:20rpx;background:#fff;width:200rpx;border-radius:8rpx;border-width:1px;border-style:solid;position:relative;overflow:hidden;}\r\n.specs-item .fd{position:absolute;z-index:1;top:10rpx;right:10rpx;width:48rpx;height:48rpx;justify-content:center;background:rgba(0,0,0,.3);border-radius:50%;}\r\n.specs-item .img{width:200rpx;height:200rpx;display:block;}\r\n.specs-item .name{width:calc(100% - 40rpx);margin:20rpx;line-height:30rpx;text-align:center;font-size:20rpx;font-weight:500;}\r\n.empty-box{flex-direction:column;align-items:center;justify-content:center;padding:100rpx 0;}\r\n.empty-box image{width:200rpx;height:200rpx;margin-bottom:30rpx;}\r\n.empty-box .e1{font-size:28rpx;font-weight:bold;margin-bottom:10rpx;}\r\n.empty-box .e2{font-size:24rpx;color:#999;}\r\n.tips-box{padding:20rpx 30rpx;border-radius:12rpx;justify-content:center;}\r\n.tips-box .tips-item{color:#fff;font-size:28rpx;font-weight:700;}\r\n.df{display:flex;align-items:center;}\r\n.bUp{box-shadow:0 -2px 5px 0 rgba(0,0,0,0.05);}\r\n.bfw{backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);background:rgba(255,255,255,.8);}\r\n.ohto{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}\r\n</style> ", "import MiniProgramPage from 'D:/uniapp/vue3/pages/goods/cart.vue'\nwx.createPage(MiniProgramPage)"], "names": ["api.default", "request", "uni"], "mappings": ";;;;;AAoMA,MAAO,QAAO,MAAW;AAEzB,MAAM,MAAM,OAAM;AAElB,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB,KAAK,OAAO,MAAM,mBAAmB;AAAA,MACtD,gBAAgB,KAAK,OAAO,MAAM,kBAAkB;AAAA,MACpD,SAAS,IAAI,WAAW,UAAU;AAAA,MAClC,MAAM,CAAE;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,aAAa,CAAC,EAAC,OAAO,EAAC,CAAC;AAAA,MACxB,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,QACR,WAAW;AAAA,UACT;AAAA,YACE,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,kBAAkB;AAAA,YAClB,OAAO;AAAA,YACP,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAAS;AAAA,cACP,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,KAAK;AAAA,cACL,OAAO;AAAA,cACP,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,UACD;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,kBAAkB;AAAA,YAClB,OAAO;AAAA,YACP,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAAS;AAAA,cACP,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,KAAK;AAAA,cACL,OAAO;AAAA,cACP,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,UACD;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,kBAAkB;AAAA,YAClB,OAAO;AAAA,YACP,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAAS;AAAA,cACP,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,KAAK;AAAA,cACL,OAAO;AAAA,cACP,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,UACD;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,kBAAkB;AAAA,YAClB,OAAO;AAAA,YACP,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAAS;AAAA,cACP,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,KAAK;AAAA,cACL,OAAO;AAAA,cACP,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACD;AAAA,QACD,iBAAiB;AAAA,UACf,KAAK;AAAA,YACH;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,KAAK;AAAA,cACL,OAAO;AAAA,cACP,OAAO;AAAA,YACR;AAAA,YACD;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,KAAK;AAAA,cACL,OAAO;AAAA,cACP,OAAO;AAAA,YACR;AAAA,YACD;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,KAAK;AAAA,cACL,OAAO;AAAA,cACP,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACD;AAAA,QACD,OAAO;AAAA,UACL;AAAA,YACE,MAAM;AAAA,cACJ,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,cAAc;AAAA,EACpB;AAAA,EACD,SAAS;AACP,QAAI,IAAI,WAAW,cAAc;AAC/B,WAAK,cAAc;AACnB,UAAI,WAAW,eAAe;AAAA,IAChC;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,gBAAgB;AACd,UAAI,OAAO;AACX,WAAK,UAAU;AAEf,UAAIA,WAAAA,SAAeA,WAAAA,MAAY,OAAOA,WAAAA,MAAY,IAAI,kBAAkB;AACtEC,sBAAO,QAACD,WAAW,MAAC,IAAI,gBAAgB,EAAE,KAAK,SAAS,KAAK;AAC3D,cAAI,IAAI,QAAQ,OAAO,IAAI,KAAK,QAAQ;AACtC,iBAAK,OAAO,IAAI;AAChB,iBAAK,UAAU;AAAA,iBACV;AACL,iBAAK,UAAU;AAAA,UACjB;AAAA,SACD;AAAA,aACI;AACL,mBAAW,MAAM;AACf,cAAI,KAAK,SAAS,UAAU,QAAQ;AAClC,iBAAK,OAAO,KAAK,MAAM,KAAK,UAAU,KAAK,SAAS,SAAS,CAAC;AAC9D,iBAAK,UAAU;AAAA,iBACV;AACL,iBAAK,UAAU;AAAA,UACjB;AAAA,QACD,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA,IAED,iBAAiB,GAAG;AAClB,UAAI,OAAO;AACX,UAAI,QAAQ,KAAK,SAAS,EAAE,cAAc,QAAQ,EAAE;AACpD,WAAK,UAAU;AACf,WAAK,YAAY,KAAK,KAAK,KAAK,EAAE;AAElCE,oBAAAA,MAAI,YAAY;AAAA,QACd,MAAM;AAAA,OACP;AAED,UAAIF,WAAAA,SAAeA,WAAAA,MAAY,OAAOA,WAAAA,MAAY,IAAI,iBAAiB;AACrEC,8BAAQD,WAAAA,MAAY,IAAI,iBAAiB;AAAA,UACvC,IAAI,KAAK,KAAK,KAAK,EAAE;AAAA,QACvB,CAAC,EAAE,KAAK,SAAS,KAAK;AACpBE,wBAAAA,MAAI,YAAY;AAChB,eAAK,cAAc,IAAI;AAEvB,mBAAS,KAAK,KAAK,aAAa;AAC9B,gBAAI,KAAK,YAAY,CAAC,EAAE,MAAM,KAAK,KAAK,KAAK,EAAE,kBAAkB;AAC/D,mBAAK,iBAAiB,SAAS,CAAC;AAChC,mBAAK,aAAa,SAAS,CAAC;AAC5B;AAAA,YACF;AAAA,UACF;AAEA,eAAK,MAAM,WAAW,KAAK;AAAA,SAC5B;AAAA,aACI;AACL,mBAAW,MAAM;AACfA,wBAAAA,MAAI,YAAY;AAChB,gBAAM,UAAU,KAAK,KAAK,KAAK,EAAE;AAEjC,cAAI,KAAK,SAAS,gBAAgB,OAAO,GAAG;AAC1C,iBAAK,cAAc,KAAK,SAAS,gBAAgB,OAAO;AAAA,iBACnD;AACL,iBAAK,cAAc,CAAC,KAAK,KAAK,KAAK,EAAE,OAAO;AAAA,UAC9C;AAEA,mBAAS,KAAK,KAAK,aAAa;AAC9B,gBAAI,KAAK,YAAY,CAAC,EAAE,MAAM,KAAK,KAAK,KAAK,EAAE,kBAAkB;AAC/D,mBAAK,iBAAiB,SAAS,CAAC;AAChC,mBAAK,aAAa,SAAS,CAAC;AAC5B;AAAA,YACF;AAAA,UACF;AAEA,eAAK,MAAM,WAAW,KAAK;AAAA,QAC5B,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA,IAED,mBAAmB;AACjB,UAAI,OAAO;AAEX,UAAI,KAAK,kBAAkB,KAAK,YAAY;AAC1C,YAAI,UAAU,KAAK,YAAY,KAAK,UAAU;AAE9C,YAAI,QAAQ,SAAS,GAAG;AACtB,iBAAO,KAAK,YAAY,eAAe;AAAA,QACzC;AAEAA,sBAAAA,MAAI,YAAY;AAAA,UACd,MAAM;AAAA,SACP;AAED,YAAIF,WAAAA,SAAeA,WAAAA,MAAY,OAAOA,WAAAA,MAAY,IAAI,kBAAkB;AACtEC,gCAAQD,WAAAA,MAAY,IAAI,kBAAkB;AAAA,YACxC,MAAM;AAAA,YACN,IAAI,KAAK,KAAK,KAAK,OAAO,EAAE;AAAA,YAC5B,kBAAkB,QAAQ;AAAA,YAC1B,UAAU,KAAK,KAAK,KAAK,OAAO,EAAE;AAAA,UACnC,GAAE,MAAM,EAAE,KAAK,SAAS,KAAK;AAC5BE,0BAAAA,MAAI,YAAY;AAChB,gBAAI,WAAW,eAAe;AAC9B,iBAAK,cAAc;AACnB,iBAAK,MAAM,WAAW,MAAM;AAAA,WAC7B;AAAA,eACI;AACL,qBAAW,MAAM;AACfA,0BAAAA,MAAI,YAAY;AAEhB,iBAAK,KAAK,KAAK,OAAO,EAAE,UAAU;AAClC,iBAAK,KAAK,KAAK,OAAO,EAAE,mBAAmB,QAAQ;AAEnD,gBAAI,WAAW,eAAe;AAC9B,iBAAK,UAAU;AACf,iBAAK,MAAM,WAAW,MAAM;AAAA,UAC7B,GAAE,GAAG;AAAA,QACR;AAAA,aACK;AACL,aAAK,MAAM,WAAW,MAAM;AAAA,MAC9B;AAAA,IACD;AAAA,IAED,UAAU,GAAG;AACX,UAAI,OAAO;AACX,UAAI,QAAQ,KAAK,SAAS,EAAE,cAAc,QAAQ,EAAE;AACpD,UAAI,QAAQ,EAAE,cAAc,QAAQ;AAEpC,UAAI,SAAS,KAAK,KAAK,KAAK,KAAK,EAAE,UAAU,GAAG;AAC9C,eAAO,KAAK,YAAY,eAAe;AAAA,MACzC,WAAW,SAAS,KAAK,KAAK,KAAK,KAAK,EAAE,UAAU,GAAG;AACrD,eAAO,KAAK,YAAY,aAAa;AAAA,MACvC;AAEA,UAAIF,WAAAA,SAAeA,WAAAA,MAAY,OAAOA,WAAAA,MAAY,IAAI,kBAAkB;AACtEC,8BAAQD,WAAAA,MAAY,IAAI,kBAAkB;AAAA,UACxC,MAAM,SAAS,IAAI,IAAI;AAAA,UACvB,IAAI,SAAS,IAAI,KAAK,KAAK,KAAK,EAAE,KAAK;AAAA,UACvC;AAAA,QACD,GAAE,MAAM,EAAE,KAAK,SAAS,KAAK;AAC5B,cAAI,SAAS,GAAG;AACd,iBAAK,KAAK,KAAK,EAAE,QAAQ,KAAK,KAAK,KAAK,EAAE,SAAS,IAAI,IAAI;AAAA,iBACtD;AACL,iBAAK,WAAW,KAAK,YAAY,IAAI,IAAI;AAEzC,qBAAS,KAAK,KAAK,MAAM;AACvB,kBAAI,KAAK,KAAK,CAAC,EAAE,UAAU,GAAG;AAC5B,qBAAK,KAAK,CAAC,EAAE,QAAQ,KAAK;AAAA,cAC5B;AAAA,YACF;AAAA,UACF;AAEA,eAAK,UAAU;AAAA,SAChB;AAAA,aACI;AACL,mBAAW,MAAM;AACf,cAAI,SAAS,GAAG;AACd,iBAAK,KAAK,KAAK,EAAE,QAAQ,KAAK,KAAK,KAAK,EAAE,SAAS,IAAI,IAAI;AAAA,iBACtD;AACL,iBAAK,WAAW,KAAK,YAAY,IAAI,IAAI;AAEzC,qBAAS,KAAK,KAAK,MAAM;AACvB,kBAAI,KAAK,KAAK,CAAC,EAAE,UAAU,GAAG;AAC5B,qBAAK,KAAK,CAAC,EAAE,QAAQ,KAAK;AAAA,cAC5B;AAAA,YACF;AAAA,UACF;AAEA,eAAK,UAAU;AAAA,QAChB,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA,IAED,YAAY,GAAG;AACb,UAAI,OAAO;AACX,UAAI,QAAQ,KAAK,SAAS,EAAE,cAAc,QAAQ,EAAE;AACpD,UAAI,OAAO,EAAE,cAAc,QAAQ;AACnC,UAAI,eAAe;AAEnB,UAAI,KAAK,KAAK,KAAK,EAAE,UAAU,GAAG;AAChC,eAAO,KAAK,YAAY,iBAAiB;AAAA,MAC3C,WAAW,KAAK,KAAK,KAAK,EAAE,UAAU,GAAG;AACvC,eAAO,KAAK,YAAY,eAAe;AAAA,MACzC,WAAW,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK,GAAG;AACpD,eAAO,KAAK,YAAY,eAAe;AAAA,MACzC;AAEA,UAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,KAAK,KAAK,EAAE,QAAQ,KAAK,GAAG;AACtE,uBAAe;AACf,aAAK,WAAW,SAAS,KAAK,KAAK,KAAK,EAAE,QAAQ,KAAK;AACvD,aAAK,YAAY,gBAAgB;AAAA,MACnC,WAAW,KAAK,KAAK,KAAK,EAAE,UAAU,KAAK,KAAK,YAAY,KAAK,YAAY,GAAG;AAC9E,uBAAe;AAEf,YAAI,QAAQ,GAAG;AACb,eAAK,WAAW,SAAS,KAAK,QAAQ,IAAI;AAAA,QAC5C;AAEA,YAAI,QAAQ,KAAK,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,KAAK,KAAK,EAAE,QAAQ,KAAK,GAAG;AACnF,eAAK,WAAW,SAAS,KAAK,QAAQ,IAAI;AAAA,QAC5C;AAAA,MACF;AAEA,UAAI,cAAc;AAChB,YAAIA,WAAAA,SAAeA,WAAAA,MAAY,OAAOA,WAAAA,MAAY,IAAI,kBAAkB;AACtEC,gCAAQD,WAAAA,MAAY,IAAI,kBAAkB;AAAA,YACxC,MAAM;AAAA,YACN,IAAI,KAAK,KAAK,KAAK,EAAE;AAAA,YACrB,UAAU,SAAS,KAAK,QAAQ;AAAA,UACjC,GAAE,MAAM,EAAE,KAAK,SAAS,KAAK;AAC5B,iBAAK,KAAK,KAAK,EAAE,WAAW,SAAS,KAAK,QAAQ;AAClD,iBAAK,UAAU;AAAA,WAChB;AAAA,eACI;AACL,qBAAW,MAAM;AACf,iBAAK,KAAK,KAAK,EAAE,WAAW,SAAS,KAAK,QAAQ;AAClD,iBAAK,UAAU;AAAA,UAChB,GAAE,GAAG;AAAA,QACR;AAAA,MACF;AAAA,IACD;AAAA,IAED,eAAe;AACb,UAAI,OAAO;AAEXE,oBAAAA,MAAI,YAAY;AAAA,QACd,MAAM;AAAA,OACP;AAED,UAAIF,WAAAA,SAAeA,WAAAA,MAAY,OAAOA,WAAAA,MAAY,IAAI,iBAAiB;AACrEC,8BAAQD,WAAAA,MAAY,IAAI,iBAAiB;AAAA,UACvC,IAAI,KAAK,KAAK,KAAK,OAAO,EAAE;AAAA,QAC7B,GAAE,MAAM,EAAE,KAAK,SAAS,KAAK;AAC5BE,wBAAAA,MAAI,YAAY;AAChB,cAAI,WAAW,eAAe;AAE9B,eAAK,KAAK,OAAO,KAAK,SAAS,CAAC;AAEhC,cAAI,KAAK,KAAK,SAAS,GAAG;AACxB,iBAAK,UAAU;AAAA,iBACV;AACL,iBAAK,UAAU;AAAA,UACjB;AAEA,eAAK,MAAM,UAAU,MAAM;AAAA,SAC5B;AAAA,aACI;AACL,mBAAW,MAAM;AACfA,wBAAAA,MAAI,YAAY;AAChB,cAAI,WAAW,eAAe;AAE9B,eAAK,KAAK,OAAO,KAAK,SAAS,CAAC;AAEhC,cAAI,KAAK,KAAK,SAAS,GAAG;AACxB,iBAAK,UAAU;AAAA,iBACV;AACL,iBAAK,UAAU;AAAA,UACjB;AAEA,eAAK,MAAM,UAAU,MAAM;AAAA,QAC5B,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA,IAED,YAAY;AACV,UAAI,OAAO;AACX,UAAI,cAAc;AAClB,UAAI,iBAAiB;AACrB,UAAI,cAAc;AAClB,UAAI,aAAa;AACjB,UAAI,WAAW;AACf,UAAI,eAAe;AAEnB,eAAS,QAAQ,KAAK,MAAM;AAC1B,YAAI,KAAK,SAAS,KAAK,UAAU,GAAG;AAClC,wBAAc;AACd,sBAAY,SAAS,KAAK,QAAQ;AAClC,yBAAe,WAAW,KAAK,QAAQ,aAAa,KAAK,QAAQ;AACjE,4BAAkB,YAAY,KAAK,QAAQ,aAAa,KAAK,QAAQ,SAAS,KAAK,QAAQ;AAC3F,yBAAe,WAAW,KAAK,QAAQ,QAAQ,KAAK,QAAQ;AAAA,QAC9D,WAAW,KAAK,UAAU,KAAK,KAAK,UAAU,GAAG;AAC/C,0BAAgB;AAAA,QAClB;AAAA,MACF;AAEA,WAAK,cAAc,YAAY,QAAQ,CAAC;AACxC,WAAK,iBAAiB,eAAe,QAAQ,CAAC;AAC9C,WAAK,cAAc,YAAY,QAAQ,CAAC;AACxC,WAAK,aAAa;AAClB,WAAK,WAAW;AAEhB,UAAI,KAAK,KAAK,UAAU,aAAa,gBAAgB,cAAc,GAAG;AACpE,aAAK,WAAW;AAAA,aACX;AACL,aAAK,WAAW;AAAA,MAClB;AAEA,WAAK,QAAQ;AAAA,IACd;AAAA,IAED,UAAU;AACR,UAAI,OAAO;AAEX,UAAIF,WAAAA,SAAeA,WAAAA,MAAY,OAAOA,WAAAA,MAAY,IAAI,YAAY;AAChEC,8BAAQD,WAAAA,MAAY,IAAI,YAAY;AAAA,UAClC,YAAY;AAAA,UACZ,QAAQ,KAAK;AAAA,QACd,GAAE,MAAM,EAAE,KAAK,SAAS,KAAK;AAC5B,cAAI,IAAI,QAAQ,OAAO,IAAI,KAAK,CAAC,GAAG;AAClC,iBAAK,aAAa,IAAI,KAAK,CAAC,EAAE,KAAK;AACnC,gBAAI,iBAAiB,KAAK,cAAc,IAAI,KAAK,CAAC,EAAE,KAAK;AACzD,iBAAK,cAAc,eAAe,QAAQ,CAAC;AAAA,UAC7C;AAAA,SACD;AAAA,aACI;AACL,mBAAW,MAAM;AACf,cAAI,WAAW,KAAK,WAAW,KAAK,KAAK;AACvC,iBAAK,aAAa,KAAK,SAAS,MAAM,CAAC,EAAE,KAAK;AAC9C,gBAAI,iBAAiB,WAAW,KAAK,WAAW,IAAI,WAAW,KAAK,UAAU;AAC9E,iBAAK,cAAc,eAAe,QAAQ,CAAC;AAAA,iBACtC;AACL,iBAAK,aAAa;AAAA,UACpB;AAAA,QACD,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA,IAED,aAAa,GAAG;AACd,UAAI,QAAQ,KAAK,SAAS,EAAE,cAAc,QAAQ,EAAE;AAEpD,eAAS,KAAK,KAAK,MAAM;AACvB,YAAI,SAAS,GAAG;AACd,eAAK,KAAK,CAAC,EAAE,cAAc;AAAA,QAC7B;AAAA,MACF;AAEA,WAAK,WAAW,KAAK,KAAK,KAAK,EAAE;AACjC,WAAK,KAAK,KAAK,EAAE,cAAc;AAAA,IAChC;AAAA,IAED,WAAW,GAAG;AACZ,WAAK,aAAa,EAAE,cAAc,QAAQ;AAAA,IAC3C;AAAA,IAED,SAAS,GAAG;AACV,WAAK,UAAU,KAAK,SAAS,EAAE,cAAc,QAAQ,EAAE;AACvD,WAAK,MAAM,UAAU,KAAK;AAAA,IAC3B;AAAA,IAED,gBAAgB,MAAM;AACpB,UAAI,QAAQ,GAAG;AACb,aAAK,MAAM,WAAW,MAAM;AAAA,MAC9B,WAAW,QAAQ,GAAG;AACpB,aAAK,MAAM,UAAU,MAAM;AAAA,MAC7B;AAAA,IACD;AAAA,IAED,SAAS,IAAI;AACX,eAAS,KAAK,KAAK,MAAM;AACvB,YAAI,KAAK,KAAK,CAAC,EAAE,MAAM,IAAI;AACzB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACR;AAAA,IAED,YAAY,GAAG;AACb,UAAI,MAAM,EAAE,cAAc,QAAQ;AAElCE,oBAAAA,MAAI,aAAa;AAAA,QACf,SAAS;AAAA,QACT,MAAM,CAAC,GAAG;AAAA,OACX;AAAA,IACF;AAAA,IAED,cAAc,GAAG;AACf,UAAI,MAAM,EAAE,cAAc,QAAQ;AAClC,UAAI,MAAM,EAAE,cAAc,QAAQ;AAElC,UAAI,OAAO,KAAK,cAAc,GAAG;AAC/B,eAAO,KAAK,YAAY,WAAW;AAAA,MACrC;AAEA,UAAI,OAAO,KAAK,eAAe,GAAG;AAChC,eAAO,KAAK,YAAY,eAAe;AAAA,MACzC;AAEAA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,YAAY;AAAA,OAClB;AAAA,IACF;AAAA,IAED,UAAU;AACR,UAAI,gBAAe,EAAG,SAAS,GAAG;AAChCA,sBAAAA,MAAI,aAAa;AAAA,aACZ;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,SACN;AAAA,MACH;AAAA,IACD;AAAA,IAED,YAAY,KAAK;AACf,UAAI,OAAO;AACX,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU,KAAK;AAE1B,iBAAW,WAAW;AACpB,aAAK,MAAM,UAAU,MAAM;AAAA,MAC5B,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrvBA,GAAG,WAAW,eAAe;"}