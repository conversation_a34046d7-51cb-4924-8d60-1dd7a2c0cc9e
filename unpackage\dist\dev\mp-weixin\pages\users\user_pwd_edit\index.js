"use strict";
const mixins_SendVerifyCode = require("../../../mixins/SendVerifyCode.js");
const api_api = require("../../../api/api.js");
const api_user = require("../../../api/user.js");
const libs_login = require("../../../libs/login.js");
const common_vendor = require("../../../common/vendor.js");
const mixins_color = require("../../../mixins/color.js");
const common_assets = require("../../../common/assets.js");
const authorize = () => "../../../components/Authorize.js";
const Verify = () => "../components/verify/index.js";
const _sfc_main = {
  mixins: [mixins_SendVerifyCode.sendVerifyCode, mixins_color.colors],
  components: {
    authorize,
    Verify
  },
  data() {
    return {
      userInfo: {},
      phone: "",
      password: "",
      captcha: "",
      qr_password: "",
      isAuto: false,
      //没有授权的不会自动授权
      isShowAuth: false,
      //是否隐藏授权
      key: ""
    };
  },
  computed: common_vendor.mapGetters(["isLogin"]),
  watch: {
    isLogin: {
      handler: function(newV, oldV) {
        if (newV) {
          this.getUserInfo();
        }
      },
      deep: true
    }
  },
  onLoad() {
    if (this.isLogin) {
      this.getUserInfo();
      api_api.verifyCode().then((res) => {
        this.$set(this, "key", res.data.key);
      });
    } else {
      libs_login.toLogin();
    }
  },
  methods: {
    /**
     * 授权回调
     */
    onLoadFun: function(e) {
      this.getUserInfo();
    },
    // 授权关闭
    authColse: function(e) {
      this.isShowAuth = e;
    },
    /**
     * 获取个人用户信息
     */
    getUserInfo: function() {
      let that = this;
      api_user.getUserInfo().then((res) => {
        let tel = res.data.phone;
        let phone = tel.substr(0, 3) + "****" + tel.substr(7);
        that.$set(that, "userInfo", res.data);
        that.phone = phone;
      });
    },
    /**
     * 发送验证码
     * 
     */
    async code() {
      let that = this;
      if (!that.userInfo.phone)
        return that.$util.Tips({
          title: that.$t(`手机号码不存在,无法发送验证码！`)
        });
      this.$refs.verify.show();
    },
    async success(data) {
      let that = this;
      this.$refs.verify.hide();
      await api_user.registerVerify({
        phone: that.userInfo.phone,
        type: "reset",
        key: that.key,
        captchaType: this.captchaType,
        captchaVerification: data.captchaVerification
      }).then((res) => {
        this.sendCode();
        that.$util.Tips({
          title: res.msg
        });
      }).catch((err) => {
        return that.$util.Tips({
          title: err
        });
      });
    },
    /**
     * H5登录 修改密码
     * 
     */
    editPwd: function(e) {
      let that = this, password = e.detail.value.password, qr_password = e.detail.value.qr_password, captcha = e.detail.value.captcha;
      if (!password)
        return that.$util.Tips({
          title: that.$t(`请输入新密码`)
        });
      if (qr_password != password)
        return that.$util.Tips({
          title: that.$t(`两次输入的密码不一致！`)
        });
      if (!captcha)
        return that.$util.Tips({
          title: that.$t(`请输入验证码`)
        });
      api_api.phoneRegisterReset({
        account: that.userInfo.phone,
        captcha,
        password
      }).then((res) => {
        return that.$util.Tips({
          title: res.msg
        }, {
          tab: 3,
          url: 1
        });
      }).catch((err) => {
        return that.$util.Tips({
          title: err
        });
      });
    }
  }
};
if (!Array) {
  const _component_navbar = common_vendor.resolveComponent("navbar");
  const _component_Verify = common_vendor.resolveComponent("Verify");
  (_component_navbar + _component_Verify)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0$17,
    b: common_vendor.t(_ctx.$t(`当前手机号`)),
    c: common_vendor.t($data.phone),
    d: common_assets._imports_1$17,
    e: _ctx.$t(`设置新密码`),
    f: $data.password,
    g: common_vendor.o(($event) => $data.password = $event.detail.value),
    h: common_assets._imports_1$17,
    i: _ctx.$t(`确认新密码`),
    j: $data.qr_password,
    k: common_vendor.o(($event) => $data.qr_password = $event.detail.value),
    l: common_assets._imports_1$18,
    m: _ctx.$t(`填写验证码`),
    n: $data.captcha,
    o: common_vendor.o(($event) => $data.captcha = $event.detail.value),
    p: common_vendor.t(_ctx.text),
    q: common_vendor.n(_ctx.disabled ? "active" : ""),
    r: common_vendor.o((...args) => $options.code && $options.code(...args)),
    s: common_vendor.t(_ctx.$t(`确认修改`)),
    t: common_vendor.o((...args) => $options.editPwd && $options.editPwd(...args)),
    v: common_vendor.sr("verify", "d70775bc-1"),
    w: common_vendor.o($options.success),
    x: common_vendor.p({
      captchaType: _ctx.captchaType,
      imgSize: {
        width: "330px",
        height: "155px"
      }
    }),
    y: common_vendor.s(_ctx.colorStyle)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/users/user_pwd_edit/index.js.map
