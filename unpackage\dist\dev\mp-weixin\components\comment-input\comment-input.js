"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const components_emojiPanel_sina = require("../emoji-panel/sina.js");
if (!Math) {
  (EmojiPanel + uniPopup)();
}
const EmojiPanel = () => "../emoji-panel/emoji-panel.js";
const uniPopup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
const _sfc_main = /* @__PURE__ */ Object.assign({
  name: "CommentInput"
}, {
  __name: "comment-input",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: "写评论..."
    },
    focus: {
      type: Boolean,
      default: false
    },
    maxLength: {
      type: Number,
      default: 500,
      validator: (value) => value > 0 && value <= 2e3
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ["send", "focus", "blur", "editorError"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const instance = common_vendor.getCurrentInstance();
    const editorCtx = common_vendor.ref(null);
    const commentText = common_vendor.ref("");
    const commentImage = common_vendor.ref(null);
    const isFocus = common_vendor.ref(false);
    const showEmoji = common_vendor.ref(false);
    const atUserList = common_vendor.ref([]);
    const isSubmittingComment = common_vendor.ref(false);
    const keepPanelOpen = common_vendor.ref(false);
    const hasContent = common_vendor.ref(false);
    const lastInputChar = common_vendor.ref("");
    const isToggleEmoji = common_vendor.ref(false);
    const isToggleAtUser = common_vendor.ref(false);
    const isAnyPopupOpen = common_vendor.ref(false);
    const isEditorReady = common_vendor.ref(false);
    const savedContent = common_vendor.ref(null);
    const emojiMap = common_vendor.reactive({});
    const phraseMap = common_vendor.reactive({});
    const reverseMap = common_vendor.reactive({});
    const inputDebounceTimer = common_vendor.ref(null);
    const retryTimer = common_vendor.ref(null);
    const retryCount = common_vendor.ref(0);
    const observer = common_vendor.ref(null);
    const atUserPopup = common_vendor.ref(null);
    const sendBtnClass = common_vendor.computed(() => {
      const isActive = hasContent.value && !isSubmittingComment.value;
      common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:157", "发送按钮状态计算:", {
        hasContent: hasContent.value,
        isSubmittingComment: isSubmittingComment.value,
        isActive
      });
      return {
        "send-btn": true,
        "active": isActive
      };
    });
    common_vendor.watch(() => props.focus, (val) => {
      isFocus.value = val;
    }, { immediate: true });
    common_vendor.watch(() => props.show, (val) => {
      if (!val) {
        showEmoji.value = false;
        isFocus.value = false;
      }
    }, { immediate: true });
    const initEmojiMap = () => {
      if (!Array.isArray(components_emojiPanel_sina.sinaEmoji) || components_emojiPanel_sina.sinaEmoji.length === 0) {
        common_vendor.index.__f__("warn", "at components/comment-input/comment-input.vue:184", "表情列表为空或格式错误");
        return;
      }
      Object.keys(emojiMap).forEach((key) => delete emojiMap[key]);
      Object.keys(phraseMap).forEach((key) => delete phraseMap[key]);
      Object.keys(reverseMap).forEach((key) => delete reverseMap[key]);
      components_emojiPanel_sina.sinaEmoji.forEach((emoji) => {
        const key = emoji.phrase || emoji.alt || emoji.value;
        const url = emoji.url || emoji.icon;
        if (!key || !url)
          return;
        try {
          const encodedKey = encodeURIComponent(key);
          emojiMap[encodedKey] = url;
          const bracketKey = key.startsWith("[") ? key : `[${key}]`;
          phraseMap[bracketKey] = url;
          createReverseMapping(url, bracketKey);
        } catch (err) {
          common_vendor.index.__f__("warn", "at components/comment-input/comment-input.vue:212", "处理表情映射出错:", key, err);
        }
      });
    };
    const createReverseMapping = (url, bracketKey) => {
      reverseMap[url] = bracketKey;
      const urlParts = url.split("/");
      const fileName = urlParts[urlParts.length - 1];
      if (fileName) {
        reverseMap[fileName] = bracketKey;
        reverseMap["/" + fileName] = bracketKey;
      }
      if (urlParts.length >= 2) {
        reverseMap[urlParts.slice(-2).join("/")] = bracketKey;
      }
      if (urlParts.length >= 3) {
        reverseMap[urlParts.slice(-3).join("/")] = bracketKey;
      }
    };
    const onEditorReady = () => {
      common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:241", "编辑器开始初始化");
      common_vendor.index.createSelectorQuery().in(instance).select("#commentEditor").context((res) => {
        common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:254", "编辑器上下文查询结果:", res);
        if (res && res.context) {
          editorCtx.value = res.context;
          isEditorReady.value = true;
          common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:258", "编辑器初始化完成");
          if (savedContent.value) {
            restoreContent();
          }
        } else {
          common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:265", "编辑器上下文获取失败");
          if (!retryCount.value)
            retryCount.value = 0;
          if (retryCount.value < 3) {
            retryCount.value++;
            retryTimer.value = setTimeout(() => {
              onEditorReady();
            }, 500 * retryCount.value);
          } else {
            common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:274", "编辑器初始化失败，已达到最大重试次数");
            emit("editorError", "编辑器初始化失败");
          }
        }
      }).exec();
    };
    const getSelection = () => {
      return new Promise((resolve) => {
        if (!editorCtx.value || !isEditorReady.value) {
          resolve(null);
          return;
        }
        editorCtx.value.getSelection({
          success: (res) => resolve(res),
          fail: () => resolve(null)
        });
      });
    };
    const getContents = (start, end) => {
      return new Promise((resolve) => {
        if (!editorCtx.value || !isEditorReady.value) {
          common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:302", "编辑器未就绪:", {
            editorCtx: !!editorCtx.value,
            isEditorReady: isEditorReady.value
          });
          resolve(null);
          return;
        }
        const options = {
          success: (res) => {
            common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:312", "getContents成功:", res);
            resolve(res);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:316", "getContents失败:", err);
            resolve(null);
          }
        };
        if (typeof start !== "undefined" && typeof end !== "undefined") {
          options.start = start;
          options.end = end;
        }
        try {
          editorCtx.value.getContents(options);
        } catch (err) {
          common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:329", "调用getContents异常:", err);
          resolve(null);
        }
      });
    };
    const onEditorInput = async (e) => {
      if (!editorCtx.value || !isEditorReady.value) {
        return;
      }
      if (inputDebounceTimer.value) {
        clearTimeout(inputDebounceTimer.value);
      }
      inputDebounceTimer.value = setTimeout(async () => {
        try {
          const contents = await getContents();
          if (!contents)
            return;
          commentText.value = contents.text || "";
          const hasEmoji = contents.html && contents.html.includes("emoji-img");
          const cleanText = commentText.value ? commentText.value.replace(/\n/g, "").trim() : "";
          const hasTextContent = cleanText.length > 0;
          const hasImageContent = !!commentImage.value;
          hasContent.value = hasTextContent || hasImageContent || hasEmoji;
          common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:364", "内容状态更新:", {
            originalText: commentText.value,
            cleanText,
            hasTextContent,
            hasImageContent,
            hasEmoji,
            hasContent: hasContent.value
          });
          const lastCharValue = commentText.value.slice(-1);
          if (lastCharValue === "@" && lastInputChar.value !== "@") {
            showAtUsers();
          }
          lastInputChar.value = lastCharValue;
          if (e && e.detail && (e.detail.keyCode === 8 || e.detail.keyCode === 46)) {
            await handleDelete();
          }
        } catch (err) {
          common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:385", "处理编辑器内容变化出错:", err);
        }
      }, 150);
    };
    const handleDelete = async () => {
      if (!editorCtx.value || !isEditorReady.value)
        return;
      try {
        const selection = await getSelection();
        if (!selection)
          return;
        const { start, end } = selection;
        const contents = await getContents(Math.max(0, start.offset - 1), end.offset + 1);
        if (!contents || !contents.nodes)
          return;
        const hasEmoji = contents.nodes.some(
          (node) => node.name === "img" && node.attrs && node.attrs.class && node.attrs.class.includes("emoji-img")
        );
        if (hasEmoji) {
          editorCtx.value.deleteContents({
            success: () => {
              onEditorInput();
            }
          });
        }
      } catch (err) {
        common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:419", "处理删除操作出错:", err);
      }
    };
    const editorFocus = () => {
      if (!keepPanelOpen.value && !isToggleEmoji.value && !isToggleAtUser.value) {
        showEmoji.value = false;
        if (atUserPopup.value) {
          atUserPopup.value.close();
        }
      }
      keepPanelOpen.value = false;
      isFocus.value = true;
      emit("focus");
    };
    const editorBlur = () => {
      isFocus.value = false;
      if (!isAnyPopupOpen.value && !keepPanelOpen.value && !isSubmittingComment.value) {
        if (editorCtx.value && isEditorReady.value) {
          editorCtx.value.getContents({
            success: (res) => {
              savedContent.value = res;
            }
          });
        }
        emit("blur");
      }
    };
    const toggleEmoji = () => {
      keepPanelOpen.value = true;
      isToggleEmoji.value = true;
      if (isToggleAtUser.value) {
        atUserPopup.value.close();
        isToggleAtUser.value = false;
      }
      setTimeout(() => {
        showEmoji.value = !showEmoji.value;
        isAnyPopupOpen.value = showEmoji.value;
        if (!showEmoji.value) {
          isToggleEmoji.value = false;
          keepPanelOpen.value = false;
        }
      }, 100);
    };
    const selectEmoji = (emoji) => {
      if (!editorCtx.value || !isEditorReady.value) {
        common_vendor.index.showToast({
          title: "编辑器未就绪",
          icon: "none"
        });
        return;
      }
      keepPanelOpen.value = true;
      isToggleEmoji.value = true;
      const url = emoji.url || emoji.icon;
      if (!url)
        return;
      common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:494", "插入表情:", {
        url,
        phrase: emoji.phrase || emoji.alt || emoji.value,
        mapping: reverseMap[url]
      });
      if (!reverseMap[url]) {
        const key = emoji.phrase || emoji.alt || emoji.value || "";
        if (key) {
          const bracketKey = key.startsWith("[") ? key : `[${key}]`;
          reverseMap[url] = bracketKey;
          const fileName = url.split("/").pop();
          reverseMap[fileName] = bracketKey;
          reverseMap["/" + fileName] = bracketKey;
        }
      }
      try {
        editorCtx.value.insertImage({
          src: url,
          width: "18px",
          height: "18px",
          extClass: "emoji-img",
          success: () => {
            onEditorInput();
            setTimeout(() => {
              isToggleEmoji.value = false;
            }, 100);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:528", "插入表情失败:", err);
            common_vendor.index.showToast({
              title: "插入表情失败",
              icon: "none"
            });
          }
        });
      } catch (err) {
        common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:536", "插入表情出错:", err);
        common_vendor.index.showToast({
          title: "插入表情失败",
          icon: "none"
        });
      }
    };
    const selectGif = (gif) => {
      keepPanelOpen.value = true;
      common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:548", "选择了GIF表情", gif);
      common_vendor.index.showToast({
        title: "GIF表情功能开发中",
        icon: "none"
      });
    };
    const deleteEmoji = () => {
      if (editorCtx.value) {
        editorCtx.value.undo();
      }
    };
    const selectAtUser = (user) => {
      const atText = `@${user.nickname} `;
      if (editorCtx.value && isEditorReady.value) {
        saveContent();
        editorCtx.value.insertText({
          text: atText,
          success: () => {
            onEditorInput();
            closeAtUsers();
            setTimeout(() => {
              if (editorCtx.value && isEditorReady.value) {
                editorCtx.value.focus();
              }
            }, 500);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:585", "插入@用户失败:", err);
            restoreContent();
          }
        });
      }
    };
    const convertHtmlToText = async (html) => {
      try {
        if (!html)
          return "";
        common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:598", "转换前的HTML:", html);
        const hasEmojiImg = html.includes("emoji-img");
        if (!hasEmojiImg) {
          let text2 = html.replace(/<[^>]+>/g, "");
          text2 = text2.replace(/&nbsp;/g, " ").replace(/&lt;/g, "<").replace(/&gt;/g, ">").replace(/&amp;/g, "&").replace(/&quot;/g, '"').replace(/&#39;/g, "'");
          common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:616", "纯文本转换结果:", text2);
          return text2.trim();
        }
        const emojis = [];
        const imgRegex = /<img[^>]*class="[^"]*emoji-img[^"]*"[^>]*>/g;
        const srcRegex = /src=["']([^"']+)["']/;
        let text = html.replace(imgRegex, (imgTag) => {
          common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:626", "匹配到的表情标签:", imgTag);
          const srcMatch = imgTag.match(srcRegex);
          if (!srcMatch) {
            return "[表情]";
          }
          const src = srcMatch[1];
          let emojiText = reverseMap[src];
          if (!emojiText) {
            const emoji = components_emojiPanel_sina.sinaEmoji.find((e) => {
              const emojiUrl = e.url || e.icon || "";
              return src === emojiUrl || src.endsWith(emojiUrl.split("/").pop());
            });
            if (emoji) {
              emojiText = emoji.phrase || emoji.alt || emoji.value;
            } else {
              emojiText = "[表情]";
            }
          }
          emojis.push(emojiText);
          return emojiText;
        });
        text = text.replace(/<[^>]+>/g, "");
        text = text.replace(/&nbsp;/g, " ").replace(/&lt;/g, "<").replace(/&gt;/g, ">").replace(/&amp;/g, "&").replace(/&quot;/g, '"').replace(/&#39;/g, "'");
        common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:667", "包含表情的转换结果:", text);
        return text.trim() || emojis.join("");
      } catch (err) {
        common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:671", "转换HTML出错:", err);
        const fallbackText = html.replace(/<[^>]+>/g, "").trim();
        return fallbackText || "[表情]";
      }
    };
    const onSendButtonClick = (e) => {
      common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:680", "=== 发送按钮点击事件触发 ===");
      common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:681", "点击事件详情:", e);
      common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:682", "事件类型:", e.type);
      common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:683", "当前组件状态:", {
        hasContent: hasContent.value,
        isSubmittingComment: isSubmittingComment.value,
        disabled: props.disabled,
        editorReady: isEditorReady.value,
        showEmoji: showEmoji.value,
        isFocus: isFocus.value
      });
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      if (e && e.preventDefault) {
        e.preventDefault();
      }
      if (!hasContent.value) {
        common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:702", "按钮不可点击：没有内容");
        return;
      }
      if (isSubmittingComment.value) {
        common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:707", "按钮不可点击：正在提交中");
        return;
      }
      common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:712", "准备调用发送方法...");
      handleSendComment();
    };
    const handleSendComment = async () => {
      common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:718", "=== 发送按钮被点击 ===");
      common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:719", "=== 开始发送评论 ===");
      common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:720", "编辑器状态:", {
        editorCtx: !!editorCtx.value,
        isEditorReady: isEditorReady.value,
        isSubmittingComment: isSubmittingComment.value
      });
      if (!editorCtx.value || !isEditorReady.value) {
        common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:727", "编辑器未就绪");
        common_vendor.index.showToast({
          title: "编辑器未就绪，请稍候",
          icon: "none"
        });
        return;
      }
      if (isSubmittingComment.value) {
        common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:736", "正在提交中，忽略重复点击");
        return;
      }
      try {
        common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:741", "开始获取编辑器内容...");
        const contents = await getContents();
        common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:743", "发送时获取到的编辑器内容:", contents);
        if (!contents) {
          common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:746", "未获取到编辑器内容");
          common_vendor.index.showToast({
            title: "获取内容失败，请重试",
            icon: "none"
          });
          return;
        }
        const { html, text } = contents;
        const hasEmoji = html && html.includes("emoji-img");
        const cleanText = text ? text.replace(/\n/g, "").trim() : "";
        const hasTextContent = cleanText.length > 0;
        const hasImageContent = !!commentImage.value;
        common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:764", "内容检查详情:", {
          originalText: text,
          cleanText,
          hasTextContent,
          hasImageContent,
          hasEmoji,
          textLength: cleanText.length,
          commentImage: commentImage.value
        });
        if (!hasTextContent && !hasImageContent && !hasEmoji) {
          common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:775", "没有任何内容，提示用户");
          common_vendor.index.showToast({
            title: "请输入评论内容",
            icon: "none"
          });
          return;
        }
        common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:783", "开始处理评论内容...");
        isSubmittingComment.value = true;
        let finalContent = "";
        if (hasTextContent) {
          if (hasEmoji) {
            common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:792", "处理包含表情的文字内容");
            finalContent = await convertHtmlToText(html || "");
          } else {
            common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:796", "处理纯文字内容");
            finalContent = cleanText;
          }
        } else if (hasEmoji) {
          common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:801", "处理纯表情内容");
          finalContent = await convertHtmlToText(html || "");
        }
        if (!finalContent.trim() && !hasImageContent) {
          finalContent = "[表情]";
        }
        common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:810", "=== 准备发送评论 ===");
        common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:811", "最终发送内容:", {
          content: finalContent,
          image: commentImage.value || "",
          contentLength: finalContent.length
        });
        emit("send", {
          content: finalContent.trim(),
          image: commentImage.value || ""
        });
        common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:823", "评论事件已发送");
        common_vendor.index.showToast({
          title: "发送成功",
          icon: "success",
          duration: 1500
        });
        setTimeout(() => {
          if (editorCtx.value) {
            editorCtx.value.clear();
          }
          commentImage.value = null;
          showEmoji.value = false;
          hasContent.value = false;
          savedContent.value = null;
          common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:842", "评论框状态已重置");
        }, 500);
      } catch (err) {
        common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:846", "=== 发送评论失败 ===");
        common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:847", "错误详情:", err);
        common_vendor.index.showToast({
          title: "发送失败，请重试",
          icon: "none"
        });
      } finally {
        setTimeout(() => {
          isSubmittingComment.value = false;
          common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:855", "提交状态已重置");
        }, 800);
      }
    };
    const clear = () => {
      if (editorCtx.value) {
        editorCtx.value.clear();
      }
      commentImage.value = null;
      hasContent.value = false;
      savedContent.value = null;
    };
    const setContent = (text) => {
      if (editorCtx.value && text) {
        editorCtx.value.setContents({
          html: text,
          success: () => {
            onEditorInput();
          }
        });
      }
    };
    const showImagePicker = () => {
      keepPanelOpen.value = true;
      showEmoji.value = false;
      if (instance && instance.appContext.config.globalProperties.$util && instance.appContext.config.globalProperties.$util.uploadImageChange) {
        instance.appContext.config.globalProperties.$util.uploadImageChange(
          "upload/image",
          // 上传成功
          (res) => {
            common_vendor.index.hideLoading();
            if (res.data && res.data.url) {
              commentImage.value = res.data.url;
              hasContent.value = true;
              common_vendor.index.showToast({
                title: "图片已添加",
                icon: "success"
              });
            } else {
              common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:903", "图片上传返回数据异常:", res);
              common_vendor.index.showToast({
                title: "图片添加失败",
                icon: "none"
              });
            }
          },
          // 上传失败或取消
          (err) => {
            common_vendor.index.hideLoading();
            common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:913", "图片上传取消或失败:", err);
          },
          // 处理图片尺寸
          (res) => {
            if (res && res.w && res.h) {
              common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:918", "图片尺寸:", res.w, "x", res.h);
            }
          }
        );
      } else {
        common_vendor.index.chooseImage({
          count: 1,
          sizeType: ["original", "compressed"],
          sourceType: ["album", "camera"],
          success: (res) => {
            const tempFilePath = res.tempFilePaths[0];
            common_vendor.index.getFileInfo({
              filePath: tempFilePath,
              success: (fileInfo) => {
                const fileSizeMB = fileInfo.size / (1024 * 1024);
                if (fileSizeMB > 5) {
                  common_vendor.index.showToast({
                    title: "图片大小不能超过5MB",
                    icon: "none"
                  });
                  return;
                }
                common_vendor.index.showLoading({
                  title: "上传中...",
                  mask: true
                });
                common_vendor.index.uploadFile({
                  url: instance && instance.appContext.config.globalProperties.$api && instance.appContext.config.globalProperties.$api.uploadUrl || "/api/upload/image",
                  filePath: tempFilePath,
                  name: "file",
                  success: (uploadRes) => {
                    common_vendor.index.hideLoading();
                    try {
                      const data = JSON.parse(uploadRes.data);
                      if (data.code === 200 && data.data && data.data.url) {
                        commentImage.value = data.data.url;
                        hasContent.value = true;
                        common_vendor.index.showToast({
                          title: "图片已添加",
                          icon: "success"
                        });
                      } else {
                        throw new Error("上传返回数据异常");
                      }
                    } catch (e) {
                      common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:970", "图片上传解析失败:", e);
                      common_vendor.index.showToast({
                        title: "图片添加失败",
                        icon: "none"
                      });
                    }
                  },
                  fail: (err) => {
                    common_vendor.index.hideLoading();
                    common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:979", "图片上传失败:", err);
                    common_vendor.index.showToast({
                      title: "图片上传失败",
                      icon: "none"
                    });
                  }
                });
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:988", "获取文件信息失败:", err);
                common_vendor.index.showToast({
                  title: "无法获取图片信息",
                  icon: "none"
                });
              }
            });
          }
        });
      }
    };
    const removeCommentImage = () => {
      commentImage.value = null;
    };
    const closeAtUsers = () => {
      isToggleAtUser.value = false;
      isAnyPopupOpen.value = showEmoji.value;
      atUserPopup.value.close();
      restoreContent();
    };
    const showAtUsers = () => {
      keepPanelOpen.value = true;
      isToggleAtUser.value = true;
      saveContent();
      if (showEmoji.value) {
        showEmoji.value = false;
        isToggleEmoji.value = false;
      }
      atUserPopup.value.open();
      isAnyPopupOpen.value = true;
    };
    const previewImage = () => {
      if (commentImage.value) {
        common_vendor.index.previewImage({
          urls: [commentImage.value],
          current: commentImage.value
        });
      }
    };
    const saveContent = () => {
      if (!editorCtx.value || !isEditorReady.value)
        return;
      editorCtx.value.getContents({
        success: (res) => {
          savedContent.value = res;
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:1128", "保存内容失败:", err);
        }
      });
    };
    const restoreContent = () => {
      if (!editorCtx.value || !isEditorReady.value || !savedContent.value)
        return;
      editorCtx.value.setContents({
        html: savedContent.value.html,
        success: () => {
          onEditorInput();
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:1143", "恢复内容失败:", err);
        }
      });
    };
    const setEditorFocus = () => {
      if (!editorCtx.value || !isEditorReady.value)
        return;
      try {
        if (typeof editorCtx.value.focus === "function") {
          editorCtx.value.focus();
        }
      } catch (err) {
        common_vendor.index.__f__("error", "at components/comment-input/comment-input.vue:1167", "设置编辑器焦点失败:", err);
      }
    };
    const resetState = () => {
      commentText.value = "";
      commentImage.value = null;
      showEmoji.value = false;
      isToggleEmoji.value = false;
      isToggleAtUser.value = false;
      isAnyPopupOpen.value = false;
      keepPanelOpen.value = false;
      savedContent.value = null;
      lastInputChar.value = "";
      retryCount.value = 0;
      if (inputDebounceTimer.value) {
        clearTimeout(inputDebounceTimer.value);
        inputDebounceTimer.value = null;
      }
      if (retryTimer.value) {
        clearTimeout(retryTimer.value);
        retryTimer.value = null;
      }
    };
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at components/comment-input/comment-input.vue:1197", "comment-input组件已挂载");
      initEmojiMap();
    });
    common_vendor.onBeforeUnmount(() => {
      if (inputDebounceTimer.value) {
        clearTimeout(inputDebounceTimer.value);
        inputDebounceTimer.value = null;
      }
      if (retryTimer.value) {
        clearTimeout(retryTimer.value);
        retryTimer.value = null;
      }
      if (observer.value) {
        observer.value.disconnect();
        observer.value = null;
      }
      editorCtx.value = null;
    });
    __expose({
      clear,
      setContent,
      resetState,
      setEditorFocus
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.show
      }, __props.show ? common_vendor.e({
        b: __props.placeholder,
        c: __props.disabled,
        d: common_vendor.o(onEditorReady),
        e: common_vendor.o(editorFocus),
        f: common_vendor.o(editorBlur),
        g: common_vendor.o(onEditorInput),
        h: commentImage.value
      }, commentImage.value ? {
        i: commentImage.value,
        j: common_vendor.o(previewImage),
        k: common_vendor.o(removeCommentImage)
      } : {}, {
        l: common_assets._imports_0$10,
        m: common_vendor.o(showImagePicker),
        n: common_assets._imports_1$11,
        o: common_vendor.o(showAtUsers),
        p: common_assets._imports_2$9,
        q: common_vendor.o(toggleEmoji),
        r: common_vendor.t(isSubmittingComment.value ? "发送中..." : "发送"),
        s: common_vendor.n(sendBtnClass.value),
        t: common_vendor.o(onSendButtonClick),
        v: common_vendor.o(onSendButtonClick),
        w: common_vendor.o(onSendButtonClick),
        x: common_vendor.o(selectEmoji),
        y: common_vendor.o(selectGif),
        z: common_vendor.o(deleteEmoji),
        A: common_vendor.p({
          show: showEmoji.value,
          content: commentText.value
        }),
        B: common_vendor.o(closeAtUsers),
        C: common_vendor.f(atUserList.value, (user, index, i0) => {
          return {
            a: user.avatar,
            b: common_vendor.t(user.nickname),
            c: index,
            d: common_vendor.o(($event) => selectAtUser(user), index)
          };
        }),
        D: common_vendor.sr(atUserPopup, "0f1cc78f-1", {
          "k": "atUserPopup"
        }),
        E: common_vendor.p({
          type: "bottom"
        })
      }) : {});
    };
  }
});
wx.createComponent(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/comment-input/comment-input.js.map
