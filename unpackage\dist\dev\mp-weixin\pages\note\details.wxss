
/* 全局表情样式重置 - 确保尺寸一致 */
page image[data-emoji],
page img[data-emoji],
.container image[data-emoji],
.container img[data-emoji] {
  width: 32rpx !important;
  height: 32rpx !important;
  max-width: 32rpx !important;
  max-height: 32rpx !important;
  min-width: 32rpx !important;
  min-height: 32rpx !important;
  object-fit: cover !important;
  display: inline-block !important;
  vertical-align: middle !important;
  margin: 0 4rpx !important;
  border-radius: 4rpx !important;
}
.nav-box{
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  width: 100%;
  background: #fff;
  box-sizing: border-box;
}
.nav-item .nav-back{
  padding: 0 30rpx;
  width: 34rpx;
  height: 100%;
  border-radius: 50%;
}
.nav-item{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.nav-user .nav-user-avatar{
  margin-right: 15rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f8f8f8;
  border: 1px solid #f5f5f5;
}
.nav-user .nav-user-name{
  max-width: 150rpx;
  font-size: 26rpx;
  font-weight: 700;
}
.nav-user .nav-user-adds image{
  width: 18rpx;
  height: 18rpx;
}
.nav-user .nav-user-adds text{
  margin-left: 4rpx;
  color: #999;
  font-size: 18rpx;
  font-weight: 500;
}
.nav-user{
  flex: 1;
  justify-content: flex-start;
  padding-right: 10rpx;
}
.nav-user .user-info{
  display: flex;
  align-items: center;
}
.nav-user .follow-btn{
  margin-left: 20rpx;
  padding: 0 20rpx;
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 48rpx;
  font-size: 20rpx;
  font-weight: 700;
  color: #f8f8f8;
  background: #555555;
  text-align: center;
  transition: all 0.3s;
}
.nav-user .follow-btn.active{
  color: #999;
  background: rgba(0, 0, 0, 0.08);
}
.avatar-info{
  margin-left: 20rpx;
}
.content-box .swiper-box{
  z-index: 1;
  width: 100%;
  transition: height .3s;
  overflow: hidden;
  background-color: #f8f8f8;
}
.swiper-box .swiper-item{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 图片样式 */
.swiper-image {
  width: 100%;
  height: 100%;
  display: block;
}
.content-box .current{
  position: absolute;
  z-index: 1;
  right: 15rpx;
  padding: 0 15rpx;
  height: 40rpx;
  color: #fff;
  font-size: 20rpx;
  font-weight: 700;
  background: rgba(0, 0, 0, .4);
  border: 1px solid rgba(255, 255, 255, .16);
  border-radius: 40rpx;
}
.content-box .indicator{
  margin: -40rpx 30rpx 0;
  width: calc(100% - 60rpx);
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 10;
}
.indicator .indicator-item{
  z-index: 1;
  margin: 0 2.5px;
  height: 3px;
  border-radius: 3px;
  background: rgba(255, 255, 255, .3);
  transition: background-color 0.3s;
}
.indicator .indicator-item.active{
  background: rgba(255, 255, 255, .9);
}
.content-box .video-box{
  width: 100%;
  height: 421.875rpx;
}
.content-box .audio-box{
  width: calc(100% - 60rpx);
  margin: 0 30rpx;
  height: 180rpx;
  color: #fff;
  border-radius: 8rpx;
  position: relative;
  overflow: hidden;
}
.audio-box .audio-left{
  margin-right: 30rpx;
  width: 180rpx;
  height: 180rpx;
  position: relative;
}
.audio-box .audio-left .icon{
  position: absolute;
  top: 65rpx;
  right: 65rpx;
  bottom: 65rpx;
  left: 65rpx;
  width: 50rpx;
  height: 50rpx;
}
.audio-box .audio-bg{
  position: absolute;
  top: 0;
  left: 0;
  z-index: -2;
  width: 100%;
  height: 100%;
}
.audio-box .audio-mb{
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  -webkit-backdrop-filter: saturate(150%) blur(25px);
  backdrop-filter: saturate(150%) blur(25px);
  background: rgba(0, 0, 0, .5);
}
.audio-box .audio-t1{
  font-size: 32rpx;
  font-weight: 700;
}
.audio-box .audio-t2{
  margin-top: 12rpx;
  opacity: .8;
  font-size: 24rpx;
}
.audio-box .audio-play{
  margin: 0 30rpx;
  width: 68rpx;
  height: 68rpx;
  background: rgba(255, 255, 255, .15);
  border-radius: 50%;
}
.audio-box .audio-play .icon{
  margin: 20rpx;
  width: 28rpx;
  height: 28rpx;
}
.content-box .info-box{
  width: calc(100% - 60rpx);
  padding: 30rpx;
}
.info-box .info-content{
  width: 100%;
  word-break: break-word;
  white-space: pre-line;
}
.info-box .info-content text{
  font-size: 28rpx;
  font-weight: 400;
}
.info-box .info-tag{
  margin-top: 20rpx;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.info-tag .tag-item{
  margin: 10rpx 10rpx 0 0;
  padding: 8rpx;
  height: 40rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
}
.info-tag .tag-item .icon{
  width: 40rpx;
  height: 40rpx;
}
.info-tag .tag-item text{
  font-size: 20rpx;
  padding: 0 8rpx 0 12rpx;
}
.info-box .more{
  padding: 30rpx 0;
  width: 100%;
  justify-content: space-between;
  border-bottom: 1px solid #f8f8f8;
  position: relative;
}
.info-box .more .more-left{
  color: #999;
  font-size: 20rpx;
}
.info-box .more .more-right{
  position: absolute;
  right: -30rpx;
  padding: 20rpx 30rpx;
  width: 24rpx;
  height: 24rpx;
}
.comment-box{
  width: calc(100% - 60rpx);
  padding: 0 30rpx 120px;
}
.comment-box .comment-top{
  width: 100%;
  justify-content: space-between;
}
.comment-top .top-title{
  font-size: 26rpx;
  font-weight: 700;
}
.comment-top .top-btn{
  width: 136rpx;
  padding: 6rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  position: relative;
}
.comment-top .top-btn .btn-item, .comment-top .top-btn .btn-active{
  width: 68rpx;
  height: 44rpx;
}
.comment-top .top-btn .btn-item{
  z-index: 2;
  line-height: 44rpx;
  text-align: center;
  font-size: 20rpx;
  font-weight: 500;
  transition: color .3s;
}
.comment-top .top-btn .btn-active{
  position: absolute;
  z-index: 1;
  background: #fff;
  border-radius: 4rpx;
  transition: left .3s;
}
.comment-box .comment-item{
  width: 100%;
  margin-top: 30rpx;
  justify-content: space-between;
  align-items: flex-start !important;
}
.comment-item .comment-avatar, .comment-item .comment-avatar-z{
  background: #f8f8f8;
  border: 1px solid #f5f5f5;
  border-radius: 50%;
  overflow: hidden;
}
.comment-item .comment-avatar{
  width: 64rpx;
  height: 64rpx;
}
.comment-item .comment-avatar-z{
  width: 44rpx;
  height: 44rpx;
}
.comment-item .comment-info{
  width: calc(100% - 88rpx);
}
.unfold{
  padding: 20rpx 68rpx;
  color: #4e6ef2;
  font-size: 24rpx;
  font-weight: 700;
  transition: opacity 0.3s;
}
.unfold:active {
  opacity: 0.7;
}
.comment-info .comment-info-top{
  font-size: 24rpx;
  color: #999;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.comment-info .user-info-left {
  display: flex;
  align-items: center;
}
.comment-info .like-icon {
  display: flex;
  align-items: center;
  margin-left: auto;
}
.comment-info .like-icon image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 4rpx;
}
.comment-info .like-icon text {
  font-size: 20rpx;
  color: #999;
}
.comment-info .comment-info-top-z {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.comment-info .comment-info-top-z view{
  max-width: 230rpx;
  font-size: 22rpx;
  color: #999;
}
.comment-info .comment-info-top-z text{
  margin-right: 8rpx;
  color: #333;
  font-size: 22rpx;
  font-weight: 500;
}
.comment-info .nn, .comment-info .zz, .comment-info .wo{
  margin-right: 8rpx;
}
.comment-info .zz{
  color: #FA5150 !important;
  font-weight: 700;
}
.comment-info .wo{
  color: #000 !important;
  font-weight: 700;
}
.comment-info .db{
  color: #ccc !important;
}
.comment-info .comment-info-content{
  word-break: break-word;
  white-space: pre-wrap;
}
.comment-info .comment-info-content text{
  color: #333;
  font-size: 26rpx;
  font-weight: 400;
  display: inline;
}
.comment-info .comment-info-bottom{
  margin-top: 15rpx;
  color: #999;
  font-size: 20rpx;
}
.comment-info .comment-info-bottom view{
  margin-left: 30rpx;
  font-weight: 700;
}

/* 嵌套回复样式 */
/* 嵌套回复样式已移除，使用扁平化结构 */
.comment-box .no-more{
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  color: #999;
  font-size: 20rpx;
}
.footer-box{
  position: fixed;
  z-index: 99;
  left: 0;
  bottom: 0;
  width: calc(100% - 20rpx);
  padding: 20rpx 10rpx;
  background: #fff;
  border-top: 1px solid #f8f8f8;
  padding-bottom: max(env(safe-area-inset-bottom), 20rpx);
}
.footer-box .footer-item{
  width: 100%;
  height: 80rpx;
  justify-content: space-between;
}
.footer-means{
  margin-left: 20rpx;
  padding: 0 30rpx;
  height: 80rpx;
  font-size: 20rpx;
  font-weight: 700;
  background: #f8f8f8;
  border-radius: 40rpx;
}
.footer-means image{
  margin-left: 10rpx;
  width: 20rpx;
  height: 20rpx;
}
.footer-comment{
  margin-left: 20rpx;
  width: 280rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #f8f8f8;
  border-radius: 40rpx;
}
.footer-comment image{
  margin: 0 20rpx 0 10rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}
.footer-comment view{
  max-width: calc(100% - 120rpx);
  color: #999;
  font-size: 24rpx;
  font-weight: 400;
  word-break: break-word;
  white-space: pre-line;
  display: block;
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}
.footer-item .footer-icon{
  padding: 16rpx 20rpx;
  display: flex;
}
.footer-item .footer-icon image{
  width: 48rpx;
  height: 48rpx;
}
.footer-item .footer-icon text{
  margin-left: 10rpx;
  color: #999;
  font-size: 18rpx;
  font-weight: 700;
}
.popup-comment-mask{
  position: fixed;
  z-index: 998;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.01); /* 几乎透明的背景，只为捕获点击事件 */
}
.popup-comment{
  position: fixed;
  z-index: 999;
  left: 0;
  bottom: 0;
  width: 100%;
  background: #fff;
  border-top: 1px solid #f1f1f1;
  display: flex;
  flex-direction: column;
}
.popup-comment .send{
  margin: 0 0 15rpx 30rpx;
  width: 48rpx;
  height: 48rpx;
}
.popup-comment .comment-textarea{
  width: calc(100% - 98rpx);
  padding: 10rpx 20rpx;
  background: #f8f8f8;
  border-radius: 30rpx;
}
.popup-comment .comment-textarea textarea{
  width: 100%;
  line-height: 32rpx;
  min-height: 96rpx;
  max-height: 320rpx;
  font-size: 26rpx;
}
.popup-comment .comment-icon{
  width: calc(100% - 20rpx);
  padding: 30rpx 10rpx;
}
.share-popup{
  background: #fff;
  border-radius: 30rpx;
  padding: 30rpx;
  overflow: hidden;
}
.share-popup .share-img{
  width: 473rpx;
  height: 237.5rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  display: block;
}
.share-popup .share-tips{
  margin: 30rpx 0;
  width: 473rpx;
  font-size: 26rpx;
  line-height: 48rpx;
  position: relative;
}
.share-popup .share-tips image{
  position: absolute;
  top: 0;
  width: 48rpx;
  height: 48rpx;
  margin: 0 15rpx;
}
.share-popup .share-btn{
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 16rpx;
}
.more-popup{
  width: 100%;
  background: #fff;
  border-radius: 30rpx 30rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
  overflow: hidden;
}
.more-popup .more-tips{
  width: calc(100% - 60rpx);
  padding: 20rpx 30rpx;
  font-size: 20rpx;
  color: #999;
  background: #f8f8f8;
}
.more-popup .more-item{
  width: calc(100% - 60rpx);
  padding: 30rpx;
  font-size: 26rpx;
  font-weight: 700;
  justify-content: space-between;
}
.more-popup .more-item:first-child{
  padding-top: 40rpx;
}
.more-popup .more-item:last-child{
  padding-bottom: 40rpx;
}
.more-popup .more-item:hover{
  background: #f8f8f8;
}
.more-popup .more-item image{
  width: 36rpx;
  height: 36rpx;
}
.empty-box{
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}
.empty-box image{
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-box .e1{
  font-size: 28rpx;
  font-weight: 700;
  margin-bottom: 10rpx;
}
.empty-box .e2{
  font-size: 24rpx;
  color: #999;
}
.tips-box{
  justify-content: center;
  width: 100%;
}
.tips-box .tips-item{
  background: #000;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 700;
}
.df{
  display: flex;
  align-items: center;
}
.ohto{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.bUp{
  transition: bottom .3s;
}

/* 点赞按钮样式 */
.like-action {
  margin-left: 24rpx;
  font-size: 24rpx;
  color: #999;
}
.like-text {
  color: #999;
}
.like-text.liked {
  color: #FA5150;
}
.like-icon text {
  color: #999;
}
.like-icon image {
  width: 32rpx;
  height: 32rpx;
}
.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 20rpx;
}
.loading-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.load-more-children {
  padding: 10rpx 0 10rpx 60rpx;
  font-size: 24rpx;
  color: #4e6ef2;
}
.audio-error {
  margin: 0 30rpx;
  width: 68rpx;
  height: 68rpx;
  background: rgba(255, 255, 255, .15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.audio-error text {
  color: #ff4d4f;
  font-size: 20rpx;
  text-align: center;
}
.send-button-wrapper {
  padding: 8rpx;
  margin: 0 0 15rpx 30rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.send-button-wrapper {
  padding: 8rpx;
  margin: 0 0 15rpx 30rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}
.send-button-wrapper.active {
  background-color: #ff4081;
  border-radius: 50%;
}
.send-button-wrapper .send {
  width: 48rpx;
  height: 48rpx;
}




/* 图片容器样式 */
/* 删除多余样式，使用上面的样式统一处理 */

/* 评论输入框样式已移至组件中 */

/* 表情面板样式已移至组件中 */
.at-user-popup {
  position: fixed;
  z-index: 999;
  left: 0;
  width: 100%;
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.close-btn {
  width: 80rpx;
  height: 48rpx;
  line-height: 48rpx;
  text-align: center;
  color: #999;
  font-size: 24rpx;
  font-weight: 700;
  border-radius: 48rpx;
  border: 1px solid #f5f5f5;
}
.user-list {
  margin-top: 10rpx;
}
.user-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}
.user-name {
  max-width: 200rpx;
  font-size: 26rpx;
  font-weight: 700;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.empty-users {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  color: #999;
  font-size: 20rpx;
}

/* 评论图片样式 */
.comment-image {
  margin-top: 10rpx;
  max-width: 200rpx;
  max-height: 200rpx;
  border-radius: 8rpx;
  transition: transform 0.2s ease;
}
.comment-image:active {
  transform: scale(0.95);
}
.reply-comment-image {
  max-width: 160rpx;
  max-height: 160rpx;
}

/* 表情显示优化样式 */
.comment-rich-text {
  line-height: 1.6;
  word-break: break-word;
  white-space: pre-wrap;
}
.reply-rich-text {
  font-size: 24rpx;
}

/* 表情图片样式 - 强制固定尺寸 */
.emoji-img, .emoji-img-inline {
  width: 32rpx !important;
  height: 32rpx !important;
  max-width: 32rpx !important;
  max-height: 32rpx !important;
  min-width: 32rpx !important;
  min-height: 32rpx !important;
  vertical-align: middle !important;
  margin: 0 4rpx !important;
  display: inline-block !important;
  border-radius: 4rpx !important;
  object-fit: cover !important;
  transition: transform 0.1s ease;
}
.emoji-img:active, .emoji-img-inline:active {
  transform: scale(1.1);
}

/* 回复中的表情稍小一些 */
.reply-rich-text .emoji-img,
.reply-rich-text .emoji-img-inline {
  width: 28rpx !important;
  height: 28rpx !important;
  max-width: 28rpx !important;
  max-height: 28rpx !important;
  min-width: 28rpx !important;
  min-height: 28rpx !important;
  margin: 0 2rpx !important;
}

/* 删除的评论样式 */
.deleted-comment {
  color: #ccc !important;
  font-style: italic;
}

/* 表情加载失败的占位样式 */
.emoji-img-error {
  width: 32rpx;
  height: 32rpx;
  background-color: #f5f5f5;
  border-radius: 4rpx;
  display: inline-block;
  vertical-align: middle;
  margin: 0 4rpx;
  position: relative;
}
.emoji-img-error::after {
  content: '😊';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20rpx;
  opacity: 0.5;
}

/* 评论内容整体优化 */
.comment-info-content {
  margin-top: 8rpx;
  padding: 4rpx 0;
  border-radius: 4rpx;
  transition: background-color 0.2s ease;
}
.comment-info-content:active {
  background-color: rgba(0, 0, 0, 0.02);
}

/* 强制表情尺寸一致 - 更高优先级选择器 */
.comment-rich-text image[data-emoji],
.comment-rich-text img[data-emoji],
.reply-rich-text image[data-emoji],
.reply-rich-text img[data-emoji] {
  width: 32rpx !important;
  height: 32rpx !important;
  max-width: 32rpx !important;
  max-height: 32rpx !important;
  min-width: 32rpx !important;
  min-height: 32rpx !important;
  object-fit: cover !important;
  display: inline-block !important;
  vertical-align: middle !important;
  margin: 0 4rpx !important;
  border-radius: 4rpx !important;
}

/* 回复中的表情强制尺寸 */
.reply-rich-text image[data-emoji],
.reply-rich-text img[data-emoji] {
  width: 28rpx !important;
  height: 28rpx !important;
  max-width: 28rpx !important;
  max-height: 28rpx !important;
  min-width: 28rpx !important;
  min-height: 28rpx !important;
  margin: 0 2rpx !important;
}

/* 通用表情图片强制样式 */
rich-text image[data-emoji],
rich-text img[data-emoji] {
  width: 32rpx !important;
  height: 32rpx !important;
  max-width: 32rpx !important;
  max-height: 32rpx !important;
  min-width: 32rpx !important;
  min-height: 32rpx !important;
  object-fit: cover !important;
  display: inline-block !important;
  vertical-align: middle !important;
}

/* 响应式表情大小 */
@media screen and (max-width: 750rpx) {
.emoji-img, .emoji-img-inline {
    width: 28rpx !important;
    height: 28rpx !important;
    max-width: 28rpx !important;
    max-height: 28rpx !important;
    min-width: 28rpx !important;
    min-height: 28rpx !important;
}
.reply-rich-text .emoji-img,
  .reply-rich-text .emoji-img-inline {
    width: 24rpx !important;
    height: 24rpx !important;
    max-width: 24rpx !important;
    max-height: 24rpx !important;
    min-width: 24rpx !important;
    min-height: 24rpx !important;
}
}

/* 表情预览弹窗样式 */
.emoji-preview-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  border-radius: 12rpx;
  padding: 20rpx;
  z-index: 9999;
  animation: fadeIn 0.2s ease;
}
.emoji-preview-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}
@keyframes fadeIn {
from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
}
to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}
}
.emoji-tabs {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 20rpx;
  border-bottom: 1rpx solid #f1f1f1;
}
.emoji-tabs-inner {
  display: flex;
  justify-content: space-between;
}
.emoji-tab-item {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  color: #999;
}
.emoji-tab-item.active {
  border-bottom: 2rpx solid #ff4d6a;
  color: #ff4d6a;
}
.emoji-content {
  flex: 1;
  padding: 20rpx;
}
.empty-emoji {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
  font-size: 24rpx;
}
.emoji-tools {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 20rpx;
  border-top: 1rpx solid #f1f1f1;
}
.emoji-backspace {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.emoji-backspace image {
  width: 44rpx;
  height: 44rpx;
}
.emoji-send {
  width: 120rpx;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  background-color: #f5f5f5;
  color: #ccc;
  font-size: 28rpx;
  border-radius: 32rpx;
}
.emoji-send.active {
  background-color: #ff4d6a;
  color: #fff;
}
.gif-search {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 64rpx;
  border-bottom: 1rpx solid #f1f1f1;
}
.gif-search image {
  width: 44rpx;
  height: 44rpx;
}
.gif-item {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.gif-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}



