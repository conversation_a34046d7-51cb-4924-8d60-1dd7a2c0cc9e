"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "money",
  props: {
    type: {
      type: Number,
      default: 0
    },
    price: {
      type: [String, Number],
      default: "0.00"
    },
    qs: {
      type: String,
      default: "38rpx"
    },
    ts: {
      type: String,
      default: "22rpx"
    },
    cor: {
      type: String,
      default: "#000"
    }
  },
  methods: {
    qsHandle(price) {
      return parseInt(String(price));
    },
    tsHandle(price) {
      var decimalPart = parseFloat(String(price)).toFixed(2).slice(-2);
      if (this.type == 1 && decimalPart === "00") {
        return "";
      } else if (this.type == 1 && decimalPart[1] === "0") {
        return "." + decimalPart[0];
      } else {
        return "." + decimalPart;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $props.ts,
    b: $props.ts,
    c: $props.cor,
    d: common_vendor.t($options.qsHandle($props.price)),
    e: $props.qs,
    f: $props.qs,
    g: $props.cor,
    h: common_vendor.t($options.tsHandle($props.price)),
    i: $props.ts,
    j: $props.ts,
    k: $props.cor
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/money/money.js.map
