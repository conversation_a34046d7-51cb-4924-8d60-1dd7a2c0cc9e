"use strict";
const common_vendor = require("../../../../common/vendor.js");
const VerifySlide = () => "./verifySlider/index.js";
const verifySliderPc = () => "./verifySlider/verifySliderPc.js";
const VerifyPoint = () => "./verifyPoint/verifyPoint.js";
const _sfc_main = {
  name: "Vue2Verify",
  props: {
    captchaType: {
      type: String,
      required: true
    },
    figure: {
      type: Number
    },
    arith: {
      type: Number
    },
    mode: {
      type: String,
      default: "pop"
    },
    vSpace: {
      type: Number,
      default: 5
    },
    explain: {
      type: String,
      default: "向右滑动完成验证"
    },
    imgSize: {
      type: Object,
      default() {
        return {
          width: "310px",
          height: "155px"
        };
      }
    },
    blockSize: {
      type: Object,
      default() {
        return {
          width: "50px",
          height: "50px"
        };
      }
    },
    barSize: {
      type: Object
    }
  },
  data() {
    return {
      // showBox:true,
      clickShow: false,
      // 内部类型
      verifyType: void 0,
      // 所用组件类型
      componentType: void 0,
      defaultImg: ""
    };
  },
  mounted() {
    this.uuid();
  },
  methods: {
    // 生成 uuid
    uuid() {
      var s = [];
      var hexDigits = "0123456789abcdef";
      for (var i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 16), 1);
      }
      s[14] = "4";
      s[19] = hexDigits.substr(s[19] & 3 | 8, 1);
      s[8] = s[13] = s[18] = s[23] = "-";
      var slider = "slider-" + s.join("");
      var point = "point-" + s.join("");
      if (!common_vendor.index.getStorageSync("slider")) {
        common_vendor.index.setStorageSync("slider", slider);
      }
      if (!common_vendor.index.getStorageSync("point")) {
        common_vendor.index.setStorageSync("point", point);
      }
    },
    success(e) {
      this.$emit("success", e);
    },
    /**
     * refresh
     * @description 刷新
     * */
    refresh() {
      if (this.instance.refresh) {
        this.instance.refresh();
      }
    },
    show() {
      if (this.mode == "pop") {
        this.clickShow = true;
      }
    },
    hide() {
      if (this.mode == "pop") {
        this.clickShow = false;
      }
    }
  },
  computed: {
    instance() {
      return this.$refs.instance || {};
    },
    showBox() {
      if (this.mode == "pop") {
        return this.clickShow;
      } else {
        return true;
      }
    }
  },
  watch: {
    captchaType: {
      immediate: true,
      handler(captchaType) {
        switch (captchaType.toString()) {
          case "blockPuzzle":
            this.verifyType = "2";
            this.componentType = "VerifySlide";
            break;
          case "clickWord":
            this.verifyType = "";
            this.componentType = "VerifyPoints";
            break;
        }
      }
    }
  },
  components: {
    VerifySlide,
    VerifyPoint,
    verifySliderPc
  }
};
if (!Array) {
  const _component_VerifySlide = common_vendor.resolveComponent("VerifySlide");
  const _component_VerifyPoint = common_vendor.resolveComponent("VerifyPoint");
  (_component_VerifySlide + _component_VerifyPoint)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.mode == "pop"
  }, $props.mode == "pop" ? {
    b: common_vendor.o(($event) => $data.clickShow = false)
  } : {}, {
    c: $data.componentType == "VerifySlide"
  }, $data.componentType == "VerifySlide" ? {
    d: common_vendor.sr("instance", "80837e18-0"),
    e: common_vendor.o($options.success),
    f: common_vendor.p({
      captchaType: $props.captchaType,
      type: $data.verifyType,
      figure: $props.figure,
      arith: $props.arith,
      mode: $props.mode,
      vSpace: $props.vSpace,
      explain: $props.explain,
      imgSize: $props.imgSize,
      blockSize: $props.blockSize,
      barSize: $props.barSize,
      defaultImg: $data.defaultImg
    })
  } : {}, {
    g: $data.componentType == "VerifyPoints"
  }, $data.componentType == "VerifyPoints" ? {
    h: common_vendor.sr("instance", "80837e18-1"),
    i: common_vendor.o($options.success),
    j: common_vendor.p({
      captchaType: $props.captchaType,
      type: $data.verifyType,
      figure: $props.figure,
      arith: $props.arith,
      mode: $props.mode,
      vSpace: $props.vSpace,
      explain: $props.explain,
      imgSize: $props.imgSize,
      blockSize: $props.blockSize,
      barSize: $props.barSize,
      defaultImg: $data.defaultImg
    })
  } : {}, {
    k: $props.mode == "pop" ? "15px" : "0",
    l: common_vendor.n($props.mode == "pop" ? "verifybox" : ""),
    m: parseInt($props.imgSize.width) + 30 + "px",
    n: common_vendor.n($props.mode == "pop" ? "masks" : ""),
    o: $options.showBox
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages/users/components/verify/index.js.map
