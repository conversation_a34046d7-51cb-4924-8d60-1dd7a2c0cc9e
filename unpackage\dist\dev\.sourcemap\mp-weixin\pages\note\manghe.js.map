{"version": 3, "file": "manghe.js", "sources": ["pages/note/manghe.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbm90ZS9tYW5naGUudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"tree-hole-container\">\r\n    <!-- 背景装饰 -->\r\n    <view class=\"bg-decoration\">\r\n      <view class=\"star star1\"></view>\r\n      <view class=\"star star2\"></view>\r\n      <view class=\"star star3\"></view>\r\n      <view class=\"star star4\"></view>\r\n      <view class=\"star star5\"></view>\r\n    </view>\r\n\r\n    <!-- 顶部导航 -->\r\n    <view class=\"header\">\r\n      <view class=\"back-btn\" @click=\"goBack\">\r\n        <text class=\"iconfont icon-left\"></text>\r\n      </view>\r\n      <view class=\"header-title\">\r\n        <view class=\"title-circle\">\r\n          <text class=\"title-text\">树洞盲盒</text>\r\n        </view>\r\n      </view>\r\n      <view class=\"header-placeholder\"></view>\r\n    </view>\r\n\r\n    <!-- 我的纸条按钮 - 右侧中间 -->\r\n    <view class=\"my-box-floating-btn\" @click=\"goMyBox\">\r\n      <view class=\"floating-btn-icon\">📝</view>\r\n      <text class=\"floating-btn-text\">我的纸条</text>\r\n    </view>\r\n\r\n    <!-- 类型选择器 -->\r\n    <view class=\"type-selector\">\r\n      <!-- 波浪背景 -->\r\n      <view class=\"wave-bg\">\r\n        <view class=\"wave wave1\"></view>\r\n        <view class=\"wave wave2\"></view>\r\n        <view class=\"wave wave3\"></view>\r\n      </view>\r\n\r\n      <view\r\n        class=\"type-item\"\r\n        :class=\"{active: currentType === item.value}\"\r\n        v-for=\"(item, index) in typeList\"\r\n        :key=\"item.value\"\r\n        :style=\"{animationDelay: index * 0.2 + 's'}\"\r\n        @click=\"selectType(item.value)\"\r\n      >\r\n        <view class=\"type-circle\">\r\n          <view class=\"icon-glow\"></view>\r\n          <text class=\"type-icon\">{{item.icon}}</text>\r\n        </view>\r\n        <text class=\"type-name\">{{item.name}}</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 主要盲盒区域 -->\r\n    <view class=\"main-box-area\">\r\n      <!-- 魔法光环 -->\r\n      <view class=\"magic-circle\">\r\n        <view class=\"magic-ring ring1\"></view>\r\n        <view class=\"magic-ring ring2\"></view>\r\n        <view class=\"magic-ring ring3\"></view>\r\n      </view>\r\n\r\n      <!-- 盲盒容器 -->\r\n      <view class=\"box-container\" @click=\"drawBox\">\r\n        <view class=\"box-glow\"></view>\r\n        <view class=\"box-3d\">\r\n          <view class=\"box-face box-front\">\r\n            <view class=\"question-container\">\r\n              <text class=\"box-question\">?</text>\r\n              <view class=\"question-sparkles\">\r\n                <view class=\"sparkle sparkle1\">✨</view>\r\n                <view class=\"sparkle sparkle2\">✨</view>\r\n                <view class=\"sparkle sparkle3\">✨</view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"box-face box-top\"></view>\r\n          <view class=\"box-face box-right\"></view>\r\n        </view>\r\n\r\n        <!-- 增强纸条效果 -->\r\n        <view class=\"papers\">\r\n          <view class=\"paper paper1\">\r\n            <view class=\"paper-content\">💭</view>\r\n          </view>\r\n          <view class=\"paper paper2\">\r\n            <view class=\"paper-content\">🤫</view>\r\n          </view>\r\n          <view class=\"paper paper3\">\r\n            <view class=\"paper-content\">🌟</view>\r\n          </view>\r\n          <view class=\"paper paper4\">\r\n            <view class=\"paper-content\">🎵</view>\r\n          </view>\r\n          <view class=\"paper paper5\">\r\n            <view class=\"paper-content\">💝</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 增强提示文字 -->\r\n      <view class=\"hint-text\">\r\n        <view class=\"hint-decoration left\"></view>\r\n        <text class=\"hint-icon\">💝</text>\r\n        <text class=\"hint-content\">倾听秘密，邂逅缘分</text>\r\n        <text class=\"hint-icon\">💝</text>\r\n        <view class=\"hint-decoration right\"></view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 底部操作按钮 -->\r\n    <view class=\"bottom-actions\">\r\n      <view class=\"action-btn draw-btn\" @click=\"drawBox\">\r\n        <text class=\"btn-text\">抽取纸条</text>\r\n      </view>\r\n      <view class=\"action-btn publish-btn\" @click=\"publishBox\">\r\n        <text class=\"btn-text\">放入纸条</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 底部说明 -->\r\n    <view class=\"bottom-tip\">\r\n      <text class=\"tip-text\">如何投送纸条呢</text>\r\n      <text class=\"tip-icon\">?</text>\r\n    </view>\r\n\r\n    <!-- 纸条详情弹窗 -->\r\n    <view v-if=\"showDetailModal\" class=\"detail-modal\">\r\n      <!-- 背景遮罩 -->\r\n      <view class=\"modal-overlay\" @click=\"closeDetailModal\"></view>\r\n\r\n      <!-- 弹窗内容 -->\r\n      <view class=\"modal-content\">\r\n        <!-- 顶部装饰 -->\r\n        <view class=\"modal-header\">\r\n          <view class=\"paper-icon\">📄</view>\r\n          <view class=\"sparkles\">\r\n            <text class=\"sparkle\">✨</text>\r\n            <text class=\"sparkle\">✨</text>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 标题 -->\r\n        <view class=\"modal-title\">\r\n          <text>抽到纸条啦～</text>\r\n          <view class=\"info-icon\">ⓘ</view>\r\n        </view>\r\n\r\n        <!-- 用户信息卡片 -->\r\n        <view v-if=\"currentBoxData\" class=\"user-card\">\r\n          <view class=\"user-avatar\">\r\n            <image :src=\"currentBoxData.avatar || '/static/default-avatar.png'\" class=\"avatar-image\"></image>\r\n          </view>\r\n          <view class=\"user-info-text\">\r\n            <view class=\"user-name\">{{currentBoxData.nickname || '匿名用户'}}</view>\r\n            <view class=\"user-details\">\r\n              <text class=\"gender\">{{getGenderIcon(currentBoxData.sex)}}</text>\r\n              <text class=\"age-location\">{{currentBoxData.age || '24'}} · {{currentBoxData.location || 'Sub'}}</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"type-badge\" :class=\"'badge-type-' + currentBoxData.type\">\r\n            <text class=\"badge-icon\">{{getTypeIcon(currentBoxData.type)}}</text>\r\n            <text class=\"badge-text\">{{getTypeText(currentBoxData.type)}}</text>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 纸条内容 -->\r\n        <view v-if=\"currentBoxData\" class=\"paper-content\">\r\n          <!-- 文字内容 -->\r\n          <view v-if=\"currentBoxData.type !== 4\" class=\"content-text\">\r\n            {{currentBoxData.content}}\r\n          </view>\r\n\r\n          <!-- 语音内容 -->\r\n          <view v-else class=\"voice-section\">\r\n            <view class=\"voice-player\" @click=\"toggleVoice\">\r\n              <text class=\"voice-icon\">{{isPlaying ? '⏸️' : '▶️'}}</text>\r\n              <text class=\"voice-duration\">{{currentBoxData.voice_duration}}s</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 回应区域 -->\r\n        <view class=\"response-section\">\r\n          <view class=\"response-header\">\r\n            <text class=\"response-count\">共 {{responseCount}} 条回应</text>\r\n          </view>\r\n\r\n          <!-- 回应输入框 -->\r\n          <view class=\"response-input\">\r\n            <textarea\r\n              class=\"input-field\"\r\n              placeholder=\"礼貌和真诚，才能收获对方的好感哦～\"\r\n              maxlength=\"100\"\r\n              v-model=\"responseText\"\r\n            />\r\n            <view class=\"input-counter\">{{responseText.length}}/100</view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 底部按钮 -->\r\n        <view class=\"bottom-actions\">\r\n          <view class=\"action-button secondary\" @click=\"returnBox\">\r\n            <text>放回</text>\r\n          </view>\r\n          <view class=\"action-button primary\" @click=\"sendResponse\">\r\n            <text>回应</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { drawTreeHoleBox, responseTreeHoleBox, returnTreeHoleBox } from '@/api/social.js'\r\nimport { checkLogin, toLogin } from '@/libs/login.js'\r\n\r\nexport default {\r\n  name: 'TreeHoleBox',\r\n  data() {\r\n    return {\r\n      currentType: 0, // 0-随机 1-问题 2-秘密 3-心愿 4-语音\r\n      typeList: [\r\n        { value: 0, name: '随机', icon: '🎲' },\r\n        { value: 1, name: '问题', icon: '❓' },\r\n        { value: 2, name: '秘密', icon: '🤫' },\r\n        { value: 3, name: '心愿', icon: '🌠' },\r\n        { value: 4, name: '语音', icon: '🎵' }\r\n      ],\r\n      showDetailModal: false, // 是否显示详情弹窗\r\n      currentBoxData: null, // 当前抽取的纸条数据\r\n      currentDrawId: null, // 当前抽取记录ID\r\n      isPlaying: false, // 语音播放状态\r\n      responseText: '', // 回应内容\r\n      responseCount: 0 // 回应数量\r\n    }\r\n  },\r\n  methods: {\r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack()\r\n    },\r\n\r\n    // 前往我的盲盒\r\n    goMyBox() {\r\n      uni.navigateTo({\r\n        url: '/pages/note/my-tree-hole'\r\n      })\r\n    },\r\n\r\n    // 选择类型\r\n    selectType(type) {\r\n      this.currentType = type\r\n      // TODO: 根据类型筛选盲盒\r\n    },\r\n\r\n    // 抽取盲盒\r\n    async drawBox() {\r\n      console.log('=== 开始抽取盲盒 ===')\r\n      console.log('当前选择类型:', this.currentType)\r\n\r\n      // 检查登录状态\r\n      if (!this.checkLoginStatus()) {\r\n        return\r\n      }\r\n\r\n      uni.showToast({\r\n        title: '正在为您抽取纸条...',\r\n        icon: 'loading'\r\n      })\r\n\r\n      try {\r\n        // 调用API抽取盲盒\r\n        const requestData = {\r\n          type: this.currentType // 根据当前选择的类型抽取\r\n        }\r\n        console.log('抽取请求参数:', requestData)\r\n\r\n        const result = await drawTreeHoleBox(requestData)\r\n        console.log('抽取API响应:', result)\r\n\r\n        if (result.status === 200 && result.data) {\r\n          console.log('抽取成功，纸条数据:', result.data)\r\n          this.currentBoxData = result.data\r\n          this.currentDrawId = result.data.draw_id || null // 保存抽取记录ID\r\n          this.responseCount = result.data.response_count || 0\r\n\r\n          uni.hideToast()\r\n          uni.showToast({\r\n            title: '抽取成功！',\r\n            icon: 'success'\r\n          })\r\n\r\n          // 显示详情弹窗\r\n          setTimeout(() => {\r\n            this.showDetailModal = true\r\n          }, 1500)\r\n        } else {\r\n          throw new Error(result.msg || '抽取失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('抽取盲盒失败:', error)\r\n        uni.hideToast()\r\n\r\n        // 显示具体错误信息\r\n        uni.showToast({\r\n          title: error.message || '抽取失败，请稍后重试',\r\n          icon: 'none',\r\n          duration: 2000\r\n        })\r\n\r\n        // 如果是网络错误或服务器错误，可以考虑使用模拟数据\r\n        if (error.message && error.message.includes('网络')) {\r\n          console.log('网络错误，使用模拟数据')\r\n          this.showMockData()\r\n        }\r\n      }\r\n    },\r\n\r\n    // 关闭详情弹窗\r\n    closeDetailModal() {\r\n      this.showDetailModal = false\r\n      this.currentBoxData = null\r\n      this.currentDrawId = null\r\n      this.isPlaying = false\r\n      this.responseText = ''\r\n      this.responseCount = 0\r\n    },\r\n\r\n    // 放回纸条\r\n    async returnBox() {\r\n      if (!this.currentDrawId) {\r\n        uni.showToast({\r\n          title: '抽取记录不存在',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      uni.showModal({\r\n        title: '确认放回',\r\n        content: '确定要放回这张纸条吗？放回后将不再显示在您的抽取列表中。',\r\n        success: async (res) => {\r\n          if (res.confirm) {\r\n            try {\r\n              uni.showLoading({\r\n                title: '正在放回...'\r\n              })\r\n\r\n              console.log('放回纸条，draw_id:', this.currentDrawId)\r\n              const result = await returnTreeHoleBox(this.currentDrawId)\r\n\r\n              uni.hideLoading()\r\n\r\n              if (result.status === 200) {\r\n                uni.showToast({\r\n                  title: '已放回纸条',\r\n                  icon: 'success'\r\n                })\r\n\r\n                setTimeout(() => {\r\n                  this.closeDetailModal()\r\n                }, 1500)\r\n              } else {\r\n                throw new Error(result.msg || '放回失败')\r\n              }\r\n            } catch (error) {\r\n              console.error('放回纸条失败:', error)\r\n              uni.hideLoading()\r\n\r\n              uni.showToast({\r\n                title: error.message || '放回失败，请稍后重试',\r\n                icon: 'none'\r\n              })\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 发送回应\r\n    async sendResponse() {\r\n      if (!this.responseText.trim()) {\r\n        uni.showToast({\r\n          title: '请输入回应内容',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      if (this.responseText.length < 5) {\r\n        uni.showToast({\r\n          title: '回应内容至少5个字符',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      if (!this.currentBoxData || !this.currentBoxData.id) {\r\n        uni.showToast({\r\n          title: '纸条信息不存在',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      try {\r\n        uni.showLoading({\r\n          title: '正在发送回应...'\r\n        })\r\n\r\n        const responseData = {\r\n          box_id: this.currentBoxData.id,\r\n          content: this.responseText.trim()\r\n        }\r\n\r\n        // 如果有抽取记录ID，添加到请求中\r\n        if (this.currentDrawId) {\r\n          responseData.draw_id = this.currentDrawId\r\n        }\r\n\r\n        console.log('发送回应，参数:', responseData)\r\n        const result = await responseTreeHoleBox(responseData)\r\n\r\n        uni.hideLoading()\r\n\r\n        if (result.status === 200) {\r\n          uni.showToast({\r\n            title: '回应成功',\r\n            icon: 'success'\r\n          })\r\n\r\n          // 保存box_id，避免在setTimeout中访问已清空的数据\r\n          const boxId = this.currentBoxData.id\r\n\r\n          // 跳转到详情页面\r\n          setTimeout(() => {\r\n            this.closeDetailModal()\r\n            uni.navigateTo({\r\n              url: `/pages/note/detail?id=${boxId}`\r\n            })\r\n          }, 1500)\r\n        } else {\r\n          throw new Error(result.msg || '回应失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('发送回应失败:', error)\r\n        uni.hideLoading()\r\n\r\n        uni.showToast({\r\n          title: error.message || '回应失败，请稍后重试',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 切换语音播放\r\n    toggleVoice() {\r\n      if (!this.currentBoxData || !this.currentBoxData.voice_url) {\r\n        uni.showToast({\r\n          title: '暂无语音内容',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      this.isPlaying = !this.isPlaying\r\n\r\n      if (this.isPlaying) {\r\n        // TODO: 实现语音播放\r\n        uni.showToast({\r\n          title: '开始播放语音',\r\n          icon: 'none'\r\n        })\r\n      } else {\r\n        // TODO: 停止语音播放\r\n        uni.showToast({\r\n          title: '停止播放语音',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 获取性别图标\r\n    getGenderIcon(sex) {\r\n      return sex === 1 ? '♂' : sex === 2 ? '♀' : '⚪'\r\n    },\r\n\r\n    // 获取类型图标\r\n    getTypeIcon(type) {\r\n      const icons = {\r\n        1: '❓',\r\n        2: '🤫',\r\n        3: '🌠',\r\n        4: '🎵'\r\n      }\r\n      return icons[type] || '📝'\r\n    },\r\n\r\n    // 获取类型文本\r\n    getTypeText(type) {\r\n      const texts = {\r\n        1: '问题咨询',\r\n        2: '秘密',\r\n        3: '心愿',\r\n        4: '语音纸条'\r\n      }\r\n      return texts[type] || '纸条'\r\n    },\r\n\r\n\r\n\r\n    // 获取随机内容\r\n    getRandomContent(type) {\r\n      const contents = {\r\n        1: ['有什么好的学习方法吗？', '如何提高工作效率？', '怎样保持健康的生活习惯？'],\r\n        2: ['其实我一直很喜欢你', '我有一个不为人知的梦想', '有些话我一直不敢说出口'],\r\n        3: ['希望能找到真爱', '想要环游世界', '希望家人身体健康'],\r\n        4: ['这是一条语音纸条', '听听我的心声吧', '用声音传递温暖']\r\n      }\r\n      const typeContents = contents[type] || ['这是一张神秘的纸条']\r\n      return typeContents[Math.floor(Math.random() * typeContents.length)]\r\n    },\r\n\r\n    // 获取随机昵称\r\n    getRandomNickname() {\r\n      const adjectives = ['优雅的', '神秘的', '可爱的', '勇敢的', '温柔的', '聪明的']\r\n      const animals = ['小猫', '小狗', '兔子', '熊猫', '狐狸', '小鸟']\r\n      const adj = adjectives[Math.floor(Math.random() * adjectives.length)]\r\n      const animal = animals[Math.floor(Math.random() * animals.length)]\r\n      return adj + animal\r\n    },\r\n\r\n    // 发布盲盒\r\n    publishBox() {\r\n      // 检查登录状态\r\n      if (!this.checkLoginStatus()) {\r\n        return\r\n      }\r\n\r\n      uni.navigateTo({\r\n        url: '/pages/note/publish-tree-hole-simple'\r\n      })\r\n    },\r\n\r\n    // 检查登录状态\r\n    checkLoginStatus() {\r\n      console.log('=== 主页登录状态检查 ===')\r\n\r\n      const checkLoginResult = checkLogin()\r\n      const storeToken = this.$store?.state?.app?.token\r\n      const isLoggedIn = checkLoginResult && storeToken\r\n\r\n      console.log('checkLogin()结果:', checkLoginResult)\r\n      console.log('store token:', storeToken)\r\n      console.log('最终登录状态:', isLoggedIn)\r\n\r\n      if (!isLoggedIn) {\r\n        uni.showModal({\r\n          title: '提示',\r\n          content: '请先登录后再使用此功能',\r\n          confirmText: '去登录',\r\n          cancelText: '取消',\r\n          success: (res) => {\r\n            if (res.confirm) {\r\n              toLogin()\r\n            }\r\n          }\r\n        })\r\n        return false\r\n      }\r\n\r\n      return true\r\n    },\r\n\r\n    // 统一错误处理\r\n    handleError(error, defaultMessage = '操作失败') {\r\n      console.error('错误处理:', error)\r\n\r\n      let message = defaultMessage\r\n\r\n      if (typeof error === 'string') {\r\n        message = error\r\n      } else if (error && typeof error === 'object') {\r\n        if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network')) {\r\n          message = '网络连接异常，请检查网络设置'\r\n        } else if (error.code === 'TIMEOUT' || error.message?.includes('timeout')) {\r\n          message = '请求超时，请稍后重试'\r\n        } else {\r\n          message = error.msg || error.message || error.data?.msg || defaultMessage\r\n        }\r\n      }\r\n\r\n      uni.showToast({\r\n        title: message,\r\n        icon: 'none',\r\n        duration: 2000\r\n      })\r\n\r\n      return message\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tree-hole-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(180deg, #6B46C1 0%, #3B1F8B 50%, #1E0A4F 100%);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 背景装饰星星 */\r\n.bg-decoration {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n  z-index: 1;\r\n}\r\n\r\n.star {\r\n  position: absolute;\r\n  width: 8rpx;\r\n  height: 8rpx;\r\n  background: radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.3) 100%);\r\n  border-radius: 50%;\r\n  animation: twinkle 3s infinite;\r\n  box-shadow: 0 0 20rpx rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.star1 {\r\n  top: 15%; left: 10%;\r\n  animation-delay: 0s;\r\n  background: radial-gradient(circle, rgba(168, 85, 247, 1) 0%, rgba(168, 85, 247, 0.3) 100%);\r\n  box-shadow: 0 0 20rpx rgba(168, 85, 247, 0.5);\r\n}\r\n\r\n.star2 {\r\n  top: 25%; right: 15%;\r\n  animation-delay: 0.8s;\r\n  background: radial-gradient(circle, rgba(236, 72, 153, 1) 0%, rgba(236, 72, 153, 0.3) 100%);\r\n  box-shadow: 0 0 20rpx rgba(236, 72, 153, 0.5);\r\n}\r\n\r\n.star3 {\r\n  top: 55%; left: 20%;\r\n  animation-delay: 1.6s;\r\n  background: radial-gradient(circle, rgba(245, 158, 11, 1) 0%, rgba(245, 158, 11, 0.3) 100%);\r\n  box-shadow: 0 0 20rpx rgba(245, 158, 11, 0.5);\r\n}\r\n\r\n.star4 {\r\n  top: 75%; right: 25%;\r\n  animation-delay: 2.4s;\r\n  background: radial-gradient(circle, rgba(34, 197, 94, 1) 0%, rgba(34, 197, 94, 0.3) 100%);\r\n  box-shadow: 0 0 20rpx rgba(34, 197, 94, 0.5);\r\n}\r\n\r\n.star5 {\r\n  top: 40%; left: 70%;\r\n  animation-delay: 1.2s;\r\n  background: radial-gradient(circle, rgba(59, 130, 246, 1) 0%, rgba(59, 130, 246, 0.3) 100%);\r\n  box-shadow: 0 0 20rpx rgba(59, 130, 246, 0.5);\r\n}\r\n\r\n@keyframes twinkle {\r\n  0%, 100% {\r\n    opacity: 0.4;\r\n    transform: scale(1) rotate(0deg);\r\n  }\r\n  25% {\r\n    opacity: 0.8;\r\n    transform: scale(1.3) rotate(90deg);\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n    transform: scale(1.5) rotate(180deg);\r\n  }\r\n  75% {\r\n    opacity: 0.8;\r\n    transform: scale(1.3) rotate(270deg);\r\n  }\r\n}\r\n\r\n/* 顶部导航 */\r\n.header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 80rpx 40rpx 40rpx;\r\n  position: relative;\r\n  z-index: 10;\r\n}\r\n\r\n.back-btn {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(10rpx);\r\n}\r\n\r\n.back-btn .iconfont {\r\n  color: #fff;\r\n  font-size: 32rpx;\r\n}\r\n\r\n.header-title {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.title-circle {\r\n  background: linear-gradient(135deg, #A855F7 0%, #EC4899 100%);\r\n  border-radius: 50rpx;\r\n  padding: 20rpx 40rpx;\r\n  box-shadow: 0 8rpx 32rpx rgba(168, 85, 247, 0.3);\r\n}\r\n\r\n.title-text {\r\n  color: #fff;\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n.header-placeholder {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n}\r\n\r\n/* 我的纸条浮动按钮 - 右侧中间 */\r\n.my-box-floating-btn {\r\n  position: fixed;\r\n  right: 40rpx;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  background: linear-gradient(135deg, #A855F7 0%, #EC4899 100%);\r\n  border-radius: 60rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow:\r\n    0 8rpx 32rpx rgba(168, 85, 247, 0.4),\r\n    0 0 60rpx rgba(168, 85, 247, 0.2);\r\n  backdrop-filter: blur(10rpx);\r\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\r\n  z-index: 100;\r\n  transition: all 0.3s ease;\r\n  animation: floatingPulse 3s ease-in-out infinite;\r\n}\r\n\r\n.my-box-floating-btn:active {\r\n  transform: translateY(-50%) scale(0.9);\r\n}\r\n\r\n.floating-btn-icon {\r\n  font-size: 32rpx;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.floating-btn-text {\r\n  color: #fff;\r\n  font-size: 20rpx;\r\n  font-weight: 500;\r\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n@keyframes floatingPulse {\r\n  0%, 100% {\r\n    transform: translateY(-50%) scale(1);\r\n    box-shadow:\r\n      0 8rpx 32rpx rgba(168, 85, 247, 0.4),\r\n      0 0 60rpx rgba(168, 85, 247, 0.2);\r\n  }\r\n  50% {\r\n    transform: translateY(-50%) scale(1.05);\r\n    box-shadow:\r\n      0 12rpx 40rpx rgba(168, 85, 247, 0.6),\r\n      0 0 80rpx rgba(168, 85, 247, 0.4);\r\n  }\r\n}\r\n\r\n/* 类型选择器 */\r\n.type-selector {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  padding: 60rpx 40rpx;\r\n  position: relative;\r\n  z-index: 10;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 波浪背景 */\r\n.wave-bg {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 1;\r\n}\r\n\r\n.wave {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: -100%;\r\n  width: 200%;\r\n  height: 200rpx;\r\n  background: linear-gradient(90deg,\r\n    transparent,\r\n    rgba(255, 255, 255, 0.1),\r\n    transparent\r\n  );\r\n  border-radius: 50%;\r\n  animation: waveMove 4s ease-in-out infinite;\r\n}\r\n\r\n.wave1 {\r\n  animation-delay: 0s;\r\n  background: linear-gradient(90deg,\r\n    transparent,\r\n    rgba(168, 85, 247, 0.2),\r\n    transparent\r\n  );\r\n}\r\n\r\n.wave2 {\r\n  animation-delay: 1.3s;\r\n  background: linear-gradient(90deg,\r\n    transparent,\r\n    rgba(236, 72, 153, 0.2),\r\n    transparent\r\n  );\r\n}\r\n\r\n.wave3 {\r\n  animation-delay: 2.6s;\r\n  background: linear-gradient(90deg,\r\n    transparent,\r\n    rgba(245, 158, 11, 0.2),\r\n    transparent\r\n  );\r\n}\r\n\r\n@keyframes waveMove {\r\n  0% {\r\n    left: -100%;\r\n    transform: translateY(-50%) scaleY(0.5);\r\n  }\r\n  50% {\r\n    left: 0%;\r\n    transform: translateY(-50%) scaleY(1);\r\n  }\r\n  100% {\r\n    left: 100%;\r\n    transform: translateY(-50%) scaleY(0.5);\r\n  }\r\n}\r\n\r\n.type-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  transition: all 0.4s ease;\r\n  position: relative;\r\n  z-index: 2;\r\n  animation: typeFloat 3s ease-in-out infinite;\r\n}\r\n\r\n.type-item.active .type-circle {\r\n  background: linear-gradient(135deg, #F59E0B 0%, #EF4444 100%);\r\n  transform: scale(1.2);\r\n  box-shadow:\r\n    0 8rpx 32rpx rgba(245, 158, 11, 0.6),\r\n    0 0 60rpx rgba(245, 158, 11, 0.3);\r\n}\r\n\r\n.type-item.active .icon-glow {\r\n  opacity: 1;\r\n  transform: scale(1.5);\r\n}\r\n\r\n.type-circle {\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 16rpx;\r\n  backdrop-filter: blur(15rpx);\r\n  transition: all 0.4s ease;\r\n  position: relative;\r\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.icon-glow {\r\n  position: absolute;\r\n  top: -10rpx;\r\n  left: -10rpx;\r\n  right: -10rpx;\r\n  bottom: -10rpx;\r\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);\r\n  border-radius: 50%;\r\n  opacity: 0;\r\n  transition: all 0.4s ease;\r\n}\r\n\r\n.type-icon {\r\n  font-size: 36rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.type-name {\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n  opacity: 0.9;\r\n  font-weight: 500;\r\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n@keyframes typeFloat {\r\n  0%, 100% { transform: translateY(0); }\r\n  50% { transform: translateY(-10rpx); }\r\n}\r\n\r\n/* 主要盲盒区域 */\r\n.main-box-area {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 80rpx 40rpx;\r\n  position: relative;\r\n  z-index: 10;\r\n}\r\n\r\n/* 魔法光环 */\r\n.magic-circle {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  z-index: 1;\r\n}\r\n\r\n.magic-ring {\r\n  position: absolute;\r\n  border-radius: 50%;\r\n  border: 2rpx solid rgba(255, 255, 255, 0.2);\r\n  animation: magicRotate 8s linear infinite;\r\n}\r\n\r\n.ring1 {\r\n  width: 400rpx;\r\n  height: 400rpx;\r\n  top: -200rpx;\r\n  left: -200rpx;\r\n  animation-duration: 8s;\r\n}\r\n\r\n.ring2 {\r\n  width: 500rpx;\r\n  height: 500rpx;\r\n  top: -250rpx;\r\n  left: -250rpx;\r\n  animation-duration: 12s;\r\n  animation-direction: reverse;\r\n}\r\n\r\n.ring3 {\r\n  width: 600rpx;\r\n  height: 600rpx;\r\n  top: -300rpx;\r\n  left: -300rpx;\r\n  animation-duration: 16s;\r\n}\r\n\r\n@keyframes magicRotate {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.box-container {\r\n  position: relative;\r\n  margin-bottom: 60rpx;\r\n  z-index: 2;\r\n}\r\n\r\n.box-glow {\r\n  position: absolute;\r\n  top: -50rpx;\r\n  left: -50rpx;\r\n  right: -50rpx;\r\n  bottom: -50rpx;\r\n  background: radial-gradient(circle, rgba(168, 85, 247, 0.3) 0%, transparent 70%);\r\n  border-radius: 50%;\r\n  animation: glowPulse 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes glowPulse {\r\n  0%, 100% { opacity: 0.3; transform: scale(1); }\r\n  50% { opacity: 0.6; transform: scale(1.1); }\r\n}\r\n\r\n/* 3D盲盒效果 */\r\n.box-3d {\r\n  width: 300rpx;\r\n  height: 300rpx;\r\n  position: relative;\r\n  transform-style: preserve-3d;\r\n  animation: float 3s ease-in-out infinite;\r\n  cursor: pointer;\r\n}\r\n\r\n.box-face {\r\n  position: absolute;\r\n  width: 300rpx;\r\n  height: 300rpx;\r\n  background: linear-gradient(135deg, #A855F7 0%, #EC4899 100%);\r\n  border: 4rpx solid rgba(255, 255, 255, 0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.box-front {\r\n  transform: translateZ(150rpx);\r\n  border-radius: 20rpx;\r\n}\r\n\r\n.box-top {\r\n  transform: rotateX(90deg) translateZ(150rpx);\r\n  background: linear-gradient(135deg, #C084FC 0%, #F472B6 100%);\r\n}\r\n\r\n.box-right {\r\n  transform: rotateY(90deg) translateZ(150rpx);\r\n  background: linear-gradient(135deg, #9333EA 0%, #DB2777 100%);\r\n}\r\n\r\n.question-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.box-question {\r\n  color: #fff;\r\n  font-size: 120rpx;\r\n  font-weight: bold;\r\n  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);\r\n  animation: questionPulse 2s ease-in-out infinite;\r\n}\r\n\r\n.question-sparkles {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n}\r\n\r\n.sparkle {\r\n  position: absolute;\r\n  font-size: 24rpx;\r\n  animation: sparkleFloat 3s ease-in-out infinite;\r\n}\r\n\r\n.sparkle1 {\r\n  top: 20%;\r\n  left: 20%;\r\n  animation-delay: 0s;\r\n}\r\n\r\n.sparkle2 {\r\n  top: 30%;\r\n  right: 20%;\r\n  animation-delay: 1s;\r\n}\r\n\r\n.sparkle3 {\r\n  bottom: 20%;\r\n  left: 30%;\r\n  animation-delay: 2s;\r\n}\r\n\r\n@keyframes questionPulse {\r\n  0%, 100% { transform: scale(1); }\r\n  50% { transform: scale(1.05); }\r\n}\r\n\r\n@keyframes sparkleFloat {\r\n  0%, 100% {\r\n    opacity: 0.5;\r\n    transform: translateY(0) rotate(0deg);\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n    transform: translateY(-10rpx) rotate(180deg);\r\n  }\r\n}\r\n\r\n@keyframes float {\r\n  0%, 100% { transform: translateY(0) rotateY(0deg); }\r\n  50% { transform: translateY(-20rpx) rotateY(10deg); }\r\n}\r\n\r\n/* 增强纸条效果 - 完全居中 */\r\n.papers {\r\n  position: absolute;\r\n  top: -60rpx;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 300rpx;\r\n  height: 200rpx;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n}\r\n\r\n.paper {\r\n  width: 50rpx;\r\n  height: 100rpx;\r\n  background: #fff;\r\n  border-radius: 8rpx 8rpx 0 0;\r\n  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.3);\r\n  animation: paperFloat 3s ease-in-out infinite;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 2rpx solid rgba(255, 255, 255, 0.8);\r\n  position: relative;\r\n}\r\n\r\n.paper-content {\r\n  font-size: 20rpx;\r\n  margin-top: 15rpx;\r\n}\r\n\r\n.paper1 {\r\n  animation-delay: 0s;\r\n  background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);\r\n}\r\n\r\n.paper2 {\r\n  animation-delay: 0.4s;\r\n  background: linear-gradient(135deg, #DBEAFE 0%, #93C5FD 100%);\r\n}\r\n\r\n.paper3 {\r\n  animation-delay: 0.8s;\r\n  background: linear-gradient(135deg, #FCE7F3 0%, #F9A8D4 100%);\r\n}\r\n\r\n.paper4 {\r\n  animation-delay: 1.2s;\r\n  background: linear-gradient(135deg, #F3E8FF 0%, #C4B5FD 100%);\r\n}\r\n\r\n.paper5 {\r\n  animation-delay: 1.6s;\r\n  background: linear-gradient(135deg, #ECFDF5 0%, #86EFAC 100%);\r\n}\r\n\r\n@keyframes paperFloat {\r\n  0%, 100% {\r\n    transform: translateY(0) rotate(0deg) scale(1);\r\n    opacity: 0.8;\r\n  }\r\n  25% {\r\n    transform: translateY(-15rpx) rotate(3deg) scale(1.05);\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    transform: translateY(-25rpx) rotate(-2deg) scale(1.1);\r\n    opacity: 0.9;\r\n  }\r\n  75% {\r\n    transform: translateY(-15rpx) rotate(1deg) scale(1.05);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* 为不同位置的纸条添加不同的动画变化 */\r\n.paper1 {\r\n  animation-name: paperFloat1;\r\n}\r\n\r\n.paper2 {\r\n  animation-name: paperFloat2;\r\n}\r\n\r\n.paper3 {\r\n  animation-name: paperFloat3;\r\n}\r\n\r\n.paper4 {\r\n  animation-name: paperFloat4;\r\n}\r\n\r\n.paper5 {\r\n  animation-name: paperFloat5;\r\n}\r\n\r\n@keyframes paperFloat1 {\r\n  0%, 100% { transform: translateY(0) rotate(-2deg) scale(1); opacity: 0.8; }\r\n  50% { transform: translateY(-18rpx) rotate(2deg) scale(1.06); opacity: 1; }\r\n}\r\n\r\n@keyframes paperFloat2 {\r\n  0%, 100% { transform: translateY(0) rotate(1deg) scale(1); opacity: 0.8; }\r\n  50% { transform: translateY(-22rpx) rotate(-3deg) scale(1.08); opacity: 1; }\r\n}\r\n\r\n@keyframes paperFloat3 {\r\n  0%, 100% { transform: translateY(0) rotate(0deg) scale(1); opacity: 0.9; }\r\n  50% { transform: translateY(-25rpx) rotate(0deg) scale(1.1); opacity: 1; }\r\n}\r\n\r\n@keyframes paperFloat4 {\r\n  0%, 100% { transform: translateY(0) rotate(-1deg) scale(1); opacity: 0.8; }\r\n  50% { transform: translateY(-22rpx) rotate(3deg) scale(1.08); opacity: 1; }\r\n}\r\n\r\n@keyframes paperFloat5 {\r\n  0%, 100% { transform: translateY(0) rotate(2deg) scale(1); opacity: 0.8; }\r\n  50% { transform: translateY(-18rpx) rotate(-2deg) scale(1.06); opacity: 1; }\r\n}\r\n\r\n/* 增强提示文字 */\r\n.hint-text {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16rpx;\r\n  position: relative;\r\n  padding: 20rpx 40rpx;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 50rpx;\r\n  backdrop-filter: blur(10rpx);\r\n  border: 1rpx solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.hint-decoration {\r\n  width: 40rpx;\r\n  height: 2rpx;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);\r\n  position: relative;\r\n}\r\n\r\n.hint-decoration.left {\r\n  animation: decorationGlow 3s ease-in-out infinite;\r\n}\r\n\r\n.hint-decoration.right {\r\n  animation: decorationGlow 3s ease-in-out infinite reverse;\r\n}\r\n\r\n@keyframes decorationGlow {\r\n  0%, 100% { opacity: 0.3; transform: scaleX(1); }\r\n  50% { opacity: 1; transform: scaleX(1.5); }\r\n}\r\n\r\n.hint-icon {\r\n  font-size: 32rpx;\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.hint-content {\r\n  color: rgba(255, 255, 255, 0.95);\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\r\n  letter-spacing: 2rpx;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% { transform: scale(1); }\r\n  50% { transform: scale(1.1); }\r\n}\r\n\r\n/* 底部操作按钮 */\r\n.bottom-actions {\r\n  display: flex;\r\n  gap: 40rpx;\r\n  padding: 0 60rpx 40rpx;\r\n  position: relative;\r\n  z-index: 10;\r\n}\r\n\r\n.action-btn {\r\n  flex: 1;\r\n  height: 100rpx;\r\n  border-radius: 50rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: all 0.4s ease;\r\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.action-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);\r\n  transform: translateX(-100%);\r\n  transition: transform 0.6s ease;\r\n}\r\n\r\n.action-btn::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: -2rpx;\r\n  left: -2rpx;\r\n  right: -2rpx;\r\n  bottom: -2rpx;\r\n  background: linear-gradient(45deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7, #DDA0DD);\r\n  background-size: 400% 400%;\r\n  border-radius: 50rpx;\r\n  z-index: -1;\r\n  animation: gradientShift 3s ease infinite;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.action-btn:active::before {\r\n  transform: translateX(100%);\r\n}\r\n\r\n.action-btn:active::after {\r\n  opacity: 1;\r\n}\r\n\r\n.action-btn:active {\r\n  transform: scale(0.95);\r\n}\r\n\r\n@keyframes gradientShift {\r\n  0% { background-position: 0% 50%; }\r\n  50% { background-position: 100% 50%; }\r\n  100% { background-position: 0% 50%; }\r\n}\r\n\r\n.draw-btn {\r\n  background: linear-gradient(135deg, #F59E0B 0%, #EF4444 100%);\r\n  box-shadow:\r\n    0 8rpx 32rpx rgba(245, 158, 11, 0.4),\r\n    0 0 60rpx rgba(245, 158, 11, 0.2);\r\n  animation: btnGlow1 2s ease-in-out infinite;\r\n}\r\n\r\n.publish-btn {\r\n  background: linear-gradient(135deg, #EC4899 0%, #A855F7 100%);\r\n  box-shadow:\r\n    0 8rpx 32rpx rgba(236, 72, 153, 0.4),\r\n    0 0 60rpx rgba(236, 72, 153, 0.2);\r\n  animation: btnGlow2 2s ease-in-out infinite;\r\n  animation-delay: 1s;\r\n}\r\n\r\n@keyframes btnGlow1 {\r\n  0%, 100% {\r\n    box-shadow:\r\n      0 8rpx 32rpx rgba(245, 158, 11, 0.4),\r\n      0 0 60rpx rgba(245, 158, 11, 0.2);\r\n  }\r\n  50% {\r\n    box-shadow:\r\n      0 12rpx 40rpx rgba(245, 158, 11, 0.6),\r\n      0 0 80rpx rgba(245, 158, 11, 0.4);\r\n  }\r\n}\r\n\r\n@keyframes btnGlow2 {\r\n  0%, 100% {\r\n    box-shadow:\r\n      0 8rpx 32rpx rgba(236, 72, 153, 0.4),\r\n      0 0 60rpx rgba(236, 72, 153, 0.2);\r\n  }\r\n  50% {\r\n    box-shadow:\r\n      0 12rpx 40rpx rgba(236, 72, 153, 0.6),\r\n      0 0 80rpx rgba(236, 72, 153, 0.4);\r\n  }\r\n}\r\n\r\n.btn-text {\r\n  color: #fff;\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  position: relative;\r\n  z-index: 1;\r\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\r\n  letter-spacing: 2rpx;\r\n}\r\n\r\n/* 底部说明 */\r\n.bottom-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8rpx;\r\n  padding: 40rpx;\r\n  position: relative;\r\n  z-index: 10;\r\n}\r\n\r\n.tip-text {\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-size: 24rpx;\r\n}\r\n\r\n.tip-icon {\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-size: 24rpx;\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 响应式适配 */\r\n@media (max-width: 750rpx) {\r\n  .type-selector {\r\n    padding: 40rpx 20rpx;\r\n  }\r\n\r\n  .type-circle {\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n  }\r\n\r\n  .type-icon {\r\n    font-size: 28rpx;\r\n  }\r\n\r\n  .box-3d {\r\n    width: 250rpx;\r\n    height: 250rpx;\r\n  }\r\n\r\n  .box-face {\r\n    width: 250rpx;\r\n    height: 250rpx;\r\n  }\r\n\r\n  .box-question {\r\n    font-size: 100rpx;\r\n  }\r\n}\r\n\r\n/* 详情弹窗样式 */\r\n.detail-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.modal-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  backdrop-filter: blur(10rpx);\r\n}\r\n\r\n.modal-content {\r\n  position: relative;\r\n  width: 680rpx;\r\n  max-height: 85vh;\r\n  background: white;\r\n  border-radius: 24rpx;\r\n  padding: 0;\r\n  margin: 0 40rpx;\r\n  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);\r\n  animation: modalSlideIn 0.3s ease-out;\r\n  overflow: hidden;\r\n}\r\n\r\n@keyframes modalSlideIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(100rpx) scale(0.9);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0) scale(1);\r\n  }\r\n}\r\n\r\n.modal-header {\r\n  text-align: center;\r\n  padding: 40rpx 0 20rpx;\r\n  position: relative;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.paper-icon {\r\n  font-size: 80rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.sparkles {\r\n  position: absolute;\r\n  top: 20rpx;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  display: flex;\r\n  gap: 40rpx;\r\n}\r\n\r\n.sparkle {\r\n  font-size: 32rpx;\r\n  animation: sparkle 2s infinite;\r\n  color: white;\r\n}\r\n\r\n.sparkle:nth-child(2) {\r\n  animation-delay: 1s;\r\n}\r\n\r\n@keyframes sparkle {\r\n  0%, 100% { opacity: 0.3; transform: scale(0.8); }\r\n  50% { opacity: 1; transform: scale(1.2); }\r\n}\r\n\r\n.modal-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 20rpx;\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  padding: 30rpx 40rpx 20rpx;\r\n}\r\n\r\n.info-icon {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n  border-radius: 50%;\r\n  background: #ccc;\r\n  color: white;\r\n  font-size: 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 用户卡片 */\r\n.user-card {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 30rpx 40rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.user-avatar {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  margin-right: 20rpx;\r\n  background: #f5f5f5;\r\n}\r\n\r\n.avatar-image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.user-info-text {\r\n  flex: 1;\r\n}\r\n\r\n.user-name {\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n}\r\n\r\n.user-details {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n}\r\n\r\n.gender {\r\n  color: #ff69b4;\r\n}\r\n\r\n.age-location {\r\n  color: #999;\r\n}\r\n\r\n.type-badge {\r\n  padding: 8rpx 16rpx;\r\n  border-radius: 20rpx;\r\n  font-size: 22rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6rpx;\r\n}\r\n\r\n.badge-type-1 {\r\n  background: #e3f2fd;\r\n  color: #1976d2;\r\n}\r\n\r\n.badge-type-2 {\r\n  background: #fce4ec;\r\n  color: #c2185b;\r\n}\r\n\r\n.badge-type-3 {\r\n  background: #f3e5f5;\r\n  color: #7b1fa2;\r\n}\r\n\r\n.badge-type-4 {\r\n  background: #fff3e0;\r\n  color: #f57c00;\r\n}\r\n\r\n/* 纸条内容 */\r\n.paper-content {\r\n  padding: 30rpx 40rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.content-text {\r\n  color: #333;\r\n  font-size: 28rpx;\r\n  line-height: 1.6;\r\n}\r\n\r\n.voice-section {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20rpx 0;\r\n}\r\n\r\n.voice-player {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20rpx;\r\n  padding: 20rpx 40rpx;\r\n  background: #f5f5f5;\r\n  border-radius: 50rpx;\r\n  color: #333;\r\n  font-size: 28rpx;\r\n}\r\n\r\n/* 回应区域 */\r\n.response-section {\r\n  padding: 30rpx 40rpx;\r\n}\r\n\r\n.response-header {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.response-count {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  font-weight: bold;\r\n}\r\n\r\n.response-input {\r\n  position: relative;\r\n  background: #f8f8f8;\r\n  border-radius: 16rpx;\r\n  padding: 20rpx;\r\n}\r\n\r\n.input-field {\r\n  width: 100%;\r\n  min-height: 120rpx;\r\n  font-size: 26rpx;\r\n  color: #333;\r\n  background: transparent;\r\n  border: none;\r\n  outline: none;\r\n  resize: none;\r\n}\r\n\r\n.input-counter {\r\n  position: absolute;\r\n  bottom: 20rpx;\r\n  right: 20rpx;\r\n  font-size: 22rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 底部按钮 */\r\n.bottom-actions {\r\n  display: flex;\r\n  padding: 30rpx 40rpx;\r\n  gap: 20rpx;\r\n}\r\n\r\n.action-button {\r\n  flex: 1;\r\n  padding: 24rpx;\r\n  border-radius: 25rpx;\r\n  text-align: center;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n.action-button.secondary {\r\n  background: #f5f5f5;\r\n  color: #666;\r\n}\r\n\r\n.action-button.primary {\r\n  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);\r\n  color: white;\r\n  box-shadow: 0 8rpx 20rpx rgba(139, 92, 246, 0.3);\r\n}\r\n</style>", "import MiniProgramPage from 'D:/uniapp/vue3/pages/note/manghe.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "drawTreeHoleBox", "returnTreeHoleBox", "responseTreeHoleBox", "checkLogin", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;AA6NA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA;AAAA,MACb,UAAU;AAAA,QACR,EAAE,OAAO,GAAG,MAAM,MAAM,MAAM,KAAM;AAAA,QACpC,EAAE,OAAO,GAAG,MAAM,MAAM,MAAM,IAAK;AAAA,QACnC,EAAE,OAAO,GAAG,MAAM,MAAM,MAAM,KAAM;AAAA,QACpC,EAAE,OAAO,GAAG,MAAM,MAAM,MAAM,KAAM;AAAA,QACpC,EAAE,OAAO,GAAG,MAAM,MAAM,MAAM,KAAK;AAAA,MACpC;AAAA,MACD,iBAAiB;AAAA;AAAA,MACjB,gBAAgB;AAAA;AAAA,MAChB,eAAe;AAAA;AAAA,MACf,WAAW;AAAA;AAAA,MACX,cAAc;AAAA;AAAA,MACd,eAAe;AAAA;AAAA,IACjB;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,UAAU;AACRA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,WAAK,cAAc;AAAA,IAEpB;AAAA;AAAA,IAGD,MAAM,UAAU;AACdA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,gBAAgB;AAC5BA,oBAAY,MAAA,MAAA,OAAA,gCAAA,WAAW,KAAK,WAAW;AAGvC,UAAI,CAAC,KAAK,oBAAoB;AAC5B;AAAA,MACF;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAED,UAAI;AAEF,cAAM,cAAc;AAAA,UAClB,MAAM,KAAK;AAAA;AAAA,QACb;AACAA,sBAAAA,MAAA,MAAA,OAAA,gCAAY,WAAW,WAAW;AAElC,cAAM,SAAS,MAAMC,WAAe,gBAAC,WAAW;AAChDD,sBAAAA,MAAY,MAAA,OAAA,gCAAA,YAAY,MAAM;AAE9B,YAAI,OAAO,WAAW,OAAO,OAAO,MAAM;AACxCA,wBAAA,MAAA,MAAA,OAAA,gCAAY,cAAc,OAAO,IAAI;AACrC,eAAK,iBAAiB,OAAO;AAC7B,eAAK,gBAAgB,OAAO,KAAK,WAAW;AAC5C,eAAK,gBAAgB,OAAO,KAAK,kBAAkB;AAEnDA,wBAAAA,MAAI,UAAU;AACdA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAGD,qBAAW,MAAM;AACf,iBAAK,kBAAkB;AAAA,UACxB,GAAE,IAAI;AAAA,eACF;AACL,gBAAM,IAAI,MAAM,OAAO,OAAO,MAAM;AAAA,QACtC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAGdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AAGD,YAAI,MAAM,WAAW,MAAM,QAAQ,SAAS,IAAI,GAAG;AACjDA,wBAAAA,MAAA,MAAA,OAAA,gCAAY,aAAa;AACzB,eAAK,aAAa;AAAA,QACpB;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,kBAAkB;AACvB,WAAK,iBAAiB;AACtB,WAAK,gBAAgB;AACrB,WAAK,YAAY;AACjB,WAAK,eAAe;AACpB,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,MAAM,YAAY;AAChB,UAAI,CAAC,KAAK,eAAe;AACvBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAI;AACFA,4BAAAA,MAAI,YAAY;AAAA,gBACd,OAAO;AAAA,eACR;AAEDA,4BAAA,MAAA,MAAA,OAAA,gCAAY,iBAAiB,KAAK,aAAa;AAC/C,oBAAM,SAAS,MAAME,6BAAkB,KAAK,aAAa;AAEzDF,4BAAAA,MAAI,YAAY;AAEhB,kBAAI,OAAO,WAAW,KAAK;AACzBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,iBACP;AAED,2BAAW,MAAM;AACf,uBAAK,iBAAiB;AAAA,gBACvB,GAAE,IAAI;AAAA,qBACF;AACL,sBAAM,IAAI,MAAM,OAAO,OAAO,MAAM;AAAA,cACtC;AAAA,YACA,SAAO,OAAO;AACdA,4BAAAA,MAAc,MAAA,SAAA,gCAAA,WAAW,KAAK;AAC9BA,4BAAAA,MAAI,YAAY;AAEhBA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO,MAAM,WAAW;AAAA,gBACxB,MAAM;AAAA,eACP;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,eAAe;AACnB,UAAI,CAAC,KAAK,aAAa,QAAQ;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI,KAAK,aAAa,SAAS,GAAG;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,kBAAkB,CAAC,KAAK,eAAe,IAAI;AACnDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,SACR;AAED,cAAM,eAAe;AAAA,UACnB,QAAQ,KAAK,eAAe;AAAA,UAC5B,SAAS,KAAK,aAAa,KAAK;AAAA,QAClC;AAGA,YAAI,KAAK,eAAe;AACtB,uBAAa,UAAU,KAAK;AAAA,QAC9B;AAEAA,sBAAAA,MAAY,MAAA,OAAA,gCAAA,YAAY,YAAY;AACpC,cAAM,SAAS,MAAMG,WAAmB,oBAAC,YAAY;AAErDH,sBAAAA,MAAI,YAAY;AAEhB,YAAI,OAAO,WAAW,KAAK;AACzBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAGD,gBAAM,QAAQ,KAAK,eAAe;AAGlC,qBAAW,MAAM;AACf,iBAAK,iBAAiB;AACtBA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK,yBAAyB,KAAK;AAAA,aACpC;AAAA,UACF,GAAE,IAAI;AAAA,eACF;AACL,gBAAM,IAAI,MAAM,OAAO,OAAO,MAAM;AAAA,QACtC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,YAAY;AAEhBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACZ,UAAI,CAAC,KAAK,kBAAkB,CAAC,KAAK,eAAe,WAAW;AAC1DA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,WAAK,YAAY,CAAC,KAAK;AAEvB,UAAI,KAAK,WAAW;AAElBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,aACI;AAELA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,KAAK;AACjB,aAAO,QAAQ,IAAI,MAAM,QAAQ,IAAI,MAAM;AAAA,IAC5C;AAAA;AAAA,IAGD,YAAY,MAAM;AAChB,YAAM,QAAQ;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,aAAO,MAAM,IAAI,KAAK;AAAA,IACvB;AAAA;AAAA,IAGD,YAAY,MAAM;AAChB,YAAM,QAAQ;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,aAAO,MAAM,IAAI,KAAK;AAAA,IACvB;AAAA;AAAA,IAKD,iBAAiB,MAAM;AACrB,YAAM,WAAW;AAAA,QACf,GAAG,CAAC,eAAe,aAAa,cAAc;AAAA,QAC9C,GAAG,CAAC,aAAa,eAAe,aAAa;AAAA,QAC7C,GAAG,CAAC,WAAW,UAAU,UAAU;AAAA,QACnC,GAAG,CAAC,YAAY,WAAW,SAAS;AAAA,MACtC;AACA,YAAM,eAAe,SAAS,IAAI,KAAK,CAAC,WAAW;AACnD,aAAO,aAAa,KAAK,MAAM,KAAK,WAAW,aAAa,MAAM,CAAC;AAAA,IACpE;AAAA;AAAA,IAGD,oBAAoB;AAClB,YAAM,aAAa,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAC5D,YAAM,UAAU,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AACnD,YAAM,MAAM,WAAW,KAAK,MAAM,KAAK,OAAS,IAAE,WAAW,MAAM,CAAC;AACpE,YAAM,SAAS,QAAQ,KAAK,MAAM,KAAK,OAAS,IAAE,QAAQ,MAAM,CAAC;AACjE,aAAO,MAAM;AAAA,IACd;AAAA;AAAA,IAGD,aAAa;AAEX,UAAI,CAAC,KAAK,oBAAoB;AAC5B;AAAA,MACF;AAEAA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB;;AACjBA,oBAAAA,mDAAY,kBAAkB;AAE9B,YAAM,mBAAmBI,WAAAA,WAAW;AACpC,YAAM,cAAa,sBAAK,WAAL,mBAAa,UAAb,mBAAoB,QAApB,mBAAyB;AAC5C,YAAM,aAAa,oBAAoB;AAEvCJ,oBAAAA,MAAA,MAAA,OAAA,gCAAY,mBAAmB,gBAAgB;AAC/CA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,gBAAgB,UAAU;AACtCA,oBAAAA,MAAA,MAAA,OAAA,gCAAY,WAAW,UAAU;AAEjC,UAAI,CAAC,YAAY;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACfK,iCAAQ;AAAA,YACV;AAAA,UACF;AAAA,SACD;AACD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,YAAY,OAAO,iBAAiB,QAAQ;;AAC1CL,oBAAAA,MAAc,MAAA,SAAA,gCAAA,SAAS,KAAK;AAE5B,UAAI,UAAU;AAEd,UAAI,OAAO,UAAU,UAAU;AAC7B,kBAAU;AAAA,MACZ,WAAW,SAAS,OAAO,UAAU,UAAU;AAC7C,YAAI,MAAM,SAAS,qBAAmB,WAAM,YAAN,mBAAe,SAAS,aAAY;AACxE,oBAAU;AAAA,QACZ,WAAW,MAAM,SAAS,eAAa,WAAM,YAAN,mBAAe,SAAS,aAAY;AACzE,oBAAU;AAAA,eACL;AACL,oBAAU,MAAM,OAAO,MAAM,aAAW,WAAM,SAAN,mBAAY,QAAO;AAAA,QAC7D;AAAA,MACF;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,OACX;AAED,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7lBA,GAAG,WAAW,eAAe;"}