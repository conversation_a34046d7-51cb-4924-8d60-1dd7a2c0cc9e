"use strict";
const common_vendor = require("../../../common/vendor.js");
const libs_login = require("../../../libs/login.js");
const api_user = require("../../../api/user.js");
const api_api = require("../../../api/api.js");
const mixins_color = require("../../../mixins/color.js");
const config_app = require("../../../config/app.js");
const common_assets = require("../../../common/assets.js");
const authorize = () => "../../../components/Authorize.js";
const { HTTP_REQUEST_URL } = config_app.config;
const _sfc_main = {
  components: {
    authorize
  },
  mixins: [mixins_color.colors],
  data() {
    return {
      active: false,
      userInfo: {},
      signCount: [],
      signSystemList: [],
      signList: [],
      integral: 0,
      isAuto: false,
      //没有授权的不会自动授权
      isShowAuth: false,
      //是否隐藏授权
      sign_index: 0,
      picUrl: [],
      imgHost: HTTP_REQUEST_URL,
      sginBg: "",
      sginTip: "",
      signMode: 0,
      // 0月签到 1周签到
      nextContinuousDays: 0,
      nextCumulativeDays: 0,
      continuousSignDays: 0,
      signRemindSwitch: 0,
      checkSign: 0,
      remindStatus: false,
      weekArr: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
    };
  },
  computed: common_vendor.mapGetters(["isLogin"]),
  watch: {
    isLogin: {
      handler: function(newV, oldV) {
        if (newV) {
          this.getUserInfo();
          this.getSignSysteam();
          this.getSignList();
        }
      },
      deep: true
    }
  },
  onLoad() {
    if (this.isLogin) {
      this.getColor();
      this.getUserInfo();
      this.getSignSysteam();
      this.getSignList();
    } else {
      libs_login.toLogin();
    }
  },
  methods: {
    /**
     * 授权回调
     */
    onLoadFun: function() {
      this.getUserInfo();
      this.getSignSysteam();
      this.getSignList();
    },
    // 授权关闭
    authColse: function(e) {
      this.isShowAuth = e;
    },
    getColor() {
      api_api.colorChange("color_change").then((res) => {
        this.sginBg = `${this.imgHost}/statics/images/sgin_bg_${res.data.status}.png`;
        this.sginTip = `${this.imgHost}/statics/images/sgin_tip_${res.data.status}.png`;
        let theme = ["#1db0fc", "#42CA4D", "#e93323", "#ff448f", "#FE5C2D"];
        common_vendor.index.setNavigationBarColor({
          frontColor: "#ffffff",
          // 必写项
          backgroundColor: theme[res.data.status - 1]
          // 必写项
        });
      });
    },
    /**
     * 获取签到配置
     */
    getSignSysteam: function() {
      let that = this;
      api_user.getSignConfig().then((res) => {
        if (!res.data.signStatus) {
          return that.$util.Tips({
            title: that.$t(`签到功能已关闭`)
          }, {
            tab: 3
          });
        }
        that.$set(that, "signSystemList", res.data.signList);
        that.signMode = res.data.signMode;
        that.nextContinuousDays = res.data.nextContinuousDays;
        that.nextCumulativeDays = res.data.nextCumulativeDays;
        that.continuousSignDays = res.data.continuousSignDays;
        that.signRemindSwitch = res.data.signRemindSwitch;
        that.checkSign = res.data.checkSign;
        that.remindStatus = !!res.data.signRemindStatus;
        that.signCount = that.PrefixInteger(res.data.cumulativeSignDays, 4);
      });
    },
    changeRemind(e) {
      let status = e.detail.value ? 1 : 0;
      api_user.changeRemindStatus(status).then((res) => {
        common_vendor.index.__f__("log", "at pages/users/user_sgin/index.vue:241", res);
      });
    },
    getTypeImg(type, isSgin) {
      let src;
      if (isSgin) {
        src = `${this.imgHost}/statics/images/sgin_suc_1.png`;
        return src;
      }
      switch (type) {
        case 1:
          src = `${this.imgHost}/statics/images/sgin_icon_1.png`;
          break;
        case 2:
          src = `${this.imgHost}/statics/images/sgin_icon_2.png`;
          break;
        case 3:
          src = `${this.imgHost}/statics/images/sgin_icon_3.png`;
          break;
        case 4:
          src = `${this.imgHost}/statics/images/sgin_icon_3.png`;
          break;
      }
      return src;
    },
    /**
     * 去签到记录页面
     *
     */
    goSignList: function() {
      return this.$util.Tips("/pages/users/user_sgin_list/index");
    },
    /**
     * 获取用户信息
     */
    getUserInfo: function() {
      api_user.postSignUser({
        sign: 1
      }).then((res) => {
      });
    },
    /**
     * 获取签到列表
     *
     */
    getSignList: function() {
      let that = this;
      api_user.getSignList({
        page: 1,
        limit: 8
      }).then((res) => {
        that.$set(that, "signList", res.data);
      });
    },
    /**
     * 数字转中文
     *
     */
    Rp: function(n) {
      let cnum = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
      let s = "";
      n = "" + n;
      for (let i = 0; i < n.length; i++) {
        s += cnum[parseInt(n.charAt(i))];
      }
      return s;
    },
    /**
     * 数字分割为数组
     * @param int num 需要分割的数字
     * @param int length 需要分割为n位数组
     */
    PrefixInteger: function(num, length) {
      return (Array(length).join("0") + num).slice(-length).split("");
    },
    /**
     * 用户签到
     */
    goSign: function(e) {
      let that = this;
      that.userInfo.sum_sgin_day;
      if (that.userInfo.is_day_sgin)
        return this.$util.Tips({
          title: that.$t(`您今日已签到!`)
        });
      api_user.setSignIntegral().then((res) => {
        that.active = true;
        that.integral = res.data.integral;
        that.getSignSysteam();
        that.getSignList();
      }).catch((err) => {
        return this.$util.Tips({
          title: err
        });
      });
    },
    /**
     * 关闭签到提示
     */
    close: function() {
      this.active = false;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.sginTip,
    b: common_vendor.t(_ctx.$t(`明细`)),
    c: common_vendor.s("background-image: url(" + $data.sginBg + ");"),
    d: common_vendor.t($data.continuousSignDays),
    e: $data.signRemindSwitch == 1
  }, $data.signRemindSwitch == 1 ? {
    f: $data.remindStatus,
    g: common_vendor.o((...args) => $options.changeRemind && $options.changeRemind(...args))
  } : {}, {
    h: $data.signMode == 0 || $data.signMode == -1
  }, $data.signMode == 0 || $data.signMode == -1 ? {
    i: common_vendor.f($data.signSystemList, (item, index, i0) => {
      return {
        a: common_vendor.f(item, (e, i, i1) => {
          return common_vendor.e({
            a: !e.is_sign
          }, !e.is_sign ? {
            b: $options.getTypeImg(e.type, e.is_sign)
          } : {}, {
            c: common_vendor.t(e.day),
            d: e.sign_day ? 1 : "",
            e: e.is_sign ? 1 : "",
            f: i
          });
        }),
        b: index
      };
    })
  } : {
    j: common_vendor.f($data.signSystemList, (item, index, i0) => {
      return {
        a: common_vendor.f(item, (e, i, i1) => {
          return common_vendor.e({
            a: common_vendor.t(e.point),
            b: !e.is_sign
          }, !e.is_sign ? {
            c: $options.getTypeImg(e.type, e.is_sign)
          } : {}, {
            d: common_vendor.t($data.weekArr[i]),
            e: e.sign_day ? 1 : "",
            f: e.is_sign ? 1 : "",
            g: i
          });
        }),
        b: index
      };
    })
  }, {
    k: $data.checkSign
  }, $data.checkSign ? {
    l: common_vendor.t(_ctx.$t(`今日已签到，明日再来吧`))
  } : {
    m: common_vendor.t(_ctx.$t(`立即签到`)),
    n: common_vendor.o((...args) => $options.goSign && $options.goSign(...args))
  }, {
    o: $data.nextContinuousDays > 0
  }, $data.nextContinuousDays > 0 ? {
    p: `${$data.imgHost}/statics/images/sgin_icon_4.png`,
    q: common_vendor.t($data.nextContinuousDays)
  } : {}, {
    r: common_vendor.t(_ctx.$t(`已累计签到`)),
    s: common_vendor.t($data.signCount[0] || 0),
    t: common_vendor.t($data.signCount[1] || 0),
    v: common_vendor.t($data.signCount[2] || 0),
    w: common_vendor.t($data.signCount[3] || 0),
    x: common_vendor.t(_ctx.$t(`天`)),
    y: $data.nextCumulativeDays > 0
  }, $data.nextCumulativeDays > 0 ? {
    z: `${$data.imgHost}/statics/images/sgin_icon_4.png`,
    A: common_vendor.t(_ctx.$t(`再累计签到`)),
    B: common_vendor.t($data.nextCumulativeDays),
    C: common_vendor.t(_ctx.$t(`天，可额外获得惊喜礼包`))
  } : {}, {
    D: $data.signList.length
  }, $data.signList.length ? common_vendor.e({
    E: common_vendor.f($data.signList, (item, index, i0) => {
      return {
        a: common_vendor.t(_ctx.$t(item.title)),
        b: common_vendor.t(item.add_time),
        c: common_vendor.t(item.number),
        d: index
      };
    }),
    F: $data.signList.length >= 8
  }, $data.signList.length >= 8 ? {
    G: common_vendor.t(_ctx.$t(`点击加载更多`)),
    H: common_vendor.o((...args) => $options.goSignList && $options.goSignList(...args))
  } : {}) : {}, {
    I: common_assets._imports_0$12,
    J: common_vendor.t(_ctx.$t(`签到成功`)),
    K: common_vendor.t(_ctx.$t(`获得`)),
    L: common_vendor.t($data.integral),
    M: common_vendor.t(_ctx.$t(`积分`)),
    N: common_vendor.t(_ctx.$t(`好的`)),
    O: common_vendor.o((...args) => $options.close && $options.close(...args)),
    P: common_vendor.n($data.active == true ? "on" : ""),
    Q: common_vendor.o(($event) => false),
    R: common_vendor.s(_ctx.colorStyle),
    S: common_vendor.s(_ctx.colorStyle)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-72e91ee0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/users/user_sgin/index.js.map
