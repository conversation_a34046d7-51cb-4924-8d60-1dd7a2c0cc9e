"use strict";
const common_vendor = require("../common/vendor.js");
const useSocialStore = common_vendor.defineStore("social", {
  state: () => ({
    // 简化的动态缓存（只缓存关键数据）
    currentDynamicList: [],
    currentTab: 0,
    // 动态数据缓存
    dynamicCache: {
      personal: {
        notes: { data: [], timestamp: 0, totalCount: 0 },
        likes: { data: [], timestamp: 0, totalCount: 0 }
      },
      home: { data: [], timestamp: 0, totalCount: 0 },
      following: { data: [], timestamp: 0, totalCount: 0 }
    },
    // 发布状态
    publishStatus: {
      isPublishing: false,
      progress: 0,
      error: null
    },
    // 点赞状态缓存（使用Map但不持久化）
    likeStatusCache: /* @__PURE__ */ new Map(),
    // 缓存配置
    cacheConfig: {
      timeout: 3e5,
      // 5分钟
      maxItems: 20
      // 减少缓存数量
    }
  }),
  getters: {
    // 获取个人笔记缓存
    getPersonalNotes: (state) => state.dynamicCache.personal.notes.data,
    // 获取个人点赞缓存
    getPersonalLikes: (state) => state.dynamicCache.personal.likes.data,
    // 获取首页动态缓存
    getHomeDynamics: (state) => state.dynamicCache.home.data,
    // 检查缓存是否有效
    isCacheValid: (state) => (cacheType, subType = null) => {
      var _a;
      const now = Date.now();
      let cache;
      if (subType) {
        cache = (_a = state.dynamicCache[cacheType]) == null ? void 0 : _a[subType];
      } else {
        cache = state.dynamicCache[cacheType];
      }
      return cache && cache.timestamp && now - cache.timestamp < state.cacheConfig.timeout && cache.data.length > 0;
    },
    // 获取热门话题
    getHotTopics: (state) => state.topics.hot,
    // 获取已加入圈子
    getJoinedCircles: (state) => state.circles.joined,
    // 获取关注用户
    getFollowUsers: (state) => state.followUsers,
    // 检查动态是否已点赞
    isLiked: (state) => (dynamicId) => {
      return state.likeStatusCache.get(dynamicId) || false;
    },
    // 是否正在发布
    isPublishing: (state) => state.publishStatus.isPublishing
  },
  actions: {
    // 设置动态缓存
    setDynamicCache(type, subType, data, totalCount = 0) {
      const now = Date.now();
      if (subType) {
        if (!this.dynamicCache[type]) {
          this.dynamicCache[type] = {};
        }
        this.dynamicCache[type][subType] = {
          data: data.slice(0, this.cacheConfig.maxItems),
          timestamp: now,
          totalCount
        };
      } else {
        this.dynamicCache[type] = {
          data: data.slice(0, this.cacheConfig.maxItems),
          timestamp: now,
          totalCount
        };
      }
    },
    // 获取动态缓存
    getDynamicCache(type, subType = null) {
      var _a;
      if (subType) {
        return ((_a = this.dynamicCache[type]) == null ? void 0 : _a[subType]) || { data: [], timestamp: 0, totalCount: 0 };
      } else {
        return this.dynamicCache[type] || { data: [], timestamp: 0, totalCount: 0 };
      }
    },
    // 清除动态缓存
    clearDynamicCache(type = null, subType = null) {
      if (type && subType) {
        if (this.dynamicCache[type]) {
          this.dynamicCache[type][subType] = { data: [], timestamp: 0, totalCount: 0 };
        }
      } else if (type) {
        this.dynamicCache[type] = { data: [], timestamp: 0, totalCount: 0 };
      } else {
        this.dynamicCache = {
          personal: {
            notes: { data: [], timestamp: 0, totalCount: 0 },
            likes: { data: [], timestamp: 0, totalCount: 0 }
          },
          home: { data: [], timestamp: 0, totalCount: 0 },
          following: { data: [], timestamp: 0, totalCount: 0 }
        };
      }
    },
    // 更新动态数据（添加新动态到缓存）
    addDynamicToCache(type, subType, dynamic) {
      const cache = this.getDynamicCache(type, subType);
      const newData = [dynamic, ...cache.data];
      this.setDynamicCache(type, subType, newData, cache.totalCount + 1);
    },
    // 从缓存中删除动态
    removeDynamicFromCache(type, subType, dynamicId) {
      const cache = this.getDynamicCache(type, subType);
      const newData = cache.data.filter((item) => item.id !== dynamicId);
      this.setDynamicCache(type, subType, newData, Math.max(0, cache.totalCount - 1));
    },
    // 设置话题数据
    setTopics(type, topics) {
      this.topics[type] = topics;
    },
    // 设置圈子数据
    setCircles(type, circles) {
      this.circles[type] = circles;
    },
    // 设置关注用户列表
    setFollowUsers(users) {
      this.followUsers = users;
    },
    // 设置@用户列表
    setMentionUsers(users) {
      this.mentionUsers = users;
    },
    // 设置评论缓存
    setCommentCache(dynamicId, comments) {
      this.commentCache.set(dynamicId, {
        data: comments,
        timestamp: Date.now()
      });
    },
    // 获取评论缓存
    getCommentCache(dynamicId) {
      const cache = this.commentCache.get(dynamicId);
      if (!cache)
        return null;
      const now = Date.now();
      if (now - cache.timestamp > this.cacheConfig.timeout) {
        this.commentCache.delete(dynamicId);
        return null;
      }
      return cache.data;
    },
    // 设置点赞状态
    setLikeStatus(dynamicId, isLiked) {
      this.likeStatusCache.set(dynamicId, isLiked);
    },
    // 批量设置点赞状态
    setBatchLikeStatus(likeStatusMap) {
      for (const [dynamicId, isLiked] of Object.entries(likeStatusMap)) {
        this.likeStatusCache.set(dynamicId, isLiked);
      }
    },
    // 设置发布状态
    setPublishStatus(status) {
      this.publishStatus = { ...this.publishStatus, ...status };
    },
    // 开始发布
    startPublish() {
      this.publishStatus = {
        isPublishing: true,
        progress: 0,
        error: null
      };
    },
    // 更新发布进度
    updatePublishProgress(progress) {
      this.publishStatus.progress = progress;
    },
    // 发布成功
    publishSuccess() {
      this.publishStatus = {
        isPublishing: false,
        progress: 100,
        error: null
      };
    },
    // 发布失败
    publishError(error) {
      this.publishStatus = {
        isPublishing: false,
        progress: 0,
        error
      };
    },
    // 清理过期缓存
    cleanExpiredCache() {
      const now = Date.now();
      for (const [key, cache] of this.commentCache.entries()) {
        if (now - cache.timestamp > this.cacheConfig.timeout) {
          this.commentCache.delete(key);
        }
      }
      Object.keys(this.dynamicCache).forEach((type) => {
        const cache = this.dynamicCache[type];
        if (cache.timestamp && now - cache.timestamp > this.cacheConfig.timeout) {
          if (typeof cache === "object" && cache.data) {
            this.dynamicCache[type] = { data: [], timestamp: 0, totalCount: 0 };
          } else {
            Object.keys(cache).forEach((subType) => {
              if (cache[subType].timestamp && now - cache[subType].timestamp > this.cacheConfig.timeout) {
                cache[subType] = { data: [], timestamp: 0, totalCount: 0 };
              }
            });
          }
        }
      });
    },
    // 重置社交状态
    reset() {
      this.clearDynamicCache();
      this.topics = {
        hot: [],
        recent: [],
        followed: []
      };
      this.circles = {
        joined: [],
        recommended: []
      };
      this.followUsers = [];
      this.mentionUsers = [];
      this.commentCache.clear();
      this.likeStatusCache.clear();
      this.publishStatus = {
        isPublishing: false,
        progress: 0,
        error: null
      };
    }
  }
});
exports.useSocialStore = useSocialStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/stores/social.js.map
