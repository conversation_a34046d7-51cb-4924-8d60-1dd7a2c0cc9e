{"version": 3, "file": "indexData.js", "sources": ["store/modules/indexData.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nexport default {\n\tnamespaced: true,\n\tstate: {\n\t\t// 搜索关键字\n\t\tindexDatas: {},\n\t\tcartNum: 0\n\t},\n\tgetters: {},\n\tmutations: {\n\t\tsetIndexData(state, data) {\n\t\t\tstate.indexDatas = data;\n\t\t},\n\t\tsetCartNum(state, data) {\n\t\t\tstate.cartNum = data;\n\t\t}\n\t}\n}\n"], "names": [], "mappings": ";AAUA,MAAe,YAAA;AAAA,EACd,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,IAEN,YAAY,CAAE;AAAA,IACd,SAAS;AAAA,EACT;AAAA,EACD,SAAS,CAAE;AAAA,EACX,WAAW;AAAA,IACV,aAAa,OAAO,MAAM;AACzB,YAAM,aAAa;AAAA,IACnB;AAAA,IACD,WAAW,OAAO,MAAM;AACvB,YAAM,UAAU;AAAA,IAChB;AAAA,EACD;AACF;;"}