.tree-hole-container[data-v-792157c3] {
  min-height: 100vh;
  background: linear-gradient(180deg, #6B46C1 0%, #3B1F8B 50%, #1E0A4F 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰星星 */
.bg-decoration[data-v-792157c3] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}
.star[data-v-792157c3] {
  position: absolute;
  width: 0.25rem;
  height: 0.25rem;
  background: radial-gradient(circle, white 0%, rgba(255, 255, 255, 0.3) 100%);
  border-radius: 50%;
  animation: twinkle-792157c3 3s infinite;
  box-shadow: 0 0 0.625rem rgba(255, 255, 255, 0.5);
}
.star1[data-v-792157c3] {
  top: 15%;
  left: 10%;
  animation-delay: 0s;
  background: radial-gradient(circle, #a855f7 0%, rgba(168, 85, 247, 0.3) 100%);
  box-shadow: 0 0 0.625rem rgba(168, 85, 247, 0.5);
}
.star2[data-v-792157c3] {
  top: 25%;
  right: 15%;
  animation-delay: 0.8s;
  background: radial-gradient(circle, #ec4899 0%, rgba(236, 72, 153, 0.3) 100%);
  box-shadow: 0 0 0.625rem rgba(236, 72, 153, 0.5);
}
.star3[data-v-792157c3] {
  top: 55%;
  left: 20%;
  animation-delay: 1.6s;
  background: radial-gradient(circle, #f59e0b 0%, rgba(245, 158, 11, 0.3) 100%);
  box-shadow: 0 0 0.625rem rgba(245, 158, 11, 0.5);
}
.star4[data-v-792157c3] {
  top: 75%;
  right: 25%;
  animation-delay: 2.4s;
  background: radial-gradient(circle, #22c55e 0%, rgba(34, 197, 94, 0.3) 100%);
  box-shadow: 0 0 0.625rem rgba(34, 197, 94, 0.5);
}
.star5[data-v-792157c3] {
  top: 40%;
  left: 70%;
  animation-delay: 1.2s;
  background: radial-gradient(circle, #3b82f6 0%, rgba(59, 130, 246, 0.3) 100%);
  box-shadow: 0 0 0.625rem rgba(59, 130, 246, 0.5);
}
@keyframes twinkle-792157c3 {
0%, 100% {
    opacity: 0.4;
    transform: scale(1) rotate(0deg);
}
25% {
    opacity: 0.8;
    transform: scale(1.3) rotate(90deg);
}
50% {
    opacity: 1;
    transform: scale(1.5) rotate(180deg);
}
75% {
    opacity: 0.8;
    transform: scale(1.3) rotate(270deg);
}
}
/* 顶部导航 */
.header[data-v-792157c3] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2.5rem 1.25rem 1.25rem;
  position: relative;
  z-index: 10;
}
.back-btn[data-v-792157c3] {
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(0.3125rem);
          backdrop-filter: blur(0.3125rem);
}
.back-btn .iconfont[data-v-792157c3] {
  color: #fff;
  font-size: 1rem;
}
.header-title[data-v-792157c3] {
  flex: 1;
  display: flex;
  justify-content: center;
}
.title-circle[data-v-792157c3] {
  background: linear-gradient(135deg, #A855F7 0%, #EC4899 100%);
  border-radius: 1.5625rem;
  padding: 0.625rem 1.25rem;
  box-shadow: 0 0.25rem 1rem rgba(168, 85, 247, 0.3);
}
.title-text[data-v-792157c3] {
  color: #fff;
  font-size: 1rem;
  font-weight: bold;
}
.header-placeholder[data-v-792157c3] {
  width: 2.5rem;
  height: 2.5rem;
}

/* 我的纸条浮动按钮 - 右侧中间 */
.my-box-floating-btn[data-v-792157c3] {
  position: fixed;
  right: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  width: 3.75rem;
  height: 3.75rem;
  background: linear-gradient(135deg, #A855F7 0%, #EC4899 100%);
  border-radius: 1.875rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0.25rem 1rem rgba(168, 85, 247, 0.4), 0 0 1.875rem rgba(168, 85, 247, 0.2);
  -webkit-backdrop-filter: blur(0.3125rem);
          backdrop-filter: blur(0.3125rem);
  border: 0.0625rem solid rgba(255, 255, 255, 0.3);
  z-index: 100;
  transition: all 0.3s ease;
  animation: floatingPulse-792157c3 3s ease-in-out infinite;
}
.my-box-floating-btn[data-v-792157c3]:active {
  transform: translateY(-50%) scale(0.9);
}
.floating-btn-icon[data-v-792157c3] {
  font-size: 1rem;
  margin-bottom: 0.25rem;
}
.floating-btn-text[data-v-792157c3] {
  color: #fff;
  font-size: 0.625rem;
  font-weight: 500;
  text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.3);
}
@keyframes floatingPulse-792157c3 {
0%, 100% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0.25rem 1rem rgba(168, 85, 247, 0.4), 0 0 1.875rem rgba(168, 85, 247, 0.2);
}
50% {
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 0.375rem 1.25rem rgba(168, 85, 247, 0.6), 0 0 2.5rem rgba(168, 85, 247, 0.4);
}
}
/* 类型选择器 */
.type-selector[data-v-792157c3] {
  display: flex;
  justify-content: space-around;
  padding: 1.875rem 1.25rem;
  position: relative;
  z-index: 10;
  overflow: hidden;
}

/* 波浪背景 */
.wave-bg[data-v-792157c3] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.wave[data-v-792157c3] {
  position: absolute;
  top: 50%;
  left: -100%;
  width: 200%;
  height: 6.25rem;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 50%;
  animation: waveMove-792157c3 4s ease-in-out infinite;
}
.wave1[data-v-792157c3] {
  animation-delay: 0s;
  background: linear-gradient(90deg, transparent, rgba(168, 85, 247, 0.2), transparent);
}
.wave2[data-v-792157c3] {
  animation-delay: 1.3s;
  background: linear-gradient(90deg, transparent, rgba(236, 72, 153, 0.2), transparent);
}
.wave3[data-v-792157c3] {
  animation-delay: 2.6s;
  background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.2), transparent);
}
@keyframes waveMove-792157c3 {
0% {
    left: -100%;
    transform: translateY(-50%) scaleY(0.5);
}
50% {
    left: 0%;
    transform: translateY(-50%) scaleY(1);
}
100% {
    left: 100%;
    transform: translateY(-50%) scaleY(0.5);
}
}
.type-item[data-v-792157c3] {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.4s ease;
  position: relative;
  z-index: 2;
  animation: typeFloat-792157c3 3s ease-in-out infinite;
}
.type-item.active .type-circle[data-v-792157c3] {
  background: linear-gradient(135deg, #F59E0B 0%, #EF4444 100%);
  transform: scale(1.2);
  box-shadow: 0 0.25rem 1rem rgba(245, 158, 11, 0.6), 0 0 1.875rem rgba(245, 158, 11, 0.3);
}
.type-item.active .icon-glow[data-v-792157c3] {
  opacity: 1;
  transform: scale(1.5);
}
.type-circle[data-v-792157c3] {
  width: 3.125rem;
  height: 3.125rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  -webkit-backdrop-filter: blur(0.46875rem);
          backdrop-filter: blur(0.46875rem);
  transition: all 0.4s ease;
  position: relative;
  border: 0.0625rem solid rgba(255, 255, 255, 0.3);
}
.icon-glow[data-v-792157c3] {
  position: absolute;
  top: -0.3125rem;
  left: -0.3125rem;
  right: -0.3125rem;
  bottom: -0.3125rem;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: all 0.4s ease;
}
.type-icon[data-v-792157c3] {
  font-size: 1.125rem;
  position: relative;
  z-index: 1;
}
.type-name[data-v-792157c3] {
  color: #fff;
  font-size: 0.75rem;
  opacity: 0.9;
  font-weight: 500;
  text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.3);
}
@keyframes typeFloat-792157c3 {
0%, 100% {
    transform: translateY(0);
}
50% {
    transform: translateY(-0.3125rem);
}
}
/* 主要盲盒区域 */
.main-box-area[data-v-792157c3] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2.5rem 1.25rem;
  position: relative;
  z-index: 10;
}

/* 魔法光环 */
.magic-circle[data-v-792157c3] {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}
.magic-ring[data-v-792157c3] {
  position: absolute;
  border-radius: 50%;
  border: 0.0625rem solid rgba(255, 255, 255, 0.2);
  animation: magicRotate-792157c3 8s linear infinite;
}
.ring1[data-v-792157c3] {
  width: 12.5rem;
  height: 12.5rem;
  top: -6.25rem;
  left: -6.25rem;
  animation-duration: 8s;
}
.ring2[data-v-792157c3] {
  width: 15.625rem;
  height: 15.625rem;
  top: -7.8125rem;
  left: -7.8125rem;
  animation-duration: 12s;
  animation-direction: reverse;
}
.ring3[data-v-792157c3] {
  width: 18.75rem;
  height: 18.75rem;
  top: -9.375rem;
  left: -9.375rem;
  animation-duration: 16s;
}
@keyframes magicRotate-792157c3 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.box-container[data-v-792157c3] {
  position: relative;
  margin-bottom: 1.875rem;
  z-index: 2;
}
.box-glow[data-v-792157c3] {
  position: absolute;
  top: -1.5625rem;
  left: -1.5625rem;
  right: -1.5625rem;
  bottom: -1.5625rem;
  background: radial-gradient(circle, rgba(168, 85, 247, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: glowPulse-792157c3 2s ease-in-out infinite;
}
@keyframes glowPulse-792157c3 {
0%, 100% {
    opacity: 0.3;
    transform: scale(1);
}
50% {
    opacity: 0.6;
    transform: scale(1.1);
}
}
/* 3D盲盒效果 */
.box-3d[data-v-792157c3] {
  width: 9.375rem;
  height: 9.375rem;
  position: relative;
  transform-style: preserve-3d;
  animation: float-792157c3 3s ease-in-out infinite;
  cursor: pointer;
}
.box-face[data-v-792157c3] {
  position: absolute;
  width: 9.375rem;
  height: 9.375rem;
  background: linear-gradient(135deg, #A855F7 0%, #EC4899 100%);
  border: 0.125rem solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}
.box-front[data-v-792157c3] {
  transform: translateZ(4.6875rem);
  border-radius: 0.625rem;
}
.box-top[data-v-792157c3] {
  transform: rotateX(90deg) translateZ(4.6875rem);
  background: linear-gradient(135deg, #C084FC 0%, #F472B6 100%);
}
.box-right[data-v-792157c3] {
  transform: rotateY(90deg) translateZ(4.6875rem);
  background: linear-gradient(135deg, #9333EA 0%, #DB2777 100%);
}
.question-container[data-v-792157c3] {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.box-question[data-v-792157c3] {
  color: #fff;
  font-size: 3.75rem;
  font-weight: bold;
  text-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
  animation: questionPulse-792157c3 2s ease-in-out infinite;
}
.question-sparkles[data-v-792157c3] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.sparkle[data-v-792157c3] {
  position: absolute;
  font-size: 0.75rem;
  animation: sparkleFloat-792157c3 3s ease-in-out infinite;
}
.sparkle1[data-v-792157c3] {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}
.sparkle2[data-v-792157c3] {
  top: 30%;
  right: 20%;
  animation-delay: 1s;
}
.sparkle3[data-v-792157c3] {
  bottom: 20%;
  left: 30%;
  animation-delay: 2s;
}
@keyframes questionPulse-792157c3 {
0%, 100% {
    transform: scale(1);
}
50% {
    transform: scale(1.05);
}
}
@keyframes sparkleFloat-792157c3 {
0%, 100% {
    opacity: 0.5;
    transform: translateY(0) rotate(0deg);
}
50% {
    opacity: 1;
    transform: translateY(-0.3125rem) rotate(180deg);
}
}
@keyframes float-792157c3 {
0%, 100% {
    transform: translateY(0) rotateY(0deg);
}
50% {
    transform: translateY(-0.625rem) rotateY(10deg);
}
}
/* 增强纸条效果 - 完全居中 */
.papers[data-v-792157c3] {
  position: absolute;
  top: -1.875rem;
  left: 50%;
  transform: translateX(-50%);
  width: 9.375rem;
  height: 6.25rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.paper[data-v-792157c3] {
  width: 1.5625rem;
  height: 3.125rem;
  background: #fff;
  border-radius: 0.25rem 0.25rem 0 0;
  box-shadow: 0 0.1875rem 0.625rem rgba(0, 0, 0, 0.3);
  animation: paperFloat-792157c3 3s ease-in-out infinite;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.0625rem solid rgba(255, 255, 255, 0.8);
  position: relative;
}
.paper-content[data-v-792157c3] {
  font-size: 0.625rem;
  margin-top: 0.46875rem;
}
.paper1[data-v-792157c3] {
  animation-delay: 0s;
  background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
}
.paper2[data-v-792157c3] {
  animation-delay: 0.4s;
  background: linear-gradient(135deg, #DBEAFE 0%, #93C5FD 100%);
}
.paper3[data-v-792157c3] {
  animation-delay: 0.8s;
  background: linear-gradient(135deg, #FCE7F3 0%, #F9A8D4 100%);
}
.paper4[data-v-792157c3] {
  animation-delay: 1.2s;
  background: linear-gradient(135deg, #F3E8FF 0%, #C4B5FD 100%);
}
.paper5[data-v-792157c3] {
  animation-delay: 1.6s;
  background: linear-gradient(135deg, #ECFDF5 0%, #86EFAC 100%);
}
@keyframes paperFloat-792157c3 {
0%, 100% {
    transform: translateY(0) rotate(0deg) scale(1);
    opacity: 0.8;
}
25% {
    transform: translateY(-0.46875rem) rotate(3deg) scale(1.05);
    opacity: 1;
}
50% {
    transform: translateY(-0.78125rem) rotate(-2deg) scale(1.1);
    opacity: 0.9;
}
75% {
    transform: translateY(-0.46875rem) rotate(1deg) scale(1.05);
    opacity: 1;
}
}
/* 为不同位置的纸条添加不同的动画变化 */
.paper1[data-v-792157c3] {
  animation-name: paperFloat1-792157c3;
}
.paper2[data-v-792157c3] {
  animation-name: paperFloat2-792157c3;
}
.paper3[data-v-792157c3] {
  animation-name: paperFloat3-792157c3;
}
.paper4[data-v-792157c3] {
  animation-name: paperFloat4-792157c3;
}
.paper5[data-v-792157c3] {
  animation-name: paperFloat5-792157c3;
}
@keyframes paperFloat1-792157c3 {
0%, 100% {
    transform: translateY(0) rotate(-2deg) scale(1);
    opacity: 0.8;
}
50% {
    transform: translateY(-0.5625rem) rotate(2deg) scale(1.06);
    opacity: 1;
}
}
@keyframes paperFloat2-792157c3 {
0%, 100% {
    transform: translateY(0) rotate(1deg) scale(1);
    opacity: 0.8;
}
50% {
    transform: translateY(-0.6875rem) rotate(-3deg) scale(1.08);
    opacity: 1;
}
}
@keyframes paperFloat3-792157c3 {
0%, 100% {
    transform: translateY(0) rotate(0deg) scale(1);
    opacity: 0.9;
}
50% {
    transform: translateY(-0.78125rem) rotate(0deg) scale(1.1);
    opacity: 1;
}
}
@keyframes paperFloat4-792157c3 {
0%, 100% {
    transform: translateY(0) rotate(-1deg) scale(1);
    opacity: 0.8;
}
50% {
    transform: translateY(-0.6875rem) rotate(3deg) scale(1.08);
    opacity: 1;
}
}
@keyframes paperFloat5-792157c3 {
0%, 100% {
    transform: translateY(0) rotate(2deg) scale(1);
    opacity: 0.8;
}
50% {
    transform: translateY(-0.5625rem) rotate(-2deg) scale(1.06);
    opacity: 1;
}
}
/* 增强提示文字 */
.hint-text[data-v-792157c3] {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  padding: 0.625rem 1.25rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1.5625rem;
  -webkit-backdrop-filter: blur(0.3125rem);
          backdrop-filter: blur(0.3125rem);
  border: 0.03125rem solid rgba(255, 255, 255, 0.2);
}
.hint-decoration[data-v-792157c3] {
  width: 1.25rem;
  height: 0.0625rem;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  position: relative;
}
.hint-decoration.left[data-v-792157c3] {
  animation: decorationGlow-792157c3 3s ease-in-out infinite;
}
.hint-decoration.right[data-v-792157c3] {
  animation: decorationGlow-792157c3 3s ease-in-out infinite reverse;
}
@keyframes decorationGlow-792157c3 {
0%, 100% {
    opacity: 0.3;
    transform: scaleX(1);
}
50% {
    opacity: 1;
    transform: scaleX(1.5);
}
}
.hint-icon[data-v-792157c3] {
  font-size: 1rem;
  animation: pulse-792157c3 2s infinite;
}
.hint-content[data-v-792157c3] {
  color: rgba(255, 255, 255, 0.95);
  font-size: 0.875rem;
  font-weight: 500;
  text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.3);
  letter-spacing: 0.0625rem;
}
@keyframes pulse-792157c3 {
0%, 100% {
    transform: scale(1);
}
50% {
    transform: scale(1.1);
}
}
/* 底部操作按钮 */
.bottom-actions[data-v-792157c3] {
  display: flex;
  gap: 1.25rem;
  padding: 0 1.875rem 1.25rem;
  position: relative;
  z-index: 10;
}
.action-btn[data-v-792157c3] {
  flex: 1;
  height: 3.125rem;
  border-radius: 1.5625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  border: 0.0625rem solid rgba(255, 255, 255, 0.3);
}
.action-btn[data-v-792157c3]::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}
.action-btn[data-v-792157c3]::after {
  content: "";
  position: absolute;
  top: -0.0625rem;
  left: -0.0625rem;
  right: -0.0625rem;
  bottom: -0.0625rem;
  background: linear-gradient(45deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7, #DDA0DD);
  background-size: 400% 400%;
  border-radius: 1.5625rem;
  z-index: -1;
  animation: gradientShift-792157c3 3s ease infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.action-btn[data-v-792157c3]:active::before {
  transform: translateX(100%);
}
.action-btn[data-v-792157c3]:active::after {
  opacity: 1;
}
.action-btn[data-v-792157c3]:active {
  transform: scale(0.95);
}
@keyframes gradientShift-792157c3 {
0% {
    background-position: 0% 50%;
}
50% {
    background-position: 100% 50%;
}
100% {
    background-position: 0% 50%;
}
}
.draw-btn[data-v-792157c3] {
  background: linear-gradient(135deg, #F59E0B 0%, #EF4444 100%);
  box-shadow: 0 0.25rem 1rem rgba(245, 158, 11, 0.4), 0 0 1.875rem rgba(245, 158, 11, 0.2);
  animation: btnGlow1-792157c3 2s ease-in-out infinite;
}
.publish-btn[data-v-792157c3] {
  background: linear-gradient(135deg, #EC4899 0%, #A855F7 100%);
  box-shadow: 0 0.25rem 1rem rgba(236, 72, 153, 0.4), 0 0 1.875rem rgba(236, 72, 153, 0.2);
  animation: btnGlow2-792157c3 2s ease-in-out infinite;
  animation-delay: 1s;
}
@keyframes btnGlow1-792157c3 {
0%, 100% {
    box-shadow: 0 0.25rem 1rem rgba(245, 158, 11, 0.4), 0 0 1.875rem rgba(245, 158, 11, 0.2);
}
50% {
    box-shadow: 0 0.375rem 1.25rem rgba(245, 158, 11, 0.6), 0 0 2.5rem rgba(245, 158, 11, 0.4);
}
}
@keyframes btnGlow2-792157c3 {
0%, 100% {
    box-shadow: 0 0.25rem 1rem rgba(236, 72, 153, 0.4), 0 0 1.875rem rgba(236, 72, 153, 0.2);
}
50% {
    box-shadow: 0 0.375rem 1.25rem rgba(236, 72, 153, 0.6), 0 0 2.5rem rgba(236, 72, 153, 0.4);
}
}
.btn-text[data-v-792157c3] {
  color: #fff;
  font-size: 1rem;
  font-weight: bold;
  position: relative;
  z-index: 1;
  text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.3);
  letter-spacing: 0.0625rem;
}

/* 底部说明 */
.bottom-tip[data-v-792157c3] {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 1.25rem;
  position: relative;
  z-index: 10;
}
.tip-text[data-v-792157c3] {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
}
.tip-icon[data-v-792157c3] {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
  width: 1rem;
  height: 1rem;
  border: 0.0625rem solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
.type-selector[data-v-792157c3] {
    padding: 1.25rem 0.625rem;
}
.type-circle[data-v-792157c3] {
    width: 2.5rem;
    height: 2.5rem;
}
.type-icon[data-v-792157c3] {
    font-size: 0.875rem;
}
.box-3d[data-v-792157c3] {
    width: 7.8125rem;
    height: 7.8125rem;
}
.box-face[data-v-792157c3] {
    width: 7.8125rem;
    height: 7.8125rem;
}
.box-question[data-v-792157c3] {
    font-size: 3.125rem;
}
}
/* 详情弹窗样式 */
.detail-modal[data-v-792157c3] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-overlay[data-v-792157c3] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  -webkit-backdrop-filter: blur(0.3125rem);
          backdrop-filter: blur(0.3125rem);
}
.modal-content[data-v-792157c3] {
  position: relative;
  width: 21.25rem;
  max-height: 85vh;
  background: white;
  border-radius: 0.75rem;
  padding: 0;
  margin: 0 1.25rem;
  box-shadow: 0 0.625rem 1.875rem rgba(0, 0, 0, 0.3);
  animation: modalSlideIn-792157c3 0.3s ease-out;
  overflow: hidden;
}
@keyframes modalSlideIn-792157c3 {
from {
    opacity: 0;
    transform: translateY(3.125rem) scale(0.9);
}
to {
    opacity: 1;
    transform: translateY(0) scale(1);
}
}
.modal-header[data-v-792157c3] {
  text-align: center;
  padding: 1.25rem 0 0.625rem;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.paper-icon[data-v-792157c3] {
  font-size: 2.5rem;
  margin-bottom: 0.625rem;
}
.sparkles[data-v-792157c3] {
  position: absolute;
  top: 0.625rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1.25rem;
}
.sparkle[data-v-792157c3] {
  font-size: 1rem;
  animation: sparkle-792157c3 2s infinite;
  color: white;
}
.sparkle[data-v-792157c3]:nth-child(2) {
  animation-delay: 1s;
}
@keyframes sparkle-792157c3 {
0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
}
50% {
    opacity: 1;
    transform: scale(1.2);
}
}
.modal-title[data-v-792157c3] {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  padding: 0.9375rem 1.25rem 0.625rem;
}
.info-icon[data-v-792157c3] {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background: #ccc;
  color: white;
  font-size: 0.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 用户卡片 */
.user-card[data-v-792157c3] {
  display: flex;
  align-items: center;
  padding: 0.9375rem 1.25rem;
  border-bottom: 0.03125rem solid #f0f0f0;
}
.user-avatar[data-v-792157c3] {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.625rem;
  background: #f5f5f5;
}
.avatar-image[data-v-792157c3] {
  width: 100%;
  height: 100%;
}
.user-info-text[data-v-792157c3] {
  flex: 1;
}
.user-name[data-v-792157c3] {
  font-size: 0.875rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.25rem;
}
.user-details[data-v-792157c3] {
  font-size: 0.75rem;
  color: #999;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}
.gender[data-v-792157c3] {
  color: #ff69b4;
}
.age-location[data-v-792157c3] {
  color: #999;
}
.type-badge[data-v-792157c3] {
  padding: 0.25rem 0.5rem;
  border-radius: 0.625rem;
  font-size: 0.6875rem;
  display: flex;
  align-items: center;
  gap: 0.1875rem;
}
.badge-type-1[data-v-792157c3] {
  background: #e3f2fd;
  color: #1976d2;
}
.badge-type-2[data-v-792157c3] {
  background: #fce4ec;
  color: #c2185b;
}
.badge-type-3[data-v-792157c3] {
  background: #f3e5f5;
  color: #7b1fa2;
}
.badge-type-4[data-v-792157c3] {
  background: #fff3e0;
  color: #f57c00;
}

/* 纸条内容 */
.paper-content[data-v-792157c3] {
  padding: 0.9375rem 1.25rem;
  border-bottom: 0.03125rem solid #f0f0f0;
}
.content-text[data-v-792157c3] {
  color: #333;
  font-size: 0.875rem;
  line-height: 1.6;
}
.voice-section[data-v-792157c3] {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.625rem 0;
}
.voice-player[data-v-792157c3] {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  padding: 0.625rem 1.25rem;
  background: #f5f5f5;
  border-radius: 1.5625rem;
  color: #333;
  font-size: 0.875rem;
}

/* 回应区域 */
.response-section[data-v-792157c3] {
  padding: 0.9375rem 1.25rem;
}
.response-header[data-v-792157c3] {
  margin-bottom: 0.625rem;
}
.response-count[data-v-792157c3] {
  font-size: 0.8125rem;
  color: #666;
  font-weight: bold;
}
.response-input[data-v-792157c3] {
  position: relative;
  background: #f8f8f8;
  border-radius: 0.5rem;
  padding: 0.625rem;
}
.input-field[data-v-792157c3] {
  width: 100%;
  min-height: 3.75rem;
  font-size: 0.8125rem;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
}
.input-counter[data-v-792157c3] {
  position: absolute;
  bottom: 0.625rem;
  right: 0.625rem;
  font-size: 0.6875rem;
  color: #999;
}

/* 底部按钮 */
.bottom-actions[data-v-792157c3] {
  display: flex;
  padding: 0.9375rem 1.25rem;
  gap: 0.625rem;
}
.action-button[data-v-792157c3] {
  flex: 1;
  padding: 0.75rem;
  border-radius: 0.78125rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: bold;
}
.action-button.secondary[data-v-792157c3] {
  background: #f5f5f5;
  color: #666;
}
.action-button.primary[data-v-792157c3] {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  color: white;
  box-shadow: 0 0.25rem 0.625rem rgba(139, 92, 246, 0.3);
}