{"version": 3, "file": "details.js", "sources": ["pages/user/details.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9kZXRhaWxzLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 导航栏 -->\n    <view class=\"nav-box df\" :style=\"{'padding-top': statusBarHeight + 'px', 'background': 'rgba(255, 255, 255,' + navbarTrans + ')'}\">\n      <view class=\"nav-back df\" :style=\"{'height': titleBarHeight + 'px'}\" @tap=\"navBack\">\n        <image :class=\"[navbarTrans != 1 ? 'xwb' : '']\" :src=\"navbarTrans == 1 ? '/static/img/z.png' : '/static/img/z1.png'\" style=\"width:34rpx;height:34rpx\"></image>\n      </view>\n      <view v-if=\"navbarTrans == 1\" class=\"nav-title ohto\">{{userInfo.name}}</view>\n    </view>\n    \n    <!-- 用户信息 -->\n    <view class=\"user-box\" :style=\"{'padding-top': statusBarHeight + titleBarHeight + 'px'}\">\n      <view class=\"user-bg\"></view>\n      <view class=\"user-img\" style=\"z-index:-2\">\n        <!-- 背景轮播区域 -->\n        <view v-if=\"backgroundImages.length > 0\" class=\"background-carousel\">\n          <view \n            v-for=\"(img, index) in backgroundImages\" \n            :key=\"index\"\n            class=\"carousel-item\"\n            :class=\"{'active': index === currentBgIndex}\"\n          >\n            <lazy-image :src=\"img.url\" mode=\"aspectFill\"></lazy-image>\n          </view>\n          <!-- 轮播指示器 -->\n          <view v-if=\"backgroundImages.length > 1\" class=\"carousel-indicators\">\n            <view \n              v-for=\"(item, index) in backgroundImages\" \n              :key=\"index\"\n              class=\"indicator\"\n              :class=\"{'active': index === currentBgIndex}\"\n              @tap=\"switchBackground(index)\"\n            ></view>\n          </view>\n        </view>\n        <!-- 默认头像背景 -->\n        <view v-else class=\"default-background\">\n          <lazy-image :src=\"userInfo.avatar || '/static/img/avatar.png'\" mode=\"aspectFill\"></lazy-image>\n        </view>\n      </view>\n      <view class=\"user-top df\">\n        <view class=\"avatar\">\n          <lazy-image :src=\"userInfo.avatar\"></lazy-image>\n        </view>\n        <view :class=\"['btn', followBtnClass]\" @tap=\"followClick\">\n          {{followBtnText}}\n        </view>\n      </view>\n      <view class=\"user-name\">{{userInfo.name}}</view>\n      <view v-if=\"userInfo.intro\" class=\"user-intro\">\n        <text user-select=\"true\">{{userInfo.intro}}</text>\n      </view>\n      <view class=\"user-tag df\">\n        <view v-if=\"userInfo.gender != undefined || userInfo.sex != undefined\" class=\"tag-item df\">\n          <image :src=\"(userInfo.gender == 1 || userInfo.sex == 1) ? '/static/img/nan.png' : '/static/img/nv.png'\"></image>\n        </view>\n        <view v-if=\"userInfo.constellation_label\" class=\"tag-item df\">\n          {{userInfo.constellation_label}}\n        </view>\n        <view v-else-if=\"userInfo.age && userInfo.age != '暂不展示'\" class=\"tag-item df\">\n          {{userInfo.age}}\n        </view>\n        <view class=\"tag-item df\">IP属地：{{userInfo.province || '未知'}}</view>\n      </view>\n      <view class=\"user-num df\">\n        <view class=\"num-item df\" data-type=\"0\" @tap=\"toFollow\">\n          <text class=\"t1\">{{userInfo.follow_count}}</text>\n          <text>关注</text>\n        </view>\n        <view class=\"num-item df\" data-type=\"1\" @tap=\"toFollow\">\n          <text class=\"t1\">{{userInfo.fans_count}}</text>\n          <text>粉丝</text>\n        </view>\n        <view class=\"num-item df\" @tap=\"likePopupClick(true)\">\n          <text class=\"t1\">{{userInfo.like_count_str}}</text>\n          <text>获赞</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view class=\"content-box\">\n      <!-- 圈子信息 -->\n      <view v-if=\"userInfo.circle.length\" class=\"block-box\">\n        <view class=\"block-title\">{{userInfo.name}} 加入的圈子</view>\n        <scroll-view scroll-x=\"true\" style=\"width:100%;white-space:nowrap\">\n          <view class=\"circle-box\">\n            <view v-for=\"(item, index) in userInfo.circle\" \n                  :key=\"index\" \n                  class=\"circle-item df\" \n                  :data-url=\"'note/circle?id=' + item.id\" \n                  @tap=\"navigateToFun\">\n              <image class=\"circle-avatar\" :src=\"item.avatar\" mode=\"aspectFill\"></image>\n              <view class=\"circle-name ohto\">{{item.name}}</view>\n            </view>\n            <view style=\"flex-shrink:0;width:15rpx;height:15rpx\"></view>\n          </view>\n        </scroll-view>\n      </view>\n      \n      <!-- 导航栏选项 -->\n      <view class=\"bar-box df\" :style=\"{'top': statusBarHeight + titleBarHeight - 1 + 'px'}\">\n        <view v-for=\"(item, index) in barList\" \n              :key=\"index\" \n              class=\"bar-item df\" \n              @tap=\"barClick\" \n              :data-idx=\"index\">\n          <text :style=\"{\n            'color': index == barIdx ? '#000' : '#999',\n            'font-size': index == barIdx ? '28rpx' : '26rpx'\n          }\">{{item}}</text>\n          <view :style=\"{'opacity': index == barIdx ? 1 : 0}\" class=\"bar-line\"></view>\n        </view>\n      </view>\n      \n      <!-- 加载中提示 -->\n      <view v-if=\"loadStatus == 'loading' && !isThrottling\" class=\"loading-box df\">\n        <uni-load-more :status=\"loadStatus\"></uni-load-more>\n      </view>\n      \n      <!-- 错误状态提示 -->\n      <emptyPage\n        v-if=\"hasError\"\n        title=\"加载失败\"\n        :description=\"errorMessage || '网络连接异常，请稍后重试'\"\n        image=\"/static/img/empty.png\"\n        buttonText=\"重新加载\"\n        @buttonClick=\"retryLoad\"\n      />\n\n      <!-- 空内容提示 -->\n      <emptyPage\n        v-else-if=\"isEmpty && !hasError\"\n        :title=\"barIdx == 0 ? '暂无笔记内容' : '暂无喜欢的内容'\"\n        :description=\"userInfo.name + (barIdx == 0 ? ' 还没有发布过' : ' 还没有点赞')\"\n        image=\"/static/img/empty.png\"\n      />\n\n      <!-- 隐私设置提示 -->\n      <emptyPage\n        v-else-if=\"barIdx == 1 && userInfo.privacy.like == 0\"\n        title=\"点赞内容不可见\"\n        description=\"该用户已将点赞设为私密\"\n        image=\"/static/img/empty.png\"\n      />\n      \n      <!-- 内容列表 -->\n      <view v-else-if=\"list.length > 0\" :class=\"[isWaterfall ? 'dynamic-box' : '']\">\n        <waterfall v-if=\"isWaterfall\" :note=\"list\" :page=\"page\"></waterfall>\n        <block v-else>\n          <block v-for=\"(item, index) in list\" :key=\"index\">\n            <card-gg :item=\"item\" :idx=\"index\" @likeback=\"likeClick\" @followback=\"followBack\"></card-gg>\n          </block>\n        </block>\n        <!-- 底部加载更多状态 -->\n        <view v-if=\"loadStatus !== 'loading'\" class=\"load-more-box\">\n        <uni-load-more :status=\"loadStatus\"></uni-load-more>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 获赞弹窗 -->\n    <uni-popup ref=\"likePopup\">\n      <view class=\"like-popup\">\n        <image class=\"like-img\" src=\"/static/img/like-big.png\" mode=\"aspectFill\"></image>\n        <view class=\"like-content\">\n          <text>\"</text>{{userInfo.name}}<text>\"</text>共获得 {{userInfo.like_count}} 个赞\n        </view>\n        <view class=\"like-btn\" @tap=\"likePopupClick(false)\">确认</view>\n      </view>\n    </uni-popup>\n    \n    <!-- 提示弹窗 -->\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\n      <view class=\"tips-box df\">\n        <view class=\"tips-item\">{{tipsTitle}}</view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport lazyImage from '@/components/lazyImage/lazyImage'\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\nimport waterfall from '@/components/waterfall/waterfall'\nimport cardGg from '@/components/card-gg/card-gg'\nimport emptyPage from '@/components/emptyPage/emptyPage.vue'\nimport { getUserHomepage, getOtherUserDynamicList, followUser, getLikeDynamicList } from '@/api/social.js'\nimport { useUserStore } from '@/stores/user.js'\n\nexport default {\n  components: {\n    lazyImage,\n    uniLoadMore,\n    waterfall,\n    cardGg,\n    emptyPage\n  },\n  data() {\n    return {\n      userStore: useUserStore(),\n      statusBarHeight: this.$store?.state?.statusBarHeight || 20,\n      titleBarHeight: this.$store?.state?.titleBarHeight || 44,\n      navbarTrans: 0,\n      userInfo: {\n        id: 0,\n        uid: 0,\n        avatar: \"\",\n        name: \"昵称加载中\",\n        nickname: \"\",\n        intro: \"\",\n        about_me: \"\",\n        gender: 0,\n        sex: 0,\n        age: \"\",\n        constellation: 0,\n        constellation_label: \"\",\n        province: \"\",\n        follow_count: 0,\n        fans_count: 0,\n        like_count: 0,\n        like_count_str: 0,\n        is_follow: 0,\n        is_mutual_follow: 0,\n        circle: [],\n        user_id_number: \"\",\n        privacy: {\n          like: 1,\n          follow: 1\n        },\n        home_background: \"[]\",\n        visitors: [], // 访客记录\n        // VIP相关信息\n        vip: false,\n        vip_id: 0,\n        vip_icon: '',\n        vip_name: '',\n        vip_status: 2,\n        svip_open: false,\n        is_ever_level: 0,\n        is_money_level: 0,\n        overdue_time: 0\n      },\n      barList: [\"笔记\", \"赞过\"],\n      barIdx: 0,\n      isThrottling: false,\n      list: [],\n      page: 1,\n      isEmpty: false,\n      loadStatus: \"loading\",\n      tipsTitle: \"\",\n      isWaterfall: false,\n      backgroundImages: [],\n      currentBgIndex: 0,\n      carouselTimer: null,\n      userId: 0,\n      isFollowing: false,\n      limit: 10,\n      isLoading: false,\n      isProcessing: false,\n      // 页面状态管理\n      pageLoaded: false,\n      userLoaded: false,\n      // 错误状态\n      hasError: false,\n      errorMessage: \"\"\n    }\n  },\n  computed: {\n    isLogin() {\n      return this.userStore.isLoggedIn;\n    },\n    loginUserId() {\n      return this.userStore.uid || 0;\n    },\n    \n    // 关注按钮样式\n    followBtnClass() {\n      // 检查是否为互相关注\n      if (parseInt(this.userInfo.is_mutual_follow) === 1) {\n        return 'mutual';\n      } \n      // 检查是否已关注\n      else if (this.isFollowing || parseInt(this.userInfo.is_follow) === 1) {\n        return 'active';\n      } \n      // 未关注状态\n      else {\n        return '';\n      }\n    },\n    \n    // 关注按钮文本\n    followBtnText() {\n      // 检查是否为互相关注\n      if (parseInt(this.userInfo.is_mutual_follow) === 1) {\n        return '互相关注';\n      } \n      // 检查是否已关注\n      else if (this.isFollowing || parseInt(this.userInfo.is_follow) === 1) {\n        return '已关注';\n      } \n      // 未关注状态\n      else {\n        return '＋关注';\n      }\n    }\n  },\n  async onLoad(options) {\n    // 显示分享菜单 - 添加平台兼容性检查\n    try {\n      // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ\n      if (typeof uni.showShareMenu === 'function') {\n        uni.showShareMenu()\n      }\n      // #endif\n    } catch (e) {\n      console.warn('showShareMenu not supported on this platform:', e)\n    }\n\n    // 等待应用初始化完成\n    await this.$onLaunched\n    \n    console.log('页面onLoad参数:', options);\n    \n    try {\n      // 同时支持id和user_id参数\n      const userId = options?.id || options?.user_id || options?.uid || 0;\n      \n      if (userId) {\n        // 确保ID为数字类型\n        this.userId = parseInt(userId);\n        \n        if (isNaN(this.userId) || this.userId <= 0) {\n          console.error('用户ID无效:', userId);\n          this.opTipsPopup(\"用户ID无效\", true);\n          return;\n        }\n        \n        // 检查是否是查看自己的页面\n        const loginUserId = this.loginUserId || uni.getStorageSync('uid') || 0;\n        const isSelfProfile = this.userId === parseInt(loginUserId);\n        \n        console.log('加载用户ID:', this.userId, '是否自己:', isSelfProfile);\n        \n        // 如果是查看自己的页面，可以添加一些特殊处理\n        if (isSelfProfile) {\n          this.barList = [\"笔记\", \"赞过\"]; // 确保可以看到自己的点赞\n        } else {\n          this.barList = [\"笔记\", \"赞过\"]; // 其他用户的点赞可能受隐私设置影响\n        }\n        \n        // 调用获取用户详情方法\n        this.getUserInfo();\n        this.loadDynamicList();\n      } else {\n        console.error('缺少用户ID参数');\n        this.opTipsPopup(\"用户状态异常或已注销！\", true);\n      }\n    } catch (e) {\n      console.error('onLoad异常:', e);\n      this.opTipsPopup(\"加载用户信息失败\", true);\n    }\n  },\n  onUnload() {\n    // 页面卸载时清理轮播定时器\n    this.clearCarouselTimer();\n  },\n  methods: {\n    /**\n     * 获取用户信息\n     */\n    getUserInfo() {\n      console.log('开始获取用户信息, userId:', this.userId);\n      \n      // 确保userId有效\n      if (!this.userId || this.userId <= 0) {\n        console.error('获取用户信息失败: 用户ID无效', this.userId);\n        this.opTipsPopup(\"用户ID无效\", true);\n        return;\n      }\n      \n      // 显示加载状态\n      uni.showLoading({\n        title: '加载中...',\n        mask: true\n      });\n      \n      // 简化调用，后端会自动获取当前登录用户ID\n      getUserHomepage({\n        user_id: this.userId\n      }).then(res => {\n        console.log('获取用户信息成功:', res);\n        if (res.data) {\n          // 保存原始数据\n          this.userInfo = res.data;\n          \n          // 处理ID字段\n          if (!this.userInfo.id && this.userInfo.uid) {\n            this.userInfo.id = this.userInfo.uid;\n          }\n          \n          // 处理昵称字段\n          if (!this.userInfo.name && this.userInfo.nickname) {\n            this.userInfo.name = this.userInfo.nickname;\n          }\n          \n          // 处理简介字段\n          if (!this.userInfo.intro && this.userInfo.about_me) {\n            this.userInfo.intro = this.userInfo.about_me;\n          }\n          \n          // 确保关注状态是数字类型\n          this.userInfo.is_follow = parseInt(this.userInfo.is_follow || 0);\n          this.userInfo.is_mutual_follow = parseInt(this.userInfo.is_mutual_follow || 0);\n          \n          // 设置关注状态\n          this.isFollowing = this.userInfo.is_follow === 1;\n          \n          // 确保隐私设置存在\n          if (!this.userInfo.privacy) {\n            this.userInfo.privacy = {\n              like: 1,\n              follow: 1\n            };\n          }\n\n          // 处理访客记录（只有查看自己主页时才有数据）\n          if (res.data.visitors && Array.isArray(res.data.visitors)) {\n            this.userInfo.visitors = res.data.visitors;\n            console.log('获取到访客记录:', res.data.visitors.length, '条');\n          } else {\n            this.userInfo.visitors = [];\n          }\n\n          // 处理VIP信息\n          if (res.data.vip !== undefined) {\n            this.userInfo.vip = res.data.vip;\n            this.userInfo.vip_id = res.data.vip_id || 0;\n            this.userInfo.vip_icon = res.data.vip_icon || '';\n            this.userInfo.vip_name = res.data.vip_name || '';\n            this.userInfo.vip_status = res.data.vip_status || 2;\n            this.userInfo.svip_open = res.data.svip_open || false;\n            this.userInfo.is_ever_level = res.data.is_ever_level || 0;\n            this.userInfo.is_money_level = res.data.is_money_level || 0;\n            this.userInfo.overdue_time = res.data.overdue_time || 0;\n            console.log('获取到VIP信息:', {\n              vip: this.userInfo.vip,\n              vip_name: this.userInfo.vip_name,\n              vip_status: this.userInfo.vip_status,\n              svip_open: this.userInfo.svip_open\n            });\n          }\n\n          console.log('处理后的用户信息:', {\n            id: this.userInfo.id,\n            name: this.userInfo.name,\n            is_follow: this.userInfo.is_follow,\n            is_mutual_follow: this.userInfo.is_mutual_follow,\n            isFollowing: this.isFollowing,\n            privacy: this.userInfo.privacy,\n            visitorsCount: this.userInfo.visitors.length,\n            vip: this.userInfo.vip,\n            vip_status: this.userInfo.vip_status\n          });\n          \n          // 处理背景图片\n          this.updateBackgroundImages();\n          \n          // 设置页面标题\n          uni.setNavigationBarTitle({\n            title: this.userInfo.name || '用户详情'\n          });\n        } else {\n          console.error('获取用户信息返回数据为空');\n          this.opTipsPopup(\"获取用户信息失败\", true);\n        }\n      }).catch(err => {\n        console.error('获取用户信息失败', err);\n        this.opTipsPopup(\"获取用户信息失败: \" + (err.msg || \"网络错误\"), true);\n      }).finally(() => {\n        uni.hideLoading();\n      });\n    },\n    \n    /**\n     * 加载用户动态列表\n     */\n    loadDynamicList() {\n      if (this.isLoading || this.loadStatus === 'noMore') return;\n      \n      this.isLoading = true;\n      this.loadStatus = 'loading';\n      \n      // 判断是否是查看\"赞过\"标签，且是查看自己的页面\n      const isLikedTab = this.barIdx === 1;\n      const isOwnProfile = this.userId === this.loginUserId;\n      \n      let apiCall;\n      \n      if (isLikedTab) {\n        // \"赞过\"标签，调用getLikeDynamicList接口\n        console.log('加载点赞动态列表 - 用户ID:', this.userId);\n        apiCall = getLikeDynamicList(this.userId, {\n          page: this.page,\n          limit: this.limit\n        });\n      } else {\n        // \"笔记\"标签，查看用户发布的动态列表\n        console.log('加载用户动态列表, userId:', this.userId, 'barIdx:', this.barIdx);\n        apiCall = getOtherUserDynamicList(this.userId, {\n          page: this.page,\n          limit: this.limit\n        });\n      }\n      \n      apiCall.then(res => {\n        console.log('动态列表接口返回:', res);\n        const list = res.data.list || [];\n        \n        if (this.page === 1) {\n          this.list = list;\n          this.isEmpty = list.length === 0;\n        } else {\n          this.list = [...this.list, ...list];\n        }\n        \n        this.loadStatus = list.length < this.limit ? 'noMore' : 'more';\n        this.page++;\n        \n        // 如果是第一页且有数据，判断是否需要使用瀑布流布局\n        if (this.page === 2 && list.length > 0) {\n          // 检查是否有图片或视频内容\n          const hasMediaContent = list.some(item => \n            item.type === 2 || item.type === 3 || \n            (item.images && item.images.length > 0)\n          );\n          this.isWaterfall = hasMediaContent;\n        }\n      }).catch(err => {\n        console.error('获取动态列表失败', err);\n        this.loadStatus = 'more';\n        \n        // 如果是点赞列表且出错，可能需要特殊处理\n        if (isLikedTab && !isOwnProfile) {\n          // 检查是否是隐私设置问题\n          if (err.code === 403 || err.status === 403) {\n            this.opTipsPopup(\"该用户已将点赞设为私密\");\n          } else {\n            uni.showToast({\n              title: '加载失败，请重试',\n              icon: 'none'\n            });\n          }\n        } else {\n          uni.showToast({\n            title: '加载失败，请重试',\n            icon: 'none'\n          });\n        }\n      }).finally(() => {\n        this.isLoading = false;\n        uni.stopPullDownRefresh();\n      });\n    },\n    \n    /**\n     * 处理关注/取消关注\n     */\n    handleFollow() {\n      if (!this.isLogin) {\n        uni.navigateTo({\n          url: '/pages/login/index'\n        });\n        return;\n      }\n      \n      // 防止重复点击\n      if (this.isProcessing) return;\n      this.isProcessing = true;\n      \n      // 获取目标用户ID，确保是整数类型\n      const targetUserId = parseInt(this.userId);\n      \n      // 验证用户ID\n      if (!targetUserId || targetUserId <= 0) {\n        uni.showToast({\n          title: \"获取用户ID失败\",\n          icon: \"none\"\n        });\n        console.error('关注操作失败: 无效的用户ID', targetUserId);\n        this.isProcessing = false;\n        return;\n      }\n      \n      // 当前的关注状态\n      const isMutual = parseInt(this.userInfo.is_mutual_follow) === 1;\n      const isFollowed = parseInt(this.userInfo.is_follow) === 1 || isMutual;\n      \n      console.log('当前关注状态:', {isFollowed, isMutual, targetUserId});\n      \n      // 先更新UI状态\n      this.$set(this.userInfo, 'is_follow', isFollowed ? 0 : 1);\n      \n      // 如果取消关注，也需要更新互相关注状态\n      if (isFollowed) {\n        this.$set(this.userInfo, 'is_mutual_follow', 0);\n      }\n      \n      this.isFollowing = !isFollowed;\n      \n      // 显示加载提示\n      uni.showToast({\n        title: isFollowed ? \"取消关注中...\" : \"关注中...\",\n        icon: \"none\",\n        duration: 500\n      });\n      \n      // 准备请求参数 - 修复参数名问题\n      const params = {\n        follow_uid: targetUserId,  // 修改为follow_uid，与card-gg.vue保持一致\n        is_follow: isFollowed ? 0 : 1\n      };\n      \n      console.log('发送关注请求参数:', JSON.stringify(params));\n      \n      followUser(params).then(res => {\n        console.log('关注接口返回:', res);\n        \n        if (res.status === 200) {\n          // 处理互相关注状态\n          if (res.data && res.data.is_mutual !== undefined) {\n            const isMutual = parseInt(res.data.is_mutual) === 1;\n            \n            // 更新用户信息中的互相关注状态\n            this.$set(this.userInfo, 'is_mutual_follow', isMutual ? 1 : 0);\n            console.log('更新互相关注状态:', this.userInfo.is_mutual_follow);\n          }\n          \n          // 更新粉丝数量\n          if (res.data && res.data.fans_count !== undefined) {\n            this.$set(this.userInfo, 'fans_count', res.data.fans_count);\n          } else {\n            // 如果接口没有返回粉丝数，手动更新\n            const currentFansCount = parseInt(this.userInfo.fans_count) || 0;\n            this.$set(this.userInfo, 'fans_count', isFollowed ? Math.max(0, currentFansCount - 1) : currentFansCount + 1);\n          }\n          \n          uni.showToast({\n            title: isFollowed ? '已取消关注' : '关注成功',\n            icon: 'none'\n          });\n        } else {\n          // 恢复原状态 - Vue3兼容：直接赋值替代$set\n          this.userInfo.is_follow = isFollowed ? 1 : 0;\n          if (isMutual) {\n            this.userInfo.is_mutual_follow = isMutual ? 1 : 0;\n          }\n          this.isFollowing = isFollowed;\n          \n          uni.showToast({\n            title: res.msg || '操作失败，请重试',\n            icon: 'none'\n          });\n        }\n      }).catch(err => {\n        // 失败时恢复状态 - Vue3兼容：直接赋值替代$set\n        this.userInfo.is_follow = isFollowed ? 1 : 0;\n        if (isMutual) {\n          this.userInfo.is_mutual_follow = isMutual ? 1 : 0;\n        }\n        this.isFollowing = isFollowed;\n        \n        uni.showToast({\n          title: '网络错误，请重试',\n          icon: 'none'\n        });\n        console.error('关注操作异常:', err);\n      }).finally(() => {\n        this.isProcessing = false;\n      });\n    },\n    \n    /**\n     * 处理点赞回调\n     */\n    likeClick(e) {\n      const { id, isLike } = e;\n      // 点赞状态已经在组件内部处理，这里可以做额外操作\n      console.log('点赞状态变更', id, isLike);\n    },\n    \n    /**\n     * 处理关注回调\n     */\n    followBack(e) {\n      const { idx, uid, is_follow, is_mutual } = e;\n      console.log('关注状态回调', { idx, uid, is_follow, is_mutual });\n      \n      // 更新列表中对应项的关注状态\n      if (this.list[idx]) {\n        this.$set(this.list[idx], 'is_follow', is_follow);\n        this.$set(this.list[idx], 'is_mutual_follow', is_mutual);\n        \n        // 如果有user_info字段，也要更新\n        if (this.list[idx].user_info) {\n          this.$set(this.list[idx].user_info, 'is_follow', is_follow);\n          this.$set(this.list[idx].user_info, 'is_mutual_follow', is_mutual);\n        }\n      }\n    },\n    \n    /**\n     * 下拉刷新\n     */\n    onPullDownRefresh() {\n      console.log('下拉刷新');\n      \n      // 重置分页状态\n      this.page = 1;\n      this.list = [];\n      this.isEmpty = false;\n      this.loadStatus = 'loading';\n      \n      // 重新获取用户信息和动态列表\n      Promise.all([\n        this.getUserInfo(),\n        this.loadDynamicList()\n      ]).finally(() => {\n        uni.stopPullDownRefresh();\n      });\n    },\n    \n    /**\n     * 触底加载更多\n     */\n    onReachBottom() {\n      console.log('触底加载更多, 当前页:', this.page, '加载状态:', this.loadStatus);\n      \n      // 只有在有数据且不是加载中状态时才加载更多\n      if (this.loadStatus === 'more' && !this.isLoading) {\n      this.loadDynamicList();\n      }\n    },\n    barClick(e) {\n      // 防止重复点击\n      if (this.isThrottling) return;\n      \n      const newBarIdx = parseInt(e.currentTarget.dataset.idx);\n      \n      // 如果点击的是当前标签，不做任何操作\n      if (newBarIdx === this.barIdx) return;\n      \n      this.isThrottling = true;\n      this.barIdx = newBarIdx;\n      \n      console.log('切换标签:', this.barIdx, '用户ID:', this.userId, '登录用户ID:', this.loginUserId);\n      \n      // 检查隐私设置：只有查看其他人的\"赞过\"时才需要检查隐私设置\n      const isOtherUserLikedTab = this.barIdx === 1 && this.userId !== this.loginUserId;\n      if (isOtherUserLikedTab && this.userInfo.privacy && this.userInfo.privacy.like === 0) {\n        console.log('该用户的点赞内容不可见');\n        this.isEmpty = true;\n        this.isThrottling = false;\n        return;\n      }\n      \n      // 显示加载状态\n      uni.showLoading({\n        title: '加载中...',\n        mask: true\n      });\n      \n      // 重置分页和状态\n      this.page = 1;\n      this.list = [];\n      this.isEmpty = false;\n      this.loadStatus = 'loading';\n      this.isWaterfall = false; // 重置瀑布流状态\n      \n      // 加载数据\n      this.loadDynamicList();\n      \n      // 重置节流状态\n      setTimeout(() => {\n        this.isThrottling = false;\n        uni.hideLoading();\n      }, 300);\n    },\n    followClick() {\n      // 直接调用handleFollow处理关注逻辑\n      this.handleFollow();\n    },\n    toFollow(e) {\n      let type = e.currentTarget.dataset.type\n      \n      if (this.userInfo.privacy.follow == 0) {\n        let msg = \"由于该用户隐私设置，关注列表不可见\"\n        if (type == 1) {\n          msg = \"由于该用户隐私设置，粉丝列表不可见\"\n        }\n        return this.opTipsPopup(msg)\n      }\n      \n      uni.navigateTo({\n        url: \"/pages/center/follow?type=\" + type + \"&id=\" + this.userInfo.id + \"&name=\" + this.userInfo.name\n      })\n    },\n    likePopupClick(show) {\n      if (!show) {\n        this.$refs.likePopup.close()\n      }\n      if (show) {\n        this.$refs.likePopup.open()\n      }\n    },\n    navigateToFun(e) {\n      uni.navigateTo({\n        url: \"/pages/\" + e.currentTarget.dataset.url\n      })\n    },\n    navBack() {\n      if (getCurrentPages().length > 1) {\n        uni.navigateBack()\n      } else {\n        uni.switchTab({\n          url: \"/pages/index/index\"\n        })\n      }\n    },\n    opTipsPopup(msg, back) {\n      let that = this\n      that.tipsTitle = msg\n      that.$refs.tipsPopup.open()\n      \n      setTimeout(function() {\n        that.$refs.tipsPopup.close()\n        if (back) {\n          that.navBack()\n        }\n      }, 2000)\n    },\n    navigationBarColor(color) {\n      uni.setNavigationBarColor({\n        frontColor: color,\n        backgroundColor: \"#ffffff\",\n        animation: {\n          duration: 400,\n          timingFunc: \"easeIn\"\n        }\n      })\n    },\n    switchBackground(index) {\n      this.currentBgIndex = index\n      // 重新启动轮播定时器\n      this.startCarousel()\n    },\n    updateBackgroundImages() {\n      try {\n        console.log('处理用户背景图片数据:', this.userInfo.home_background);\n        \n        // 清空当前背景图片数组\n        this.backgroundImages = [];\n        \n        // 如果存在背景图片数据\n        if (this.userInfo.home_background) {\n          let bgImages = [];\n          \n          // 尝试解析JSON字符串\n          if (typeof this.userInfo.home_background === 'string') {\n            try {\n              // 先检查是否是空字符串或\"[]\"\n              if (this.userInfo.home_background.trim() === '' || this.userInfo.home_background.trim() === '[]') {\n                bgImages = [];\n              } else {\n              bgImages = JSON.parse(this.userInfo.home_background);\n              console.log('解析背景图片JSON成功:', bgImages);\n              }\n            } catch (error) {\n              console.error('解析背景图片JSON失败:', error);\n              // 如果解析失败，可能是单个图片URL\n              if (this.userInfo.home_background.trim().startsWith('http')) {\n                bgImages = [{ url: this.userInfo.home_background }];\n              }\n            }\n          } \n          // 如果已经是数组，直接使用\n          else if (Array.isArray(this.userInfo.home_background)) {\n            bgImages = this.userInfo.home_background;\n          }\n          \n          // 确保每个背景图片对象都有url属性\n          this.backgroundImages = bgImages.filter(item => item && item.url && item.url.trim() !== '');\n        }\n        \n        // 如果没有背景图片，使用用户头像作为背景\n        if (this.backgroundImages.length === 0) {\n          const avatarUrl = this.userInfo.avatar || '/static/img/default-bg.png';\n          this.backgroundImages = [{ url: avatarUrl }];\n        }\n        \n        console.log('处理后的背景图片数组:', this.backgroundImages);\n        \n        // 重置轮播索引\n        this.currentBgIndex = 0;\n        \n        // 只有当有多张图片时才启动轮播\n        if (this.backgroundImages.length > 1) {\n          this.startCarousel();\n        } else {\n          this.clearCarouselTimer();\n        }\n      } catch (error) {\n        console.error('处理背景图片数据异常:', error);\n        // 出错时使用默认背景\n        this.backgroundImages = [{ url: this.userInfo.avatar || '/static/img/default-bg.png' }];\n        this.currentBgIndex = 0;\n        this.clearCarouselTimer();\n      }\n    },\n    // 启动背景轮播\n    startCarousel() {\n      this.clearCarouselTimer();\n      \n      // 如果有多张图片才启动轮播\n      if (this.backgroundImages.length > 1) {\n        this.carouselTimer = setInterval(() => {\n          this.nextBackground();\n        }, 4000); // 每4秒切换一次\n      }\n    },\n    // 清理轮播定时器\n    clearCarouselTimer() {\n      if (this.carouselTimer) {\n        clearInterval(this.carouselTimer);\n        this.carouselTimer = null;\n      }\n    },\n    // 下一张背景\n    nextBackground() {\n      if (this.backgroundImages.length > 0) {\n        this.currentBgIndex = (this.currentBgIndex + 1) % this.backgroundImages.length;\n      }\n  },\n    \n    /**\n     * 重试加载\n     */\n    retryLoad() {\n      this.hasError = false;\n      this.errorMessage = \"\";\n      this.page = 1;\n      this.list = [];\n      this.isEmpty = false;\n      this.loadStatus = 'loading';\n      \n      // 重新加载数据\n      this.getUserInfo();\n      this.loadDynamicList();\n    },\n    \n    /**\n     * 设置错误状态\n     */\n    setError(message) {\n      this.hasError = true;\n      this.errorMessage = message;\n      this.loadStatus = 'more';\n    },\n    \n    /**\n     * 清除错误状态\n     */\n    clearError() {\n      this.hasError = false;\n      this.errorMessage = \"\";\n    }\n  },\n  onPageScroll(e) {\n    let frontColor = \"#ffffff\"\n    let ratio = (e.scrollTop > 180 ? 180 : e.scrollTop) / 180\n    \n    if (ratio >= 1) {\n      frontColor = \"#000000\"\n    }\n    \n    this.navbarTrans = ratio\n    this.navigationBarColor(frontColor)\n  },\n  onShareAppMessage: function() {\n    return {\n      title: this.userInfo.name + \" 的个人名片\",\n      imageUrl: this.userInfo.avatar,\n      path: \"/pages/user/details?id=\" + this.userInfo.id\n    }\n  },\n  onShareTimeline() {\n    return {\n      title: this.userInfo.name + \" 的个人名片\",\n      imageUrl: this.userInfo.avatar,\n      query: \"id=\" + this.userInfo.id\n    }\n  }\n}\n</script>\n\n<style>\n.nav-box{\n  position: fixed;\n  z-index: 99;\n  top: 0;\n  left: 0;\n  width: 100%;\n  box-sizing: border-box;\n}\n.nav-box .nav-back{\n  padding: 0 30rpx;\n  width: 34rpx;\n}\n.nav-box .nav-title{\n  max-width: 60%;\n  font-size: 32rpx;\n  font-weight: 700;\n}\n.user-box{\n  width: calc(100% - 60rpx);\n  padding: 60rpx 30rpx;\n  color: #fff;\n  position: relative;\n  overflow: hidden;\n}\n.user-box .user-img, .user-box .user-bg{\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n.user-box .user-bg{\n  z-index: -1;\n  background: rgba(0, 0, 0, .5);\n}\n.user-box .user-top{\n  width: 100%;\n  justify-content: space-between;\n}\n.user-top .avatar{\n  width: 180rpx;\n  height: 180rpx;\n  border-radius: 50%;\n  background: #fff;\n  border: 2px solid #f5f5f5;\n  overflow: hidden;\n}\n.user-top .btn{\n  padding: 0 30rpx;\n  height: 64rpx;\n  line-height: 64rpx;\n  border-radius: 8rpx;\n  font-size: 20rpx;\n  font-weight: 700;\n  color: #000;\n  background: #fff;\n}\n.user-top .active{\n  color: rgba(255, 255, 255, .52);\n  background: rgba(255, 255, 255, .1);\n}\n.user-top .mutual{\n  color: #576b95;\n  background: rgba(255, 255, 255, .2);\n}\n.user-box .user-name{\n  margin: 20rpx 0 10rpx;\n  width: 100%;\n  font-size: 34rpx;\n  font-weight: 700;\n}\n.user-box .user-intro{\n  width: 100%;\n  word-break: break-word;\n  white-space: pre-line;\n}\n.user-box .user-intro text{\n  color: #ccc;\n  font-size: 24rpx;\n  font-weight: 400;\n}\n.user-box .user-tag{\n  margin: 20rpx 0;\n  width: 100%;\n}\n.user-tag .tag-item{\n  margin-right: 16rpx;\n  height: 44rpx;\n  padding: 0 14rpx;\n  border-radius: 8rpx;\n  background: rgba(255, 255, 255, .1);\n  font-weight: 500;\n  font-size: 20rpx;\n  justify-content: center;\n}\n.user-tag .tag-item image{\n  width: 24rpx;\n  height: 24rpx;\n}\n.user-num .num-item{\n  margin-right: 30rpx;\n  font-size: 20rpx;\n  font-weight: 300;\n  color: #ccc;\n}\n.user-num .num-item .t1{\n  color: #fff;\n  font-size: 28rpx;\n  font-weight: 700;\n  margin-right: 6rpx;\n}\n.content-box{\n  margin-top: -30rpx;\n  background: #fff;\n  padding: 30rpx 0;\n  border-radius: 30rpx 30rpx 0 0;\n}\n.block-box .block-title{\n  padding: 0 30rpx;\n  font-size: 26rpx;\n  font-weight: 700;\n}\n.block-box .circle-box{\n  width: calc(100% - 20rpx);\n  padding: 30rpx 10rpx;\n  display: flex;\n}\n.circle-box .circle-item{\n  flex-shrink: 0;\n  margin: 0 10rpx;\n  flex-direction: column;\n  justify-content: center;\n}\n.circle-item .circle-avatar{\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50%;\n  background: #f8f8f8;\n}\n.circle-item .circle-name{\n  margin-top: 15rpx;\n  width: 120rpx;\n  color: #999;\n  font-size: 20rpx;\n  text-align: center;\n}\n.bar-box{\n  position: -webkit-sticky;\n  position: sticky;\n  left: 0;\n  z-index: 99;\n  margin-top: -1px;\n  width: 100%;\n  height: 80rpx;\n  background: #fff;\n}\n.bar-box .bar-item{\n  padding: 0 30rpx;\n  height: 100%;\n  flex-direction: column;\n  justify-content: center;\n  position: relative;\n}\n.bar-box .bar-item text{\n  font-weight: 700;\n  transition: all .3s ease-in-out;\n}\n.bar-item .bar-line{\n  position: absolute;\n  bottom: 12rpx;\n  width: 18rpx;\n  height: 6rpx;\n  border-radius: 6rpx;\n  background: #000;\n  transition: opacity .3s ease-in-out;\n}\n.content-box .dynamic-box{\n  width: calc(100% - 16rpx);\n  padding: 22rpx 8rpx 0;\n}\n.loading-box {\n  width: 100%;\n  padding: 60rpx 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.load-more-box {\n  width: 100%;\n  padding: 30rpx 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.empty-box{\n  width: 100%;\n  padding: 100rpx 0;\n  flex-direction: column;\n}\n.empty-box image{\n  width: 300rpx;\n  height: 300rpx;\n  margin-bottom: 30rpx;\n}\n.empty-box .e1{\n  font-size: 30rpx;\n  font-weight: 700;\n}\n.empty-box .e2{\n  margin-top: 10rpx;\n  color: #999;\n  font-size: 26rpx;\n}\n.error-box{\n  width: 100%;\n  padding: 100rpx 0;\n  flex-direction: column;\n}\n.error-box image{\n  width: 300rpx;\n  height: 300rpx;\n  margin-bottom: 30rpx;\n}\n.error-box .e1{\n  font-size: 30rpx;\n  font-weight: 700;\n  color: #333;\n}\n.error-box .e2{\n  margin-top: 10rpx;\n  color: #999;\n  font-size: 26rpx;\n  text-align: center;\n  padding: 0 60rpx;\n}\n.error-box .retry-btn{\n  margin-top: 40rpx;\n  width: 200rpx;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  font-size: 28rpx;\n  font-weight: 700;\n  color: #fff;\n  background: #007aff;\n  border-radius: 40rpx;\n}\n.tips-box{\n  justify-content: center;\n  width: 100%;\n}\n.tips-box .tips-item{\n  background: #000;\n  color: #fff;\n  padding: 20rpx 40rpx;\n  border-radius: 12rpx;\n  font-size: 24rpx;\n  font-weight: 700;\n}\n.df{\n  display: flex;\n  align-items: center;\n}\n.ohto{\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.xwb{\n  filter: invert(1);\n}\n.background-carousel {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.carousel-item {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  opacity: 0;\n  transition: opacity 0.5s ease-in-out;\n}\n.carousel-item.active {\n  opacity: 1;\n}\n.carousel-indicators {\n  position: absolute;\n  bottom: 20rpx;\n  left: 0;\n  right: 0;\n  display: flex;\n  justify-content: center;\n}\n.indicator {\n  width: 12rpx;\n  height: 4rpx;\n  background-color: rgba(255, 255, 255, 0.5);\n  border-radius: 2rpx;\n  margin: 0 4rpx;\n  cursor: pointer;\n}\n.indicator.active {\n  background-color: #fff;\n}\n.default-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n.like-popup{\n  width: 400rpx;\n  background: #fff;\n  padding: 30rpx;\n  border-radius: 30rpx;\n  overflow: hidden;\n}\n.like-popup .like-img{\n  margin: 0 40rpx;\n  width: 320rpx;\n  height: 200rpx;\n}\n.like-popup .like-content{\n  margin: 20rpx 0 40rpx;\n  width: 100%;\n  color: #333;\n  font-size: 26rpx;\n  text-align: center;\n}\n.like-popup .like-btn{\n  width: 100%;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  font-size: 24rpx;\n  font-weight: 700;\n  color: #fff;\n  background: #000;\n  border-radius: 16rpx;\n}\n</style> ", "import MiniProgramPage from 'D:/uniapp/vue3/pages/user/details.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getUserHomepage", "getLikeDynamicList", "getOtherUserDynamicList"], "mappings": ";;;;;AAsLA,MAAA,YAAA,MAAA;AACA,MAAA,cAAA,MAAA;AACA,MAAA,YAAA,MAAA;AACA,MAAA,SAAA,MAAA;AACA,MAAA,YAAA,MAAA;AAIA,MAAA,YAAA;AAAA;IAEI;AAAA;IAEA;AAAA,IACA;AAAA,IACA;AAAA;EAEF,OAAA;;AACE,WAAA;AAAA;;;;MAKE,UAAA;AAAA;;QAGE,QAAA;AAAA;;QAGA,OAAA;AAAA;QAEA,QAAA;AAAA;QAEA,KAAA;AAAA,QACA,eAAA;AAAA;;QAGA,cAAA;AAAA;;QAGA,gBAAA;AAAA;QAEA,kBAAA;AAAA,QACA,QAAA,CAAA;AAAA,QACA,gBAAA;AAAA,QACA,SAAA;AAAA,UACE,MAAA;AAAA,UACA,QAAA;AAAA;QAEF,iBAAA;AAAA,QACA,UAAA,CAAA;AAAA;AAAA;AAAA,QAEA,KAAA;AAAA,QACA,QAAA;AAAA;;;QAIA,WAAA;AAAA,QACA,eAAA;AAAA,QACA,gBAAA;AAAA;;MAGF,SAAA,CAAA,MAAA,IAAA;AAAA,MACA,QAAA;AAAA,MACA,cAAA;AAAA,MACA,MAAA,CAAA;AAAA,MACA,MAAA;AAAA;MAEA,YAAA;AAAA;MAEA,aAAA;AAAA,MACA,kBAAA,CAAA;AAAA,MACA,gBAAA;AAAA,MACA,eAAA;AAAA,MACA,QAAA;AAAA,MACA,aAAA;AAAA,MACA,OAAA;AAAA,MACA,WAAA;AAAA,MACA,cAAA;AAAA;AAAA,MAEA,YAAA;AAAA,MACA,YAAA;AAAA;AAAA,MAEA,UAAA;AAAA,MACA,cAAA;AAAA,IACF;AAAA;EAEF,UAAA;AAAA,IACE,UAAA;AACE,aAAA,KAAA,UAAA;AAAA;;;;;IAOF,iBAAA;;AAGI,eAAA;AAAA,MACF,WAEA,KAAA,eAAA,SAAA,KAAA,SAAA,SAAA,MAAA,GAAA;AACE,eAAA;AAAA,MACF;AAGE,eAAA;AAAA,MACF;AAAA;;IAIF,gBAAA;;;MAIE,WAEA,KAAA,eAAA,SAAA,KAAA,SAAA,SAAA,MAAA,GAAA;;MAEA;;MAIA;AAAA,IACF;AAAA;EAEF,MAAA,OAAA,SAAA;AAEE,QAAA;AAEE,UAAA,OAAAA,cAAAA,MAAA,kBAAA,YAAA;AACEA,sBAAAA,MAAA,cAAA;AAAA,MACF;AAAA,IAEF,SAAA,GAAA;AACEA,oBAAAA,MAAA,MAAA,QAAA,iCAAA,iDAAA,CAAA;AAAA,IACF;AAGA,UAAA,KAAA;AAEAA,kBAAA,MAAA,MAAA,OAAA,iCAAA,eAAA,OAAA;AAEA,QAAA;;;;AAQI,YAAA,MAAA,KAAA,MAAA,KAAA,KAAA,UAAA,GAAA;AACEA,wBAAA,MAAA,MAAA,SAAA,iCAAA,WAAA,MAAA;AACA,eAAA,YAAA,UAAA,IAAA;AACA;AAAA,QACF;AAGA,cAAA,cAAA,KAAA,eAAAA,cAAA,MAAA,eAAA,KAAA,KAAA;;AAGAA,4BAAA,MAAA,OAAA,iCAAA,WAAA,KAAA,QAAA,SAAA,aAAA;AAGA,YAAA,eAAA;AACE,eAAA,UAAA,CAAA,MAAA,IAAA;AAAA;AAEA,eAAA,UAAA,CAAA,MAAA,IAAA;AAAA,QACF;AAGA,aAAA,YAAA;;;;AAIA,aAAA,YAAA,eAAA,IAAA;AAAA,MACF;AAAA,IACF,SAAA,GAAA;AACEA,oBAAA,MAAA,MAAA,SAAA,iCAAA,aAAA,CAAA;AACA,WAAA,YAAA,YAAA,IAAA;AAAA,IACF;AAAA;;;;EAMF,SAAA;AAAA;AAAA;AAAA;AAAA;AAKIA,oBAAA,MAAA,MAAA,OAAA,iCAAA,qBAAA,KAAA,MAAA;AAGA,UAAA,CAAA,KAAA,UAAA,KAAA,UAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,iCAAA,oBAAA,KAAA,MAAA;AACA,aAAA,YAAA,UAAA,IAAA;AACA;AAAA,MACF;AAGAA,oBAAAA,MAAA,YAAA;AAAA,QACE,OAAA;AAAA,QACA,MAAA;AAAA,MACF,CAAA;AAGAC,iCAAA;AAAA,QACE,SAAA,KAAA;AAAA;AAEAD,sBAAA,MAAA,MAAA,OAAA,iCAAA,aAAA,GAAA;;;AAME,cAAA,CAAA,KAAA,SAAA,MAAA,KAAA,SAAA,KAAA;AACE,iBAAA,SAAA,KAAA,KAAA,SAAA;AAAA,UACF;;AAIE,iBAAA,SAAA,OAAA,KAAA,SAAA;AAAA,UACF;;AAIE,iBAAA,SAAA,QAAA,KAAA,SAAA;AAAA,UACF;;AAIA,eAAA,SAAA,mBAAA,SAAA,KAAA,SAAA,oBAAA,CAAA;AAGA,eAAA,cAAA,KAAA,SAAA,cAAA;;;cAKI,MAAA;AAAA,cACA,QAAA;AAAA;UAEJ;;AAIE,iBAAA,SAAA,WAAA,IAAA,KAAA;AACAA,0BAAAA,MAAA,MAAA,OAAA,iCAAA,YAAA,IAAA,KAAA,SAAA,QAAA,GAAA;AAAA;;UAGF;;AAIE,iBAAA,SAAA,MAAA,IAAA,KAAA;AACA,iBAAA,SAAA,SAAA,IAAA,KAAA,UAAA;AACA,iBAAA,SAAA,WAAA,IAAA,KAAA,YAAA;AACA,iBAAA,SAAA,WAAA,IAAA,KAAA,YAAA;;;;;;;cAOE,KAAA,KAAA,SAAA;AAAA,cACA,UAAA,KAAA,SAAA;AAAA,cACA,YAAA,KAAA,SAAA;AAAA,cACA,WAAA,KAAA,SAAA;AAAA,YACF,CAAA;AAAA,UACF;;YAGE,IAAA,KAAA,SAAA;AAAA;YAEA,WAAA,KAAA,SAAA;AAAA;;;YAIA,eAAA,KAAA,SAAA,SAAA;AAAA,YACA,KAAA,KAAA,SAAA;AAAA,YACA,YAAA,KAAA,SAAA;AAAA,UACF,CAAA;;;YAOE,OAAA,KAAA,SAAA,QAAA;AAAA,UACF,CAAA;AAAA;AAEAA,wBAAAA,MAAA,MAAA,SAAA,iCAAA,cAAA;AACA,eAAA,YAAA,YAAA,IAAA;AAAA,QACF;AAAA,MACF,CAAA,EAAA,MAAA,SAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,iCAAA,YAAA,GAAA;;MAEF,CAAA,EAAA,QAAA,MAAA;AACEA,sBAAA,MAAA,YAAA;AAAA,MACF,CAAA;AAAA;;;;IAMF,kBAAA;;;AAGE,WAAA,YAAA;;AAIA,YAAA,aAAA,KAAA,WAAA;;;AAKA,UAAA,YAAA;AAEEA,sBAAA,MAAA,MAAA,OAAA,iCAAA,oBAAA,KAAA,MAAA;AACA,kBAAAE,WAAAA,mBAAA,KAAA,QAAA;AAAA,UACE,MAAA,KAAA;AAAA,UACA,OAAA,KAAA;AAAA,QACF,CAAA;AAAA;;AAIA,kBAAAC,WAAAA,wBAAA,KAAA,QAAA;AAAA,UACE,MAAA,KAAA;AAAA,UACA,OAAA,KAAA;AAAA,QACF,CAAA;AAAA,MACF;AAEA,cAAA,KAAA,SAAA;AACEH,sBAAA,MAAA,MAAA,OAAA,iCAAA,aAAA,GAAA;;AAGA,YAAA,KAAA,SAAA,GAAA;AACE,eAAA,OAAA;;;AAGA,eAAA,OAAA,CAAA,GAAA,KAAA,MAAA,GAAA,IAAA;AAAA,QACF;;;AAMA,YAAA,KAAA,SAAA,KAAA,KAAA,SAAA,GAAA;AAEE,gBAAA,kBAAA,KAAA;AAAA,YAAA,gDAEE,KAAA,UAAA,KAAA,OAAA,SAAA;AAAA;AAEF,eAAA,cAAA;AAAA,QACF;AAAA,MACF,CAAA,EAAA,MAAA,SAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,iCAAA,YAAA,GAAA;;;AAME,cAAA,IAAA,SAAA,OAAA,IAAA,WAAA,KAAA;AACE,iBAAA,YAAA,aAAA;AAAA;AAEAA,0BAAAA,MAAA,UAAA;AAAA,cACE,OAAA;AAAA;YAEF,CAAA;AAAA,UACF;AAAA;AAEAA,wBAAAA,MAAA,UAAA;AAAA,YACE,OAAA;AAAA;UAEF,CAAA;AAAA,QACF;AAAA,MACF,CAAA,EAAA,QAAA,MAAA;AACE,aAAA,YAAA;;MAEF,CAAA;AAAA;;;;IAMF,eAAA;AACE,UAAA,CAAA,KAAA,SAAA;AACEA,sBAAAA,MAAA,WAAA;AAAA;QAEA,CAAA;AACA;AAAA,MACF;;;;AAOA,YAAA,eAAA,SAAA,KAAA,MAAA;AAGA,UAAA,CAAA,gBAAA,gBAAA,GAAA;AACEA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;QAEF,CAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,iCAAA,mBAAA,YAAA;;AAEA;AAAA,MACF;;AAIA,YAAA,aAAA,SAAA,KAAA,SAAA,SAAA,MAAA,KAAA;AAEAA,0BAAA,MAAA,OAAA,iCAAA,WAAA,EAAA,YAAA,UAAA,aAAA,CAAA;;AAMA,UAAA,YAAA;AACE,aAAA,KAAA,KAAA,UAAA,oBAAA,CAAA;AAAA,MACF;;AAKAA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA,aAAA,aAAA;AAAA;;MAGF,CAAA;;;;;;AAQAA,0BAAA,MAAA,OAAA,iCAAA,aAAA,KAAA,UAAA,MAAA,CAAA;;;AAKE,YAAA,IAAA,WAAA,KAAA;AAEE,cAAA,IAAA,QAAA,IAAA,KAAA,cAAA,QAAA;;;AAKEA,gCAAA,MAAA,OAAA,iCAAA,aAAA,KAAA,SAAA,gBAAA;AAAA,UACF;AAGA,cAAA,IAAA,QAAA,IAAA,KAAA,eAAA,QAAA;;;;;UAMA;AAEAA,wBAAAA,MAAA,UAAA;AAAA,YACE,OAAA,aAAA,UAAA;AAAA;UAEF,CAAA;AAAA;AAGA,eAAA,SAAA,YAAA,aAAA,IAAA;;AAEE,iBAAA,SAAA,mBAAA,WAAA,IAAA;AAAA,UACF;;AAGAA,wBAAAA,MAAA,UAAA;AAAA;;UAGA,CAAA;AAAA,QACF;AAAA,MACF,CAAA,EAAA,MAAA,SAAA;AAEE,aAAA,SAAA,YAAA,aAAA,IAAA;;AAEE,eAAA,SAAA,mBAAA,WAAA,IAAA;AAAA,QACF;;AAGAA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;QAEF,CAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,iCAAA,WAAA,GAAA;AAAA,MACF,CAAA,EAAA,QAAA,MAAA;;MAEA,CAAA;AAAA;;;;;AAOA,YAAA,EAAA,IAAA,OAAA,IAAA;AAEAA,oBAAA,MAAA,MAAA,OAAA,iCAAA,UAAA,IAAA,MAAA;AAAA;;;;;AAOA,YAAA,EAAA,KAAA,KAAA,WAAA,UAAA,IAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,iCAAA,UAAA,EAAA,KAAA,KAAA,WAAA,UAAA,CAAA;AAGA,UAAA,KAAA,KAAA,GAAA,GAAA;;;;;AAOI,eAAA,KAAA,KAAA,KAAA,GAAA,EAAA,WAAA,oBAAA,SAAA;AAAA,QACF;AAAA,MACF;AAAA;;;;IAMF,oBAAA;;;;AAME,WAAA,UAAA;;;QAKE,KAAA,YAAA;AAAA,QACA,KAAA,gBAAA;AAAA,MACF,CAAA,EAAA,QAAA,MAAA;;MAEA,CAAA;AAAA;;;;IAMF,gBAAA;;AAIE,UAAA,KAAA,eAAA,UAAA,CAAA,KAAA,WAAA;;MAEA;AAAA;;;;;AASA,UAAA,cAAA,KAAA;AAAA;;AAGA,WAAA,SAAA;;AAKA,YAAA,sBAAA,KAAA,WAAA,KAAA,KAAA,WAAA,KAAA;AACA,UAAA,uBAAA,KAAA,SAAA,WAAA,KAAA,SAAA,QAAA,SAAA,GAAA;;AAEE,aAAA,UAAA;;AAEA;AAAA,MACF;AAGAA,oBAAAA,MAAA,YAAA;AAAA,QACE,OAAA;AAAA,QACA,MAAA;AAAA,MACF,CAAA;;;AAKA,WAAA,UAAA;;AAEA,WAAA,cAAA;;AAMA,iBAAA,MAAA;;AAEEA,sBAAA,MAAA,YAAA;AAAA,MACF,GAAA,GAAA;AAAA;;AAIA,WAAA,aAAA;AAAA;;AAGA,UAAA,OAAA,EAAA,cAAA,QAAA;AAEA,UAAA,KAAA,SAAA,QAAA,UAAA,GAAA;;;;QAIE;;MAEF;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,+BAAA,OAAA,SAAA,KAAA,SAAA,KAAA,WAAA,KAAA,SAAA;AAAA;;IAGJ,eAAA,MAAA;AACE,UAAA,CAAA,MAAA;;MAEA;AACA,UAAA,MAAA;;MAEA;AAAA;IAEF,cAAA,GAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,YAAA,EAAA,cAAA,QAAA;AAAA;;IAGJ,UAAA;AACE,UAAA,gBAAA,EAAA,SAAA,GAAA;AACEA,sBAAAA,MAAA,aAAA;AAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA;;MAGF;AAAA;IAEF,YAAA,KAAA,MAAA;;AAEE,WAAA,YAAA;;AAGA,iBAAA,WAAA;;AAEE,YAAA,MAAA;;QAEA;AAAA,MACF,GAAA,GAAA;AAAA;;;QAIE,YAAA;AAAA;;;UAIE,YAAA;AAAA,QACF;AAAA;;;;AAMF,WAAA,cAAA;AAAA;;AAGA,UAAA;AACEA,4BAAA,MAAA,OAAA,iCAAA,eAAA,KAAA,SAAA,eAAA;;AAMA,YAAA,KAAA,SAAA,iBAAA;AACE,cAAA,WAAA,CAAA;;AAIE,gBAAA;AAEE,kBAAA,KAAA,SAAA,gBAAA,KAAA,MAAA,MAAA,KAAA,SAAA,gBAAA,KAAA,MAAA,MAAA;;;;AAIAA,8BAAA,MAAA,MAAA,OAAA,iCAAA,iBAAA,QAAA;AAAA,cACA;AAAA;AAEAA,4BAAA,MAAA,MAAA,SAAA,iCAAA,iBAAA,KAAA;;;cAIA;AAAA,YACF;AAAA,UACF;AAGE,uBAAA,KAAA,SAAA;AAAA,UACF;AAGA,eAAA,mBAAA,SAAA,OAAA,UAAA,QAAA,KAAA,OAAA,KAAA,IAAA,KAAA,MAAA,EAAA;AAAA,QACF;AAGA,YAAA,KAAA,iBAAA,WAAA,GAAA;AACE,gBAAA,YAAA,KAAA,SAAA,UAAA;AACA,eAAA,mBAAA,CAAA,EAAA,KAAA,UAAA,CAAA;AAAA,QACF;AAEAA,sBAAA,MAAA,MAAA,OAAA,iCAAA,eAAA,KAAA,gBAAA;AAGA,aAAA,iBAAA;AAGA,YAAA,KAAA,iBAAA,SAAA,GAAA;AACE,eAAA,cAAA;AAAA;;QAGF;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,iCAAA,eAAA,KAAA;AAEA,aAAA,mBAAA,CAAA,EAAA,KAAA,KAAA,SAAA,UAAA,6BAAA,CAAA;AACA,aAAA,iBAAA;;MAEF;AAAA;;IAGF,gBAAA;;AAIE,UAAA,KAAA,iBAAA,SAAA,GAAA;AACE,aAAA,gBAAA,YAAA,MAAA;AACE,eAAA,eAAA;AAAA,QACF,GAAA,GAAA;AAAA,MACF;AAAA;;IAGF,qBAAA;;AAEI,sBAAA,KAAA,aAAA;;MAEF;AAAA;;IAGF,iBAAA;AACE,UAAA,KAAA,iBAAA,SAAA,GAAA;AACE,aAAA,kBAAA,KAAA,iBAAA,KAAA,KAAA,iBAAA;AAAA,MACF;AAAA;;;;;AAOA,WAAA,WAAA;AACA,WAAA,eAAA;;;AAGA,WAAA,UAAA;;AAIA,WAAA,YAAA;;;;;;IAOF,SAAA,SAAA;AACE,WAAA,WAAA;;;;;;;;AASA,WAAA,WAAA;AACA,WAAA,eAAA;AAAA,IACF;AAAA;EAEF,aAAA,GAAA;;AAEE,QAAA,SAAA,EAAA,YAAA,MAAA,MAAA,EAAA,aAAA;;AAGE,mBAAA;AAAA,IACF;AAEA,SAAA,cAAA;AACA,SAAA,mBAAA,UAAA;AAAA;;AAGA,WAAA;AAAA,MACE,OAAA,KAAA,SAAA,OAAA;AAAA;;IAGF;AAAA;EAEF,kBAAA;AACE,WAAA;AAAA,MACE,OAAA,KAAA,SAAA,OAAA;AAAA;;IAGF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1+BA,GAAG,WAAW,eAAe;"}