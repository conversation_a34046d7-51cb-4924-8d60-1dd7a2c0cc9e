"use strict";
const common_vendor = require("../../common/vendor.js");
const components_emojiPanel_sina = require("./sina.js");
const RECENT_EMOJIS_KEY = "recent_emojis_v2";
const _sfc_main = {
  name: "EmojiPanel",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    maxRecentCount: {
      type: Number,
      default: 9,
      validator: (value) => value > 0 && value <= 20
    },
    enablePreview: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      emojiList: components_emojiPanel_sina.sinaEmoji || [],
      recentEmojis: [],
      previewEmojiData: null
    };
  },
  watch: {
    show(val) {
      if (val) {
        this.loadRecentEmojis();
      } else {
        this.closePreview();
      }
    }
  },
  methods: {
    // 加载最近使用的表情
    loadRecentEmojis() {
      try {
        const recentEmojiStr = common_vendor.index.getStorageSync(RECENT_EMOJIS_KEY);
        if (recentEmojiStr) {
          const recentData = JSON.parse(recentEmojiStr);
          if (Array.isArray(recentData)) {
            this.recentEmojis = recentData.slice(0, this.maxRecentCount);
          }
        }
      } catch (e) {
        this.recentEmojis = [];
      }
    },
    // 保存最近使用的表情
    saveRecentEmojis() {
      try {
        common_vendor.index.setStorageSync(RECENT_EMOJIS_KEY, JSON.stringify(this.recentEmojis));
      } catch (e) {
      }
    },
    // 选择表情
    selectEmoji(emoji) {
      if (!emoji)
        return;
      common_vendor.index.hideKeyboard && common_vendor.index.hideKeyboard();
      this.closePreview();
      this.addToRecentEmojis(emoji);
      this.$emit("select", emoji);
    },
    // 添加到最近使用
    addToRecentEmojis(emoji) {
      if (!emoji)
        return;
      const existingIndex = this.recentEmojis.findIndex((item) => {
        const itemId = item.url || item.icon;
        const emojiId = emoji.url || emoji.icon;
        return itemId === emojiId;
      });
      if (existingIndex !== -1) {
        this.recentEmojis.splice(existingIndex, 1);
      }
      this.recentEmojis.unshift(emoji);
      if (this.recentEmojis.length > this.maxRecentCount) {
        this.recentEmojis = this.recentEmojis.slice(0, this.maxRecentCount);
      }
      this.saveRecentEmojis();
    },
    // 预览表情
    previewEmoji(emoji) {
      if (!this.enablePreview || !emoji)
        return;
      this.previewEmojiData = emoji;
    },
    // 关闭预览
    closePreview() {
      this.previewEmojiData = null;
    },
    // 图片加载失败
    onImageError() {
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.show
  }, $props.show ? common_vendor.e({
    b: $data.recentEmojis.length > 0
  }, $data.recentEmojis.length > 0 ? {
    c: common_vendor.f($data.recentEmojis, (emoji, index, i0) => {
      return {
        a: emoji.url || emoji.icon,
        b: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args), `recent-${index}`),
        c: `recent-${index}`,
        d: common_vendor.o(($event) => $options.selectEmoji(emoji), `recent-${index}`),
        e: common_vendor.o(($event) => $options.previewEmoji(emoji), `recent-${index}`)
      };
    })
  } : {}, {
    d: $data.recentEmojis.length > 0
  }, $data.recentEmojis.length > 0 ? {} : {}, {
    e: common_vendor.f($data.emojiList, (emoji, index, i0) => {
      return {
        a: emoji.url || emoji.icon,
        b: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args), `emoji-${index}`),
        c: `emoji-${index}`,
        d: common_vendor.o(($event) => $options.selectEmoji(emoji), `emoji-${index}`),
        e: common_vendor.o(($event) => $options.previewEmoji(emoji), `emoji-${index}`)
      };
    }),
    f: $data.previewEmojiData
  }, $data.previewEmojiData ? {
    g: $data.previewEmojiData.url || $data.previewEmojiData.icon,
    h: common_vendor.t($data.previewEmojiData.phrase),
    i: common_vendor.o(($event) => $options.selectEmoji($data.previewEmojiData)),
    j: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args)),
    k: common_vendor.o(() => {
    }),
    l: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args))
  } : {}) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/emoji-panel/emoji-panel.js.map
