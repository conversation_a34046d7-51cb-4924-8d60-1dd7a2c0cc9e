{"version": 3, "file": "circlemember.js", "sources": ["pages/note/circlemember.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbm90ZS9jaXJjbGVtZW1iZXIudnVl"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 搜索框 -->\n    <view class=\"search-box df\">\n      <image src=\"/static/img/search.png\" class=\"search-icon\"></image>\n      <input\n        class=\"search-input\"\n        placeholder=\"输入昵称搜索成员\"\n        v-model=\"search\"\n        @input=\"onSearchInput\"\n        @confirm=\"onSearchConfirm\"\n      />\n      <view v-if=\"search\" class=\"clear-btn\" @tap=\"clearSearch\">\n        <image src=\"/static/img/close.png\" class=\"clear-icon\"></image>\n      </view>\n    </view>\n\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-container\">\n      <view class=\"loading-spinner\"></view>\n      <text class=\"loading-text\">加载中...</text>\n    </view>\n    <!-- 内容区域 -->\n    <view v-if=\"!loading\" class=\"content-area\">\n      <!-- 空状态 -->\n      <view v-if=\"isEmpty\" class=\"empty-state\">\n        <image src=\"/static/img/empty.png\" class=\"empty-icon\"></image>\n        <text class=\"empty-text\">{{ search ? '未找到相关成员' : '暂无成员' }}</text>\n        <text v-if=\"search\" class=\"empty-tip\">试试其他关键词</text>\n      </view>\n\n      <!-- 管理团队 -->\n      <view v-if=\"adminList.length > 0\" class=\"section\">\n        <view class=\"section-title\">\n          <text>管理团队</text>\n          <text class=\"member-count\">({{ adminList.length }})</text>\n        </view>\n        <view class=\"member-list\">\n          <view\n            class=\"member-item df\"\n            v-for=\"(user, idx) in adminList\"\n            :key=\"'admin-'+idx\"\n            @tap=\"goToUserProfile(user.uid)\"\n          >\n            <image :src=\"user.user_avatar || '/static/img/avatar.png'\" class=\"avatar\" mode=\"aspectFill\"></image>\n            <view class=\"info\">\n              <view class=\"name df\">\n                <text>{{ user.user_nickname || '未知用户' }}</text>\n                <view v-if=\"user.role==='圈主'\" class=\"role-tag owner\">圈主</view>\n                <view v-else-if=\"user.role==='管理员'\" class=\"role-tag admin\">管理员</view>\n                <view v-if=\"user.is_mute === 1\" class=\"status-tag muted\">已禁言</view>\n              </view>\n              <view class=\"meta df\">\n                <text v-if=\"user.gender\" :class=\"['gender', user.gender==='男' ? 'male' : 'female']\">\n                  {{ user.gender==='男'?'♂':'♀' }}\n                </text>\n                <text v-if=\"user.age\" class=\"age\">{{ user.age }}岁</text>\n                <text class=\"contrib\">加入时间：{{ formatJoinTime(user.join_time) }}</text>\n              </view>\n            </view>\n            <!-- 操作按钮 -->\n            <view\n              v-if=\"user.uid !== getCurrentUserId() && isManager && canManage(user)\"\n              class=\"action-buttons\"\n              @tap.stop=\"showActionMenu(user)\"\n            >\n              <view class=\"action-btn\">\n                <text>管理</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 普通成员 -->\n      <view v-if=\"memberList.length > 0\" class=\"section\">\n        <view class=\"section-title\">\n          <text>普通成员</text>\n          <text class=\"member-count\">({{ memberList.length }})</text>\n        </view>\n        <view class=\"member-list\">\n          <view\n            class=\"member-item df\"\n            v-for=\"(user, idx) in memberList\"\n            :key=\"'member-'+idx\"\n            @tap=\"goToUserProfile(user.uid)\"\n          >\n            <image :src=\"user.user_avatar || '/static/img/avatar.png'\" class=\"avatar\" mode=\"aspectFill\"></image>\n            <view class=\"info\">\n              <view class=\"name df\">\n                <text>{{ user.user_nickname || '未知用户' }}</text>\n                <view v-if=\"user.is_mute === 1\" class=\"status-tag muted\">已禁言</view>\n              </view>\n              <view class=\"meta df\">\n                <text v-if=\"user.gender\" :class=\"['gender', user.gender==='男' ? 'male' : 'female']\">\n                  {{ user.gender==='男'?'♂':'♀' }}\n                </text>\n                <text v-if=\"user.age\" class=\"age\">{{ user.age }}岁</text>\n                <text class=\"contrib\">加入时间：{{ formatJoinTime(user.join_time) }}</text>\n              </view>\n            </view>\n            <!-- 操作按钮 -->\n            <view\n              v-if=\"user.uid !== getCurrentUserId() && isManager\"\n              class=\"action-buttons\"\n              @tap.stop=\"showActionMenu(user)\"\n            >\n              <view class=\"action-btn\">\n                <text>管理</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted, onUnmounted, watch, getCurrentInstance } from 'vue'\nimport { getCircleMemberList, kickCircleMember, setCircleMemberRole, muteCircleMember, unmuteCircleMember } from '@/api/social.js'\n\n// 获取当前实例\nconst { proxy } = getCurrentInstance()\n\n// 响应式数据\nconst search = ref('')\nconst adminList = ref([])\nconst memberList = ref([])\nconst circleId = ref(null)\nconst allMembers = ref([]) // 用于搜索\nconst currentUser = ref(null) // 当前用户信息\nconst selectedMember = ref(null) // 选中的成员\nconst isManager = ref(false) // 是否有管理权限\nconst loading = ref(false) // 加载状态\nconst searchTimer = ref(null) // 搜索防抖定时器\nconst cacheKey = ref('') // 缓存键\nconst lastUpdateTime = ref(0) // 最后更新时间\n// 计算属性\nconst isEmpty = computed(() => {\n  return !loading.value && adminList.value.length === 0 && memberList.value.length === 0\n})\n\nconst totalMembers = computed(() => {\n  return adminList.value.length + memberList.value.length\n})\n\n// 页面加载\nonMounted(() => {\n  const pages = getCurrentPages()\n  const currentPage = pages[pages.length - 1]\n  const options = currentPage.options\n\n  circleId.value = options.id\n  cacheKey.value = `circle_members_${circleId.value}`\n  getCurrentUser()\n  checkCache()\n  fetchMembers()\n})\n// 监听搜索变化\nwatch(search, (val) => {\n  // 防抖处理搜索\n  if (searchTimer.value) {\n    clearTimeout(searchTimer.value)\n  }\n  searchTimer.value = setTimeout(() => {\n    filterMembers(val)\n  }, 300)\n})\n// 检查缓存\nconst checkCache = () => {\n  try {\n    const cached = uni.getStorageSync(cacheKey.value)\n    const now = Date.now()\n\n    // 缓存有效期3分钟\n    if (cached && cached.timestamp && (now - cached.timestamp < 3 * 60 * 1000)) {\n      adminList.value = cached.data.adminList || []\n      memberList.value = cached.data.memberList || []\n      allMembers.value = cached.data.allMembers || { adminList: [], memberList: [] }\n      lastUpdateTime.value = cached.timestamp\n      checkManagerRole()\n    }\n  } catch (e) {\n    console.warn('读取成员缓存失败:', e)\n  }\n}\n\n// 更新缓存\nconst updateCache = () => {\n  try {\n    const cacheData = {\n      data: {\n        adminList: adminList.value,\n        memberList: memberList.value,\n        allMembers: allMembers.value\n      },\n      timestamp: Date.now()\n    }\n    uni.setStorageSync(cacheKey.value, cacheData)\n    lastUpdateTime.value = cacheData.timestamp\n  } catch (e) {\n    console.warn('更新成员缓存失败:', e)\n  }\n}\n\n// 搜索输入处理\nconst onSearchInput = (e) => {\n  search.value = e.detail.value\n}\n\n// 搜索确认\nconst onSearchConfirm = () => {\n  filterMembers(search.value)\n}\n\n// 清除搜索\nconst clearSearch = () => {\n  search.value = ''\n  filterMembers('')\n}\n\n\n// 跳转到用户详情\nconst goToUserProfile = (uid) => {\n  uni.navigateTo({\n    url: `/pages/user/details?id=${uid}`\n  })\n}\n\n// 获取当前用户信息\nconst getCurrentUser = () => {\n  const userInfo = uni.getStorageSync('USER_INFO')\n  const vuexUid = proxy.$store.state.app.uid\n  currentUser.value = {\n    uid: vuexUid || (userInfo && userInfo.uid) || 0\n  }\n}\n\n// 获取当前用户ID\nconst getCurrentUserId = () => {\n  return currentUser.value ? currentUser.value.uid : 0\n}\n// 检查当前用户是否为圈主/管理员\nconst checkManagerRole = () => {\n  const userId = getCurrentUserId()\n  const admin = adminList.value.find(u => u.uid === userId)\n  isManager.value = admin && (admin.role_type === 2 || admin.role_type === 3)\n\n  // 存储当前用户的角色信息\n  if (admin) {\n    currentUser.value.role_type = admin.role_type\n    currentUser.value.role = admin.role\n  }\n}\n\n// 检查是否可以管理某个成员\nconst canManage = (member) => {\n  const currentUserId = getCurrentUserId()\n  const currentUserRole = currentUser.value?.role_type || 0\n\n  // 不能管理自己\n  if (member.uid === currentUserId) {\n    return false\n  }\n\n  // 圈主可以管理所有人\n  if (currentUserRole === 3) {\n    return true\n  }\n\n  // 管理员只能管理普通成员\n  if (currentUserRole === 2 && member.role_type === 1) {\n    return true\n  }\n\n  return false\n}\n// 显示操作菜单\nconst showActionMenu = (member) => {\n  selectedMember.value = member\n  const actions = []\n\n  // 根据成员角色显示不同操作\n  if (member.role_type === 1) { // 普通成员\n    actions.push('设为管理员')\n  } else if (member.role_type === 2) { // 管理员\n    actions.push('取消管理员')\n  }\n\n  // 根据禁言状态显示对应操作\n  if (member.is_mute === 1) {\n    actions.push('解除禁言')\n  } else {\n    actions.push('禁言')\n  }\n\n  // 踢出圈子操作\n  actions.push('踢出圈子')\n\n  uni.showActionSheet({\n    itemList: actions,\n    success: (res) => {\n      const action = actions[res.tapIndex]\n      handleMemberAction(action, member)\n    }\n  })\n}\n// 处理成员操作\nconst handleMemberAction = async (action, member) => {\n  try {\n    switch (action) {\n      case '设为管理员':\n        await setMemberRole(member.uid, 2)\n        break\n      case '取消管理员':\n        await setMemberRole(member.uid, 1)\n        break\n      case '禁言':\n        await muteMember(member.uid)\n        break\n      case '解除禁言':\n        await unmuteMember(member.uid)\n        break\n      case '踢出圈子':\n        await kickMember(member.uid)\n        break\n    }\n  } catch (e) {\n    uni.showToast({ title: '操作失败', icon: 'none' })\n  }\n}\n// 设置成员角色\nconst setMemberRole = async (uid, roleType) => {\n  uni.showModal({\n    title: '确认操作',\n    content: roleType === 2 ? '确定要设为管理员吗？' : '确定要取消管理员权限吗？',\n    success: async (res) => {\n      if (res.confirm) {\n        try {\n          const result = await setCircleMemberRole({\n            circle_id: parseInt(circleId.value),\n            target_uid: parseInt(uid),\n            role_type: parseInt(roleType)\n          })\n\n          if (result.status === 200) {\n            uni.showToast({ title: '操作成功', icon: 'success' })\n            fetchMembers() // 刷新成员列表\n          } else {\n            uni.showToast({ title: result.msg || '操作失败', icon: 'none' })\n          }\n        } catch (e) {\n          uni.showToast({ title: '网络错误', icon: 'none' })\n        }\n      }\n    }\n  })\n}\n// 禁言成员\nconst muteMember = async (uid) => {\n  // 显示禁言原因选择\n  uni.showActionSheet({\n    itemList: ['发布违规内容', '恶意刷屏', '人身攻击', '其他原因'],\n    success: (res) => {\n      const reasons = ['发布违规内容', '恶意刷屏', '人身攻击', '其他原因']\n      const selectedReason = reasons[res.tapIndex]\n\n      if (selectedReason === '其他原因') {\n        // 弹出输入框让用户输入自定义原因\n        uni.showModal({\n          title: '输入禁言原因',\n          editable: true,\n          placeholderText: '请输入禁言原因',\n          success: async (modalRes) => {\n            if (modalRes.confirm && modalRes.content) {\n              await executeMuteMember(uid, modalRes.content)\n            }\n          }\n        })\n      } else {\n        executeMuteMember(uid, selectedReason)\n      }\n    }\n  })\n}\n// 执行禁言操作\nconst executeMuteMember = async (uid, reason) => {\n  try {\n    const result = await muteCircleMember({\n      circle_id: parseInt(circleId.value),\n      target_uid: parseInt(uid),\n      mute_days: parseInt(1),\n      reason: reason\n    })\n\n    if (result.status === 200) {\n      uni.showToast({ title: '禁言成功', icon: 'success' })\n      fetchMembers()\n    } else {\n      uni.showToast({ title: result.msg || '禁言失败', icon: 'none' })\n    }\n  } catch (e) {\n    uni.showToast({ title: '网络错误', icon: 'none' })\n  }\n}\n// 解除禁言\nconst unmuteMember = async (uid) => {\n  try {\n    const result = await unmuteCircleMember({\n      circle_id: parseInt(circleId.value),\n      target_uid: parseInt(uid)\n    })\n\n    if (result.status === 200) {\n      uni.showToast({ title: '解除禁言成功', icon: 'success' })\n      fetchMembers()\n    } else {\n      uni.showToast({ title: result.msg || '操作失败', icon: 'none' })\n    }\n  } catch (e) {\n    uni.showToast({ title: '网络错误', icon: 'none' })\n  }\n}\n// 踢出成员\nconst kickMember = async (uid) => {\n  // 显示踢出原因选择\n  uni.showActionSheet({\n    itemList: ['严重违规', '恶意刷屏', '人身攻击', '其他原因'],\n    success: (res) => {\n      const reasons = ['严重违规', '恶意刷屏', '人身攻击', '其他原因']\n      const selectedReason = reasons[res.tapIndex]\n\n      if (selectedReason === '其他原因') {\n        // 弹出输入框让用户输入自定义原因\n        uni.showModal({\n          title: '输入踢出原因',\n          editable: true,\n          placeholderText: '请输入踢出原因',\n          success: async (modalRes) => {\n            if (modalRes.confirm && modalRes.content) {\n              await executeKickMember(uid, modalRes.content)\n            }\n          }\n        })\n      } else {\n        executeKickMember(uid, selectedReason)\n      }\n    }\n  })\n}\n// 执行踢出操作\nconst executeKickMember = async (uid, reason) => {\n  uni.showModal({\n    title: '确认踢出',\n    content: `确定要踢出该成员吗？原因：${reason}\\n此操作不可撤销。`,\n    confirmColor: '#FA5150',\n    success: async (res) => {\n      if (res.confirm) {\n        try {\n          const result = await kickCircleMember({\n            circle_id: parseInt(circleId.value),\n            target_uid: parseInt(uid),\n            reason: reason\n          })\n\n          if (result.status === 200) {\n            uni.showToast({ title: '踢出成功', icon: 'success' })\n            fetchMembers()\n          } else {\n            uni.showToast({ title: result.msg || '踢出失败', icon: 'none' })\n          }\n        } catch (e) {\n          uni.showToast({ title: '网络错误', icon: 'none' })\n        }\n      }\n    }\n  })\n}\n// 获取成员列表\nconst fetchMembers = async () => {\n  try {\n    loading.value = true\n\n    const res = await getCircleMemberList({\n      circle_id: circleId.value,\n      page: 1,\n      limit: 500 // 增加限制数量\n    })\n\n    if (res.status === 200 && res.data && res.data.list) {\n      const list = res.data.list\n\n      // 圈主、管理员、普通成员分组\n      const adminListData = []\n      const memberListData = []\n\n      list.forEach(user => {\n        // 确保用户数据完整性\n        const userData = {\n          ...user,\n          user_avatar: user.user_avatar || '/static/img/avatar.png',\n          user_nickname: user.user_nickname || '未知用户',\n          gender: user.gender || '',\n          age: user.age || '',\n          join_time: user.join_time || '',\n          is_mute: user.is_mute || 0\n        }\n\n        if (user.role_type === 3) {\n          adminListData.unshift({ ...userData, role: '圈主' })\n        } else if (user.role_type === 2) {\n          adminListData.push({ ...userData, role: '管理员' })\n        } else {\n          memberListData.push(userData)\n        }\n      })\n\n      adminList.value = adminListData\n      memberList.value = memberListData\n      allMembers.value = { adminList: adminListData, memberList: memberListData }\n\n      // 更新缓存\n      updateCache()\n\n      // 更新管理权限\n      checkManagerRole()\n    } else {\n      throw new Error(res.msg || '获取成员列表失败')\n    }\n  } catch (e) {\n    console.error('获取成员列表失败:', e)\n    handleError(e, '成员加载失败')\n  } finally {\n    loading.value = false\n  }\n}\n// 格式化加入时间\nconst formatJoinTime = (timeStr) => {\n  if (!timeStr) return ''\n  const date = new Date(timeStr)\n  const year = date.getFullYear()\n  const month = String(date.getMonth() + 1).padStart(2, '0')\n  const day = String(date.getDate()).padStart(2, '0')\n  return `${year}-${month}-${day}`\n}\n// 统一错误处理\nconst handleError = (error, defaultMessage = '操作失败') => {\n  console.error('错误处理:', error)\n\n  let message = defaultMessage\n\n  if (typeof error === 'string') {\n    message = error\n  } else if (error && typeof error === 'object') {\n    if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network')) {\n      message = '网络连接异常，请检查网络设置'\n    } else if (error.code === 'TIMEOUT' || error.message?.includes('timeout')) {\n      message = '请求超时，请稍后重试'\n    } else {\n      message = error.msg || error.message || error.data?.msg || defaultMessage\n    }\n  }\n\n  uni.showToast({\n    title: message,\n    icon: 'none',\n    duration: 2000\n  })\n\n  return message\n}\n\n// 优化的搜索过滤\nconst filterMembers = (val) => {\n  if (!val || !val.trim()) {\n    // 恢复原始分组\n    if (allMembers.value.adminList && allMembers.value.memberList) {\n      adminList.value = allMembers.value.adminList\n      memberList.value = allMembers.value.memberList\n    }\n    return\n  }\n\n  const searchTerm = val.trim().toLowerCase()\n\n  const filter = (arr) => arr.filter(u => {\n    const nickname = (u.user_nickname || '').toLowerCase()\n    const uid = String(u.uid || '')\n\n    return nickname.includes(searchTerm) || uid.includes(searchTerm)\n  })\n\n  adminList.value = filter(allMembers.value.adminList || [])\n  memberList.value = filter(allMembers.value.memberList || [])\n}\n\n// 页面卸载时清理定时器\nonUnmounted(() => {\n  if (searchTimer.value) {\n    clearTimeout(searchTimer.value)\n    searchTimer.value = null\n  }\n})\n</script>\n\n<style>\n.container {\n  background: #fff;\n  min-height: 100vh;\n  padding-bottom: 40rpx;\n}\n\n.search-box {\n  margin: 30rpx 30rpx 30rpx 30rpx;\n  background: #f6f7fa;\n  border-radius: 30rpx;\n  height: 60rpx;\n  align-items: center;\n  padding: 0 20rpx;\n}\n.search-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 10rpx;\n}\n.search-input {\n  flex: 1;\n  border: none;\n  background: transparent;\n  font-size: 26rpx;\n  color: #333;\n}\n.clear-btn {\n  width: 32rpx;\n  height: 32rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 10rpx;\n}\n.clear-icon {\n  width: 20rpx;\n  height: 20rpx;\n}\n/* 加载状态 */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 0;\n}\n.loading-spinner {\n  width: 60rpx;\n  height: 60rpx;\n  border: 4rpx solid #f3f3f3;\n  border-top: 4rpx solid #007aff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n.loading-text {\n  margin-top: 20rpx;\n  font-size: 26rpx;\n  color: #999;\n}\n\n/* 空状态 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 40rpx;\n}\n.empty-icon {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 30rpx;\n  opacity: 0.6;\n}\n.empty-text {\n  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n}\n.empty-tip {\n  font-size: 24rpx;\n  color: #999;\n}\n\n/* 内容区域 */\n.content-area {\n  min-height: 60vh;\n}\n\n.section {\n  margin-bottom: 40rpx;\n}\n\n.section-title {\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #222;\n  margin: 30rpx 0 18rpx 30rpx;\n  display: flex;\n  align-items: center;\n}\n.member-count {\n  font-size: 24rpx;\n  color: #999;\n  font-weight: normal;\n  margin-left: 8rpx;\n}\n.member-list {\n  margin: 0;\n}\n.member-item {\n  padding: 5rpx 25rpx;\n  display: flex;\n  align-items: center;\n  position: relative;\n  min-height: 100rpx;\n}\n\n.avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  margin-right: 20rpx;\n  background: #f5f5f5;\n  flex-shrink: 0;\n}\n.info {\n  flex: 1;\n  min-width: 0;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n.name {\n  font-size: 28rpx;\n  font-weight: 700;\n  color: #222;\n  display: flex;\n  align-items: center;\n  margin-bottom: 6rpx;\n}\n.role-tag {\n  font-size: 20rpx;\n  border-radius: 8rpx;\n  padding: 2rpx 14rpx;\n  margin-left: 12rpx;\n  color: #fff;\n  font-weight: 400;\n}\n.role-tag.owner {\n  background: #ff9500;\n}\n.role-tag.admin {\n  background: #3da0ff;\n}\n.status-tag {\n  font-size: 20rpx;\n  border-radius: 8rpx;\n  padding: 2rpx 14rpx;\n  margin-left: 12rpx;\n  font-weight: 400;\n}\n.status-tag.muted {\n  background: #ff4757;\n  color: #fff;\n}\n.meta {\n  font-size: 22rpx;\n  color: #999;\n  display: flex;\n  align-items: center;\n  line-height: 1.4;\n}\n.gender {\n  margin-right: 6rpx;\n}\n.male {\n  color: #4e6ef2;\n}\n.female {\n  color: #fa5a8a;\n}\n.age {\n  margin-right: 12rpx;\n}\n.contrib {\n  color: #999;\n}\n.action-buttons {\n  margin-left: 20rpx;\n  flex-shrink: 0;\n}\n.action-btn {\n  background: #007aff;\n  border-radius: 16rpx;\n  padding: 6rpx 16rpx;\n  font-size: 22rpx;\n  color: #fff;\n  min-width: 60rpx;\n  text-align: center;\n}\n.action-btn:active {\n  background: #0056cc;\n}\n.df {\n  display: flex;\n  align-items: center;\n}\n</style>\n", "import MiniProgramPage from 'D:/uniapp/vue3/pages/note/circlemember.vue'\nwx.createPage(MiniProgramPage)"], "names": ["getCurrentInstance", "ref", "computed", "onMounted", "watch", "uni", "setCircleMemberRole", "muteCircleMember", "unmuteCircleMember", "kickCircleMember", "getCircleMemberList", "onUnmounted", "MiniProgramPage"], "mappings": ";;;;;;;AA2HA,UAAM,EAAE,MAAO,IAAGA,iCAAoB;AAGtC,UAAM,SAASC,cAAG,IAAC,EAAE;AACrB,UAAM,YAAYA,cAAG,IAAC,EAAE;AACxB,UAAM,aAAaA,cAAG,IAAC,EAAE;AACzB,UAAM,WAAWA,cAAG,IAAC,IAAI;AACzB,UAAM,aAAaA,cAAG,IAAC,EAAE;AACzB,UAAM,cAAcA,cAAG,IAAC,IAAI;AAC5B,UAAM,iBAAiBA,cAAG,IAAC,IAAI;AAC/B,UAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,UAAM,UAAUA,cAAG,IAAC,KAAK;AACzB,UAAM,cAAcA,cAAG,IAAC,IAAI;AAC5B,UAAM,WAAWA,cAAG,IAAC,EAAE;AACvB,UAAM,iBAAiBA,cAAG,IAAC,CAAC;AAE5B,UAAM,UAAUC,cAAQ,SAAC,MAAM;AAC7B,aAAO,CAAC,QAAQ,SAAS,UAAU,MAAM,WAAW,KAAK,WAAW,MAAM,WAAW;AAAA,IACvF,CAAC;AAEoBA,kBAAAA,SAAS,MAAM;AAClC,aAAO,UAAU,MAAM,SAAS,WAAW,MAAM;AAAA,IACnD,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACd,YAAM,QAAQ,gBAAiB;AAC/B,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,UAAU,YAAY;AAE5B,eAAS,QAAQ,QAAQ;AACzB,eAAS,QAAQ,kBAAkB,SAAS,KAAK;AACjD,qBAAgB;AAChB,iBAAY;AACZ,mBAAc;AAAA,IAChB,CAAC;AAEDC,kBAAAA,MAAM,QAAQ,CAAC,QAAQ;AAErB,UAAI,YAAY,OAAO;AACrB,qBAAa,YAAY,KAAK;AAAA,MAC/B;AACD,kBAAY,QAAQ,WAAW,MAAM;AACnC,sBAAc,GAAG;AAAA,MAClB,GAAE,GAAG;AAAA,IACR,CAAC;AAED,UAAM,aAAa,MAAM;AACvB,UAAI;AACF,cAAM,SAASC,cAAG,MAAC,eAAe,SAAS,KAAK;AAChD,cAAM,MAAM,KAAK,IAAK;AAGtB,YAAI,UAAU,OAAO,aAAc,MAAM,OAAO,YAAY,IAAI,KAAK,KAAO;AAC1E,oBAAU,QAAQ,OAAO,KAAK,aAAa,CAAE;AAC7C,qBAAW,QAAQ,OAAO,KAAK,cAAc,CAAE;AAC/C,qBAAW,QAAQ,OAAO,KAAK,cAAc,EAAE,WAAW,CAAA,GAAI,YAAY,GAAI;AAC9E,yBAAe,QAAQ,OAAO;AAC9B,2BAAkB;AAAA,QACnB;AAAA,MACF,SAAQ,GAAG;AACVA,sBAAAA,0DAAa,aAAa,CAAC;AAAA,MAC5B;AAAA,IACH;AAGA,UAAM,cAAc,MAAM;AACxB,UAAI;AACF,cAAM,YAAY;AAAA,UAChB,MAAM;AAAA,YACJ,WAAW,UAAU;AAAA,YACrB,YAAY,WAAW;AAAA,YACvB,YAAY,WAAW;AAAA,UACxB;AAAA,UACD,WAAW,KAAK,IAAK;AAAA,QACtB;AACDA,sBAAAA,MAAI,eAAe,SAAS,OAAO,SAAS;AAC5C,uBAAe,QAAQ,UAAU;AAAA,MAClC,SAAQ,GAAG;AACVA,sBAAAA,0DAAa,aAAa,CAAC;AAAA,MAC5B;AAAA,IACH;AAGA,UAAM,gBAAgB,CAAC,MAAM;AAC3B,aAAO,QAAQ,EAAE,OAAO;AAAA,IAC1B;AAGA,UAAM,kBAAkB,MAAM;AAC5B,oBAAc,OAAO,KAAK;AAAA,IAC5B;AAGA,UAAM,cAAc,MAAM;AACxB,aAAO,QAAQ;AACf,oBAAc,EAAE;AAAA,IAClB;AAIA,UAAM,kBAAkB,CAAC,QAAQ;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,0BAA0B,GAAG;AAAA,MACtC,CAAG;AAAA,IACH;AAGA,UAAM,iBAAiB,MAAM;AAC3B,YAAM,WAAWA,cAAAA,MAAI,eAAe,WAAW;AAC/C,YAAM,UAAU,MAAM,OAAO,MAAM,IAAI;AACvC,kBAAY,QAAQ;AAAA,QAClB,KAAK,WAAY,YAAY,SAAS,OAAQ;AAAA,MAC/C;AAAA,IACH;AAGA,UAAM,mBAAmB,MAAM;AAC7B,aAAO,YAAY,QAAQ,YAAY,MAAM,MAAM;AAAA,IACrD;AAEA,UAAM,mBAAmB,MAAM;AAC7B,YAAM,SAAS,iBAAkB;AACjC,YAAM,QAAQ,UAAU,MAAM,KAAK,OAAK,EAAE,QAAQ,MAAM;AACxD,gBAAU,QAAQ,UAAU,MAAM,cAAc,KAAK,MAAM,cAAc;AAGzE,UAAI,OAAO;AACT,oBAAY,MAAM,YAAY,MAAM;AACpC,oBAAY,MAAM,OAAO,MAAM;AAAA,MAChC;AAAA,IACH;AAGA,UAAM,YAAY,CAAC,WAAW;;AAC5B,YAAM,gBAAgB,iBAAkB;AACxC,YAAM,oBAAkB,iBAAY,UAAZ,mBAAmB,cAAa;AAGxD,UAAI,OAAO,QAAQ,eAAe;AAChC,eAAO;AAAA,MACR;AAGD,UAAI,oBAAoB,GAAG;AACzB,eAAO;AAAA,MACR;AAGD,UAAI,oBAAoB,KAAK,OAAO,cAAc,GAAG;AACnD,eAAO;AAAA,MACR;AAED,aAAO;AAAA,IACT;AAEA,UAAM,iBAAiB,CAAC,WAAW;AACjC,qBAAe,QAAQ;AACvB,YAAM,UAAU,CAAE;AAGlB,UAAI,OAAO,cAAc,GAAG;AAC1B,gBAAQ,KAAK,OAAO;AAAA,MACxB,WAAa,OAAO,cAAc,GAAG;AACjC,gBAAQ,KAAK,OAAO;AAAA,MACrB;AAGD,UAAI,OAAO,YAAY,GAAG;AACxB,gBAAQ,KAAK,MAAM;AAAA,MACvB,OAAS;AACL,gBAAQ,KAAK,IAAI;AAAA,MAClB;AAGD,cAAQ,KAAK,MAAM;AAEnBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AAChB,gBAAM,SAAS,QAAQ,IAAI,QAAQ;AACnC,6BAAmB,QAAQ,MAAM;AAAA,QAClC;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,qBAAqB,OAAO,QAAQ,WAAW;AACnD,UAAI;AACF,gBAAQ,QAAM;AAAA,UACZ,KAAK;AACH,kBAAM,cAAc,OAAO,KAAK,CAAC;AACjC;AAAA,UACF,KAAK;AACH,kBAAM,cAAc,OAAO,KAAK,CAAC;AACjC;AAAA,UACF,KAAK;AACH,kBAAM,WAAW,OAAO,GAAG;AAC3B;AAAA,UACF,KAAK;AACH,kBAAM,aAAa,OAAO,GAAG;AAC7B;AAAA,UACF,KAAK;AACH,kBAAM,WAAW,OAAO,GAAG;AAC3B;AAAA,QACH;AAAA,MACF,SAAQ,GAAG;AACVA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,MAC9C;AAAA,IACH;AAEA,UAAM,gBAAgB,OAAO,KAAK,aAAa;AAC7CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,aAAa,IAAI,eAAe;AAAA,QACzC,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAI;AACF,oBAAM,SAAS,MAAMC,+BAAoB;AAAA,gBACvC,WAAW,SAAS,SAAS,KAAK;AAAA,gBAClC,YAAY,SAAS,GAAG;AAAA,gBACxB,WAAW,SAAS,QAAQ;AAAA,cACxC,CAAW;AAED,kBAAI,OAAO,WAAW,KAAK;AACzBD,8BAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,WAAW;AAChD,6BAAc;AAAA,cAC1B,OAAiB;AACLA,oCAAI,UAAU,EAAE,OAAO,OAAO,OAAO,QAAQ,MAAM,QAAQ;AAAA,cAC5D;AAAA,YACF,SAAQ,GAAG;AACVA,4BAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,YAC9C;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,aAAa,OAAO,QAAQ;AAEhCA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,UAAU,QAAQ,QAAQ,MAAM;AAAA,QAC3C,SAAS,CAAC,QAAQ;AAChB,gBAAM,UAAU,CAAC,UAAU,QAAQ,QAAQ,MAAM;AACjD,gBAAM,iBAAiB,QAAQ,IAAI,QAAQ;AAE3C,cAAI,mBAAmB,QAAQ;AAE7BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,UAAU;AAAA,cACV,iBAAiB;AAAA,cACjB,SAAS,OAAO,aAAa;AAC3B,oBAAI,SAAS,WAAW,SAAS,SAAS;AACxC,wBAAM,kBAAkB,KAAK,SAAS,OAAO;AAAA,gBAC9C;AAAA,cACF;AAAA,YACX,CAAS;AAAA,UACT,OAAa;AACL,8BAAkB,KAAK,cAAc;AAAA,UACtC;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,oBAAoB,OAAO,KAAK,WAAW;AAC/C,UAAI;AACF,cAAM,SAAS,MAAME,4BAAiB;AAAA,UACpC,WAAW,SAAS,SAAS,KAAK;AAAA,UAClC,YAAY,SAAS,GAAG;AAAA,UACxB,WAAW,SAAS,CAAC;AAAA,UACrB;AAAA,QACN,CAAK;AAED,YAAI,OAAO,WAAW,KAAK;AACzBF,wBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,WAAW;AAChD,uBAAc;AAAA,QACpB,OAAW;AACLA,8BAAI,UAAU,EAAE,OAAO,OAAO,OAAO,QAAQ,MAAM,QAAQ;AAAA,QAC5D;AAAA,MACF,SAAQ,GAAG;AACVA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,MAC9C;AAAA,IACH;AAEA,UAAM,eAAe,OAAO,QAAQ;AAClC,UAAI;AACF,cAAM,SAAS,MAAMG,8BAAmB;AAAA,UACtC,WAAW,SAAS,SAAS,KAAK;AAAA,UAClC,YAAY,SAAS,GAAG;AAAA,QAC9B,CAAK;AAED,YAAI,OAAO,WAAW,KAAK;AACzBH,wBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,WAAW;AAClD,uBAAc;AAAA,QACpB,OAAW;AACLA,8BAAI,UAAU,EAAE,OAAO,OAAO,OAAO,QAAQ,MAAM,QAAQ;AAAA,QAC5D;AAAA,MACF,SAAQ,GAAG;AACVA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,MAC9C;AAAA,IACH;AAEA,UAAM,aAAa,OAAO,QAAQ;AAEhCA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACzC,SAAS,CAAC,QAAQ;AAChB,gBAAM,UAAU,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAC/C,gBAAM,iBAAiB,QAAQ,IAAI,QAAQ;AAE3C,cAAI,mBAAmB,QAAQ;AAE7BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,UAAU;AAAA,cACV,iBAAiB;AAAA,cACjB,SAAS,OAAO,aAAa;AAC3B,oBAAI,SAAS,WAAW,SAAS,SAAS;AACxC,wBAAM,kBAAkB,KAAK,SAAS,OAAO;AAAA,gBAC9C;AAAA,cACF;AAAA,YACX,CAAS;AAAA,UACT,OAAa;AACL,8BAAkB,KAAK,cAAc;AAAA,UACtC;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,oBAAoB,OAAO,KAAK,WAAW;AAC/CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,gBAAgB,MAAM;AAAA;AAAA,QAC/B,cAAc;AAAA,QACd,SAAS,OAAO,QAAQ;AACtB,cAAI,IAAI,SAAS;AACf,gBAAI;AACF,oBAAM,SAAS,MAAMI,4BAAiB;AAAA,gBACpC,WAAW,SAAS,SAAS,KAAK;AAAA,gBAClC,YAAY,SAAS,GAAG;AAAA,gBACxB;AAAA,cACZ,CAAW;AAED,kBAAI,OAAO,WAAW,KAAK;AACzBJ,8BAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,WAAW;AAChD,6BAAc;AAAA,cAC1B,OAAiB;AACLA,oCAAI,UAAU,EAAE,OAAO,OAAO,OAAO,QAAQ,MAAM,QAAQ;AAAA,cAC5D;AAAA,YACF,SAAQ,GAAG;AACVA,4BAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,YAC9C;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,eAAe,YAAY;AAC/B,UAAI;AACF,gBAAQ,QAAQ;AAEhB,cAAM,MAAM,MAAMK,+BAAoB;AAAA,UACpC,WAAW,SAAS;AAAA,UACpB,MAAM;AAAA,UACN,OAAO;AAAA;AAAA,QACb,CAAK;AAED,YAAI,IAAI,WAAW,OAAO,IAAI,QAAQ,IAAI,KAAK,MAAM;AACnD,gBAAM,OAAO,IAAI,KAAK;AAGtB,gBAAM,gBAAgB,CAAE;AACxB,gBAAM,iBAAiB,CAAE;AAEzB,eAAK,QAAQ,UAAQ;AAEnB,kBAAM,WAAW;AAAA,cACf,GAAG;AAAA,cACH,aAAa,KAAK,eAAe;AAAA,cACjC,eAAe,KAAK,iBAAiB;AAAA,cACrC,QAAQ,KAAK,UAAU;AAAA,cACvB,KAAK,KAAK,OAAO;AAAA,cACjB,WAAW,KAAK,aAAa;AAAA,cAC7B,SAAS,KAAK,WAAW;AAAA,YAC1B;AAED,gBAAI,KAAK,cAAc,GAAG;AACxB,4BAAc,QAAQ,EAAE,GAAG,UAAU,MAAM,KAAI,CAAE;AAAA,YAC3D,WAAmB,KAAK,cAAc,GAAG;AAC/B,4BAAc,KAAK,EAAE,GAAG,UAAU,MAAM,MAAK,CAAE;AAAA,YACzD,OAAe;AACL,6BAAe,KAAK,QAAQ;AAAA,YAC7B;AAAA,UACT,CAAO;AAED,oBAAU,QAAQ;AAClB,qBAAW,QAAQ;AACnB,qBAAW,QAAQ,EAAE,WAAW,eAAe,YAAY,eAAgB;AAG3E,sBAAa;AAGb,2BAAkB;AAAA,QACxB,OAAW;AACL,gBAAM,IAAI,MAAM,IAAI,OAAO,UAAU;AAAA,QACtC;AAAA,MACF,SAAQ,GAAG;AACVL,sBAAAA,2DAAc,aAAa,CAAC;AAC5B,oBAAY,GAAG,QAAQ;AAAA,MAC3B,UAAY;AACR,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH;AAEA,UAAM,iBAAiB,CAAC,YAAY;AAClC,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,OAAO,IAAI,KAAK,OAAO;AAC7B,YAAM,OAAO,KAAK,YAAa;AAC/B,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAChC;AAEA,UAAM,cAAc,CAAC,OAAO,iBAAiB,WAAW;;AACtDA,oBAAAA,MAAc,MAAA,SAAA,sCAAA,SAAS,KAAK;AAE5B,UAAI,UAAU;AAEd,UAAI,OAAO,UAAU,UAAU;AAC7B,kBAAU;AAAA,MACX,WAAU,SAAS,OAAO,UAAU,UAAU;AAC7C,YAAI,MAAM,SAAS,qBAAmB,WAAM,YAAN,mBAAe,SAAS,aAAY;AACxE,oBAAU;AAAA,QAChB,WAAe,MAAM,SAAS,eAAa,WAAM,YAAN,mBAAe,SAAS,aAAY;AACzE,oBAAU;AAAA,QAChB,OAAW;AACL,oBAAU,MAAM,OAAO,MAAM,aAAW,WAAM,SAAN,mBAAY,QAAO;AAAA,QAC5D;AAAA,MACF;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACd,CAAG;AAED,aAAO;AAAA,IACT;AAGA,UAAM,gBAAgB,CAAC,QAAQ;AAC7B,UAAI,CAAC,OAAO,CAAC,IAAI,KAAI,GAAI;AAEvB,YAAI,WAAW,MAAM,aAAa,WAAW,MAAM,YAAY;AAC7D,oBAAU,QAAQ,WAAW,MAAM;AACnC,qBAAW,QAAQ,WAAW,MAAM;AAAA,QACrC;AACD;AAAA,MACD;AAED,YAAM,aAAa,IAAI,KAAI,EAAG,YAAa;AAE3C,YAAM,SAAS,CAAC,QAAQ,IAAI,OAAO,OAAK;AACtC,cAAM,YAAY,EAAE,iBAAiB,IAAI,YAAa;AACtD,cAAM,MAAM,OAAO,EAAE,OAAO,EAAE;AAE9B,eAAO,SAAS,SAAS,UAAU,KAAK,IAAI,SAAS,UAAU;AAAA,MACnE,CAAG;AAED,gBAAU,QAAQ,OAAO,WAAW,MAAM,aAAa,EAAE;AACzD,iBAAW,QAAQ,OAAO,WAAW,MAAM,cAAc,EAAE;AAAA,IAC7D;AAGAM,kBAAAA,YAAY,MAAM;AAChB,UAAI,YAAY,OAAO;AACrB,qBAAa,YAAY,KAAK;AAC9B,oBAAY,QAAQ;AAAA,MACrB;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1lBD,GAAG,WAAWC,SAAe;"}