"use strict";
const common_vendor = require("../common/vendor.js");
const utils_cache = require("./cache.js");
let lang = "";
lang = utils_cache.Cache.has("locale") ? utils_cache.Cache.get("locale") : "zh-CN";
const i18n = common_vendor.createI18n({
  locale: lang,
  fallbackLocale: "zh-CN",
  messages: common_vendor.index.getStorageSync("localeJson") || {},
  legacy: false,
  // Vue3 Composition API模式
  globalInjection: true,
  // 全局注入$t函数
  silentTranslationWarn: true
  // 去除国际化警告
});
exports.i18n = i18n;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/lang.js.map
