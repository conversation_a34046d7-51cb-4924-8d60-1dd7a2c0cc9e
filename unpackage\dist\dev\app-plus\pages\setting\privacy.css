
.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.nav-bar[data-v-eaf4c2e5] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.nav-bar .navbar-item[data-v-eaf4c2e5] {
  width: 100%;
  display: flex;
  align-items: center;
}
.navbar-item .back-box[data-v-eaf4c2e5] {
  padding: 0 0.9375rem;
  display: flex;
  align-items: center;
}
.navbar-item .back-box uni-image[data-v-eaf4c2e5] {
  width: 1.0625rem;
  height: 1.0625rem;
}
.bfw[data-v-eaf4c2e5] {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,.8);
}
.bf8[data-v-eaf4c2e5] {
  background: #f8f8f8;
}


body {
  background: #f8f8f8;
  padding: 0 0.9375rem;
  box-sizing: border-box;
  overflow-x: hidden;
}
.title-box {
	padding: 0.625rem 0;
	font-size: 1.25rem;
	font-weight: bold;
}
.table {
	padding: 0.9375rem 0;
	color: #999;
	font-size: 0.75rem;
	font-weight: 500;
}
.list-box {
  width: 100%;
  border-radius: 0.75rem;
  overflow: hidden;
  box-sizing: border-box; 
  margin-bottom: 0.625rem;
}
.list-box .list {
	margin: 0;
	padding: 0;
	width: 100%;
	background: #fff!important;
	border-radius: 0;
}
.list-box .list:last-child {
  border-bottom: none;
}
.list-box .list .icon {
  margin: 0 0.9375rem;
  width: 1.1875rem;
  height: 1.1875rem;
}
.list-box .list-item {
  width: calc(100% - 3.0625rem);
  padding: 0.9375rem 0.9375rem 0.9375rem 0;
  justify-content: space-between;
  border-bottom: 1px solid #f8f8f8;
}
.list-item .title {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.list-item .title .t1 {
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.5rem;
}
.list-item .title .t2 {
  color: #999;
  font-size: 0.5625rem;
  font-weight: 300;
  line-height: 0.5625rem;
}
.list-box .list-item uni-image {
  width: 0.75rem;
  height: 0.75rem;
  transform: rotate(-90deg);
}
.description-box {
  background: #fff;
  border-radius: 0.75rem;
  padding: 0.9375rem;
  margin-bottom: 0.9375rem;
}
.description-title {
  font-size: 0.875rem;
  font-weight: bold;
  margin-bottom: 0.625rem;
}
.description-content {
  font-size: 0.75rem;
  color: #666;
  line-height: 1.6;
}
.version {
  padding: 1.875rem 0;
  flex-direction: column;
  justify-content: center;
}
.version uni-image {
  margin-right: 0.3125rem;
  width: 0.625rem;
  height: 0.625rem;
}
.version uni-text {
  color: #999;
  font-size: 0.5625rem;
}
.tips-box {
  padding: 0.625rem 0.9375rem;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 0.375rem;
  justify-content: center;
}
.tips-box .tips-item {
  color: #fff;
  font-size: 0.875rem;
  font-weight: 700;
}
.df {
  display: flex;
  align-items: center;
}
