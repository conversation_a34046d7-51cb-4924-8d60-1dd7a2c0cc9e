{"version": 3, "file": "color.js", "sources": ["mixins/color.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tcolorStyle: '',\n\t\t\tcolorStatus: ''\n\t\t};\n\t},\n\tcreated() {\n\t\tthis.colorStyle = uni.getStorageSync('viewColor')\n\t\tuni.$on('ok', (data, status) => {\n\t\t\tthis.colorStyle = data\n\t\t\tthis.colorStatus = status\n\t\t})\n\t},\n\tmethods: {}\n};\n"], "names": ["uni"], "mappings": ";;AAUA,MAAe,SAAA;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,YAAY;AAAA,MACZ,aAAa;AAAA,IAChB;AAAA,EACE;AAAA,EACD,UAAU;AACT,SAAK,aAAaA,oBAAI,eAAe,WAAW;AAChDA,kBAAAA,MAAI,IAAI,MAAM,CAAC,MAAM,WAAW;AAC/B,WAAK,aAAa;AAClB,WAAK,cAAc;AAAA,IACtB,CAAG;AAAA,EACD;AAAA,EACD,SAAS,CAAE;AACZ;;"}