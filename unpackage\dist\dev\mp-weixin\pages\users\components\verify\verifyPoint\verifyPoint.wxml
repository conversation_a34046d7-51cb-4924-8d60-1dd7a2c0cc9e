<view class="data-v-76118314" style="position:relative"><view class="verify-image-out data-v-76118314" hidden="{{!i}}"><view class="verify-image-panel data-v-76118314" style="{{'width:' + f + ';' + ('height:' + g) + ';' + ('margin-bottom:' + h)}}"><view class="verify-refresh data-v-76118314" style="z-index:3" bindtap="{{a}}" hidden="{{!b}}"><text class="iconfont icon-refresh data-v-76118314"></text></view><image class="data-v-76118314" src="{{c}}" id="image" ref="canvas" style="width:100%;height:100%;display:block" bindtap="{{d}}"></image><view wx:for="{{e}}" wx:for-item="tempPoint" wx:key="b" class="point-area data-v-76118314" style="{{'background-color:' + '#1abd6c' + ';' + ('color:' + '#fff') + ';' + ('z-index:' + 9999) + ';' + ('width:' + '20px') + ';' + ('height:' + '20px') + ';' + ('text-align:' + 'center') + ';' + ('line-height:' + '20px') + ';' + ('border-radius:' + '50%') + ';' + ('position:' + 'absolute') + ';' + ('top:' + tempPoint.c) + ';' + ('left:' + tempPoint.d)}}">{{tempPoint.a}}</view></view></view><view class="verify-bar-area data-v-76118314" style="{{'width:' + k + ';' + ('color:' + l) + ';' + ('border-color:' + m) + ';' + ('line-height:' + '40px')}}"><text class="verify-msg data-v-76118314">{{j}}</text></view></view>