.container {
  width: 100%;
  background: #fff;
  height: 100vh;
  overflow: hidden;
}
.content-box {
  width: 100%;
}
.login-box {
  width: calc(100% - 60rpx);
  margin: 30rpx;
}
.login-box .title {
  font-size: 40rpx;
  font-weight: 700;
  color: #000;
}
.login-box .sub-title {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #999;
}
.login-box .input-item {
  margin-top: 60rpx;
  padding: 0 30rpx;
  width: calc(100% - 60rpx);
  height: 100rpx;
  border-radius: 100rpx;
  background: #f8f8f8;
  position: relative;
}
.input-item .icon-mobile,
.input-item .icon-code {
  margin-right: 20rpx;
  width: 36rpx;
  height: 36rpx;
}
.input-item .icon-mobile image,
.input-item .icon-code image {
  width: 100%;
  height: 100%;
}
.input-item input {
  width: calc(100% - 56rpx);
  height: 100%;
  font-size: 28rpx;
}
.input-item .send-code {
  position: absolute;
  right: 30rpx;
  width: 180rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 700;
  background: #000;
  color: #fff;
  border-radius: 30rpx;
}
.input-item .send-code.active {
  background: #f5f5f5;
  color: #999;
}
.login-box .tips-text {
  margin-top: 30rpx;
  font-size: 24rpx;
  color: #999;
}
.login-box .btn-submit {
  margin-top: 60rpx;
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 700;
  background: #000;
  color: #fff;
  border-radius: 100rpx;
  justify-content: center;
}
.login-box .agreement {
  margin-top: 30rpx;
  font-size: 24rpx;
  color: #999;
}
.agreement .agreement-text {
  font-size: 24rpx;
  color: #999;
}
.agreement-text text {
  color: #576b95;
}
.df {
  display: flex;
  align-items: center;
}
.trembling {
  animation: shake 0.6s;
}
@keyframes shake {
0%, 100% {
    transform: translateX(0);
}
10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
}
20%, 40%, 60%, 80% {
    transform: translateX(5px);
}
}