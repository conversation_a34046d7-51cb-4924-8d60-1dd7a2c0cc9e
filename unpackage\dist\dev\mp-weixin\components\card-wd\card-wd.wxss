
.gg-box {
  position: relative;
  border-bottom: 1px solid #f8f8f8;
  width: calc(100% - 60rpx);
  padding: 30rpx;
  display: flex;
}
.gg-box .gg-avatar {
  position: relative;
  z-index: 1;
  width: 68rpx;
  height: 68rpx;
  border-radius: 50%;
  border: 1px solid #f8f8f8;
  overflow: hidden;
}
.gg-box .rq {
  line-height: 68rpx;
  font-size: 26rpx;
  font-weight: bolder;
  text-align: center;
  color: #fff;
  background: #000;
  border: 1px solid #000;
}
.gg-box .gg-item {
  width: calc(100% - 88rpx - 2px);
  margin-left: 20rpx;
}
.timeline-line {
  position: absolute;
  left: 58rpx;
  top: 120rpx; 
  bottom: 30rpx;
  width: 2rpx;
  background-color: #e5e5e5; 
  z-index: 0;
}
.timeline-line::before {
  content: '';
  position: absolute;
  left: -4rpx;
  top: -4rpx;
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  background-color: #e5e5e5;
}
.gg-box:last-child .timeline-line {
  display: none;
}
.gg-item .gg-item-name {
  color: #999;
  font-size: 22rpx;
  line-height: 22rpx;
  font-weight: 300;
  display: flex;
  align-items: flex-end;
}
.gg-item .gg-item-name text {
  margin-right: 20rpx;
  color: #fa5150;
  font-weight: 700;
}
.gg-item .gg-item-name view {
  max-width: 320rpx;
  margin-right: 20rpx;
  color: #000;
  font-size: 28rpx;
  line-height: 28rpx;
  font-weight: 700;
}
.gg-item .gg-item-content {
  margin-top: 15rpx;
  font-size: 26rpx;
  font-weight: 400;
  line-height: 36rpx;
  word-break: break-word;
  white-space: pre-line;
}
.gg-item .gg-item-file {
  margin-top: 20rpx;
  display: flex;
  position: relative;
  z-index: 1;
  flex-wrap: wrap;
}
.gg-item-file .file-h,
.gg-item-file .file-w,
.gg-item-file .file-img {
  border-radius: 8rpx;
  overflow: hidden;
}
.gg-item-file .file-h {
  width: 320rpx;
  height: 420rpx;
}
.gg-item-file .file-w {
  width: 420rpx;
  height: 320rpx;
}
.gg-item-file .file-img {
  width: 196rpx;
  height: 196rpx;
  margin-right: 4rpx;
  margin-bottom: 4rpx;
}
.gg-item-file .file-img:nth-child(3n) {
  margin-right: 0rpx !important;
}
.gg-item-file .file-count {
  position: absolute;
  right: 20rpx;
  bottom: 30rpx;
  padding: 0 10rpx;
  height: 40rpx;
  color: #fff;
  font-size: 20rpx;
  font-weight: 700;
  background: rgba(0, 0, 0, 0.6);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  border-radius: 8rpx;
}
.gg-item-file .file-count image,
.gg-item-file .file-video image {
  width: 20rpx;
  height: 20rpx;
}
.gg-item-file .file-count image {
  margin-right: 10rpx;
}
.gg-item-file .file-video {
  position: absolute;
  top: 20rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  justify-content: center;
  border-radius: 50%;
}
.gg-item-file .file-audio {
  width: 100%;
  height: 140rpx;
  border-radius: 8rpx;
  color: #fff;
  position: relative;
  overflow: hidden;
}
.file-audio .audio-left {
  margin-right: 30rpx;
  width: 140rpx;
  height: 140rpx;
  position: relative;
}
.file-audio .audio-left .icon {
  position: absolute;
  top: 45rpx;
  right: 45rpx;
  bottom: 45rpx;
  left: 45rpx;
  width: 50rpx;
  height: 50rpx;
}
.file-audio .audio-bg,
.file-audio .audio-mb {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.file-audio .audio-mb {
  z-index: -1;
  -webkit-backdrop-filter: saturate(150%) blur(25px);
  backdrop-filter: saturate(150%) blur(25px);
  background: rgba(0, 0, 0, 0.5);
}
.file-audio .audio-t1 {
  font-size: 26rpx;
  font-weight: 700;
}
.file-audio .audio-t2 {
  margin-top: 10rpx;
  opacity: 0.8;
  font-size: 20rpx;
}
.file-audio .audio-play {
  margin: 0 30rpx;
  width: 100rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 18rpx;
  font-weight: 700;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 60rpx;
}
.gg-item .gg-item-g {
  margin-top: 10rpx;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.gg-item-g .g-item {
  margin: 10rpx 10rpx 0 0;
  padding: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
  border-radius: 8rpx;
  background: #f8f8f8;
}
.g-item .g-item-icon {
  width: 32rpx;
  height: 32rpx;
}
.g-item .g-item-img {
  width: 40rpx;
  height: 40rpx;
  background: #f8f8f8;
  border-radius: 4rpx;
  overflow: hidden;
}
.gg-item-unm .gg-item-unm-txt {
  margin: 20rpx 20rpx 0 0;
  color: #999;
  font-size: 20rpx;
}
.wlc8 {
  -webkit-line-clamp: 8 !important;
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2 {
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

