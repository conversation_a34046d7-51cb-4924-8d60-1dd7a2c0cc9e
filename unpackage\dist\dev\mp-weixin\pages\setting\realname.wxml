<view class="page-wrapper data-v-a3319d4f"><scroll-view scroll-y class="content-scroll data-v-a3319d4f"><view class="container data-v-a3319d4f"><view class="title-box data-v-a3319d4f"><view class="data-v-a3319d4f">请填写真实身份信息</view></view><view class="input-tips data-v-a3319d4f" style="color:#FA5150"> 注：因监管部门要求，社区类产品需实名后使用，绑定手机号即可发布动态、评论、参加活动等。认证信息将用于平台服务，与账号唯一绑定，无法解绑。我们会对信息进行严格保密。 </view><view class="form-section data-v-a3319d4f"><view class="title-label data-v-a3319d4f">手机号</view><view class="input-box df data-v-a3319d4f"><view class="data-v-a3319d4f">{{a}}</view><button class="input-btn df data-v-a3319d4f" open-type="getPhoneNumber" bindgetphonenumber="{{d}}"><image class="data-v-a3319d4f" src="{{b}}"></image><text class="data-v-a3319d4f">{{c}}</text></button></view><view class="title-label data-v-a3319d4f">证件类型</view><view class="input-box data-v-a3319d4f"><view class="value data-v-a3319d4f">居民身份证</view></view><view class="title-label data-v-a3319d4f">真实姓名</view><input class="input-box data-v-a3319d4f" disabled="{{e}}" placeholder="请填写真实姓名" maxlength="20" bindblur="{{f}}" value="{{g}}" bindinput="{{h}}"/><view class="title-label data-v-a3319d4f">身份证号</view><input class="input-box data-v-a3319d4f" disabled="{{i}}" placeholder="请填写身份证号码" maxlength="18" bindblur="{{j}}" value="{{k}}" bindinput="{{l}}"/><view wx:if="{{m}}" class="fail-reason data-v-a3319d4f"> 审核失败：{{n}}</view><view wx:if="{{o}}" class="success-msg data-v-a3319d4f"> 已认证，无法修改 </view><view wx:if="{{p}}" class="pending-msg data-v-a3319d4f"> 已提交，等待审核 </view></view><view wx:if="{{q}}" class="protocol-row data-v-a3319d4f"><checkbox-group class="data-v-a3319d4f" bindchange="{{v}}"><label class="df data-v-a3319d4f"><checkbox class="data-v-a3319d4f" checked="{{r}}" disabled="{{s}}" style="transform:scale(0.7)"/><text class="protocol-text data-v-a3319d4f">我已阅读并同意 <text class="protocol-link data-v-a3319d4f" bindtap="{{t}}">《实名认证服务协议》</text></text></label></checkbox-group></view><view wx:if="{{w}}" class="cancel-section data-v-a3319d4f"><view class="footer-item df data-v-a3319d4f"><button class="btn cancel-btn data-v-a3319d4f" bindtap="{{x}}" disabled="{{y}}"> 撤销申请 </button></view></view><view class="bottom-safe-area data-v-a3319d4f"></view></view></scroll-view><view class="footer-box bfw bUp data-v-a3319d4f"><view class="footer-item df data-v-a3319d4f"><button class="{{['btn', 'data-v-a3319d4f', A]}}" bindtap="{{B}}" disabled="{{C}}">{{z}}</button></view></view><uni-popup wx:if="{{F}}" class="r data-v-a3319d4f" u-s="{{['d']}}" u-r="tipsPopup" u-i="a3319d4f-0" bind:__l="__l" u-p="{{F}}"><view class="tips-box df data-v-a3319d4f"><view class="tips-item data-v-a3319d4f">{{D}}</view></view></uni-popup></view>