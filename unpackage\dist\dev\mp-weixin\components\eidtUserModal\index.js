"use strict";
const common_vendor = require("../../common/vendor.js");
const mixins_color = require("../../mixins/color.js");
require("../../utils/cache.js");
const api_user = require("../../api/user.js");
const _sfc_main = {
  mixins: [mixins_color.colors],
  props: {
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      defHead: "/static/img/def_avatar.png",
      mp_is_new: this.$Cache.get("MP_VERSION_ISNEW") || false,
      userInfo: {
        avatar: "",
        nickname: ""
      },
      mpData: common_vendor.index.getStorageSync("copyRight"),
      canvasStatus: false
    };
  },
  mounted() {
  },
  methods: {
    /**
     * 上传文件
     * 
     */
    uploadpic: function() {
      let that = this;
      this.canvasStatus = true;
      that.$util.uploadImageChange("upload/image", (res) => {
        let userInfo = that.userInfo;
        if (userInfo !== void 0) {
          that.userInfo.avatar = res.data.url;
        }
        this.canvasStatus = false;
      }, (res) => {
        this.canvasStatus = false;
      }, (res) => {
        this.canvasWidth = res.w;
        this.canvasHeight = res.h;
      });
    },
    // 微信头像获取
    onChooseAvatar(e) {
      const {
        avatarUrl
      } = e.detail;
      this.$util.uploadImgs("upload/image", avatarUrl, (res) => {
        this.userInfo.avatar = res.data.url;
      }, (err) => {
        common_vendor.index.__f__("log", "at components/eidtUserModal/index.vue:117", err);
      });
    },
    closeAttr: function() {
      this.$emit("closeEdit");
    },
    /**
     * 提交修改
     */
    formSubmit(e) {
      let that = this;
      if (!this.userInfo.avatar)
        return that.$util.Tips({
          title: that.$t(`请上传头像`)
        });
      if (!e.detail.value.nickname)
        return that.$util.Tips({
          title: that.$t(`请输入昵称`)
        });
      this.userInfo.nickname = e.detail.value.nickname;
      api_user.userEdit(this.userInfo).then((res) => {
        this.$emit("editSuccess");
        return that.$util.Tips({
          title: res.msg,
          icon: "success"
        }, {
          tab: 3
        });
      }).catch((msg) => {
        return that.$util.Tips({
          title: msg || that.$t(`保存失败`)
        }, {
          tab: 3,
          url: 1
        });
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.closeAttr && $options.closeAttr(...args)),
    b: $data.mpData.siteLogo,
    c: common_vendor.t($data.mpData.siteName),
    d: common_vendor.t(_ctx.$t(`获取您的昵称、头像`)),
    e: common_vendor.t(_ctx.$t(`提供具有辨识度的用户中心界面`)),
    f: common_vendor.t(_ctx.$t(`头像`)),
    g: !$data.mp_is_new
  }, !$data.mp_is_new ? {
    h: $data.userInfo.avatar || $data.defHead,
    i: common_vendor.o((...args) => $options.uploadpic && $options.uploadpic(...args))
  } : {
    j: $data.userInfo.avatar || $data.defHead,
    k: common_vendor.o((...args) => $options.onChooseAvatar && $options.onChooseAvatar(...args))
  }, {
    l: common_vendor.t(_ctx.$t(`昵称`)),
    m: _ctx.$t(`请输入昵称`),
    n: $data.userInfo.nickname,
    o: common_vendor.t(_ctx.$t(`保存`)),
    p: $data.userInfo.avatar ? 1 : "",
    q: common_vendor.o((...args) => $options.formSubmit && $options.formSubmit(...args)),
    r: $props.isShow ? 1 : "",
    s: $data.canvasStatus
  }, $data.canvasStatus ? {
    t: _ctx.canvasWidth + "px",
    v: _ctx.canvasHeight + "px"
  } : {}, {
    w: $props.isShow
  }, $props.isShow ? {
    x: common_vendor.o(() => {
    }),
    y: common_vendor.o((...args) => $options.closeAttr && $options.closeAttr(...args))
  } : {}, {
    z: common_vendor.s(_ctx.colorStyle)
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f7f0d621"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/eidtUserModal/index.js.map
