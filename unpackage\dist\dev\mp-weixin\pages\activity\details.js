"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const money = () => "../../components/money/money.js";
const waterfall = () => "../../components/waterfall/waterfall.js";
const cardGg = () => "../../components/card-gg/card-gg.js";
const app = getApp();
const _sfc_main = {
  components: {
    uniLoadMore,
    lazyImage,
    money,
    waterfall,
    cardGg
  },
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      isUser: false,
      navbarTrans: 0,
      userAvatar: "",
      barList: ["详情", "笔记"],
      barIdx: 0,
      shareView: false,
      activityInfo: {
        id: 0,
        imgs: [],
        name: "名称加载中",
        intro: "介绍加载中",
        activity_time: "时间加载中",
        adds_name: "地址加载中",
        status_str: "加载中",
        start_time: "时间加载中",
        is_join: false,
        user_count: 0,
        browse: 1,
        status: 1,
        product: [{
          name: "名称加载中",
          price: "0.00",
          line_price: "0.00",
          stock: 1
        }]
      },
      productIdx: 0,
      isQuantity: false,
      quantity: 1,
      swiperIdx: 0,
      list: [],
      page: 1,
      isEmpty: false,
      loadStatus: "more",
      tipsTitle: "",
      isWaterfall: false
    };
  },
  async onLoad(option) {
    try {
      if (typeof common_vendor.index.showShareMenu === "function") {
        common_vendor.index.showShareMenu();
      }
    } catch (e) {
      common_vendor.index.__f__("warn", "at pages/activity/details.vue:280", "showShareMenu not supported on this platform:", e);
    }
    await this.$onLaunched;
    if (option.id) {
      this.activityInfo.id = option.id;
      this.activityDetails();
      if (option.share) {
        this.shareView = true;
      }
      this.userAvatar = common_vendor.index.getStorageSync("userInfo").avatar || "/static/img/avatar.png";
    } else {
      this.opTipsPopup("活动异常或已被下架，请稍后重试！", true);
    }
  },
  onShow() {
    const userInfo = common_vendor.index.getStorageSync("userInfo");
    if (userInfo && userInfo.mobile) {
      this.isUser = true;
    }
  },
  methods: {
    activityDetails() {
      setTimeout(() => {
        const activityData = {
          id: this.activityInfo.id,
          imgs: ["/static/img/avatar.png", "/static/img/avatar.png"],
          name: "夏季摄影大赛",
          intro: "这是一场摄影爱好者的盛会，将在市中心广场举办。参赛者需携带自己的设备，活动现场将有专业摄影师指导。\n\n获奖者将有机会获得丰厚奖品，作品也将在展览厅展出一个月。\n\n欢迎各位摄影爱好者参加！",
          activity_time: "2023-07-20 14:00-17:00",
          adds_name: "市中心广场",
          status_str: "报名中",
          start_time: "2023-07-19 23:59:59",
          is_join: false,
          user_count: 15,
          browse: 123,
          status: 1,
          lat: "39.908823",
          lng: "116.397470",
          avatar_list: ["/static/img/avatar.png", "/static/img/avatar.png", "/static/img/avatar.png"],
          product: [
            {
              id: 101,
              name: "普通票",
              price: "19.90",
              line_price: "39.90",
              stock: 20
            },
            {
              id: 102,
              name: "VIP票",
              price: "49.90",
              line_price: "99.90",
              stock: 10
            }
          ]
        };
        this.activityInfo = activityData;
        if (this.shareView) {
          this.shareClick(true);
        }
        this.navigationBarColor("#ffffff");
      }, 500);
    },
    dynamicRecommend() {
      let that = this;
      that.loadStatus = "loading";
      that.isEmpty = false;
      that.isWaterfall = app.globalData.isWaterfall || false;
      setTimeout(() => {
        if (that.page == 1) {
          that.list = [
            {
              id: 201,
              uid: 1001,
              user: {
                id: 1001,
                name: "摄影爱好者",
                avatar: "/static/img/avatar.png"
              },
              title: "参加了摄影活动的感受",
              content: "今天参加了一场很棒的摄影活动，学到了很多技巧...",
              imgs: ["/static/img/avatar.png"],
              like_count: 25,
              comment_count: 8,
              is_like: false
            },
            {
              id: 202,
              uid: 1002,
              user: {
                id: 1002,
                name: "风景摄影师",
                avatar: "/static/img/avatar.png"
              },
              title: "分享几张活动中拍摄的照片",
              content: "这些是我在活动中拍摄的几张照片，欢迎大家点评...",
              imgs: ["/static/img/avatar.png", "/static/img/avatar.png"],
              like_count: 36,
              comment_count: 12,
              is_like: false
            }
          ];
          that.isEmpty = false;
        } else if (that.page == 2) {
          that.list.push({
            id: 203,
            uid: 1003,
            user: {
              id: 1003,
              name: "手机摄影达人",
              avatar: "/static/img/avatar.png"
            },
            title: "用手机也能拍出好照片",
            content: "今天的活动中学到了很多用手机拍出专业效果的技巧...",
            imgs: ["/static/img/avatar.png"],
            like_count: 18,
            comment_count: 5,
            is_like: false
          });
        } else {
          that.loadStatus = "noMore";
        }
        if (that.list.length === 0) {
          that.isEmpty = true;
        }
        that.loadStatus = "more";
      }, 500);
    },
    wxPayClick() {
      let that = this;
      common_vendor.index.showLoading({
        title: "正在支付..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "支付确认",
          content: "是否确认支付" + that.activityInfo.product[that.productIdx].price * that.quantity + "元？",
          success: function(res) {
            if (res.confirm) {
              app.globalData.isCenterPage = true;
              that.$refs.registerPopup.close();
              that.opTipsPopup("购买成功！正在为您出票...");
              setTimeout(function() {
                common_vendor.index.navigateTo({
                  url: "/pages/activity/index?type=1"
                });
              }, 2e3);
            }
          }
        });
      }, 500);
    },
    barClick(e) {
      this.barIdx = e.currentTarget.dataset.idx;
      if (this.barIdx == 1) {
        this.page = 1;
        this.list = [];
        this.dynamicRecommend();
      }
    },
    quantityBtn(t) {
      if (t == 1 && this.quantity < this.activityInfo.product[this.productIdx].stock) {
        this.quantity = parseInt(this.quantity) + 1;
      } else if (t == 0 && this.quantity != 1) {
        this.quantity = parseInt(this.quantity) - 1;
      } else if (t == 2 && this.quantity <= 0) {
        this.quantity = 1;
      } else if (t == 2 && this.quantity > this.activityInfo.product[this.productIdx].stock) {
        this.quantity = this.activityInfo.product[this.productIdx].stock;
      }
    },
    swiperClick(e) {
      let i = e.currentTarget.dataset.i;
      common_vendor.index.previewImage({
        current: i,
        urls: this.activityInfo.imgs
      });
    },
    swiperChange(e) {
      this.swiperIdx = e.detail.current;
    },
    openLocationClick() {
      common_vendor.index.openLocation({
        latitude: parseFloat(this.activityInfo.lat),
        longitude: parseFloat(this.activityInfo.lng),
        name: this.activityInfo.adds_name
      });
    },
    openActivityNote() {
      this.$refs.notePopup.open();
    },
    toActivityNote(type) {
      if (this.activityInfo.is_join) {
        common_vendor.index.navigateTo({
          url: "/pages/note/add?type=" + type + "&aid=" + this.activityInfo.id + "&aname=" + this.activityInfo.name + "&aimg=" + this.activityInfo.imgs[0]
        });
      } else {
        this.opTipsPopup("未参加该活动无法发布相关笔记！");
      }
    },
    registerClick(show) {
      if (!show) {
        this.$refs.registerPopup.close();
        return;
      }
      let msg = "活动 " + this.activityInfo.status_str + " 无法参加！";
      if (this.activityInfo.status != 1) {
        return this.opTipsPopup(msg);
      }
      this.$refs.registerPopup.open();
    },
    shareClick(show) {
      if (!show) {
        this.$refs.sharePopup.close();
      } else {
        this.$refs.sharePopup.open();
      }
    },
    likeClick(e) {
      this.list[e.idx].is_like = e.is_like;
      this.list[e.idx].like_count = e.like_count;
    },
    onRichTextTap() {
      const intro = this.activityInfo.intro;
      const imgRegex = /<img[^>]+src="([^">]+)"/g;
      const imageUrls = [];
      let match;
      while ((match = imgRegex.exec(intro)) !== null) {
        imageUrls.push(match[1]);
      }
      if (imageUrls.length) {
        common_vendor.index.previewImage({
          current: 0,
          urls: imageUrls
        });
      }
    },
    navigateToFun(e) {
      let url = e.currentTarget.dataset.url;
      common_vendor.index.navigateTo({
        url: "/pages/" + url
      });
    },
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "/pages/index/index"
        });
      }
    },
    opTipsPopup(title, isNeedBack = false) {
      this.tipsTitle = title;
      this.$refs.tipsPopup.open();
      setTimeout(() => {
        this.$refs.tipsPopup.close();
        if (isNeedBack) {
          this.navBack();
        }
      }, 2e3);
    },
    navigationBarColor(frontColor) {
      common_vendor.index.setNavigationBarColor({
        frontColor,
        backgroundColor: "#ffffff",
        animation: {
          duration: 400,
          timingFunc: "easeIn"
        }
      });
    }
  },
  onReachBottom() {
    if (!this.isEmpty && this.list.length && this.barIdx == 1) {
      this.page = this.page + 1;
      this.dynamicRecommend();
    }
  },
  onPageScroll(e) {
    let frontColor = "#ffffff";
    const scrollTop = e.scrollTop > 150 ? 150 : e.scrollTop;
    const opacity = scrollTop / 150;
    if (opacity >= 1) {
      frontColor = "#000000";
    }
    this.navbarTrans = opacity;
    this.navigationBarColor(frontColor);
  },
  onShareAppMessage() {
    return {
      title: this.activityInfo.name,
      imageUrl: this.activityInfo.imgs[0],
      path: "/pages/activity/details?id=" + this.activityInfo.id + "&share=1"
    };
  },
  onShareTimeline() {
    return {
      title: this.activityInfo.name,
      imageUrl: this.activityInfo.imgs[0],
      query: "id=" + this.activityInfo.id
    };
  }
};
if (!Array) {
  const _component_lazy_image = common_vendor.resolveComponent("lazy-image");
  const _component_waterfall = common_vendor.resolveComponent("waterfall");
  const _component_card_gg = common_vendor.resolveComponent("card-gg");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _component_money = common_vendor.resolveComponent("money");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_lazy_image + _component_waterfall + _component_card_gg + _easycom_uni_load_more2 + _component_money + _easycom_uni_popup2)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.n($data.navbarTrans != 1 ? "" : "xwb"),
    b: $data.navbarTrans == 1 ? "/static/img/z.png" : "/static/img/z1.png",
    c: $data.titleBarHeight + "px",
    d: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    e: $data.navbarTrans == 1
  }, $data.navbarTrans == 1 ? {
    f: common_vendor.t($data.activityInfo.name)
  } : {}, {
    g: $data.statusBarHeight + "px",
    h: "rgba(255, 255, 255," + $data.navbarTrans + ")",
    i: common_vendor.f($data.activityInfo.imgs, (item, index, i0) => {
      return {
        a: "4263f868-0-" + i0,
        b: common_vendor.p({
          src: item
        }),
        c: index,
        d: common_vendor.o((...args) => $options.swiperClick && $options.swiperClick(...args), index),
        e: index
      };
    }),
    j: common_vendor.o((...args) => $options.swiperChange && $options.swiperChange(...args)),
    k: $data.activityInfo.imgs.length > 1
  }, $data.activityInfo.imgs.length > 1 ? {
    l: common_vendor.f($data.activityInfo.imgs.length, (item, index, i0) => {
      return {
        a: index,
        b: common_vendor.n($data.swiperIdx == index ? "active" : "")
      };
    })
  } : {}, {
    m: common_vendor.f($data.barList, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index == $data.barIdx ? "#000" : "#999",
        c: index == $data.barIdx ? "28rpx" : "26rpx",
        d: index == $data.barIdx ? 1 : 0,
        e: index,
        f: common_vendor.o((...args) => $options.barClick && $options.barClick(...args), index),
        g: index
      };
    }),
    n: common_vendor.t($data.activityInfo.status_str),
    o: common_vendor.n($data.activityInfo.status == 1 ? "s1" : ""),
    p: common_vendor.n($data.activityInfo.status == 2 ? "s2" : ""),
    q: $data.userAvatar,
    r: common_vendor.o((...args) => $options.openActivityNote && $options.openActivityNote(...args)),
    s: common_assets._imports_2$7,
    t: common_vendor.o(($event) => $options.shareClick(true)),
    v: $data.statusBarHeight + $data.titleBarHeight - 1 + "px",
    w: $data.barIdx == 0
  }, $data.barIdx == 0 ? common_vendor.e({
    x: $data.activityInfo.is_join
  }, $data.activityInfo.is_join ? {
    y: $data.userAvatar,
    z: common_assets._imports_1$8,
    A: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {}, {
    B: common_assets._imports_1$9,
    C: common_vendor.t($data.activityInfo.adds_name),
    D: common_vendor.t($data.activityInfo.activity_time),
    E: $data.activityInfo.user_count
  }, $data.activityInfo.user_count ? {
    F: common_vendor.f($data.activityInfo.avatar_list, (img, index, i0) => {
      return {
        a: img,
        b: index
      };
    }),
    G: common_vendor.t($data.activityInfo.user_count)
  } : {
    H: common_vendor.t($data.activityInfo.browse)
  }, {
    I: common_assets._imports_3$5,
    J: common_vendor.o((...args) => $options.openLocationClick && $options.openLocationClick(...args)),
    K: common_vendor.t($data.activityInfo.name),
    L: $data.activityInfo.intro,
    M: common_vendor.o((...args) => $options.onRichTextTap && $options.onRichTextTap(...args))
  }) : common_vendor.e({
    N: $data.isEmpty
  }, $data.isEmpty ? {
    O: common_assets._imports_1$5
  } : {}, {
    P: $data.isWaterfall
  }, $data.isWaterfall ? {
    Q: common_vendor.p({
      note: $data.list,
      page: $data.page
    })
  } : {
    R: common_vendor.f($data.list, (item, index, i0) => {
      return {
        a: common_vendor.o($options.likeClick, index),
        b: "4263f868-2-" + i0,
        c: common_vendor.p({
          item,
          idx: index
        }),
        d: index
      };
    })
  }, {
    S: common_vendor.p({
      status: $data.loadStatus
    }),
    T: common_vendor.n($data.isWaterfall ? "dynamic-box" : "")
  }), {
    U: !$data.isUser
  }, !$data.isUser ? {
    V: common_assets._imports_2$6,
    W: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {
    X: common_vendor.p({
      price: $data.activityInfo.product[$data.productIdx].price
    }),
    Y: common_vendor.t($data.activityInfo.product[$data.productIdx].line_price),
    Z: common_assets._imports_3$5,
    aa: common_vendor.o(($event) => $options.registerClick(true))
  }, {
    ab: common_vendor.t($data.activityInfo.name),
    ac: common_assets._imports_0$4,
    ad: common_vendor.o(($event) => $options.registerClick(false)),
    ae: common_vendor.f($data.activityInfo.product, (item, index, i0) => {
      return {
        a: common_vendor.t(item.stock),
        b: common_vendor.t(item.name),
        c: "4263f868-6-" + i0 + ",4263f868-5",
        d: common_vendor.p({
          price: item.price
        }),
        e: common_vendor.t(item.line_price),
        f: index,
        g: common_vendor.n($data.productIdx == index ? "active" : ""),
        h: common_vendor.o(($event) => $data.productIdx = index, index)
      };
    }),
    af: common_vendor.t($data.activityInfo.start_time),
    ag: $data.quantity > 1 ? "#000" : "#ccc",
    ah: common_vendor.o(($event) => $options.quantityBtn(0)),
    ai: common_vendor.o(($event) => $options.quantityBtn(2)),
    aj: $data.quantity,
    ak: common_vendor.o(($event) => $data.quantity = $event.detail.value),
    al: $data.quantity < $data.activityInfo.product[$data.productIdx].stock ? "#000" : "#ccc",
    am: common_vendor.o(($event) => $options.quantityBtn(1)),
    an: common_vendor.p({
      price: $data.activityInfo.product[$data.productIdx].price * $data.quantity
    }),
    ao: common_assets._imports_3$7,
    ap: common_vendor.o((...args) => $options.wxPayClick && $options.wxPayClick(...args)),
    aq: common_vendor.sr("registerPopup", "4263f868-5"),
    ar: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    as: common_assets._imports_8,
    at: common_assets._imports_9$1,
    av: common_vendor.o(($event) => $options.shareClick(false)),
    aw: common_vendor.sr("sharePopup", "4263f868-8"),
    ax: common_assets._imports_1$7,
    ay: common_vendor.o(($event) => $options.toActivityNote(1)),
    az: common_assets._imports_3$8,
    aA: common_vendor.o(($event) => $options.toActivityNote(2)),
    aB: common_assets._imports_2$5,
    aC: common_vendor.o(($event) => $options.toActivityNote(3)),
    aD: common_vendor.sr("notePopup", "4263f868-9"),
    aE: common_vendor.p({
      type: "center"
    }),
    aF: common_vendor.t($data.tipsTitle),
    aG: common_vendor.sr("tipsPopup", "4263f868-10"),
    aH: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 7;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/activity/details.js.map
