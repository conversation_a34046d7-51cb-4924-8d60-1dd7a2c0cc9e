{"version": 3, "file": "detail.js", "sources": ["pages/note/detail.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbm90ZS9kZXRhaWwudnVl"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"nav-bar bfw\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\n      <view class=\"bar-box df\" :style=\"{'height': titleBarHeight + 'px', 'width': '100%'}\">\n        <view class=\"bar-back df\" @tap=\"goBack\">\n          <image src=\"/static/img/z.png\" style=\"width:34rpx;height:34rpx\"></image>\n        </view>\n        <view class=\"bar-title ohto\">纸条详情</view>\n        <view class=\"nav-action df\" @tap=\"showMoreActions\">\n          <text>⋯</text>\n        </view>\n      </view>\n    </view>\n\n    <view class=\"content-box\" :style=\"{'margin-top': 'calc(' + (statusBarHeight + titleBarHeight) + 'px + 20rpx)'}\">\n      <!-- 加载状态 -->\n      <view v-if=\"loading\" class=\"loading-container\">\n        <view class=\"loading-indicator\"></view>\n      </view>\n\n      <!-- 纸条内容 -->\n      <view v-else-if=\"boxData.id\" class=\"content\">\n      <!-- 纸条信息 -->\n      <view class=\"box-card\">\n        <!-- 用户信息 -->\n        <view class=\"user-info\">\n          <view class=\"avatar\">\n            <image :src=\"boxData.avatar || '/static/default-avatar.png'\" class=\"avatar-img\"></image>\n          </view>\n          <view class=\"user-details\">\n            <view class=\"username\">{{boxData.nickname || '匿名用户'}}</view>\n            <view class=\"user-meta\">\n              <text class=\"gender-icon\">{{getGenderIcon(boxData.sex)}}</text>\n              <text class=\"age\">{{boxData.age || '未知'}} · {{boxData.location || 'Sub'}}</text>\n            </view>\n          </view>\n          <view class=\"type-tag\" :class=\"'type-' + boxData.type\">\n            <text class=\"type-icon\">{{getTypeIcon(boxData.type)}}</text>\n            <text class=\"type-text\">{{getTypeText(boxData.type)}}</text>\n          </view>\n        </view>\n\n        <!-- 纸条内容 -->\n        <view class=\"box-content\">\n          <!-- 文字内容 -->\n          <view v-if=\"boxData.type !== 4\" class=\"text-content\">\n            <text class=\"content-text\">{{boxData.content}}</text>\n          </view>\n          \n          <!-- 语音内容 -->\n          <view v-else class=\"voice-content\">\n            <view class=\"voice-player\" @tap=\"toggleVoice\">\n              <text class=\"voice-icon\">{{isPlaying ? '⏸️' : '▶️'}}</text>\n              <text class=\"voice-duration\">{{boxData.voice_duration}}s</text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 时间信息 -->\n        <view class=\"time-info\">\n          <text class=\"time-text\">{{formatTime(boxData.create_time)}}</text>\n        </view>\n      </view>\n\n      <!-- 回应区域 -->\n      <view class=\"response-section\">\n        <view class=\"section-title\">\n          <text>回应 ({{responses.length}})</text>\n        </view>\n\n        <!-- 隐私保护提示 -->\n        <view v-if=\"!isAuthor && responses.length > 0\" class=\"privacy-notice\">\n          <text class=\"notice-icon\">🔒</text>\n          <text class=\"notice-text\">为保护用户隐私，除了您自己的回应外，其他回应者信息将显示为匿名</text>\n        </view>\n\n        <!-- 回应列表 -->\n        <view class=\"response-list\">\n          <view \n            v-for=\"item in responses\" \n            :key=\"item.id\"\n            class=\"response-item\"\n          >\n            <view class=\"response-user\">\n              <!-- 纸条作者和回应者本人可以看到真实信息 -->\n              <image\n                :src=\"canViewResponseUserInfo(item) ? (item.avatar || '/static/images/def_avatar.png') : '/static/images/def_avatar.png'\"\n                class=\"response-avatar\"\n              ></image>\n              <text class=\"response-username\">\n                {{canViewResponseUserInfo(item) ? item.nickname : '匿名用户'}}\n              </text>\n              <text class=\"response-time\">{{formatTime(item.create_time)}}</text>\n              <!-- 标识自己的回应 -->\n              <text v-if=\"currentUserId && item.uid === currentUserId\" class=\"my-response-tag\">我的回应</text>\n            </view>\n            <view class=\"response-content\">\n              <text>{{item.content}}</text>\n            </view>\n          </view>\n        </view>\n\n        <!-- 回应输入 -->\n        <view class=\"response-input\">\n          <textarea \n            class=\"input-area\" \n            placeholder=\"写下你的回应...\" \n            maxlength=\"200\" \n            v-model=\"responseText\"\n          />\n          <view class=\"input-actions\">\n            <text class=\"char-count\">{{responseText.length}}/200</text>\n            <view class=\"send-btn\" :class=\"{'active': canSend}\" @tap=\"sendResponse\">\n              <text>发送</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n      <!-- 错误状态 -->\n      <view v-else class=\"error-state\">\n        <text class=\"error-icon\">😕</text>\n        <text class=\"error-text\">纸条不存在或已被删除</text>\n        <view class=\"error-btn\" @tap=\"goBack\">\n          <text>返回</text>\n        </view>\n      </view>\n    </view>\n\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\n      <view class=\"tips-box df\">\n        <view class=\"tips-item bfh\">{{ tipsTitle }}</view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport { getTreeHoleBoxDetail, responseTreeHoleBox, getTreeHoleResponseList } from '@/api/social.js'\nimport { checkLogin, toLogin } from '@/libs/login.js'\nimport { getUserInfo } from '@/api/user.js'\n\nexport default {\n  data() {\n    return {\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\n      tipsTitle: '',\n      boxId: 0,\n      boxData: {},\n      responses: [],\n      responseText: '',\n      loading: false,\n      isPlaying: false,\n      currentUserId: null // 当前用户ID\n    }\n  },\n  computed: {\n    canSend() {\n      return this.responseText.trim().length >= 5\n    },\n\n    // 判断当前用户是否为纸条作者\n    isAuthor() {\n      return this.boxData && this.currentUserId && this.boxData.uid === this.currentUserId\n    }\n  },\n\n  async onLoad(options) {\n    console.log('=== 纸条详情页面加载 ===')\n    console.log('页面参数:', options)\n\n    // 获取当前用户信息\n    await this.getCurrentUser()\n\n    if (options.id) {\n      this.boxId = parseInt(options.id)\n      this.loadBoxDetail()\n    } else {\n      uni.showToast({\n        title: '参数错误',\n        icon: 'none'\n      })\n    }\n  },\n\n  methods: {\n    // 判断是否可以查看回应者信息\n    canViewResponseUserInfo(responseItem) {\n      // 纸条作者可以查看所有回应者信息\n      if (this.isAuthor) {\n        return true\n      }\n\n      // 用户可以查看自己的回应信息\n      if (this.currentUserId && responseItem.uid === this.currentUserId) {\n        return true\n      }\n\n      // 其他情况不能查看\n      return false\n    },\n    goBack() {\n      uni.navigateBack()\n    },\n\n    // 获取当前用户信息\n    async getCurrentUser() {\n      try {\n        const userInfo = uni.getStorageSync('userInfo')\n        if (userInfo && userInfo.uid) {\n          this.currentUserId = userInfo.uid\n          console.log('当前用户ID:', this.currentUserId)\n        } else {\n          // 如果本地没有，尝试从API获取\n          const result = await getUserInfo()\n          if (result.status === 200 && result.data) {\n            this.currentUserId = result.data.uid\n            console.log('从API获取用户ID:', this.currentUserId)\n          }\n        }\n      } catch (error) {\n        console.error('获取用户信息失败:', error)\n      }\n    },\n\n    showMoreActions() {\n      uni.showActionSheet({\n        itemList: ['举报', '分享'],\n        success: (res) => {\n          if (res.tapIndex === 0) {\n            this.reportBox()\n          } else if (res.tapIndex === 1) {\n            this.shareBox()\n          }\n        }\n      })\n    },\n\n    async loadBoxDetail() {\n      console.log('=== 加载纸条详情 ===')\n      console.log('纸条ID:', this.boxId)\n      \n      this.loading = true\n      \n      try {\n        const result = await getTreeHoleBoxDetail(this.boxId)\n        console.log('详情API响应:', result)\n\n        if (result.status === 200 && result.data) {\n          this.boxData = result.data\n          console.log('纸条数据:', this.boxData)\n          \n          // 加载回应列表\n          this.loadResponses()\n        } else {\n          throw new Error(result.msg || '获取纸条详情失败')\n        }\n      } catch (error) {\n        console.error('加载纸条详情失败:', error)\n        uni.showToast({\n          title: error.message || '加载失败',\n          icon: 'none'\n        })\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async loadResponses() {\n      try {\n        const result = await getTreeHoleResponseList({\n          box_id: this.boxId,\n          page: 1,\n          limit: 50\n        })\n\n        if (result.status === 200 && result.data) {\n          this.responses = result.data.list || []\n          console.log('回应列表:', this.responses)\n        }\n      } catch (error) {\n        console.error('加载回应列表失败:', error)\n      }\n    },\n\n    async sendResponse() {\n      if (!this.canSend) {\n        return\n      }\n\n      // 检查登录状态\n      if (!checkLogin()) {\n        uni.showModal({\n          title: '提示',\n          content: '请先登录后再回应',\n          confirmText: '去登录',\n          success: (res) => {\n            if (res.confirm) {\n              toLogin()\n            }\n          }\n        })\n        return\n      }\n\n      try {\n        const result = await responseTreeHoleBox({\n          box_id: this.boxId,\n          content: this.responseText.trim()\n        })\n\n        if (result.status === 200) {\n          uni.showToast({\n            title: '回应成功',\n            icon: 'success'\n          })\n          \n          this.responseText = ''\n          this.loadResponses() // 重新加载回应列表\n        } else {\n          throw new Error(result.msg || '回应失败')\n        }\n      } catch (error) {\n        console.error('发送回应失败:', error)\n        uni.showToast({\n          title: error.message || '回应失败',\n          icon: 'none'\n        })\n      }\n    },\n\n    toggleVoice() {\n      // TODO: 实现语音播放功能\n      this.isPlaying = !this.isPlaying\n      uni.showToast({\n        title: this.isPlaying ? '开始播放' : '暂停播放',\n        icon: 'none'\n      })\n    },\n\n    getGenderIcon(sex) {\n      return sex === 1 ? '♂' : sex === 2 ? '♀' : '⚪'\n    },\n\n    getTypeIcon(type) {\n      const icons = {\n        1: '❓',\n        2: '🤫',\n        3: '🌠',\n        4: '🎵'\n      }\n      return icons[type] || '📝'\n    },\n\n    getTypeText(type) {\n      const texts = {\n        1: '问题咨询',\n        2: '秘密',\n        3: '心愿',\n        4: '语音纸条'\n      }\n      return texts[type] || '纸条'\n    },\n\n    formatTime(time) {\n      if (!time) return ''\n      \n      const now = new Date()\n      const createTime = new Date(time)\n      const diff = now - createTime\n      const minutes = Math.floor(diff / 60000)\n      \n      if (minutes < 1) return '刚刚'\n      if (minutes < 60) return `${minutes}分钟前`\n      if (minutes < 1440) return `${Math.floor(minutes / 60)}小时前`\n      return `${Math.floor(minutes / 1440)}天前`\n    },\n\n    reportBox() {\n      uni.showToast({\n        title: '举报功能开发中',\n        icon: 'none'\n      })\n    },\n\n    shareBox() {\n      uni.showToast({\n        title: '分享功能开发中',\n        icon: 'none'\n      })\n    },\n\n    // 显示提示信息\n    opTipsPopup(msg) {\n      this.tipsTitle = msg;\n      this.$refs.tipsPopup.open();\n      setTimeout(() => {\n        this.$refs.tipsPopup.close();\n      }, 2000);\n    }\n  }\n}\n</script>\n\n<style>\n.nav-bar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  z-index: 99;\n  box-sizing: border-box;\n}\n.bar-box .bar-back {\n  padding: 0 30rpx;\n  width: 34rpx;\n  height: 100%;\n}\n.bar-box .bar-title {\n  max-width: 60%;\n  font-size: 32rpx;\n  font-weight: 700;\n  flex: 1;\n  text-align: center;\n}\n.nav-action {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 36rpx;\n  padding: 0 30rpx;\n}\n.content-box {\n  width: calc(100% - 60rpx);\n  padding: 30rpx;\n}\n\n/* 加载中状态样式 */\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 60rpx;\n  margin-bottom: 20rpx;\n}\n.loading-indicator {\n  width: 30rpx;\n  height: 30rpx;\n  border: 3rpx solid #f3f3f3;\n  border-top: 3rpx solid #000;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.error-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 40rpx;\n  text-align: center;\n}\n\n.error-icon {\n  font-size: 120rpx;\n  margin-bottom: 30rpx;\n}\n\n.error-text {\n  font-size: 28rpx;\n  color: #999;\n  margin-bottom: 40rpx;\n}\n\n.error-btn {\n  padding: 20rpx 40rpx;\n  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);\n  border-radius: 25rpx;\n  color: white;\n}\n\n.content {\n  width: 100%;\n}\n\n.box-card {\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n\n.avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  overflow: hidden;\n  margin-right: 20rpx;\n}\n\n.avatar-img {\n  width: 100%;\n  height: 100%;\n}\n\n.user-details {\n  flex: 1;\n}\n\n.username {\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n}\n\n.user-meta {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.type-tag {\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n}\n\n.type-1 { background: #e3f2fd; color: #1976d2; }\n.type-2 { background: #fce4ec; color: #c2185b; }\n.type-3 { background: #f3e5f5; color: #7b1fa2; }\n.type-4 { background: #e8f5e8; color: #388e3c; }\n\n.box-content {\n  margin-bottom: 20rpx;\n}\n\n.content-text {\n  font-size: 30rpx;\n  line-height: 1.6;\n  color: #333;\n}\n\n.voice-content {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n  background: #f5f5f5;\n  border-radius: 12rpx;\n}\n\n.voice-player {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n  font-size: 28rpx;\n}\n\n.time-info {\n  text-align: right;\n}\n\n.time-text {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.response-section {\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-bottom: 30rpx;\n}\n\n/* 隐私保护提示 */\n.privacy-notice {\n  display: flex;\n  align-items: center;\n  background: #f8f9fa;\n  border: 1rpx solid #e9ecef;\n  border-radius: 12rpx;\n  padding: 20rpx;\n  margin-bottom: 30rpx;\n}\n\n.notice-icon {\n  font-size: 28rpx;\n  margin-right: 12rpx;\n  color: #6c757d;\n}\n\n.notice-text {\n  font-size: 24rpx;\n  color: #6c757d;\n  line-height: 1.4;\n}\n\n.response-item {\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.response-user {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15rpx;\n}\n\n.response-avatar {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n  margin-right: 15rpx;\n}\n\n.response-username {\n  font-size: 28rpx;\n  font-weight: bold;\n  margin-right: 20rpx;\n}\n\n.response-time {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.my-response-tag {\n  font-size: 20rpx;\n  color: #007aff;\n  background: #e3f2fd;\n  padding: 4rpx 8rpx;\n  border-radius: 8rpx;\n  margin-left: 12rpx;\n}\n\n.response-content {\n  font-size: 28rpx;\n  line-height: 1.5;\n  color: #333;\n}\n\n.response-input {\n  margin-top: 30rpx;\n  padding-top: 30rpx;\n  border-top: 1rpx solid #f0f0f0;\n}\n\n.input-area {\n  width: 100%;\n  min-height: 120rpx;\n  padding: 20rpx;\n  background: #f8f9fa;\n  border-radius: 12rpx;\n  font-size: 28rpx;\n  margin-bottom: 20rpx;\n}\n\n.input-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.char-count {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.send-btn {\n  padding: 16rpx 32rpx;\n  background: #e9ecef;\n  border-radius: 20rpx;\n  color: #999;\n  font-size: 28rpx;\n}\n\n.send-btn.active {\n  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);\n  color: white;\n}\n\n.df {\n  display: flex;\n  align-items: center;\n}\n.bfw {\n  backdrop-filter: blur(10px);\n  -webkit-backdrop-filter: blur(10px);\n  background: rgba(255,255,255,.8);\n}\n.bfh {\n  background: #000;\n  color: #fff;\n  padding: 20rpx 40rpx;\n  border-radius: 12rpx;\n  font-size: 24rpx;\n  font-weight: 700;\n}\n.tips-box {\n  justify-content: center;\n  width: 100%;\n}\n.ohto {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n</style>\n", "import MiniProgramPage from 'D:/uniapp/vue3/pages/note/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getUserInfo", "getTreeHoleBoxDetail", "getTreeHoleResponseList", "checkLogin", "<PERSON><PERSON><PERSON><PERSON>", "responseTreeHoleBox"], "mappings": ";;;;;;AA+IA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB,KAAK,OAAO,MAAM,mBAAmB;AAAA,MACtD,gBAAgB,KAAK,OAAO,MAAM,kBAAkB;AAAA,MACpD,WAAW;AAAA,MACX,OAAO;AAAA,MACP,SAAS,CAAE;AAAA,MACX,WAAW,CAAE;AAAA,MACb,cAAc;AAAA,MACd,SAAS;AAAA,MACT,WAAW;AAAA,MACX,eAAe;AAAA;AAAA,IACjB;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,UAAU;AACR,aAAO,KAAK,aAAa,KAAM,EAAC,UAAU;AAAA,IAC3C;AAAA;AAAA,IAGD,WAAW;AACT,aAAO,KAAK,WAAW,KAAK,iBAAiB,KAAK,QAAQ,QAAQ,KAAK;AAAA,IACzE;AAAA,EACD;AAAA,EAED,MAAM,OAAO,SAAS;AACpBA,kBAAAA,MAAA,MAAA,OAAA,gCAAY,kBAAkB;AAC9BA,kBAAAA,MAAA,MAAA,OAAA,gCAAY,SAAS,OAAO;AAG5B,UAAM,KAAK,eAAe;AAE1B,QAAI,QAAQ,IAAI;AACd,WAAK,QAAQ,SAAS,QAAQ,EAAE;AAChC,WAAK,cAAc;AAAA,WACd;AACLA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAAA,IACH;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,wBAAwB,cAAc;AAEpC,UAAI,KAAK,UAAU;AACjB,eAAO;AAAA,MACT;AAGA,UAAI,KAAK,iBAAiB,aAAa,QAAQ,KAAK,eAAe;AACjE,eAAO;AAAA,MACT;AAGA,aAAO;AAAA,IACR;AAAA,IACD,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACrB,UAAI;AACF,cAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,YAAI,YAAY,SAAS,KAAK;AAC5B,eAAK,gBAAgB,SAAS;AAC9BA,wBAAA,MAAA,MAAA,OAAA,gCAAY,WAAW,KAAK,aAAa;AAAA,eACpC;AAEL,gBAAM,SAAS,MAAMC,qBAAY;AACjC,cAAI,OAAO,WAAW,OAAO,OAAO,MAAM;AACxC,iBAAK,gBAAgB,OAAO,KAAK;AACjCD,0BAAY,MAAA,MAAA,OAAA,gCAAA,eAAe,KAAK,aAAa;AAAA,UAC/C;AAAA,QACF;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,gCAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA,IAED,kBAAkB;AAChBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,MAAM,IAAI;AAAA,QACrB,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,aAAa,GAAG;AACtB,iBAAK,UAAU;AAAA,qBACN,IAAI,aAAa,GAAG;AAC7B,iBAAK,SAAS;AAAA,UAChB;AAAA,QACF;AAAA,OACD;AAAA,IACF;AAAA,IAED,MAAM,gBAAgB;AACpBA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,gBAAgB;AAC5BA,uEAAY,SAAS,KAAK,KAAK;AAE/B,WAAK,UAAU;AAEf,UAAI;AACF,cAAM,SAAS,MAAME,gCAAqB,KAAK,KAAK;AACpDF,sBAAAA,MAAY,MAAA,OAAA,gCAAA,YAAY,MAAM;AAE9B,YAAI,OAAO,WAAW,OAAO,OAAO,MAAM;AACxC,eAAK,UAAU,OAAO;AACtBA,wBAAA,MAAA,MAAA,OAAA,gCAAY,SAAS,KAAK,OAAO;AAGjC,eAAK,cAAc;AAAA,eACd;AACL,gBAAM,IAAI,MAAM,OAAO,OAAO,UAAU;AAAA,QAC1C;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,gCAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,IAED,MAAM,gBAAgB;AACpB,UAAI;AACF,cAAM,SAAS,MAAMG,mCAAwB;AAAA,UAC3C,QAAQ,KAAK;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,SACR;AAED,YAAI,OAAO,WAAW,OAAO,OAAO,MAAM;AACxC,eAAK,YAAY,OAAO,KAAK,QAAQ,CAAC;AACtCH,wBAAY,MAAA,MAAA,OAAA,gCAAA,SAAS,KAAK,SAAS;AAAA,QACrC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,gCAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA,IAED,MAAM,eAAe;AACnB,UAAI,CAAC,KAAK,SAAS;AACjB;AAAA,MACF;AAGA,UAAI,CAACI,WAAU,WAAA,GAAI;AACjBJ,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACfK,iCAAQ;AAAA,YACV;AAAA,UACF;AAAA,SACD;AACD;AAAA,MACF;AAEA,UAAI;AACF,cAAM,SAAS,MAAMC,+BAAoB;AAAA,UACvC,QAAQ,KAAK;AAAA,UACb,SAAS,KAAK,aAAa,KAAK;AAAA,SACjC;AAED,YAAI,OAAO,WAAW,KAAK;AACzBN,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAED,eAAK,eAAe;AACpB,eAAK,cAAc;AAAA,eACd;AACL,gBAAM,IAAI,MAAM,OAAO,OAAO,MAAM;AAAA,QACtC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA,IAED,cAAc;AAEZ,WAAK,YAAY,CAAC,KAAK;AACvBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,YAAY,SAAS;AAAA,QACjC,MAAM;AAAA,OACP;AAAA,IACF;AAAA,IAED,cAAc,KAAK;AACjB,aAAO,QAAQ,IAAI,MAAM,QAAQ,IAAI,MAAM;AAAA,IAC5C;AAAA,IAED,YAAY,MAAM;AAChB,YAAM,QAAQ;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,aAAO,MAAM,IAAI,KAAK;AAAA,IACvB;AAAA,IAED,YAAY,MAAM;AAChB,YAAM,QAAQ;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,aAAO,MAAM,IAAI,KAAK;AAAA,IACvB;AAAA,IAED,WAAW,MAAM;AACf,UAAI,CAAC;AAAM,eAAO;AAElB,YAAM,MAAM,oBAAI,KAAK;AACrB,YAAM,aAAa,IAAI,KAAK,IAAI;AAChC,YAAM,OAAO,MAAM;AACnB,YAAM,UAAU,KAAK,MAAM,OAAO,GAAK;AAEvC,UAAI,UAAU;AAAG,eAAO;AACxB,UAAI,UAAU;AAAI,eAAO,GAAG,OAAO;AACnC,UAAI,UAAU;AAAM,eAAO,GAAG,KAAK,MAAM,UAAU,EAAE,CAAC;AACtD,aAAO,GAAG,KAAK,MAAM,UAAU,IAAI,CAAC;AAAA,IACrC;AAAA,IAED,YAAY;AACVA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAAA,IACF;AAAA,IAED,WAAW;AACTA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,KAAK;AACf,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU;AACrB,iBAAW,MAAM;AACf,aAAK,MAAM,UAAU;MACtB,GAAE,GAAI;AAAA,IACT;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClZA,GAAG,WAAW,eAAe;"}