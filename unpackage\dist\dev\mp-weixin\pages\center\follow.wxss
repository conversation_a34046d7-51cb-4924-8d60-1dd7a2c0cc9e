
/* 优化性能：添加will-change和transform3d */
.nav-bar{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
  /* APP端兼容性：条件编译backdrop-filter */

  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
.bar-box .bar-back{
  padding: 0 30rpx;
  width: 34rpx;
  height: 100%;
}
.bar-box .bar-title{
  max-width: 60%;
  font-size: 32rpx;
  font-weight: 700;
}
.nav-box{
  width: 100%;
  height: 80rpx;
}
.nav-box .nav-item{
  padding: 0 30rpx;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.nav-box .nav-item text{
  font-weight: 700;
  transition: all .3s ease-in-out;
}
.nav-box .nav-line{
  position: absolute;
  bottom: 12rpx;
  width: 18rpx;
  height: 6rpx;
  border-radius: 6rpx;
  background: #000;
  transition: opacity .3s ease-in-out;
}
.content-box{
  width: calc(100% - 80rpx);
  padding: 20rpx 40rpx;
}
/* 加载中状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60rpx;
  margin-bottom: 20rpx;
}
.loading-indicator {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.list-box{
  width: 100%;
  padding: 20rpx 0;
  justify-content: space-between;
}
.list-box .list-item{
  width: calc(100% - 120rpx - 2px);
}
.list-item .list-avatar{
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 1px solid #f8f8f8;
  overflow: hidden;
}
.list-item .item-content{
  width: calc(100% - 100rpx);
  margin-left: 20rpx;
}
.item-content .name{
  font-size: 28rpx;
  font-weight: 700;
}
.item-content .intro{
  color: #999;
  font-size: 20rpx;
}
.list-box .list-btn{
  width: 100rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  font-size: 20rpx;
  font-weight: 700;
  border-radius: 8rpx;
}
.bg1{
  color: #999;
  background: #f8f8f8;
}
.bg2{
  color: #fff;
  background: #000;
}
.heio{
  transition: height 0.3s;
}
.empty-box{
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-box image{
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-box .e1{
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.empty-box .e2{
  font-size: 24rpx;
  color: #999;
}
.df{
  display: flex;
  align-items: center;
}
.bfw{
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,.8);
}
.ohto{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
