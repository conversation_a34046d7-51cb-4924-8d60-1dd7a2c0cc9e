{"version": 3, "file": "index.js", "sources": ["utils/index.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport {\n\tspread\n} from \"@/api/user\";\nimport Cache from \"@/utils/cache\";\nimport {\n\tgetCustomerType\n} from '@/api/api.js'\nimport {\n\tgetWorkermanUrl\n} from '@/api/kefu.js'\nimport store from '@/store';\nimport appConfig from '@/config/app.js';\n/**\n * 绑定用户授权\n * @param {Object} puid\n */\nexport function silenceBindingSpread(app) {\n\t//#ifdef H5\n\tlet puid = Cache.get('spread'),\n\t\tcode = 0;\n\t//#endif\n\n\t//#ifdef MP || APP-PLUS\n\tlet puid = app.spid,\n\t\tcode = app.code;\n\t//#endif\n\n\tpuid = parseInt(puid);\n\tif (Number.isNaN(puid)) {\n\t\tpuid = 0;\n\t}\n\tif ((code || puid) && store.state.app.token) {\n\t\tspread({\n\t\t\tpuid,\n\t\t\tcode\n\t\t}).then(res => {\n\t\t\t//#ifdef H5\n\t\t\tCache.clear('spread');\n\t\t\t//#endif\n\t\t\t//#ifdef MP || APP-PLUS\n\t\t\tapp.spid = 0;\n\t\t\tapp.code = 0;\n\t\t\t//#endif\n\n\t\t}).catch(res => {});\n\t}\n}\n\nexport function isWeixin() {\n\treturn navigator.userAgent.toLowerCase().indexOf(\"micromessenger\") !== -1;\n}\n\nexport function getCustomer(url) {\n\tgetCustomerType().then(res => {\n\t\tlet type = res.data.customer_type\n\t\tif (type == '0') {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: url || '/pages/extension/customer_list/chat'\n\t\t\t})\n\t\t} else if (type == '1') {\n\t\t\tuni.makePhoneCall({\n\t\t\t\tphoneNumber: res.data.customer_phone //客服电话\n\t\t\t});\n\t\t} else {\n\t\t\t// #ifdef APP-PLUS\n\t\t\tplus.runtime.openURL(res.data.customer_url)\n\t\t\t// #endif\n\t\t\t// #ifdef H5 || MP\n\t\t\tif (res.data.customer_url.indexOf('work.weixin.qq.com') > 0) {\n\t\t\t\t// #ifdef H5\n\t\t\t\treturn window.location.href = res.data.customer_url\n\t\t\t\t// #endif\t\t\t\n\t\t\t\t// #ifdef MP\n\t\t\t\tuni.openCustomerServiceChat({\n\t\t\t\t\textInfo: {\n\t\t\t\t\t\turl: res.data.customer_url\n\t\t\t\t\t},\n\t\t\t\t\tcorpId: res.data.customer_corpId,\n\t\t\t\t\tsuccess(res) {},\n\t\t\t\t\tfail(err) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: err.errMsg,\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t} else {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/annex/web_view/index?url=${res.data.customer_url}`\n\t\t\t\t});\n\t\t\t}\n\t\t\t// #endif\n\t\t}\n\t})\n}\n\n\nexport function parseQuery() {\n\tconst res = {};\n\n\tconst query = (location.href.split(\"?\")[1] || \"\")\n\t\t.trim()\n\t\t.replace(/^(\\?|#|&)/, \"\");\n\n\tif (!query) {\n\t\treturn res;\n\t}\n\n\tquery.split(\"&\").forEach(param => {\n\t\tconst parts = param.replace(/\\+/g, \" \").split(\"=\");\n\t\tconst key = decodeURIComponent(parts.shift());\n\t\tconst val = parts.length > 0 ? decodeURIComponent(parts.join(\"=\")) : null;\n\n\t\tif (res[key] === undefined) {\n\t\t\tres[key] = val;\n\t\t} else if (Array.isArray(res[key])) {\n\t\t\tres[key].push(val);\n\t\t} else {\n\t\t\tres[key] = [res[key], val];\n\t\t}\n\t});\n\n\treturn res;\n}\nexport function updateURLParameter(url, param, paramVal) {\n\tvar newAdditionalURL = \"\";\n\tvar tempArray = url.split(\"?\");\n\tvar baseURL = tempArray[0];\n\tvar additionalURL = tempArray[1];\n\tvar temp = \"\";\n\tif (additionalURL) {\n\t\ttempArray = additionalURL.split(\"&\");\n\t\tfor (let i = 0; i < tempArray.length; i++) {\n\t\t\tif (tempArray[i].split('=')[0] != param) {\n\t\t\t\tnewAdditionalURL += temp + tempArray[i];\n\t\t\t\ttemp = \"&\";\n\t\t\t}\n\t\t}\n\t}\n\n\tvar rows_txt = temp + \"\" + param + \"=\" + paramVal;\n\treturn baseURL + \"?\" + newAdditionalURL + rows_txt;\n}\n\nlet VUE_APP_WS_URL = Cache.get('WORKERMAN_URL') || ''\n\n// 延迟初始化WebSocket URL，避免循环依赖问题\nfunction initWorkermanUrl() {\n\tif (!VUE_APP_WS_URL) {\n\t\tgetWorkermanUrl().then(res => {\n\t\t\tCache.set('WORKERMAN_URL', res.data.chat)\n\t\t\tVUE_APP_WS_URL = res.data.chat;\n\t\t}).catch(err => {\n\t\t\tconsole.warn('获取Workerman URL失败:', err);\n\t\t});\n\t}\n}\n\n// 添加API基础URL导出\nconst { HTTP_REQUEST_URL } = appConfig;\nconst VUE_APP_API_URL = HTTP_REQUEST_URL;\n\nexport {\n\tVUE_APP_WS_URL,\n\tVUE_APP_API_URL,\n\tinitWorkermanUrl\n}\n\nexport default parseQuery;\n"], "names": ["store", "spread", "VUE_APP_WS_URL", "<PERSON><PERSON>", "getWorkermanUrl", "uni", "appConfig"], "mappings": ";;;;;;;;AA0BO,SAAS,qBAAqB,KAAK;AAOzC,MAAI,OAAO,IAAI,MACd,OAAO,IAAI;AAGZ,SAAO,SAAS,IAAI;AACpB,MAAI,OAAO,MAAM,IAAI,GAAG;AACvB,WAAO;AAAA,EACP;AACD,OAAK,QAAQ,SAASA,YAAK,MAAC,MAAM,IAAI,OAAO;AAC5CC,oBAAO;AAAA,MACN;AAAA,MACA;AAAA,IACH,CAAG,EAAE,KAAK,SAAO;AAKd,UAAI,OAAO;AACX,UAAI,OAAO;AAAA,IAGX,CAAA,EAAE,MAAM,SAAO;AAAA,IAAA,CAAE;AAAA,EAClB;AACF;AAoGIC,QAAc,iBAAGC,kBAAM,IAAI,eAAe,KAAK;AAGnD,SAAS,mBAAmB;AAC3B,MAAI,CAACD,QAAAA,gBAAgB;AACpBE,6BAAiB,EAAC,KAAK,SAAO;AAC7BD,kBAAAA,MAAM,IAAI,iBAAiB,IAAI,KAAK,IAAI;AACxCD,+BAAiB,IAAI,KAAK;AAAA,IAC7B,CAAG,EAAE,MAAM,SAAO;AACfG,oBAAa,MAAA,MAAA,QAAA,yBAAA,sBAAsB,GAAG;AAAA,IACzC,CAAG;AAAA,EACD;AACF;AAGA,MAAM,EAAE,iBAAkB,IAAGC;AACxB,MAAC,kBAAkB;;;;"}