"use strict";
const utils_cache = require("../utils/cache.js");
function ActivePermission(key) {
  const config = utils_cache.Cache.get("BASIC_CONFIG") || {};
  let arr = config && config.site_func || ["seckill", "bargain", "combination"];
  let index = arr.indexOf(key);
  if (index > -1) {
    return true;
  } else {
    return false;
  }
}
exports.ActivePermission = ActivePermission;
//# sourceMappingURL=../../.sourcemap/mp-weixin/libs/permission.js.map
