{"version": 3, "file": "index.js", "sources": ["pages/users/user_pwd_edit/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlcnMvdXNlcl9wd2RfZWRpdC9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"container\" :style=\"colorStyle\">\r\n\t\t<navbar></navbar>\r\n\t\t<view class=\"content-box\">\r\n\t\t\t<view class=\"logo-box\">\r\n\t\t\t\t<image src=\"/static/logo.png\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"login-box\">\r\n\t\t\t\t<view class=\"title\">修改密码</view>\r\n\t\t\t\t<view class=\"sub-title\">{{$t(`当前手机号`)}}：{{phone}}</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"input-item df\">\r\n\t\t\t\t\t<view class=\"icon-pwd\">\r\n\t\t\t\t\t\t<image src=\"/static/img/icon-pwd.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input type='password' :placeholder='$t(`设置新密码`)' v-model=\"password\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"input-item df\">\r\n\t\t\t\t\t<view class=\"icon-pwd\">\r\n\t\t\t\t\t\t<image src=\"/static/img/icon-pwd.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input type='password' :placeholder='$t(`确认新密码`)' v-model=\"qr_password\"/>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"input-item df\">\r\n\t\t\t\t\t<view class=\"icon-code\">\r\n\t\t\t\t\t\t<image src=\"/static/img/icon-code.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<input type='number' :placeholder='$t(`填写验证码`)' maxlength=\"6\" v-model=\"captcha\"/>\r\n\t\t\t\t\t<view :class=\"['send-code', disabled ? 'active' : '']\" @tap=\"code\">{{text}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"btn-submit df\" @tap=\"editPwd\">{{$t(`确认修改`)}}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<Verify @success=\"success\" :captchaType=\"captchaType\" :imgSize=\"{ width: '330px', height: '155px' }\" ref=\"verify\"></Verify>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport sendVerifyCode from \"@/mixins/SendVerifyCode\";\r\n\timport {\r\n\t\tphoneRegisterReset,\r\n\t\tverifyCode\r\n\t} from '@/api/api.js';\r\n\timport {\r\n\t\tgetUserInfo,\r\n\t\tregisterVerify\r\n\t} from '@/api/user.js';\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport colors from '@/mixins/color.js';\r\n\timport Verify from '../components/verify/index.vue';\r\n\texport default {\r\n\t\tmixins: [sendVerifyCode, colors],\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t\tVerify\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuserInfo: {},\r\n\t\t\t\tphone: '',\r\n\t\t\t\tpassword: '',\r\n\t\t\t\tcaptcha: '',\r\n\t\t\t\tqr_password: '',\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tkey: '',\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\twatch: {\r\n\t\t\tisLogin: {\r\n\t\t\t\thandler: function(newV, oldV) {\r\n\t\t\t\t\tif (newV) {\r\n\t\t\t\t\t\tthis.getUserInfo();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.getUserInfo();\r\n\t\t\t\tverifyCode().then(res => {\r\n\t\t\t\t\tthis.$set(this, 'key', res.data.key)\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin()\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 授权回调\r\n\t\t\t */\r\n\t\t\tonLoadFun: function(e) {\r\n\t\t\t\tthis.getUserInfo();\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取个人用户信息\r\n\t\t\t */\r\n\t\t\tgetUserInfo: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetUserInfo().then(res => {\r\n\t\t\t\t\tlet tel = res.data.phone;\r\n\t\t\t\t\tlet phone = tel.substr(0, 3) + \"****\" + tel.substr(7);\r\n\t\t\t\t\tthat.$set(that, 'userInfo', res.data);\r\n\t\t\t\t\tthat.phone = phone;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 发送验证码\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tasync code() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (!that.userInfo.phone) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`手机号码不存在,无法发送验证码！`)\r\n\t\t\t\t});\r\n\t\t\t\tthis.$refs.verify.show()\r\n\r\n\t\t\t},\r\n\t\t\tasync success(data) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthis.$refs.verify.hide()\r\n\t\t\t\tawait registerVerify({\r\n\t\t\t\t\tphone: that.userInfo.phone,\r\n\t\t\t\t\ttype: 'reset',\r\n\t\t\t\t\tkey: that.key,\r\n\t\t\t\t\tcaptchaType: this.captchaType,\r\n\t\t\t\t\tcaptchaVerification: data.captchaVerification\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.sendCode()\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: res.msg\r\n\t\t\t\t\t});\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * H5登录 修改密码\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\teditPwd: function(e) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tpassword = e.detail.value.password,\r\n\t\t\t\t\tqr_password = e.detail.value.qr_password,\r\n\t\t\t\t\tcaptcha = e.detail.value.captcha;\r\n\t\t\t\tif (!password) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请输入新密码`)\r\n\t\t\t\t});\r\n\t\t\t\tif (qr_password != password) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`两次输入的密码不一致！`)\r\n\t\t\t\t});\r\n\t\t\t\tif (!captcha) return that.$util.Tips({\r\n\t\t\t\t\ttitle: that.$t(`请输入验证码`)\r\n\t\t\t\t});\r\n\t\t\t\tphoneRegisterReset({\r\n\t\t\t\t\taccount: that.userInfo.phone,\r\n\t\t\t\t\tcaptcha: captcha,\r\n\t\t\t\t\tpassword: password\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: res.msg\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ttab: 3,\r\n\t\t\t\t\t\turl: 1\r\n\t\t\t\t\t});\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.container {\r\n\twidth: 100%;\r\n\tbackground: #fff;\r\n\theight: 100vh;\r\n\toverflow: hidden;\r\n}\r\n\r\n.content-box {\r\n\twidth: 100%;\r\n\tmargin-top: 30px;\r\n}\r\n\r\n.logo-box {\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\t/* #ifdef APP-VUE */\r\n\tmargin-top: 50rpx;\r\n\t/* #endif */\r\n\t/* #ifndef APP-VUE */\r\n\tmargin-top: 200rpx;\r\n\t/* #endif */\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.logo-box image {\r\n\twidth: 240rpx;\r\n\theight: 240rpx;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.login-box {\r\n\twidth: calc(100% - 60rpx);\r\n\tmargin: 30rpx;\r\n}\r\n\r\n.login-box .title {\r\n\tfont-size: 40rpx;\r\n\tfont-weight: 700;\r\n\tcolor: #000;\r\n}\r\n\r\n.login-box .sub-title {\r\n\tmargin-top: 20rpx;\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.login-box .input-item {\r\n\tmargin-top: 60rpx;\r\n\tpadding: 0 30rpx;\r\n\twidth: calc(100% - 60rpx);\r\n\theight: 100rpx;\r\n\tborder-radius: 100rpx;\r\n\tbackground: #f8f8f8;\r\n\tposition: relative;\r\n}\r\n\r\n.input-item .icon-pwd,\r\n.input-item .icon-code {\r\n\tmargin-right: 20rpx;\r\n\twidth: 36rpx;\r\n\theight: 36rpx;\r\n}\r\n\r\n.input-item .icon-pwd image,\r\n.input-item .icon-code image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.input-item input {\r\n\twidth: calc(100% - 56rpx);\r\n\theight: 100%;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.input-item .send-code {\r\n\tposition: absolute;\r\n\tright: 30rpx;\r\n\twidth: 180rpx;\r\n\theight: 60rpx;\r\n\tline-height: 60rpx;\r\n\ttext-align: center;\r\n\tfont-size: 24rpx;\r\n\tfont-weight: 700;\r\n\tbackground: #000;\r\n\tcolor: #fff;\r\n\tborder-radius: 30rpx;\r\n}\r\n\r\n.input-item .send-code.active {\r\n\tbackground: #f5f5f5;\r\n\tcolor: #999;\r\n}\r\n\r\n.login-box .btn-submit {\r\n\tmargin-top: 60rpx;\r\n\twidth: 100%;\r\n\theight: 100rpx;\r\n\tline-height: 100rpx;\r\n\ttext-align: center;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 700;\r\n\tbackground: #000;\r\n\tcolor: #fff;\r\n\tborder-radius: 100rpx;\r\n\tjustify-content: center;\r\n}\r\n\r\n.df {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/uniapp/vue3/pages/users/user_pwd_edit/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["sendVerifyCode", "colors", "mapGetters", "verifyCode", "<PERSON><PERSON><PERSON><PERSON>", "getUserInfo", "registerVerify", "phoneRegisterReset"], "mappings": ";;;;;;;;AA0DC,MAAO,YAAW,MAAW;AAG7B,eAAe,MAAW;AAC1B,MAAK,YAAU;AAAA,EACd,QAAQ,CAACA,sBAAc,gBAAEC,mBAAM;AAAA,EAC/B,YAAY;AAAA,IAEX;AAAA,IAEA;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,UAAU,CAAE;AAAA,MACZ,OAAO;AAAA,MACP,UAAU;AAAA,MACV,SAAS;AAAA,MACT,aAAa;AAAA,MACb,QAAQ;AAAA;AAAA,MACR,YAAY;AAAA;AAAA,MACZ,KAAK;AAAA;EAEN;AAAA,EACD,UAAUC,cAAAA,WAAW,CAAC,SAAS,CAAC;AAAA,EAChC,OAAO;AAAA,IACN,SAAS;AAAA,MACR,SAAS,SAAS,MAAM,MAAM;AAC7B,YAAI,MAAM;AACT,eAAK,YAAW;AAAA,QACjB;AAAA,MACA;AAAA,MACD,MAAM;AAAA,IACP;AAAA,EACA;AAAA,EACD,SAAS;AACR,QAAI,KAAK,SAAS;AACjB,WAAK,YAAW;AAChBC,yBAAY,EAAC,KAAK,SAAO;AACxB,aAAK,KAAK,MAAM,OAAO,IAAI,KAAK,GAAG;AAAA,MACpC,CAAC;AAAA,WACK;AACNC,yBAAQ;AAAA,IACT;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAIR,WAAW,SAAS,GAAG;AACtB,WAAK,YAAW;AAAA,IAChB;AAAA;AAAA,IAED,WAAW,SAAS,GAAG;AACtB,WAAK,aAAa;AAAA,IAClB;AAAA;AAAA;AAAA;AAAA,IAID,aAAa,WAAW;AACvB,UAAI,OAAO;AACXC,2BAAa,EAAC,KAAK,SAAO;AACzB,YAAI,MAAM,IAAI,KAAK;AACnB,YAAI,QAAQ,IAAI,OAAO,GAAG,CAAC,IAAI,SAAS,IAAI,OAAO,CAAC;AACpD,aAAK,KAAK,MAAM,YAAY,IAAI,IAAI;AACpC,aAAK,QAAQ;AAAA,MACd,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,OAAO;AACZ,UAAI,OAAO;AACX,UAAI,CAAC,KAAK,SAAS;AAAO,eAAO,KAAK,MAAM,KAAK;AAAA,UAChD,OAAO,KAAK,GAAG,kBAAkB;AAAA,QAClC,CAAC;AACD,WAAK,MAAM,OAAO,KAAK;AAAA,IAEvB;AAAA,IACD,MAAM,QAAQ,MAAM;AACnB,UAAI,OAAO;AACX,WAAK,MAAM,OAAO,KAAK;AACvB,YAAMC,wBAAe;AAAA,QACpB,OAAO,KAAK,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,KAAK,KAAK;AAAA,QACV,aAAa,KAAK;AAAA,QAClB,qBAAqB,KAAK;AAAA,OAC1B,EAAE,KAAK,SAAO;AACd,aAAK,SAAS;AACd,aAAK,MAAM,KAAK;AAAA,UACf,OAAO,IAAI;AAAA,QACZ,CAAC;AAAA,MACF,CAAC,EAAE,MAAM,SAAO;AACf,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO;AAAA,QACR,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,SAAS,SAAS,GAAG;AACpB,UAAI,OAAO,MACV,WAAW,EAAE,OAAO,MAAM,UAC1B,cAAc,EAAE,OAAO,MAAM,aAC7B,UAAU,EAAE,OAAO,MAAM;AAC1B,UAAI,CAAC;AAAU,eAAO,KAAK,MAAM,KAAK;AAAA,UACrC,OAAO,KAAK,GAAG,QAAQ;AAAA,QACxB,CAAC;AACD,UAAI,eAAe;AAAU,eAAO,KAAK,MAAM,KAAK;AAAA,UACnD,OAAO,KAAK,GAAG,aAAa;AAAA,QAC7B,CAAC;AACD,UAAI,CAAC;AAAS,eAAO,KAAK,MAAM,KAAK;AAAA,UACpC,OAAO,KAAK,GAAG,QAAQ;AAAA,QACxB,CAAC;AACDC,iCAAmB;AAAA,QAClB,SAAS,KAAK,SAAS;AAAA,QACvB;AAAA,QACA;AAAA,OACA,EAAE,KAAK,SAAO;AACd,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,IAAI;AAAA,QACZ,GAAG;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AAAA,QACN,CAAC;AAAA,MACF,CAAC,EAAE,MAAM,SAAO;AACf,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO;AAAA,QACR,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjMD,GAAG,WAAW,eAAe;"}