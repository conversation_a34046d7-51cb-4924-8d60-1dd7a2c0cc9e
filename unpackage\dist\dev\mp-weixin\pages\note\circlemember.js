"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_social = require("../../api/social.js");
const _sfc_main = {
  __name: "circlemember",
  setup(__props) {
    const { proxy } = common_vendor.getCurrentInstance();
    const search = common_vendor.ref("");
    const adminList = common_vendor.ref([]);
    const memberList = common_vendor.ref([]);
    const circleId = common_vendor.ref(null);
    const allMembers = common_vendor.ref([]);
    const currentUser = common_vendor.ref(null);
    const selectedMember = common_vendor.ref(null);
    const isManager = common_vendor.ref(false);
    const loading = common_vendor.ref(false);
    const searchTimer = common_vendor.ref(null);
    const cacheKey = common_vendor.ref("");
    const lastUpdateTime = common_vendor.ref(0);
    const isEmpty = common_vendor.computed(() => {
      return !loading.value && adminList.value.length === 0 && memberList.value.length === 0;
    });
    common_vendor.computed(() => {
      return adminList.value.length + memberList.value.length;
    });
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;
      circleId.value = options.id;
      cacheKey.value = `circle_members_${circleId.value}`;
      getCurrentUser();
      checkCache();
      fetchMembers();
    });
    common_vendor.watch(search, (val) => {
      if (searchTimer.value) {
        clearTimeout(searchTimer.value);
      }
      searchTimer.value = setTimeout(() => {
        filterMembers(val);
      }, 300);
    });
    const checkCache = () => {
      try {
        const cached = common_vendor.index.getStorageSync(cacheKey.value);
        const now = Date.now();
        if (cached && cached.timestamp && now - cached.timestamp < 3 * 60 * 1e3) {
          adminList.value = cached.data.adminList || [];
          memberList.value = cached.data.memberList || [];
          allMembers.value = cached.data.allMembers || { adminList: [], memberList: [] };
          lastUpdateTime.value = cached.timestamp;
          checkManagerRole();
        }
      } catch (e) {
        common_vendor.index.__f__("warn", "at pages/note/circlemember.vue:185", "读取成员缓存失败:", e);
      }
    };
    const updateCache = () => {
      try {
        const cacheData = {
          data: {
            adminList: adminList.value,
            memberList: memberList.value,
            allMembers: allMembers.value
          },
          timestamp: Date.now()
        };
        common_vendor.index.setStorageSync(cacheKey.value, cacheData);
        lastUpdateTime.value = cacheData.timestamp;
      } catch (e) {
        common_vendor.index.__f__("warn", "at pages/note/circlemember.vue:203", "更新成员缓存失败:", e);
      }
    };
    const onSearchInput = (e) => {
      search.value = e.detail.value;
    };
    const onSearchConfirm = () => {
      filterMembers(search.value);
    };
    const clearSearch = () => {
      search.value = "";
      filterMembers("");
    };
    const goToUserProfile = (uid) => {
      common_vendor.index.navigateTo({
        url: `/pages/user/details?id=${uid}`
      });
    };
    const getCurrentUser = () => {
      const userInfo = common_vendor.index.getStorageSync("USER_INFO");
      const vuexUid = proxy.$store.state.app.uid;
      currentUser.value = {
        uid: vuexUid || userInfo && userInfo.uid || 0
      };
    };
    const getCurrentUserId = () => {
      return currentUser.value ? currentUser.value.uid : 0;
    };
    const checkManagerRole = () => {
      const userId = getCurrentUserId();
      const admin = adminList.value.find((u) => u.uid === userId);
      isManager.value = admin && (admin.role_type === 2 || admin.role_type === 3);
      if (admin) {
        currentUser.value.role_type = admin.role_type;
        currentUser.value.role = admin.role;
      }
    };
    const canManage = (member) => {
      var _a;
      const currentUserId = getCurrentUserId();
      const currentUserRole = ((_a = currentUser.value) == null ? void 0 : _a.role_type) || 0;
      if (member.uid === currentUserId) {
        return false;
      }
      if (currentUserRole === 3) {
        return true;
      }
      if (currentUserRole === 2 && member.role_type === 1) {
        return true;
      }
      return false;
    };
    const showActionMenu = (member) => {
      selectedMember.value = member;
      const actions = [];
      if (member.role_type === 1) {
        actions.push("设为管理员");
      } else if (member.role_type === 2) {
        actions.push("取消管理员");
      }
      if (member.is_mute === 1) {
        actions.push("解除禁言");
      } else {
        actions.push("禁言");
      }
      actions.push("踢出圈子");
      common_vendor.index.showActionSheet({
        itemList: actions,
        success: (res) => {
          const action = actions[res.tapIndex];
          handleMemberAction(action, member);
        }
      });
    };
    const handleMemberAction = async (action, member) => {
      try {
        switch (action) {
          case "设为管理员":
            await setMemberRole(member.uid, 2);
            break;
          case "取消管理员":
            await setMemberRole(member.uid, 1);
            break;
          case "禁言":
            await muteMember(member.uid);
            break;
          case "解除禁言":
            await unmuteMember(member.uid);
            break;
          case "踢出圈子":
            await kickMember(member.uid);
            break;
        }
      } catch (e) {
        common_vendor.index.showToast({ title: "操作失败", icon: "none" });
      }
    };
    const setMemberRole = async (uid, roleType) => {
      common_vendor.index.showModal({
        title: "确认操作",
        content: roleType === 2 ? "确定要设为管理员吗？" : "确定要取消管理员权限吗？",
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await api_social.setCircleMemberRole({
                circle_id: parseInt(circleId.value),
                target_uid: parseInt(uid),
                role_type: parseInt(roleType)
              });
              if (result.status === 200) {
                common_vendor.index.showToast({ title: "操作成功", icon: "success" });
                fetchMembers();
              } else {
                common_vendor.index.showToast({ title: result.msg || "操作失败", icon: "none" });
              }
            } catch (e) {
              common_vendor.index.showToast({ title: "网络错误", icon: "none" });
            }
          }
        }
      });
    };
    const muteMember = async (uid) => {
      common_vendor.index.showActionSheet({
        itemList: ["发布违规内容", "恶意刷屏", "人身攻击", "其他原因"],
        success: (res) => {
          const reasons = ["发布违规内容", "恶意刷屏", "人身攻击", "其他原因"];
          const selectedReason = reasons[res.tapIndex];
          if (selectedReason === "其他原因") {
            common_vendor.index.showModal({
              title: "输入禁言原因",
              editable: true,
              placeholderText: "请输入禁言原因",
              success: async (modalRes) => {
                if (modalRes.confirm && modalRes.content) {
                  await executeMuteMember(uid, modalRes.content);
                }
              }
            });
          } else {
            executeMuteMember(uid, selectedReason);
          }
        }
      });
    };
    const executeMuteMember = async (uid, reason) => {
      try {
        const result = await api_social.muteCircleMember({
          circle_id: parseInt(circleId.value),
          target_uid: parseInt(uid),
          mute_days: parseInt(1),
          reason
        });
        if (result.status === 200) {
          common_vendor.index.showToast({ title: "禁言成功", icon: "success" });
          fetchMembers();
        } else {
          common_vendor.index.showToast({ title: result.msg || "禁言失败", icon: "none" });
        }
      } catch (e) {
        common_vendor.index.showToast({ title: "网络错误", icon: "none" });
      }
    };
    const unmuteMember = async (uid) => {
      try {
        const result = await api_social.unmuteCircleMember({
          circle_id: parseInt(circleId.value),
          target_uid: parseInt(uid)
        });
        if (result.status === 200) {
          common_vendor.index.showToast({ title: "解除禁言成功", icon: "success" });
          fetchMembers();
        } else {
          common_vendor.index.showToast({ title: result.msg || "操作失败", icon: "none" });
        }
      } catch (e) {
        common_vendor.index.showToast({ title: "网络错误", icon: "none" });
      }
    };
    const kickMember = async (uid) => {
      common_vendor.index.showActionSheet({
        itemList: ["严重违规", "恶意刷屏", "人身攻击", "其他原因"],
        success: (res) => {
          const reasons = ["严重违规", "恶意刷屏", "人身攻击", "其他原因"];
          const selectedReason = reasons[res.tapIndex];
          if (selectedReason === "其他原因") {
            common_vendor.index.showModal({
              title: "输入踢出原因",
              editable: true,
              placeholderText: "请输入踢出原因",
              success: async (modalRes) => {
                if (modalRes.confirm && modalRes.content) {
                  await executeKickMember(uid, modalRes.content);
                }
              }
            });
          } else {
            executeKickMember(uid, selectedReason);
          }
        }
      });
    };
    const executeKickMember = async (uid, reason) => {
      common_vendor.index.showModal({
        title: "确认踢出",
        content: `确定要踢出该成员吗？原因：${reason}
此操作不可撤销。`,
        confirmColor: "#FA5150",
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await api_social.kickCircleMember({
                circle_id: parseInt(circleId.value),
                target_uid: parseInt(uid),
                reason
              });
              if (result.status === 200) {
                common_vendor.index.showToast({ title: "踢出成功", icon: "success" });
                fetchMembers();
              } else {
                common_vendor.index.showToast({ title: result.msg || "踢出失败", icon: "none" });
              }
            } catch (e) {
              common_vendor.index.showToast({ title: "网络错误", icon: "none" });
            }
          }
        }
      });
    };
    const fetchMembers = async () => {
      try {
        loading.value = true;
        const res = await api_social.getCircleMemberList({
          circle_id: circleId.value,
          page: 1,
          limit: 500
          // 增加限制数量
        });
        if (res.status === 200 && res.data && res.data.list) {
          const list = res.data.list;
          const adminListData = [];
          const memberListData = [];
          list.forEach((user) => {
            const userData = {
              ...user,
              user_avatar: user.user_avatar || "/static/img/avatar.png",
              user_nickname: user.user_nickname || "未知用户",
              gender: user.gender || "",
              age: user.age || "",
              join_time: user.join_time || "",
              is_mute: user.is_mute || 0
            };
            if (user.role_type === 3) {
              adminListData.unshift({ ...userData, role: "圈主" });
            } else if (user.role_type === 2) {
              adminListData.push({ ...userData, role: "管理员" });
            } else {
              memberListData.push(userData);
            }
          });
          adminList.value = adminListData;
          memberList.value = memberListData;
          allMembers.value = { adminList: adminListData, memberList: memberListData };
          updateCache();
          checkManagerRole();
        } else {
          throw new Error(res.msg || "获取成员列表失败");
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/circlemember.vue:532", "获取成员列表失败:", e);
        handleError(e, "成员加载失败");
      } finally {
        loading.value = false;
      }
    };
    const formatJoinTime = (timeStr) => {
      if (!timeStr)
        return "";
      const date = new Date(timeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    };
    const handleError = (error, defaultMessage = "操作失败") => {
      var _a, _b, _c;
      common_vendor.index.__f__("error", "at pages/note/circlemember.vue:549", "错误处理:", error);
      let message = defaultMessage;
      if (typeof error === "string") {
        message = error;
      } else if (error && typeof error === "object") {
        if (error.code === "NETWORK_ERROR" || ((_a = error.message) == null ? void 0 : _a.includes("Network"))) {
          message = "网络连接异常，请检查网络设置";
        } else if (error.code === "TIMEOUT" || ((_b = error.message) == null ? void 0 : _b.includes("timeout"))) {
          message = "请求超时，请稍后重试";
        } else {
          message = error.msg || error.message || ((_c = error.data) == null ? void 0 : _c.msg) || defaultMessage;
        }
      }
      common_vendor.index.showToast({
        title: message,
        icon: "none",
        duration: 2e3
      });
      return message;
    };
    const filterMembers = (val) => {
      if (!val || !val.trim()) {
        if (allMembers.value.adminList && allMembers.value.memberList) {
          adminList.value = allMembers.value.adminList;
          memberList.value = allMembers.value.memberList;
        }
        return;
      }
      const searchTerm = val.trim().toLowerCase();
      const filter = (arr) => arr.filter((u) => {
        const nickname = (u.user_nickname || "").toLowerCase();
        const uid = String(u.uid || "");
        return nickname.includes(searchTerm) || uid.includes(searchTerm);
      });
      adminList.value = filter(allMembers.value.adminList || []);
      memberList.value = filter(allMembers.value.memberList || []);
    };
    common_vendor.onUnmounted(() => {
      if (searchTimer.value) {
        clearTimeout(searchTimer.value);
        searchTimer.value = null;
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$2,
        b: common_vendor.o([($event) => search.value = $event.detail.value, onSearchInput]),
        c: common_vendor.o(onSearchConfirm),
        d: search.value,
        e: search.value
      }, search.value ? {
        f: common_assets._imports_1,
        g: common_vendor.o(clearSearch)
      } : {}, {
        h: loading.value
      }, loading.value ? {} : {}, {
        i: !loading.value
      }, !loading.value ? common_vendor.e({
        j: isEmpty.value
      }, isEmpty.value ? common_vendor.e({
        k: common_assets._imports_3$1,
        l: common_vendor.t(search.value ? "未找到相关成员" : "暂无成员"),
        m: search.value
      }, search.value ? {} : {}) : {}, {
        n: adminList.value.length > 0
      }, adminList.value.length > 0 ? {
        o: common_vendor.t(adminList.value.length),
        p: common_vendor.f(adminList.value, (user, idx, i0) => {
          return common_vendor.e({
            a: user.user_avatar || "/static/img/avatar.png",
            b: common_vendor.t(user.user_nickname || "未知用户"),
            c: user.role === "圈主"
          }, user.role === "圈主" ? {} : user.role === "管理员" ? {} : {}, {
            d: user.role === "管理员",
            e: user.is_mute === 1
          }, user.is_mute === 1 ? {} : {}, {
            f: user.gender
          }, user.gender ? {
            g: common_vendor.t(user.gender === "男" ? "♂" : "♀"),
            h: common_vendor.n(user.gender === "男" ? "male" : "female")
          } : {}, {
            i: user.age
          }, user.age ? {
            j: common_vendor.t(user.age)
          } : {}, {
            k: common_vendor.t(formatJoinTime(user.join_time)),
            l: user.uid !== getCurrentUserId() && isManager.value && canManage(user)
          }, user.uid !== getCurrentUserId() && isManager.value && canManage(user) ? {
            m: common_vendor.o(($event) => showActionMenu(user), "admin-" + idx)
          } : {}, {
            n: "admin-" + idx,
            o: common_vendor.o(($event) => goToUserProfile(user.uid), "admin-" + idx)
          });
        })
      } : {}, {
        q: memberList.value.length > 0
      }, memberList.value.length > 0 ? {
        r: common_vendor.t(memberList.value.length),
        s: common_vendor.f(memberList.value, (user, idx, i0) => {
          return common_vendor.e({
            a: user.user_avatar || "/static/img/avatar.png",
            b: common_vendor.t(user.user_nickname || "未知用户"),
            c: user.is_mute === 1
          }, user.is_mute === 1 ? {} : {}, {
            d: user.gender
          }, user.gender ? {
            e: common_vendor.t(user.gender === "男" ? "♂" : "♀"),
            f: common_vendor.n(user.gender === "男" ? "male" : "female")
          } : {}, {
            g: user.age
          }, user.age ? {
            h: common_vendor.t(user.age)
          } : {}, {
            i: common_vendor.t(formatJoinTime(user.join_time)),
            j: user.uid !== getCurrentUserId() && isManager.value
          }, user.uid !== getCurrentUserId() && isManager.value ? {
            k: common_vendor.o(($event) => showActionMenu(user), "member-" + idx)
          } : {}, {
            l: "member-" + idx,
            m: common_vendor.o(($event) => goToUserProfile(user.uid), "member-" + idx)
          });
        })
      } : {}) : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/note/circlemember.js.map
