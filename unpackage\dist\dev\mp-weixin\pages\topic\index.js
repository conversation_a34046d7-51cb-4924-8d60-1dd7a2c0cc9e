"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      topicList: [],
      loading: false
    };
  },
  onLoad() {
    this.getTopics();
  },
  methods: {
    // 获取话题列表
    getTopics() {
      this.loading = true;
      api_social.getTopicList({
        page: 1,
        limit: 20,
        keyword: ""
      }).then((res) => {
        this.loading = false;
        if (res.status === 200 && res.data && res.data.list) {
          this.topicList = res.data.list;
        }
      }).catch((err) => {
        this.loading = false;
        common_vendor.index.__f__("error", "at pages/topic/index.vue:79", "获取话题列表失败:", err);
        common_vendor.index.showToast({
          title: "获取话题列表失败",
          icon: "none"
        });
      });
    },
    // 跳转到话题详情
    goToTopicDetail(topic) {
      if (!topic || !topic.id)
        return;
      common_vendor.index.navigateTo({
        url: `/pages/topic/details?id=${topic.id}`,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/topic/index.vue:94", "跳转话题详情失败:", err);
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none"
          });
        }
      });
    },
    // 返回上一页
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "/pages/index/index"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: $data.titleBarHeight + "px",
    c: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    d: $data.statusBarHeight + "px",
    e: common_vendor.f($data.topicList, (topic, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(topic.title),
        b: common_vendor.t(topic.post_count || 0),
        c: common_vendor.t(topic.view_count || 0),
        d: topic.description
      }, topic.description ? {
        e: common_vendor.t(topic.description)
      } : {}, {
        f: topic.id || index,
        g: common_vendor.o(($event) => $options.goToTopicDetail(topic), topic.id || index)
      });
    }),
    f: common_assets._imports_1$19,
    g: $data.topicList.length === 0 && !$data.loading
  }, $data.topicList.length === 0 && !$data.loading ? {
    h: common_assets._imports_3$1
  } : {}, {
    i: $data.loading
  }, $data.loading ? {} : {}, {
    j: $data.statusBarHeight + $data.titleBarHeight + 20 + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/topic/index.js.map
