<view class="jsfun-record" bindtap="{{s}}"><slot></slot><view wx:if="{{a}}" class="mask" catchtap="{{b}}" catchtouchmove="{{c}}"></view><view class="{{['record-container', r && 'show']}}"><view class="time-display">{{d}}</view><view class="time-hint">最短{{e}}秒，最长{{f}}秒</view><view class="record-box"><label wx:if="{{g}}" class="control-btn stop-btn" catchtap="{{h}}"></label><label wx:if="{{i}}" class="control-btn play-btn" catchtap="{{j}}"></label><block wx:if="{{r0}}"><canvas class="record-canvas" canvas-id="recordCanvas" bindtouchstart="{{l}}" bindlongpress="{{m}}" bindtouchend="{{n}}" catchtouchmove="{{o}}"><label class="{{['record-indicator', k && 'recording']}}"></label></canvas></block><label wx:if="{{p}}" class="control-btn confirm-btn" catchtap="{{q}}"></label></view><view class="instruction-text">长按录音</view></view></view>