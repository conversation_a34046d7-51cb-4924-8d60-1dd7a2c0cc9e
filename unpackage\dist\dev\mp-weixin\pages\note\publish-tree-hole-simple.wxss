
.container.data-v-22b8a2a2 {
  min-height: 100vh;
  background-color: #f8f8f8;
}
.nav-bar.data-v-22b8a2a2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}
.nav-back.data-v-22b8a2a2 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
}
.nav-title.data-v-22b8a2a2 {
  font-size: 32rpx;
  font-weight: bold;
}
.nav-submit.data-v-22b8a2a2 {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  border-radius: 20rpx;
  color: white;
  font-size: 28rpx;
}
.form-container.data-v-22b8a2a2 {
  padding: 30rpx;
}
.form-item.data-v-22b8a2a2 {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.form-label.data-v-22b8a2a2 {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.type-list.data-v-22b8a2a2 {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}
.type-item.data-v-22b8a2a2 {
  flex: 1;
  min-width: 140rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  text-align: center;
  border: 2rpx solid transparent;
}
.type-item.active.data-v-22b8a2a2 {
  background: #e6f0ff;
  border-color: #3B82F6;
}
.type-icon.data-v-22b8a2a2 {
  display: block;
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.type-name.data-v-22b8a2a2 {
  font-size: 24rpx;
  color: #666;
}
.form-textarea.data-v-22b8a2a2 {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.6;
}
.form-count.data-v-22b8a2a2 {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.voice-section.data-v-22b8a2a2 {
  padding: 60rpx;
  text-align: center;
  color: #999;
}
.setting-row.data-v-22b8a2a2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.setting-text.data-v-22b8a2a2 {
  font-size: 28rpx;
}
.switch.data-v-22b8a2a2 {
  width: 80rpx;
  height: 40rpx;
  background: #e9ecef;
  border-radius: 20rpx;
  position: relative;
  transition: all 0.3s ease;
}
.switch.active.data-v-22b8a2a2 {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
}
.switch-dot.data-v-22b8a2a2 {
  width: 32rpx;
  height: 32rpx;
  background: #fff;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: all 0.3s ease;
}
.switch.active .switch-dot.data-v-22b8a2a2 {
  left: 44rpx;
}

/* 调试信息样式 */
.debug-info.data-v-22b8a2a2 {
  background: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 12rpx;
  padding: 20rpx;
  margin: 20rpx;
}
.debug-title.data-v-22b8a2a2 {
  font-size: 28rpx;
  font-weight: bold;
  color: #856404;
  margin-bottom: 16rpx;
  text-align: center;
}
.debug-item.data-v-22b8a2a2 {
  font-size: 24rpx;
  color: #856404;
  margin-bottom: 8rpx;
  padding: 8rpx;
  background: rgba(255, 234, 167, 0.3);
  border-radius: 6rpx;
  word-break: break-all;
}
