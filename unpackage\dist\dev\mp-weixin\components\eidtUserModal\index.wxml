<view class="data-v-f7f0d621" style="{{z}}"><view class="{{['product-window', 'data-v-f7f0d621', r && 'on']}}"><view class="iconfont icon-guanbi data-v-f7f0d621" bindtap="{{a}}"></view><view class="mp-data data-v-f7f0d621"><image class="data-v-f7f0d621" src="{{b}}" mode=""></image><text class="mp-name data-v-f7f0d621">{{c}} 申请</text></view><view class="trip-msg data-v-f7f0d621"><view class="title data-v-f7f0d621">{{d}}</view><view class="trip data-v-f7f0d621">{{e}}</view></view><form class="data-v-f7f0d621" bindsubmit="{{q}}"><view class="edit data-v-f7f0d621"><view class="avatar edit-box data-v-f7f0d621"><view class="left data-v-f7f0d621"><view class="head data-v-f7f0d621">{{f}}</view><view wx:if="{{g}}" class="avatar-box data-v-f7f0d621" catchtap="{{i}}"><image class="data-v-f7f0d621" src="{{h}}"></image></view><button wx:else class="avatar-box data-v-f7f0d621" open-type="chooseAvatar" bindchooseavatar="{{k}}"><image class="data-v-f7f0d621" src="{{j}}"></image></button></view></view><view class="nickname edit-box data-v-f7f0d621"><view class="left data-v-f7f0d621"><view class="head data-v-f7f0d621">{{l}}</view><view class="input data-v-f7f0d621"><input class="data-v-f7f0d621" type="nickname" placeholder-class="pl-sty" placeholder="{{m}}" name="nickname" maxlength="{{16}}" value="{{n}}"></input></view></view></view></view><view class="bottom data-v-f7f0d621"><button formType="submit" class="{{['save', 'data-v-f7f0d621', p && 'open']}}">{{o}}</button></view></form></view><canvas wx:if="{{s}}" class="data-v-f7f0d621" canvas-id="canvas" style="{{'width:' + t + ';' + ('height:' + v) + ';' + ('position:' + 'absolute') + ';' + ('left:' + '-100000px') + ';' + ('top:' + '-100000px')}}"></canvas><view wx:if="{{w}}" class="mask data-v-f7f0d621" catchtouchmove="{{x}}" bindtap="{{y}}"></view></view>