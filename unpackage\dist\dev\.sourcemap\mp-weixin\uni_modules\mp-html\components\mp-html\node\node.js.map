{"version": 3, "file": "node.js", "sources": ["uni_modules/mp-html/components/mp-html/node/node.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvdW5pX21vZHVsZXMvbXAtaHRtbC9jb21wb25lbnRzL21wLWh0bWwvbm9kZS9ub2RlLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view :id=\"attrs.id\" :class=\"'_block _'+name+' '+attrs.class\" :style=\"attrs.style\">\r\n    <block v-for=\"(n, i) in childs\" v-bind:key=\"i\">\r\n      <!-- 图片 -->\r\n      <!-- 占位图 -->\r\n      <image v-if=\"n.name==='img'&&!n.t&&((opts[1]&&!ctrl[i])||ctrl[i]<0)\" class=\"_img\" :style=\"n.attrs.style\" :src=\"ctrl[i]<0?opts[2]:opts[1]\" mode=\"widthFix\" />\r\n      <!-- 显示图片 -->\r\n      <!-- #ifdef H5 || (APP-PLUS && VUE2) -->\r\n      <img v-if=\"n.name==='img'\" :id=\"n.attrs.id\" :class=\"'_img '+n.attrs.class\" :style=\"(ctrl[i]===-1?'display:none;':'')+n.attrs.style\" :src=\"n.attrs.src||(ctrl.load?n.attrs['data-src']:'')\" :data-i=\"i\" @load=\"imgLoad\" @error=\"mediaError\" @tap.stop=\"imgTap\" @longpress=\"imgLongTap\" />\r\n      <!-- #endif -->\r\n      <!-- #ifndef H5 || (APP-PLUS && VUE2) -->\r\n      <!-- 表格中的图片，使用 rich-text 防止大小不正确 -->\r\n      <rich-text v-if=\"n.name==='img'&&n.t\" :style=\"'display:'+n.t\" :nodes=\"[{attrs:{style:n.attrs.style||'',src:n.attrs.src},name:'img'}]\" :data-i=\"i\" @tap.stop=\"imgTap\" />\r\n      <!-- #endif -->\r\n      <!-- #ifdef APP-HARMONY -->\r\n      <image v-else-if=\"n.name==='img'\" :id=\"n.attrs.id\" :class=\"'_img '+n.attrs.class\" :style=\"(ctrl[i]===-1?'display:none;':'')+'width:'+ctrl[i]+'px;'+n.attrs.style\" :src=\"n.attrs.src||(ctrl.load?n.attrs['data-src']:'')\" :mode=\"!n.h?'widthFix':(!n.w?'heightFix':(n.m||'scaleToFill'))\" :data-i=\"i\" @load=\"imgLoad\" @error=\"mediaError\" @tap.stop=\"imgTap\" @longpress=\"imgLongTap\" />\r\n      <!-- #endif -->\r\n      <!-- #ifndef H5 || APP-PLUS || MP-KUAISHOU -->\r\n      <image v-else-if=\"n.name==='img'\" :id=\"n.attrs.id\" :class=\"'_img '+n.attrs.class\" :style=\"(ctrl[i]===-1?'display:none;':'')+'width:'+(ctrl[i]||1)+'px;height:1px;'+n.attrs.style\" :src=\"n.attrs.src\" :mode=\"!n.h?'widthFix':(!n.w?'heightFix':(n.m||'scaleToFill'))\" :lazy-load=\"opts[0]\" :webp=\"n.webp\" :show-menu-by-longpress=\"opts[3]&&!n.attrs.ignore\" :image-menu-prevent=\"!opts[3]||n.attrs.ignore\" :data-i=\"i\" @load=\"imgLoad\" @error=\"mediaError\" @tap.stop=\"imgTap\" @longpress=\"imgLongTap\" />\r\n      <!-- #endif -->\r\n      <!-- #ifdef MP-KUAISHOU -->\r\n      <image v-else-if=\"n.name==='img'\" :id=\"n.attrs.id\" :class=\"'_img '+n.attrs.class\" :style=\"(ctrl[i]===-1?'display:none;':'')+n.attrs.style\" :src=\"n.attrs.src\" :lazy-load=\"opts[0]\" :data-i=\"i\" @load=\"imgLoad\" @error=\"mediaError\" @tap.stop=\"imgTap\"></image>\r\n      <!-- #endif -->\r\n      <!-- #ifdef APP-PLUS && VUE3 -->\r\n      <image v-else-if=\"n.name==='img'\" :id=\"n.attrs.id\" :class=\"'_img '+n.attrs.class\" :style=\"(ctrl[i]===-1?'display:none;':'')+'width:'+(ctrl[i]||1)+'px;'+n.attrs.style\" :src=\"n.attrs.src||(ctrl.load?n.attrs['data-src']:'')\" :mode=\"!n.h?'widthFix':(!n.w?'heightFix':(n.m||''))\" :data-i=\"i\" @load=\"imgLoad\" @error=\"mediaError\" @tap.stop=\"imgTap\" @longpress=\"imgLongTap\" />\r\n      <!-- #endif -->\r\n      <!-- 文本 -->\r\n      <!-- #ifdef MP-WEIXIN -->\r\n      <text v-else-if=\"n.text\" :user-select=\"opts[4]=='force'&&isiOS\" decode>{{n.text}}</text>\r\n      <!-- #endif -->\r\n      <!-- #ifndef MP-WEIXIN || MP-BAIDU || MP-ALIPAY || MP-TOUTIAO -->\r\n      <text v-else-if=\"n.text\" decode>{{n.text}}</text>\r\n      <!-- #endif -->\r\n      <text v-else-if=\"n.name==='br'\">\\n</text>\r\n      <!-- 链接 -->\r\n      <view v-else-if=\"n.name==='a'\" :id=\"n.attrs.id\" :class=\"(n.attrs.href?'_a ':'')+n.attrs.class\" hover-class=\"_hover\" :style=\"'display:inline;'+n.attrs.style\" :data-i=\"i\" @tap.stop=\"linkTap\">\r\n        <node name=\"span\" :childs=\"n.children\" :opts=\"opts\" style=\"display:inherit\" />\r\n      </view>\r\n      <!-- 视频 -->\r\n      <!-- #ifdef APP-PLUS -->\r\n      <view v-else-if=\"n.html\" :id=\"n.attrs.id\" :class=\"'_video '+n.attrs.class\" :style=\"n.attrs.style\" v-html=\"n.html\" :data-i=\"i\" @vplay.stop=\"play\" />\r\n      <!-- #endif -->\r\n      <!-- #ifndef APP-PLUS -->\r\n      <video v-else-if=\"n.name==='video'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :autoplay=\"n.attrs.autoplay\" :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :muted=\"n.attrs.muted\" :object-fit=\"n.attrs['object-fit']\" :poster=\"n.attrs.poster\" :src=\"n.src[ctrl[i]||0]\" :data-i=\"i\" @play=\"play\" @error=\"mediaError\" />\r\n      <!-- #endif -->\r\n      <!-- #ifdef H5 || APP-PLUS -->\r\n      <iframe v-else-if=\"n.name==='iframe'\" :style=\"n.attrs.style\" :allowfullscreen=\"n.attrs.allowfullscreen\" :frameborder=\"n.attrs.frameborder\" :src=\"n.attrs.src\" />\r\n      <embed v-else-if=\"n.name==='embed'\" :style=\"n.attrs.style\" :src=\"n.attrs.src\" />\r\n      <!-- #endif -->\r\n      <!-- #ifndef MP-TOUTIAO || ((H5 || APP-PLUS) && VUE3) -->\r\n      <!-- 音频 -->\r\n      <audio v-else-if=\"n.name==='audio'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :author=\"n.attrs.author\" :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :name=\"n.attrs.name\" :poster=\"n.attrs.poster\" :src=\"n.src[ctrl[i]||0]\" :data-i=\"i\" @play=\"play\" @error=\"mediaError\" />\r\n      <!-- #endif -->\r\n      <view v-else-if=\"(n.name==='table'&&n.c)||n.name==='li'\" :id=\"n.attrs.id\" :class=\"'_'+n.name+' '+n.attrs.class\" :style=\"n.attrs.style\">\r\n        <node v-if=\"n.name==='li'\" :childs=\"n.children\" :opts=\"opts\" />\r\n        <view v-else v-for=\"(tbody, x) in n.children\" v-bind:key=\"x\" :class=\"'_'+tbody.name+' '+tbody.attrs.class\" :style=\"tbody.attrs.style\">\r\n          <node v-if=\"tbody.name==='td'||tbody.name==='th'\" :childs=\"tbody.children\" :opts=\"opts\" />\r\n          <block v-else v-for=\"(tr, y) in tbody.children\" v-bind:key=\"y\">\r\n            <view v-if=\"tr.name==='td'||tr.name==='th'\" :class=\"'_'+tr.name+' '+tr.attrs.class\" :style=\"tr.attrs.style\">\r\n              <node :childs=\"tr.children\" :opts=\"opts\" />\r\n            </view>\r\n            <view v-else :class=\"'_'+tr.name+' '+tr.attrs.class\" :style=\"tr.attrs.style\">\r\n              <view v-for=\"(td, z) in tr.children\" v-bind:key=\"z\" :class=\"'_'+td.name+' '+td.attrs.class\" :style=\"td.attrs.style\">\r\n                <node :childs=\"td.children\" :opts=\"opts\" />\r\n              </view>\r\n            </view>\r\n          </block>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 富文本 -->\r\n      <!-- #ifdef H5 || ((MP-WEIXIN || MP-QQ || APP-PLUS || MP-360) && VUE2) -->\r\n      <rich-text v-else-if=\"!n.c&&!handler.isInline(n.name, n.attrs.style)\" :id=\"n.attrs.id\" :style=\"n.f\" :user-select=\"opts[4]\" :nodes=\"[n]\" />\r\n      <!-- #endif -->\r\n      <!-- #ifndef H5 || ((MP-WEIXIN || MP-QQ || APP-PLUS || MP-360) && VUE2) -->\r\n      <rich-text v-else-if=\"!n.c\" :id=\"n.attrs.id\" :style=\"'display:inline;'+n.f\" :preview=\"false\" :selectable=\"opts[4]\" :user-select=\"opts[4]\" :nodes=\"[n]\" />\r\n      <!-- #endif -->\r\n      <!-- 继续递归 -->\r\n      <view v-else-if=\"n.c===2\" :id=\"n.attrs.id\" :class=\"'_block _'+n.name+' '+n.attrs.class\" :style=\"n.f+';'+n.attrs.style\">\r\n        <node v-for=\"(n2, j) in n.children\" v-bind:key=\"j\" :style=\"n2.f\" :name=\"n2.name\" :attrs=\"n2.attrs\" :childs=\"n2.children\" :opts=\"opts\" />\r\n      </view>\r\n      <node v-else :style=\"n.f\" :name=\"n.name\" :attrs=\"n.attrs\" :childs=\"n.children\" :opts=\"opts\" />\r\n    </block>\r\n  </view>\r\n</template>\r\n<script module=\"handler\" lang=\"wxs\">\r\n// 行内标签列表\r\nvar inlineTags = {\r\n  abbr: true,\r\n  b: true,\r\n  big: true,\r\n  code: true,\r\n  del: true,\r\n  em: true,\r\n  i: true,\r\n  ins: true,\r\n  label: true,\r\n  q: true,\r\n  small: true,\r\n  span: true,\r\n  strong: true,\r\n  sub: true,\r\n  sup: true\r\n}\r\n/**\r\n * @description 判断是否为行内标签\r\n */\r\nmodule.exports = {\r\n  isInline: function (tagName, style) {\r\n    return inlineTags[tagName] || (style || '').indexOf('display:inline') !== -1\r\n  }\r\n}\r\n</script>\r\n<script>\n\r\nimport node from './node'\r\nexport default {\r\n  name: 'node',\r\n  options: {\r\n    // #ifdef MP-WEIXIN\r\n    virtualHost: true,\r\n    // #endif\r\n    // #ifdef MP-TOUTIAO\r\n    addGlobalClass: false\r\n    // #endif\r\n  },\r\n  data () {\r\n    return {\r\n      ctrl: {},\r\n      // #ifdef MP-WEIXIN\r\n      isiOS: uni.getSystemInfoSync().system.includes('iOS')\r\n      // #endif\r\n    }\r\n  },\r\n  props: {\r\n    name: String,\r\n    attrs: {\r\n      type: Object,\r\n      default () {\r\n        return {}\r\n      }\r\n    },\r\n    childs: Array,\r\n    opts: Array\r\n  },\r\n  components: {\n\r\n    // #ifndef ((H5 || APP-PLUS) && VUE3) || APP-HARMONY\r\n    node\r\n    // #endif\r\n  },\r\n  mounted () {\r\n    this.$nextTick(() => {\r\n      for (this.root = this.$parent; this.root.$options.name !== 'mp-html'; this.root = this.root.$parent);\r\n    })\r\n    // #ifdef H5 || APP-PLUS\r\n    if (this.opts[0]) {\r\n      let i\r\n      for (i = this.childs.length; i--;) {\r\n        if (this.childs[i].name === 'img') break\r\n      }\r\n      if (i !== -1) {\r\n        this.observer = uni.createIntersectionObserver(this).relativeToViewport({\r\n          top: 500,\r\n          bottom: 500\r\n        })\r\n        this.observer.observe('._img', res => {\r\n          if (res.intersectionRatio) {\r\n            this.$set(this.ctrl, 'load', 1)\r\n            this.observer.disconnect()\r\n          }\r\n        })\r\n      }\r\n    }\r\n    // #endif\r\n  },\r\n  beforeDestroy () {\r\n    // #ifdef H5 || APP-PLUS\r\n    if (this.observer) {\r\n      this.observer.disconnect()\r\n    }\r\n    // #endif\r\n  },\r\n  methods:{\r\n    // #ifdef MP-WEIXIN\r\n    toJSON () { return this },\r\n    // #endif\r\n    /**\r\n     * @description 播放视频事件\r\n     * @param {Event} e\r\n     */\r\n    play (e) {\r\n      const i = e.currentTarget.dataset.i\r\n      const node = this.childs[i]\r\n      this.root.$emit('play', {\r\n        source: node.name,\r\n        attrs: {\r\n          ...node.attrs,\r\n          src: node.src[this.ctrl[i] || 0]\r\n        }\r\n      })\r\n      // #ifndef APP-PLUS\r\n      if (this.root.pauseVideo) {\r\n        let flag = false\r\n        const id = e.target.id\r\n        for (let i = this.root._videos.length; i--;) {\r\n          if (this.root._videos[i].id === id) {\r\n            flag = true\r\n          } else {\r\n            this.root._videos[i].pause() // 自动暂停其他视频\r\n          }\r\n        }\r\n        // 将自己加入列表\r\n        if (!flag) {\r\n          const ctx = uni.createVideoContext(id\r\n            // #ifndef MP-BAIDU\r\n            , this\r\n            // #endif\r\n          )\r\n          ctx.id = id\r\n          if (this.root.playbackRate) {\r\n            ctx.playbackRate(this.root.playbackRate)\r\n          }\r\n          this.root._videos.push(ctx)\r\n        }\r\n      }\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * @description 图片点击事件\r\n     * @param {Event} e\r\n     */\r\n    imgTap (e) {\r\n      const node = this.childs[e.currentTarget.dataset.i]\r\n      if (node.a) {\r\n        this.linkTap(node.a)\r\n        return\r\n      }\r\n      if (node.attrs.ignore) return\r\n      // #ifdef H5 || APP-PLUS\r\n      node.attrs.src = node.attrs.src || node.attrs['data-src']\r\n      // #endif\r\n      // #ifndef APP-HARMONY\r\n      this.root.$emit('imgtap', node.attrs)\r\n      // #endif\r\n      // #ifdef APP-HARMONY\r\n      this.root.$emit('imgtap', {\r\n        ...node.attrs\r\n      })\r\n      // #endif\r\n      // 自动预览图片\r\n      if (this.root.previewImg) {\r\n        uni.previewImage({\r\n          // #ifdef MP-WEIXIN\r\n          showmenu: this.root.showImgMenu,\r\n          // #endif\r\n          // #ifdef MP-ALIPAY\r\n          enablesavephoto: this.root.showImgMenu,\r\n          enableShowPhotoDownload: this.root.showImgMenu,\r\n          // #endif\r\n          current: parseInt(node.attrs.i),\r\n          urls: this.root.imgList\r\n        })\r\n      }\r\n    },\r\n\r\n    /**\r\n     * @description 图片长按\r\n     */\r\n    imgLongTap (e) {\r\n      // #ifdef APP-PLUS\r\n      const attrs = this.childs[e.currentTarget.dataset.i].attrs\r\n      if (this.opts[3] && !attrs.ignore) {\r\n        uni.showActionSheet({\r\n          itemList: ['保存图片'],\r\n          success: () => {\r\n            const save = path => {\r\n              uni.saveImageToPhotosAlbum({\r\n                filePath: path,\r\n                success () {\r\n                  uni.showToast({\r\n                    title: '保存成功'\r\n                  })\r\n                }\r\n              })\r\n            }\r\n            if (this.root.imgList[attrs.i].startsWith('http')) {\r\n              uni.downloadFile({\r\n                url: this.root.imgList[attrs.i],\r\n                success: res => save(res.tempFilePath)\r\n              })\r\n            } else {\r\n              save(this.root.imgList[attrs.i])\r\n            }\r\n          }\r\n        })\r\n      }\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * @description 图片加载完成事件\r\n     * @param {Event} e\r\n     */\r\n    imgLoad (e) {\r\n      const i = e.currentTarget.dataset.i\r\n      /* #ifndef H5 || (APP-PLUS && VUE2) */\r\n      if (!this.childs[i].w) {\r\n        // 设置原宽度\r\n        this.$set(this.ctrl, i, e.detail.width)\r\n      } else /* #endif */ if ((this.opts[1] && !this.ctrl[i]) || this.ctrl[i] === -1) {\r\n        // 加载完毕，取消加载中占位图\r\n        this.$set(this.ctrl, i, 1)\r\n      }\r\n      this.checkReady()\r\n    },\r\n\r\n    /**\r\n     * @description 检查是否所有图片加载完毕\r\n     */\r\n    checkReady () {\r\n      if (this.root && !this.root.lazyLoad) {\r\n        this.root._unloadimgs -= 1\r\n        if (!this.root._unloadimgs) {\r\n          setTimeout(() => {\r\n            this.root.getRect().then(rect => {\r\n              this.root.$emit('ready', rect)\r\n            }).catch(() => {\r\n              this.root.$emit('ready', {})\r\n            })\r\n          }, 350)\r\n        }\r\n      }\r\n    },\r\n\r\n    /**\r\n     * @description 链接点击事件\r\n     * @param {Event} e\r\n     */\r\n    linkTap (e) {\r\n      const node = e.currentTarget ? this.childs[e.currentTarget.dataset.i] : {}\r\n      const attrs = node.attrs || e\r\n      const href = attrs.href\r\n      this.root.$emit('linktap', Object.assign({\r\n        innerText: this.root.getText(node.children || []) // 链接内的文本内容\r\n      }, attrs))\r\n      if (href) {\r\n        if (href[0] === '#') {\r\n          // 跳转锚点\r\n          this.root.navigateTo(href.substring(1)).catch(() => { })\r\n        } else if (href.split('?')[0].includes('://')) {\r\n          // 复制外部链接\r\n          if (this.root.copyLink) {\r\n            // #ifdef H5\r\n            window.open(href)\r\n            // #endif\r\n            // #ifdef MP\r\n            uni.setClipboardData({\r\n              data: href,\r\n              success: () =>\r\n                uni.showToast({\r\n                  title: '链接已复制'\r\n                })\r\n            })\r\n            // #endif\r\n            // #ifdef APP-PLUS\r\n            plus.runtime.openWeb(href)\r\n            // #endif\r\n          }\r\n        } else {\r\n          // 跳转页面\r\n          uni.navigateTo({\r\n            url: href,\r\n            fail () {\r\n              uni.switchTab({\r\n                url: href,\r\n                fail () { }\r\n              })\r\n            }\r\n          })\r\n        }\r\n      }\r\n    },\r\n\r\n    /**\r\n     * @description 错误事件\r\n     * @param {Event} e\r\n     */\r\n    mediaError (e) {\r\n      const i = e.currentTarget.dataset.i\r\n      const node = this.childs[i]\r\n      // 加载其他源\r\n      if (node.name === 'video' || node.name === 'audio') {\r\n        let index = (this.ctrl[i] || 0) + 1\r\n        if (index > node.src.length) {\r\n          index = 0\r\n        }\r\n        if (index < node.src.length) {\r\n          this.$set(this.ctrl, i, index)\r\n          return\r\n        }\r\n      } else if (node.name === 'img') {\r\n        // #ifdef H5 && VUE3\r\n        if (this.opts[0] && !this.ctrl.load) return\r\n        // #endif\r\n        // 显示错误占位图\r\n        if (this.opts[2]) {\r\n          this.$set(this.ctrl, i, -1)\r\n        }\r\n        this.checkReady()\r\n      }\r\n      if (this.root) {\r\n        this.root.$emit('error', {\r\n          source: node.name,\r\n          attrs: node.attrs,\r\n          // #ifndef H5 && VUE3\r\n          errMsg: e.detail.errMsg\r\n          // #endif\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style>\r\n/* a 标签默认效果 */\r\n._a {\r\n  padding: 1.5px 0 1.5px 0;\r\n  color: #366092;\r\n  word-break: break-all;\r\n}\r\n\r\n/* a 标签点击态效果 */\r\n._hover {\r\n  text-decoration: underline;\r\n  opacity: 0.7;\r\n}\r\n\r\n/* 图片默认效果 */\r\n._img {\r\n  max-width: 100%;\r\n  -webkit-touch-callout: none;\r\n}\r\n\r\n/* 内部样式 */\r\n\r\n._block {\r\n  display: block;\r\n}\r\n\r\n._b,\r\n._strong {\r\n  font-weight: bold;\r\n}\r\n\r\n._code {\r\n  font-family: monospace;\r\n}\r\n\r\n._del {\r\n  text-decoration: line-through;\r\n}\r\n\r\n._em,\r\n._i {\r\n  font-style: italic;\r\n}\r\n\r\n._h1 {\r\n  font-size: 2em;\r\n}\r\n\r\n._h2 {\r\n  font-size: 1.5em;\r\n}\r\n\r\n._h3 {\r\n  font-size: 1.17em;\r\n}\r\n\r\n._h5 {\r\n  font-size: 0.83em;\r\n}\r\n\r\n._h6 {\r\n  font-size: 0.67em;\r\n}\r\n\r\n._h1,\r\n._h2,\r\n._h3,\r\n._h4,\r\n._h5,\r\n._h6 {\r\n  display: block;\r\n  font-weight: bold;\r\n}\r\n\r\n._image {\r\n  height: 1px;\r\n}\r\n\r\n._ins {\r\n  text-decoration: underline;\r\n}\r\n\r\n._li {\r\n  display: list-item;\r\n}\r\n\r\n._ol {\r\n  list-style-type: decimal;\r\n}\r\n\r\n._ol,\r\n._ul {\r\n  display: block;\r\n  padding-left: 40px;\r\n  margin: 1em 0;\r\n}\r\n\r\n._q::before {\r\n  content: '\"';\r\n}\r\n\r\n._q::after {\r\n  content: '\"';\r\n}\r\n\r\n._sub {\r\n  font-size: smaller;\r\n  vertical-align: sub;\r\n}\r\n\r\n._sup {\r\n  font-size: smaller;\r\n  vertical-align: super;\r\n}\r\n\r\n._thead,\r\n._tbody,\r\n._tfoot {\r\n  display: table-row-group;\r\n}\r\n\r\n._tr {\r\n  display: table-row;\r\n}\r\n\r\n._td,\r\n._th {\r\n  display: table-cell;\r\n  vertical-align: middle;\r\n}\r\n\r\n._th {\r\n  font-weight: bold;\r\n  text-align: center;\r\n}\r\n\r\n._ul {\r\n  list-style-type: disc;\r\n}\r\n\r\n._ul ._ul {\r\n  margin: 0;\r\n  list-style-type: circle;\r\n}\r\n\r\n._ul ._ul ._ul {\r\n  list-style-type: square;\r\n}\r\n\r\n._abbr,\r\n._b,\r\n._code,\r\n._del,\r\n._em,\r\n._i,\r\n._ins,\r\n._label,\r\n._q,\r\n._span,\r\n._strong,\r\n._sub,\r\n._sup {\r\n  display: inline;\r\n}\r\n\r\n/* #ifdef APP-PLUS */\r\n._video {\r\n  width: 300px;\r\n  height: 225px;\r\n}\r\n/* #endif */\r\n</style>", "import Component from 'D:/uniapp/vue3/uni_modules/mp-html/components/mp-html/node/node.vue'\nwx.createComponent(Component)"], "names": ["uni", "node", "i"], "mappings": ";;;AAmHA,MAAO,OAAM,MAAI,QAAA,QAAA,EAAA,KAAA,MAAA,0FAAA;AACjB,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,IAEP,aAAa;AAAA,EAKd;AAAA,EACD,OAAQ;AACN,WAAO;AAAA,MACL,MAAM,CAAE;AAAA,MAER,OAAOA,cAAG,MAAC,kBAAiB,EAAG,OAAO,SAAS,KAAK;AAAA,IAEtD;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAW;AACT,eAAO,CAAC;AAAA,MACV;AAAA,IACD;AAAA,IACD,QAAQ;AAAA,IACR,MAAM;AAAA,EACP;AAAA,EACD,YAAY;AAAA,IAGV;AAAA,EAED;AAAA,EACD,UAAW;AACT,SAAK,UAAU,MAAM;AACnB,WAAK,KAAK,OAAO,KAAK,SAAS,KAAK,KAAK,SAAS,SAAS,WAAW,KAAK,OAAO,KAAK,KAAK;AAAQ;AAAA,KACrG;AAAA,EAqBF;AAAA,EACD,gBAAiB;AAAA,EAMhB;AAAA,EACD,SAAQ;AAAA,IAEN,SAAU;AAAE,aAAO;AAAA,IAAM;AAAA;AAAA;AAAA;AAAA;AAAA,IAMzB,KAAM,GAAG;AACP,YAAM,IAAI,EAAE,cAAc,QAAQ;AAClC,YAAMC,QAAO,KAAK,OAAO,CAAC;AAC1B,WAAK,KAAK,MAAM,QAAQ;AAAA,QACtB,QAAQA,MAAK;AAAA,QACb,OAAO;AAAA,UACL,GAAGA,MAAK;AAAA,UACR,KAAKA,MAAK,IAAI,KAAK,KAAK,CAAC,KAAK,CAAC;AAAA,QACjC;AAAA,OACD;AAED,UAAI,KAAK,KAAK,YAAY;AACxB,YAAI,OAAO;AACX,cAAM,KAAK,EAAE,OAAO;AACpB,iBAASC,KAAI,KAAK,KAAK,QAAQ,QAAQA,QAAM;AAC3C,cAAI,KAAK,KAAK,QAAQA,EAAC,EAAE,OAAO,IAAI;AAClC,mBAAO;AAAA,iBACF;AACL,iBAAK,KAAK,QAAQA,EAAC,EAAE,MAAM;AAAA,UAC7B;AAAA,QACF;AAEA,YAAI,CAAC,MAAM;AACT,gBAAM,MAAMF,oBAAI;AAAA,YAAmB;AAAA,YAE/B;AAAA,UAEJ;AACA,cAAI,KAAK;AACT,cAAI,KAAK,KAAK,cAAc;AAC1B,gBAAI,aAAa,KAAK,KAAK,YAAY;AAAA,UACzC;AACA,eAAK,KAAK,QAAQ,KAAK,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,IAED;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,OAAQ,GAAG;AACT,YAAMC,QAAO,KAAK,OAAO,EAAE,cAAc,QAAQ,CAAC;AAClD,UAAIA,MAAK,GAAG;AACV,aAAK,QAAQA,MAAK,CAAC;AACnB;AAAA,MACF;AACA,UAAIA,MAAK,MAAM;AAAQ;AAKvB,WAAK,KAAK,MAAM,UAAUA,MAAK,KAAK;AAQpC,UAAI,KAAK,KAAK,YAAY;AACxBD,sBAAAA,MAAI,aAAa;AAAA,UAEf,UAAU,KAAK,KAAK;AAAA,UAMpB,SAAS,SAASC,MAAK,MAAM,CAAC;AAAA,UAC9B,MAAM,KAAK,KAAK;AAAA,SACjB;AAAA,MACH;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,WAAY,GAAG;AAAA,IA6Bd;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,QAAS,GAAG;AACV,YAAM,IAAI,EAAE,cAAc,QAAQ;AAElC,UAAI,CAAC,KAAK,OAAO,CAAC,EAAE,GAAG;AAErB,aAAK,KAAK,KAAK,MAAM,GAAG,EAAE,OAAO,KAAK;AAAA,MACtC,WAAU,KAAK,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,KAAM,KAAK,KAAK,CAAC,MAAM,IAAI;AAEjE,aAAK,KAAK,KAAK,MAAM,GAAG,CAAC;AAAA,MAC3B;AACA,WAAK,WAAW;AAAA,IACjB;AAAA;AAAA;AAAA;AAAA,IAKD,aAAc;AACZ,UAAI,KAAK,QAAQ,CAAC,KAAK,KAAK,UAAU;AACpC,aAAK,KAAK,eAAe;AACzB,YAAI,CAAC,KAAK,KAAK,aAAa;AAC1B,qBAAW,MAAM;AACf,iBAAK,KAAK,UAAU,KAAK,UAAQ;AAC/B,mBAAK,KAAK,MAAM,SAAS,IAAI;AAAA,aAC9B,EAAE,MAAM,MAAM;AACb,mBAAK,KAAK,MAAM,SAAS,CAAA,CAAE;AAAA,aAC5B;AAAA,UACF,GAAE,GAAG;AAAA,QACR;AAAA,MACF;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,QAAS,GAAG;AACV,YAAMA,QAAO,EAAE,gBAAgB,KAAK,OAAO,EAAE,cAAc,QAAQ,CAAC,IAAI,CAAC;AACzE,YAAM,QAAQA,MAAK,SAAS;AAC5B,YAAM,OAAO,MAAM;AACnB,WAAK,KAAK,MAAM,WAAW,OAAO,OAAO;AAAA,QACvC,WAAW,KAAK,KAAK,QAAQA,MAAK,YAAY,EAAE;AAAA;AAAA,MACjD,GAAE,KAAK,CAAC;AACT,UAAI,MAAM;AACR,YAAI,KAAK,CAAC,MAAM,KAAK;AAEnB,eAAK,KAAK,WAAW,KAAK,UAAU,CAAC,CAAC,EAAE,MAAM,MAAM;AAAA,WAAG;AAAA,QACzD,WAAW,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE,SAAS,KAAK,GAAG;AAE7C,cAAI,KAAK,KAAK,UAAU;AAKtBD,0BAAAA,MAAI,iBAAiB;AAAA,cACnB,MAAM;AAAA,cACN,SAAS,MACPA,cAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,eACR;AAAA,aACJ;AAAA,UAKH;AAAA,eACK;AAELA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK;AAAA,YACL,OAAQ;AACNA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,KAAK;AAAA,gBACL,OAAQ;AAAA,gBAAE;AAAA,eACX;AAAA,YACH;AAAA,WACD;AAAA,QACH;AAAA,MACF;AAAA,IACD;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,WAAY,GAAG;AACb,YAAM,IAAI,EAAE,cAAc,QAAQ;AAClC,YAAMC,QAAO,KAAK,OAAO,CAAC;AAE1B,UAAIA,MAAK,SAAS,WAAWA,MAAK,SAAS,SAAS;AAClD,YAAI,SAAS,KAAK,KAAK,CAAC,KAAK,KAAK;AAClC,YAAI,QAAQA,MAAK,IAAI,QAAQ;AAC3B,kBAAQ;AAAA,QACV;AACA,YAAI,QAAQA,MAAK,IAAI,QAAQ;AAC3B,eAAK,KAAK,KAAK,MAAM,GAAG,KAAK;AAC7B;AAAA,QACF;AAAA,iBACSA,MAAK,SAAS,OAAO;AAK9B,YAAI,KAAK,KAAK,CAAC,GAAG;AAChB,eAAK,KAAK,KAAK,MAAM,GAAG,EAAE;AAAA,QAC5B;AACA,aAAK,WAAW;AAAA,MAClB;AACA,UAAI,KAAK,MAAM;AACb,aAAK,KAAK,MAAM,SAAS;AAAA,UACvB,QAAQA,MAAK;AAAA,UACb,OAAOA,MAAK;AAAA,UAEZ,QAAQ,EAAE,OAAO;AAAA,SAElB;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtaA,GAAG,gBAAgB,SAAS;;;;"}