
.container {
  background: #fff;
  min-height: 100vh;
  padding-bottom: 40rpx;
}
.search-box {
  margin: 30rpx 30rpx 30rpx 30rpx;
  background: #f6f7fa;
  border-radius: 30rpx;
  height: 60rpx;
  align-items: center;
  padding: 0 20rpx;
}
.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}
.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 26rpx;
  color: #333;
}
.clear-btn {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
}
.clear-icon {
  width: 20rpx;
  height: 20rpx;
}
/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}
.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.empty-tip {
  font-size: 24rpx;
  color: #999;
}

/* 内容区域 */
.content-area {
  min-height: 60vh;
}
.section {
  margin-bottom: 40rpx;
}
.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #222;
  margin: 30rpx 0 18rpx 30rpx;
  display: flex;
  align-items: center;
}
.member-count {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
  margin-left: 8rpx;
}
.member-list {
  margin: 0;
}
.member-item {
  padding: 5rpx 25rpx;
  display: flex;
  align-items: center;
  position: relative;
  min-height: 100rpx;
}
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background: #f5f5f5;
  flex-shrink: 0;
}
.info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.name {
  font-size: 28rpx;
  font-weight: 700;
  color: #222;
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}
.role-tag {
  font-size: 20rpx;
  border-radius: 8rpx;
  padding: 2rpx 14rpx;
  margin-left: 12rpx;
  color: #fff;
  font-weight: 400;
}
.role-tag.owner {
  background: #ff9500;
}
.role-tag.admin {
  background: #3da0ff;
}
.status-tag {
  font-size: 20rpx;
  border-radius: 8rpx;
  padding: 2rpx 14rpx;
  margin-left: 12rpx;
  font-weight: 400;
}
.status-tag.muted {
  background: #ff4757;
  color: #fff;
}
.meta {
  font-size: 22rpx;
  color: #999;
  display: flex;
  align-items: center;
  line-height: 1.4;
}
.gender {
  margin-right: 6rpx;
}
.male {
  color: #4e6ef2;
}
.female {
  color: #fa5a8a;
}
.age {
  margin-right: 12rpx;
}
.contrib {
  color: #999;
}
.action-buttons {
  margin-left: 20rpx;
  flex-shrink: 0;
}
.action-btn {
  background: #007aff;
  border-radius: 16rpx;
  padding: 6rpx 16rpx;
  font-size: 22rpx;
  color: #fff;
  min-width: 60rpx;
  text-align: center;
}
.action-btn:active {
  background: #0056cc;
}
.df {
  display: flex;
  align-items: center;
}
