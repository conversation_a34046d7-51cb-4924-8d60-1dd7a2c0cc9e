{"version": 3, "file": "cache.js", "sources": ["config/cache.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nconst cacheConfig = {\n\t//token\n\tLOGIN_STATUS: 'LOGIN_STATUS_TOKEN',\n\t//uid\n\tUID:'UID',\n\t//用户信息\n\tUSER_INFO: 'USER_INFO',\n\t//token过期时间\n\tEXPIRES_TIME: 'EXPIRES_TIME',\n\t//微信登录\n\tWX_AUTH: 'WX_AUTH',\n\t//公众号登录code\n\tSTATE_KEY: 'wx_authorize_state',\n\t//登录类型\n\tLOGINTYPE: 'loginType',\n\t//登录跳转地址\n\tBACK_URL: 'login_back_url',\n\t//小程序登录状态code\n\tSTATE_R_KEY: 'roution_authorize_state',\n\t//logo 地址\n\tLOGO_URL: 'LOGO_URL',\n\t//模板缓存\n\tSUBSCRIBE_MESSAGE: 'SUBSCRIBE_MESSAGE',\n\n\tTIPS_KEY: 'TIPS_KEY',\n\n\tSPREAD: 'spread',\n\t//缓存经度\n\tCACHE_LONGITUDE: 'LONGITUDE',\n\t//缓存纬度\n\tCACHE_LATITUDE: 'LATITUDE',\n}\n\n// ES6默认导出\nexport default cacheConfig\n"], "names": [], "mappings": ";AAUK,MAAC,cAAc;AAAA;AAAA,EAEnB,cAAc;AAAA;AAAA,EAEd,KAAI;AAAA;AAAA,EAEJ,WAAW;AAAA;AAAA,EAEX,cAAc;AAAA;AAAA,EAEd,SAAS;AAAA;AAAA,EAET,WAAW;AAAA;AAAA,EAEX,WAAW;AAAA;AAAA,EAEX,UAAU;AAAA;AAAA,EAEV,aAAa;AAAA;AAAA,EAEb,UAAU;AAAA;AAAA,EAEV,mBAAmB;AAAA,EAEnB,UAAU;AAAA,EAEV,QAAQ;AAAA;AAAA,EAER,iBAAiB;AAAA;AAAA,EAEjB,gBAAgB;AACjB;;"}