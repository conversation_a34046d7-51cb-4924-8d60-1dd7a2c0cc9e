
.container {
  min-height: 100vh;
  background: #f8f8f8;
}
.nav-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}
.nav-back {
  padding: 0 30rpx;
  width: 34rpx;
  height: 100%;
}
.nav-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
}
.topic-list {
  padding: 20rpx;
}
.topic-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}
.topic-content {
  flex: 1;
}
.topic-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.topic-hash {
  font-size: 32rpx;
  font-weight: bold;
  color: #007AFF;
  margin-right: 8rpx;
}
.topic-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.topic-stats {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.stat-text {
  font-size: 24rpx;
  color: #999;
}
.stat-divider {
  margin: 0 8rpx;
  font-size: 24rpx;
  color: #999;
}
.topic-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}
.topic-arrow {
  width: 32rpx;
  height: 32rpx;
}
.topic-arrow image {
  width: 100%;
  height: 100%;
}
.empty-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-box image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
.loading-box {
  display: flex;
  justify-content: center;
  padding: 50rpx 0;
}
.loading-box text {
  font-size: 28rpx;
  color: #999;
}
.df {
  display: flex;
  align-items: center;
}
