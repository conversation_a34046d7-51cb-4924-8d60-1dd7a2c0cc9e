
.share-container.data-v-fd563daa {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
}
.share-mask.data-v-fd563daa {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
}
.share-content.data-v-fd563daa {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: #ffffff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.share-header.data-v-fd563daa {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 90rpx;
  border-bottom: 1rpx solid #f2f2f2;
}
.share-title.data-v-fd563daa {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}
.popup-close.data-v-fd563daa {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f8f8;
  justify-content: center;
}

/* 分割线样式 */
.divider-line.data-v-fd563daa {
  height: 1px;
  background-color: #eeeeee;
  width: 100%;
}

/* 人员列表样式 */
.person-scroll.data-v-fd563daa {
  width: 100%;
  white-space: nowrap;
  padding: 30rpx 0;
  background-color: #ffffff;
}
.person-list.data-v-fd563daa {
  width: 100%;
  display: flex;
  padding: 0 10rpx;
}
.person-item.data-v-fd563daa {
  flex-shrink: 0;
}
.person-avatar.data-v-fd563daa {
  margin: 0 20rpx;
  width: 116rpx;
  height: 116rpx;
  border-radius: 50%;
  background: #f8f8f8;
  border: 2rpx solid #f5f5f5;
  position: relative;
}
.person-avatar image.data-v-fd563daa {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.person-online-dot.data-v-fd563daa {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  border: 6rpx solid #fff;
  z-index: 10;
}
.person-name.data-v-fd563daa {
  margin: 20rpx 0 10rpx;
  width: 160rpx;
  color: #000;
  font-weight: 500;
  font-size: 24rpx;
  line-height: 24rpx;
  text-align: center;
}
.person-tips.data-v-fd563daa {
  width: 160rpx;
  color: #999;
  font-size: 18rpx;
  line-height: 18rpx;
  font-weight: 300;
  text-align: center;
}
.ohto.data-v-fd563daa {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 分享选项滚动样式 */
.share-scroll.data-v-fd563daa {
  width: 100%;
  white-space: nowrap;
  padding: 30rpx 0;
  background-color: #ffffff;
}
.share-list.data-v-fd563daa {
  display: flex;
  padding: 0 10rpx;
}
.share-item.data-v-fd563daa {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 20rpx;
  background: none;
  border: none;
  padding: 0;
  font-size: inherit;
  color: inherit;
}
.share-button.data-v-fd563daa {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 20rpx !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}
.share-button.data-v-fd563daa::after {
  border: none !important;
}
.share-icon-bg.data-v-fd563daa {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}
.bottom-icon.data-v-fd563daa {
  background-color: #f8f8f8 !important;
}
.delete-item.data-v-fd563daa {
  background-color: #fff2f2 !important;
}
.share-icon.data-v-fd563daa {
  width: 50rpx;
  height: 50rpx;
}
.share-text.data-v-fd563daa {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  width: 120rpx;
  margin-top: 8rpx;
}
.delete-text.data-v-fd563daa {
  color: #ff4757 !important;
}
.safe-bottom.data-v-fd563daa {
  height: 34rpx;
  height: calc(34rpx + env(safe-area-inset-bottom));
}

/* 海报相关样式 */
.poster-popup.data-v-fd563daa {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.8);
}
.poster-popup .close.data-v-fd563daa {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
}
.poster-img.data-v-fd563daa {
  max-width: 80%;
  max-height: 70%;
  border-radius: 10rpx;
}
.save-poster.data-v-fd563daa {
  margin-top: 40rpx;
  padding: 20rpx 40rpx;
  background-color: #007aff;
  color: white;
  border-radius: 50rpx;
  font-size: 28rpx;
}
.keep.data-v-fd563daa {
  margin-top: 40rpx;
  color: white;
  font-size: 28rpx;
  text-align: center;
}
.canvas.data-v-fd563daa {
  position: fixed;
  top: -9999rpx;
  left: -9999rpx;
  width: 750rpx;
  height: 1334rpx;
}
.mask.data-v-fd563daa {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 299;
  background-color: rgba(0, 0, 0, 0.6);
}

/* H5分享图片 */
.share-box.data-v-fd563daa {
  z-index: 1000;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.share-box image.data-v-fd563daa {
  width: 100%;
  height: 100%;
}
