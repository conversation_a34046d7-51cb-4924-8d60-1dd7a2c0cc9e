"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const money = () => "../../components/money/money.js";
const app = getApp();
const _sfc_main = {
  components: {
    lazyImage,
    money
  },
  data() {
    return {
      statusBarHeight: app.globalData.statusBarHeight || 20,
      titleBarHeight: app.globalData.titleBarHeight || 44,
      navbarTrans: 0,
      goodsInfo: {
        id: 1,
        name: "智能手表",
        intro: "支持心率监测、睡眠监测、消息提醒等多种功能",
        imgs: [
          "/static/img/avatar.png",
          "/static/img/avatar.png",
          "/static/img/avatar.png"
        ],
        details: "<p>这是一款功能强大的智能手表，支持心率监测、睡眠监测、消息提醒等多种功能。</p>",
        buy: 1250,
        view: 3560,
        cart: 80,
        comment: 35,
        tags: ["新品", "热卖", "质保"],
        comment_user: [
          "/static/img/avatar.png",
          "/static/img/avatar.png",
          "/static/img/avatar.png"
        ],
        product: [
          {
            id: 101,
            name: "黑色标准版",
            img: "/static/img/avatar.png",
            price: "299.00",
            line_price: "399.00",
            stock: 120
          },
          {
            id: 102,
            name: "白色标准版",
            img: "/static/img/avatar.png",
            price: "299.00",
            line_price: "399.00",
            stock: 85
          },
          {
            id: 103,
            name: "蓝色豪华版",
            img: "/static/img/avatar.png",
            price: "399.00",
            line_price: "499.00",
            stock: 0
          }
        ],
        cart_count: 2,
        type: 1
      },
      imgIdx: 0,
      productIdx: 0,
      quantity: 1,
      tipsTitle: ""
    };
  },
  onLoad(options) {
    common_vendor.index.showShareMenu({
      withShareTicket: true,
      menus: ["shareAppMessage", "shareTimeline"]
    });
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight || 20;
    this.titleBarHeight = 44;
    if (options.id && options.id > 0) {
      this.goodsInfo.id = options.id;
    }
  },
  methods: {
    // 添加到购物车
    addCartClick() {
      this.goodsInfo.cart_count = parseInt(this.goodsInfo.cart_count) + 1;
      this.opTipsPopup("共 " + this.quantity + " 件商品已经为您加入购物车 🎉");
      app.globalData.isCenterPage = true;
    },
    // 立即购买
    buyNowClick() {
      if (this.goodsInfo.product[this.productIdx].stock > 0) {
        common_vendor.index.navigateTo({
          url: "/pages/order/settlement?type=1&pid=" + this.goodsInfo.product[this.productIdx].id + "&quantity=" + this.quantity
        });
      } else {
        this.opTipsPopup("该款式已售罄暂时无法购买，请稍后重试！");
      }
    },
    // 数量选择
    quantityBtn(e) {
      let type = e.currentTarget.dataset.type;
      if (type == 0 && parseInt(this.quantity) <= 1)
        return;
      if (parseInt(this.quantity) > this.goodsInfo.product[this.productIdx].stock) {
        this.quantity = this.goodsInfo.product[this.productIdx].stock;
        this.opTipsPopup("该款式最多可购买 " + this.quantity + " 件！");
      } else if (this.goodsInfo.product[this.productIdx].stock && this.quantity && this.quantity != 0) {
        if (type == 0) {
          this.quantity = parseInt(this.quantity) - 1;
        }
        if (type == 1 && parseInt(this.quantity) < this.goodsInfo.product[this.productIdx].stock) {
          this.quantity = parseInt(this.quantity) + 1;
        }
      } else {
        this.quantity = 1;
      }
    },
    // 淘宝链接
    taobaoClick() {
      common_vendor.index.setClipboardData({
        data: "https://example.com/shopping",
        success: function() {
          common_vendor.index.hideToast();
        }
      });
      this.opTipsPopup("复制成功，打开手机淘宝即可优惠购！");
    },
    // 图片点击
    imgParamTap(e) {
      let type = e.currentTarget.dataset.type;
      let idx = e.currentTarget.dataset.idx;
      let current = "";
      let urls = [];
      if (type == 1) {
        current = this.goodsInfo.imgs[idx];
        urls = this.goodsInfo.imgs;
      } else {
        current = this.goodsInfo.product[idx].img;
        urls = [current];
      }
      common_vendor.index.previewImage({
        current,
        urls
      });
    },
    // 轮播图切换
    imagesSwiper(e) {
      this.imgIdx = e.detail.current;
    },
    // 进入购物车
    toCart() {
      common_vendor.index.navigateTo({
        url: "/pages/goods/cart"
      });
    },
    // 进入评价页
    toEvaluate() {
      common_vendor.index.navigateTo({
        url: "/pages/goods/evaluate?id=" + this.goodsInfo.id + "&name=" + this.goodsInfo.name + "&count=" + this.goodsInfo.comment + "&img=" + this.goodsInfo.imgs[0]
      });
    },
    // 返回
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "/pages/tabbar/shop"
        });
      }
    },
    // 显示提示
    opTipsPopup(msg, isBack = false) {
      this.tipsTitle = msg;
      this.$refs.tipsPopup.open();
      setTimeout(() => {
        this.$refs.tipsPopup.close();
        if (isBack) {
          setTimeout(() => {
            this.navBack();
          }, 300);
        }
      }, 2e3);
    }
  },
  onPageScroll(e) {
    let t = (e.scrollTop > 150 ? 150 : e.scrollTop) / 150;
    this.navbarTrans = t;
  },
  onShareAppMessage() {
    return {
      title: this.goodsInfo.name,
      imageUrl: this.goodsInfo.imgs[0],
      path: "/pages/goods/details?id=" + this.goodsInfo.id
    };
  },
  onShareTimeline() {
    return {
      title: this.goodsInfo.name,
      imageUrl: this.goodsInfo.imgs[0],
      query: "id=" + this.goodsInfo.id
    };
  }
};
if (!Array) {
  const _component_lazy_image = common_vendor.resolveComponent("lazy-image");
  const _component_money = common_vendor.resolveComponent("money");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_lazy_image + _component_money + _easycom_uni_popup2)();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: $data.titleBarHeight + "px",
    c: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    d: $data.navbarTrans == 1
  }, $data.navbarTrans == 1 ? {
    e: common_vendor.t($data.goodsInfo.name)
  } : {}, {
    f: $data.statusBarHeight + "px",
    g: "rgba(255,255,255," + $data.navbarTrans + ")",
    h: common_vendor.f($data.goodsInfo.imgs, (item, index, i0) => {
      return {
        a: "122fecef-0-" + i0,
        b: common_vendor.p({
          src: item
        }),
        c: index,
        d: index,
        e: common_vendor.o((...args) => $options.imgParamTap && $options.imgParamTap(...args), index)
      };
    }),
    i: common_vendor.o((...args) => $options.imagesSwiper && $options.imagesSwiper(...args)),
    j: $data.goodsInfo.imgs.length
  }, $data.goodsInfo.imgs.length ? {
    k: common_vendor.f($data.goodsInfo.imgs, (v, index, i0) => {
      return {
        a: index,
        b: common_vendor.n($data.imgIdx == index && "act")
      };
    }),
    l: 100 / $data.goodsInfo.imgs.length + "%"
  } : {}, {
    m: common_vendor.f($data.goodsInfo.tags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag),
        b: index
      };
    }),
    n: common_vendor.t($data.goodsInfo.name),
    o: common_vendor.t($data.goodsInfo.intro),
    p: common_vendor.p({
      price: $data.goodsInfo.product[$data.productIdx].price
    }),
    q: $data.goodsInfo.product[$data.productIdx].line_price
  }, $data.goodsInfo.product[$data.productIdx].line_price ? {
    r: common_vendor.t($data.goodsInfo.product[$data.productIdx].line_price)
  } : {}, {
    s: common_vendor.t($data.goodsInfo.buy ? $data.goodsInfo.buy + "人已买" : $data.goodsInfo.cart + $data.goodsInfo.browse + "人想买"),
    t: common_vendor.f($data.goodsInfo.product, (item, index, i0) => {
      return {
        a: index,
        b: common_vendor.o((...args) => $options.imgParamTap && $options.imgParamTap(...args), index),
        c: item.img,
        d: common_vendor.t(item.name),
        e: common_vendor.t(item.price),
        f: index,
        g: $data.productIdx == index ? "#000" : "#f8f8f8",
        h: common_vendor.o(() => $data.productIdx = index, index)
      };
    }),
    v: common_assets._imports_1$14,
    w: $data.quantity > 1 ? "#000" : "#ccc",
    x: common_vendor.o((...args) => $options.quantityBtn && $options.quantityBtn(...args)),
    y: common_vendor.o((...args) => $options.quantityBtn && $options.quantityBtn(...args)),
    z: $data.quantity,
    A: common_vendor.o(($event) => $data.quantity = $event.detail.value),
    B: $data.quantity < $data.goodsInfo.product[$data.productIdx].stock ? "#000" : "#ccc",
    C: common_vendor.o((...args) => $options.quantityBtn && $options.quantityBtn(...args)),
    D: $data.goodsInfo.comment > 0
  }, $data.goodsInfo.comment > 0 ? common_vendor.e({
    E: common_vendor.t($data.goodsInfo.comment),
    F: $data.goodsInfo.comment_user.length > 0
  }, $data.goodsInfo.comment_user.length > 0 ? {
    G: common_vendor.f($data.goodsInfo.comment_user, (img, index, i0) => {
      return {
        a: img,
        b: index
      };
    })
  } : {}, {
    H: common_assets._imports_2$6,
    I: common_vendor.o((...args) => $options.toEvaluate && $options.toEvaluate(...args))
  }) : {}, {
    J: $data.goodsInfo.details,
    K: common_assets._imports_2$3,
    L: $data.goodsInfo.name,
    M: "/pages/goods/details?id=" + $data.goodsInfo.id,
    N: $data.goodsInfo.imgs[0],
    O: common_assets._imports_4,
    P: $data.goodsInfo.cart_count
  }, $data.goodsInfo.cart_count ? {
    Q: common_vendor.t($data.goodsInfo.cart_count > 99 ? "99+" : $data.goodsInfo.cart_count)
  } : {}, {
    R: common_vendor.o((...args) => $options.toCart && $options.toCart(...args)),
    S: $data.goodsInfo.type == 2
  }, $data.goodsInfo.type == 2 ? {
    T: common_assets._imports_5$3,
    U: common_vendor.o((...args) => $options.taobaoClick && $options.taobaoClick(...args))
  } : {
    V: common_vendor.o((...args) => $options.addCartClick && $options.addCartClick(...args)),
    W: common_vendor.t($data.goodsInfo.product[$data.productIdx].stock <= 0 ? "暂无库存" : "立即购买"),
    X: common_vendor.o((...args) => $options.buyNowClick && $options.buyNowClick(...args)),
    Y: common_vendor.n($data.goodsInfo.product[$data.productIdx].stock <= 0 && "bg2")
  }, {
    Z: common_vendor.t($data.tipsTitle),
    aa: common_vendor.sr("tipsPopup", "122fecef-2"),
    ab: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 7;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/goods/details.js.map
