.mobile-bg {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
}
.mobile-mask {
  z-index: 20;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 67rpx 30rpx;
  background: #fff;
}
.mobile-mask .input-item {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 86rpx;
  margin-bottom: 38rpx;
}
.mobile-mask .input-item input {
  flex: 1;
  display: block;
  height: 100%;
  padding-left: 40rpx;
  border-radius: 43rpx;
  border: 1px solid #dcdcdc;
}
.mobile-mask .input-item .code {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 220rpx;
  height: 86rpx;
  margin-left: 30rpx;
  background: var(--view-minorColorT);
  font-size: 28rpx;
  color: var(--view-theme);
  border-radius: 43rpx;
}
.mobile-mask .input-item .code[disabled] {
  background: rgba(0,0,0,0.05);
  color: #999;
}
.mobile-mask .sub_btn {
  width: 690rpx;
  height: 86rpx;
  line-height: 86rpx;
  margin-top: 60rpx;
  background: var(--view-theme);
  border-radius: 43rpx;
  color: #fff;
  font-size: 28rpx;
  text-align: center;
}
.animated {
  animation-duration: 0.4s;
}
