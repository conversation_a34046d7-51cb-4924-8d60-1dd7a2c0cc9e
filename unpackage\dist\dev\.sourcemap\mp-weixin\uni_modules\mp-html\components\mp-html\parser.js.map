{"version": 3, "file": "parser.js", "sources": ["uni_modules/mp-html/components/mp-html/parser.js"], "sourcesContent": ["/**\r\n * @fileoverview html 解析器\r\n */\r\n\r\n// 配置\r\nconst config = {\r\n  // 信任的标签（保持标签名不变）\r\n  trustTags: makeMap('a,abbr,ad,audio,b,blockquote,br,code,col,colgroup,dd,del,dl,dt,div,em,fieldset,h1,h2,h3,h4,h5,h6,hr,i,img,ins,label,legend,li,ol,p,q,ruby,rt,source,span,strong,sub,sup,table,tbody,td,tfoot,th,thead,tr,title,ul,video'),\r\n\r\n  // 块级标签（转为 div，其他的非信任标签转为 span）\r\n  blockTags: makeMap('address,article,aside,body,caption,center,cite,footer,header,html,nav,pre,section'),\r\n\r\n  // #ifdef (MP-WEIXIN || MP-QQ || APP-PLUS || MP-360) && VUE3\r\n  // 行内标签\r\n  inlineTags: makeMap('abbr,b,big,code,del,em,i,ins,label,q,small,span,strong,sub,sup'),\r\n  // #endif\r\n\r\n  // 要移除的标签\r\n  ignoreTags: makeMap('area,base,canvas,embed,frame,head,iframe,input,link,map,meta,param,rp,script,source,style,textarea,title,track,wbr'),\r\n\r\n  // 自闭合的标签\r\n  voidTags: makeMap('area,base,br,col,circle,ellipse,embed,frame,hr,img,input,line,link,meta,param,path,polygon,rect,source,track,use,wbr'),\r\n\r\n  // html 实体\r\n  entities: {\r\n    lt: '<',\r\n    gt: '>',\r\n    quot: '\"',\r\n    apos: \"'\",\r\n    ensp: '\\u2002',\r\n    emsp: '\\u2003',\r\n    nbsp: '\\xA0',\r\n    semi: ';',\r\n    ndash: '–',\r\n    mdash: '—',\r\n    middot: '·',\r\n    lsquo: '‘',\r\n    rsquo: '’',\r\n    ldquo: '“',\r\n    rdquo: '”',\r\n    bull: '•',\r\n    hellip: '…',\r\n    larr: '←',\r\n    uarr: '↑',\r\n    rarr: '→',\r\n    darr: '↓'\r\n  },\r\n\r\n  // 默认的标签样式\r\n  tagStyle: {\r\n    // #ifndef APP-PLUS-NVUE\r\n    address: 'font-style:italic',\r\n    big: 'display:inline;font-size:1.2em',\r\n    caption: 'display:table-caption;text-align:center',\r\n    center: 'text-align:center',\r\n    cite: 'font-style:italic',\r\n    dd: 'margin-left:40px',\r\n    mark: 'background-color:yellow',\r\n    pre: 'font-family:monospace;white-space:pre',\r\n    s: 'text-decoration:line-through',\r\n    small: 'display:inline;font-size:0.8em',\r\n    strike: 'text-decoration:line-through',\r\n    u: 'text-decoration:underline'\r\n    // #endif\r\n  },\r\n\r\n  // svg 大小写对照表\r\n  svgDict: {\r\n    animatetransform: 'animateTransform',\r\n    lineargradient: 'linearGradient',\r\n    viewbox: 'viewBox',\r\n    attributename: 'attributeName',\r\n    repeatcount: 'repeatCount',\r\n    repeatdur: 'repeatDur',\r\n    foreignobject: 'foreignObject'\r\n  }\r\n}\r\nconst tagSelector={}\r\nlet windowWidth, system\r\n// #ifdef MP-WEIXIN\r\nif (uni.canIUse('getWindowInfo')) {\r\n  windowWidth = uni.getWindowInfo().windowWidth\r\n  system = uni.getDeviceInfo().system\r\n} else {\r\n// #endif\r\n  const systemInfo = uni.getSystemInfoSync()\r\n  windowWidth = systemInfo.windowWidth\r\n  // #ifdef MP-WEIXIN\r\n  system = systemInfo.system\r\n}\r\n// #endif\r\nconst blankChar = makeMap(' ,\\r,\\n,\\t,\\f')\r\nlet idIndex = 0\r\n\r\n// #ifdef H5 || APP-PLUS\r\nconfig.ignoreTags.iframe = undefined\r\nconfig.trustTags.iframe = true\r\nconfig.ignoreTags.embed = undefined\r\nconfig.trustTags.embed = true\r\n// #endif\r\n// #ifdef APP-PLUS-NVUE\r\nconfig.ignoreTags.source = undefined\r\nconfig.ignoreTags.style = undefined\r\n// #endif\r\n\r\n/**\r\n * @description 创建 map\r\n * @param {String} str 逗号分隔\r\n */\r\nfunction makeMap (str) {\r\n  const map = Object.create(null)\r\n  const list = str.split(',')\r\n  for (let i = list.length; i--;) {\r\n    map[list[i]] = true\r\n  }\r\n  return map\r\n}\r\n\r\n/**\r\n * @description 解码 html 实体\r\n * @param {String} str 要解码的字符串\r\n * @param {Boolean} amp 要不要解码 &amp;\r\n * @returns {String} 解码后的字符串\r\n */\r\nfunction decodeEntity (str, amp) {\r\n  let i = str.indexOf('&')\r\n  while (i !== -1) {\r\n    const j = str.indexOf(';', i + 3)\r\n    let code\r\n    if (j === -1) break\r\n    if (str[i + 1] === '#') {\r\n      // &#123; 形式的实体\r\n      code = parseInt((str[i + 2] === 'x' ? '0' : '') + str.substring(i + 2, j))\r\n      if (!isNaN(code)) {\r\n        str = str.substr(0, i) + String.fromCharCode(code) + str.substr(j + 1)\r\n      }\r\n    } else {\r\n      // &nbsp; 形式的实体\r\n      code = str.substring(i + 1, j)\r\n      if (config.entities[code] || (code === 'amp' && amp)) {\r\n        str = str.substr(0, i) + (config.entities[code] || '&') + str.substr(j + 1)\r\n      }\r\n    }\r\n    i = str.indexOf('&', i + 1)\r\n  }\r\n  return str\r\n}\r\n\r\n/**\r\n * @description 合并多个块级标签，加快长内容渲染\r\n * @param {Array} nodes 要合并的标签数组\r\n */\r\nfunction mergeNodes (nodes) {\r\n  let i = nodes.length - 1\r\n  for (let j = i; j >= -1; j--) {\r\n    if (j === -1 || nodes[j].c || !nodes[j].name || (nodes[j].name !== 'div' && nodes[j].name !== 'p' && nodes[j].name[0] !== 'h') || (nodes[j].attrs.style || '').includes('inline')) {\r\n      if (i - j >= 5) {\r\n        nodes.splice(j + 1, i - j, {\r\n          name: 'div',\r\n          attrs: {},\r\n          children: nodes.slice(j + 1, i + 1)\r\n        })\r\n      }\r\n      i = j - 1\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * @description html 解析器\r\n * @param {Object} vm 组件实例\r\n */\r\nfunction Parser (vm) {\r\n  this.options = vm || {}\r\n  this.tagStyle = Object.assign({}, config.tagStyle, this.options.tagStyle)\r\n  this.imgList = vm.imgList || []\r\n  this.imgList._unloadimgs = 0\r\n  this.plugins = vm.plugins || []\r\n  this.attrs = Object.create(null)\r\n  this.stack = []\r\n  this.nodes = []\r\n  this.pre = (this.options.containerStyle || '').includes('white-space') && this.options.containerStyle.includes('pre') ? 2 : 0\r\n}\r\n\r\n/**\r\n * @description 执行解析\r\n * @param {String} content 要解析的文本\r\n */\r\nParser.prototype.parse = function (content) {\r\n  // 插件处理\r\n  for (let i = this.plugins.length; i--;) {\r\n    if (this.plugins[i].onUpdate) {\r\n      content = this.plugins[i].onUpdate(content, config) || content\r\n    }\r\n  }\r\n\r\n  new Lexer(this).parse(content)\r\n  // 出栈未闭合的标签\r\n  while (this.stack.length) {\r\n    this.popNode()\r\n  }\r\n  if (this.nodes.length > 50) {\r\n    mergeNodes(this.nodes)\r\n  }\r\n  return this.nodes\r\n}\r\n\r\n/**\r\n * @description 将标签暴露出来（不被 rich-text 包含）\r\n */\r\nParser.prototype.expose = function () {\r\n  // #ifndef APP-PLUS-NVUE\r\n  for (let i = this.stack.length; i--;) {\r\n    const item = this.stack[i]\r\n    if (item.c || item.name === 'a' || item.name === 'video' || item.name === 'audio') return\r\n    item.c = 1\r\n  }\r\n  // #endif\r\n}\r\n\r\n/**\r\n * @description 处理插件\r\n * @param {Object} node 要处理的标签\r\n * @returns {Boolean} 是否要移除此标签\r\n */\r\nParser.prototype.hook = function (node) {\r\n  for (let i = this.plugins.length; i--;) {\r\n    if (this.plugins[i].onParse && this.plugins[i].onParse(node, this) === false) {\r\n      return false\r\n    }\r\n  }\r\n  return true\r\n}\r\n\r\n/**\r\n * @description 将链接拼接上主域名\r\n * @param {String} url 需要拼接的链接\r\n * @returns {String} 拼接后的链接\r\n */\r\nParser.prototype.getUrl = function (url) {\r\n  const domain = this.options.domain\r\n  if (url[0] === '/') {\r\n    if (url[1] === '/') {\r\n      // // 开头的补充协议名\r\n      url = (domain ? domain.split('://')[0] : 'http') + ':' + url\r\n    } else if (domain) {\r\n      // 否则补充整个域名\r\n      url = domain + url\r\n    } /* #ifdef APP-PLUS */ else {\r\n      url = plus.io.convertLocalFileSystemURL(url)\r\n    } /* #endif */\r\n  } else if (!url.includes('data:') && !url.includes('://')) {\r\n    if (domain) {\r\n      url = domain + '/' + url\r\n    } /* #ifdef APP-PLUS */ else {\r\n      url = plus.io.convertLocalFileSystemURL(url)\r\n    } /* #endif */\r\n  }\r\n  return url\r\n}\r\n\r\n/**\r\n * @description 解析样式表\r\n * @param {Object} node 标签\r\n * @returns {Object}\r\n */\r\nParser.prototype.parseStyle = function (node) {\r\n  const attrs = node.attrs\r\n  const list = (this.tagStyle[node.name] || '').split(';').concat((attrs.style || '').split(';'))\r\n  const styleObj = {}\r\n  let tmp = ''\r\n\r\n  if (attrs.id && !this.xml) {\r\n    // 暴露锚点\r\n    if (this.options.useAnchor) {\r\n      this.expose()\r\n    } else if (node.name !== 'img' && node.name !== 'a' && node.name !== 'video' && node.name !== 'audio') {\r\n      attrs.id = undefined\r\n    }\r\n  }\r\n\r\n  // 转换 width 和 height 属性\r\n  if (attrs.width) {\r\n    styleObj.width = parseFloat(attrs.width) + (attrs.width.includes('%') ? '%' : 'px')\r\n    attrs.width = undefined\r\n  }\r\n  if (attrs.height) {\r\n    styleObj.height = parseFloat(attrs.height) + (attrs.height.includes('%') ? '%' : 'px')\r\n    attrs.height = undefined\r\n  }\r\n\r\n  for (let i = 0, len = list.length; i < len; i++) {\r\n    const info = list[i].split(':')\r\n    if (info.length < 2) continue\r\n    const key = info.shift().trim().toLowerCase()\r\n    let value = info.join(':').trim()\r\n    if ((value[0] === '-' && value.lastIndexOf('-') > 0) || value.includes('safe')) {\r\n      // 兼容性的 css 不压缩\r\n      tmp += `;${key}:${value}`\r\n    } else if (!styleObj[key] || value.includes('import') || !styleObj[key].includes('import')) {\r\n      // 重复的样式进行覆盖\r\n      if (value.includes('url')) {\r\n        // 填充链接\r\n        let j = value.indexOf('(') + 1\r\n        if (j) {\r\n          while (value[j] === '\"' || value[j] === \"'\" || blankChar[value[j]]) {\r\n            j++\r\n          }\r\n          value = value.substr(0, j) + this.getUrl(value.substr(j))\r\n        }\r\n      } else if (value.includes('rpx')) {\r\n        // 转换 rpx（rich-text 内部不支持 rpx）\r\n        value = value.replace(/[0-9.]+\\s*rpx/g, $ => parseFloat($) * windowWidth / 750 + 'px')\r\n      }\r\n      styleObj[key] = value\r\n    }\r\n  }\r\n\r\n  node.attrs.style = tmp\r\n  return styleObj\r\n}\r\n\r\n/**\r\n * @description 解析到标签名\r\n * @param {String} name 标签名\r\n * @private\r\n */\r\nParser.prototype.onTagName = function (name) {\r\n  this.tagName = this.xml ? name : name.toLowerCase()\r\n  if (this.tagName === 'svg') {\r\n    this.xml = (this.xml || 0) + 1 // svg 标签内大小写敏感\r\n    config.ignoreTags.style = undefined // svg 标签内 style 可用\r\n  }\r\n}\r\n\r\n/**\r\n * @description 解析到属性名\r\n * @param {String} name 属性名\r\n * @private\r\n */\r\nParser.prototype.onAttrName = function (name) {\r\n  name = this.xml ? name : name.toLowerCase()\r\n  // #ifdef (VUE3 && (H5 || APP-PLUS)) || APP-PLUS-NVUE\r\n  if (name.includes('?') || name.includes(';')) {\r\n    this.attrName = undefined\r\n    return\r\n  }\r\n  // #endif\r\n  if (name.substr(0, 5) === 'data-') {\r\n    if (name === 'data-src' && !this.attrs.src) {\r\n      // data-src 自动转为 src\r\n      this.attrName = 'src'\r\n    } else if (this.tagName === 'img' || this.tagName === 'a') {\r\n      // a 和 img 标签保留 data- 的属性，可以在 imgtap 和 linktap 事件中使用\r\n      this.attrName = name\r\n    } else {\r\n      // 剩余的移除以减小大小\r\n      this.attrName = undefined\r\n    }\r\n  } else {\r\n    this.attrName = name\r\n    this.attrs[name] = 'T' // boolean 型属性缺省设置\r\n  }\r\n}\r\n\r\n/**\r\n * @description 解析到属性值\r\n * @param {String} val 属性值\r\n * @private\r\n */\r\nParser.prototype.onAttrVal = function (val) {\r\n  const name = this.attrName || ''\r\n  if (name === 'style' || name === 'href') {\r\n    // 部分属性进行实体解码\r\n    this.attrs[name] = decodeEntity(val, true)\r\n  } else if (name.includes('src')) {\r\n    // 拼接主域名\r\n    this.attrs[name] = this.getUrl(decodeEntity(val, true))\r\n  } else if (name) {\r\n    this.attrs[name] = val\r\n  }\r\n}\r\n\r\n/**\r\n * @description 解析到标签开始\r\n * @param {Boolean} selfClose 是否有自闭合标识 />\r\n * @private\r\n */\r\nParser.prototype.onOpenTag = function (selfClose) {\r\n  // 拼装 node\r\n  const node = Object.create(null)\r\n  node.name = this.tagName\r\n  node.attrs = this.attrs\r\n  // 避免因为自动 diff 使得 type 被设置为 null 导致部分内容不显示\r\n  if (this.options.nodes.length) {\r\n    node.type = 'node'\r\n  }\r\n  this.attrs = Object.create(null)\r\n\r\n  const attrs = node.attrs\r\n  const parent = this.stack[this.stack.length - 1]\r\n  const siblings = parent ? parent.children : this.nodes\r\n  const close = this.xml ? selfClose : config.voidTags[node.name]\r\n\r\n  // 替换标签名选择器\r\n  if (tagSelector[node.name]) {\r\n    attrs.class = tagSelector[node.name] + (attrs.class ? ' ' + attrs.class : '')\r\n  }\r\n\r\n  // 转换 embed 标签\r\n  if (node.name === 'embed') {\r\n    // #ifndef H5 || APP-PLUS\r\n    const src = attrs.src || ''\r\n    // 按照后缀名和 type 将 embed 转为 video 或 audio\r\n    if (src.includes('.mp4') || src.includes('.3gp') || src.includes('.m3u8') || (attrs.type || '').includes('video')) {\r\n      node.name = 'video'\r\n    } else if (src.includes('.mp3') || src.includes('.wav') || src.includes('.aac') || src.includes('.m4a') || (attrs.type || '').includes('audio')) {\r\n      node.name = 'audio'\r\n    }\r\n    if (attrs.autostart) {\r\n      attrs.autoplay = 'T'\r\n    }\r\n    attrs.controls = 'T'\r\n    // #endif\r\n    // #ifdef H5 || APP-PLUS\r\n    this.expose()\r\n    // #endif\r\n  }\r\n\r\n  // #ifndef APP-PLUS-NVUE\r\n  // 处理音视频\r\n  if (node.name === 'video' || node.name === 'audio') {\r\n    // 设置 id 以便获取 context\r\n    if (node.name === 'video' && !attrs.id) {\r\n      attrs.id = 'v' + idIndex++\r\n    }\r\n    // 没有设置 controls 也没有设置 autoplay 的自动设置 controls\r\n    if (!attrs.controls && !attrs.autoplay) {\r\n      attrs.controls = 'T'\r\n    }\r\n    // 用数组存储所有可用的 source\r\n    node.src = []\r\n    if (attrs.src) {\r\n      node.src.push(attrs.src)\r\n      attrs.src = undefined\r\n    }\r\n    this.expose()\r\n  }\r\n  // #endif\r\n\r\n  // 处理自闭合标签\r\n  if (close) {\r\n    if (!this.hook(node) || config.ignoreTags[node.name]) {\r\n      // 通过 base 标签设置主域名\r\n      if (node.name === 'base' && !this.options.domain) {\r\n        this.options.domain = attrs.href\r\n      } /* #ifndef APP-PLUS-NVUE */ else if (node.name === 'source' && parent && (parent.name === 'video' || parent.name === 'audio') && attrs.src) {\r\n        // 设置 source 标签（仅父节点为 video 或 audio 时有效）\r\n        parent.src.push(attrs.src)\r\n      } /* #endif */\r\n      return\r\n    }\r\n\r\n    // 解析 style\r\n    const styleObj = this.parseStyle(node)\r\n\r\n    // 处理图片\r\n    if (node.name === 'img') {\r\n      if (attrs.src) {\r\n        // 标记 webp\r\n        if (attrs.src.includes('webp')) {\r\n          node.webp = 'T'\r\n        }\r\n        // data url 图片如果没有设置 original-src 默认为不可预览的小图片\r\n        if (attrs.src.includes('data:') && this.options.previewImg !== 'all' && !attrs['original-src']) {\r\n          attrs.ignore = 'T'\r\n        }\r\n        if (!attrs.ignore || node.webp || attrs.src.includes('cloud://')) {\r\n          for (let i = this.stack.length; i--;) {\r\n            const item = this.stack[i]\r\n            if (item.name === 'a') {\r\n              node.a = item.attrs\r\n            }\r\n            if (item.name === 'table' && !node.webp && !attrs.src.includes('cloud://')) {\r\n              if (!styleObj.display || styleObj.display.includes('inline')) {\r\n                node.t = 'inline-block'\r\n              } else {\r\n                node.t = styleObj.display\r\n              }\r\n              styleObj.display = undefined\r\n            }\r\n            // #ifndef H5 || APP-PLUS\r\n            const style = item.attrs.style || ''\r\n            if (style.includes('flex:') && !style.includes('flex:0') && !style.includes('flex: 0') && (!styleObj.width || parseInt(styleObj.width) > 100)) {\r\n              styleObj.width = '100% !important'\r\n              styleObj.height = ''\r\n              for (let j = i + 1; j < this.stack.length; j++) {\r\n                this.stack[j].attrs.style = (this.stack[j].attrs.style || '').replace('inline-', '')\r\n              }\r\n            } else if (style.includes('flex') && styleObj.width === '100%') {\r\n              for (let j = i + 1; j < this.stack.length; j++) {\r\n                const style = this.stack[j].attrs.style || ''\r\n                if (!style.includes(';width') && !style.includes(' width') && style.indexOf('width') !== 0) {\r\n                  styleObj.width = ''\r\n                  break\r\n                }\r\n              }\r\n            } else if (style.includes('inline-block')) {\r\n              if (styleObj.width && styleObj.width[styleObj.width.length - 1] === '%') {\r\n                item.attrs.style += ';max-width:' + styleObj.width\r\n                styleObj.width = ''\r\n              } else {\r\n                item.attrs.style += ';max-width:100%'\r\n              }\r\n            }\r\n            // #endif\r\n            item.c = 1\r\n          }\r\n          attrs.i = this.imgList.length.toString()\r\n          let src = attrs['original-src'] || attrs.src\r\n          // #ifndef H5 || MP-ALIPAY || APP-PLUS || MP-360\r\n          if (this.imgList.includes(src)) {\r\n            // 如果有重复的链接则对域名进行随机大小写变换避免预览时错位\r\n            let i = src.indexOf('://')\r\n            if (i !== -1) {\r\n              i += 3\r\n              let newSrc = src.substr(0, i)\r\n              for (; i < src.length; i++) {\r\n                if (src[i] === '/') break\r\n                newSrc += Math.random() > 0.5 ? src[i].toUpperCase() : src[i]\r\n              }\r\n              newSrc += src.substr(i)\r\n              src = newSrc\r\n            }\r\n          }\r\n          // #endif\r\n          this.imgList.push(src)\r\n          if (!node.t) {\r\n            this.imgList._unloadimgs += 1\r\n          }\r\n          // #ifdef H5 || APP-PLUS\r\n          if (this.options.lazyLoad) {\r\n            attrs['data-src'] = attrs.src\r\n            attrs.src = undefined\r\n          }\r\n          // #endif\r\n        }\r\n      }\r\n      if (styleObj.display === 'inline') {\r\n        styleObj.display = ''\r\n      }\r\n      // #ifndef APP-PLUS-NVUE\r\n      if (attrs.ignore) {\r\n        styleObj['max-width'] = styleObj['max-width'] || '100%'\r\n        attrs.style += ';-webkit-touch-callout:none'\r\n      }\r\n      // #endif\r\n      // 设置的宽度超出屏幕，为避免变形，高度转为自动\r\n      if (parseInt(styleObj.width) > windowWidth) {\r\n        styleObj.height = undefined\r\n      }\r\n      // 记录是否设置了宽高\r\n      if (!isNaN(parseInt(styleObj.width))) {\r\n        node.w = 'T'\r\n      }\r\n      if (!isNaN(parseInt(styleObj.height)) && (!styleObj.height.includes('%') || (parent && (parent.attrs.style || '').includes('height')))) {\r\n        node.h = 'T'\r\n      }\r\n      if (node.w && node.h && styleObj['object-fit']) {\r\n        if (styleObj['object-fit'] === 'contain') {\r\n          node.m = 'aspectFit'\r\n        } else if (styleObj['object-fit'] === 'cover') {\r\n          node.m = 'aspectFill'\r\n        }\r\n      }\r\n    } else if (node.name === 'svg') {\r\n      siblings.push(node)\r\n      this.stack.push(node)\r\n      this.popNode()\r\n      return\r\n    }\r\n    for (const key in styleObj) {\r\n      if (styleObj[key]) {\r\n        attrs.style += `;${key}:${styleObj[key].replace(' !important', '')}`\r\n      }\r\n    }\r\n    attrs.style = attrs.style.substr(1) || undefined\r\n    // #ifdef (MP-WEIXIN || MP-QQ) && VUE3\r\n    if (!attrs.style) {\r\n      delete attrs.style\r\n    }\r\n    // #endif\r\n  } else {\r\n    if ((node.name === 'pre' || ((attrs.style || '').includes('white-space') && attrs.style.includes('pre'))) && this.pre !== 2) {\r\n      this.pre = node.pre = 1\r\n    }\r\n    node.children = []\r\n    this.stack.push(node)\r\n  }\r\n\r\n  // 加入节点树\r\n  siblings.push(node)\r\n}\r\n\r\n/**\r\n * @description 解析到标签结束\r\n * @param {String} name 标签名\r\n * @private\r\n */\r\nParser.prototype.onCloseTag = function (name) {\r\n  // 依次出栈到匹配为止\r\n  name = this.xml ? name : name.toLowerCase()\r\n  let i\r\n  for (i = this.stack.length; i--;) {\r\n    if (this.stack[i].name === name) break\r\n  }\r\n  if (i !== -1) {\r\n    while (this.stack.length > i) {\r\n      this.popNode()\r\n    }\r\n  } else if (name === 'p' || name === 'br') {\r\n    const siblings = this.stack.length ? this.stack[this.stack.length - 1].children : this.nodes\r\n    siblings.push({\r\n      name,\r\n      attrs: {\r\n        class: tagSelector[name] || '',\r\n        style: this.tagStyle[name] || ''\r\n      }\r\n    })\r\n  }\r\n}\r\n\r\n/**\r\n * @description 处理标签出栈\r\n * @private\r\n */\r\nParser.prototype.popNode = function () {\r\n  const node = this.stack.pop()\r\n  let attrs = node.attrs\r\n  const children = node.children\r\n  const parent = this.stack[this.stack.length - 1]\r\n  const siblings = parent ? parent.children : this.nodes\r\n\r\n  if (!this.hook(node) || config.ignoreTags[node.name]) {\r\n    // 获取标题\r\n    if (node.name === 'title' && children.length && children[0].type === 'text' && this.options.setTitle) {\r\n      uni.setNavigationBarTitle({\r\n        title: children[0].text\r\n      })\r\n    }\r\n    siblings.pop()\r\n    return\r\n  }\r\n\r\n  if (node.pre && this.pre !== 2) {\r\n    // 是否合并空白符标识\r\n    this.pre = node.pre = undefined\r\n    for (let i = this.stack.length; i--;) {\r\n      if (this.stack[i].pre) {\r\n        this.pre = 1\r\n      }\r\n    }\r\n  }\r\n\r\n  const styleObj = {}\r\n\r\n  // 转换 svg\r\n  if (node.name === 'svg') {\r\n    if (this.xml > 1) {\r\n      // 多层 svg 嵌套\r\n      this.xml--\r\n      return\r\n    }\r\n    // #ifdef APP-PLUS-NVUE\r\n    (function traversal (node) {\r\n      if (node.name) {\r\n        // 调整 svg 的大小写\r\n        node.name = config.svgDict[node.name] || node.name\r\n        for (const item in node.attrs) {\r\n          if (config.svgDict[item]) {\r\n            node.attrs[config.svgDict[item]] = node.attrs[item]\r\n            node.attrs[item] = undefined\r\n          }\r\n        }\r\n        for (let i = 0; i < (node.children || []).length; i++) {\r\n          traversal(node.children[i])\r\n        }\r\n      }\r\n    })(node)\r\n    // #endif\r\n    // #ifndef APP-PLUS-NVUE\r\n    let src = ''\r\n    const style = attrs.style\r\n    attrs.style = ''\r\n    attrs.xmlns = 'http://www.w3.org/2000/svg';\r\n    (function traversal (node) {\r\n      if (node.type === 'text') {\r\n        src += node.text\r\n        return\r\n      }\r\n      const name = config.svgDict[node.name] || node.name\r\n      if (name === 'foreignObject') {\r\n        for (const child of (node.children || [])) {\r\n          if (child.attrs && !child.attrs.xmlns) {\r\n            child.attrs.xmlns = 'http://www.w3.org/1999/xhtml'\r\n            break\r\n          }\r\n        }\r\n      }\r\n      src += '<' + name\r\n      for (const item in node.attrs) {\r\n        const val = node.attrs[item]\r\n        if (val) {\r\n          src += ` ${config.svgDict[item] || item}=\"${val.replace(/\"/g, '')}\"`\r\n        }\r\n      }\r\n      if (!node.children) {\r\n        src += '/>'\r\n      } else {\r\n        src += '>'\r\n        for (let i = 0; i < node.children.length; i++) {\r\n          traversal(node.children[i])\r\n        }\r\n        src += '</' + name + '>'\r\n      }\r\n    })(node)\r\n    node.name = 'img'\r\n    node.attrs = {\r\n      src: 'data:image/svg+xml;utf8,' + src.replace(/#/g, '%23'),\r\n      style,\r\n      ignore: 'T'\r\n    }\r\n    node.children = undefined\r\n    // #endif\r\n    this.xml = false\r\n    config.ignoreTags.style = true\r\n    return\r\n  }\r\n\r\n  // #ifndef APP-PLUS-NVUE\r\n  // 转换 align 属性\r\n  if (attrs.align) {\r\n    if (node.name === 'table') {\r\n      if (attrs.align === 'center') {\r\n        styleObj['margin-inline-start'] = styleObj['margin-inline-end'] = 'auto'\r\n      } else {\r\n        styleObj.float = attrs.align\r\n      }\r\n    } else {\r\n      styleObj['text-align'] = attrs.align\r\n    }\r\n    attrs.align = undefined\r\n  }\r\n\r\n  // 转换 dir 属性\r\n  if (attrs.dir) {\r\n    styleObj.direction = attrs.dir\r\n    attrs.dir = undefined\r\n  }\r\n\r\n  // 转换 font 标签的属性\r\n  if (node.name === 'font') {\r\n    if (attrs.color) {\r\n      styleObj.color = attrs.color\r\n      attrs.color = undefined\r\n    }\r\n    if (attrs.face) {\r\n      styleObj['font-family'] = attrs.face\r\n      attrs.face = undefined\r\n    }\r\n    if (attrs.size) {\r\n      let size = parseInt(attrs.size)\r\n      if (!isNaN(size)) {\r\n        if (size < 1) {\r\n          size = 1\r\n        } else if (size > 7) {\r\n          size = 7\r\n        }\r\n        styleObj['font-size'] = ['x-small', 'small', 'medium', 'large', 'x-large', 'xx-large', 'xxx-large'][size - 1]\r\n      }\r\n      attrs.size = undefined\r\n    }\r\n  }\r\n  // #endif\r\n\r\n  // 一些编辑器的自带 class\r\n  if ((attrs.class || '').includes('align-center')) {\r\n    styleObj['text-align'] = 'center'\r\n  }\r\n\r\n  Object.assign(styleObj, this.parseStyle(node))\r\n\r\n  if (node.name !== 'table' && parseInt(styleObj.width) > windowWidth) {\r\n    styleObj['max-width'] = '100%'\r\n    styleObj['box-sizing'] = 'border-box'\r\n  }\r\n\r\n  // #ifndef APP-PLUS-NVUE\r\n  if (config.blockTags[node.name]) {\r\n    node.name = 'div'\r\n  } else if (!config.trustTags[node.name] && !this.xml) {\r\n    // 未知标签转为 span，避免无法显示\r\n    node.name = 'span'\r\n  }\r\n\r\n  if (node.name === 'a' || node.name === 'ad'\r\n    // #ifdef H5 || APP-PLUS\r\n    || node.name === 'iframe' // eslint-disable-line\r\n    // #endif\r\n  ) {\r\n    this.expose()\r\n  } else if (node.name === 'video') {\r\n    if ((styleObj.height || '').includes('auto')) {\r\n      styleObj.height = undefined\r\n    }\r\n    /* #ifdef APP-PLUS */\r\n    let str = '<video style=\"width:100%;height:100%\"'\r\n    for (const item in attrs) {\r\n      if (attrs[item]) {\r\n        str += ' ' + item + '=\"' + attrs[item] + '\"'\r\n      }\r\n    }\r\n    if (this.options.pauseVideo) {\r\n      str += ' onplay=\"this.dispatchEvent(new CustomEvent(\\'vplay\\',{bubbles:!0}));for(var e=document.getElementsByTagName(\\'video\\'),t=0;t<e.length;t++)e[t]!=this&&e[t].pause()\"'\r\n    }\r\n    str += '>'\r\n    for (let i = 0; i < node.src.length; i++) {\r\n      str += '<source src=\"' + node.src[i] + '\">'\r\n    }\r\n    str += '</video>'\r\n    node.html = str\r\n    /* #endif */\r\n  } else if ((node.name === 'ul' || node.name === 'ol') && node.c) {\r\n    // 列表处理\r\n    const types = {\r\n      a: 'lower-alpha',\r\n      A: 'upper-alpha',\r\n      i: 'lower-roman',\r\n      I: 'upper-roman'\r\n    }\r\n    if (types[attrs.type]) {\r\n      attrs.style += ';list-style-type:' + types[attrs.type]\r\n      attrs.type = undefined\r\n    }\r\n    for (let i = children.length; i--;) {\r\n      if (children[i].name === 'li') {\r\n        children[i].c = 1\r\n      }\r\n    }\r\n  } else if (node.name === 'table') {\r\n    // 表格处理\r\n    // cellpadding、cellspacing、border 这几个常用表格属性需要通过转换实现\r\n    let padding = parseFloat(attrs.cellpadding)\r\n    let spacing = parseFloat(attrs.cellspacing)\r\n    const border = parseFloat(attrs.border)\r\n    const bordercolor = styleObj['border-color']\r\n    const borderstyle = styleObj['border-style']\r\n    if (node.c) {\r\n      // padding 和 spacing 默认 2\r\n      if (isNaN(padding)) {\r\n        padding = 2\r\n      }\r\n      if (isNaN(spacing)) {\r\n        spacing = 2\r\n      }\r\n    }\r\n    if (border) {\r\n      attrs.style += `;border:${border}px ${borderstyle || 'solid'} ${bordercolor || 'gray'}`\r\n    }\r\n    if (node.flag && node.c) {\r\n      // 有 colspan 或 rowspan 且含有链接的表格通过 grid 布局实现\r\n      styleObj.display = 'grid'\r\n      if (styleObj['border-collapse'] === 'collapse') {\r\n        styleObj['border-collapse'] = undefined\r\n        spacing = 0\r\n      }\r\n      if (spacing) {\r\n        styleObj['grid-gap'] = spacing + 'px'\r\n        styleObj.padding = spacing + 'px'\r\n      } else if (border) {\r\n        // 无间隔的情况下避免边框重叠\r\n        attrs.style += ';border-left:0;border-top:0'\r\n      }\r\n\r\n      const width = [] // 表格的列宽\r\n      const trList = [] // tr 列表\r\n      const cells = [] // 保存新的单元格\r\n      const map = {}; // 被合并单元格占用的格子\r\n\r\n      (function traversal (nodes) {\r\n        for (let i = 0; i < nodes.length; i++) {\r\n          if (nodes[i].name === 'tr') {\r\n            trList.push(nodes[i])\r\n          } else if (nodes[i].name === 'colgroup') {\r\n            let colI = 1\r\n            for (const col of (nodes[i].children || [])) {\r\n              if (col.name === 'col') {\r\n                const style = col.attrs.style || ''\r\n                const start = style.indexOf('width') ? style.indexOf(';width') : 0\r\n                // 提取出宽度\r\n                if (start !== -1) {\r\n                  let end = style.indexOf(';', start + 6)\r\n                  if (end === -1) {\r\n                    end = style.length\r\n                  }\r\n                  width[colI] = style.substring(start ? start + 7 : 6, end)\r\n                }\r\n                colI += 1\r\n              }\r\n            }\r\n          } else {\r\n            traversal(nodes[i].children || [])\r\n          }\r\n        }\r\n      })(children)\r\n\r\n      for (let row = 1; row <= trList.length; row++) {\r\n        let col = 1\r\n        for (let j = 0; j < trList[row - 1].children.length; j++) {\r\n          const td = trList[row - 1].children[j]\r\n          if (td.name === 'td' || td.name === 'th') {\r\n            // 这个格子被上面的单元格占用，则列号++\r\n            while (map[row + '.' + col]) {\r\n              col++\r\n            }\r\n            let style = td.attrs.style || ''\r\n            let start = style.indexOf('width') ? style.indexOf(';width') : 0\r\n            // 提取出 td 的宽度\r\n            if (start !== -1) {\r\n              let end = style.indexOf(';', start + 6)\r\n              if (end === -1) {\r\n                end = style.length\r\n              }\r\n              if (!td.attrs.colspan) {\r\n                width[col] = style.substring(start ? start + 7 : 6, end)\r\n              }\r\n              style = style.substr(0, start) + style.substr(end)\r\n            }\r\n            // 设置竖直对齐\r\n            style += ';display:flex'\r\n            start = style.indexOf('vertical-align')\r\n            if (start !== -1) {\r\n              const val = style.substr(start + 15, 10)\r\n              if (val.includes('middle')) {\r\n                style += ';align-items:center'\r\n              } else if (val.includes('bottom')) {\r\n                style += ';align-items:flex-end'\r\n              }\r\n            } else {\r\n              style += ';align-items:center'\r\n            }\r\n            // 设置水平对齐\r\n            start = style.indexOf('text-align')\r\n            if (start !== -1) {\r\n              const val = style.substr(start + 11, 10)\r\n              if (val.includes('center')) {\r\n                style += ';justify-content: center'\r\n              } else if (val.includes('right')) {\r\n                style += ';justify-content: right'\r\n              }\r\n            }\r\n            style = (border ? `;border:${border}px ${borderstyle || 'solid'} ${bordercolor || 'gray'}` + (spacing ? '' : ';border-right:0;border-bottom:0') : '') + (padding ? `;padding:${padding}px` : '') + ';' + style\r\n            // 处理列合并\r\n            if (td.attrs.colspan) {\r\n              style += `;grid-column-start:${col};grid-column-end:${col + parseInt(td.attrs.colspan)}`\r\n              if (!td.attrs.rowspan) {\r\n                style += `;grid-row-start:${row};grid-row-end:${row + 1}`\r\n              }\r\n              col += parseInt(td.attrs.colspan) - 1\r\n            }\r\n            // 处理行合并\r\n            if (td.attrs.rowspan) {\r\n              style += `;grid-row-start:${row};grid-row-end:${row + parseInt(td.attrs.rowspan)}`\r\n              if (!td.attrs.colspan) {\r\n                style += `;grid-column-start:${col};grid-column-end:${col + 1}`\r\n              }\r\n              // 记录下方单元格被占用\r\n              for (let rowspan = 1; rowspan < td.attrs.rowspan; rowspan++) {\r\n                for (let colspan = 0; colspan < (td.attrs.colspan || 1); colspan++) {\r\n                  map[(row + rowspan) + '.' + (col - colspan)] = 1\r\n                }\r\n              }\r\n            }\r\n            if (style) {\r\n              td.attrs.style = style\r\n            }\r\n            cells.push(td)\r\n            col++\r\n          }\r\n        }\r\n        if (row === 1) {\r\n          let temp = ''\r\n          for (let i = 1; i < col; i++) {\r\n            temp += (width[i] ? width[i] : 'auto') + ' '\r\n          }\r\n          styleObj['grid-template-columns'] = temp\r\n        }\r\n      }\r\n      node.children = cells\r\n    } else {\r\n      // 没有使用合并单元格的表格通过 table 布局实现\r\n      if (node.c) {\r\n        styleObj.display = 'table'\r\n      }\r\n      if (!isNaN(spacing)) {\r\n        styleObj['border-spacing'] = spacing + 'px'\r\n      }\r\n      if (border || padding) {\r\n        // 遍历\r\n        (function traversal (nodes) {\r\n          for (let i = 0; i < nodes.length; i++) {\r\n            const td = nodes[i]\r\n            if (td.name === 'th' || td.name === 'td') {\r\n              if (border) {\r\n                td.attrs.style = `border:${border}px ${borderstyle || 'solid'} ${bordercolor || 'gray'};${td.attrs.style || ''}`\r\n              }\r\n              if (padding) {\r\n                td.attrs.style = `padding:${padding}px;${td.attrs.style || ''}`\r\n              }\r\n            } else if (td.children) {\r\n              traversal(td.children)\r\n            }\r\n          }\r\n        })(children)\r\n      }\r\n    }\r\n    // 给表格添加一个单独的横向滚动层\r\n    if (this.options.scrollTable && !(attrs.style || '').includes('inline')) {\r\n      const table = Object.assign({}, node)\r\n      node.name = 'div'\r\n      node.attrs = {\r\n        style: 'overflow:auto'\r\n      }\r\n      node.children = [table]\r\n      attrs = table.attrs\r\n    }\r\n  } else if ((node.name === 'tbody' || node.name === 'tr') && node.flag && node.c) {\r\n    node.flag = undefined;\r\n    (function traversal (nodes) {\r\n      for (let i = 0; i < nodes.length; i++) {\r\n        if (nodes[i].name === 'td') {\r\n          // 颜色样式设置给单元格避免丢失\r\n          for (const style of ['color', 'background', 'background-color']) {\r\n            if (styleObj[style]) {\r\n              nodes[i].attrs.style = style + ':' + styleObj[style] + ';' + (nodes[i].attrs.style || '')\r\n            }\r\n          }\r\n        } else {\r\n          traversal(nodes[i].children || [])\r\n        }\r\n      }\r\n    })(children)\r\n  } else if ((node.name === 'td' || node.name === 'th') && (attrs.colspan || attrs.rowspan)) {\r\n    for (let i = this.stack.length; i--;) {\r\n      if (this.stack[i].name === 'table' || this.stack[i].name === 'tbody' || this.stack[i].name === 'tr') {\r\n        this.stack[i].flag = 1 // 指示含有合并单元格\r\n      }\r\n    }\r\n  } else if (node.name === 'ruby') {\r\n    // 转换 ruby\r\n    node.name = 'span'\r\n    for (let i = 0; i < children.length - 1; i++) {\r\n      if (children[i].type === 'text' && children[i + 1].name === 'rt') {\r\n        children[i] = {\r\n          name: 'div',\r\n          attrs: {\r\n            style: 'display:inline-block;text-align:center'\r\n          },\r\n          children: [{\r\n            name: 'div',\r\n            attrs: {\r\n              style: 'font-size:50%;' + (children[i + 1].attrs.style || '')\r\n            },\r\n            children: children[i + 1].children\r\n          }, children[i]]\r\n        }\r\n        children.splice(i + 1, 1)\r\n      }\r\n    }\r\n  } else if (node.c) {\r\n    (function traversal (node) {\r\n      node.c = 2\r\n      for (let i = node.children.length; i--;) {\r\n        const child = node.children[i]\r\n        // #ifdef (MP-WEIXIN || MP-QQ || APP-PLUS || MP-360) && VUE3\r\n        if (child.name && (config.inlineTags[child.name] || ((child.attrs.style || '').includes('inline') && child.children)) && !child.c) {\r\n          traversal(child)\r\n        }\r\n        // #endif\r\n        if (!child.c || child.name === 'table') {\r\n          node.c = 1\r\n        }\r\n      }\r\n    })(node)\r\n  }\r\n\r\n  if ((styleObj.display || '').includes('flex') && !node.c) {\r\n    for (let i = children.length; i--;) {\r\n      const item = children[i]\r\n      if (item.f) {\r\n        item.attrs.style = (item.attrs.style || '') + item.f\r\n        item.f = undefined\r\n      }\r\n    }\r\n  }\r\n  // flex 布局时部分样式需要提取到 rich-text 外层\r\n  const flex = parent && ((parent.attrs.style || '').includes('flex') || (parent.attrs.style || '').includes('grid'))\r\n    // #ifdef MP-WEIXIN\r\n    // 检查基础库版本 virtualHost 是否可用\r\n    && !(node.c && wx.getNFCAdapter) // eslint-disable-line\r\n    // #endif\r\n    // #ifndef MP-WEIXIN || MP-QQ || MP-BAIDU || MP-TOUTIAO\r\n    && !node.c // eslint-disable-line\r\n  // #endif\r\n  if (flex) {\r\n    node.f = ';max-width:100%'\r\n  }\r\n\r\n  if (children.length >= 50 && node.c && !(styleObj.display || '').includes('flex')) {\r\n    mergeNodes(children)\r\n  }\r\n  // #endif\r\n\r\n  for (const key in styleObj) {\r\n    if (styleObj[key]) {\r\n      const val = `;${key}:${styleObj[key].replace(' !important', '')}`\r\n      /* #ifndef APP-PLUS-NVUE */\r\n      if (flex && ((key.includes('flex') && key !== 'flex-direction') || key === 'align-self' || key.includes('grid') || styleObj[key][0] === '-' || (key.includes('width') && val.includes('%')))) {\r\n        node.f += val\r\n        if (key === 'width') {\r\n          attrs.style += ';width:100%'\r\n        }\r\n      } else /* #endif */ {\r\n        attrs.style += val\r\n      }\r\n    }\r\n  }\r\n  attrs.style = attrs.style.substr(1) || undefined\r\n  // #ifdef (MP-WEIXIN || MP-QQ) && VUE3\r\n  for (const key in attrs) {\r\n    if (!attrs[key]) {\r\n      delete attrs[key]\r\n    }\r\n  }\r\n  // #endif\r\n}\r\n\r\n/**\r\n * @description 解析到文本\r\n * @param {String} text 文本内容\r\n */\r\nParser.prototype.onText = function (text) {\r\n  if (!this.pre) {\r\n    // 合并空白符\r\n    let trim = ''\r\n    let flag\r\n    for (let i = 0, len = text.length; i < len; i++) {\r\n      if (!blankChar[text[i]]) {\r\n        trim += text[i]\r\n      } else {\r\n        if (trim[trim.length - 1] !== ' ') {\r\n          trim += ' '\r\n        }\r\n        if (text[i] === '\\n' && !flag) {\r\n          flag = true\r\n        }\r\n      }\r\n    }\r\n    // 去除含有换行符的空串\r\n    if (trim === ' ') {\r\n      if (flag) return\r\n      // #ifdef VUE3\r\n      else {\r\n        const parent = this.stack[this.stack.length - 1]\r\n        if (parent && parent.name[0] === 't') return\r\n      }\r\n      // #endif\r\n    }\r\n    text = trim\r\n  }\r\n  const node = Object.create(null)\r\n  node.type = 'text'\r\n  // #ifdef (MP-BAIDU || MP-ALIPAY || MP-TOUTIAO) && VUE3\r\n  node.attrs = {}\r\n  // #endif\r\n  node.text = decodeEntity(text)\r\n  if (this.hook(node)) {\r\n    // #ifdef MP-WEIXIN\r\n    if (this.options.selectable === 'force' && system.includes('iOS') && !uni.canIUse('rich-text.user-select')) {\r\n      this.expose()\r\n    }\r\n    // #endif\r\n    const siblings = this.stack.length ? this.stack[this.stack.length - 1].children : this.nodes\r\n    siblings.push(node)\r\n  }\r\n}\r\n\r\n/**\r\n * @description html 词法分析器\r\n * @param {Object} handler 高层处理器\r\n */\r\nfunction Lexer (handler) {\r\n  this.handler = handler\r\n}\r\n\r\n/**\r\n * @description 执行解析\r\n * @param {String} content 要解析的文本\r\n */\r\nLexer.prototype.parse = function (content) {\r\n  this.content = content || ''\r\n  this.i = 0 // 标记解析位置\r\n  this.start = 0 // 标记一个单词的开始位置\r\n  this.state = this.text // 当前状态\r\n  for (let len = this.content.length; this.i !== -1 && this.i < len;) {\r\n    this.state()\r\n  }\r\n}\r\n\r\n/**\r\n * @description 检查标签是否闭合\r\n * @param {String} method 如果闭合要进行的操作\r\n * @returns {Boolean} 是否闭合\r\n * @private\r\n */\r\nLexer.prototype.checkClose = function (method) {\r\n  const selfClose = this.content[this.i] === '/'\r\n  if (this.content[this.i] === '>' || (selfClose && this.content[this.i + 1] === '>')) {\r\n    if (method) {\r\n      this.handler[method](this.content.substring(this.start, this.i))\r\n    }\r\n    this.i += selfClose ? 2 : 1\r\n    this.start = this.i\r\n    this.handler.onOpenTag(selfClose)\r\n    if (this.handler.tagName === 'script') {\r\n      this.i = this.content.indexOf('</', this.i)\r\n      if (this.i !== -1) {\r\n        this.i += 2\r\n        this.start = this.i\r\n      }\r\n      this.state = this.endTag\r\n    } else {\r\n      this.state = this.text\r\n    }\r\n    return true\r\n  }\r\n  return false\r\n}\r\n\r\n/**\r\n * @description 文本状态\r\n * @private\r\n */\r\nLexer.prototype.text = function () {\r\n  this.i = this.content.indexOf('<', this.i) // 查找最近的标签\r\n  if (this.i === -1) {\r\n    // 没有标签了\r\n    if (this.start < this.content.length) {\r\n      this.handler.onText(this.content.substring(this.start, this.content.length))\r\n    }\r\n    return\r\n  }\r\n  const c = this.content[this.i + 1]\r\n  if ((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z')) {\r\n    // 标签开头\r\n    if (this.start !== this.i) {\r\n      this.handler.onText(this.content.substring(this.start, this.i))\r\n    }\r\n    this.start = ++this.i\r\n    this.state = this.tagName\r\n  } else if (c === '/' || c === '!' || c === '?') {\r\n    if (this.start !== this.i) {\r\n      this.handler.onText(this.content.substring(this.start, this.i))\r\n    }\r\n    const next = this.content[this.i + 2]\r\n    if (c === '/' && ((next >= 'a' && next <= 'z') || (next >= 'A' && next <= 'Z'))) {\r\n      // 标签结尾\r\n      this.i += 2\r\n      this.start = this.i\r\n      this.state = this.endTag\r\n      return\r\n    }\r\n    // 处理注释\r\n    let end = '-->'\r\n    if (c !== '!' || this.content[this.i + 2] !== '-' || this.content[this.i + 3] !== '-') {\r\n      end = '>'\r\n    }\r\n    this.i = this.content.indexOf(end, this.i)\r\n    if (this.i !== -1) {\r\n      this.i += end.length\r\n      this.start = this.i\r\n    }\r\n  } else {\r\n    this.i++\r\n  }\r\n}\r\n\r\n/**\r\n * @description 标签名状态\r\n * @private\r\n */\r\nLexer.prototype.tagName = function () {\r\n  if (blankChar[this.content[this.i]]) {\r\n    // 解析到标签名\r\n    this.handler.onTagName(this.content.substring(this.start, this.i))\r\n    while (blankChar[this.content[++this.i]]);\r\n    if (this.i < this.content.length && !this.checkClose()) {\r\n      this.start = this.i\r\n      this.state = this.attrName\r\n    }\r\n  } else if (!this.checkClose('onTagName')) {\r\n    this.i++\r\n  }\r\n}\r\n\r\n/**\r\n * @description 属性名状态\r\n * @private\r\n */\r\nLexer.prototype.attrName = function () {\r\n  let c = this.content[this.i]\r\n  if (blankChar[c] || c === '=') {\r\n    // 解析到属性名\r\n    this.handler.onAttrName(this.content.substring(this.start, this.i))\r\n    let needVal = c === '='\r\n    const len = this.content.length\r\n    while (++this.i < len) {\r\n      c = this.content[this.i]\r\n      if (!blankChar[c]) {\r\n        if (this.checkClose()) return\r\n        if (needVal) {\r\n          // 等号后遇到第一个非空字符\r\n          this.start = this.i\r\n          this.state = this.attrVal\r\n          return\r\n        }\r\n        if (this.content[this.i] === '=') {\r\n          needVal = true\r\n        } else {\r\n          this.start = this.i\r\n          this.state = this.attrName\r\n          return\r\n        }\r\n      }\r\n    }\r\n  } else if (!this.checkClose('onAttrName')) {\r\n    this.i++\r\n  }\r\n}\r\n\r\n/**\r\n * @description 属性值状态\r\n * @private\r\n */\r\nLexer.prototype.attrVal = function () {\r\n  const c = this.content[this.i]\r\n  const len = this.content.length\r\n  if (c === '\"' || c === \"'\") {\r\n    // 有冒号的属性\r\n    this.start = ++this.i\r\n    this.i = this.content.indexOf(c, this.i)\r\n    if (this.i === -1) return\r\n    this.handler.onAttrVal(this.content.substring(this.start, this.i))\r\n  } else {\r\n    // 没有冒号的属性\r\n    for (; this.i < len; this.i++) {\r\n      if (blankChar[this.content[this.i]]) {\r\n        this.handler.onAttrVal(this.content.substring(this.start, this.i))\r\n        break\r\n      } else if (this.checkClose('onAttrVal')) return\r\n    }\r\n  }\r\n  while (blankChar[this.content[++this.i]]);\r\n  if (this.i < len && !this.checkClose()) {\r\n    this.start = this.i\r\n    this.state = this.attrName\r\n  }\r\n}\r\n\r\n/**\r\n * @description 结束标签状态\r\n * @returns {String} 结束的标签名\r\n * @private\r\n */\r\nLexer.prototype.endTag = function () {\r\n  const c = this.content[this.i]\r\n  if (blankChar[c] || c === '>' || c === '/') {\r\n    this.handler.onCloseTag(this.content.substring(this.start, this.i))\r\n    if (c !== '>') {\r\n      this.i = this.content.indexOf('>', this.i)\r\n      if (this.i === -1) return\r\n    }\r\n    this.start = ++this.i\r\n    this.state = this.text\r\n  } else {\r\n    this.i++\r\n  }\r\n}\r\n\r\nexport default Parser\r\n"], "names": ["uni", "style", "node", "wx"], "mappings": ";;AAKA,MAAM,SAAS;AAAA;AAAA,EAEb,WAAW,QAAQ,yNAAyN;AAAA;AAAA,EAG5O,WAAW,QAAQ,mFAAmF;AAAA;AAAA,EAItG,YAAY,QAAQ,gEAAgE;AAAA;AAAA,EAIpF,YAAY,QAAQ,oHAAoH;AAAA;AAAA,EAGxI,UAAU,QAAQ,sHAAsH;AAAA;AAAA,EAGxI,UAAU;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACP;AAAA;AAAA,EAGD,UAAU;AAAA,IAER,SAAS;AAAA,IACT,KAAK;AAAA,IACL,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,KAAK;AAAA,IACL,GAAG;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,GAAG;AAAA,EAEJ;AAAA;AAAA,EAGD,SAAS;AAAA,IACP,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,eAAe;AAAA,EAChB;AACH;AACA,MAAM,cAAY,CAAE;AACpB,IAAI,aAAa;AAEjB,IAAIA,oBAAI,QAAQ,eAAe,GAAG;AAChC,gBAAcA,cAAAA,MAAI,cAAa,EAAG;AAClC,WAASA,cAAAA,MAAI,cAAa,EAAG;AAC/B,OAAO;AAEL,QAAM,aAAaA,cAAG,MAAC,kBAAmB;AAC1C,gBAAc,WAAW;AAEzB,WAAS,WAAW;AACtB;AAEA,MAAM,YAAY,QAAQ,cAAe;AACzC,IAAI,UAAU;AAiBd,SAAS,QAAS,KAAK;AACrB,QAAM,MAAM,uBAAO,OAAO,IAAI;AAC9B,QAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,WAAS,IAAI,KAAK,QAAQ,OAAM;AAC9B,QAAI,KAAK,CAAC,CAAC,IAAI;AAAA,EAChB;AACD,SAAO;AACT;AAQA,SAAS,aAAc,KAAK,KAAK;AAC/B,MAAI,IAAI,IAAI,QAAQ,GAAG;AACvB,SAAO,MAAM,IAAI;AACf,UAAM,IAAI,IAAI,QAAQ,KAAK,IAAI,CAAC;AAChC,QAAI;AACJ,QAAI,MAAM;AAAI;AACd,QAAI,IAAI,IAAI,CAAC,MAAM,KAAK;AAEtB,aAAO,UAAU,IAAI,IAAI,CAAC,MAAM,MAAM,MAAM,MAAM,IAAI,UAAU,IAAI,GAAG,CAAC,CAAC;AACzE,UAAI,CAAC,MAAM,IAAI,GAAG;AAChB,cAAM,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,aAAa,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;AAAA,MACtE;AAAA,IACP,OAAW;AAEL,aAAO,IAAI,UAAU,IAAI,GAAG,CAAC;AAC7B,UAAI,OAAO,SAAS,IAAI,KAAM,SAAS,SAAS,KAAM;AACpD,cAAM,IAAI,OAAO,GAAG,CAAC,KAAK,OAAO,SAAS,IAAI,KAAK,OAAO,IAAI,OAAO,IAAI,CAAC;AAAA,MAC3E;AAAA,IACF;AACD,QAAI,IAAI,QAAQ,KAAK,IAAI,CAAC;AAAA,EAC3B;AACD,SAAO;AACT;AAMA,SAAS,WAAY,OAAO;AAC1B,MAAI,IAAI,MAAM,SAAS;AACvB,WAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC5B,QAAI,MAAM,MAAM,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,QAAS,MAAM,CAAC,EAAE,SAAS,SAAS,MAAM,CAAC,EAAE,SAAS,OAAO,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,QAAS,MAAM,CAAC,EAAE,MAAM,SAAS,IAAI,SAAS,QAAQ,GAAG;AACjL,UAAI,IAAI,KAAK,GAAG;AACd,cAAM,OAAO,IAAI,GAAG,IAAI,GAAG;AAAA,UACzB,MAAM;AAAA,UACN,OAAO,CAAE;AAAA,UACT,UAAU,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC;AAAA,QAC5C,CAAS;AAAA,MACF;AACD,UAAI,IAAI;AAAA,IACT;AAAA,EACF;AACH;AAMA,SAAS,OAAQ,IAAI;AACnB,OAAK,UAAU,MAAM,CAAE;AACvB,OAAK,WAAW,OAAO,OAAO,CAAA,GAAI,OAAO,UAAU,KAAK,QAAQ,QAAQ;AACxE,OAAK,UAAU,GAAG,WAAW,CAAE;AAC/B,OAAK,QAAQ,cAAc;AAC3B,OAAK,UAAU,GAAG,WAAW,CAAE;AAC/B,OAAK,QAAQ,uBAAO,OAAO,IAAI;AAC/B,OAAK,QAAQ,CAAE;AACf,OAAK,QAAQ,CAAE;AACf,OAAK,OAAO,KAAK,QAAQ,kBAAkB,IAAI,SAAS,aAAa,KAAK,KAAK,QAAQ,eAAe,SAAS,KAAK,IAAI,IAAI;AAC9H;AAMA,OAAO,UAAU,QAAQ,SAAU,SAAS;AAE1C,WAAS,IAAI,KAAK,QAAQ,QAAQ,OAAM;AACtC,QAAI,KAAK,QAAQ,CAAC,EAAE,UAAU;AAC5B,gBAAU,KAAK,QAAQ,CAAC,EAAE,SAAS,SAAS,MAAM,KAAK;AAAA,IACxD;AAAA,EACF;AAED,MAAI,MAAM,IAAI,EAAE,MAAM,OAAO;AAE7B,SAAO,KAAK,MAAM,QAAQ;AACxB,SAAK,QAAS;AAAA,EACf;AACD,MAAI,KAAK,MAAM,SAAS,IAAI;AAC1B,eAAW,KAAK,KAAK;AAAA,EACtB;AACD,SAAO,KAAK;AACd;AAKA,OAAO,UAAU,SAAS,WAAY;AAEpC,WAAS,IAAI,KAAK,MAAM,QAAQ,OAAM;AACpC,UAAM,OAAO,KAAK,MAAM,CAAC;AACzB,QAAI,KAAK,KAAK,KAAK,SAAS,OAAO,KAAK,SAAS,WAAW,KAAK,SAAS;AAAS;AACnF,SAAK,IAAI;AAAA,EACV;AAEH;AAOA,OAAO,UAAU,OAAO,SAAU,MAAM;AACtC,WAAS,IAAI,KAAK,QAAQ,QAAQ,OAAM;AACtC,QAAI,KAAK,QAAQ,CAAC,EAAE,WAAW,KAAK,QAAQ,CAAC,EAAE,QAAQ,MAAM,IAAI,MAAM,OAAO;AAC5E,aAAO;AAAA,IACR;AAAA,EACF;AACD,SAAO;AACT;AAOA,OAAO,UAAU,SAAS,SAAU,KAAK;AACvC,QAAM,SAAS,KAAK,QAAQ;AAC5B,MAAI,IAAI,CAAC,MAAM,KAAK;AAClB,QAAI,IAAI,CAAC,MAAM,KAAK;AAElB,aAAO,SAAS,OAAO,MAAM,KAAK,EAAE,CAAC,IAAI,UAAU,MAAM;AAAA,IAC1D,WAAU,QAAQ;AAEjB,YAAM,SAAS;AAAA,IAChB;AAAA,EAGL,WAAa,CAAC,IAAI,SAAS,OAAO,KAAK,CAAC,IAAI,SAAS,KAAK,GAAG;AACzD,QAAI,QAAQ;AACV,YAAM,SAAS,MAAM;AAAA,IACtB;AAAA,EAGF;AACD,SAAO;AACT;AAOA,OAAO,UAAU,aAAa,SAAU,MAAM;AAC5C,QAAM,QAAQ,KAAK;AACnB,QAAM,QAAQ,KAAK,SAAS,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,EAAE,QAAQ,MAAM,SAAS,IAAI,MAAM,GAAG,CAAC;AAC9F,QAAM,WAAW,CAAE;AACnB,MAAI,MAAM;AAEV,MAAI,MAAM,MAAM,CAAC,KAAK,KAAK;AAEzB,QAAI,KAAK,QAAQ,WAAW;AAC1B,WAAK,OAAQ;AAAA,IACd,WAAU,KAAK,SAAS,SAAS,KAAK,SAAS,OAAO,KAAK,SAAS,WAAW,KAAK,SAAS,SAAS;AACrG,YAAM,KAAK;AAAA,IACZ;AAAA,EACF;AAGD,MAAI,MAAM,OAAO;AACf,aAAS,QAAQ,WAAW,MAAM,KAAK,KAAK,MAAM,MAAM,SAAS,GAAG,IAAI,MAAM;AAC9E,UAAM,QAAQ;AAAA,EACf;AACD,MAAI,MAAM,QAAQ;AAChB,aAAS,SAAS,WAAW,MAAM,MAAM,KAAK,MAAM,OAAO,SAAS,GAAG,IAAI,MAAM;AACjF,UAAM,SAAS;AAAA,EAChB;AAED,WAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAC/C,UAAM,OAAO,KAAK,CAAC,EAAE,MAAM,GAAG;AAC9B,QAAI,KAAK,SAAS;AAAG;AACrB,UAAM,MAAM,KAAK,MAAO,EAAC,KAAI,EAAG,YAAa;AAC7C,QAAI,QAAQ,KAAK,KAAK,GAAG,EAAE,KAAM;AACjC,QAAK,MAAM,CAAC,MAAM,OAAO,MAAM,YAAY,GAAG,IAAI,KAAM,MAAM,SAAS,MAAM,GAAG;AAE9E,aAAO,IAAI,GAAG,IAAI,KAAK;AAAA,IACxB,WAAU,CAAC,SAAS,GAAG,KAAK,MAAM,SAAS,QAAQ,KAAK,CAAC,SAAS,GAAG,EAAE,SAAS,QAAQ,GAAG;AAE1F,UAAI,MAAM,SAAS,KAAK,GAAG;AAEzB,YAAI,IAAI,MAAM,QAAQ,GAAG,IAAI;AAC7B,YAAI,GAAG;AACL,iBAAO,MAAM,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM,OAAO,UAAU,MAAM,CAAC,CAAC,GAAG;AAClE;AAAA,UACD;AACD,kBAAQ,MAAM,OAAO,GAAG,CAAC,IAAI,KAAK,OAAO,MAAM,OAAO,CAAC,CAAC;AAAA,QACzD;AAAA,MACF,WAAU,MAAM,SAAS,KAAK,GAAG;AAEhC,gBAAQ,MAAM,QAAQ,kBAAkB,OAAK,WAAW,CAAC,IAAI,cAAc,MAAM,IAAI;AAAA,MACtF;AACD,eAAS,GAAG,IAAI;AAAA,IACjB;AAAA,EACF;AAED,OAAK,MAAM,QAAQ;AACnB,SAAO;AACT;AAOA,OAAO,UAAU,YAAY,SAAU,MAAM;AAC3C,OAAK,UAAU,KAAK,MAAM,OAAO,KAAK,YAAa;AACnD,MAAI,KAAK,YAAY,OAAO;AAC1B,SAAK,OAAO,KAAK,OAAO,KAAK;AAC7B,WAAO,WAAW,QAAQ;AAAA,EAC3B;AACH;AAOA,OAAO,UAAU,aAAa,SAAU,MAAM;AAC5C,SAAO,KAAK,MAAM,OAAO,KAAK,YAAa;AAO3C,MAAI,KAAK,OAAO,GAAG,CAAC,MAAM,SAAS;AACjC,QAAI,SAAS,cAAc,CAAC,KAAK,MAAM,KAAK;AAE1C,WAAK,WAAW;AAAA,IACtB,WAAe,KAAK,YAAY,SAAS,KAAK,YAAY,KAAK;AAEzD,WAAK,WAAW;AAAA,IACtB,OAAW;AAEL,WAAK,WAAW;AAAA,IACjB;AAAA,EACL,OAAS;AACL,SAAK,WAAW;AAChB,SAAK,MAAM,IAAI,IAAI;AAAA,EACpB;AACH;AAOA,OAAO,UAAU,YAAY,SAAU,KAAK;AAC1C,QAAM,OAAO,KAAK,YAAY;AAC9B,MAAI,SAAS,WAAW,SAAS,QAAQ;AAEvC,SAAK,MAAM,IAAI,IAAI,aAAa,KAAK,IAAI;AAAA,EAC1C,WAAU,KAAK,SAAS,KAAK,GAAG;AAE/B,SAAK,MAAM,IAAI,IAAI,KAAK,OAAO,aAAa,KAAK,IAAI,CAAC;AAAA,EACvD,WAAU,MAAM;AACf,SAAK,MAAM,IAAI,IAAI;AAAA,EACpB;AACH;AAOA,OAAO,UAAU,YAAY,SAAU,WAAW;AAEhD,QAAM,OAAO,uBAAO,OAAO,IAAI;AAC/B,OAAK,OAAO,KAAK;AACjB,OAAK,QAAQ,KAAK;AAElB,MAAI,KAAK,QAAQ,MAAM,QAAQ;AAC7B,SAAK,OAAO;AAAA,EACb;AACD,OAAK,QAAQ,uBAAO,OAAO,IAAI;AAE/B,QAAM,QAAQ,KAAK;AACnB,QAAM,SAAS,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC/C,QAAM,WAAW,SAAS,OAAO,WAAW,KAAK;AACjD,QAAM,QAAQ,KAAK,MAAM,YAAY,OAAO,SAAS,KAAK,IAAI;AAG9D,MAAI,YAAY,KAAK,IAAI,GAAG;AAC1B,UAAM,QAAQ,YAAY,KAAK,IAAI,KAAK,MAAM,QAAQ,MAAM,MAAM,QAAQ;AAAA,EAC3E;AAGD,MAAI,KAAK,SAAS,SAAS;AAEzB,UAAM,MAAM,MAAM,OAAO;AAEzB,QAAI,IAAI,SAAS,MAAM,KAAK,IAAI,SAAS,MAAM,KAAK,IAAI,SAAS,OAAO,MAAM,MAAM,QAAQ,IAAI,SAAS,OAAO,GAAG;AACjH,WAAK,OAAO;AAAA,IAClB,WAAe,IAAI,SAAS,MAAM,KAAK,IAAI,SAAS,MAAM,KAAK,IAAI,SAAS,MAAM,KAAK,IAAI,SAAS,MAAM,MAAM,MAAM,QAAQ,IAAI,SAAS,OAAO,GAAG;AAC/I,WAAK,OAAO;AAAA,IACb;AACD,QAAI,MAAM,WAAW;AACnB,YAAM,WAAW;AAAA,IAClB;AACD,UAAM,WAAW;AAAA,EAKlB;AAID,MAAI,KAAK,SAAS,WAAW,KAAK,SAAS,SAAS;AAElD,QAAI,KAAK,SAAS,WAAW,CAAC,MAAM,IAAI;AACtC,YAAM,KAAK,MAAM;AAAA,IAClB;AAED,QAAI,CAAC,MAAM,YAAY,CAAC,MAAM,UAAU;AACtC,YAAM,WAAW;AAAA,IAClB;AAED,SAAK,MAAM,CAAE;AACb,QAAI,MAAM,KAAK;AACb,WAAK,IAAI,KAAK,MAAM,GAAG;AACvB,YAAM,MAAM;AAAA,IACb;AACD,SAAK,OAAQ;AAAA,EACd;AAID,MAAI,OAAO;AACT,QAAI,CAAC,KAAK,KAAK,IAAI,KAAK,OAAO,WAAW,KAAK,IAAI,GAAG;AAEpD,UAAI,KAAK,SAAS,UAAU,CAAC,KAAK,QAAQ,QAAQ;AAChD,aAAK,QAAQ,SAAS,MAAM;AAAA,MAC7B,WAAU,KAAK,SAAS,YAAY,WAAW,OAAO,SAAS,WAAW,OAAO,SAAS,YAAY,MAAM,KAAK;AAEhH,eAAO,IAAI,KAAK,MAAM,GAAG;AAAA,MAC1B;AACD;AAAA,IACD;AAGD,UAAM,WAAW,KAAK,WAAW,IAAI;AAGrC,QAAI,KAAK,SAAS,OAAO;AACvB,UAAI,MAAM,KAAK;AAEb,YAAI,MAAM,IAAI,SAAS,MAAM,GAAG;AAC9B,eAAK,OAAO;AAAA,QACb;AAED,YAAI,MAAM,IAAI,SAAS,OAAO,KAAK,KAAK,QAAQ,eAAe,SAAS,CAAC,MAAM,cAAc,GAAG;AAC9F,gBAAM,SAAS;AAAA,QAChB;AACD,YAAI,CAAC,MAAM,UAAU,KAAK,QAAQ,MAAM,IAAI,SAAS,UAAU,GAAG;AAChE,mBAAS,IAAI,KAAK,MAAM,QAAQ,OAAM;AACpC,kBAAM,OAAO,KAAK,MAAM,CAAC;AACzB,gBAAI,KAAK,SAAS,KAAK;AACrB,mBAAK,IAAI,KAAK;AAAA,YACf;AACD,gBAAI,KAAK,SAAS,WAAW,CAAC,KAAK,QAAQ,CAAC,MAAM,IAAI,SAAS,UAAU,GAAG;AAC1E,kBAAI,CAAC,SAAS,WAAW,SAAS,QAAQ,SAAS,QAAQ,GAAG;AAC5D,qBAAK,IAAI;AAAA,cACzB,OAAqB;AACL,qBAAK,IAAI,SAAS;AAAA,cACnB;AACD,uBAAS,UAAU;AAAA,YACpB;AAED,kBAAM,QAAQ,KAAK,MAAM,SAAS;AAClC,gBAAI,MAAM,SAAS,OAAO,KAAK,CAAC,MAAM,SAAS,QAAQ,KAAK,CAAC,MAAM,SAAS,SAAS,MAAM,CAAC,SAAS,SAAS,SAAS,SAAS,KAAK,IAAI,MAAM;AAC7I,uBAAS,QAAQ;AACjB,uBAAS,SAAS;AAClB,uBAAS,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC9C,qBAAK,MAAM,CAAC,EAAE,MAAM,SAAS,KAAK,MAAM,CAAC,EAAE,MAAM,SAAS,IAAI,QAAQ,WAAW,EAAE;AAAA,cACpF;AAAA,YACf,WAAuB,MAAM,SAAS,MAAM,KAAK,SAAS,UAAU,QAAQ;AAC9D,uBAAS,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC9C,sBAAMC,SAAQ,KAAK,MAAM,CAAC,EAAE,MAAM,SAAS;AAC3C,oBAAI,CAACA,OAAM,SAAS,QAAQ,KAAK,CAACA,OAAM,SAAS,QAAQ,KAAKA,OAAM,QAAQ,OAAO,MAAM,GAAG;AAC1F,2BAAS,QAAQ;AACjB;AAAA,gBACD;AAAA,cACF;AAAA,YACF,WAAU,MAAM,SAAS,cAAc,GAAG;AACzC,kBAAI,SAAS,SAAS,SAAS,MAAM,SAAS,MAAM,SAAS,CAAC,MAAM,KAAK;AACvE,qBAAK,MAAM,SAAS,gBAAgB,SAAS;AAC7C,yBAAS,QAAQ;AAAA,cACjC,OAAqB;AACL,qBAAK,MAAM,SAAS;AAAA,cACrB;AAAA,YACF;AAED,iBAAK,IAAI;AAAA,UACV;AACD,gBAAM,IAAI,KAAK,QAAQ,OAAO,SAAU;AACxC,cAAI,MAAM,MAAM,cAAc,KAAK,MAAM;AAEzC,cAAI,KAAK,QAAQ,SAAS,GAAG,GAAG;AAE9B,gBAAI,IAAI,IAAI,QAAQ,KAAK;AACzB,gBAAI,MAAM,IAAI;AACZ,mBAAK;AACL,kBAAI,SAAS,IAAI,OAAO,GAAG,CAAC;AAC5B,qBAAO,IAAI,IAAI,QAAQ,KAAK;AAC1B,oBAAI,IAAI,CAAC,MAAM;AAAK;AACpB,0BAAU,KAAK,OAAQ,IAAG,MAAM,IAAI,CAAC,EAAE,YAAW,IAAK,IAAI,CAAC;AAAA,cAC7D;AACD,wBAAU,IAAI,OAAO,CAAC;AACtB,oBAAM;AAAA,YACP;AAAA,UACF;AAED,eAAK,QAAQ,KAAK,GAAG;AACrB,cAAI,CAAC,KAAK,GAAG;AACX,iBAAK,QAAQ,eAAe;AAAA,UAC7B;AAAA,QAOF;AAAA,MACF;AACD,UAAI,SAAS,YAAY,UAAU;AACjC,iBAAS,UAAU;AAAA,MACpB;AAED,UAAI,MAAM,QAAQ;AAChB,iBAAS,WAAW,IAAI,SAAS,WAAW,KAAK;AACjD,cAAM,SAAS;AAAA,MAChB;AAGD,UAAI,SAAS,SAAS,KAAK,IAAI,aAAa;AAC1C,iBAAS,SAAS;AAAA,MACnB;AAED,UAAI,CAAC,MAAM,SAAS,SAAS,KAAK,CAAC,GAAG;AACpC,aAAK,IAAI;AAAA,MACV;AACD,UAAI,CAAC,MAAM,SAAS,SAAS,MAAM,CAAC,MAAM,CAAC,SAAS,OAAO,SAAS,GAAG,KAAM,WAAW,OAAO,MAAM,SAAS,IAAI,SAAS,QAAQ,IAAK;AACtI,aAAK,IAAI;AAAA,MACV;AACD,UAAI,KAAK,KAAK,KAAK,KAAK,SAAS,YAAY,GAAG;AAC9C,YAAI,SAAS,YAAY,MAAM,WAAW;AACxC,eAAK,IAAI;AAAA,QACV,WAAU,SAAS,YAAY,MAAM,SAAS;AAC7C,eAAK,IAAI;AAAA,QACV;AAAA,MACF;AAAA,IACP,WAAe,KAAK,SAAS,OAAO;AAC9B,eAAS,KAAK,IAAI;AAClB,WAAK,MAAM,KAAK,IAAI;AACpB,WAAK,QAAS;AACd;AAAA,IACD;AACD,eAAW,OAAO,UAAU;AAC1B,UAAI,SAAS,GAAG,GAAG;AACjB,cAAM,SAAS,IAAI,GAAG,IAAI,SAAS,GAAG,EAAE,QAAQ,eAAe,EAAE,CAAC;AAAA,MACnE;AAAA,IACF;AACD,UAAM,QAAQ,MAAM,MAAM,OAAO,CAAC,KAAK;AAEvC,QAAI,CAAC,MAAM,OAAO;AAChB,aAAO,MAAM;AAAA,IACd;AAAA,EAEL,OAAS;AACL,SAAK,KAAK,SAAS,UAAW,MAAM,SAAS,IAAI,SAAS,aAAa,KAAK,MAAM,MAAM,SAAS,KAAK,MAAO,KAAK,QAAQ,GAAG;AAC3H,WAAK,MAAM,KAAK,MAAM;AAAA,IACvB;AACD,SAAK,WAAW,CAAE;AAClB,SAAK,MAAM,KAAK,IAAI;AAAA,EACrB;AAGD,WAAS,KAAK,IAAI;AACpB;AAOA,OAAO,UAAU,aAAa,SAAU,MAAM;AAE5C,SAAO,KAAK,MAAM,OAAO,KAAK,YAAa;AAC3C,MAAI;AACJ,OAAK,IAAI,KAAK,MAAM,QAAQ,OAAM;AAChC,QAAI,KAAK,MAAM,CAAC,EAAE,SAAS;AAAM;AAAA,EAClC;AACD,MAAI,MAAM,IAAI;AACZ,WAAO,KAAK,MAAM,SAAS,GAAG;AAC5B,WAAK,QAAS;AAAA,IACf;AAAA,EACF,WAAU,SAAS,OAAO,SAAS,MAAM;AACxC,UAAM,WAAW,KAAK,MAAM,SAAS,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC,EAAE,WAAW,KAAK;AACvF,aAAS,KAAK;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,OAAO,YAAY,IAAI,KAAK;AAAA,QAC5B,OAAO,KAAK,SAAS,IAAI,KAAK;AAAA,MAC/B;AAAA,IACP,CAAK;AAAA,EACF;AACH;AAMA,OAAO,UAAU,UAAU,WAAY;AACrC,QAAM,OAAO,KAAK,MAAM,IAAK;AAC7B,MAAI,QAAQ,KAAK;AACjB,QAAM,WAAW,KAAK;AACtB,QAAM,SAAS,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC/C,QAAM,WAAW,SAAS,OAAO,WAAW,KAAK;AAEjD,MAAI,CAAC,KAAK,KAAK,IAAI,KAAK,OAAO,WAAW,KAAK,IAAI,GAAG;AAEpD,QAAI,KAAK,SAAS,WAAW,SAAS,UAAU,SAAS,CAAC,EAAE,SAAS,UAAU,KAAK,QAAQ,UAAU;AACpGD,oBAAAA,MAAI,sBAAsB;AAAA,QACxB,OAAO,SAAS,CAAC,EAAE;AAAA,MAC3B,CAAO;AAAA,IACF;AACD,aAAS,IAAK;AACd;AAAA,EACD;AAED,MAAI,KAAK,OAAO,KAAK,QAAQ,GAAG;AAE9B,SAAK,MAAM,KAAK,MAAM;AACtB,aAAS,IAAI,KAAK,MAAM,QAAQ,OAAM;AACpC,UAAI,KAAK,MAAM,CAAC,EAAE,KAAK;AACrB,aAAK,MAAM;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAED,QAAM,WAAW,CAAE;AAGnB,MAAI,KAAK,SAAS,OAAO;AACvB,QAAI,KAAK,MAAM,GAAG;AAEhB,WAAK;AACL;AAAA,IACD;AAmBD,QAAI,MAAM;AACV,UAAM,QAAQ,MAAM;AACpB,UAAM,QAAQ;AACd,UAAM,QAAQ;AACd,KAAC,SAAS,UAAWE,OAAM;AACzB,UAAIA,MAAK,SAAS,QAAQ;AACxB,eAAOA,MAAK;AACZ;AAAA,MACD;AACD,YAAM,OAAO,OAAO,QAAQA,MAAK,IAAI,KAAKA,MAAK;AAC/C,UAAI,SAAS,iBAAiB;AAC5B,mBAAW,SAAUA,MAAK,YAAY,CAAA,GAAK;AACzC,cAAI,MAAM,SAAS,CAAC,MAAM,MAAM,OAAO;AACrC,kBAAM,MAAM,QAAQ;AACpB;AAAA,UACD;AAAA,QACF;AAAA,MACF;AACD,aAAO,MAAM;AACb,iBAAW,QAAQA,MAAK,OAAO;AAC7B,cAAM,MAAMA,MAAK,MAAM,IAAI;AAC3B,YAAI,KAAK;AACP,iBAAO,IAAI,OAAO,QAAQ,IAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,MAAM,EAAE,CAAC;AAAA,QAClE;AAAA,MACF;AACD,UAAI,CAACA,MAAK,UAAU;AAClB,eAAO;AAAA,MACf,OAAa;AACL,eAAO;AACP,iBAAS,IAAI,GAAG,IAAIA,MAAK,SAAS,QAAQ,KAAK;AAC7C,oBAAUA,MAAK,SAAS,CAAC,CAAC;AAAA,QAC3B;AACD,eAAO,OAAO,OAAO;AAAA,MACtB;AAAA,IACF,GAAE,IAAI;AACP,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,MACX,KAAK,6BAA6B,IAAI,QAAQ,MAAM,KAAK;AAAA,MACzD;AAAA,MACA,QAAQ;AAAA,IACT;AACD,SAAK,WAAW;AAEhB,SAAK,MAAM;AACX,WAAO,WAAW,QAAQ;AAC1B;AAAA,EACD;AAID,MAAI,MAAM,OAAO;AACf,QAAI,KAAK,SAAS,SAAS;AACzB,UAAI,MAAM,UAAU,UAAU;AAC5B,iBAAS,qBAAqB,IAAI,SAAS,mBAAmB,IAAI;AAAA,MAC1E,OAAa;AACL,iBAAS,QAAQ,MAAM;AAAA,MACxB;AAAA,IACP,OAAW;AACL,eAAS,YAAY,IAAI,MAAM;AAAA,IAChC;AACD,UAAM,QAAQ;AAAA,EACf;AAGD,MAAI,MAAM,KAAK;AACb,aAAS,YAAY,MAAM;AAC3B,UAAM,MAAM;AAAA,EACb;AAGD,MAAI,KAAK,SAAS,QAAQ;AACxB,QAAI,MAAM,OAAO;AACf,eAAS,QAAQ,MAAM;AACvB,YAAM,QAAQ;AAAA,IACf;AACD,QAAI,MAAM,MAAM;AACd,eAAS,aAAa,IAAI,MAAM;AAChC,YAAM,OAAO;AAAA,IACd;AACD,QAAI,MAAM,MAAM;AACd,UAAI,OAAO,SAAS,MAAM,IAAI;AAC9B,UAAI,CAAC,MAAM,IAAI,GAAG;AAChB,YAAI,OAAO,GAAG;AACZ,iBAAO;AAAA,QACjB,WAAmB,OAAO,GAAG;AACnB,iBAAO;AAAA,QACR;AACD,iBAAS,WAAW,IAAI,CAAC,WAAW,SAAS,UAAU,SAAS,WAAW,YAAY,WAAW,EAAE,OAAO,CAAC;AAAA,MAC7G;AACD,YAAM,OAAO;AAAA,IACd;AAAA,EACF;AAID,OAAK,MAAM,SAAS,IAAI,SAAS,cAAc,GAAG;AAChD,aAAS,YAAY,IAAI;AAAA,EAC1B;AAED,SAAO,OAAO,UAAU,KAAK,WAAW,IAAI,CAAC;AAE7C,MAAI,KAAK,SAAS,WAAW,SAAS,SAAS,KAAK,IAAI,aAAa;AACnE,aAAS,WAAW,IAAI;AACxB,aAAS,YAAY,IAAI;AAAA,EAC1B;AAGD,MAAI,OAAO,UAAU,KAAK,IAAI,GAAG;AAC/B,SAAK,OAAO;AAAA,EAChB,WAAa,CAAC,OAAO,UAAU,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK;AAEpD,SAAK,OAAO;AAAA,EACb;AAED,MAAI,KAAK,SAAS,OAAO,KAAK,SAAS,MAIrC;AACA,SAAK,OAAQ;AAAA,EACjB,WAAa,KAAK,SAAS,SAAS;AAChC,SAAK,SAAS,UAAU,IAAI,SAAS,MAAM,GAAG;AAC5C,eAAS,SAAS;AAAA,IACnB;AAAA,EAkBL,YAAc,KAAK,SAAS,QAAQ,KAAK,SAAS,SAAS,KAAK,GAAG;AAE/D,UAAM,QAAQ;AAAA,MACZ,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACJ;AACD,QAAI,MAAM,MAAM,IAAI,GAAG;AACrB,YAAM,SAAS,sBAAsB,MAAM,MAAM,IAAI;AACrD,YAAM,OAAO;AAAA,IACd;AACD,aAAS,IAAI,SAAS,QAAQ,OAAM;AAClC,UAAI,SAAS,CAAC,EAAE,SAAS,MAAM;AAC7B,iBAAS,CAAC,EAAE,IAAI;AAAA,MACjB;AAAA,IACF;AAAA,EACL,WAAa,KAAK,SAAS,SAAS;AAGhC,QAAI,UAAU,WAAW,MAAM,WAAW;AAC1C,QAAI,UAAU,WAAW,MAAM,WAAW;AAC1C,UAAM,SAAS,WAAW,MAAM,MAAM;AACtC,UAAM,cAAc,SAAS,cAAc;AAC3C,UAAM,cAAc,SAAS,cAAc;AAC3C,QAAI,KAAK,GAAG;AAEV,UAAI,MAAM,OAAO,GAAG;AAClB,kBAAU;AAAA,MACX;AACD,UAAI,MAAM,OAAO,GAAG;AAClB,kBAAU;AAAA,MACX;AAAA,IACF;AACD,QAAI,QAAQ;AACV,YAAM,SAAS,WAAW,MAAM,MAAM,eAAe,OAAO,IAAI,eAAe,MAAM;AAAA,IACtF;AACD,QAAI,KAAK,QAAQ,KAAK,GAAG;AAEvB,eAAS,UAAU;AACnB,UAAI,SAAS,iBAAiB,MAAM,YAAY;AAC9C,iBAAS,iBAAiB,IAAI;AAC9B,kBAAU;AAAA,MACX;AACD,UAAI,SAAS;AACX,iBAAS,UAAU,IAAI,UAAU;AACjC,iBAAS,UAAU,UAAU;AAAA,MAC9B,WAAU,QAAQ;AAEjB,cAAM,SAAS;AAAA,MAChB;AAED,YAAM,QAAQ,CAAE;AAChB,YAAM,SAAS,CAAE;AACjB,YAAM,QAAQ,CAAE;AAChB,YAAM,MAAM,CAAA;AAEZ,OAAC,SAAS,UAAW,OAAO;AAC1B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAI,MAAM,CAAC,EAAE,SAAS,MAAM;AAC1B,mBAAO,KAAK,MAAM,CAAC,CAAC;AAAA,UACrB,WAAU,MAAM,CAAC,EAAE,SAAS,YAAY;AACvC,gBAAI,OAAO;AACX,uBAAW,OAAQ,MAAM,CAAC,EAAE,YAAY,CAAA,GAAK;AAC3C,kBAAI,IAAI,SAAS,OAAO;AACtB,sBAAM,QAAQ,IAAI,MAAM,SAAS;AACjC,sBAAM,QAAQ,MAAM,QAAQ,OAAO,IAAI,MAAM,QAAQ,QAAQ,IAAI;AAEjE,oBAAI,UAAU,IAAI;AAChB,sBAAI,MAAM,MAAM,QAAQ,KAAK,QAAQ,CAAC;AACtC,sBAAI,QAAQ,IAAI;AACd,0BAAM,MAAM;AAAA,kBACb;AACD,wBAAM,IAAI,IAAI,MAAM,UAAU,QAAQ,QAAQ,IAAI,GAAG,GAAG;AAAA,gBACzD;AACD,wBAAQ;AAAA,cACT;AAAA,YACF;AAAA,UACb,OAAiB;AACL,sBAAU,MAAM,CAAC,EAAE,YAAY,CAAA,CAAE;AAAA,UAClC;AAAA,QACF;AAAA,MACF,GAAE,QAAQ;AAEX,eAAS,MAAM,GAAG,OAAO,OAAO,QAAQ,OAAO;AAC7C,YAAI,MAAM;AACV,iBAAS,IAAI,GAAG,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS,QAAQ,KAAK;AACxD,gBAAM,KAAK,OAAO,MAAM,CAAC,EAAE,SAAS,CAAC;AACrC,cAAI,GAAG,SAAS,QAAQ,GAAG,SAAS,MAAM;AAExC,mBAAO,IAAI,MAAM,MAAM,GAAG,GAAG;AAC3B;AAAA,YACD;AACD,gBAAI,QAAQ,GAAG,MAAM,SAAS;AAC9B,gBAAI,QAAQ,MAAM,QAAQ,OAAO,IAAI,MAAM,QAAQ,QAAQ,IAAI;AAE/D,gBAAI,UAAU,IAAI;AAChB,kBAAI,MAAM,MAAM,QAAQ,KAAK,QAAQ,CAAC;AACtC,kBAAI,QAAQ,IAAI;AACd,sBAAM,MAAM;AAAA,cACb;AACD,kBAAI,CAAC,GAAG,MAAM,SAAS;AACrB,sBAAM,GAAG,IAAI,MAAM,UAAU,QAAQ,QAAQ,IAAI,GAAG,GAAG;AAAA,cACxD;AACD,sBAAQ,MAAM,OAAO,GAAG,KAAK,IAAI,MAAM,OAAO,GAAG;AAAA,YAClD;AAED,qBAAS;AACT,oBAAQ,MAAM,QAAQ,gBAAgB;AACtC,gBAAI,UAAU,IAAI;AAChB,oBAAM,MAAM,MAAM,OAAO,QAAQ,IAAI,EAAE;AACvC,kBAAI,IAAI,SAAS,QAAQ,GAAG;AAC1B,yBAAS;AAAA,cACV,WAAU,IAAI,SAAS,QAAQ,GAAG;AACjC,yBAAS;AAAA,cACV;AAAA,YACf,OAAmB;AACL,uBAAS;AAAA,YACV;AAED,oBAAQ,MAAM,QAAQ,YAAY;AAClC,gBAAI,UAAU,IAAI;AAChB,oBAAM,MAAM,MAAM,OAAO,QAAQ,IAAI,EAAE;AACvC,kBAAI,IAAI,SAAS,QAAQ,GAAG;AAC1B,yBAAS;AAAA,cACV,WAAU,IAAI,SAAS,OAAO,GAAG;AAChC,yBAAS;AAAA,cACV;AAAA,YACF;AACD,qBAAS,SAAS,WAAW,MAAM,MAAM,eAAe,OAAO,IAAI,eAAe,MAAM,MAAM,UAAU,KAAK,qCAAqC,OAAO,UAAU,YAAY,OAAO,OAAO,MAAM,MAAM;AAEzM,gBAAI,GAAG,MAAM,SAAS;AACpB,uBAAS,sBAAsB,GAAG,oBAAoB,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC;AACtF,kBAAI,CAAC,GAAG,MAAM,SAAS;AACrB,yBAAS,mBAAmB,GAAG,iBAAiB,MAAM,CAAC;AAAA,cACxD;AACD,qBAAO,SAAS,GAAG,MAAM,OAAO,IAAI;AAAA,YACrC;AAED,gBAAI,GAAG,MAAM,SAAS;AACpB,uBAAS,mBAAmB,GAAG,iBAAiB,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC;AAChF,kBAAI,CAAC,GAAG,MAAM,SAAS;AACrB,yBAAS,sBAAsB,GAAG,oBAAoB,MAAM,CAAC;AAAA,cAC9D;AAED,uBAAS,UAAU,GAAG,UAAU,GAAG,MAAM,SAAS,WAAW;AAC3D,yBAAS,UAAU,GAAG,WAAW,GAAG,MAAM,WAAW,IAAI,WAAW;AAClE,sBAAK,MAAM,UAAW,OAAO,MAAM,QAAQ,IAAI;AAAA,gBAChD;AAAA,cACF;AAAA,YACF;AACD,gBAAI,OAAO;AACT,iBAAG,MAAM,QAAQ;AAAA,YAClB;AACD,kBAAM,KAAK,EAAE;AACb;AAAA,UACD;AAAA,QACF;AACD,YAAI,QAAQ,GAAG;AACb,cAAI,OAAO;AACX,mBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,qBAAS,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,UAAU;AAAA,UAC1C;AACD,mBAAS,uBAAuB,IAAI;AAAA,QACrC;AAAA,MACF;AACD,WAAK,WAAW;AAAA,IACtB,OAAW;AAEL,UAAI,KAAK,GAAG;AACV,iBAAS,UAAU;AAAA,MACpB;AACD,UAAI,CAAC,MAAM,OAAO,GAAG;AACnB,iBAAS,gBAAgB,IAAI,UAAU;AAAA,MACxC;AACD,UAAI,UAAU,SAAS;AAErB,SAAC,SAAS,UAAW,OAAO;AAC1B,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,kBAAM,KAAK,MAAM,CAAC;AAClB,gBAAI,GAAG,SAAS,QAAQ,GAAG,SAAS,MAAM;AACxC,kBAAI,QAAQ;AACV,mBAAG,MAAM,QAAQ,UAAU,MAAM,MAAM,eAAe,OAAO,IAAI,eAAe,MAAM,IAAI,GAAG,MAAM,SAAS,EAAE;AAAA,cAC/G;AACD,kBAAI,SAAS;AACX,mBAAG,MAAM,QAAQ,WAAW,OAAO,MAAM,GAAG,MAAM,SAAS,EAAE;AAAA,cAC9D;AAAA,YACf,WAAuB,GAAG,UAAU;AACtB,wBAAU,GAAG,QAAQ;AAAA,YACtB;AAAA,UACF;AAAA,QACF,GAAE,QAAQ;AAAA,MACZ;AAAA,IACF;AAED,QAAI,KAAK,QAAQ,eAAe,EAAE,MAAM,SAAS,IAAI,SAAS,QAAQ,GAAG;AACvE,YAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,IAAI;AACpC,WAAK,OAAO;AACZ,WAAK,QAAQ;AAAA,QACX,OAAO;AAAA,MACR;AACD,WAAK,WAAW,CAAC,KAAK;AACtB,cAAQ,MAAM;AAAA,IACf;AAAA,EACF,YAAW,KAAK,SAAS,WAAW,KAAK,SAAS,SAAS,KAAK,QAAQ,KAAK,GAAG;AAC/E,SAAK,OAAO;AACZ,KAAC,SAAS,UAAW,OAAO;AAC1B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,MAAM,CAAC,EAAE,SAAS,MAAM;AAE1B,qBAAW,SAAS,CAAC,SAAS,cAAc,kBAAkB,GAAG;AAC/D,gBAAI,SAAS,KAAK,GAAG;AACnB,oBAAM,CAAC,EAAE,MAAM,QAAQ,QAAQ,MAAM,SAAS,KAAK,IAAI,OAAO,MAAM,CAAC,EAAE,MAAM,SAAS;AAAA,YACvF;AAAA,UACF;AAAA,QACX,OAAe;AACL,oBAAU,MAAM,CAAC,EAAE,YAAY,CAAA,CAAE;AAAA,QAClC;AAAA,MACF;AAAA,IACF,GAAE,QAAQ;AAAA,EACZ,YAAW,KAAK,SAAS,QAAQ,KAAK,SAAS,UAAU,MAAM,WAAW,MAAM,UAAU;AACzF,aAAS,IAAI,KAAK,MAAM,QAAQ,OAAM;AACpC,UAAI,KAAK,MAAM,CAAC,EAAE,SAAS,WAAW,KAAK,MAAM,CAAC,EAAE,SAAS,WAAW,KAAK,MAAM,CAAC,EAAE,SAAS,MAAM;AACnG,aAAK,MAAM,CAAC,EAAE,OAAO;AAAA,MACtB;AAAA,IACF;AAAA,EACL,WAAa,KAAK,SAAS,QAAQ;AAE/B,SAAK,OAAO;AACZ,aAAS,IAAI,GAAG,IAAI,SAAS,SAAS,GAAG,KAAK;AAC5C,UAAI,SAAS,CAAC,EAAE,SAAS,UAAU,SAAS,IAAI,CAAC,EAAE,SAAS,MAAM;AAChE,iBAAS,CAAC,IAAI;AAAA,UACZ,MAAM;AAAA,UACN,OAAO;AAAA,YACL,OAAO;AAAA,UACR;AAAA,UACD,UAAU,CAAC;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,cACL,OAAO,oBAAoB,SAAS,IAAI,CAAC,EAAE,MAAM,SAAS;AAAA,YAC3D;AAAA,YACD,UAAU,SAAS,IAAI,CAAC,EAAE;AAAA,UACtC,GAAa,SAAS,CAAC,CAAC;AAAA,QACf;AACD,iBAAS,OAAO,IAAI,GAAG,CAAC;AAAA,MACzB;AAAA,IACF;AAAA,EACL,WAAa,KAAK,GAAG;AACjB,KAAC,SAAS,UAAWA,OAAM;AACzB,MAAAA,MAAK,IAAI;AACT,eAAS,IAAIA,MAAK,SAAS,QAAQ,OAAM;AACvC,cAAM,QAAQA,MAAK,SAAS,CAAC;AAE7B,YAAI,MAAM,SAAS,OAAO,WAAW,MAAM,IAAI,MAAO,MAAM,MAAM,SAAS,IAAI,SAAS,QAAQ,KAAK,MAAM,aAAc,CAAC,MAAM,GAAG;AACjI,oBAAU,KAAK;AAAA,QAChB;AAED,YAAI,CAAC,MAAM,KAAK,MAAM,SAAS,SAAS;AACtC,UAAAA,MAAK,IAAI;AAAA,QACV;AAAA,MACF;AAAA,IACF,GAAE,IAAI;AAAA,EACR;AAED,OAAK,SAAS,WAAW,IAAI,SAAS,MAAM,KAAK,CAAC,KAAK,GAAG;AACxD,aAAS,IAAI,SAAS,QAAQ,OAAM;AAClC,YAAM,OAAO,SAAS,CAAC;AACvB,UAAI,KAAK,GAAG;AACV,aAAK,MAAM,SAAS,KAAK,MAAM,SAAS,MAAM,KAAK;AACnD,aAAK,IAAI;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAED,QAAM,OAAO,YAAY,OAAO,MAAM,SAAS,IAAI,SAAS,MAAM,MAAM,OAAO,MAAM,SAAS,IAAI,SAAS,MAAM,MAG5G,EAAE,KAAK,KAAKC,cAAE,KAAC;AAKpB,MAAI,MAAM;AACR,SAAK,IAAI;AAAA,EACV;AAED,MAAI,SAAS,UAAU,MAAM,KAAK,KAAK,EAAE,SAAS,WAAW,IAAI,SAAS,MAAM,GAAG;AACjF,eAAW,QAAQ;AAAA,EACpB;AAGD,aAAW,OAAO,UAAU;AAC1B,QAAI,SAAS,GAAG,GAAG;AACjB,YAAM,MAAM,IAAI,GAAG,IAAI,SAAS,GAAG,EAAE,QAAQ,eAAe,EAAE,CAAC;AAE/D,UAAI,SAAU,IAAI,SAAS,MAAM,KAAK,QAAQ,oBAAqB,QAAQ,gBAAgB,IAAI,SAAS,MAAM,KAAK,SAAS,GAAG,EAAE,CAAC,MAAM,OAAQ,IAAI,SAAS,OAAO,KAAK,IAAI,SAAS,GAAG,IAAK;AAC5L,aAAK,KAAK;AACV,YAAI,QAAQ,SAAS;AACnB,gBAAM,SAAS;AAAA,QAChB;AAAA,MACT,OAAa;AACL,cAAM,SAAS;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACD,QAAM,QAAQ,MAAM,MAAM,OAAO,CAAC,KAAK;AAEvC,aAAW,OAAO,OAAO;AACvB,QAAI,CAAC,MAAM,GAAG,GAAG;AACf,aAAO,MAAM,GAAG;AAAA,IACjB;AAAA,EACF;AAEH;AAMA,OAAO,UAAU,SAAS,SAAU,MAAM;AACxC,MAAI,CAAC,KAAK,KAAK;AAEb,QAAI,OAAO;AACX,QAAI;AACJ,aAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAC/C,UAAI,CAAC,UAAU,KAAK,CAAC,CAAC,GAAG;AACvB,gBAAQ,KAAK,CAAC;AAAA,MACtB,OAAa;AACL,YAAI,KAAK,KAAK,SAAS,CAAC,MAAM,KAAK;AACjC,kBAAQ;AAAA,QACT;AACD,YAAI,KAAK,CAAC,MAAM,QAAQ,CAAC,MAAM;AAC7B,iBAAO;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAED,QAAI,SAAS,KAAK;AAChB,UAAI;AAAM;AAAA,WAEL;AACH,cAAM,SAAS,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC/C,YAAI,UAAU,OAAO,KAAK,CAAC,MAAM;AAAK;AAAA,MACvC;AAAA,IAEF;AACD,WAAO;AAAA,EACR;AACD,QAAM,OAAO,uBAAO,OAAO,IAAI;AAC/B,OAAK,OAAO;AAIZ,OAAK,OAAO,aAAa,IAAI;AAC7B,MAAI,KAAK,KAAK,IAAI,GAAG;AAEnB,QAAI,KAAK,QAAQ,eAAe,WAAW,OAAO,SAAS,KAAK,KAAK,CAACH,cAAAA,MAAI,QAAQ,uBAAuB,GAAG;AAC1G,WAAK,OAAQ;AAAA,IACd;AAED,UAAM,WAAW,KAAK,MAAM,SAAS,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC,EAAE,WAAW,KAAK;AACvF,aAAS,KAAK,IAAI;AAAA,EACnB;AACH;AAMA,SAAS,MAAO,SAAS;AACvB,OAAK,UAAU;AACjB;AAMA,MAAM,UAAU,QAAQ,SAAU,SAAS;AACzC,OAAK,UAAU,WAAW;AAC1B,OAAK,IAAI;AACT,OAAK,QAAQ;AACb,OAAK,QAAQ,KAAK;AAClB,WAAS,MAAM,KAAK,QAAQ,QAAQ,KAAK,MAAM,MAAM,KAAK,IAAI,OAAM;AAClE,SAAK,MAAO;AAAA,EACb;AACH;AAQA,MAAM,UAAU,aAAa,SAAU,QAAQ;AAC7C,QAAM,YAAY,KAAK,QAAQ,KAAK,CAAC,MAAM;AAC3C,MAAI,KAAK,QAAQ,KAAK,CAAC,MAAM,OAAQ,aAAa,KAAK,QAAQ,KAAK,IAAI,CAAC,MAAM,KAAM;AACnF,QAAI,QAAQ;AACV,WAAK,QAAQ,MAAM,EAAE,KAAK,QAAQ,UAAU,KAAK,OAAO,KAAK,CAAC,CAAC;AAAA,IAChE;AACD,SAAK,KAAK,YAAY,IAAI;AAC1B,SAAK,QAAQ,KAAK;AAClB,SAAK,QAAQ,UAAU,SAAS;AAChC,QAAI,KAAK,QAAQ,YAAY,UAAU;AACrC,WAAK,IAAI,KAAK,QAAQ,QAAQ,MAAM,KAAK,CAAC;AAC1C,UAAI,KAAK,MAAM,IAAI;AACjB,aAAK,KAAK;AACV,aAAK,QAAQ,KAAK;AAAA,MACnB;AACD,WAAK,QAAQ,KAAK;AAAA,IACxB,OAAW;AACL,WAAK,QAAQ,KAAK;AAAA,IACnB;AACD,WAAO;AAAA,EACR;AACD,SAAO;AACT;AAMA,MAAM,UAAU,OAAO,WAAY;AACjC,OAAK,IAAI,KAAK,QAAQ,QAAQ,KAAK,KAAK,CAAC;AACzC,MAAI,KAAK,MAAM,IAAI;AAEjB,QAAI,KAAK,QAAQ,KAAK,QAAQ,QAAQ;AACpC,WAAK,QAAQ,OAAO,KAAK,QAAQ,UAAU,KAAK,OAAO,KAAK,QAAQ,MAAM,CAAC;AAAA,IAC5E;AACD;AAAA,EACD;AACD,QAAM,IAAI,KAAK,QAAQ,KAAK,IAAI,CAAC;AACjC,MAAK,KAAK,OAAO,KAAK,OAAS,KAAK,OAAO,KAAK,KAAM;AAEpD,QAAI,KAAK,UAAU,KAAK,GAAG;AACzB,WAAK,QAAQ,OAAO,KAAK,QAAQ,UAAU,KAAK,OAAO,KAAK,CAAC,CAAC;AAAA,IAC/D;AACD,SAAK,QAAQ,EAAE,KAAK;AACpB,SAAK,QAAQ,KAAK;AAAA,EACtB,WAAa,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AAC9C,QAAI,KAAK,UAAU,KAAK,GAAG;AACzB,WAAK,QAAQ,OAAO,KAAK,QAAQ,UAAU,KAAK,OAAO,KAAK,CAAC,CAAC;AAAA,IAC/D;AACD,UAAM,OAAO,KAAK,QAAQ,KAAK,IAAI,CAAC;AACpC,QAAI,MAAM,QAAS,QAAQ,OAAO,QAAQ,OAAS,QAAQ,OAAO,QAAQ,MAAO;AAE/E,WAAK,KAAK;AACV,WAAK,QAAQ,KAAK;AAClB,WAAK,QAAQ,KAAK;AAClB;AAAA,IACD;AAED,QAAI,MAAM;AACV,QAAI,MAAM,OAAO,KAAK,QAAQ,KAAK,IAAI,CAAC,MAAM,OAAO,KAAK,QAAQ,KAAK,IAAI,CAAC,MAAM,KAAK;AACrF,YAAM;AAAA,IACP;AACD,SAAK,IAAI,KAAK,QAAQ,QAAQ,KAAK,KAAK,CAAC;AACzC,QAAI,KAAK,MAAM,IAAI;AACjB,WAAK,KAAK,IAAI;AACd,WAAK,QAAQ,KAAK;AAAA,IACnB;AAAA,EACL,OAAS;AACL,SAAK;AAAA,EACN;AACH;AAMA,MAAM,UAAU,UAAU,WAAY;AACpC,MAAI,UAAU,KAAK,QAAQ,KAAK,CAAC,CAAC,GAAG;AAEnC,SAAK,QAAQ,UAAU,KAAK,QAAQ,UAAU,KAAK,OAAO,KAAK,CAAC,CAAC;AACjE,WAAO,UAAU,KAAK,QAAQ,EAAE,KAAK,CAAC,CAAC;AAAE;AACzC,QAAI,KAAK,IAAI,KAAK,QAAQ,UAAU,CAAC,KAAK,cAAc;AACtD,WAAK,QAAQ,KAAK;AAClB,WAAK,QAAQ,KAAK;AAAA,IACnB;AAAA,EACF,WAAU,CAAC,KAAK,WAAW,WAAW,GAAG;AACxC,SAAK;AAAA,EACN;AACH;AAMA,MAAM,UAAU,WAAW,WAAY;AACrC,MAAI,IAAI,KAAK,QAAQ,KAAK,CAAC;AAC3B,MAAI,UAAU,CAAC,KAAK,MAAM,KAAK;AAE7B,SAAK,QAAQ,WAAW,KAAK,QAAQ,UAAU,KAAK,OAAO,KAAK,CAAC,CAAC;AAClE,QAAI,UAAU,MAAM;AACpB,UAAM,MAAM,KAAK,QAAQ;AACzB,WAAO,EAAE,KAAK,IAAI,KAAK;AACrB,UAAI,KAAK,QAAQ,KAAK,CAAC;AACvB,UAAI,CAAC,UAAU,CAAC,GAAG;AACjB,YAAI,KAAK,WAAU;AAAI;AACvB,YAAI,SAAS;AAEX,eAAK,QAAQ,KAAK;AAClB,eAAK,QAAQ,KAAK;AAClB;AAAA,QACD;AACD,YAAI,KAAK,QAAQ,KAAK,CAAC,MAAM,KAAK;AAChC,oBAAU;AAAA,QACpB,OAAe;AACL,eAAK,QAAQ,KAAK;AAClB,eAAK,QAAQ,KAAK;AAClB;AAAA,QACD;AAAA,MACF;AAAA,IACF;AAAA,EACF,WAAU,CAAC,KAAK,WAAW,YAAY,GAAG;AACzC,SAAK;AAAA,EACN;AACH;AAMA,MAAM,UAAU,UAAU,WAAY;AACpC,QAAM,IAAI,KAAK,QAAQ,KAAK,CAAC;AAC7B,QAAM,MAAM,KAAK,QAAQ;AACzB,MAAI,MAAM,OAAO,MAAM,KAAK;AAE1B,SAAK,QAAQ,EAAE,KAAK;AACpB,SAAK,IAAI,KAAK,QAAQ,QAAQ,GAAG,KAAK,CAAC;AACvC,QAAI,KAAK,MAAM;AAAI;AACnB,SAAK,QAAQ,UAAU,KAAK,QAAQ,UAAU,KAAK,OAAO,KAAK,CAAC,CAAC;AAAA,EACrE,OAAS;AAEL,WAAO,KAAK,IAAI,KAAK,KAAK,KAAK;AAC7B,UAAI,UAAU,KAAK,QAAQ,KAAK,CAAC,CAAC,GAAG;AACnC,aAAK,QAAQ,UAAU,KAAK,QAAQ,UAAU,KAAK,OAAO,KAAK,CAAC,CAAC;AACjE;AAAA,MACD,WAAU,KAAK,WAAW,WAAW;AAAG;AAAA,IAC1C;AAAA,EACF;AACD,SAAO,UAAU,KAAK,QAAQ,EAAE,KAAK,CAAC,CAAC;AAAE;AACzC,MAAI,KAAK,IAAI,OAAO,CAAC,KAAK,WAAU,GAAI;AACtC,SAAK,QAAQ,KAAK;AAClB,SAAK,QAAQ,KAAK;AAAA,EACnB;AACH;AAOA,MAAM,UAAU,SAAS,WAAY;AACnC,QAAM,IAAI,KAAK,QAAQ,KAAK,CAAC;AAC7B,MAAI,UAAU,CAAC,KAAK,MAAM,OAAO,MAAM,KAAK;AAC1C,SAAK,QAAQ,WAAW,KAAK,QAAQ,UAAU,KAAK,OAAO,KAAK,CAAC,CAAC;AAClE,QAAI,MAAM,KAAK;AACb,WAAK,IAAI,KAAK,QAAQ,QAAQ,KAAK,KAAK,CAAC;AACzC,UAAI,KAAK,MAAM;AAAI;AAAA,IACpB;AACD,SAAK,QAAQ,EAAE,KAAK;AACpB,SAAK,QAAQ,KAAK;AAAA,EACtB,OAAS;AACL,SAAK;AAAA,EACN;AACH;;"}