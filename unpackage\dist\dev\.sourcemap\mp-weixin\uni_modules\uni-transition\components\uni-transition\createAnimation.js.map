{"version": 3, "file": "createAnimation.js", "sources": ["uni_modules/uni-transition/components/uni-transition/createAnimation.js"], "sourcesContent": ["export const createAnimation = function(option, _this) {\r\n  if (!_this) return;\r\n  clearTimeout(_this.timer);\r\n  return new Animation(option, _this);\r\n}\r\n\r\nconst animateTypes1 = ['matrix', 'matrix3d', 'rotate', 'rotate3d', 'rotateX', 'rotateY', 'rotateZ', 'scale', 'scale3d',\r\n  'scaleX', 'scaleY', 'scaleZ', 'skew', 'skewX', 'skewY', 'translate', 'translate3d', 'translateX', 'translateY',\r\n  'translateZ'\r\n]\r\n\r\nconst animateTypes2 = ['opacity', 'backgroundColor']\r\nconst animateTypes3 = ['width', 'height', 'left', 'right', 'top', 'bottom']\r\nconst animateTypes = [...animateTypes1, ...animateTypes2, ...animateTypes3]\r\n\r\nclass Animation {\r\n  constructor(option, _this) {\r\n    this.options = option\r\n    this.$$ = _this\r\n    this.animation = uni.createAnimation({\r\n      ...option\r\n    })\r\n    this.currentStepAnimates = {}\r\n    this.next = 0\r\n    this.isEnd = false\r\n  }\r\n\r\n  _nvuePushAnimates(type, args) {\r\n    let aniObj = this.currentStepAnimates[this.next]\r\n    let styles = {}\r\n    if (!aniObj) {\r\n      styles = {\r\n        styles: {},\r\n        config: {}\r\n      }\r\n    } else {\r\n      styles = aniObj\r\n    }\r\n    if (animateTypes1.includes(type)) {\r\n      if (!styles.styles.transform) {\r\n        styles.styles.transform = ''\r\n      }\r\n      let unit = ''\r\n      if (type === 'rotate') {\r\n        unit = 'deg'\r\n      }\r\n      styles.styles.transform += `${type}(${args + unit}) `\r\n    } else {\r\n      styles.styles[type] = `${args}`\r\n    }\r\n    this.currentStepAnimates[this.next] = styles\r\n  }\r\n\r\n  _animateRun(styles = {}, config = {}) {\r\n    let ref = this.$.$refs['ani'].ref\r\n    if (!ref) return\r\n    return new Promise((resolve, reject) => {\r\n      nvueAnimation.transition(ref, {\r\n        styles,\r\n        ...config\r\n      }, res => {\r\n        resolve()\r\n      })\r\n    })\r\n  }\r\n\r\n  _nvueNextAnimate(animates, step = 0, fn) {\r\n    let obj = animates[step]\r\n    if (obj) {\r\n      let {\r\n        styles,\r\n        config\r\n      } = obj\r\n      this._animateRun(styles, config).then(() => {\r\n        step += 1\r\n        this._nvueNextAnimate(animates, step, fn)\r\n      })\r\n    } else {\r\n      this.currentStepAnimates = {}\r\n      typeof fn === 'function' && fn()\r\n      this.isEnd = true\r\n    }\r\n  }\r\n\r\n  step(config = {}) {\r\n    this.animation.step(config)\r\n    return this\r\n  }\r\n\r\n  run(fn) {\r\n    this.$$.animationData = this.animation.export()\r\n    this.$$.timer = setTimeout(() => {\r\n      typeof fn === 'function' && fn()\r\n    }, this.$$.durationTime)\r\n  }\r\n}\r\n\r\nanimateTypes.forEach(type => {\r\n  Animation.prototype[type] = function (...args) {\r\n    this.animation[type](...args)\r\n    return this\r\n  }\r\n}) "], "names": ["uni"], "mappings": ";;AAAY,MAAC,kBAAkB,SAAS,QAAQ,OAAO;AACrD,MAAI,CAAC;AAAO;AACZ,eAAa,MAAM,KAAK;AACxB,SAAO,IAAI,UAAU,QAAQ,KAAK;AACpC;AAEA,MAAM,gBAAgB;AAAA,EAAC;AAAA,EAAU;AAAA,EAAY;AAAA,EAAU;AAAA,EAAY;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAS;AAAA,EAC3G;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAa;AAAA,EAAe;AAAA,EAAc;AAAA,EAClG;AACF;AAEA,MAAM,gBAAgB,CAAC,WAAW,iBAAiB;AACnD,MAAM,gBAAgB,CAAC,SAAS,UAAU,QAAQ,SAAS,OAAO,QAAQ;AAC1E,MAAM,eAAe,CAAC,GAAG,eAAe,GAAG,eAAe,GAAG,aAAa;AAE1E,MAAM,UAAU;AAAA,EACd,YAAY,QAAQ,OAAO;AACzB,SAAK,UAAU;AACf,SAAK,KAAK;AACV,SAAK,YAAYA,cAAG,MAAC,gBAAgB;AAAA,MACnC,GAAG;AAAA,IACT,CAAK;AACD,SAAK,sBAAsB,CAAE;AAC7B,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,kBAAkB,MAAM,MAAM;AAC5B,QAAI,SAAS,KAAK,oBAAoB,KAAK,IAAI;AAC/C,QAAI,SAAS,CAAE;AACf,QAAI,CAAC,QAAQ;AACX,eAAS;AAAA,QACP,QAAQ,CAAE;AAAA,QACV,QAAQ,CAAE;AAAA,MACX;AAAA,IACP,OAAW;AACL,eAAS;AAAA,IACV;AACD,QAAI,cAAc,SAAS,IAAI,GAAG;AAChC,UAAI,CAAC,OAAO,OAAO,WAAW;AAC5B,eAAO,OAAO,YAAY;AAAA,MAC3B;AACD,UAAI,OAAO;AACX,UAAI,SAAS,UAAU;AACrB,eAAO;AAAA,MACR;AACD,aAAO,OAAO,aAAa,GAAG,IAAI,IAAI,OAAO,IAAI;AAAA,IACvD,OAAW;AACL,aAAO,OAAO,IAAI,IAAI,GAAG,IAAI;AAAA,IAC9B;AACD,SAAK,oBAAoB,KAAK,IAAI,IAAI;AAAA,EACvC;AAAA,EAED,YAAY,SAAS,IAAI,SAAS,CAAA,GAAI;AACpC,QAAI,MAAM,KAAK,EAAE,MAAM,KAAK,EAAE;AAC9B,QAAI,CAAC;AAAK;AACV,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,oBAAc,WAAW,KAAK;AAAA,QAC5B;AAAA,QACA,GAAG;AAAA,MACJ,GAAE,SAAO;AACR,gBAAS;AAAA,MACjB,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA,EAED,iBAAiB,UAAU,OAAO,GAAG,IAAI;AACvC,QAAI,MAAM,SAAS,IAAI;AACvB,QAAI,KAAK;AACP,UAAI;AAAA,QACF;AAAA,QACA;AAAA,MACR,IAAU;AACJ,WAAK,YAAY,QAAQ,MAAM,EAAE,KAAK,MAAM;AAC1C,gBAAQ;AACR,aAAK,iBAAiB,UAAU,MAAM,EAAE;AAAA,MAChD,CAAO;AAAA,IACP,OAAW;AACL,WAAK,sBAAsB,CAAE;AAC7B,aAAO,OAAO,cAAc,GAAI;AAChC,WAAK,QAAQ;AAAA,IACd;AAAA,EACF;AAAA,EAED,KAAK,SAAS,IAAI;AAChB,SAAK,UAAU,KAAK,MAAM;AAC1B,WAAO;AAAA,EACR;AAAA,EAED,IAAI,IAAI;AACN,SAAK,GAAG,gBAAgB,KAAK,UAAU,OAAQ;AAC/C,SAAK,GAAG,QAAQ,WAAW,MAAM;AAC/B,aAAO,OAAO,cAAc,GAAI;AAAA,IACtC,GAAO,KAAK,GAAG,YAAY;AAAA,EACxB;AACH;AAEA,aAAa,QAAQ,UAAQ;AAC3B,YAAU,UAAU,IAAI,IAAI,YAAa,MAAM;AAC7C,SAAK,UAAU,IAAI,EAAE,GAAG,IAAI;AAC5B,WAAO;AAAA,EACR;AACH,CAAC;;"}