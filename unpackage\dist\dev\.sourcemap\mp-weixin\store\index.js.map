{"version": 3, "file": "index.js", "sources": ["store/index.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { createStore } from \"vuex\";\nimport modules from \"./modules\";\nimport getters from \"./getters\";\n\nconst debug = process.env.NODE_ENV !== \"production\";\n\nexport default createStore({\n  state: {\n    statusBarHeight: uni.getSystemInfoSync().statusBarHeight,\n    titleBarHeight: 44,\n  },\n  modules,\n  getters,\n  strict: debug\n});\n"], "names": ["createStore", "uni", "modules", "getters"], "mappings": ";;;;AAcA,MAAM,QAAQ;AAEd,MAAA,QAAeA,0BAAY;AAAA,EACzB,OAAO;AAAA,IACL,iBAAiBC,cAAAA,MAAI,kBAAA,EAAoB;AAAA,IACzC,gBAAgB;AAAA,EAClB;AAAA,EAAA,SACAC,oBAAA;AAAA,EAAA,SACAC,cAAA;AAAA,EACA,QAAQ;AACV,CAAC;;"}