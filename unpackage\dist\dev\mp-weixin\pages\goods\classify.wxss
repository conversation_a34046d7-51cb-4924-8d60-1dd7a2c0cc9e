
page {
  background: #f8f8f8;
}
.nav-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.nav-box .nav-back {
  padding: 0 30rpx;
}
.bg-mk, .bg-mk2, .bg-img {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.bg-mk {
  z-index: -1;
  background: linear-gradient(to left, rgba(255, 255, 255, .3), #fff);
}
.bg-mk2 {
  z-index: -1;
  background: linear-gradient(to bottom, rgba(255, 255, 255, .3), #fff, #fff);
}
.bg-img {
  z-index: -2;
}
.nav-box .classify-scroll {
  width: 100%;
  white-space: nowrap;
  transition: all .3s ease-in-out;
  overflow: hidden;
}
.classify-scroll .one-box {
  width: calc(100% - 30rpx);
  padding: 0 15rpx;
  height: 100rpx;
}
.one-box .one-item {
  flex-shrink: 0;
  margin: 0 15rpx;
  padding: 0 30rpx;
  height: 68rpx;
  font-size: 20rpx;
  font-weight: 700;
  background: #f8f8f8;
  border-radius: 68rpx;
  justify-content: center;
}
.one-box .active {
  color: #fff;
  background: #000;
}
.classify-scroll .two-box {
  width: 100%;
  height: 68rpx;
}
.two-box .two-item {
  flex-shrink: 0;
  padding: 0 30rpx;
  height: 68rpx;
  line-height: 68rpx;
  font-size: 24rpx;
  font-weight: 700;
  transition: color .3s ease-in-out;
}
.content-box {
  width: 100%;
  transition: all .3s ease-in-out;
}
.goods-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.goods-box .goods-item {
  width: calc(50% - 15rpx);
  margin: 10rpx 0 0 10rpx;
  background: #fff;
  border-radius: 8rpx;
  overflow: hidden;
}
.goods-item .goods-img {
  width: 100%;
  padding-top: 100%;
  position: relative;
}
.goods-img .goods-img-item {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.goods-item .goods-name {
  width: calc(100% - 40rpx);
  margin: 15rpx 20rpx;
  font-size: 26rpx;
  line-height: 36rpx;
  font-weight: 500;
}
.goods-item .goods-price {
  width: calc(100% - 30rpx);
  margin: 0 20rpx 20rpx;
  display: flex;
  align-items: flex-end;
}
.goods-price .price-h {
  margin-left: 15rpx;
  color: #999;
  font-size: 20rpx;
  line-height: 20rpx;
}
.goods-item .goods-tag {
  width: calc(100% - 30rpx);
  margin: 0 15rpx 15rpx;
  display: flex;
  flex-wrap: wrap;
}
.goods-tag .tag-item {
  margin: 0 5rpx 5rpx;
  height: 40rpx;
  padding: 0 12rpx;
  line-height: 40rpx;
  font-size: 18rpx;
  font-weight: 500;
  background: #f8f8f8;
  border-radius: 8rpx;
}

/* 空状态样式 */
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-box image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-box .e1 {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.empty-box .e2 {
  font-size: 24rpx;
  color: #999;
}

/* 辅助类 */
.df {
  display: flex;
  align-items: center;
}
.ohto2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.heio {
  width: 100%;
  justify-content: center;
  transition: all .3s ease-in-out;
  overflow: hidden;
}
