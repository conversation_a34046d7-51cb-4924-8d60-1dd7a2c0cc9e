
/* 基础样式 - 参考 details.vue 的 empty-box 样式 */
.empty-page.data-v-5cea664a {
  width: 100%;
  padding: 100rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.empty-content.data-v-5cea664a {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

/* 图片样式 - 参考 details.vue */
.empty-image.data-v-5cea664a {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 30rpx;
  opacity: 0.8;
}

/* 标题样式 - 参考 details.vue 的 .e1 */
.empty-title.data-v-5cea664a {
  font-size: 30rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

/* 描述样式 - 参考 details.vue 的 .e2 */
.empty-description.data-v-5cea664a {
  margin-top: 10rpx;
  color: #999;
  font-size: 26rpx;
  line-height: 1.5;
  margin-bottom: 40rpx;
  max-width: 500rpx;
  text-align: center;
}

/* 按钮样式 - 参考 details.vue 的 retry-btn */
.empty-button.data-v-5cea664a {
  margin-top: 40rpx;
  width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 700;
  color: #fff;
  background: #007aff;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}
.empty-button.data-v-5cea664a:active {
  background: #0056cc;
  transform: scale(0.95);
}

/* 工具类 */
.df.data-v-5cea664a {
  display: flex;
  align-items: center;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
.empty-title.data-v-5cea664a {
    color: #fff;
}
.empty-description.data-v-5cea664a {
    color: #ccc;
}
.empty-page.data-v-5cea664a {
    background-color: #1a1a1a;
}
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.empty-image.data-v-5cea664a {
    width: 250rpx;
    height: 250rpx;
}
.empty-title.data-v-5cea664a {
    font-size: 28rpx;
}
.empty-description.data-v-5cea664a {
    font-size: 24rpx;
    padding: 0 40rpx;
}
}

/* 小程序兼容性 */
.empty-page.data-v-5cea664a {
  box-sizing: border-box;
}


/* H5 兼容性 */






