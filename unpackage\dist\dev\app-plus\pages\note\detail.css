
.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.bar-box .bar-back {
  padding: 0 0.9375rem;
  width: 1.0625rem;
  height: 100%;
}
.bar-box .bar-title {
  max-width: 60%;
  font-size: 1rem;
  font-weight: 700;
  flex: 1;
  text-align: center;
}
.nav-action {
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.125rem;
  padding: 0 0.9375rem;
}
.content-box {
  width: calc(100% - 1.875rem);
  padding: 0.9375rem;
}

/* 加载中状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 1.875rem;
  margin-bottom: 0.625rem;
}
.loading-indicator {
  width: 0.9375rem;
  height: 0.9375rem;
  border: 0.09375rem solid #f3f3f3;
  border-top: 0.09375rem solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.75rem 1.25rem;
  text-align: center;
}
.error-icon {
  font-size: 3.75rem;
  margin-bottom: 0.9375rem;
}
.error-text {
  font-size: 0.875rem;
  color: #999;
  margin-bottom: 1.25rem;
}
.error-btn {
  padding: 0.625rem 1.25rem;
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  border-radius: 0.78125rem;
  color: white;
}
.content {
  width: 100%;
}
.box-card {
  background: #fff;
  border-radius: 0.5rem;
  padding: 0.9375rem;
  margin-bottom: 0.9375rem;
}
.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 0.9375rem;
}
.avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.625rem;
}
.avatar-img {
  width: 100%;
  height: 100%;
}
.user-details {
  flex: 1;
}
.username {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}
.user-meta {
  font-size: 0.75rem;
  color: #999;
}
.type-tag {
  padding: 0.25rem 0.5rem;
  border-radius: 0.625rem;
  font-size: 0.75rem;
}
.type-1 { background: #e3f2fd; color: #1976d2;
}
.type-2 { background: #fce4ec; color: #c2185b;
}
.type-3 { background: #f3e5f5; color: #7b1fa2;
}
.type-4 { background: #e8f5e8; color: #388e3c;
}
.box-content {
  margin-bottom: 0.625rem;
}
.content-text {
  font-size: 0.9375rem;
  line-height: 1.6;
  color: #333;
}
.voice-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.25rem;
  background: #f5f5f5;
  border-radius: 0.375rem;
}
.voice-player {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  font-size: 0.875rem;
}
.time-info {
  text-align: right;
}
.time-text {
  font-size: 0.75rem;
  color: #999;
}
.response-section {
  background: #fff;
  border-radius: 0.5rem;
  padding: 0.9375rem;
}
.section-title {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 0.9375rem;
}

/* 隐私保护提示 */
.privacy-notice {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 0.03125rem solid #e9ecef;
  border-radius: 0.375rem;
  padding: 0.625rem;
  margin-bottom: 0.9375rem;
}
.notice-icon {
  font-size: 0.875rem;
  margin-right: 0.375rem;
  color: #6c757d;
}
.notice-text {
  font-size: 0.75rem;
  color: #6c757d;
  line-height: 1.4;
}
.response-item {
  padding: 0.625rem 0;
  border-bottom: 0.03125rem solid #f0f0f0;
}
.response-user {
  display: flex;
  align-items: center;
  margin-bottom: 0.46875rem;
}
.response-avatar {
  width: 1.875rem;
  height: 1.875rem;
  border-radius: 50%;
  margin-right: 0.46875rem;
}
.response-username {
  font-size: 0.875rem;
  font-weight: bold;
  margin-right: 0.625rem;
}
.response-time {
  font-size: 0.75rem;
  color: #999;
}
.my-response-tag {
  font-size: 0.625rem;
  color: #007aff;
  background: #e3f2fd;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  margin-left: 0.375rem;
}
.response-content {
  font-size: 0.875rem;
  line-height: 1.5;
  color: #333;
}
.response-input {
  margin-top: 0.9375rem;
  padding-top: 0.9375rem;
  border-top: 0.03125rem solid #f0f0f0;
}
.input-area {
  width: 100%;
  min-height: 3.75rem;
  padding: 0.625rem;
  background: #f8f9fa;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  margin-bottom: 0.625rem;
}
.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.char-count {
  font-size: 0.75rem;
  color: #999;
}
.send-btn {
  padding: 0.5rem 1rem;
  background: #e9ecef;
  border-radius: 0.625rem;
  color: #999;
  font-size: 0.875rem;
}
.send-btn.active {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  color: white;
}
.df {
  display: flex;
  align-items: center;
}
.bfw {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,.8);
}
.bfh {
  background: #000;
  color: #fff;
  padding: 0.625rem 1.25rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 700;
}
.tips-box {
  justify-content: center;
  width: 100%;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
