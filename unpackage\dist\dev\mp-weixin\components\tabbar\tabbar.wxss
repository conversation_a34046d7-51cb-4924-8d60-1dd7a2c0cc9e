
/* APP端兼容性优化 - 移除CSS变量，使用具体值 */
.tabbar{
  position: fixed;
  z-index: 998;
  width: 100%;
  /* APP端兼容性：条件编译底部安全区域 */




  bottom: max(env(safe-area-inset-bottom), 50rpx);

  box-sizing: border-box;
  justify-content: center;
  pointer-events: none; /* 优化性能，只有子元素可点击 */
}
.tabbar-box{
  z-index: 998;
  width: calc(100% - 120rpx);
  height: 100rpx;
  border-radius: 50rpx;
  justify-content: space-around;
  /* APP端兼容性：条件编译backdrop-filter */

  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  pointer-events: auto; /* 恢复点击事件 */
  transition: all 0.3s ease; /* 添加过渡动画 */
}
.tb-bs{
  box-shadow: 0 0 24rpx rgba(0, 0, 0, 0.06);
  border: 1px solid #f8f8f8;
}
.tabbar-box .tabbar-item{
  width: 20%;
  height: 100rpx;
  justify-content: center;
  position: relative;
  transition: transform 0.2s ease; /* 添加点击反馈动画 */
  cursor: pointer;
}

/* 小程序兼容性：使用class替代:active伪类 */
.tabbar-item.active-state {
  transform: scale(0.95); /* 点击时缩放效果 */
}
.tabbar-item .icon{
  width: 48rpx;
  height: 48rpx;
  transition: opacity 0.2s ease; /* 图标切换动画 */
}
.tabbar-item .msg{
  position: absolute;
  top: 18rpx;
  left: calc(50% + 8rpx);
  min-width: 34rpx;
  height: 34rpx;
  line-height: 34rpx;
  text-align: center;
  font-size: 18rpx;
  font-weight: 700;
  color: #fff;
  background: #fa5150;
  border-radius: 34rpx;
  border: 2rpx solid #fff;
}
.tabbar-item .add{
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  justify-content: center;
  transition: all 0.3s ease-in-out;
}
.tabbar-item .add image{
  width: 16rpx;
  height: 16rpx;
}
.tabbar .tabbar-add{
  position: fixed;
  z-index: 997;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  background-image: linear-gradient(to bottom, rgb(173 173 173 / 95%), rgb(25 25 25 / 95%));
  /* APP端兼容性：条件编译backdrop-filter */

  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}
.content-wrapper {
  width: 100%;
  padding-top: 180rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 头部标题区域 */
.add-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 80%;
  padding-top: 90rpx;
}
.header-content {
  flex: 1;
}
.header-image {
  width: 300rpx;
  height: 300rpx;
}
.header-image image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}
.add-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
  display: flex;
  align-items: center;
}
.add-plus {
  margin-left: 8rpx;
  font-weight: normal;
}
.add-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 8rpx;
}

/* 新增卡片样式 */
.card-container {
  width: 80%;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-top: 240rpx;
  pointer-events: auto; /* 确保容器可以传递点击事件 */
}

/* 卡片通用样式 */
.card {
  display: flex;
  align-items: center;
  border-radius: 20rpx;
  padding: 30rpx;
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  pointer-events: auto; /* 确保卡片可以被点击 */
}

/* 小程序兼容性：使用class替代:active伪类 */
.card.active-state {
  transform: scale(0.98);
  background-color: rgba(255, 255, 255, 0.15);
}
.cream-card {
  background-color: rgba(255, 255, 255, 0.15);
}
.mint-card {
  background-color: rgba(255, 255, 255, 0.15);
}
.card-left {
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.card-left image {
  width: 40rpx;
  height: 40rpx;
}
.card-content {
  flex: 1;
}
.card-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
}
.card-subtitle {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 6rpx;
}
.card-right {
  width: 36rpx;
  height: 36rpx;
  transform: rotate(180deg);
}
.card-right image {
  width: 100%;
  height: 100%;
}

/* 两列卡片布局 */
.two-column-container {
  display: flex;
  width: 100%;
  gap: 20rpx;
}
.two-column-card {
  flex: 1;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  cursor: pointer;
  pointer-events: auto; /* 确保两列卡片可以被点击 */
}

/* 小程序兼容性：使用class替代:active伪类 */
.two-column-card.active-state {
  transform: scale(0.98);
}
.video-card {
  background-color: rgba(76, 130, 219, 0.3);
}
.audio-card {
  background-color: rgba(245, 166, 35, 0.3);
}
.card-content-left {
  flex: 1;
}
.two-column-card-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #FFFFFF;
}
.two-column-card-desc {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 6rpx;
}
.card-content-right {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.card-content-right image {
  width: 40rpx;
  height: 40rpx;
}
.close-btn image {
  width: 24rpx;
  height: 24rpx;
}
.df{
  display: flex;
  align-items: center;
}
.bfh{
  background: rgba(0, 0, 0, 0.8);
}
.bfw{
  background: #fff;
}

/* 动画优化 - APP端兼容性 */
.fade-in{
  animation: fadeIn 0.3s forwards;
}
.fade-out{
  animation: fadeOut 0.3s forwards;
}
@keyframes fadeIn{
from{
    opacity: 0;
    transform: translateY(10rpx);
}
to{
    opacity: 1;
    transform: translateY(0);
}
}
@keyframes fadeOut{
from{
    opacity: 1;
    transform: translateY(0);
}
to{
    opacity: 0;
    transform: translateY(10rpx);
}
}
