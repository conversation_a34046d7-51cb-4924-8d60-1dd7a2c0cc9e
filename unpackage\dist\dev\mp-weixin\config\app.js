"use strict";
const config = {
  // 小程序 / APP请求配置
  // 请求域名 格式： https://您的域名
  HTTP_REQUEST_URL: `http://nas4.weiyun6.com:8033`,
  //HTTP_REQUEST_URL: `http://192.168.31.62`,
  // H5请求配置
  // 后台版本号
  SYSTEM_VERSION: 540,
  // 以下配置在不做二开的前提下,不需要做任何的修改
  HEADER: {
    "content-type": "application/json",
    "Form-type": "routine"
  },
  // 回话密钥名称 请勿修改此配置
  TOKENNAME: "Authori-zation",
  // 缓存时间 0 永久
  EXPIRE: 0,
  //分页最多显示条数
  LIMIT: 10,
  // 请求超时限制 默认10秒
  TIMEOUT: 1e4
};
exports.config = config;
//# sourceMappingURL=../../.sourcemap/mp-weixin/config/app.js.map
