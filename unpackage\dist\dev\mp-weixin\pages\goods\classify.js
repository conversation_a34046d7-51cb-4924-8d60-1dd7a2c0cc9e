"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const config_api = require("../../config/api.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const money = () => "../../components/money/money.js";
getApp();
const _sfc_main = {
  components: {
    lazyImage,
    uniLoadMore,
    money
  },
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      isOrder: false,
      navbarTrans: 0,
      classifyList: [
        {
          id: 1,
          name: "推荐",
          img: "",
          children: []
        }
      ],
      classifyIdx: 0,
      classifyChildrenIdx: -1,
      isThrottling: false,
      list: [],
      page: 1,
      isEmpty: false,
      loadStatus: "more",
      // Mock数据
      mockData: {
        // 分类数据
        classifyList: [
          {
            id: 1,
            name: "推荐",
            img: "/static/img/avatar.png",
            children: []
          },
          {
            id: 2,
            name: "女装",
            img: "/static/img/avatar.png",
            children: [
              { id: 21, name: "T恤" },
              { id: 22, name: "裙装" },
              { id: 23, name: "裤装" },
              { id: 24, name: "套装" },
              { id: 25, name: "外套" }
            ]
          },
          {
            id: 3,
            name: "男装",
            img: "/static/img/avatar.png",
            children: [
              { id: 31, name: "T恤" },
              { id: 32, name: "裤装" },
              { id: 33, name: "衬衫" },
              { id: 34, name: "外套" }
            ]
          },
          {
            id: 4,
            name: "鞋包",
            img: "/static/img/avatar.png",
            children: [
              { id: 41, name: "女鞋" },
              { id: 42, name: "男鞋" },
              { id: 43, name: "双肩包" },
              { id: 44, name: "手提包" }
            ]
          },
          {
            id: 5,
            name: "配饰",
            img: "/static/img/avatar.png",
            children: [
              { id: 51, name: "项链" },
              { id: 52, name: "手链" },
              { id: 53, name: "耳饰" },
              { id: 54, name: "帽子" }
            ]
          }
        ],
        // 商品列表数据
        goodsList: {
          // 推荐商品
          1: [
            {
              id: 101,
              name: "夏季新款连衣裙气质淑女温柔风碎花裙",
              imgs: ["/static/img/avatar.png"],
              product: {
                price: "169.00",
                line_price: "269.00"
              },
              tags: ["新品", "包邮"],
              buy: 156,
              cart: 20,
              browse: 358
            },
            {
              id: 102,
              name: "高腰阔腿牛仔裤女夏季薄款宽松直筒",
              imgs: ["/static/img/avatar.png"],
              product: {
                price: "128.00",
                line_price: "198.00"
              },
              tags: ["热卖", "包邮"],
              buy: 239,
              cart: 45,
              browse: 520
            },
            {
              id: 103,
              name: "小香风短袖t恤女2023新款夏季宽松上衣",
              imgs: ["/static/img/avatar.png"],
              product: {
                price: "79.00",
                line_price: "129.00"
              },
              tags: ["折扣", "包邮"],
              buy: 315,
              cart: 67,
              browse: 782
            },
            {
              id: 104,
              name: "轻奢品牌小白鞋女夏季新款透气板鞋",
              imgs: ["/static/img/avatar.png"],
              product: {
                price: "299.00",
                line_price: "459.00"
              },
              tags: ["限量", "包邮"],
              buy: 98,
              cart: 32,
              browse: 256
            }
          ],
          // 女装T恤
          21: [
            {
              id: 201,
              name: "纯棉短袖T恤女装夏季薄款宽松圆领打底衫",
              imgs: ["/static/img/avatar.png"],
              product: {
                price: "69.00",
                line_price: "99.00"
              },
              tags: ["新款", "纯棉"],
              buy: 234,
              cart: 56,
              browse: 467
            },
            {
              id: 202,
              name: "设计感小众短袖T恤女装夏季新款韩版上衣",
              imgs: ["/static/img/avatar.png"],
              product: {
                price: "89.00",
                line_price: "139.00"
              },
              tags: ["ins风", "宽松"],
              buy: 178,
              cart: 43,
              browse: 385
            }
          ],
          // 男装T恤
          31: [
            {
              id: 301,
              name: "夏季薄款男士短袖T恤纯棉圆领体恤衫",
              imgs: ["/static/img/avatar.png"],
              product: {
                price: "79.00",
                line_price: "119.00"
              },
              tags: ["纯棉", "透气"],
              buy: 256,
              cart: 48,
              browse: 512
            }
          ]
        }
      }
    };
  },
  onLoad() {
    this.getClassifyList();
  },
  methods: {
    getClassifyList() {
      let that = this;
      if (config_api.api$1 && config_api.api$1.api && config_api.api$1.api.classifyListUrl) {
        utils_request.request(config_api.api$1.api.classifyListUrl).then(function(res) {
          if (res.code == 200) {
            that.classifyList = res.data;
            that.goodsList();
          }
        });
      } else {
        setTimeout(() => {
          that.classifyList = that.mockData.classifyList;
          that.goodsList();
        }, 500);
      }
    },
    goodsList() {
      let that = this;
      that.isEmpty = false;
      let classifyId = that.classifyList[that.classifyIdx].id;
      if (that.classifyChildrenIdx != -1) {
        classifyId = that.classifyList[that.classifyIdx].children[that.classifyChildrenIdx].id;
      }
      if (config_api.api$1 && config_api.api$1.api && config_api.api$1.api.goodsListUrl) {
        utils_request.request(config_api.api$1.api.goodsListUrl, {
          page: that.page,
          classify_type: that.classifyChildrenIdx != -1 ? 1 : 0,
          classify_id: classifyId
        }).then(function(res) {
          that.isThrottling = true;
          that.loadStatus = "more";
          if (res.data.data.length > 0) {
            if (that.page == 1) {
              that.list = res.data.data;
            } else {
              that.list = that.list.concat(res.data.data);
            }
            that.page = res.data.current_page;
          } else if (that.page == 1) {
            that.isEmpty = true;
          } else {
            that.loadStatus = "no-more";
          }
        });
      } else {
        setTimeout(() => {
          that.isThrottling = true;
          that.loadStatus = "more";
          let mockGoods = that.mockData.goodsList[classifyId] || [];
          const pageSize = 4;
          const startIndex = (that.page - 1) * pageSize;
          const endIndex = startIndex + pageSize;
          const pageData = mockGoods.slice(startIndex, endIndex);
          if (pageData.length > 0) {
            if (that.page == 1) {
              that.list = pageData;
            } else {
              that.list = that.list.concat(pageData);
            }
          } else if (that.page == 1) {
            that.isEmpty = true;
          } else {
            that.loadStatus = "no-more";
          }
        }, 500);
      }
    },
    classifyClick(e) {
      let dataset = e.currentTarget.dataset;
      if (dataset.type == 1) {
        this.classifyIdx = dataset.idx;
        this.classifyChildrenIdx = -1;
      }
      if (dataset.type == 2) {
        this.classifyChildrenIdx = dataset.idx;
      }
      this.isThrottling = false;
      this.page = 1;
      this.goodsList();
    },
    navigateToFun(e) {
      common_vendor.index.navigateTo({
        url: "/pages/" + e.currentTarget.dataset.url
      });
    },
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "/pages/tabbar/shop"
        });
      }
    }
  },
  onPageScroll(e) {
    var opacity = (e.scrollTop > 150 ? 150 : e.scrollTop) / 150;
    this.navbarTrans = opacity;
  },
  onReachBottom() {
    if (this.list.length) {
      this.loadStatus = "loading";
      this.page = this.page + 1;
      this.goodsList();
    }
  }
};
if (!Array) {
  const _component_lazy_image = common_vendor.resolveComponent("lazy-image");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _component_money = common_vendor.resolveComponent("money");
  (_component_lazy_image + _easycom_uni_load_more2 + _component_money)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
if (!Math) {
  _easycom_uni_load_more();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.classifyList[$data.classifyIdx].img
  }, $data.classifyList[$data.classifyIdx].img ? {
    b: common_vendor.p({
      src: $data.classifyList[$data.classifyIdx].img
    })
  } : {
    c: common_vendor.p({
      src: "/static/img/inset/map.jpg"
    })
  }, {
    d: common_assets._imports_0$7,
    e: $data.titleBarHeight + "px",
    f: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    g: common_vendor.f($data.classifyList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: index,
        c: common_vendor.n(index == $data.classifyIdx ? "active" : ""),
        d: index,
        e: common_vendor.o((...args) => $options.classifyClick && $options.classifyClick(...args), index)
      };
    }),
    h: common_vendor.f($data.classifyList[$data.classifyIdx].children, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: index,
        c: index == $data.classifyChildrenIdx ? "#000" : "#999",
        d: index,
        e: common_vendor.o((...args) => $options.classifyClick && $options.classifyClick(...args), index)
      };
    }),
    i: $data.classifyList[$data.classifyIdx].children.length ? "68rpx" : "0px",
    j: $data.statusBarHeight + "px",
    k: "rgba(255, 255, 255," + $data.navbarTrans + ")"
  }, {
    l: common_vendor.p({
      status: "loading"
    })
  }, {
    m: $data.isThrottling || $data.loadStatus == "loading" ? "0px" : "60rpx",
    n: !$data.isEmpty
  }, !$data.isEmpty ? {
    o: common_vendor.f($data.list, (item, index, i0) => {
      return {
        a: "30b8d6b2-3-" + i0,
        b: common_vendor.p({
          src: item.imgs[0]
        }),
        c: common_vendor.t(item.name),
        d: "30b8d6b2-4-" + i0,
        e: common_vendor.p({
          price: item.product.price
        }),
        f: common_vendor.t(item.product.line_price),
        g: common_vendor.t(item.buy ? item.buy + "人已买" : item.cart + item.browse + "人想买"),
        h: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        i: index,
        j: "goods/details?id=" + item.id,
        k: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), index)
      };
    })
  } : {}, {
    p: $data.isEmpty
  }, $data.isEmpty ? {
    q: common_assets._imports_3$1
  } : {}, {
    r: common_vendor.p({
      status: $data.loadStatus
    }),
    s: $data.classifyList[$data.classifyIdx].children.length ? "calc(" + ($data.statusBarHeight + $data.titleBarHeight) + "px + 168rpx)" : "calc(" + ($data.statusBarHeight + $data.titleBarHeight) + "px + 100rpx)"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 1;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/goods/classify.js.map
