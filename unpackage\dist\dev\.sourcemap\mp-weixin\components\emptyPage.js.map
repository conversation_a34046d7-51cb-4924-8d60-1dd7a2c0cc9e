{"version": 3, "file": "emptyPage.js", "sources": ["components/emptyPage.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/WjovV1dXL3NoZWppYW8vdnVlMy9jb21wb25lbnRzL2VtcHR5UGFnZS52dWU"], "sourcesContent": ["<template>\n  <view class=\"empty-page\">\n    <view class=\"empty-icon\">\n      <text class=\"icon\">📝</text>\n    </view>\n    <view class=\"empty-title\">\n      <text>{{ title || '暂无数据' }}</text>\n    </view>\n    <view v-if=\"description\" class=\"empty-description\">\n      <text>{{ description }}</text>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'EmptyPage',\n  props: {\n    title: {\n      type: String,\n      default: '暂无数据'\n    },\n    description: {\n      type: String,\n      default: ''\n    },\n    icon: {\n      type: String,\n      default: '📝'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.empty-page {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 100rpx 40rpx;\n  min-height: 400rpx;\n}\n\n.empty-icon {\n  margin-bottom: 30rpx;\n}\n\n.empty-icon .icon {\n  font-size: 120rpx;\n  opacity: 0.3;\n}\n\n.empty-title {\n  margin-bottom: 20rpx;\n}\n\n.empty-title text {\n  font-size: 28rpx;\n  color: #999;\n  text-align: center;\n}\n\n.empty-description {\n  text-align: center;\n}\n\n.empty-description text {\n  font-size: 24rpx;\n  color: #ccc;\n  line-height: 1.5;\n}\n</style>\n", "import Component from 'Z:/WWW/shejiao/vue3/components/emptyPage.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AAeA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AACF;;;;;;;;;;AC9BA,GAAG,gBAAgB,SAAS;"}