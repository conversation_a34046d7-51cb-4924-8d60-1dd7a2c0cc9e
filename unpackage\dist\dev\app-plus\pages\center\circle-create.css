
.container[data-v-531fb3c5] {
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 导航栏 */
.nav-bar[data-v-531fb3c5] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 2.75rem;
  padding: 0 0.9375rem;
  background-color: #fff;
  border-bottom: 0.03125rem solid #f0f0f0;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 999;
}
.nav-back[data-v-531fb3c5] {
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon[data-v-531fb3c5] {
  width: 1rem;
  height: 1rem;
}
.nav-title[data-v-531fb3c5] {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}
.nav-right[data-v-531fb3c5] {
  width: 1.875rem;
}

/* 表单容器 */
.form-container[data-v-531fb3c5] {
  background-color: #fff;
  border-radius: 0.5rem;
  padding: 1.25rem 0.9375rem;
}
.form-item[data-v-531fb3c5] {
  margin-bottom: 1.25rem;
  position: relative;
}
.form-item[data-v-531fb3c5]:last-child {
  margin-bottom: 0;
}
.form-label[data-v-531fb3c5] {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.625rem;
  color: #333;
}

/* 上传区域 */
.upload-row[data-v-531fb3c5] {
  display: flex;
  gap: 0.9375rem;
}
.upload-item[data-v-531fb3c5] {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 头像区域占1份 */
.avatar-item[data-v-531fb3c5] {
  flex: 1;
}

/* 背景区域占3份 */
.background-item[data-v-531fb3c5] {
  flex: 3;
}
.upload-box[data-v-531fb3c5] {
  width: 100%;
  height: 5rem;
  border-radius: 0.375rem;
  overflow: hidden;
  background-color: #f8f8f8;
  border: 0.0625rem dashed #ddd;
  transition: all 0.3s ease;
}
.upload-box[data-v-531fb3c5]:active {
  transform: scale(0.98);
}
.avatar-box[data-v-531fb3c5] {
  border-radius: 0.625rem;
}
.background-box[data-v-531fb3c5] {
  border-radius: 0.375rem;
}
.upload-image[data-v-531fb3c5] {
  width: 100%;
  height: 100%;
}
.upload-placeholder[data-v-531fb3c5] {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-icon[data-v-531fb3c5] {
  font-size: 1.5rem;
  font-weight: 300;
  color: #999;
  margin-bottom: 0.25rem;
}
.upload-text[data-v-531fb3c5] {
  font-size: 0.625rem;
  color: #999;
  text-align: center;
}
.upload-label[data-v-531fb3c5] {
  font-size: 0.75rem;
  color: #666;
  margin-top: 0.5rem;
  text-align: center;
}

/* 输入框 */
.form-input[data-v-531fb3c5] {
  width: 100%;
  height: 2.75rem;
  border-radius: 0.375rem;
  background-color: #f8f8f8;
  padding: 0 0.75rem;
  font-size: 0.875rem;
  color: #333;
  box-sizing: border-box;
  border: 0.0625rem solid transparent;
  transition: all 0.3s ease;
}
.form-input[data-v-531fb3c5]:focus {
  background-color: #fff;
  border-color: #333;
}
.form-textarea[data-v-531fb3c5] {
  width: 100%;
  min-height: 5rem;
  border-radius: 0.375rem;
  background-color: #f8f8f8;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #333;
  box-sizing: border-box;
  border: 0.0625rem solid transparent;
  transition: all 0.3s ease;
  line-height: 1.6;
}
.form-textarea[data-v-531fb3c5]:focus {
  background-color: #fff;
  border-color: #333;
}
.form-count[data-v-531fb3c5] {
  position: absolute;
  right: 0.75rem;
  bottom: 0.5rem;
  font-size: 0.75rem;
  color: #999;
}

/* 提交按钮 */
.btn-container[data-v-531fb3c5] {
  margin: 1.25rem 0.625rem;
}
.submit-btn[data-v-531fb3c5] {
  width: 100%;
  height: 3rem;
  line-height: 3rem;
  background: linear-gradient(135deg, #333 0%, #555 100%);
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 1.5rem;
  border: none;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}
.submit-btn[data-v-531fb3c5]:active {
  transform: translateY(0.0625rem);
  box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.15);
}
.submit-btn[disabled][data-v-531fb3c5] {
  background: #ccc;
  color: #999;
  box-shadow: none;
  transform: none;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
.upload-row[data-v-531fb3c5] {
    gap: 0.625rem;
}
.upload-box[data-v-531fb3c5] {
    height: 4.375rem;
}
.upload-icon[data-v-531fb3c5] {
    font-size: 1.25rem;
}
.upload-text[data-v-531fb3c5] {
    font-size: 0.5625rem;
}
}
