
  /* 基础样式 */
.nav-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 99;
    box-sizing: border-box;
}
.nav-box .nav-back {
    padding: 0 30rpx;
    width: 34rpx;
    height: 100%;
}
.nav-box .nav-title {
    max-width: 60%;
    font-size: 32rpx;
    font-weight: 700;
}
  
  /* 圈子头部区域 - 参考商品轮播图设计 */
.circle-header {
    position: relative;
    overflow: hidden;
}
  
  /* 主背景容器 - 参考商品轮播图样式 */
.images-box {
    width: 100%;
    flex-direction: column;
    position: relative;
    background: #f8f8f8;
    border-radius: 0 0 24rpx 24rpx;
    overflow: hidden;
}
  
  /* 圈子背景图片容器 - 沉浸式适中高度 */
.circle-image-container {
    position: relative;
    width: 100%;
    height: 600rpx; /* 500rpx + 状态栏和导航栏高度 */
    background: #f8f8f8;
    overflow: hidden;
}
  
  /* 圈子背景图片 */
.circle-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    will-change: transform; /* 优化动画性能 */
}

  /* 默认背景 */
.default-bg {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
  
  /* 图片点击效果 */
.circle-image-container:active .circle-bg-image {
    transform: scale(0.98);
}
  
  /* 装饰指示器 - 参考商品轮播图指示器 */
.circle-indicator {
    position: absolute;
    bottom: 30rpx;
    right: 30rpx;
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20rpx;
    backdrop-filter: blur(10rpx);
    -webkit-backdrop-filter: blur(10rpx);
}
.indicator-dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
}
.indicator-dot.active {
    background: #fff;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}
  
  /* 沉浸式背景遮罩层 */
.bg-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
      /* 主要渐变 - 从透明到深色 */
      linear-gradient(to bottom, 
        transparent 0%, 
        rgba(0,0,0,0.1) 30%, 
        rgba(0,0,0,0.4) 70%, 
        rgba(0,0,0,0.8) 100%
      ),
      /* 径向渐变 - 增强中心聚焦效果 */
      radial-gradient(circle at center bottom, 
        rgba(0,0,0,0.2) 0%, 
        rgba(0,0,0,0.6) 70%, 
        rgba(0,0,0,0.9) 100%
      ),
      /* 顶部微妙渐变 - 增强层次感 */
      linear-gradient(to bottom,
        rgba(0,0,0,0.1) 0%,
        transparent 25%,
        transparent 75%,
        rgba(0,0,0,0.2) 100%
      );
    
    /* 现代模糊效果 - 增强沉浸感 */
    backdrop-filter: blur(1px) saturate(180%);
    -webkit-backdrop-filter: blur(1px) saturate(180%);
    
    /* 微妙的边缘阴影 */
    box-shadow: 
      inset 0 -80rpx 120rpx -40rpx rgba(0,0,0,0.4),
      inset 0 80rpx 120rpx -40rpx rgba(0,0,0,0.1);
    
    /* 平滑过渡效果 */
    transition: all 0.3s ease-out;
}
  
  /* 悬停时增强效果（如果需要交互） */
.circle-header:hover .bg-overlay {
    backdrop-filter: blur(2px) saturate(200%);
    -webkit-backdrop-filter: blur(2px) saturate(200%);
}
  
  /* 圈子信息叠加层 - 居中显示内容 */
.circle-info-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 40rpx 30rpx 30rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center; /* 垂直居中 */
}
.circle-main-info {
    align-items: flex-end;
    margin-bottom: 20rpx;
}
  
  /* 圈子头像 */
.circle-avatar {
    margin-right: 20rpx;
}
.avatar-img {
    width: 120rpx;
    height: 120rpx;
    border-radius: 16rpx;
    border: 4rpx solid rgba(255,255,255,0.3);
}
  
  /* 圈子详情 */
.circle-details {
    flex: 1;
}
.circle-name {
    color: #fff;
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}
.circle-stats {
    color: rgba(255,255,255,0.9);
    font-size: 22rpx;
    line-height: 1.4;
}
.stat-item {
    font-size: 22rpx;
    font-weight: 400;
    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}
.stat-divider {
    margin: 0 8rpx;
    font-size: 22rpx;
}
  
  /* 圈子描述和公告 */
.circle-description,
  .circle-notice {
    color: rgba(255,255,255,0.9);
    font-size: 24rpx;
    line-height: 1.4;
    margin-bottom: 15rpx;
    text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}
.circle-notice {
    color: #ffeb3b;
}
  
  /* 操作按钮 - 参考 pages/note/1.vue 设计 */
.btn-box {
    width: 100%;
    height: 80rpx;
    justify-content: space-between;
    margin-top: 30rpx;
}
.btn-box .btn {
    height: 80rpx;
    font-size: 24rpx;
    font-weight: 700;
    justify-content: center;
    border-radius: 80rpx;
    transition: all 0.3s ease;
    transform: translateZ(0); /* 启用硬件加速 */
    will-change: transform, background-color;
}
.btn-box .btn:active {
    transform: scale(0.98);
}
.btn-box .btn .icon {
    width: 32rpx;
    height: 32rpx;
}
.btn-box .btn text {
    margin: 0 12rpx;
    white-space: nowrap;
}
.btn-box .bg1 {
    width: calc(100% - 100rpx);
    color: #000;
    background: #fff;
}
.btn-box .bg2 {
    width: 80rpx;
    background: #f8f8f8;
}
.btn-box .joined-state {
    color: #000;
    background: #f8f8f8;
    width: calc(50% - 10rpx);
}
.btn-box .share-expanded {
    width: calc(50% - 10rpx);
}
  
  /* 加载状态样式 */
.btn-box .loading {
    background: #ccc;
    color: transparent;
    pointer-events: none;
}
.loading-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
}
.loading-dots .dot {
    width: 8rpx;
    height: 8rpx;
    border-radius: 50%;
    background: #999;
    animation: loadingDot 1.4s infinite ease-in-out;
}
.loading-dots .dot:nth-child(1) {
    animation-delay: -0.32s;
}
.loading-dots .dot:nth-child(2) {
    animation-delay: -0.16s;
}
@keyframes loadingDot {
0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
}
40% {
      transform: scale(1);
      opacity: 1;
}
}
  
  /* 骨架屏样式 */
.skeleton-text {
    width: 200rpx;
    height: 36rpx;
    background: linear-gradient(90deg, rgba(255,255,255,0.3) 25%, rgba(255,255,255,0.6) 50%, rgba(255,255,255,0.3) 75%);
    background-size: 200% 100%;
    border-radius: 6rpx;
    animation: skeletonLoading 1.5s infinite;
}
.skeleton-stats {
    display: flex;
    gap: 16rpx;
}
.skeleton-item {
    width: 80rpx;
    height: 22rpx;
    background: linear-gradient(90deg, rgba(255,255,255,0.3) 25%, rgba(255,255,255,0.6) 50%, rgba(255,255,255,0.3) 75%);
    background-size: 200% 100%;
    border-radius: 4rpx;
    animation: skeletonLoading 1.5s infinite;
}
@keyframes skeletonLoading {
0% {
      background-position: -200% 0;
}
100% {
      background-position: 200% 0;
}
}
  
  /* 头像骨架屏 */
.skeleton-avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 16rpx;
    background: linear-gradient(90deg, rgba(255,255,255,0.3) 25%, rgba(255,255,255,0.6) 50%, rgba(255,255,255,0.3) 75%);
    background-size: 200% 100%;
    animation: skeletonLoading 1.5s infinite;
}
  
  /* 成员头像组样式 */
.cu-img-group {
    direction: ltr;
    unicode-bidi: bidi-override;
    display: inline-block;
}
.cu-img-group .cu-img {
    width: 32rpx;
    height: 32rpx;
    display: inline-flex;
    position: relative;
    margin-left: -16rpx;
    border: 4rpx solid #000;
    background: #111;
    vertical-align: middle;
    border-radius: 50%;
}
.cu-img-group .cu-img image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
}

  
  /* 分类标签 - 适应沉浸式设计 */
.bar-box {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 98;
    width: 100%;
    height: 80rpx;
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;
    margin-top: -24rpx; /* 抵消圆角造成的间隙 */
    border-radius: 24rpx 24rpx 0 0; /* 与图片底部圆角呼应 */
    backdrop-filter: blur(10rpx); /* 毛玻璃效果 */
    -webkit-backdrop-filter: blur(10rpx);
}
.bar-box .bar-item {
    padding: 0 30rpx;
    height: 100%;
    flex-direction: column;
    justify-content: center;
    position: relative;
}
.bar-box .bar-item text {
    font-weight: 700;
    transition: all 0.3s ease-in-out;
}
.bar-item .bar-line {
    position: absolute;
    bottom: 12rpx;
    width: 18rpx;
    height: 6rpx;
    border-radius: 6rpx;
    background: #000;
    transition: opacity 0.3s ease-in-out;
}
  
  /* 内容区域 */
.content-area {
    min-height: 600rpx;
    background: #fff;
}
.dynamic-box {
    width: calc(100% - 16rpx);
    padding: 22rpx 8rpx 0;
}
  
  /* 加载中状态样式 */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60rpx;
    margin: 20rpx 0;
}
.loading-indicator {
    width: 30rpx;
    height: 30rpx;
    border: 3rpx solid #f3f3f3;
    border-top: 3rpx solid #000;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
  
  /* 空状态 */
.empty-box {
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
}
.empty-box image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
}
.empty-box .e1 {
    font-size: 28rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
}
.empty-box .e2 {
    font-size: 24rpx;
    color: #999;
}
  
  /* 提示弹窗 */
.tips-box {
    padding: 20rpx 30rpx;
    border-radius: 12rpx;
    justify-content: center;
}
.tips-box .tips-item {
    color: #fff;
    font-size: 28rpx;
    font-weight: 700;
}
  
  /* 工具类 */
.df {
    display: flex;
    align-items: center;
}
.ohto {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.rule-box {
    background: #fff;
    padding: 30rpx 30rpx 0 30rpx;
}
.rule-title {
    font-size: 28rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
}
.rule-list {
    margin-bottom: 30rpx;
}
.rule-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16rpx;
}
.rule-tag {
    font-size: 20rpx;
    color: #fff;
    border-radius: 8rpx;
    padding: 4rpx 16rpx;
    margin-right: 12rpx;
    margin-top: 2rpx;
    height: 32rpx;
    line-height: 32rpx;
    flex-shrink: 0; /* 防止标签被压缩 */
    white-space: nowrap; /* 防止标签文字换行 */
}
.tag-orange {
    background: #ff9500;
}
.rule-text {
    font-size: 22rpx;
    color: #333;
    line-height: 32rpx;
    flex: 1; /* 占据剩余空间 */
    word-break: break-all; /* 允许长单词换行 */
    word-wrap: break-word; /* 兼容性更好的换行 */
}
.rule-team-title {
    margin: 30rpx 0 10rpx 0;
    font-size: 28rpx;
    font-weight: bold;
    justify-content: space-between;
}
.rule-team-all {
  margin-left:12rpx;
  padding:0 12rpx;
  height:40rpx;
  line-height:40rpx;
  font-size:20rpx;
  font-weight:700;
  color:#999;
  background:#f8f8f8;
  border-radius:8rpx
}
.rule-team-list {
    margin-bottom: 30rpx;
}
.member-item {
    padding: 0 30rpx 0 30rpx;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    position: relative;
}
.avatar {
    width: 56rpx;
    height: 56rpx;
    border-radius: 50%;
    margin-right: 16rpx;
    background: #f5f5f5;
}
.info {
    flex: 1;
    min-width: 0;
}
.name {
    font-size: 26rpx;
    font-weight: 700;
    color: #222;
    display: flex;
    align-items: center;
}
.role-tag {
    font-size: 20rpx;
    border-radius: 8rpx;
    padding: 2rpx 14rpx;
    margin-left: 12rpx;
    color: #fff;
    font-weight: 400;
}
.role-tag.owner {
    background: #ff9500;
}
.role-tag.admin {
    background: #3da0ff;
}
.meta {
    margin-top: 6rpx;
    font-size: 22rpx;
    color: #999;
    display: flex;
    align-items: center;
}
.gender {
    margin-right: 6rpx;
}
.male {
    color: #4e6ef2;
}
.female {
    color: #fa5a8a;
}
.contrib {
    color: #999;
}
  