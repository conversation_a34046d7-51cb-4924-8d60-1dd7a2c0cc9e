"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  name: "VoteComponent",
  props: {
    // 投票信息对象
    voteInfo: {
      type: Object,
      default: null
    },
    // 是否显示删除按钮（编辑模式）
    showDelete: {
      type: Boolean,
      default: false
    },
    // 是否禁用投票（已投票或其他原因）
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      voting: false
      // 投票进行中状态
    };
  },
  computed: {
    // 参与投票人数文本
    votePeopleText() {
      if (!this.voteInfo)
        return "";
      const total = this.voteInfo.total || 0;
      return this.formatNumber(total, 1e4, "万") + "人参与了投票";
    }
  },
  methods: {
    // 数字格式化
    formatNumber(num, threshold = 1e4, suffix = "w") {
      if (!num || num < threshold)
        return num.toString() || "";
      return (num / threshold).toFixed(1) + suffix;
    },
    // 处理投票点击
    async handleVote(optionId) {
      if (this.voting || this.voteInfo.user_selected || this.disabled) {
        return;
      }
      if (!this.$store.getters.isLogin) {
        common_vendor.index.navigateTo({
          url: "/pages/login/index"
        });
        return;
      }
      this.voting = true;
      try {
        const voteId = this.voteInfo.vote.id;
        const res = await api_social.vote({
          vote_id: voteId,
          option_id: optionId
        });
        if (res.status === 200 && res.data && res.data.vote_info) {
          this.$emit("vote-success", {
            voteInfo: res.data.vote_info,
            optionId
          });
          common_vendor.index.showToast({
            title: "投票成功",
            icon: "none"
          });
        } else {
          common_vendor.index.showToast({
            title: res.msg || "投票失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at components/vote-component/vote-component.vue:138", "投票失败:", error);
        common_vendor.index.showToast({
          title: error.message || "投票失败",
          icon: "none"
        });
      } finally {
        this.voting = false;
      }
    },
    // 处理删除投票
    handleDelete() {
      common_vendor.index.__f__("log", "at components/vote-component/vote-component.vue:150", "VoteComponent: handleDelete被调用");
      this.$emit("vote-delete");
      common_vendor.index.__f__("log", "at components/vote-component/vote-component.vue:153", "VoteComponent: vote-delete事件已发送");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.voteInfo
  }, $props.voteInfo ? common_vendor.e({
    b: common_assets._imports_0$23,
    c: common_vendor.t($props.voteInfo.vote.title),
    d: $props.showDelete
  }, $props.showDelete ? {
    e: common_assets._imports_0$4,
    f: common_vendor.o((...args) => $options.handleDelete && $options.handleDelete(...args))
  } : {}, {
    g: !$props.voteInfo.user_selected
  }, !$props.voteInfo.user_selected ? {
    h: common_vendor.f($props.voteInfo.options, (option, idx, i0) => {
      return {
        a: common_vendor.t(option.option_text),
        b: option.id || idx,
        c: common_vendor.o(($event) => $options.handleVote(option.id), option.id || idx)
      };
    })
  } : {
    i: common_vendor.f($props.voteInfo.options, (option, idx, i0) => {
      return common_vendor.e({
        a: $props.voteInfo.user_selected === option.id
      }, $props.voteInfo.user_selected === option.id ? {
        b: common_assets._imports_2$15
      } : {}, {
        c: common_vendor.t(option.option_text),
        d: common_vendor.t(option.percent),
        e: option.percent + "%",
        f: $props.voteInfo.user_selected === option.id ? "#ffd600" : "#eaeaea",
        g: option.id || idx
      });
    })
  }, {
    j: common_vendor.t($options.votePeopleText)
  }) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-eb14d672"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/vote-component/vote-component.js.map
