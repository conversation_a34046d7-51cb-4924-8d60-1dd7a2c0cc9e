"use strict";
const common_vendor = require("../../common/vendor.js");
const config_app = require("../../config/app.js");
const common_assets = require("../../common/assets.js");
const { HTTP_REQUEST_URL } = config_app.config;
const _sfc_main = {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    // 分享数据信息
    shareData: {
      type: Object,
      default: () => ({
        title: "默认标题",
        description: "默认描述",
        image: "/static/img/avatar.png",
        link: "",
        command_word: ""
      })
    },
    // 笔记信息
    noteInfo: {
      type: Object,
      default: () => ({})
    },
    // 当前用户ID
    userId: {
      type: [Number, String],
      default: 0
    },
    // 分享类型 goods: 商品分享, note: 笔记分享
    shareType: {
      type: String,
      default: "note"
    }
  },
  data() {
    return {
      imgHost: HTTP_REQUEST_URL,
      // 人员列表
      personList: [
        { id: 1, name: "好友1", avatar: "/static/img/avatar.png", online: true },
        { id: 2, name: "好友2", avatar: "/static/img/avatar.png", online: false },
        { id: 3, name: "好友3", avatar: "/static/img/avatar.png", online: true },
        { id: 4, name: "好友4", avatar: "/static/img/avatar.png", online: false },
        { id: 5, name: "好友5", avatar: "/static/img/avatar.png", online: true },
        { id: 6, name: "好友6", avatar: "/static/img/avatar.png", online: true },
        { id: 7, name: "好友7", avatar: "/static/img/avatar.png", online: false },
        { id: 8, name: "好友8", avatar: "/static/img/avatar.png", online: true },
        { id: 9, name: "好友9", avatar: "/static/img/avatar.png", online: false },
        { id: 10, name: "好友10", avatar: "/static/img/avatar.png", online: true }
      ],
      // 第二行分享选项 - 功能操作
      bottomItems: [],
      // 默认图标映射
      defaultIcons: {
        forward: "/static/img/fx.png",
        wechat: "/static/img/wz.png",
        moments: "/static/img/pl.png",
        qq: "/static/img/dz.png",
        qzone: "/static/img/dz1.png",
        poster: "/static/img/gd.png",
        link: "/static/img/z.png",
        copy: "/static/img/bj.png",
        dislike: "/static/img/sc.png",
        money: "/static/img/bj.png",
        report: "/static/img/setting/5.png",
        miniapp: "/static/img/xcx.png",
        edit: "/static/img/bj.png",
        delete: "/static/img/sc.png"
      },
      // 举报原因列表
      reportReasons: ["违法违规", "色情低俗", "账号诈骗", "侵犯权益", "骚扰谩骂", "内容抄袭", "其他"],
      // 海报相关数据
      posterImageStatus: false,
      posterImage: "",
      canvasStatus: false,
      H5ShareBox: false,
      // 微信状态
      weixinStatus: false,
      showWeixinButton: false
    };
  },
  computed: {
    // 动态计算底部操作项
    computedBottomItems() {
      if (this.shareType === "note") {
        const isCircle = this.noteInfo.type === "circle";
        const isOwner = this.noteInfo.uid && this.noteInfo.uid == this.userId;
        const items = [
          { name: "不喜欢", icon: "dislike", bgColor: "#f8f8f8", type: "dislike" },
          { name: "举报", icon: "report", bgColor: "#f8f8f8", type: "report" }
        ];
        if (isOwner) {
          const editText = isCircle ? "编辑圈子" : "编辑";
          const deleteText = isCircle ? "删除圈子" : "删除";
          items.splice(
            -1,
            0,
            { name: editText, icon: "edit", bgColor: "#f8f8f8", type: "edit" },
            { name: deleteText, icon: "delete", bgColor: "#f8f8f8", type: "delete" }
          );
        }
        return items;
      } else {
        return [];
      }
    },
    // 是否显示复制口令按钮
    showCopyCommand() {
      if (this.shareData.command_word) {
        return true;
      }
      if (this.shareData.title || this.shareData.link || this.noteInfo.id) {
        return true;
      }
      return false;
    }
  },
  mounted() {
    this.initClipboard();
    this.checkWeixinStatus();
  },
  methods: {
    close() {
      this.$emit("close");
    },
    // 初始化剪贴板功能
    initClipboard() {
    },
    // 检查微信状态
    checkWeixinStatus() {
    },
    // 获取图标源，确保始终有图标显示
    getIconSrc(icon) {
      if (icon.startsWith("/")) {
        return icon;
      } else {
        return this.defaultIcons[icon] || "/static/img/avatar.png";
      }
    },
    // 处理分享给特定人员
    handleShareToPerson(person) {
      common_vendor.index.__f__("log", "at components/share/index.vue:315", "分享给:", person.name);
      common_vendor.index.showToast({
        title: `分享给${person.name}`,
        icon: "none"
      });
      this.$emit("share-to-person", person);
    },
    // 分享到微信好友 (H5)
    shareToWeixin() {
    },
    // 小程序分享好友
    shareToMPFriend() {
      common_vendor.index.__f__("log", "at components/share/index.vue:340", "小程序分享好友");
      this.close();
    },
    // APP分享
    appShare(scene) {
    },
    // 微信SDK分享
    shareToWeixinSDK() {
    },
    // 生成口令
    generateCommandWord() {
      if (this.shareData.command_word) {
        return this.shareData.command_word;
      }
      let commandWord = "";
      if (this.shareType === "note") {
        const title = this.noteInfo.title || this.shareData.title || "精彩内容";
        const id = this.noteInfo.id || "";
        commandWord = `#${title}# 快来看看这个精彩内容！复制这条消息，打开APP查看详情。ID:${id}`;
      } else if (this.shareType === "goods") {
        const title = this.shareData.title || "商品分享";
        const link = this.shareData.link || "";
        commandWord = `#${title}# 发现好物推荐！复制这条消息，打开APP查看详情。${link}`;
      } else {
        const title = this.shareData.title || "精彩内容";
        commandWord = `#${title}# 快来看看这个精彩内容！`;
      }
      return commandWord;
    },
    // 复制口令
    copyCommand() {
      const commandWord = this.shareData.command_word || this.generateCommandWord();
      common_vendor.index.setClipboardData({
        data: commandWord,
        success: () => {
          common_vendor.index.showToast({
            title: "口令复制成功",
            icon: "success"
          });
          this.close();
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "none"
          });
        }
      });
    },
    // H5备用复制方法
    fallbackCopy(text) {
    },
    // 生成海报
    generatePoster() {
      common_vendor.index.__f__("log", "at components/share/index.vue:497", "生成海报");
      this.canvasStatus = true;
      this.close();
      setTimeout(() => {
        this.posterImage = this.shareData.image || "/static/img/avatar.png";
        this.posterImageStatus = true;
        this.canvasStatus = false;
      }, 2e3);
      this.$emit("generate-poster", this.shareData);
    },
    // 关闭海报
    posterImageClose() {
      this.posterImageStatus = false;
    },
    // 保存海报到手机
    savePosterPath() {
      common_vendor.index.getSetting({
        success: (res) => {
          if (!res.authSetting["scope.writePhotosAlbum"]) {
            common_vendor.index.authorize({
              scope: "scope.writePhotosAlbum",
              success: () => {
                this.saveImage();
              },
              fail: () => {
                common_vendor.index.showToast({
                  title: "需要授权保存相册",
                  icon: "none"
                });
              }
            });
          } else {
            this.saveImage();
          }
        }
      });
    },
    // 保存图片
    saveImage() {
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: this.posterImage,
        success: () => {
          this.posterImageClose();
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "none"
          });
        }
      });
    },
    // 复制链接
    copyLink() {
      let shareUrl;
      if (this.shareType === "note") {
        if (this.noteInfo.type === "circle") {
          shareUrl = this.noteInfo.share_url || `/pages/note/circle?id=${this.noteInfo.id}`;
        } else {
          shareUrl = `/pages/note/details?id=${this.noteInfo.id}`;
        }
      } else {
        shareUrl = this.shareData.link || location.href;
      }
      common_vendor.index.setClipboardData({
        data: shareUrl,
        success: () => {
          common_vendor.index.showToast({ title: "链接已复制", icon: "success" });
          this.close();
        },
        fail: () => {
          common_vendor.index.showToast({ title: "复制失败", icon: "none" });
        }
      });
    },
    handleAction(item) {
      common_vendor.index.__f__("log", "at components/share/index.vue:589", "执行操作:", item.name, item.type);
      switch (item.type) {
        case "dislike":
          this.markDislike();
          break;
        case "report":
          this.showReportModal();
          break;
        case "edit":
          this.editNote();
          break;
        case "delete":
          this.deleteNote();
          break;
      }
    },
    // 标记不感兴趣
    markDislike() {
      const isCircle = this.noteInfo.type === "circle";
      const content = isCircle ? "确定标记该圈子为不感兴趣吗？" : "确定标记为不感兴趣吗？";
      const successText = isCircle ? "已标记该圈子为不感兴趣" : "已标记为不感兴趣";
      common_vendor.index.showModal({
        title: "确认操作",
        content,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({ title: successText, icon: "success" });
            this.close();
            this.$emit("dislike", this.noteInfo.id);
          }
        }
      });
    },
    // 显示举报弹窗
    showReportModal() {
      common_vendor.index.showActionSheet({
        itemList: this.reportReasons,
        success: (res) => {
          const reason = this.reportReasons[res.tapIndex];
          this.reportNote(reason);
        }
      });
    },
    // 举报笔记
    reportNote(reason) {
      common_vendor.index.__f__("log", "at components/share/index.vue:639", "举报笔记:", reason);
      common_vendor.index.showLoading({ title: "举报中...", mask: true });
      this.$emit("report", {
        noteId: this.noteInfo.id,
        reason,
        noteInfo: this.noteInfo
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({ title: "举报成功", icon: "success" });
        this.close();
      }, 1e3);
    },
    // 编辑笔记
    editNote() {
      this.close();
      this.$emit("edit", this.noteInfo.id);
    },
    // 删除笔记或圈子
    deleteNote() {
      const isCircle = this.noteInfo.type === "circle";
      const content = isCircle ? "确认要永久删除这个圈子吗？删除后圈子内的所有内容都将清空。" : "确认要永久删除这篇笔记吗？";
      common_vendor.index.showModal({
        title: "确认删除",
        content,
        confirmColor: "#FA5150",
        success: (res) => {
          if (res.confirm) {
            this.close();
            this.$emit("delete", this.noteInfo.id);
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.show
  }, $props.show ? common_vendor.e({
    b: common_vendor.o((...args) => $options.close && $options.close(...args)),
    c: common_assets._imports_0$4,
    d: common_vendor.o((...args) => $options.close && $options.close(...args)),
    e: common_vendor.f($data.personList, (person, index, i0) => {
      return common_vendor.e({
        a: person.avatar,
        b: person.online
      }, person.online ? {} : {}, {
        c: common_vendor.t(person.name),
        d: index,
        e: common_vendor.o(($event) => $options.handleShareToPerson(person), index)
      });
    }),
    f: $data.showWeixinButton
  }, $data.showWeixinButton ? {
    g: $options.getIconSrc("wechat"),
    h: common_vendor.o((...args) => $options.shareToWeixin && $options.shareToWeixin(...args))
  } : {}, {
    i: $options.getIconSrc("wechat"),
    j: common_vendor.o((...args) => $options.shareToMPFriend && $options.shareToMPFriend(...args)),
    k: $options.showCopyCommand
  }, $options.showCopyCommand ? {
    l: $options.getIconSrc("copy"),
    m: $props.shareData.command_word || $options.generateCommandWord(),
    n: common_vendor.o((...args) => $options.copyCommand && $options.copyCommand(...args))
  } : {}, {
    o: $options.getIconSrc("poster"),
    p: common_vendor.o((...args) => $options.generatePoster && $options.generatePoster(...args)),
    q: $options.getIconSrc("link"),
    r: common_vendor.o((...args) => $options.copyLink && $options.copyLink(...args)),
    s: common_vendor.f($options.computedBottomItems, (item, index, i0) => {
      return {
        a: $options.getIconSrc(item.icon),
        b: item.type === "delete" ? 1 : "",
        c: common_vendor.t(item.name),
        d: item.type === "delete" ? 1 : "",
        e: index,
        f: common_vendor.o(($event) => $options.handleAction(item), index)
      };
    }),
    t: $data.posterImageStatus
  }, $data.posterImageStatus ? {
    v: common_assets._imports_1$23,
    w: common_vendor.o((...args) => $options.posterImageClose && $options.posterImageClose(...args)),
    x: $data.posterImage,
    y: common_vendor.o((...args) => $options.savePosterPath && $options.savePosterPath(...args))
  } : {}, {
    z: $data.posterImageStatus
  }, $data.posterImageStatus ? {
    A: common_vendor.o((...args) => $options.posterImageClose && $options.posterImageClose(...args))
  } : {}, {
    B: $data.canvasStatus
  }, $data.canvasStatus ? {} : {}) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fd563daa"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/share/index.js.map
