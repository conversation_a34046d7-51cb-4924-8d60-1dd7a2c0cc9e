{"version": 3, "file": "app.js", "sources": ["config/app.js"], "sourcesContent": ["const config = {\r\n\t// 小程序 / APP请求配置\r\n\t// #ifdef MP || APP-PLUS\r\n\t// 请求域名 格式： https://您的域名\r\n\tHTTP_REQUEST_URL: `http://nas4.weiyun6.com:8033`,\r\n\t//HTTP_REQUEST_URL: `http://*************`,\r\n\t// #endif\r\n\r\n\t// H5请求配置\r\n\t// #ifdef H5\r\n\t// H5接口是浏览器地址，非单独部署不用修改\r\n\t//HTTP_REQUEST_URL: window.location.protocol + \"//\" + window.location.host,\r\n\tHTTP_REQUEST_URL: `http://nas4.weiyun6.com:8033`,\r\n\t//HTTP_REQUEST_URL: `http://*************`,\r\n\t// #endif\r\n\r\n\r\n\t// 后台版本号\r\n\tSYSTEM_VERSION: 540,\r\n\r\n\t// 以下配置在不做二开的前提下,不需要做任何的修改\r\n\tHEADER: {\r\n\t\t'content-type': 'application/json',\r\n\t\t//#ifdef H5\r\n\t\t'Form-type': navigator.userAgent.toLowerCase().indexOf(\"micromessenger\") !== -1 ? 'wechat' : 'h5',\r\n\t\t//#endif\r\n\t\t//#ifdef MP\r\n\t\t'Form-type': 'routine',\r\n\t\t//#endif\r\n\t\t//#ifdef APP-VUE\r\n\t\t'Form-type': 'app',\r\n\t\t//#endif\r\n\t},\r\n\t// 回话密钥名称 请勿修改此配置\r\n\tTOKENNAME: 'Authori-zation',\r\n\t// 缓存时间 0 永久\r\n\tEXPIRE: 0,\r\n\t//分页最多显示条数\r\n\tLIMIT: 10,\r\n\t// 请求超时限制 默认10秒\r\n\tTIMEOUT: 10000\r\n}\r\n\r\n// ES6默认导出\r\nexport default config"], "names": [], "mappings": ";AAAK,MAAC,SAAS;AAAA;AAAA;AAAA,EAId,kBAAkB;AAAA;AAAA;AAAA;AAAA,EAclB,gBAAgB;AAAA;AAAA,EAGhB,QAAQ;AAAA,IACP,gBAAgB;AAAA,IAKhB,aAAa;AAAA,EAKb;AAAA;AAAA,EAED,WAAW;AAAA;AAAA,EAEX,QAAQ;AAAA;AAAA,EAER,OAAO;AAAA;AAAA,EAEP,SAAS;AACV;;"}