"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const _sfc_main = {
  data() {
    return {
      circleName: "",
      circleDesc: "",
      circleNotice: "",
      circleAvatar: "",
      circleBackground: "",
      isSubmitting: false
    };
  },
  methods: {
    // 返回上一页
    navBack() {
      common_vendor.index.navigateBack();
    },
    // 选择圈子头像
    chooseAvatar() {
      this.uploadImage("avatar");
    },
    // 选择圈子背景
    chooseBackground() {
      this.uploadImage("background");
    },
    // 上传图片
    uploadImage(type) {
      if (this.$util && this.$util.uploadImageChange) {
        this.$util.uploadImageChange(
          "upload/image",
          // 上传成功
          (res) => {
            common_vendor.index.hideLoading();
            if (res.data && res.data.url) {
              if (type === "avatar") {
                this.circleAvatar = res.data.url;
                common_vendor.index.showToast({
                  title: "头像上传成功",
                  icon: "success"
                });
              } else {
                this.circleBackground = res.data.url;
                common_vendor.index.showToast({
                  title: "背景上传成功",
                  icon: "success"
                });
              }
            } else {
              common_vendor.index.__f__("error", "at pages/center/circle-create.vue:116", "图片上传返回数据异常:", res);
              common_vendor.index.showToast({
                title: "图片上传失败",
                icon: "none"
              });
            }
          },
          // 上传失败或取消
          (err) => {
            common_vendor.index.hideLoading();
            common_vendor.index.__f__("log", "at pages/center/circle-create.vue:126", "图片上传取消或失败:", err);
            if (err && err.errMsg && !err.errMsg.includes("cancel")) {
              common_vendor.index.showToast({
                title: "图片上传失败",
                icon: "none"
              });
            }
          },
          // 处理图片尺寸
          (res) => {
            if (res && res.w && res.h) {
              common_vendor.index.__f__("log", "at pages/center/circle-create.vue:137", `${type}图片尺寸:`, res.w, "x", res.h);
            }
          }
        );
      } else {
        common_vendor.index.chooseImage({
          count: 1,
          sizeType: ["compressed"],
          sourceType: ["album", "camera"],
          success: (res) => {
            const tempFilePath = res.tempFilePaths[0];
            common_vendor.index.getFileInfo({
              filePath: tempFilePath,
              success: (fileInfo) => {
                const fileSizeMB = fileInfo.size / (1024 * 1024);
                if (fileSizeMB > 5) {
                  common_vendor.index.showToast({
                    title: "图片大小不能超过5MB",
                    icon: "none"
                  });
                  return;
                }
                common_vendor.index.showLoading({
                  title: "上传中...",
                  mask: true
                });
                common_vendor.index.uploadFile({
                  url: this.$api.uploadUrl || "/api/upload/image",
                  filePath: tempFilePath,
                  name: "file",
                  success: (uploadRes) => {
                    common_vendor.index.hideLoading();
                    try {
                      const data = JSON.parse(uploadRes.data);
                      if (data.code === 200 && data.data && data.data.url) {
                        if (type === "avatar") {
                          this.circleAvatar = data.data.url;
                          common_vendor.index.showToast({
                            title: "头像上传成功",
                            icon: "success"
                          });
                        } else {
                          this.circleBackground = data.data.url;
                          common_vendor.index.showToast({
                            title: "背景上传成功",
                            icon: "success"
                          });
                        }
                      } else {
                        throw new Error("上传返回数据异常");
                      }
                    } catch (e) {
                      common_vendor.index.__f__("error", "at pages/center/circle-create.vue:196", "图片上传解析失败:", e);
                      common_vendor.index.showToast({
                        title: "图片上传失败",
                        icon: "none"
                      });
                    }
                  },
                  fail: (err) => {
                    common_vendor.index.hideLoading();
                    common_vendor.index.__f__("error", "at pages/center/circle-create.vue:205", "图片上传失败:", err);
                    common_vendor.index.showToast({
                      title: "图片上传失败",
                      icon: "none"
                    });
                  }
                });
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages/center/circle-create.vue:214", "获取文件信息失败:", err);
                common_vendor.index.showToast({
                  title: "无法获取图片信息",
                  icon: "none"
                });
              }
            });
          }
        });
      }
    },
    // 提交创建圈子
    submitCircle() {
      if (!this.circleName.trim()) {
        common_vendor.index.showToast({
          title: "请输入圈子名称",
          icon: "none"
        });
        return;
      }
      if (!this.circleDesc.trim()) {
        common_vendor.index.showToast({
          title: "请输入圈子简介",
          icon: "none"
        });
        return;
      }
      if (!this.circleAvatar) {
        common_vendor.index.showToast({
          title: "请上传圈子头像",
          icon: "none"
        });
        return;
      }
      this.isSubmitting = true;
      const data = {
        circle_name: this.circleName.trim(),
        circle_description: this.circleDesc.trim(),
        circle_notice: this.circleNotice.trim(),
        circle_avatar: this.circleAvatar,
        circle_background: this.circleBackground
      };
      api_social.createCircle(data).then((res) => {
        this.isSubmitting = false;
        if (res.status === 200) {
          common_vendor.index.showToast({
            title: "创建成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          common_vendor.index.showToast({
            title: res.msg || "创建失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        this.isSubmitting = false;
        common_vendor.index.__f__("error", "at pages/center/circle-create.vue:282", "创建圈子失败:", err);
        common_vendor.index.showToast({
          title: "网络错误，请稍后重试",
          icon: "none"
        });
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.circleAvatar
  }, $data.circleAvatar ? {
    b: $data.circleAvatar
  } : {}, {
    c: common_vendor.o((...args) => $options.chooseAvatar && $options.chooseAvatar(...args)),
    d: $data.circleBackground
  }, $data.circleBackground ? {
    e: $data.circleBackground
  } : {}, {
    f: common_vendor.o((...args) => $options.chooseBackground && $options.chooseBackground(...args)),
    g: $data.circleName,
    h: common_vendor.o(($event) => $data.circleName = $event.detail.value),
    i: $data.circleDesc,
    j: common_vendor.o(($event) => $data.circleDesc = $event.detail.value),
    k: common_vendor.t($data.circleDesc.length),
    l: $data.circleNotice,
    m: common_vendor.o(($event) => $data.circleNotice = $event.detail.value),
    n: common_vendor.t($data.circleNotice.length),
    o: common_vendor.t($data.isSubmitting ? "创建中..." : "创建圈子"),
    p: common_vendor.o((...args) => $options.submitCircle && $options.submitCircle(...args)),
    q: $data.isSubmitting
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-531fb3c5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/center/circle-create.js.map
