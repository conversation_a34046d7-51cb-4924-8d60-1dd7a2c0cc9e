{"version": 3, "file": "user.js", "sources": ["stores/user.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { defineStore } from 'pinia'\nimport { getUserInfo } from '@/api/user.js'\nimport Cache from '@/utils/cache.js'\nimport cacheConfig from '@/config/cache.js'\n\nconst { UID, LOGIN_STATUS, USER_INFO } = cacheConfig\n\nexport const useUserStore = defineStore('user', {\n  state: () => ({\n    // 用户基本信息\n    userInfo: {\n      uid: 0,\n      nickname: '',\n      avatar: '',\n      phone: '',\n      spread_count: 0,\n      integral: 0,\n      now_money: 0,\n      brokerage_price: 0,\n      exp_num: 0,\n      user_type: '',\n      status: 1,\n      level: 0,\n      exp_name: '',\n      is_promoter: 0,\n      pay_count: 0,\n      order_count: 0,\n      coupon_count: 0,\n      like_count: 0,\n      follow_count: 0,\n      fans_count: 0,\n      visitor_count: 0,\n      visitor_badge: 0,\n      vip_status: 0,\n      is_money_level: 0,\n      svip_open: 0,\n      activity_count: 0,\n      activity_img: \"\"\n    },\n    \n    // 登录状态\n    isLogin: false,\n    token: '',\n    \n    // 用户权限\n    permissions: [],\n    \n    // 用户设置\n    settings: {\n      theme: 'light',\n      language: 'zh-CN',\n      notifications: true\n    }\n  }),\n\n  getters: {\n    // 是否已登录\n    isLoggedIn: (state) => state.isLogin && !!state.token,\n\n    // 用户ID\n    uid: (state) => state.userInfo.uid || state.uid || 0,\n\n    // 用户昵称（带默认值）\n    displayName: (state) => state.userInfo.nickname || '未登录用户',\n    \n    // 用户头像（带默认值）\n    displayAvatar: (state) => state.userInfo.avatar || '/static/img/avatar.png',\n    \n    // 是否为VIP用户\n    isVip: (state) => state.userInfo.vip_status === 1,\n    \n    // 是否为推广员\n    isPromoter: (state) => state.userInfo.is_promoter === 1,\n    \n    // 用户等级信息\n    levelInfo: (state) => ({\n      level: state.userInfo.level,\n      exp_name: state.userInfo.exp_name,\n      exp_num: state.userInfo.exp_num\n    }),\n    \n    // 用户统计信息\n    statsInfo: (state) => ({\n      like_count: state.userInfo.like_count,\n      follow_count: state.userInfo.follow_count,\n      fans_count: state.userInfo.fans_count,\n      visitor_count: state.userInfo.visitor_count\n    })\n  },\n\n  actions: {\n    // 设置登录状态\n    setLoginStatus(status) {\n      this.isLogin = status\n      if (!status) {\n        this.token = ''\n        this.resetUserInfo()\n      }\n    },\n\n    // 设置token\n    setToken(token) {\n      this.token = token\n      this.isLogin = !!token\n\n      // 同步到uni-app的全局存储（兼容旧系统）\n      if (token) {\n        uni.setStorageSync('token', token) // 新系统\n        Cache.set(LOGIN_STATUS, token) // 旧系统兼容\n      } else {\n        uni.removeStorageSync('token')\n        Cache.clear(LOGIN_STATUS)\n      }\n    },\n\n    // 更新用户信息\n    updateUserInfo(userInfo) {\n      // 验证输入数据\n      if (!userInfo || typeof userInfo !== 'object') {\n        console.warn('updateUserInfo: 无效的用户信息数据', userInfo);\n        return;\n      }\n\n      // 合并用户信息，确保数据完整性\n      const updatedInfo = { ...this.userInfo, ...userInfo };\n\n      // 确保关键字段存在\n      if (!updatedInfo.uid && userInfo.uid) {\n        updatedInfo.uid = userInfo.uid;\n      }\n\n      this.userInfo = updatedInfo;\n\n      // 同步到uni-app的全局存储\n      try {\n        uni.setStorageSync('USER_INFO', this.userInfo);\n        console.log('用户信息已更新到缓存:', {\n          uid: this.userInfo.uid,\n          nickname: this.userInfo.nickname,\n          timestamp: Date.now()\n        });\n      } catch (error) {\n        console.error('保存用户信息到缓存失败:', error);\n\n        // 如果是存储空间不足，尝试清理\n        if (error.name === 'QuotaExceededError') {\n          this.clearStorageCache();\n          // 重试保存\n          try {\n            uni.setStorageSync('USER_INFO', this.userInfo);\n          } catch (retryError) {\n            console.error('重试保存用户信息失败:', retryError);\n          }\n        }\n      }\n    },\n\n    // 重置用户信息\n    resetUserInfo() {\n      this.userInfo = {\n        uid: 0,\n        nickname: '',\n        avatar: '',\n        phone: '',\n        spread_count: 0,\n        integral: 0,\n        now_money: 0,\n        brokerage_price: 0,\n        exp_num: 0,\n        user_type: '',\n        status: 1,\n        level: 0,\n        exp_name: '',\n        is_promoter: 0,\n        pay_count: 0,\n        order_count: 0,\n        coupon_count: 0,\n        like_count: 0,\n        follow_count: 0,\n        fans_count: 0,\n        visitor_count: 0,\n        visitor_badge: 0,\n        vip_status: 0,\n        is_money_level: 0,\n        svip_open: 0,\n        activity_count: 0,\n        activity_img: \"\"\n      }\n      \n      // 清除本地存储\n      uni.removeStorageSync('USER_INFO')\n    },\n\n    // 获取用户信息\n    async fetchUserInfo() {\n      try {\n        const res = await getUserInfo()\n        if (res.status === 200) {\n          this.updateUserInfo(res.data)\n          return res.data\n        }\n      } catch (error) {\n        console.error('获取用户信息失败:', error)\n        throw error\n      }\n    },\n\n    // 登出\n    logout() {\n      this.setLoginStatus(false)\n      this.setToken('')\n      this.resetUserInfo()\n      this.permissions = []\n\n      // 清除所有相关存储（新旧系统兼容）\n      uni.removeStorageSync('token')\n      uni.removeStorageSync(USER_INFO)\n      uni.removeStorageSync(UID)\n      uni.removeStorageSync('pinia_user')\n\n      // 清除旧系统存储\n      Cache.clear(LOGIN_STATUS)\n      Cache.clear(USER_INFO)\n      Cache.clear(UID)\n\n      console.log('用户已登出，所有存储已清理');\n    },\n\n    // 设置用户ID\n    setUid(uid) {\n      this.userInfo.uid = uid\n      this.uid = uid // 同时更新根级别的uid\n\n      // 保存到本地存储\n      try {\n        uni.setStorageSync(UID, uid)\n        Cache.set(UID, uid)\n      } catch (error) {\n        console.warn('保存用户ID失败:', error)\n      }\n    },\n\n    // 更新用户设置\n    updateSettings(settings) {\n      this.settings = { ...this.settings, ...settings }\n    },\n\n    // 从本地存储初始化状态\n    initFromStorage() {\n      try {\n        // 恢复token（优先从新系统，兼容旧系统）\n        let token = uni.getStorageSync('token') || Cache.get(LOGIN_STATUS)\n        if (token) {\n          this.setToken(token)\n          console.log('Token已恢复:', { hasToken: !!token, tokenLength: token?.length });\n        } else {\n          console.log('未找到有效的token');\n        }\n\n        // 恢复用户信息\n        const userInfo = uni.getStorageSync(USER_INFO) || Cache.get(USER_INFO, true)\n        if (userInfo) {\n          this.updateUserInfo(userInfo)\n          console.log('用户信息已恢复:', { uid: userInfo.uid, nickname: userInfo.nickname });\n        }\n\n        // 恢复用户ID\n        const uid = uni.getStorageSync(UID) || Cache.get(UID)\n        if (uid) {\n          this.userInfo.uid = uid\n          console.log('用户ID已恢复:', uid);\n        }\n\n        // 验证登录状态\n        const isLoggedIn = this.isLogin && !!this.token && !!this.userInfo.uid\n        console.log('登录状态验证:', {\n          isLogin: this.isLogin,\n          hasToken: !!this.token,\n          hasUid: !!this.userInfo.uid,\n          finalStatus: isLoggedIn\n        });\n\n      } catch (error) {\n        console.warn('从本地存储初始化用户状态失败:', error)\n\n        // 如果是存储空间不足，尝试清理\n        if (error.name === 'QuotaExceededError') {\n          this.clearStorageCache()\n        }\n      }\n    },\n\n    // 清理存储缓存\n    clearStorageCache() {\n      try {\n        // 清理非关键数据\n        const keysToRemove = [\n          'pinia_social',\n          'SIDEBAR_MENU',\n          'APP_STATE',\n          'BASIC_CONFIG'\n        ]\n\n        keysToRemove.forEach(key => {\n          try {\n            uni.removeStorageSync(key)\n          } catch (e) {\n            console.warn(`清理${key}失败:`, e)\n          }\n        })\n\n        console.log('已清理存储缓存')\n      } catch (error) {\n        console.warn('清理存储缓存失败:', error)\n      }\n    }\n  }\n})\n"], "names": ["cacheConfig", "defineStore", "uni", "<PERSON><PERSON>", "getUserInfo"], "mappings": ";;;;;AAeA,MAAM,EAAE,KAAK,cAAc,UAAS,IAAKA,aAAW;AAExC,MAAC,eAAeC,cAAW,YAAC,QAAQ;AAAA,EAC9C,OAAO,OAAO;AAAA;AAAA,IAEZ,UAAU;AAAA,MACR,KAAK;AAAA,MACL,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU;AAAA,MACV,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,UAAU;AAAA,MACV,aAAa;AAAA,MACb,WAAW;AAAA,MACX,aAAa;AAAA,MACb,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,cAAc;AAAA,IACf;AAAA;AAAA,IAGD,SAAS;AAAA,IACT,OAAO;AAAA;AAAA,IAGP,aAAa,CAAE;AAAA;AAAA,IAGf,UAAU;AAAA,MACR,OAAO;AAAA,MACP,UAAU;AAAA,MACV,eAAe;AAAA,IAChB;AAAA,EACL;AAAA,EAEE,SAAS;AAAA;AAAA,IAEP,YAAY,CAAC,UAAU,MAAM,WAAW,CAAC,CAAC,MAAM;AAAA;AAAA,IAGhD,KAAK,CAAC,UAAU,MAAM,SAAS,OAAO,MAAM,OAAO;AAAA;AAAA,IAGnD,aAAa,CAAC,UAAU,MAAM,SAAS,YAAY;AAAA;AAAA,IAGnD,eAAe,CAAC,UAAU,MAAM,SAAS,UAAU;AAAA;AAAA,IAGnD,OAAO,CAAC,UAAU,MAAM,SAAS,eAAe;AAAA;AAAA,IAGhD,YAAY,CAAC,UAAU,MAAM,SAAS,gBAAgB;AAAA;AAAA,IAGtD,WAAW,CAAC,WAAW;AAAA,MACrB,OAAO,MAAM,SAAS;AAAA,MACtB,UAAU,MAAM,SAAS;AAAA,MACzB,SAAS,MAAM,SAAS;AAAA,IAC9B;AAAA;AAAA,IAGI,WAAW,CAAC,WAAW;AAAA,MACrB,YAAY,MAAM,SAAS;AAAA,MAC3B,cAAc,MAAM,SAAS;AAAA,MAC7B,YAAY,MAAM,SAAS;AAAA,MAC3B,eAAe,MAAM,SAAS;AAAA,IACpC;AAAA,EACG;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,eAAe,QAAQ;AACrB,WAAK,UAAU;AACf,UAAI,CAAC,QAAQ;AACX,aAAK,QAAQ;AACb,aAAK,cAAe;AAAA,MACrB;AAAA,IACF;AAAA;AAAA,IAGD,SAAS,OAAO;AACd,WAAK,QAAQ;AACb,WAAK,UAAU,CAAC,CAAC;AAGjB,UAAI,OAAO;AACTC,4BAAI,eAAe,SAAS,KAAK;AACjCC,0BAAM,IAAI,cAAc,KAAK;AAAA,MACrC,OAAa;AACLD,sBAAG,MAAC,kBAAkB,OAAO;AAC7BC,oBAAK,MAAC,MAAM,YAAY;AAAA,MACzB;AAAA,IACF;AAAA;AAAA,IAGD,eAAe,UAAU;AAEvB,UAAI,CAAC,YAAY,OAAO,aAAa,UAAU;AAC7CD,mEAAa,6BAA6B,QAAQ;AAClD;AAAA,MACD;AAGD,YAAM,cAAc,EAAE,GAAG,KAAK,UAAU,GAAG,SAAQ;AAGnD,UAAI,CAAC,YAAY,OAAO,SAAS,KAAK;AACpC,oBAAY,MAAM,SAAS;AAAA,MAC5B;AAED,WAAK,WAAW;AAGhB,UAAI;AACFA,sBAAAA,MAAI,eAAe,aAAa,KAAK,QAAQ;AAC7CA,sBAAAA,MAAY,MAAA,OAAA,yBAAA,eAAe;AAAA,UACzB,KAAK,KAAK,SAAS;AAAA,UACnB,UAAU,KAAK,SAAS;AAAA,UACxB,WAAW,KAAK,IAAK;AAAA,QAC/B,CAAS;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,yBAAc,gBAAgB,KAAK;AAGnC,YAAI,MAAM,SAAS,sBAAsB;AACvC,eAAK,kBAAiB;AAEtB,cAAI;AACFA,0BAAAA,MAAI,eAAe,aAAa,KAAK,QAAQ;AAAA,UAC9C,SAAQ,YAAY;AACnBA,0BAAc,MAAA,MAAA,SAAA,yBAAA,eAAe,UAAU;AAAA,UACxC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB;AACd,WAAK,WAAW;AAAA,QACd,KAAK;AAAA,QACL,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,cAAc;AAAA,QACd,UAAU;AAAA,QACV,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,aAAa;AAAA,QACb,WAAW;AAAA,QACX,aAAa;AAAA,QACb,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,cAAc;AAAA,MACf;AAGDA,oBAAG,MAAC,kBAAkB,WAAW;AAAA,IAClC;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI;AACF,cAAM,MAAM,MAAME,qBAAa;AAC/B,YAAI,IAAI,WAAW,KAAK;AACtB,eAAK,eAAe,IAAI,IAAI;AAC5B,iBAAO,IAAI;AAAA,QACZ;AAAA,MACF,SAAQ,OAAO;AACdF,sBAAAA,MAAc,MAAA,SAAA,yBAAA,aAAa,KAAK;AAChC,cAAM;AAAA,MACP;AAAA,IACF;AAAA;AAAA,IAGD,SAAS;AACP,WAAK,eAAe,KAAK;AACzB,WAAK,SAAS,EAAE;AAChB,WAAK,cAAe;AACpB,WAAK,cAAc,CAAE;AAGrBA,oBAAG,MAAC,kBAAkB,OAAO;AAC7BA,oBAAG,MAAC,kBAAkB,SAAS;AAC/BA,oBAAG,MAAC,kBAAkB,GAAG;AACzBA,oBAAG,MAAC,kBAAkB,YAAY;AAGlCC,kBAAK,MAAC,MAAM,YAAY;AACxBA,kBAAK,MAAC,MAAM,SAAS;AACrBA,kBAAK,MAAC,MAAM,GAAG;AAEfD,oBAAAA,MAAY,MAAA,OAAA,yBAAA,eAAe;AAAA,IAC5B;AAAA;AAAA,IAGD,OAAO,KAAK;AACV,WAAK,SAAS,MAAM;AACpB,WAAK,MAAM;AAGX,UAAI;AACFA,4BAAI,eAAe,KAAK,GAAG;AAC3BC,0BAAM,IAAI,KAAK,GAAG;AAAA,MACnB,SAAQ,OAAO;AACdD,sBAAAA,MAAa,MAAA,QAAA,yBAAA,aAAa,KAAK;AAAA,MAChC;AAAA,IACF;AAAA;AAAA,IAGD,eAAe,UAAU;AACvB,WAAK,WAAW,EAAE,GAAG,KAAK,UAAU,GAAG,SAAU;AAAA,IAClD;AAAA;AAAA,IAGD,kBAAkB;AAChB,UAAI;AAEF,YAAI,QAAQA,cAAAA,MAAI,eAAe,OAAO,KAAKC,YAAK,MAAC,IAAI,YAAY;AACjE,YAAI,OAAO;AACT,eAAK,SAAS,KAAK;AACnBD,wBAAAA,4CAAY,aAAa,EAAE,UAAU,CAAC,CAAC,OAAO,aAAa,+BAAO,OAAQ,CAAA;AAAA,QACpF,OAAe;AACLA,wBAAAA,MAAA,MAAA,OAAA,yBAAY,aAAa;AAAA,QAC1B;AAGD,cAAM,WAAWA,cAAG,MAAC,eAAe,SAAS,KAAKC,kBAAM,IAAI,WAAW,IAAI;AAC3E,YAAI,UAAU;AACZ,eAAK,eAAe,QAAQ;AAC5BD,wBAAAA,4CAAY,YAAY,EAAE,KAAK,SAAS,KAAK,UAAU,SAAS,SAAU,CAAA;AAAA,QAC3E;AAGD,cAAM,MAAMA,cAAAA,MAAI,eAAe,GAAG,KAAKC,YAAK,MAAC,IAAI,GAAG;AACpD,YAAI,KAAK;AACP,eAAK,SAAS,MAAM;AACpBD,wBAAY,MAAA,MAAA,OAAA,yBAAA,YAAY,GAAG;AAAA,QAC5B;AAGD,cAAM,aAAa,KAAK,WAAW,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,KAAK,SAAS;AACnEA,sBAAAA,MAAY,MAAA,OAAA,yBAAA,WAAW;AAAA,UACrB,SAAS,KAAK;AAAA,UACd,UAAU,CAAC,CAAC,KAAK;AAAA,UACjB,QAAQ,CAAC,CAAC,KAAK,SAAS;AAAA,UACxB,aAAa;AAAA,QACvB,CAAS;AAAA,MAEF,SAAQ,OAAO;AACdA,sBAAAA,MAAa,MAAA,QAAA,yBAAA,mBAAmB,KAAK;AAGrC,YAAI,MAAM,SAAS,sBAAsB;AACvC,eAAK,kBAAmB;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB;AAClB,UAAI;AAEF,cAAM,eAAe;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAED,qBAAa,QAAQ,SAAO;AAC1B,cAAI;AACFA,0BAAG,MAAC,kBAAkB,GAAG;AAAA,UAC1B,SAAQ,GAAG;AACVA,gCAAA,MAAA,QAAA,yBAAa,KAAK,GAAG,OAAO,CAAC;AAAA,UAC9B;AAAA,QACX,CAAS;AAEDA,sBAAAA,MAAA,MAAA,OAAA,yBAAY,SAAS;AAAA,MACtB,SAAQ,OAAO;AACdA,sBAAAA,MAAa,MAAA,QAAA,yBAAA,aAAa,KAAK;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AACH,CAAC;;"}