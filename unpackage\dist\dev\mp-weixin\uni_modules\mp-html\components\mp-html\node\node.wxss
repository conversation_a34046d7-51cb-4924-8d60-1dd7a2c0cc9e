
/* a 标签默认效果 */
._a {
  padding: 1.5px 0 1.5px 0;
  color: #366092;
  word-break: break-all;
}

/* a 标签点击态效果 */
._hover {
  text-decoration: underline;
  opacity: 0.7;
}

/* 图片默认效果 */
._img {
  max-width: 100%;
  -webkit-touch-callout: none;
}

/* 内部样式 */
._block {
  display: block;
}
._b,
._strong {
  font-weight: bold;
}
._code {
  font-family: monospace;
}
._del {
  text-decoration: line-through;
}
._em,
._i {
  font-style: italic;
}
._h1 {
  font-size: 2em;
}
._h2 {
  font-size: 1.5em;
}
._h3 {
  font-size: 1.17em;
}
._h5 {
  font-size: 0.83em;
}
._h6 {
  font-size: 0.67em;
}
._h1,
._h2,
._h3,
._h4,
._h5,
._h6 {
  display: block;
  font-weight: bold;
}
._image {
  height: 1px;
}
._ins {
  text-decoration: underline;
}
._li {
  display: list-item;
}
._ol {
  list-style-type: decimal;
}
._ol,
._ul {
  display: block;
  padding-left: 40px;
  margin: 1em 0;
}
._q::before {
  content: '"';
}
._q::after {
  content: '"';
}
._sub {
  font-size: smaller;
  vertical-align: sub;
}
._sup {
  font-size: smaller;
  vertical-align: super;
}
._thead,
._tbody,
._tfoot {
  display: table-row-group;
}
._tr {
  display: table-row;
}
._td,
._th {
  display: table-cell;
  vertical-align: middle;
}
._th {
  font-weight: bold;
  text-align: center;
}
._ul {
  list-style-type: disc;
}
._ul ._ul {
  margin: 0;
  list-style-type: circle;
}
._ul ._ul ._ul {
  list-style-type: square;
}
._abbr,
._b,
._code,
._del,
._em,
._i,
._ins,
._label,
._q,
._span,
._strong,
._sub,
._sup {
  display: inline;
}







