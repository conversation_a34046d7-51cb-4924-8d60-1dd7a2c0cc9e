<view class="data-v-6020d7bd" style="{{o}}"><view class="mask data-v-6020d7bd" catchtouchmove="{{a}}" hidden="{{undefined}}"></view><view class="{{['product-window', 'data-v-6020d7bd', n && 'on']}}"><view class="mp-data data-v-6020d7bd"><text class="mp-name data-v-6020d7bd">{{b}}{{c}}</text></view><view class="trip-msg data-v-6020d7bd"><view class="trip data-v-6020d7bd">{{d}}</view></view><view class="trip-title data-v-6020d7bd">{{e}}</view><view class="trip-msg data-v-6020d7bd"><view class="trip data-v-6020d7bd">{{f}}</view></view><view class="main-color data-v-6020d7bd" catchtap="{{i}}">{{g}}{{h}}</view><view class="bottom data-v-6020d7bd"><button class="save open data-v-6020d7bd" type="default" id="agree-btn" open-type="agreePrivacyAuthorization" bindagreeprivacyauthorization="{{k}}">{{j}}</button><button class="reject data-v-6020d7bd" bindtap="{{m}}">{{l}}</button></view></view></view>