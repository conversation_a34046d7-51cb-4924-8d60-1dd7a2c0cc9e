"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const pages_users_components_verify_utils_ase = require("../utils/ase.js");
const api_api = require("../../../../../api/api.js");
const _sfc_main = {
  name: "VerifyPoints",
  props: {
    //弹出式pop，固定fixed
    mode: {
      type: String,
      default: "fixed"
    },
    captchaType: {
      type: String
    },
    //间隔
    vSpace: {
      type: Number,
      default: 5
    },
    imgSize: {
      type: Object,
      default() {
        return {
          width: "310px",
          height: "155px"
        };
      }
    },
    barSize: {
      type: Object,
      default() {
        return {
          width: "310px",
          height: "40px"
        };
      }
    },
    defaultImg: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      secretKey: "",
      //后端返回的加密秘钥 字段
      checkNum: 3,
      //
      fontPos: [],
      // 选中的坐标信息
      checkPosArr: [],
      //用户点击的坐标
      num: 1,
      //点击的记数
      pointBackImgBase: "",
      //后端获取到的背景图片
      poinTextList: [],
      //后端返回的点击字体顺序
      backToken: "",
      //后端返回的token值
      imgRand: 0,
      //随机的背景图片
      setSize: {
        imgHeight: 0,
        imgWidth: 0,
        barHeight: 0,
        barWidth: 0
      },
      showImage: true,
      tempPoints: [],
      text: "",
      barAreaColor: "#fff",
      barAreaBorderColor: "#fff",
      showRefresh: true,
      bindingClick: true,
      imgLeft: "",
      imgTop: ""
    };
  },
  methods: {
    init() {
      this.fontPos.splice(0, this.fontPos.length);
      this.checkPosArr.splice(0, this.checkPosArr.length);
      this.num = 1;
      this.$nextTick(() => {
        this.refresh();
        this.$parent.$emit("ready", this);
      });
    },
    canvasClick(e) {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select("#image").boundingClientRect((data) => {
        this.imgLeft = Math.ceil(data.left);
        this.imgTop = Math.ceil(data.top);
        this.checkPosArr.push(this.getMousePos(this.$refs.canvas, e));
        if (this.num == this.checkNum) {
          this.num = this.createPoint(this.getMousePos(this.$refs.canvas, e));
          this.checkPosArr = this.pointTransfrom(this.checkPosArr, this.imgSize);
          setTimeout(() => {
            var captchaVerification = this.secretKey ? pages_users_components_verify_utils_ase.aesEncrypt(this.backToken + "---" + JSON.stringify(this.checkPosArr), this.secretKey) : this.backToken + "---" + JSON.stringify(this.checkPosArr);
            let data2 = {
              captchaType: this.captchaType,
              pointJson: this.secretKey ? pages_users_components_verify_utils_ase.aesEncrypt(JSON.stringify(this.checkPosArr), this.secretKey) : JSON.stringify(this.checkPosArr),
              token: this.backToken
            };
            api_api.ajcaptchaCheck(data2).then((result) => {
              result.data;
              this.barAreaColor = "#4cae4c";
              this.barAreaBorderColor = "#5cb85c";
              this.text = "验证成功";
              this.bindingClick = false;
              setTimeout(() => {
                if (this.mode == "pop") {
                  this.$parent.clickShow = false;
                }
                this.refresh();
              }, 1500);
              this.$emit("success", { captchaVerification });
            }).catch((res) => {
              this.$parent.$emit("error", this);
              this.barAreaColor = "#d9534f";
              this.barAreaBorderColor = "#d9534f";
              this.text = "验证失败";
              setTimeout(() => {
                this.refresh();
              }, 700);
            });
          }, 400);
        }
        if (this.num < this.checkNum) {
          this.num = this.createPoint(this.getMousePos(this.$refs.canvas, e));
        }
      }).exec();
    },
    //获取坐标
    getMousePos: function(obj, e) {
      let position = {
        x: Math.ceil(e.detail.x) - this.imgLeft,
        y: Math.ceil(e.detail.y) - this.imgTop
      };
      return position;
    },
    //创建坐标点
    createPoint: function(pos) {
      this.tempPoints.push(Object.assign({}, pos));
      return ++this.num;
    },
    refresh: function() {
      this.tempPoints.splice(0, this.tempPoints.length);
      this.barAreaColor = "#000";
      this.barAreaBorderColor = "#ddd";
      this.bindingClick = true;
      this.fontPos.splice(0, this.fontPos.length);
      this.checkPosArr.splice(0, this.checkPosArr.length);
      this.num = 1;
      this.getPictrue();
      this.showRefresh = true;
    },
    // 请求背景图片和验证图片
    getPictrue() {
      let data = {
        captchaType: this.captchaType,
        clientUid: common_vendor.index.getStorageSync("point"),
        ts: Date.now()
        // 现在的时间戳
      };
      api_api.getAjcaptcha(data).then((result) => {
        let res = result.data;
        this.pointBackImgBase = res.originalImageBase64;
        this.backToken = res.token;
        this.secretKey = res.secretKey;
        this.poinTextList = res.wordList;
        this.text = "请依次点击【" + this.poinTextList.join(",") + "】";
      }).catch(() => {
        this.pointBackImgBase = null;
      });
    },
    //坐标转换函数
    pointTransfrom(pointArr, imgSize) {
      var newPointArr = pointArr.map((p) => {
        let x = Math.round(310 * p.x / parseInt(imgSize.width));
        let y = Math.round(155 * p.y / parseInt(imgSize.height));
        return { x, y };
      });
      return newPointArr;
    }
  },
  watch: {
    // type变化则全面刷新
    type: {
      immediate: true,
      handler() {
        this.init();
      }
    }
  },
  mounted() {
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.refresh && $options.refresh(...args)),
    b: $data.showRefresh,
    c: $data.pointBackImgBase ? "data:image/png;base64," + $data.pointBackImgBase : $props.defaultImg,
    d: common_vendor.o(($event) => $data.bindingClick ? $options.canvasClick($event) : void 0),
    e: common_vendor.f($data.tempPoints, (tempPoint, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: index,
        c: parseInt(tempPoint.y - 10) + "px",
        d: parseInt(tempPoint.x - 10) + "px"
      };
    }),
    f: $props.imgSize.width,
    g: $props.imgSize.height,
    h: $props.vSpace + "px",
    i: $data.showImage,
    j: common_vendor.t($data.text),
    k: $props.imgSize.width,
    l: $data.barAreaColor,
    m: $data.barAreaBorderColor
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-76118314"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/pages/users/components/verify/verifyPoint/verifyPoint.js.map
