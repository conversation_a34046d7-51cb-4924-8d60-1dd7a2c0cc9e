{"version": 3, "file": "evaluate.js", "sources": ["pages/goods/evaluate.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZ29vZHMvZXZhbHVhdGUudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 导航栏 -->\r\n    <view class=\"nav-box df\" :style=\"{'padding-top': statusBarHeight + 'px', 'background': 'rgba(255,255,255,'+ navbarTrans +')'}\">\r\n      <view class=\"nav-back df\" :style=\"{'height': titleBarHeight + 'px'}\" @tap=\"navBack\">\r\n        <image src=\"/static/img/back.png\" style=\"width:34rpx;height:34rpx\"></image>\r\n      </view>\r\n      <view v-if=\"navbarTrans == 1\" class=\"nav-title ohto\">{{goodsInfo.name}}</view>\r\n    </view>\r\n    \r\n    <!-- 背景遮罩 -->\r\n    <view class=\"bg-mk\"></view>\r\n    <view class=\"bg-mk2\"></view>\r\n    <view class=\"bg-img\">\r\n      <lazy-image :src=\"goodsInfo.img ? goodsInfo.img : '/static/img/a.jpg'\"></lazy-image>\r\n    </view>\r\n    \r\n    <!-- 内容区域 -->\r\n    <view class=\"content-box\" :style=\"{'margin-top': statusBarHeight + titleBarHeight + 'px'}\">\r\n      <!-- 商品信息 -->\r\n      <view class=\"info-box\">\r\n        <view class=\"name\">{{goodsInfo.name}}</view>\r\n        <view class=\"unm df\">\r\n          共<text class=\"tt\">{{goodsInfo.count}}</text>条评论\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 空评价提示 -->\r\n      <view v-if=\"isEmpty\" class=\"empty-box df\">\r\n        <image src=\"/static/img/empty.png\" />\r\n        <view class=\"e1\">{{goodsInfo.name}} 暂无评论</view>\r\n        <view class=\"e2\">成功购买商品即可发布相关评论</view>\r\n      </view>\r\n      \r\n      <!-- 评价列表 -->\r\n      <block v-else>\r\n        <card-gg v-for=\"(item, index) in list\" :key=\"index\" :item=\"item\" :idx=\"index\" @likeback=\"likeClick\"></card-gg>\r\n      </block>\r\n      \r\n      <!-- 加载更多 -->\r\n      <uni-load-more :status=\"loadStatus\"></uni-load-more>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request.js'\r\nimport * as api from '@/config/api.js'\r\nimport lazyImage from '@/components/lazyImage/lazyImage.vue'\r\nimport cardGg from '@/components/card-gg/card-gg.vue'\r\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\r\n\r\nconst app = getApp()\r\n\r\nexport default {\r\n  components: {\r\n    lazyImage,\r\n    cardGg,\r\n    uniLoadMore\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\r\n      navbarTrans: 0,\r\n      goodsInfo: {\r\n        id: 0,\r\n        img: \"\",\r\n        name: \"名称加载中\",\r\n        count: 0\r\n      },\r\n      list: [],\r\n      page: 1,\r\n      isEmpty: false,\r\n      loadStatus: \"loading\",\r\n      // Mock数据\r\n      mockGoods: [\r\n        {\r\n          id: 1,\r\n          img: \"/static/img/temp/goods1.jpg\",\r\n          name: \"轻奢真皮小白鞋\",\r\n          count: 18\r\n        },\r\n        {\r\n          id: 102,\r\n          img: \"/static/img/temp/goods2.jpg\",\r\n          name: \"夏季薄款T恤\",\r\n          count: 24\r\n        },\r\n        {\r\n          id: 103,\r\n          img: \"/static/img/temp/goods3.jpg\",\r\n          name: \"轻便双肩包\",\r\n          count: 0\r\n        }\r\n      ],\r\n      mockEvaluates: {\r\n  // 商品101的评价数据\r\n  1: [\r\n    // 多图类型\r\n    {\r\n      id: 1001,\r\n      user: {\r\n        id: 10001,\r\n        name: \"樱桃小丸子\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 0,\r\n        age: \"25岁\"\r\n      },\r\n      user_id: 10001,\r\n      content: \"鞋子真的很好看，质量很棒，穿着很舒服，大小也很合适。物流也很快，包装也很好，没有任何破损。非常满意的一次购物体验！\",\r\n      imgs: [\r\n        { url: \"/static/img/avatar.png\", wide: 800, high: 600 },\r\n        { url: \"/static/img/avatar.png\", wide: 800, high: 600 }\r\n      ],\r\n      img_count: 2,\r\n      create_time_str: \"2023-05-16\",\r\n      create_time: \"2023-05-16\",\r\n      province: \"广东\",\r\n      browse: 156,\r\n      specs: \"白色 39码\",\r\n      is_like: false,\r\n      like_count: 12,\r\n      comment_count: 5,\r\n      type: 1,\r\n      adds_name: \"广州市天河区\",\r\n      lat: 23.12911,\r\n      lng: 113.26627,\r\n      comment: {\r\n        user_name: \"店家回复\",\r\n        content: \"感谢您的支持和喜爱，我们会继续提供优质产品！\"\r\n      },\r\n      top: false\r\n    },\r\n    \r\n    // 纯文本类型\r\n    {\r\n      id: 1,\r\n      user: {\r\n        id: 10002,\r\n        name: \"阳光男孩\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 1,\r\n        age: \"28岁\"\r\n      },\r\n      user_id: 10002,\r\n      content: \"非常好的购物体验，鞋子质量很好，做工精细，值得推荐！\",\r\n      imgs: [],\r\n      img_count: 0,\r\n      create_time_str: \"2023-05-10\",\r\n      create_time: \"2023-05-10\",\r\n      province: \"北京\",\r\n      browse: 89,\r\n      specs: \"黑色 42码\",\r\n      is_like: true,\r\n      like_count: 8,\r\n      comment_count: 2,\r\n      type: 0,\r\n      topic_info: [\r\n        { id: 201, name: \"真皮鞋推荐\" },\r\n        { id: 202, name: \"男鞋种草\" }\r\n      ],\r\n      top: false\r\n    },\r\n    \r\n    // 单图带商品关联\r\n    {\r\n      id: 1,\r\n      user: {\r\n        id: 10003,\r\n        name: \"理性消费者\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 0,\r\n        age: \"30岁\"\r\n      },\r\n      user_id: 10003,\r\n      content: \"鞋子还行吧，没有图片上看起来那么好，但是价格合适，勉强满意。\",\r\n      imgs: [\r\n        { url: \"/static/img/avatar.png\", wide: 600, high: 800 }\r\n      ],\r\n      img_count: 1,\r\n      create_time_str: \"2023-05-05\",\r\n      create_time: \"2023-05-05\",\r\n      province: \"浙江\",\r\n      browse: 67,\r\n      specs: \"白色 40码\",\r\n      is_like: false,\r\n      like_count: 3,\r\n      comment_count: 1,\r\n      type: 1,\r\n      order_id: 1001,\r\n      goods: [\r\n        {\r\n          goods_id: 301,\r\n          goods_name: \"同款黑色款式\",\r\n          product_img: \"/static/img/avatar.png\"\r\n        }\r\n      ],\r\n      comment: {\r\n        user_name: \"店家回复\",\r\n        content: \"亲爱的顾客，感谢您的反馈。我们会不断改进产品质量，欢迎您再次光临！\"\r\n      },\r\n      top: false\r\n    },\r\n    \r\n    // 视频类型\r\n    {\r\n      id: 2,\r\n      user: {\r\n        id: 10004,\r\n        name: \"挑剔先生\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 1,\r\n        age: \"32岁\"\r\n      },\r\n      user_id: 10004,\r\n      content: \"不推荐购买，鞋子质量一般，穿着不太舒服，而且颜色和图片有色差。来个开箱视频大家感受一下。\",\r\n      imgs: [],\r\n      img_count: 0,\r\n      create_time_str: \"2023-04-28\",\r\n      create_time: \"2023-04-28\",\r\n      province: \"江苏\",\r\n      browse: 203,\r\n      specs: \"黑色 41码\",\r\n      is_like: false,\r\n      like_count: 5,\r\n      comment_count: 8,\r\n      type: 2,\r\n      video: {\r\n        cover: \"/static/img/avatar.png\",\r\n        wide: 720,\r\n        high: 1280\r\n      },\r\n      circle_id: 201,\r\n      circle_name: \"品质生活圈\",\r\n      circle_avatar: \"/static/img/avatar.png\",\r\n      comment: {\r\n        user_name: \"店家回复\",\r\n        content: \"尊敬的顾客，非常抱歉给您带来不好的体验。请联系客服，我们会为您提供退换货服务。\"\r\n      },\r\n      top: false\r\n    },\r\n    \r\n    // 音频类型\r\n    {\r\n      id: 3,\r\n      user: {\r\n        id: 10005,\r\n        name: \"时尚达人\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 0,\r\n        age: \"27岁\"\r\n      },\r\n      user_id: 10005,\r\n      content: \"款式很时尚，和我想象的一样好看！就是物流有点慢。分享一段我的开箱音频。\",\r\n      imgs: [\r\n        { url: \"/static/img/avatar.png\", wide: 800, high: 600 },\r\n        { url: \"/static/img/avatar.png\", wide: 600, high: 800 }\r\n      ],\r\n      img_count: 2,\r\n      create_time_str: \"2023-04-20\",\r\n      create_time: \"2023-04-20\",\r\n      province: \"上海\",\r\n      browse: 178,\r\n      specs: \"白色 38码\",\r\n      is_like: true,\r\n      like_count: 15,\r\n      comment_count: 4,\r\n      type: 3,\r\n      audio: {\r\n        name: \"鞋子开箱体验\",\r\n        cover: \"/static/img/avatar.png\",\r\n        intro: \"分享一下这款鞋子的穿着感受和搭配建议\"\r\n      },\r\n      activity_id: 101,\r\n      activity_name: \"夏季潮鞋节\",\r\n      activity_img: \"/static/img/avatar.png\",\r\n      top: true\r\n    }\r\n  ],\r\n  \r\n  // 商品102的评价数据\r\n  1: [\r\n    // 多图类型带话题和位置\r\n    {\r\n      id: 2001,\r\n      user: {\r\n        id: 20001,\r\n        name: \"时尚博主\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 0,\r\n        age: \"26岁\"\r\n      },\r\n      user_id: 20001,\r\n      content: \"面料很舒服，款式也很时尚，穿着很凉爽，夏天穿正好。分享几张实拍图给大家参考！\",\r\n      imgs: [\r\n        { url: \"/static/img/avatar.png\", wide: 800, high: 600 },\r\n        { url: \"/static/img/avatar.png\", wide: 600, high: 800 },\r\n        { url: \"/static/img/avatar.png\", wide: 800, high: 600 },\r\n        { url: \"/static/img/avatar.png\", wide: 800, high: 600 }\r\n      ],\r\n      img_count: 4,\r\n      create_time_str: \"2023-06-01\",\r\n      create_time: \"2023-06-01\",\r\n      province: \"上海\",\r\n      browse: 342,\r\n      specs: \"蓝色 L码\",\r\n      is_like: true,\r\n      like_count: 23,\r\n      comment_count: 7,\r\n      type: 1,\r\n      adds_name: \"上海市浦东新区\",\r\n      lat: 31.22114,\r\n      lng: 121.53065,\r\n      topic_info: [\r\n        { id: 203, name: \"夏季穿搭\" },\r\n        { id: 204, name: \"舒适面料\" }\r\n      ],\r\n      top: true\r\n    },\r\n    \r\n    // 音频类型\r\n    {\r\n      id: 2002,\r\n      user: {\r\n        id: 20002,\r\n        name: \"理性购物者\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 1,\r\n        age: \"31岁\"\r\n      },\r\n      user_id: 20002,\r\n      content: \"衣服质量一般，但是价格便宜，所以还算满意。建议大家洗的时候小心一点，容易掉色。\",\r\n      imgs: [],\r\n      img_count: 0,\r\n      create_time_str: \"2023-05-25\",\r\n      create_time: \"2023-05-25\",\r\n      province: \"广东\",\r\n      browse: 156,\r\n      specs: \"白色 M码\",\r\n      is_like: false,\r\n      like_count: 4,\r\n      comment_count: 2,\r\n      type: 3,\r\n      audio: {\r\n        name: \"购物体验分享\",\r\n        cover: \"/static/img/avatar.png\",\r\n        intro: \"详细讲解此T恤的材质和洗涤注意事项\"\r\n      },\r\n      top: false\r\n    },\r\n    \r\n    // 单图带商品关联\r\n    {\r\n      id: 2,\r\n      user: {\r\n        id: 20003,\r\n        name: \"挑剔女王\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 0,\r\n        age: \"29岁\"\r\n      },\r\n      user_id: 20003,\r\n      content: \"不喜欢，面料有点粗糙，而且洗了一次就变形了，不推荐购买。\",\r\n      imgs: [\r\n        { url: \"/static/img/avatar.png\", wide: 800, high: 600 }\r\n      ],\r\n      img_count: 1,\r\n      create_time_str: \"2023-05-18\",\r\n      create_time: \"2023-05-18\",\r\n      province: \"浙江\",\r\n      browse: 213,\r\n      specs: \"黑色 S码\",\r\n      is_like: false,\r\n      like_count: 7,\r\n      comment_count: 3,\r\n      type: 1,\r\n      order_id: 2001,\r\n      goods: [\r\n        {\r\n          goods_id: 302,\r\n          goods_name: \"加厚款T恤\",\r\n          product_img: \"/static/img/avatar.png\"\r\n        }\r\n      ],\r\n      adds_name: \"杭州市西湖区\",\r\n      lat: 30.24271,\r\n      lng: 120.14972,\r\n      comment: {\r\n        user_name: \"店家回复\",\r\n        content: \"亲爱的顾客，非常抱歉给您带来不好的体验。请联系客服，我们会为您提供解决方案。\"\r\n      },\r\n      top: false\r\n    },\r\n    \r\n    // 视频类型带圈子和活动\r\n    {\r\n      id: 1,\r\n      user: {\r\n        id: 20004,\r\n        name: \"视频测评达人\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 1,\r\n        age: \"33岁\"\r\n      },\r\n      user_id: 20004,\r\n      content: \"做了一个开箱+洗涤测试视频，大家可以看看。总体来说这个价位还是值得的，但有一些小缺点。\",\r\n      imgs: [],\r\n      img_count: 0,\r\n      create_time_str: \"2023-05-12\",\r\n      create_time: \"2023-05-12\",\r\n      province: \"四川\",\r\n      browse: 487,\r\n      specs: \"蓝色 XL码\",\r\n      is_like: true,\r\n      like_count: 42,\r\n      comment_count: 15,\r\n      type: 2,\r\n      video: {\r\n        cover: \"/static/img/avatar.png\",\r\n        wide: 1920,\r\n        high: 1080\r\n      },\r\n      circle_id: 202,\r\n      circle_name: \"测评狂人圈\",\r\n      circle_avatar: \"/static/img/avatar.png\",\r\n      activity_id: 102,\r\n      activity_name: \"618好物推荐\",\r\n      activity_img: \"/static/img/avatar.png\",\r\n      comment: {\r\n        user_name: \"店家回复\",\r\n        content: \"感谢您的详细测评！我们会根据您提出的问题进行改进。\"\r\n      },\r\n      top: false\r\n    },\r\n    \r\n    // 多图+视频混合类型\r\n    {\r\n      id: 1,\r\n      user: {\r\n        id: 20005,\r\n        name: \"亲肤材质控\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 0,\r\n        age: \"28岁\"\r\n      },\r\n      user_id: 20005,\r\n      content: \"作为一个对面料特别敏感的人，我很满意这件T恤的材质。透气性好，不起球，多次洗涤后依然保持形状。以下是我的详细测评。\",\r\n      imgs: [\r\n        { url: \"/static/img/avatar.png\", wide: 800, high: 600 },\r\n        { url: \"/static/img/avatar.png\", wide: 800, high: 600 }\r\n      ],\r\n      img_count: 2,\r\n      create_time_str: \"2023-05-08\",\r\n      create_time: \"2023-05-08\",\r\n      province: \"北京\",\r\n      browse: 356,\r\n      specs: \"白色 L码\",\r\n      is_like: true,\r\n      like_count: 36,\r\n      comment_count: 9,\r\n      type: 2,\r\n      video: {\r\n        cover: \"/static/img/avatar.png\",\r\n        wide: 1080,\r\n        high: 1920\r\n      },\r\n      adds_name: \"北京市朝阳区\",\r\n      lat: 39.92148,\r\n      lng: 116.51149,\r\n      topic_info: [\r\n        { id: 205, name: \"面料测评\" },\r\n        { id: 206, name: \"亲肤材质\" }\r\n      ],\r\n      comment: {\r\n        user_name: \"材质专家\",\r\n        content: \"很专业的分析，确实这款产品的面料处理工艺很先进！\"\r\n      },\r\n      top: false\r\n    }\r\n  ],\r\n  \r\n  // 商品103的评价数据\r\n  103: [\r\n    // 多图带位置和话题\r\n    {\r\n      id: 3,\r\n      user: {\r\n        id: 30001,\r\n        name: \"旅行达人\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 1,\r\n        age: \"34岁\"\r\n      },\r\n      user_id: 30001,\r\n      content: \"这款双肩包我已经背着去了三个国家了，质量非常好，耐用且百搭。内部的分层设计很实用，可以有效整理各种物品。强烈推荐给经常旅行的朋友！\",\r\n      imgs: [\r\n        { url: \"/static/img/avatar.png\", wide: 800, high: 600 },\r\n        { url: \"/static/img/avatar.png\", wide: 600, high: 800 },\r\n        { url: \"/static/img/avatar.png\", wide: 800, high: 600 }\r\n      ],\r\n      img_count: 3,\r\n      create_time_str: \"2023-06-05\",\r\n      create_time: \"2023-06-05\",\r\n      province: \"国外\",\r\n      browse: 578,\r\n      specs: \"深蓝色\",\r\n      is_like: true,\r\n      like_count: 56,\r\n      comment_count: 18,\r\n      type: 1,\r\n      adds_name: \"泰国曼谷\",\r\n      lat: 13.75398,\r\n      lng: 100.50144,\r\n      topic_info: [\r\n        { id: 207, name: \"旅行装备\" },\r\n        { id: 208, name: \"轻便背包\" }\r\n      ],\r\n      comment: {\r\n        user_name: \"店家回复\",\r\n        content: \"感谢您的推荐！希望这款包能继续陪伴您探索更多美丽的地方~\"\r\n      },\r\n      top: true\r\n    },\r\n    \r\n    // 视频带圈子\r\n    {\r\n      id: 2,\r\n      user: {\r\n        id: 30002,\r\n        name: \"学生党\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 1,\r\n        age: \"22岁\"\r\n      },\r\n      user_id: 30002,\r\n      content: \"作为一个大学生，这个包的性价比超高！可以装下我所有的教材和笔记本电脑，而且肩带很舒适，长时间背着也不累。唯一的缺点是防水性一般，下雨天需要额外保护。\",\r\n      imgs: [],\r\n      img_count: 0,\r\n      create_time_str: \"2023-06-03\",\r\n      create_time: \"2023-06-03\",\r\n      province: \"江苏\",\r\n      browse: 234,\r\n      specs: \"黑色\",\r\n      is_like: false,\r\n      like_count: 18,\r\n      comment_count: 5,\r\n      type: 2,\r\n      video: {\r\n        cover: \"/static/img/avatar.png\",\r\n        wide: 720,\r\n        high: 1280\r\n      },\r\n      circle_id: 203,\r\n      circle_name: \"大学生活圈\",\r\n      circle_avatar: \"/static/img/avatar.png\",\r\n      comment: {\r\n        user_name: \"店家回复\",\r\n        content: \"感谢您的评价！关于防水问题，我们正在研发新的防水材料，未来的版本会有所改进。\"\r\n      },\r\n      top: false\r\n    },\r\n    \r\n    // 视频+图片带活动和商品\r\n    {\r\n      id: 1,\r\n      user: {\r\n        id: 30003,\r\n        name: \"户外摄影师\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 1,\r\n        age: \"31岁\"\r\n      },\r\n      user_id: 30003,\r\n      content: \"非常适合携带摄影器材！我把这个包改造了一下，加了几个内胆，完美适配我的相机和镜头。分享一下我的使用体验和改造过程。\",\r\n      imgs: [\r\n        { url: \"/static/img/avatar.png\", wide: 800, high: 600 },\r\n        { url: \"/static/img/avatar.png\", wide: 600, high: 800 }\r\n      ],\r\n      img_count: 2,\r\n      create_time_str: \"2023-05-30\",\r\n      create_time: \"2023-05-30\",\r\n      province: \"云南\",\r\n      browse: 389,\r\n      specs: \"灰色\",\r\n      is_like: true,\r\n      like_count: 87,\r\n      comment_count: 25,\r\n      type: 2,\r\n      video: {\r\n        cover: \"/static/img/avatar.png\",\r\n        wide: 1920,\r\n        high: 1080\r\n      },\r\n      order_id: 3001,\r\n      goods: [\r\n        {\r\n          goods_id: 303,\r\n          goods_name: \"专业摄影包\",\r\n          product_img: \"/static/img/avatar.png\"\r\n        }\r\n      ],\r\n      activity_id: 103,\r\n      activity_name: \"创意改造大赛\",\r\n      activity_img: \"/static/img/avatar.png\",\r\n      comment: {\r\n        user_name: \"店家回复\",\r\n        content: \"您的改造非常有创意！感谢分享，这给了我们产品开发的灵感。\"\r\n      },\r\n      top: false\r\n    },\r\n    \r\n    // 纯文本带音频\r\n    {\r\n      id: 3004,\r\n      user: {\r\n        id: 30004,\r\n        name: \"材质专家\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 1,\r\n        age: \"40岁\"\r\n      },\r\n      user_id: 30004,\r\n      content: \"从专业角度分析，这款包的面料是高密度尼龙，耐磨性很好，但透气性一般。缝线工艺还不错，整体做工在这个价位算是良心了。如果要提升品质，建议厂家在背部增加透气设计，肩带加厚一些会更舒适。\",\r\n      imgs: [],\r\n      img_count: 0,\r\n      create_time_str: \"2023-05-25\",\r\n      create_time: \"2023-05-25\",\r\n      province: \"北京\",\r\n      browse: 267,\r\n      specs: \"黑色\",\r\n      is_like: true,\r\n      like_count: 45,\r\n      comment_count: 10,\r\n      type: 3,\r\n      audio: {\r\n        name: \"面料与工艺分析\",\r\n        cover: \"/static/img/avatar.png\",\r\n        intro: \"专业分析这款背包所用材料的优缺点\"\r\n      },\r\n      comment: {\r\n        user_name: \"店家回复\",\r\n        content: \"感谢您的专业建议！我们已经记录下来，会在下一代产品中考虑您提出的改进点。\"\r\n      },\r\n      top: false\r\n    },\r\n    \r\n    // 单图带位置\r\n    {\r\n      id: 3005,\r\n      user: {\r\n        id: 30005,\r\n        name: \"不满意用户\",\r\n        avatar: \"/static/img/avatar.png\",\r\n        gender: 0,\r\n        age: \"27岁\"\r\n      },\r\n      user_id: 30005,\r\n      content: \"用了两周就坏了，拉链质量太差，而且背带接缝处开线了。客服态度还可以，已经申请退款。\",\r\n      imgs: [\r\n        { url: \"/static/img/avatar.png\", wide: 800, high: 600 }\r\n      ],\r\n      img_count: 1,\r\n      create_time_str: \"2023-05-20\",\r\n      create_time: \"2023-05-20\",\r\n      province: \"浙江\",\r\n      browse: 198,\r\n      specs: \"蓝色\",\r\n      is_like: false,\r\n      like_count: 12,\r\n      comment_count: 8,\r\n      type: 1,\r\n      adds_name: \"浙江杭州\",\r\n      lat: 30.25961,\r\n      lng: 120.21937,\r\n      comment: {\r\n        user_name: \"店家回复\",\r\n        content: \"非常抱歉给您带来不好的体验。我们已经联系了您，并安排了退款和赠送新品的补偿方案。也感谢您帮助我们发现质量问题。\"\r\n      },\r\n      top: false\r\n    }\r\n  ]\r\n}\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    // 获取系统信息\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    this.statusBarHeight = systemInfo.statusBarHeight || 20;\r\n    this.titleBarHeight = 44;\r\n    \r\n    if (options.id) {\r\n      this.goodsInfo.id = options.id;\r\n      this.goodsInfo.img = options.img || \"\";\r\n      this.goodsInfo.name = options.name || \"商品名称\";\r\n      this.goodsInfo.count = options.count || 0;\r\n      this.goodsDynamic();\r\n    } else {\r\n      this.navBack();\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取评论动态\r\n    goodsDynamic() {\r\n      let that = this\r\n      \r\n      // 检查API是否可用\r\n      if (api.default && api.default.api && api.default.api.goodsDynamicUrl) {\r\n        request(api.default.api.goodsDynamicUrl, {\r\n          id: that.goodsInfo.id,\r\n          page: that.page\r\n        }).then(function(res) {\r\n          if (res.code == 200) {\r\n            that.loadStatus = \"more\"\r\n            \r\n            if (res.data.data.length > 0) {\r\n              that.list = that.list.concat(res.data.data)\r\n              that.page = res.data.current_page\r\n              that.isEmpty = false\r\n            } else if (that.page == 1) {\r\n              that.isEmpty = true\r\n            }\r\n          }\r\n        })\r\n      } else {\r\n        // 使用mock数据\r\n        setTimeout(() => {\r\n          that.loadStatus = \"more\"\r\n          \r\n          // 如果没有传入商品信息，使用第一个mock商品\r\n          if (that.goodsInfo.id == 0) {\r\n            const mockGood = that.mockGoods[0]\r\n            that.goodsInfo.id = mockGood.id\r\n            that.goodsInfo.img = mockGood.img\r\n            that.goodsInfo.name = mockGood.name\r\n            that.goodsInfo.count = mockGood.count\r\n          }\r\n          \r\n          // 获取当前商品的评价\r\n          let mockEvaluateList = that.mockEvaluates[that.goodsInfo.id] || []\r\n          \r\n          // 更新评价数量\r\n          that.goodsInfo.count = mockEvaluateList.length\r\n          \r\n          // 模拟分页\r\n          const pageSize = 3\r\n          const startIndex = (that.page - 1) * pageSize\r\n          const endIndex = startIndex + pageSize\r\n          const pageData = mockEvaluateList.slice(startIndex, endIndex)\r\n          \r\n          if (pageData.length > 0) {\r\n            that.list = that.list.concat(pageData)\r\n            that.isEmpty = false\r\n          } else if (that.page == 1) {\r\n            that.isEmpty = true\r\n          }\r\n        }, 500)\r\n      }\r\n    },\r\n    \r\n    // 点赞回调\r\n    likeClick(e) {\r\n      this.list[e.idx].is_like = e.is_like\r\n      this.list[e.idx].like_count = e.like_count\r\n    },\r\n    \r\n    // 返回\r\n    navBack() {\r\n      if (getCurrentPages().length > 1) {\r\n        uni.navigateBack();\r\n      } else {\r\n        uni.switchTab({\r\n          url: \"/pages/tabbar/shop\"\r\n        });\r\n      }\r\n    }\r\n  },\r\n  onReachBottom() {\r\n    if (!this.isEmpty && this.list.length) {\r\n      this.loadStatus = \"loading\"\r\n      this.page = this.page + 1\r\n      this.goodsDynamic()\r\n    }\r\n  },\r\n  onPageScroll(e) {\r\n    // 计算导航栏透明度 (0-1)\r\n    let t = (e.scrollTop > 150 ? 150 : e.scrollTop) / 150;\r\n    this.navbarTrans = t;\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.nav-box{position:fixed;top:0;left:0;width:100%;z-index:99;box-sizing:border-box}\r\n.nav-box .nav-back{padding:0 30rpx;width:34rpx;height:100%}\r\n.nav-box .nav-title{max-width:60%;font-size:32rpx;font-weight:700}\r\n.bg-mk,.bg-mk2,.bg-img{position:absolute;width:100%;height:750rpx;top:0;left:0}\r\n.bg-mk{z-index:-1;background:linear-gradient(to left,rgba(255,255,255,.3),#fff)}\r\n.bg-mk2{z-index:-1;background:linear-gradient(to bottom,rgba(255,255,255,.3),#fff,#fff)}\r\n.bg-img{z-index:-2}\r\n.content-box .info-box{width:calc(100% - 60rpx);margin:0 30rpx 30rpx}\r\n.info-box .name{padding:15rpx 0;font-size:38rpx;font-weight:700}\r\n.info-box .unm{color:#333;font-size:20rpx;line-height:20rpx;font-weight:300}\r\n.info-box .unm .tt{margin:0 8rpx;font-size:24rpx;font-weight:700}\r\n.empty-box{width:100%;height:300rpx;flex-direction:column;justify-content:center}\r\n.empty-box image{width:80rpx;height:80rpx;margin-bottom:30rpx}\r\n.empty-box .e1{color:#666;font-size:28rpx;font-weight:700;margin-bottom:10rpx}\r\n.empty-box .e2{color:#999;font-size:24rpx}\r\n.df{display:flex;align-items:center}\r\n.ohto{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}\r\n</style>", "import MiniProgramPage from 'D:/uniapp/vue3/pages/goods/evaluate.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "api.default", "request"], "mappings": ";;;;;AAgDA,MAAA,YAAA,MAAA;AACA,MAAA,SAAA,MAAA;AACA,MAAA,cAAA,MAAA;AAEA,OAAA;AAEA,MAAA,YAAA;AAAA;IAEI;AAAA,IACA;AAAA;;EAGF,OAAA;AACE,WAAA;AAAA;;;;;QAMI,KAAA;AAAA;QAEA,OAAA;AAAA;MAEF,MAAA,CAAA;AAAA,MACA,MAAA;AAAA;MAEA,YAAA;AAAA;AAAA;QAGE;AAAA;UAEE,KAAA;AAAA,UACA,MAAA;AAAA,UACA,OAAA;AAAA;QAEF;AAAA,UACE,IAAA;AAAA,UACA,KAAA;AAAA;UAEA,OAAA;AAAA;QAEF;AAAA,UACE,IAAA;AAAA,UACA,KAAA;AAAA;UAEA,OAAA;AAAA,QACF;AAAA;MAEF,eAAA;AAAA;AAAA,QAEJ,GAAA;AAAA;AAAA,UAEE;AAAA,YACE,IAAA;AAAA;cAEE,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;YAGF,SAAA;AAAA;;;;;;;;;;;;YAaA,eAAA;AAAA,YACA,MAAA;AAAA,YACA,WAAA;AAAA;;YAGA,SAAA;AAAA,cACE,WAAA;AAAA,cACA,SAAA;AAAA;YAEF,KAAA;AAAA;;UAIF;AAAA;;cAGI,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;YAGF,SAAA;AAAA,YACA,MAAA,CAAA;AAAA;;;;YAKA,QAAA;AAAA;;;YAIA,eAAA;AAAA,YACA,MAAA;AAAA;cAEE,EAAA,IAAA,KAAA,MAAA,QAAA;AAAA,cACA,EAAA,IAAA,KAAA,MAAA,OAAA;AAAA;YAEF,KAAA;AAAA;;UAIF;AAAA;;cAGI,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;YAGF,SAAA;AAAA;;;;;;;YAQA,QAAA;AAAA;;;YAIA,eAAA;AAAA,YACA,MAAA;AAAA;YAEA,OAAA;AAAA,cACE;AAAA;gBAEE,YAAA;AAAA,gBACA,aAAA;AAAA,cACF;AAAA;YAEF,SAAA;AAAA,cACE,WAAA;AAAA,cACA,SAAA;AAAA;YAEF,KAAA;AAAA;;UAIF;AAAA;;cAGI,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;;YAIF,MAAA,CAAA;AAAA;;;;;;;;YASA,eAAA;AAAA,YACA,MAAA;AAAA,YACA,OAAA;AAAA,cACE,OAAA;AAAA,cACA,MAAA;AAAA,cACA,MAAA;AAAA;;YAGF,aAAA;AAAA,YACA,eAAA;AAAA,YACA,SAAA;AAAA,cACE,WAAA;AAAA;;YAGF,KAAA;AAAA;;UAIF;AAAA;;cAGI,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;YAGF,SAAA;AAAA;;;;;;;;;;;;YAaA,eAAA;AAAA,YACA,MAAA;AAAA,YACA,OAAA;AAAA;cAEE,OAAA;AAAA;;YAGF,aAAA;AAAA,YACA,eAAA;AAAA,YACA,cAAA;AAAA,YACA,KAAA;AAAA,UACF;AAAA;;QAIF,GAAA;AAAA;AAAA,UAEE;AAAA,YACE,IAAA;AAAA;cAEE,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;;;;;;;;;;;;;;;;YAkBF,eAAA;AAAA,YACA,MAAA;AAAA,YACA,WAAA;AAAA;;;cAIE,EAAA,IAAA,KAAA,MAAA,OAAA;AAAA,cACA,EAAA,IAAA,KAAA,MAAA,OAAA;AAAA;YAEF,KAAA;AAAA;;UAIF;AAAA,YACE,IAAA;AAAA;cAEE,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;;YAIF,MAAA,CAAA;AAAA;;;;;;;;YASA,eAAA;AAAA,YACA,MAAA;AAAA,YACA,OAAA;AAAA;cAEE,OAAA;AAAA;;YAGF,KAAA;AAAA;;UAIF;AAAA;;cAGI,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;YAGF,SAAA;AAAA;;;;;;;;;;;YAYA,eAAA;AAAA,YACA,MAAA;AAAA;YAEA,OAAA;AAAA,cACE;AAAA;gBAEE,YAAA;AAAA,gBACA,aAAA;AAAA,cACF;AAAA;YAEF,WAAA;AAAA;;YAGA,SAAA;AAAA,cACE,WAAA;AAAA;;YAGF,KAAA;AAAA;;UAIF;AAAA;;cAGI,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;;YAIF,MAAA,CAAA;AAAA;;;;;;;;YASA,eAAA;AAAA,YACA,MAAA;AAAA,YACA,OAAA;AAAA,cACE,OAAA;AAAA,cACA,MAAA;AAAA,cACA,MAAA;AAAA;;YAGF,aAAA;AAAA,YACA,eAAA;AAAA,YACA,aAAA;AAAA;YAEA,cAAA;AAAA,YACA,SAAA;AAAA,cACE,WAAA;AAAA,cACA,SAAA;AAAA;YAEF,KAAA;AAAA;;UAIF;AAAA;;cAGI,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;YAGF,SAAA;AAAA;;;;;;;;;;;;YAaA,eAAA;AAAA,YACA,MAAA;AAAA,YACA,OAAA;AAAA,cACE,OAAA;AAAA,cACA,MAAA;AAAA,cACA,MAAA;AAAA;YAEF,WAAA;AAAA;;;cAIE,EAAA,IAAA,KAAA,MAAA,OAAA;AAAA,cACA,EAAA,IAAA,KAAA,MAAA,OAAA;AAAA;YAEF,SAAA;AAAA,cACE,WAAA;AAAA,cACA,SAAA;AAAA;YAEF,KAAA;AAAA,UACF;AAAA;;;;UAMA;AAAA;;cAGI,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;YAGF,SAAA;AAAA;;;;;;;;;;;;;YAcA,eAAA;AAAA,YACA,MAAA;AAAA,YACA,WAAA;AAAA;;;cAIE,EAAA,IAAA,KAAA,MAAA,OAAA;AAAA,cACA,EAAA,IAAA,KAAA,MAAA,OAAA;AAAA;YAEF,SAAA;AAAA,cACE,WAAA;AAAA,cACA,SAAA;AAAA;YAEF,KAAA;AAAA;;UAIF;AAAA;;cAGI,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;YAGF,SAAA;AAAA,YACA,MAAA,CAAA;AAAA;;;;;;;;YASA,eAAA;AAAA,YACA,MAAA;AAAA,YACA,OAAA;AAAA,cACE,OAAA;AAAA,cACA,MAAA;AAAA,cACA,MAAA;AAAA;;YAGF,aAAA;AAAA,YACA,eAAA;AAAA,YACA,SAAA;AAAA,cACE,WAAA;AAAA;;YAGF,KAAA;AAAA;;UAIF;AAAA;;cAGI,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;YAGF,SAAA;AAAA;;;;;;;;;;;;YAaA,eAAA;AAAA,YACA,MAAA;AAAA,YACA,OAAA;AAAA,cACE,OAAA;AAAA,cACA,MAAA;AAAA,cACA,MAAA;AAAA;;YAGF,OAAA;AAAA,cACE;AAAA;gBAEE,YAAA;AAAA,gBACA,aAAA;AAAA,cACF;AAAA;YAEF,aAAA;AAAA;YAEA,cAAA;AAAA,YACA,SAAA;AAAA,cACE,WAAA;AAAA,cACA,SAAA;AAAA;YAEF,KAAA;AAAA;;UAIF;AAAA,YACE,IAAA;AAAA;cAEE,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;;YAIF,MAAA,CAAA;AAAA;;;;;;;;YASA,eAAA;AAAA,YACA,MAAA;AAAA,YACA,OAAA;AAAA,cACE,MAAA;AAAA,cACA,OAAA;AAAA;;YAGF,SAAA;AAAA,cACE,WAAA;AAAA,cACA,SAAA;AAAA;YAEF,KAAA;AAAA;;UAIF;AAAA,YACE,IAAA;AAAA;cAEE,IAAA;AAAA;cAEA,QAAA;AAAA,cACA,QAAA;AAAA,cACA,KAAA;AAAA;;;;;;;;;;;;;;YAeF,eAAA;AAAA,YACA,MAAA;AAAA,YACA,WAAA;AAAA;;YAGA,SAAA;AAAA,cACE,WAAA;AAAA,cACA,SAAA;AAAA;YAEF,KAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACI;AAAA;EAEF,OAAA,SAAA;AAEE,UAAA,aAAAA,oBAAA;;;AAIA,QAAA,QAAA,IAAA;;AAEE,WAAA,UAAA,MAAA,QAAA,OAAA;AACA,WAAA,UAAA,OAAA,QAAA,QAAA;AACA,WAAA,UAAA,QAAA,QAAA,SAAA;AACA,WAAA,aAAA;AAAA;AAEA,WAAA,QAAA;AAAA,IACF;AAAA;EAEF,SAAA;AAAA;AAAA,IAEE,eAAA;;AAIE,UAAAC,WAAAA,SAAAA,WAAAA,MAAA,OAAAA,WAAAA,MAAA,IAAA,iBAAA;AACEC,8BAAAD,WAAAA,MAAA,IAAA,iBAAA;AAAA,UACE,IAAA,KAAA,UAAA;AAAA;QAEF,CAAA,EAAA,KAAA,SAAA,KAAA;AACE,cAAA,IAAA,QAAA,KAAA;AACE,iBAAA,aAAA;;AAGE,mBAAA,OAAA,KAAA,KAAA,OAAA,IAAA,KAAA,IAAA;AACA,mBAAA,OAAA,IAAA,KAAA;AACA,mBAAA,UAAA;AAAA,YACF,WAAA,KAAA,QAAA,GAAA;AACE,mBAAA,UAAA;AAAA,YACF;AAAA,UACF;AAAA;;AAIF,mBAAA,MAAA;AACE,eAAA,aAAA;;AAIE,kBAAA,WAAA,KAAA,UAAA,CAAA;;AAEA,iBAAA,UAAA,MAAA,SAAA;AACA,iBAAA,UAAA,OAAA,SAAA;AACA,iBAAA,UAAA,QAAA,SAAA;AAAA,UACF;;AAMA,eAAA,UAAA,QAAA,iBAAA;AAGA,gBAAA,WAAA;AACA,gBAAA,cAAA,KAAA,OAAA,KAAA;AACA,gBAAA,WAAA,aAAA;;AAGA,cAAA,SAAA,SAAA,GAAA;AACE,iBAAA,OAAA,KAAA,KAAA,OAAA,QAAA;AACA,iBAAA,UAAA;AAAA,UACF,WAAA,KAAA,QAAA,GAAA;AACE,iBAAA,UAAA;AAAA,UACF;AAAA;MAEJ;AAAA;;;AAKA,WAAA,KAAA,EAAA,GAAA,EAAA,UAAA,EAAA;AACA,WAAA,KAAA,EAAA,GAAA,EAAA,aAAA,EAAA;AAAA;;IAIF,UAAA;AACE,UAAA,gBAAA,EAAA,SAAA,GAAA;AACED,sBAAA,MAAA,aAAA;AAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA;QAEA,CAAA;AAAA,MACF;AAAA,IACF;AAAA;EAEF,gBAAA;AACE,QAAA,CAAA,KAAA,WAAA,KAAA,KAAA,QAAA;;AAEE,WAAA,OAAA,KAAA,OAAA;AACA,WAAA,aAAA;AAAA,IACF;AAAA;EAEF,aAAA,GAAA;AAEE,QAAA,KAAA,EAAA,YAAA,MAAA,MAAA,EAAA,aAAA;AACA,SAAA,cAAA;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpxBA,GAAG,WAAW,eAAe;"}