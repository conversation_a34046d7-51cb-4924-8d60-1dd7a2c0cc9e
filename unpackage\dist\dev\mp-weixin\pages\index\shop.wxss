
page{
  background:#f8f8f8
}
.nav-box{
  position:fixed;
  top:0;
  width:100%;
  z-index:99;
  box-sizing:border-box;
  background:#fff
}
.nav-box .nav-bar{
  width:calc(100% - 30rpx);
  padding:0 15rpx
}
.nav-bar .nav-icon{
  padding:0 15rpx;
  height:100%;
  position:relative
}
.nav-icon .msg{
  position:absolute;
  top:0;
  right:0;
  left:calc(50% + 8rpx);
  min-width:30rpx;
  height:30rpx;
  line-height:30rpx;
  text-align:center;
  font-size:18rpx;
  font-weight:700;
  color:#fff;
  background:#000;
  border-radius:30rpx;
  border:4rpx solid #fff
}
.nav-icon image{
  width:46rpx;
  height:46rpx
}
.nav-box .nav-classify{
  width:100%;
  overflow:hidden;
  position:relative;
  transition:height .3s ease-in-out
}
.nav-box .classify-scroll{
  width:100%;
  white-space:nowrap
}
.classify-scroll .classify-box{
  width:100%;
  height:68rpx
}
.classify-box .classify-item{
  flex-shrink:0;
  padding:0 30rpx;
  height:68rpx;
  line-height:68rpx;
  font-size:24rpx;
  transition:all .3s ease-in-out
}
.nav-classify .classify-all{
  position:absolute;
  top:0;
  bottom:0;
  right:0;
  height:68rpx;
  padding:0 30rpx;
  font-size:20rpx;
  font-weight:700;
  background:linear-gradient(to right,rgba(255,255,255,0),#fff 20%);
  justify-content:center
}
.classify-all image{
  margin-right:8rpx;
  width:26rpx;
  height:26rpx
}
.content-box{
  width:100%;
  padding-bottom:180rpx
}
.goods-box{
  width:100%;
  display:flex;
  flex-wrap:wrap
}
.goods-box .goods-banner,
.goods-box .goods-item{
  width:calc(50% - 15rpx);
  margin:10rpx 0 0 10rpx;
  background:#fff;
  border-radius:8rpx;
  overflow:hidden
}
.goods-box .goods-banner{
  padding-top:calc(66.67% - 13.33rpx);
  position:relative
}
.goods-banner .banner-swiper{
  position:absolute;
  top:0;
  right:0;
  bottom:0;
  left:0;
  width:100%;
  height:100%
}
.goods-banner .swiper-idor{
  position:absolute;
  width:100%;
  bottom:20rpx;
  justify-content:center
}
.swiper-idor .idor-item{
  margin:0 6rpx;
  width:8rpx;
  height:8rpx;
  border-radius:8rpx;
  background:rgba(255,255,255,.3);
  transition:all .3s
}
.swiper-idor .active{
  width:24rpx;
  background:rgba(255,255,255,.9)
}
.goods-item .goods-img{
  width:100%;
  padding-top:100%;
  position:relative
}
.goods-img .goods-img-item{
  position:absolute;
  top:0;
  right:0;
  bottom:0;
  left:0;
  width:100%;
  height:100%
}
.goods-item .goods-name{
  width:calc(100% - 40rpx);
  margin:15rpx 20rpx;
  font-size:26rpx;
  line-height:36rpx;
  font-weight:500
}
.goods-item .goods-price{
  width:calc(100% - 30rpx);
  margin:0 20rpx 20rpx;
  display:flex;
  align-items:flex-end
}
.goods-price .price-h{
  margin-left:15rpx;
  color:#999;
  font-size:20rpx;
  line-height:20rpx
}
.goods-item .goods-tag{
  width:calc(100% - 30rpx);
  margin:0 15rpx 15rpx;
  display:flex;
  flex-wrap:wrap
}
.goods-tag .tag-item{
  margin:0 5rpx 5rpx;
  height:40rpx;
  padding:0 12rpx;
  line-height:40rpx;
  font-size:18rpx;
  font-weight:500;
  background:#f8f8f8;
  border-radius:8rpx
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.empty-box {
  flex-direction: column;
  padding: 120rpx 0;
}
.empty-box image {
  width: 280rpx;
  height: 280rpx;
}
.empty-box .e1 {
  margin-top: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}
.empty-box .e2 {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
}
.heio {
  justify-content: center;
}
.microlabel {
  position: absolute;
  width: 18rpx;
  height: 18rpx;
  background: #fa5150;
  border-radius: 50%;
  border: 4rpx solid #fff;
}
