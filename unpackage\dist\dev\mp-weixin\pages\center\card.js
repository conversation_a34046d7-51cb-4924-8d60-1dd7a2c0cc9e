"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const config_api = require("../../config/api.js");
const common_assets = require("../../common/assets.js");
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const uniPopup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
const app = getApp();
const _sfc_main = {
  components: {
    uniLoadMore,
    uniPopup
  },
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      barList: ["领券中心", "我的卡券"],
      barIdx: 0,
      list: [],
      page: 1,
      isEmpty: false,
      loadStatus: "loading",
      code: "",
      isThrottling: true,
      isCode: false,
      tipsTitle: "",
      // Mock数据
      mockCoupons: {
        center: [
          {
            id: 1,
            subscript: "满减券",
            price: "50",
            intro: "满200元可用",
            validity: "30",
            is_usable: false
          },
          {
            id: 2,
            subscript: "折扣券",
            price: "30",
            intro: "全场通用",
            validity: "15",
            is_usable: false
          },
          {
            id: 3,
            subscript: "新人券",
            price: "10",
            intro: "新用户专享",
            validity: "7",
            is_usable: false
          }
        ],
        my: [
          {
            id: 4,
            subscript: "满减券",
            price: "100",
            intro: "满300元可用",
            neck_create_time: "2023-01-01",
            expire_time: "2023-12-31",
            is_usable: false
          },
          {
            id: 5,
            subscript: "折扣券",
            price: "20",
            intro: "特定商品可用",
            neck_create_time: "2023-01-15",
            expire_time: "2023-06-30",
            is_usable: true
          }
        ]
      }
    };
  },
  onLoad(options) {
    if (options.type) {
      this.barIdx = parseInt(options.type);
    }
    this.goodsCardList();
  },
  methods: {
    goodsCardList() {
      let that = this;
      if (config_api.api$1 && config_api.api$1.api && config_api.api$1.api.goodsCardListUrl) {
        utils_request.request(config_api.api$1.api.goodsCardListUrl, {
          type: that.barIdx,
          page: that.page
        }).then(function(res) {
          that.isThrottling = true;
          that.loadStatus = "more";
          if (res.data.data.length > 0) {
            if (that.page == 1) {
              that.list = res.data.data;
            } else {
              that.list = that.list.concat(res.data.data);
            }
            that.page = res.data.current_page;
            that.isEmpty = false;
          } else if (that.page == 1) {
            that.isEmpty = true;
          }
        });
      } else {
        setTimeout(() => {
          that.isThrottling = true;
          that.loadStatus = "more";
          const mockData = that.barIdx == 0 ? that.mockCoupons.center : that.mockCoupons.my;
          const pageSize = 5;
          const startIndex = (that.page - 1) * pageSize;
          const endIndex = startIndex + pageSize;
          const pageData = mockData.slice(startIndex, endIndex);
          if (pageData.length > 0) {
            if (that.page == 1) {
              that.list = pageData;
            } else {
              that.list = that.list.concat(pageData);
            }
            that.isEmpty = false;
          } else if (that.page == 1) {
            that.isEmpty = true;
          }
        }, 500);
      }
    },
    barClick(e) {
      if (this.isThrottling) {
        this.isThrottling = false;
        this.barIdx = e.currentTarget.dataset.idx;
        this.page = 1;
        this.goodsCardList();
      }
    },
    goodsCardNeck(e) {
      let that = this;
      const dataset = e.currentTarget.dataset;
      if (dataset.type == 2 && !that.code) {
        return that.opTipsPopup("请输入兑换码！");
      }
      if (config_api.api$1 && config_api.api$1.api && config_api.api$1.api.goodsCardNeckUrl) {
        utils_request.request(config_api.api$1.api.goodsCardNeckUrl, {
          id: dataset.id,
          type: dataset.type,
          code: that.code
        }, "POST").then(function(res) {
          that.opTipsPopup(res.msg);
          if (res.code == 200) {
            app.globalData.isCenterPage = true;
            if (dataset.type == 2) {
              that.barIdx = 1;
            }
            that.list = [];
            that.page = 1;
            that.goodsCardList();
            that.$refs.exchangePopup.close();
          }
        });
      } else {
        setTimeout(() => {
          if (dataset.type == 1) {
            const coupon = that.mockCoupons.center.find((item) => item.id === dataset.id);
            if (coupon) {
              const newCoupon = {
                id: 100 + that.mockCoupons.my.length,
                subscript: coupon.subscript,
                price: coupon.price,
                intro: coupon.intro,
                neck_create_time: (/* @__PURE__ */ new Date()).toISOString().slice(0, 19).replace("T", " "),
                expire_time: new Date(Date.now() + coupon.validity * 24 * 60 * 60 * 1e3).toISOString().slice(0, 19).replace("T", " "),
                is_usable: false
              };
              that.mockCoupons.my.unshift(newCoupon);
              that.opTipsPopup("领取成功");
            }
          } else if (dataset.type == 2) {
            const validCode = /^[A-Za-z0-9]{6,12}$/.test(that.code);
            if (validCode) {
              const newCoupon = {
                id: 200 + that.mockCoupons.my.length,
                subscript: "兑换券",
                price: Math.floor(Math.random() * 100) + 10,
                intro: "兑换码" + that.code + "专享",
                neck_create_time: (/* @__PURE__ */ new Date()).toISOString().slice(0, 19).replace("T", " "),
                expire_time: new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3).toISOString().slice(0, 19).replace("T", " "),
                is_usable: false
              };
              that.mockCoupons.my.unshift(newCoupon);
              that.opTipsPopup("兑换成功");
              that.barIdx = 1;
              that.list = [];
              that.page = 1;
              that.goodsCardList();
              that.$refs.exchangePopup.close();
              that.code = "";
            } else {
              that.opTipsPopup("兑换码无效");
            }
          }
        }, 500);
      }
    },
    exchangeClick(isOpen) {
      if (isOpen) {
        this.$refs.exchangePopup.open();
        this.isCode = true;
      } else {
        this.$refs.exchangePopup.close();
        this.isCode = false;
      }
    },
    opTipsPopup(msg) {
      let that = this;
      that.tipsTitle = msg;
      that.$refs.tipsPopup.open();
      setTimeout(function() {
        that.$refs.tipsPopup.close();
      }, 2e3);
    },
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "/pages/tabbar/center"
        });
      }
    },
    // 辅助函数：格式化日期为datetime格式
    formatDate(date) {
      return date.toISOString().slice(0, 19).replace("T", " ");
    }
  },
  onReachBottom() {
    if (this.list.length) {
      this.page = this.page + 1;
      this.loadStatus = "loading";
      this.goodsCardList();
    }
  }
};
if (!Array) {
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_uni_load_more2 + _easycom_uni_popup2)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    c: $data.titleBarHeight + "px",
    d: common_vendor.f($data.barList, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index == $data.barIdx ? "#000" : "#999",
        c: index == $data.barIdx ? "28rpx" : "26rpx",
        d: index == $data.barIdx ? 1 : 0,
        e: index,
        f: common_vendor.o((...args) => $options.barClick && $options.barClick(...args), index),
        g: index
      };
    }),
    e: $data.statusBarHeight + "px",
    f: $data.isEmpty
  }, $data.isEmpty ? {
    g: common_assets._imports_3$1
  } : {
    h: common_vendor.f($data.list, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.subscript),
        b: common_vendor.t(item.price),
        c: common_vendor.t(item.intro)
      }, $data.barIdx == 0 ? {
        d: common_vendor.t(item.validity)
      } : {}, $data.barIdx == 1 ? {
        e: common_vendor.t(item.neck_create_time),
        f: common_vendor.t(item.expire_time)
      } : {}, $data.barIdx == 0 ? {
        g: common_vendor.o((...args) => $options.goodsCardNeck && $options.goodsCardNeck(...args), item.id),
        h: item.id
      } : {}, {
        i: item.is_usable
      }, item.is_usable ? {} : {}, {
        j: item.id
      });
    }),
    i: common_assets._imports_2$10,
    j: $data.barIdx == 0,
    k: $data.barIdx == 1,
    l: $data.barIdx == 0
  }, {
    m: common_vendor.p({
      status: $data.loadStatus
    }),
    n: "calc(" + ($data.statusBarHeight + $data.titleBarHeight) + "px + 90rpx)",
    o: common_assets._imports_3$4,
    p: common_vendor.o(($event) => $options.exchangeClick(true)),
    q: common_assets._imports_0$4,
    r: common_vendor.o(($event) => $options.exchangeClick(false)),
    s: $data.code,
    t: common_vendor.o(($event) => $data.code = $event.detail.value),
    v: common_vendor.o((...args) => $options.goodsCardNeck && $options.goodsCardNeck(...args)),
    w: common_vendor.sr("exchangePopup", "d456c820-1"),
    x: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    y: common_vendor.t($data.tipsTitle),
    z: common_vendor.sr("tipsPopup", "d456c820-2"),
    A: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/center/card.js.map
