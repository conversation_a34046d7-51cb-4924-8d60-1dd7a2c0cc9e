.comment-input-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 999;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.comment-input-box {
  padding: 20rpx;
  background: #f5f5f5;
  position: relative;
}

/* 编辑器样式 */
.comment-editor {
  width: 100%;
  min-height: 60rpx;
  /* 减小最小高度 */
  max-height: 240rpx;
  /* 减小最大高度 */
  font-size: 28rpx;
  line-height: 36rpx;
  padding: 10rpx 20rpx;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8rpx;
}

/* 为编辑器中的表情图片添加样式 */
.emoji-img {
  vertical-align: middle;
  margin: 0 1px;
  display: inline-block;
  transform: translateY(-1px);
  line-height: 1;
  width: 18px !important;
  height: 18px !important;
}
.comment-tools-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 20rpx;
  border-top: 1rpx solid #f1f1f1;
}
.tools-left {
  display: flex;
  align-items: center;
}
.tool-item {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
}
.tool-item image {
  width: 44rpx;
  height: 44rpx;
}
.send-btn {
  width: 120rpx;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  background-color: #f5f5f5;
  color: #ccc;
  font-size: 28rpx;
  border-radius: 32rpx;
  cursor: pointer;
  position: relative;
  z-index: 10;
}
.send-btn.active {
  background-color: #ff4d6a;
  color: #fff;
  cursor: pointer;
}
.selected-image-preview {
  padding: 20rpx;
  background: #f5f5f5;
  position: relative;
}
.selected-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  object-fit: cover;
}
.delete-image {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  line-height: 36rpx;
  text-align: center;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  font-size: 32rpx;
}
.at-user-popup {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
}
.at-user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f1f1f1;
}
.close-btn {
  color: #999;
  font-size: 28rpx;
}
.at-user-list {
  max-height: 600rpx;
}
.at-user-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.at-user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.at-user-name {
  font-size: 28rpx;
}