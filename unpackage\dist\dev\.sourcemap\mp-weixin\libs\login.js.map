{"version": 3, "file": "login.js", "sources": ["libs/login.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport store from \"../store\";\nimport Cache from '../utils/cache';\nimport {\n\tDebounce\n} from '@/utils/validate.js'\n// #ifdef H5 || APP-PLUS\nimport {\n\tisWeixin\n} from \"../utils\";\nimport auth from './wechat';\n// #endif\n\nimport cacheConfig from './../config/cache.js';\nconst {\n\tLOGIN_STATUS,\n\tUSER_INFO,\n\tEXPIRES_TIME,\n\tSTATE_R_KEY\n} = cacheConfig;\nimport Routine from '@/libs/routine';\n\n\nfunction prePage() {\n\tlet pages = getCurrentPages();\n\tlet prePage = pages[pages.length - 1];\n\t// #ifndef APP-PLUS\n\treturn prePage.route;\n\t// #endif\n\t// #ifdef APP-PLUS\n\treturn prePage.$page.fullPath;\n\t// #endif\n\n}\n\n\n\n\nexport const toLogin = Debounce(_toLogin, 800)\n\nfunction _toLogin(push, pathLogin) {\n\t// #ifdef H5\n\tif (isWeixin()) {\n\t\tif (!uni.getStorageSync('authIng')) {\n\t\t\tstore.commit(\"LOGOUT\");\n\t\t}\n\t} else {\n\t\tstore.commit(\"LOGOUT\");\n\t}\n\t// #endif\n\t// #ifndef H5\n\tstore.commit(\"LOGOUT\");\n\t// #endif\n\tlet path = prePage();\n\n\t// #ifdef H5\n\tpath = location.pathname + location.search;\n\t// #endif\n\tconst BASIC_CONFIG = Cache.get('BASIC_CONFIG') || {}\n\tif (!pathLogin)\n\t\tpathLogin = '/page/users/login/index'\n\tCache.set('login_back_url', path);\n\t// #ifdef H5\n\tif (isWeixin() && BASIC_CONFIG && BASIC_CONFIG.wechat_status) {\n\t\tuni.navigateTo({\n\t\t\turl: '/pages/users/wechat_login/index',\n\t\t});\n\n\t} else {\n\t\tuni.navigateTo({\n\t\t\turl: '/pages/users/login/index'\n\t\t})\n\t}\n\t// #endif\n\n\t// #ifdef MP\n\tlet url\n\tif (!BASIC_CONFIG || !BASIC_CONFIG.wechat_auth_switch) {\n\t\turl = '/pages/users/binding_phone/index?pageType=0'\n\t} else {\n\t\turl = '/pages/users/wechat_login/index'\n\t}\n\tuni.navigateTo({\n\t\turl\n\t})\n\t// #endif\n\n\t// #ifdef APP-PLUS\n\tuni.navigateTo({\n\t\turl: '/pages/users/login/index'\n\t})\n\t// #endif\n\n}\n\n\nexport function checkLogin() {\n\tlet token = Cache.get(LOGIN_STATUS);\n\t// let token\n\tlet expiresTime = Cache.get(EXPIRES_TIME);\n\t// let newTime = Math.round(new Date() / 1000);\n\tif (!token) {\n\t\tuni.setStorageSync('authIng', false)\n\t\tCache.clear(LOGIN_STATUS);\n\t\tCache.clear(EXPIRES_TIME);\n\t\tCache.clear(USER_INFO);\n\t\tCache.clear(STATE_R_KEY);\n\t\treturn false;\n\t} else {\n\t\tstore.commit('UPDATE_LOGIN', token);\n\t\tlet userInfo = Cache.get(USER_INFO, true);\n\t\tif (userInfo) {\n\t\t\tstore.commit('UPDATE_USERINFO', userInfo);\n\t\t}\n\t\treturn true;\n\t}\n\n}\n"], "names": ["cacheConfig", "prePage", "<PERSON><PERSON><PERSON><PERSON>", "store", "<PERSON><PERSON>", "uni"], "mappings": ";;;;;;;AAuBA,MAAM;AAAA,EACL;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,IAAIA;AAIJ,SAAS,UAAU;AAClB,MAAI,QAAQ;AACZ,MAAIC,WAAU,MAAM,MAAM,SAAS,CAAC;AAEpC,SAAOA,SAAQ;AAMhB;AAKY,MAAC,UAAUC,eAAAA,SAAS,UAAU,GAAG;AAE7C,SAAS,SAAS,MAAM,WAAW;AAWlCC,oBAAM,OAAO,QAAQ;AAErB,MAAI,OAAO;AAKX,QAAM,eAAeC,YAAK,MAAC,IAAI,cAAc,KAAK,CAAE;AAGpDA,cAAAA,MAAM,IAAI,kBAAkB,IAAI;AAehC,MAAI;AACJ,MAAI,CAAC,gBAAgB,CAAC,aAAa,oBAAoB;AACtD,UAAM;AAAA,EACR,OAAQ;AACN,UAAM;AAAA,EACN;AACDC,gBAAAA,MAAI,WAAW;AAAA,IACd;AAAA,EACF,CAAE;AASF;AAGO,SAAS,aAAa;AAC5B,MAAI,QAAQD,YAAAA,MAAM,IAAI,YAAY;AAEhBA,cAAK,MAAC,IAAI,YAAY;AAExC,MAAI,CAAC,OAAO;AACXC,wBAAI,eAAe,WAAW,KAAK;AACnCD,sBAAM,MAAM,YAAY;AACxBA,sBAAM,MAAM,YAAY;AACxBA,sBAAM,MAAM,SAAS;AACrBA,sBAAM,MAAM,WAAW;AACvB,WAAO;AAAA,EACT,OAAQ;AACND,gBAAAA,MAAM,OAAO,gBAAgB,KAAK;AAClC,QAAI,WAAWC,YAAK,MAAC,IAAI,WAAW,IAAI;AACxC,QAAI,UAAU;AACbD,kBAAAA,MAAM,OAAO,mBAAmB,QAAQ;AAAA,IACxC;AACD,WAAO;AAAA,EACP;AAEF;;;"}