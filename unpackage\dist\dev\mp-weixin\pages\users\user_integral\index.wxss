.integral-details .header.data-v-9da6c933 {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: 460rpx;
  font-size: 72rpx;
  color: #fff;
  padding: 31rpx 0 45rpx 0;
  box-sizing: border-box;
  text-align: center;
  font-family: "Guildford Pro";
  background-color: var(--view-theme);
}
.integral-details .header .currentScore.data-v-9da6c933 {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 11rpx;
}
.integral-details .header .scoreNum.data-v-9da6c933 {
  font-family: "Guildford Pro";
}
.integral-details .header .line.data-v-9da6c933 {
  width: 60rpx;
  height: 3rpx;
  background-color: #fff;
  margin: 20rpx auto 0 auto;
}
.integral-details .header .nav.data-v-9da6c933 {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  flex: 1;
  margin-top: 35rpx;
}
.integral-details .header .nav .item.data-v-9da6c933 {
  width: 33.33%;
  text-align: center;
}
.integral-details .header .nav .item .num.data-v-9da6c933 {
  color: #fff;
  font-size: 40rpx;
  margin-bottom: 5rpx;
  font-family: "Guildford Pro";
}
.integral-details .wrapper .nav.data-v-9da6c933 {
  flex: 1;
  width: 690rpx;
  border-radius: 20rpx 20rpx 0 0;
  margin: -96rpx auto 0 auto;
  background-color: #f7f7f7;
  height: 96rpx;
  font-size: 30rpx;
  color: #bbb;
}
.integral-details .wrapper .nav .item.data-v-9da6c933 {
  text-align: center;
  width: 50%;
}
.integral-details .wrapper .nav .item.on.data-v-9da6c933 {
  background-color: #fff;
  color: var(--view-theme);
  font-weight: bold;
  border-radius: 20rpx 0 0 0;
}
.integral-details .wrapper .nav .item:nth-of-type(2).on.data-v-9da6c933 {
  border-radius: 0 20rpx 0 0;
}
.integral-details .wrapper .nav .item .iconfont.data-v-9da6c933 {
  font-size: 38rpx;
  margin-right: 10rpx;
}
.integral-details .wrapper .list.data-v-9da6c933 {
  padding: 24rpx 30rpx;
}
.integral-details .wrapper .list.bag-white.data-v-9da6c933 {
  background-color: #fff;
}
.integral-details .wrapper .list .tip.data-v-9da6c933 {
  font-size: 25rpx;
  width: 690rpx;
  height: 60rpx;
  border-radius: 50rpx;
  background-color: #fff5e2;
  border: 1rpx solid #ffeac1;
  color: #c8a86b;
  padding: 0 20rpx;
  box-sizing: border-box;
  margin-bottom: 24rpx;
}
.integral-details .wrapper .list .tip .iconfont.data-v-9da6c933 {
  font-size: 35rpx;
  margin-right: 15rpx;
}
.integral-details .wrapper .list .item.data-v-9da6c933 {
  height: 124rpx;
  border-bottom: 1rpx solid #eee;
  font-size: 24rpx;
  color: #999;
}
.integral-details .wrapper .list .item .state.data-v-9da6c933 {
  font-size: 28rpx;
  color: #282828;
  margin-bottom: 8rpx;
}
.integral-details .wrapper .list .item .num.data-v-9da6c933 {
  font-size: 36rpx;
  font-family: "Guildford Pro";
  color: #16AC57;
}
.integral-details .wrapper .list .item .num.font-color.data-v-9da6c933 {
  color: #E93323 !important;
}
.integral-details .wrapper .list2.data-v-9da6c933 {
  background-color: #fff;
  padding: 24rpx 0;
}
.integral-details .wrapper .list2 .item.data-v-9da6c933 {
  background-image: linear-gradient(to right, #fff7e7 0%, #fffdf9 100%);
  width: 690rpx;
  height: 180rpx;
  position: relative;
  border-radius: 10rpx;
  margin: 0 auto 20rpx auto;
  padding: 0 25rpx 0 180rpx;
  box-sizing: border-box;
}
.integral-details .wrapper .list2 .item .pictrue.data-v-9da6c933 {
  width: 90rpx;
  height: 150rpx;
  position: absolute;
  bottom: 0;
  left: 45rpx;
}
.integral-details .wrapper .list2 .item .pictrue image.data-v-9da6c933 {
  width: 100%;
  height: 100%;
}
.integral-details .wrapper .list2 .item .name.data-v-9da6c933 {
  width: 285rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #c8a86b;
}
.integral-details .wrapper .list2 .item .earn.data-v-9da6c933 {
  font-size: 26rpx;
  color: #c8a86b;
  border: 2rpx solid #c8a86b;
  text-align: center;
  line-height: 52rpx;
  height: 52rpx;
  width: 160rpx;
  border-radius: 50rpx;
}
.apply.data-v-9da6c933 {
  top: 52rpx;
  right: 0;
  position: absolute;
  width: -webkit-max-content;
  width: max-content;
  height: 56rpx;
  padding: 0 14rpx;
  background-color: #fff1db;
  color: #a56a15;
  font-size: 22rpx;
  border-radius: 30rpx 0 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.empty-box.data-v-9da6c933 {
  padding-bottom: 300rpx;
}