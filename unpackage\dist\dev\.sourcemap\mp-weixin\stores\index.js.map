{"version": 3, "file": "index.js", "sources": ["stores/index.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { createPinia } from 'pinia'\n\n// 创建pinia实例\nconst pinia = createPinia()\n\n// 添加优化的持久化插件\npinia.use(({ store }) => {\n  // 定义需要持久化的字段\n  const persistFields = {\n    user: ['userInfo', 'isLogin', 'token', 'settings'],\n    app: ['theme', 'systemInfo', 'config']\n  }\n\n  // 防抖保存函数\n  let saveTimer = null\n  const debouncedSave = (storeId, data) => {\n    if (saveTimer) clearTimeout(saveTimer)\n    saveTimer = setTimeout(() => {\n      try {\n        // 只保存必要的字段，减少存储空间\n        const fieldsToSave = persistFields[storeId] || []\n        const dataToSave = {}\n\n        fieldsToSave.forEach(field => {\n          if (data[field] !== undefined) {\n            dataToSave[field] = data[field]\n          }\n        })\n\n        uni.setStorageSync(`pinia_${storeId}`, JSON.stringify(dataToSave))\n      } catch (error) {\n        console.warn('Pinia持久化失败:', error)\n        // 存储空间不足时，清理旧数据\n        if (error.name === 'QuotaExceededError') {\n          try {\n            uni.removeStorageSync(`pinia_${storeId}`)\n            console.log('已清理存储空间，请重新操作')\n          } catch (e) {\n            console.warn('清理存储失败:', e)\n          }\n        }\n      }\n    }, 1000) // 1秒防抖\n  }\n\n  // 监听状态变化，防抖保存\n  if (store.$id === 'user' || store.$id === 'app') {\n    store.$subscribe((mutation, state) => {\n      debouncedSave(store.$id, state)\n    })\n  }\n\n  // 从本地存储恢复状态\n  if (store.$id === 'user' || store.$id === 'app') {\n    try {\n      const savedState = uni.getStorageSync(`pinia_${store.$id}`)\n      if (savedState) {\n        const parsedState = JSON.parse(savedState)\n        store.$patch(parsedState)\n      }\n    } catch (error) {\n      console.warn('Pinia状态恢复失败:', error)\n      // 如果恢复失败，清理损坏的数据\n      try {\n        uni.removeStorageSync(`pinia_${store.$id}`)\n      } catch (e) {\n        console.warn('清理损坏数据失败:', e)\n      }\n    }\n  }\n})\n\nexport default pinia\n"], "names": ["createPinia", "uni"], "mappings": ";;AAaK,MAAC,QAAQA,cAAAA,YAAa;AAG3B,MAAM,IAAI,CAAC,EAAE,YAAY;AAEvB,QAAM,gBAAgB;AAAA,IACpB,MAAM,CAAC,YAAY,WAAW,SAAS,UAAU;AAAA,IACjD,KAAK,CAAC,SAAS,cAAc,QAAQ;AAAA,EACtC;AAGD,MAAI,YAAY;AAChB,QAAM,gBAAgB,CAAC,SAAS,SAAS;AACvC,QAAI;AAAW,mBAAa,SAAS;AACrC,gBAAY,WAAW,MAAM;AAC3B,UAAI;AAEF,cAAM,eAAe,cAAc,OAAO,KAAK,CAAE;AACjD,cAAM,aAAa,CAAE;AAErB,qBAAa,QAAQ,WAAS;AAC5B,cAAI,KAAK,KAAK,MAAM,QAAW;AAC7B,uBAAW,KAAK,IAAI,KAAK,KAAK;AAAA,UAC/B;AAAA,QACX,CAAS;AAEDC,4BAAI,eAAe,SAAS,OAAO,IAAI,KAAK,UAAU,UAAU,CAAC;AAAA,MAClE,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,QAAA,yBAAa,eAAe,KAAK;AAEjC,YAAI,MAAM,SAAS,sBAAsB;AACvC,cAAI;AACFA,0BAAAA,MAAI,kBAAkB,SAAS,OAAO,EAAE;AACxCA,0BAAAA,MAAY,MAAA,OAAA,yBAAA,eAAe;AAAA,UAC5B,SAAQ,GAAG;AACVA,0BAAAA,MAAA,MAAA,QAAA,yBAAa,WAAW,CAAC;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,IACF,GAAE,GAAI;AAAA,EACR;AAGD,MAAI,MAAM,QAAQ,UAAU,MAAM,QAAQ,OAAO;AAC/C,UAAM,WAAW,CAAC,UAAU,UAAU;AACpC,oBAAc,MAAM,KAAK,KAAK;AAAA,IACpC,CAAK;AAAA,EACF;AAGD,MAAI,MAAM,QAAQ,UAAU,MAAM,QAAQ,OAAO;AAC/C,QAAI;AACF,YAAM,aAAaA,cAAAA,MAAI,eAAe,SAAS,MAAM,GAAG,EAAE;AAC1D,UAAI,YAAY;AACd,cAAM,cAAc,KAAK,MAAM,UAAU;AACzC,cAAM,OAAO,WAAW;AAAA,MACzB;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAAA,MAAa,MAAA,QAAA,yBAAA,gBAAgB,KAAK;AAElC,UAAI;AACFA,sBAAG,MAAC,kBAAkB,SAAS,MAAM,GAAG,EAAE;AAAA,MAC3C,SAAQ,GAAG;AACVA,sBAAAA,MAAa,MAAA,QAAA,yBAAA,aAAa,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACH,CAAC;;"}