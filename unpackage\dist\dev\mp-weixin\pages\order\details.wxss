
page { background: #f8f8f8;
}
.container { padding-bottom: 240rpx;
}
.nav-box {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 99;
  box-sizing: border-box;
}
.nav-box .back {
  padding: 0 30rpx;
  height: 100%;
  justify-content: center;
}
.nav-box .nav-title {
  font-size: 32rpx;
  font-weight: 700;
}
.w100p30 {
  z-index: 1;
  margin: 20rpx 20rpx 0;
  width: calc(100% - 80rpx);
  padding: 30rpx 20rpx;
  border-radius: 30rpx;
  background: #fff;
  overflow: hidden;
  position: relative;
}
.title {
  width: 100%;
  color: #000;
  font-size: 26rpx;
  font-weight: 700;
}
.w100p30 .map-bg {
  position: absolute;
  z-index: -2;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}
.w100p30 .mask-bg {
  position: absolute;
  z-index: -1;
  right: -2rpx;
  bottom: -2rpx;
  width: calc(100% - 24rpx);
  height: calc(100% - 24rpx);
  border: 14rpx solid #fff;
  border-radius: 30rpx;
  background-image: linear-gradient(to right, #fff, rgba(255, 255, 255, .9), rgba(255, 255, 255, .6));
}
.adds-box {
  width: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}
.adds-box .adds-item {
  width: calc(100% - 60rpx);
  font-size: 24rpx;
  font-weight: 300;
}
.adds-box .adds-item .nm {
  padding: 20rpx 0 10rpx;
  font-size: 32rpx;
  font-weight: 700;
}
.adds-item .express {
  margin-top: 20rpx;
  width: 100%;
  font-size: 26rpx;
}
.adds-item .express text {
  margin: 0 8rpx;
  font-weight: 700;
}
.adds-box .adds-icon {
  margin: 0 10rpx 20rpx 0;
  width: 40rpx;
  height: 40rpx;
  background: #000;
  justify-content: center;
  border-radius: 20rpx 20rpx 4rpx;
  box-shadow: 8rpx 8rpx 8rpx -4rpx rgba(0, 0, 0, .1);
  transform: rotate(45deg);
}
.adds-box .adds-icon view {
  width: 16rpx;
  height: 16rpx;
  background: #fff;
  border-radius: 50%;
}
.goods-item {
  padding-top: 30rpx;
  display: flex;
  justify-content: space-between;
  animation: fadeIn .45s ease;
}
.goods-item image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 8rpx;
}
.goods-item .goods-info {
  width: calc(100% - 160rpx);
  font-weight: 700;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.goods-item .goods-info .t1 {
  color: #000;
  font-size: 26rpx;
}
.goods-item .goods-info .t2 {
  color: #999;
  font-size: 24rpx;
}
.goods-item .goods-info .goods-info-bom {
  margin-top: 25rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.goods-info .goods-info-bom .sum {
  color: #999;
  font-size: 20rpx;
}
.list-item {
  padding: 15rpx 0;
  justify-content: space-between;
  color: #000;
  font-size: 24rpx;
}
.list-item .list-right .zs {
  color: #999;
  margin-right: 10rpx;
}
.footer {
  position: fixed;
  z-index: 99;
  bottom: 0;
  left: 0;
  width: calc(100% - 20rpx);
  padding: 20rpx 10rpx;
  background-color: #fff;
  border-top: 2rpx solid #f5f5f5;
  padding-bottom: env(safe-area-inset-bottom);
}
.footer .icon-box {
  margin: 0;
  width: 80rpx !important;
  height: 88rpx;
  padding: 10rpx 0;
  flex-direction: column;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0);
  position: relative;
}
.footer .icon-box image {
  width: 40rpx;
  height: 40rpx;
}
.footer .icon-box text {
  line-height: 18rpx;
  font-size: 18rpx;
}
.footer .footer-btn {
  width: calc(100% - 80rpx);
  justify-content: flex-end;
}
.footer-btn view {
  margin: 0 10rpx;
  padding: 0 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 24rpx;
  font-weight: bolder;
  border-radius: 40rpx;
}
.footer-btn view image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}
.footer-btn .btn1 {
  color: #999;
  border: 2rpx solid #F5F5F5;
}
.footer-btn .btn2 {
  color: #fff;
  background: #000;
  border: 2rpx solid #000;
}
.note-box {
  padding: 15rpx;
  background: #fff;
  border-radius: 30rpx;
}
.note-box .note-add {
  margin: 30rpx;
  width: 400rpx;
  height: 90rpx;
  font-size: 24rpx;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 45rpx;
  justify-content: center;
}
.note-box .note-add image {
  margin-right: 10rpx;
  width: 40rpx;
  height: 40rpx;
}
.popup-box {
  width: calc(100% - 40rpx);
  padding: 20rpx;
  background: #fff;
  border-radius: 30rpx 30rpx 0 0;
  position: relative;
  overflow: hidden;
}
.popup-box .popup-top {
  width: calc(100% - 20rpx);
  padding: 10rpx;
  justify-content: space-between;
}
.popup-top .popup-title .t1 {
  font-size: 38rpx;
  font-weight: 700;
}
.popup-top .popup-title .t2 {
  color: #999;
  font-size: 20rpx;
  font-weight: 300;
}
.popup-top .popup-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f8f8;
  justify-content: center;
  transform: rotate(45deg);
}
.popup-box .popup-btn {
  margin: 40rpx 10rpx;
  width: calc(100% - 20rpx);
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 100rpx;
}
.popup-item {
  width: calc(100% - 20rpx);
  padding: 10rpx;
}
.popup-item .popup-type view {
  margin-right: 20rpx;
  padding: 0 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 20rpx;
  font-weight: 700;
  border-radius: 8rpx;
  border: 1px solid #f5f5f5;
}
.popup-item .popup-type .active {
  border: 1px solid #000;
  background: rgba(0, 0, 0, .125);
}
.popup-item .popup-textarea,
.popup-item .popup-adds {
  margin-top: 30rpx;
  width: calc(100% - 40rpx);
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
}
.popup-item .popup-textarea {
  font-size: 24rpx;
  font-weight: 700;
  min-height: 120rpx;
}
.popup-item .popup-adds {
  display: flex;
  flex-direction: column;
}
.popup-item .popup-adds .a1 {
  font-size: 24rpx;
  font-weight: 700;
}
.popup-item .popup-adds .a2 {
  padding-top: 10rpx;
  color: #999;
  font-size: 20rpx;
  font-weight: 300;
}
.popup-item .popup-wl {
  margin-top: 30rpx;
  width: 100%;
  justify-content: space-between;
}
.popup-item .popup-wl input {
  width: calc(50% - 50rpx);
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
  font-size: 24rpx;
  font-weight: 700;
}
.tips-box {
  padding: 20rpx 30rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  justify-content: center;
}
.tips-box .tips-item {
  color: #fff;
  font-size: 28rpx;
  font-weight: 700;
}
.bf8 {
  background: #f8f8f8;
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
