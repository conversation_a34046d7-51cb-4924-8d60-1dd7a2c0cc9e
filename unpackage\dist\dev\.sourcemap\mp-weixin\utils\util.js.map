{"version": 3, "file": "util.js", "sources": ["utils/util.js"], "sourcesContent": ["/**\r\n * 工具类\r\n */\r\nimport store from '@/store/index.js'\r\nimport appConfig from '@/config/app.js'\r\nconst { HTTP_REQUEST_URL, TOKENNAME } = appConfig;\r\n\r\nexport default {\r\n\t/**\r\n\t * 提示信息\r\n\t * @param {Object} options 配置项\r\n\t */\r\n\tTips(options) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: options.title,\r\n\t\t\ticon: options.icon || 'none',\r\n\t\t\tduration: options.duration || 2000\r\n\t\t});\r\n\t},\r\n\r\n\t/**\r\n\t * 图片上传方法（简化版）\r\n\t * @param {Object} opt 配置项\r\n\t * @param {Function} successCallback 成功回调\r\n\t * @param {Function} errorCallback 失败回调\r\n\t */\r\n\tuploadImageChange: function(opt, successCallback, errorCallback, sizeCallback) {\r\n\t\tlet that = this;\r\n\t\tif (typeof opt === 'string') {\r\n\t\t\tlet url = opt;\r\n\t\t\topt = {};\r\n\t\t\topt.url = url;\r\n\t\t}\r\n\t\tlet count = opt.count || 1,\r\n\t\t\tsizeType = opt.sizeType || ['compressed'],\r\n\t\t\tsourceType = opt.sourceType || ['album', 'camera'],\r\n\t\t\tuploadUrl = opt.url || '',\r\n\t\t\tinputName = opt.name || 'pics',\r\n\t\t\tfileType = opt.fileType || 'image';\r\n\t\t\r\n\t\tuni.chooseImage({\r\n\t\t\tcount: count,\r\n\t\t\tsizeType: sizeType,\r\n\t\t\tsourceType: sourceType,\r\n\t\t\tsuccess: function(res) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '图片上传中...',\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 逐个上传图片\r\n\t\t\t\tlet uploadedCount = 0;\r\n\t\t\t\tlet totalCount = res.tempFilePaths.length;\r\n\t\t\t\tlet uploadedImages = [];\r\n\t\t\t\t\r\n\t\t\t\tfunction uploadNext(index) {\r\n\t\t\t\t\tif (index >= totalCount) {\r\n\t\t\t\t\t\t// 所有图片上传完成\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tif (successCallback) {\r\n\t\t\t\t\t\t\tif (uploadedImages.length === 1) {\r\n\t\t\t\t\t\t\t\t// 单张图片时，返回标准格式\r\n\t\t\t\t\t\t\t\tsuccessCallback({\r\n\t\t\t\t\t\t\t\t\tstatus: 200,\r\n\t\t\t\t\t\t\t\t\tdata: uploadedImages[0]\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tsuccessCallback({\r\n\t\t\t\t\t\t\t\t\tstatus: 200,\r\n\t\t\t\t\t\t\t\t\tdata: uploadedImages\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tlet filePath = res.tempFilePaths[index];\r\n\t\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t\turl: HTTP_REQUEST_URL + '/api/' + uploadUrl,\r\n\t\t\t\t\t\tfilePath: filePath,\r\n\t\t\t\t\t\tname: inputName,\r\n\t\t\t\t\t\tformData: {\r\n\t\t\t\t\t\t\t'filename': inputName\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t[TOKENNAME]: 'Bearer ' + store.state.app.token\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tsuccess: function(uploadRes) {\r\n\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\tlet data = JSON.parse(uploadRes.data);\r\n\t\t\t\t\t\t\t\tconsole.log('上传响应数据:', data);\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 检查响应格式\r\n\t\t\t\t\t\t\t\tif (data.status === 200) {\r\n\t\t\t\t\t\t\t\t\tlet imageUrl = '';\r\n\t\t\t\t\t\t\t\t\tlet imageWide = 0;\r\n\t\t\t\t\t\t\t\t\tlet imageHigh = 0;\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t// 尝试从不同位置获取URL\r\n\t\t\t\t\t\t\t\t\tif (data.data && data.data.url) {\r\n\t\t\t\t\t\t\t\t\t\timageUrl = data.data.url;\r\n\t\t\t\t\t\t\t\t\t\timageWide = data.data.wide || data.data.w || 0;\r\n\t\t\t\t\t\t\t\t\t\timageHigh = data.data.high || data.data.h || 0;\r\n\t\t\t\t\t\t\t\t\t} else if (data.data && data.data.src) {\r\n\t\t\t\t\t\t\t\t\t\timageUrl = data.data.src;\r\n\t\t\t\t\t\t\t\t\t\timageWide = data.data.wide || data.data.w || 0;\r\n\t\t\t\t\t\t\t\t\t\timageHigh = data.data.high || data.data.h || 0;\r\n\t\t\t\t\t\t\t\t\t} else if (data.url) {\r\n\t\t\t\t\t\t\t\t\t\timageUrl = data.url;\r\n\t\t\t\t\t\t\t\t\t\timageWide = data.wide || data.w || 0;\r\n\t\t\t\t\t\t\t\t\t\timageHigh = data.high || data.h || 0;\r\n\t\t\t\t\t\t\t\t\t} else if (data.src) {\r\n\t\t\t\t\t\t\t\t\t\timageUrl = data.src;\r\n\t\t\t\t\t\t\t\t\t\timageWide = data.wide || data.w || 0;\r\n\t\t\t\t\t\t\t\t\t\timageHigh = data.high || data.h || 0;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\tif (imageUrl) {\r\n\t\t\t\t\t\t\t\t\t\tuploadedImages.push({\r\n\t\t\t\t\t\t\t\t\t\t\turl: imageUrl,\r\n\t\t\t\t\t\t\t\t\t\t\twide: imageWide,\r\n\t\t\t\t\t\t\t\t\t\t\thigh: imageHigh\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tconsole.error('无法从响应中获取图片URL:', data);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tconsole.error('上传响应状态错误:', data);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\t\t\tconsole.error('解析上传响应失败:', e, uploadRes.data);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tuploadedCount++;\r\n\t\t\t\t\t\t\tuploadNext(index + 1);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\tconsole.error('图片上传失败:', err);\r\n\t\t\t\t\t\t\tuploadedCount++;\r\n\t\t\t\t\t\t\tuploadNext(index + 1);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuploadNext(0);\r\n\t\t\t},\r\n\t\t\tfail: function(err) {\r\n\t\t\t\tthat.Tips({\r\n\t\t\t\t\ttitle: err.errMsg || '选择图片失败'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\r\n\t/**\r\n\t * 简单视频上传方法\r\n\t * @param {Object} opt 配置项\r\n\t * @param {Function} successCallback 成功回调\r\n\t * @param {Function} errorCallback 失败回调\r\n\t */\r\n\tuploadVideoSimple: function(opt, successCallback, errorCallback) {\r\n\t\tlet that = this;\r\n\t\tif (typeof opt === 'string') {\r\n\t\t\tlet url = opt;\r\n\t\t\topt = {};\r\n\t\t\topt.url = url;\r\n\t\t}\r\n\t\t\r\n\t\tlet count = opt.count || 1,\r\n\t\t\tsourceType = opt.sourceType || ['album', 'camera'],\r\n\t\t\tmaxDuration = opt.maxDuration || 60,\r\n\t\t\tcamera = opt.camera || 'back',\r\n\t\t\tuploadUrl = opt.url || 'upload/video',\r\n\t\t\tinputName = opt.name || 'file';\r\n\t\t\r\n\t\tuni.chooseVideo({\r\n\t\t\tcount: count,\r\n\t\t\tsourceType: sourceType,\r\n\t\t\tmaxDuration: maxDuration,\r\n\t\t\tcamera: camera,\r\n\t\t\tsuccess: function(res) {\r\n\t\t\t\t// 检查视频大小\r\n\t\t\t\tif (res.size && res.size > 100 * 1024 * 1024) {\r\n\t\t\t\t\tthat.Tips({\r\n\t\t\t\t\t\ttitle: '视频大小不能超过100MB'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检查视频时长\r\n\t\t\t\tif (res.duration && res.duration > 60) {\r\n\t\t\t\t\tthat.Tips({\r\n\t\t\t\t\t\ttitle: '视频时长不能超过60秒'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '视频上传中...',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 直接上传视频文件\r\n\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\turl: HTTP_REQUEST_URL + '/api/' + uploadUrl,\r\n\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\tname: inputName,\r\n\t\t\t\t\tformData: {\r\n\t\t\t\t\t\tchunkNumber: 1,\r\n\t\t\t\t\t\tcurrentChunkSize: res.size || 0,\r\n\t\t\t\t\t\tchunkSize: res.size || 0,\r\n\t\t\t\t\t\ttotalChunks: 1,\r\n\t\t\t\t\t\tfilename: `video_${Date.now()}.mp4`,\r\n\t\t\t\t\t\tmd5: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t\t[TOKENNAME]: 'Bearer ' + store.state.app.token\r\n\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\tsuccess: function(uploadRes) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tlet data = JSON.parse(uploadRes.data);\r\n\t\t\t\t\t\t\tif (data.status === 200 && data.data) {\r\n\t\t\t\t\t\t\t\t// 返回标准格式的数据\r\n\t\t\t\t\t\t\t\tlet result = {\r\n\t\t\t\t\t\t\t\t\tstatus: 200,\r\n\t\t\t\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\t\t\t\turl: data.data.url || data.data.file_path || data.data.src || data.url || data.file_path || data.src,\r\n\t\t\t\t\t\t\t\t\t\thigh: res.height || 720,\r\n\t\t\t\t\t\t\t\t\t\twide: res.width || 1280,\r\n\t\t\t\t\t\t\t\t\t\tsize: res.size || 0,\r\n\t\t\t\t\t\t\t\t\t\tduration: res.duration || 0\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\tif (successCallback) successCallback(result);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tconsole.error('视频上传响应格式错误:', data);\r\n\t\t\t\t\t\t\t\tthat.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: data.msg || '上传失败'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tif (errorCallback) errorCallback(data);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\t\tconsole.error('解析视频上传响应失败:', e);\r\n\t\t\t\t\t\t\tthat.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '上传失败，请重试'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tif (errorCallback) errorCallback(e);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthat.Tips({\r\n\t\t\t\t\t\t\ttitle: '上传失败，请重试'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tif (errorCallback) errorCallback(err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tfail: function(err) {\r\n\t\t\t\tthat.Tips({\r\n\t\t\t\t\ttitle: err.errMsg || '选择视频失败'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\r\n\t/**\r\n\t * 保存视频数据\r\n\t * @param {String} videoName 视频名称\r\n\t * @param {String} videoUrl 视频URL\r\n\t * @param {Function} successCallback 成功回调\r\n\t * @param {Function} errorCallback 失败回调\r\n\t */\r\n\tsaveVideoData: function(videoName, videoUrl, successCallback, errorCallback) {\r\n\t\tlet that = this;\r\n\t\t\r\n\t\tuni.request({\r\n\t\t\turl: HTTP_REQUEST_URL + '/api/upload/video_data_save',\r\n\t\t\tmethod: 'POST',\r\n\t\t\tdata: {\r\n\t\t\t\tpid: 0,\r\n\t\t\t\tvideo_name: videoName,\r\n\t\t\t\tvideo_path: videoUrl\r\n\t\t\t},\r\n\t\t\theader: {\r\n\t\t\t\t[TOKENNAME]: 'Bearer ' + store.state.app.token\r\n\t\t\t},\r\n\t\t\tsuccess: function(res) {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tif (res.statusCode === 200 && res.data.status === 200) {\r\n\t\t\t\t\tif (successCallback) successCallback({\r\n\t\t\t\t\t\tstatus: 200,\r\n\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\turl: videoUrl,\r\n\t\t\t\t\t\t\tname: videoName\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.Tips({\r\n\t\t\t\t\t\ttitle: res.data.msg || '保存失败'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tif (errorCallback) errorCallback(res.data);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tfail: function(err) {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tthat.Tips({\r\n\t\t\t\t\ttitle: '保存失败，请重试'\r\n\t\t\t\t});\r\n\t\t\t\tif (errorCallback) errorCallback(err);\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\r\n\t/**\r\n\t * 音频上传方法\r\n\t * @param {Object} opt 配置项\r\n\t * @param {Function} successCallback 成功回调\r\n\t * @param {Function} errorCallback 失败回调\r\n\t */\r\n\tuploadAudio: function(opt, successCallback, errorCallback) {\r\n\t\tlet that = this;\r\n\t\tif (typeof opt === 'string') {\r\n\t\t\tlet url = opt;\r\n\t\t\topt = {};\r\n\t\t\topt.url = url;\r\n\t\t}\r\n\t\t\r\n\t\tlet count = opt.count || 1,\r\n\t\t\tsourceType = opt.sourceType || ['album'],\r\n\t\t\tmaxDuration = opt.maxDuration || 300,\r\n\t\t\tuploadUrl = opt.url || 'upload/audio',\r\n\t\t\tinputName = opt.name || 'file';\r\n\t\t\r\n\t\tuni.chooseFile({\r\n\t\t\tcount: count,\r\n\t\t\ttype: 'all',\r\n\t\t\textension: ['mp3', 'wav', 'ogg', 'aac', 'm4a', 'flac'],\r\n\t\t\tsuccess: function(res) {\r\n\t\t\t\t// 检查音频大小\r\n\t\t\t\tif (res.tempFiles && res.tempFiles[0] && res.tempFiles[0].size > 50 * 1024 * 1024) {\r\n\t\t\t\t\tthat.Tips({\r\n\t\t\t\t\t\ttitle: '音频大小不能超过50MB'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '音频上传中...',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 上传音频文件\r\n\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\turl: HTTP_REQUEST_URL + '/api/' + uploadUrl,\r\n\t\t\t\t\tfilePath: res.tempFilePaths[0],\r\n\t\t\t\t\tname: inputName,\r\n\t\t\t\t\tformData: {\r\n\t\t\t\t\t\tchunkNumber: 1,\r\n\t\t\t\t\t\tcurrentChunkSize: res.tempFiles[0].size || 0,\r\n\t\t\t\t\t\tchunkSize: res.tempFiles[0].size || 0,\r\n\t\t\t\t\t\ttotalChunks: 1,\r\n\t\t\t\t\t\tfilename: res.tempFiles[0].name || `audio_${Date.now()}.mp3`,\r\n\t\t\t\t\t\tmd5: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t\t[TOKENNAME]: 'Bearer ' + store.state.app.token\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: function(uploadRes) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tlet data = JSON.parse(uploadRes.data);\r\n\t\t\t\t\t\t\tif (data.status === 200 && data.data) {\r\n\t\t\t\t\t\t\t\t// 返回标准格式的数据\r\n\t\t\t\t\t\t\t\tlet result = {\r\n\t\t\t\t\t\t\t\t\tstatus: 200,\r\n\t\t\t\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\t\t\t\turl: data.data.url || data.data.file_path || data.data.src || data.url || data.file_path || data.src,\r\n\t\t\t\t\t\t\t\t\t\tname: res.tempFiles[0].name || '音频文件',\r\n\t\t\t\t\t\t\t\t\t\tsize: res.tempFiles[0].size || 0\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\tif (successCallback) successCallback(result);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tconsole.error('音频上传响应格式错误:', data);\r\n\t\t\t\t\t\t\t\tthat.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: data.msg || '上传失败'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tif (errorCallback) errorCallback(data);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\t\tconsole.error('解析音频上传响应失败:', e);\r\n\t\t\t\t\t\t\tthat.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '上传失败，请重试'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tif (errorCallback) errorCallback(e);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthat.Tips({\r\n\t\t\t\t\t\t\ttitle: '上传失败，请重试'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tif (errorCallback) errorCallback(err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tfail: function(err) {\r\n\t\t\t\tthat.Tips({\r\n\t\t\t\t\ttitle: err.errMsg || '选择音频失败'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n}; "], "names": ["appConfig", "uni", "store"], "mappings": ";;;;AAKA,MAAM,EAAE,kBAAkB,UAAW,IAAGA;AAExC,MAAe,OAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,KAAK,SAAS;AACbC,kBAAAA,MAAI,UAAU;AAAA,MACb,OAAO,QAAQ;AAAA,MACf,MAAM,QAAQ,QAAQ;AAAA,MACtB,UAAU,QAAQ,YAAY;AAAA,IACjC,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,mBAAmB,SAAS,KAAK,iBAAiB,eAAe,cAAc;AAC9E,QAAI,OAAO;AACX,QAAI,OAAO,QAAQ,UAAU;AAC5B,UAAI,MAAM;AACV,YAAM,CAAA;AACN,UAAI,MAAM;AAAA,IACV;AACE,QAAC,QAAQ,IAAI,SAAS,GACxB,WAAW,IAAI,YAAY,CAAC,YAAY,GACxC,aAAa,IAAI,cAAc,CAAC,SAAS,QAAQ,GACjD,YAAY,IAAI,OAAO,IACvB,YAAY,IAAI,QAAQ;AACb,QAAI,YAAY;AAE5BA,kBAAAA,MAAI,YAAY;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS,SAAS,KAAK;AACtBA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,QACZ,CAAK;AAID,YAAI,aAAa,IAAI,cAAc;AACnC,YAAI,iBAAiB,CAAA;AAErB,iBAAS,WAAW,OAAO;AAC1B,cAAI,SAAS,YAAY;AAExBA,0BAAG,MAAC,YAAW;AACf,gBAAI,iBAAiB;AACpB,kBAAI,eAAe,WAAW,GAAG;AAEhC,gCAAgB;AAAA,kBACf,QAAQ;AAAA,kBACR,MAAM,eAAe,CAAC;AAAA,gBAC/B,CAAS;AAAA,cACT,OAAc;AACN,gCAAgB;AAAA,kBACf,QAAQ;AAAA,kBACR,MAAM;AAAA,gBACf,CAAS;AAAA,cACD;AAAA,YACD;AACD;AAAA,UACA;AAED,cAAI,WAAW,IAAI,cAAc,KAAK;AACtCA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK,mBAAmB,UAAU;AAAA,YAClC;AAAA,YACA,MAAM;AAAA,YACN,UAAU;AAAA,cACT,YAAY;AAAA,YACZ;AAAA,YACD,QAAQ;AAAA,cACP,CAAC,SAAS,GAAG,YAAYC,YAAK,MAAC,MAAM,IAAI;AAAA,YACzC;AAAA,YACD,SAAS,SAAS,WAAW;AAC5B,kBAAI;AACH,oBAAI,OAAO,KAAK,MAAM,UAAU,IAAI;AACpCD,8BAAA,MAAA,MAAA,OAAA,uBAAY,WAAW,IAAI;AAG3B,oBAAI,KAAK,WAAW,KAAK;AACxB,sBAAI,WAAW;AACf,sBAAI,YAAY;AAChB,sBAAI,YAAY;AAGhB,sBAAI,KAAK,QAAQ,KAAK,KAAK,KAAK;AAC/B,+BAAW,KAAK,KAAK;AACrB,gCAAY,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK;AAC7C,gCAAY,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK;AAAA,kBAC7C,WAAU,KAAK,QAAQ,KAAK,KAAK,KAAK;AACtC,+BAAW,KAAK,KAAK;AACrB,gCAAY,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK;AAC7C,gCAAY,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK;AAAA,kBACvD,WAAoB,KAAK,KAAK;AACpB,+BAAW,KAAK;AAChB,gCAAY,KAAK,QAAQ,KAAK,KAAK;AACnC,gCAAY,KAAK,QAAQ,KAAK,KAAK;AAAA,kBAC7C,WAAoB,KAAK,KAAK;AACpB,+BAAW,KAAK;AAChB,gCAAY,KAAK,QAAQ,KAAK,KAAK;AACnC,gCAAY,KAAK,QAAQ,KAAK,KAAK;AAAA,kBACnC;AAED,sBAAI,UAAU;AACb,mCAAe,KAAK;AAAA,sBACnB,KAAK;AAAA,sBACL,MAAM;AAAA,sBACN,MAAM;AAAA,oBACjB,CAAW;AAAA,kBACX,OAAgB;AACNA,+EAAc,kBAAkB,IAAI;AAAA,kBACpC;AAAA,gBACV,OAAe;AACNA,gCAAc,MAAA,MAAA,SAAA,wBAAA,aAAa,IAAI;AAAA,gBAC/B;AAAA,cACD,SAAQ,GAAG;AACXA,oCAAc,MAAA,SAAA,wBAAA,aAAa,GAAG,UAAU,IAAI;AAAA,cAC5C;AAGD,yBAAW,QAAQ,CAAC;AAAA,YACpB;AAAA,YACD,MAAM,SAAS,KAAK;AACnBA,4BAAA,MAAA,MAAA,SAAA,wBAAc,WAAW,GAAG;AAE5B,yBAAW,QAAQ,CAAC;AAAA,YACpB;AAAA,UACP,CAAM;AAAA,QACD;AAED,mBAAW,CAAC;AAAA,MACZ;AAAA,MACD,MAAM,SAAS,KAAK;AACnB,aAAK,KAAK;AAAA,UACT,OAAO,IAAI,UAAU;AAAA,QAC1B,CAAK;AAAA,MACD;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,mBAAmB,SAAS,KAAK,iBAAiB,eAAe;AAChE,QAAI,OAAO;AACX,QAAI,OAAO,QAAQ,UAAU;AAC5B,UAAI,MAAM;AACV,YAAM,CAAA;AACN,UAAI,MAAM;AAAA,IACV;AAED,QAAI,QAAQ,IAAI,SAAS,GACxB,aAAa,IAAI,cAAc,CAAC,SAAS,QAAQ,GACjD,cAAc,IAAI,eAAe,IACjC,SAAS,IAAI,UAAU,QACvB,YAAY,IAAI,OAAO,gBACvB,YAAY,IAAI,QAAQ;AAEzBA,kBAAAA,MAAI,YAAY;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS,SAAS,KAAK;AAEtB,YAAI,IAAI,QAAQ,IAAI,OAAO,MAAM,OAAO,MAAM;AAC7C,eAAK,KAAK;AAAA,YACT,OAAO;AAAA,UACb,CAAM;AACD;AAAA,QACA;AAGD,YAAI,IAAI,YAAY,IAAI,WAAW,IAAI;AACtC,eAAK,KAAK;AAAA,YACT,OAAO;AAAA,UACb,CAAM;AACD;AAAA,QACA;AAEDA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACX,CAAK;AAGDA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,mBAAmB,UAAU;AAAA,UAClC,UAAU,IAAI;AAAA,UACd,MAAM;AAAA,UACN,UAAU;AAAA,YACT,aAAa;AAAA,YACb,kBAAkB,IAAI,QAAQ;AAAA,YAC9B,WAAW,IAAI,QAAQ;AAAA,YACvB,aAAa;AAAA,YACb,UAAU,SAAS,KAAK,IAAG,CAAE;AAAA,YAC7B,KAAK;AAAA,UACL;AAAA,UACD,QAAQ;AAAA,YACP,CAAC,SAAS,GAAG,YAAYC,YAAK,MAAC,MAAM,IAAI;AAAA,UACzC;AAAA,UACK,SAAS,SAAS,WAAW;AAClCD,0BAAG,MAAC,YAAW;AACf,gBAAI;AACH,kBAAI,OAAO,KAAK,MAAM,UAAU,IAAI;AACpC,kBAAI,KAAK,WAAW,OAAO,KAAK,MAAM;AAErC,oBAAI,SAAS;AAAA,kBACZ,QAAQ;AAAA,kBACR,MAAM;AAAA,oBACL,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,aAAa,KAAK,KAAK,OAAO,KAAK,OAAO,KAAK,aAAa,KAAK;AAAA,oBACjG,MAAM,IAAI,UAAU;AAAA,oBACpB,MAAM,IAAI,SAAS;AAAA,oBACnB,MAAM,IAAI,QAAQ;AAAA,oBAClB,UAAU,IAAI,YAAY;AAAA,kBAC1B;AAAA,gBACV;AACQ,oBAAI;AAAiB,kCAAgB,MAAM;AAAA,cACnD,OAAc;AACNA,8BAAA,MAAA,MAAA,SAAA,wBAAc,eAAe,IAAI;AACjC,qBAAK,KAAK;AAAA,kBACT,OAAO,KAAK,OAAO;AAAA,gBAC5B,CAAS;AACD,oBAAI;AAAe,gCAAc,IAAI;AAAA,cACrC;AAAA,YACD,SAAQ,GAAG;AACXA,4BAAc,MAAA,MAAA,SAAA,wBAAA,eAAe,CAAC;AAC9B,mBAAK,KAAK;AAAA,gBACT,OAAO;AAAA,cACf,CAAQ;AACD,kBAAI;AAAe,8BAAc,CAAC;AAAA,YAClC;AAAA,UACD;AAAA,UACD,MAAM,SAAS,KAAK;AACnBA,0BAAG,MAAC,YAAW;AACf,iBAAK,KAAK;AAAA,cACT,OAAO;AAAA,YACd,CAAO;AACD,gBAAI;AAAe,4BAAc,GAAG;AAAA,UACpC;AAAA,QACN,CAAK;AAAA,MACD;AAAA,MACD,MAAM,SAAS,KAAK;AACnB,aAAK,KAAK;AAAA,UACT,OAAO,IAAI,UAAU;AAAA,QAC1B,CAAK;AAAA,MACD;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,eAAe,SAAS,WAAW,UAAU,iBAAiB,eAAe;AAC5E,QAAI,OAAO;AAEXA,kBAAAA,MAAI,QAAQ;AAAA,MACX,KAAK,mBAAmB;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM;AAAA,QACL,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,YAAY;AAAA,MACZ;AAAA,MACD,QAAQ;AAAA,QACP,CAAC,SAAS,GAAG,YAAYC,YAAK,MAAC,MAAM,IAAI;AAAA,MACzC;AAAA,MACD,SAAS,SAAS,KAAK;AACtBD,sBAAG,MAAC,YAAW;AACf,YAAI,IAAI,eAAe,OAAO,IAAI,KAAK,WAAW,KAAK;AACtD,cAAI;AAAiB,4BAAgB;AAAA,cACpC,QAAQ;AAAA,cACR,MAAM;AAAA,gBACL,KAAK;AAAA,gBACL,MAAM;AAAA,cACN;AAAA,YACP,CAAM;AAAA,QACN,OAAW;AACN,eAAK,KAAK;AAAA,YACT,OAAO,IAAI,KAAK,OAAO;AAAA,UAC7B,CAAM;AACD,cAAI;AAAe,0BAAc,IAAI,IAAI;AAAA,QACzC;AAAA,MACD;AAAA,MACD,MAAM,SAAS,KAAK;AACnBA,sBAAG,MAAC,YAAW;AACf,aAAK,KAAK;AAAA,UACT,OAAO;AAAA,QACZ,CAAK;AACD,YAAI;AAAe,wBAAc,GAAG;AAAA,MACpC;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,aAAa,SAAS,KAAK,iBAAiB,eAAe;AAC1D,QAAI,OAAO;AACX,QAAI,OAAO,QAAQ,UAAU;AAC5B,UAAI,MAAM;AACV,YAAM,CAAA;AACN,UAAI,MAAM;AAAA,IACV;AAEE,QAAC,QAAQ,IAAI,SAAS;AACX,QAAI,cAAc,CAAC,OAAO;AACzB,QAAI,eAAe;AACpC,QAAG,YAAY,IAAI,OAAO,gBACvB,YAAY,IAAI,QAAQ;AAEzBA,kBAAAA,MAAI,WAAW;AAAA,MACd;AAAA,MACA,MAAM;AAAA,MACN,WAAW,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,MAAM;AAAA,MACrD,SAAS,SAAS,KAAK;AAEtB,YAAI,IAAI,aAAa,IAAI,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,EAAE,OAAO,KAAK,OAAO,MAAM;AAClF,eAAK,KAAK;AAAA,YACT,OAAO;AAAA,UACb,CAAM;AACD;AAAA,QACA;AAEDA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACX,CAAK;AAGDA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,mBAAmB,UAAU;AAAA,UAClC,UAAU,IAAI,cAAc,CAAC;AAAA,UAC7B,MAAM;AAAA,UACN,UAAU;AAAA,YACT,aAAa;AAAA,YACb,kBAAkB,IAAI,UAAU,CAAC,EAAE,QAAQ;AAAA,YAC3C,WAAW,IAAI,UAAU,CAAC,EAAE,QAAQ;AAAA,YACpC,aAAa;AAAA,YACb,UAAU,IAAI,UAAU,CAAC,EAAE,QAAQ,SAAS,KAAK,IAAK,CAAA;AAAA,YACtD,KAAK;AAAA,UACL;AAAA,UACD,QAAQ;AAAA,YACP,CAAC,SAAS,GAAG,YAAYC,YAAK,MAAC,MAAM,IAAI;AAAA,UACzC;AAAA,UACD,SAAS,SAAS,WAAW;AAC5BD,0BAAG,MAAC,YAAW;AACf,gBAAI;AACH,kBAAI,OAAO,KAAK,MAAM,UAAU,IAAI;AACpC,kBAAI,KAAK,WAAW,OAAO,KAAK,MAAM;AAErC,oBAAI,SAAS;AAAA,kBACZ,QAAQ;AAAA,kBACR,MAAM;AAAA,oBACL,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,aAAa,KAAK,KAAK,OAAO,KAAK,OAAO,KAAK,aAAa,KAAK;AAAA,oBACjG,MAAM,IAAI,UAAU,CAAC,EAAE,QAAQ;AAAA,oBAC/B,MAAM,IAAI,UAAU,CAAC,EAAE,QAAQ;AAAA,kBAC/B;AAAA,gBACV;AACQ,oBAAI;AAAiB,kCAAgB,MAAM;AAAA,cACnD,OAAc;AACNA,8BAAA,MAAA,MAAA,SAAA,wBAAc,eAAe,IAAI;AACjC,qBAAK,KAAK;AAAA,kBACT,OAAO,KAAK,OAAO;AAAA,gBAC5B,CAAS;AACD,oBAAI;AAAe,gCAAc,IAAI;AAAA,cACrC;AAAA,YACD,SAAQ,GAAG;AACXA,4BAAc,MAAA,MAAA,SAAA,wBAAA,eAAe,CAAC;AAC9B,mBAAK,KAAK;AAAA,gBACT,OAAO;AAAA,cACf,CAAQ;AACD,kBAAI;AAAe,8BAAc,CAAC;AAAA,YAClC;AAAA,UACD;AAAA,UACD,MAAM,SAAS,KAAK;AACnBA,0BAAG,MAAC,YAAW;AACf,iBAAK,KAAK;AAAA,cACT,OAAO;AAAA,YACd,CAAO;AACD,gBAAI;AAAe,4BAAc,GAAG;AAAA,UACpC;AAAA,QACN,CAAK;AAAA,MACD;AAAA,MACD,MAAM,SAAS,KAAK;AACnB,aAAK,KAAK;AAAA,UACT,OAAO,IAAI,UAAU;AAAA,QAC1B,CAAK;AAAA,MACD;AAAA,IACJ,CAAG;AAAA,EACD;AACF;;"}