
.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.money-box[data-v-433ff6d7] {
  display: flex;
  align-items: flex-end;
  font-weight: bolder;
}
.money-box .sale[data-v-433ff6d7] {
  margin: 0 0.0625rem;
}
.money-box .unit[data-v-433ff6d7] {
  margin-bottom: 0.0625rem;
}


.container{padding-bottom:7.5rem;}
.btn{height:3.125rem;line-height:3.125rem;text-align:center;font-size:0.75rem;font-weight:bolder;border-radius:1.5625rem;}
.nav-box{position:fixed;top:0;left:0;width:100%;z-index:99;box-sizing:border-box;}
.nav-box .nav-back{padding:0 0.9375rem;width:1.0625rem;height:100%;}
.nav-box .nav-title{font-size:1rem;font-weight:700;}
.content-box,.list-box{width:100%;}
.list-box .list-item{border-top:1px solid #f8f8f8;width:100%;height:7.5rem;position:relative;}
.list-item .choose{width:3.125rem;height:100%;justify-content:center;}
.list-item .choose uni-image,.footer-box .footer-all uni-image{width:1.1875rem;height:1.1875rem;}
.list-item .item-img{width:5.625rem;height:5.625rem;justify-content:center;background:#f8f8f8;border-radius:0.25rem;position:relative;overflow:hidden;}
.list-item .item-img uni-image{position:absolute;width:100%;height:100%;}
.list-item .item-img .sold-out{position:absolute;z-index:1;padding:0 0.9375rem;height:1.25rem;color:#fff;font-size:0.625rem;font-weight:700;border-radius:1.25rem;background:rgba(0,0,0,.4);}
.list-item .info{width:calc(100% - 9.6875rem);height:5.625rem;margin-left:0.9375rem;display:flex;flex-direction:column;justify-content:space-between;}
.list-item .info .info-sp{display:flex;justify-content:space-between;}
.info .info-sp .txt{font-size:0.75rem;color:#000;font-weight:700;}
.info .info-sp .txt-box{margin-top:0.3125rem;padding:0 1.5625rem 0 0.625rem;color:#999;font-size:0.625rem;font-weight:700;height:1.25rem;line-height:1.25rem;border-radius:0.625rem;background:#f8f8f8;position:relative;}
.info .info-sp .txt-box .dw{position:absolute;top:0;right:0;width:1.25rem;height:1.25rem;}
.info .info-sp .more{padding:0 0.9375rem;z-index:2;}
.info .info-sp .quantity-box{margin-right:0.9375rem;height:1.875rem;line-height:1.875rem;border-radius:0.9375rem;border:1px solid #f8f8f8;font-size:0.6875rem;font-weight:700;text-align:center;}
.info .info-sp .quantity-box uni-input{width:1.25rem;height:1.875rem;line-height:1.875rem;color:#000;}
.info .info-sp .quantity-box .quantity-btn{width:1.875rem;height:1.875rem;line-height:1.875rem;}
.list-count{width:calc(100% - 1.875rem);padding:0.46875rem 0.9375rem;justify-content:space-between;font-size:0.75rem;}
.list-count .t1{color:#999;}
.list-count .t2{color:#000;}
.list-count .t3{color:#999;font-weight:700;margin-right:0.3125rem;}
.footer-box{position:fixed;z-index:99;bottom:0;width:calc(100% - 1.875rem);padding:0.9375rem;justify-content:space-between;border-top:1px solid #f8f8f8;background:rgba(255,255,255,.95);padding-bottom:max(env(safe-area-inset-bottom),0.9375rem);}
.footer-box .footer-all{flex-direction:column;justify-content:center;}
.footer-box .footer-all uni-text{margin-top:0.3125rem;color:#000;font-size:0.625rem;}
.footer-box .not{min-width:4.6875rem;padding:0 1.5625rem;color:#999;background:#f8f8f8;}
.footer-box .not uni-text{margin-right:0.3125rem;}
.act{color:#fff!important;background:#000!important;}
.popup-box{width:100%;background:#fff;border-radius:0.9375rem 0.9375rem 0 0;overflow:hidden;}
.popup-box .popup-item{width:calc(100% - 1.875rem);padding:0.9375rem;color:#000;font-size:0.8125rem;font-weight:700;justify-content:space-between;}
.popup-box .popup-item uni-image{width:1.125rem;height:1.125rem;}
.popup-box .popup-item:first-child{padding-top:1.25rem;}
.popup-box .popup-item:last-child{padding-bottom:2.5rem;}
.popup-box .popup-item:hover{background:#f8f8f8;}
.goods-box{width:100%;padding:0.9375rem 0;background:#fff;border-radius:0.9375rem 0.9375rem 0 0;padding-bottom:max(env(safe-area-inset-bottom),0.9375rem);}
.goods-box .gtn{margin:0 0.9375rem;width:calc(100% - 1.875rem);color:#999;background:#f8f8f8;}
.goods-box .goods-top{width:calc(100% - 1.875rem);height:1.5rem;padding:0 0.9375rem;justify-content:space-between;}
.goods-top .popup-name{width:calc(100% - 2.125rem);color:#000;font-size:1rem;font-weight:700;}
.goods-top .popup-close{width:1.5rem;height:1.5rem;border-radius:50%;background:#f8f8f8;justify-content:center;transform:rotate(45deg);}
.goods-box .specs-title{padding:0.9375rem;color:#999;font-size:0.75rem;font-weight:700;}
.goods-box .specs-overflow{overflow-x:auto;width:100%;padding-bottom:1.875rem;}
.specs-box .specs-item{flex-shrink:0;margin-left:0.625rem;background:#fff;width:6.25rem;border-radius:0.25rem;border-width:1px;border-style:solid;position:relative;overflow:hidden;}
.specs-item .fd{position:absolute;z-index:1;top:0.3125rem;right:0.3125rem;width:1.5rem;height:1.5rem;justify-content:center;background:rgba(0,0,0,.3);border-radius:50%;}
.specs-item .img{width:6.25rem;height:6.25rem;display:block;}
.specs-item .name{width:calc(100% - 1.25rem);margin:0.625rem;line-height:0.9375rem;text-align:center;font-size:0.625rem;font-weight:500;}
.empty-box{flex-direction:column;align-items:center;justify-content:center;padding:3.125rem 0;}
.empty-box uni-image{width:6.25rem;height:6.25rem;margin-bottom:0.9375rem;}
.empty-box .e1{font-size:0.875rem;font-weight:bold;margin-bottom:0.3125rem;}
.empty-box .e2{font-size:0.75rem;color:#999;}
.tips-box{padding:0.625rem 0.9375rem;border-radius:0.375rem;justify-content:center;}
.tips-box .tips-item{color:#fff;font-size:0.875rem;font-weight:700;}
.df{display:flex;align-items:center;}
.bUp{box-shadow:0 -2px 5px 0 rgba(0,0,0,0.05);}
.bfw{backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);background:rgba(255,255,255,.8);}
.ohto{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
