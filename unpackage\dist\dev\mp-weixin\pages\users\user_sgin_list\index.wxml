<view class="container"><view class="content-box"><view wx:if="{{false}}" class="debug-info" style="padding:20rpx;background:#f0f0f0;margin-bottom:20rpx;border-radius:10rpx"><text style="font-size:24rpx;color:#666">调试信息：</text><text style="font-size:24rpx;color:#333">signList长度: {{a}}</text><text style="font-size:24rpx;color:#333">loading: {{b}}</text><text style="font-size:24rpx;color:#333">isLogin: {{c}}</text><text style="font-size:24rpx;color:#333">数据: {{d}}</text></view><view wx:if="{{e}}" class="loading-container"><view class="loading-indicator"></view></view><view wx:elif="{{f}}" class="sign-list"><view wx:for="{{g}}" wx:for-item="item" wx:key="c" class="month-group"><view class="month-title">{{item.a}}</view><view class="sign-items"><view wx:for="{{item.b}}" wx:for-item="record" wx:key="d" class="sign-item"><view class="sign-info"><view class="sign-title">{{record.a}}</view><view class="sign-date">{{record.b}}</view></view><view class="sign-reward"><text class="reward-text">+{{record.c}}</text><image class="reward-icon" src="{{h}}"></image></view></view></view></view><view class="load-more"><view wx:if="{{i}}" class="loading-text">加载中...</view><view wx:elif="{{j}}" class="no-more-text">没有更多内容</view><view wx:else class="load-more-text" bindtap="{{k}}">加载更多</view></view></view><empty-page wx:else bindbuttonClick="{{l}}" u-i="72f8cce4-0" bind:__l="__l" u-p="{{m||''}}"/></view><uni-popup wx:if="{{p}}" class="r" u-s="{{['d']}}" u-r="tipsPopup" u-i="72f8cce4-1" bind:__l="__l" u-p="{{p}}"><view class="tips-box df"><view class="tips-item bfh">{{n}}</view></view></uni-popup></view>