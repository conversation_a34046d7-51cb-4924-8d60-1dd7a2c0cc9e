"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const stores_user = require("../../stores/user.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const waterfall = () => "../../components/waterfall/waterfall.js";
const cardGg = () => "../../components/card-gg/card-gg.js";
const emptyPage = () => "../../components/emptyPage/emptyPage.js";
const _sfc_main = {
  components: {
    lazyImage,
    uniLoadMore,
    waterfall,
    cardGg,
    emptyPage
  },
  data() {
    var _a, _b, _c, _d;
    return {
      userStore: stores_user.useUserStore(),
      statusBarHeight: ((_b = (_a = this.$store) == null ? void 0 : _a.state) == null ? void 0 : _b.statusBarHeight) || 20,
      titleBarHeight: ((_d = (_c = this.$store) == null ? void 0 : _c.state) == null ? void 0 : _d.titleBarHeight) || 44,
      navbarTrans: 0,
      userInfo: {
        id: 0,
        uid: 0,
        avatar: "",
        name: "昵称加载中",
        nickname: "",
        intro: "",
        about_me: "",
        gender: 0,
        sex: 0,
        age: "",
        constellation: 0,
        constellation_label: "",
        province: "",
        follow_count: 0,
        fans_count: 0,
        like_count: 0,
        like_count_str: 0,
        is_follow: 0,
        is_mutual_follow: 0,
        circle: [],
        user_id_number: "",
        privacy: {
          like: 1,
          follow: 1
        },
        home_background: "[]",
        visitors: [],
        // 访客记录
        // VIP相关信息
        vip: false,
        vip_id: 0,
        vip_icon: "",
        vip_name: "",
        vip_status: 2,
        svip_open: false,
        is_ever_level: 0,
        is_money_level: 0,
        overdue_time: 0
      },
      barList: ["笔记", "赞过"],
      barIdx: 0,
      isThrottling: false,
      list: [],
      page: 1,
      isEmpty: false,
      loadStatus: "loading",
      tipsTitle: "",
      isWaterfall: false,
      backgroundImages: [],
      currentBgIndex: 0,
      carouselTimer: null,
      userId: 0,
      isFollowing: false,
      limit: 10,
      isLoading: false,
      isProcessing: false,
      // 页面状态管理
      pageLoaded: false,
      userLoaded: false,
      // 错误状态
      hasError: false,
      errorMessage: ""
    };
  },
  computed: {
    isLogin() {
      return this.userStore.isLoggedIn;
    },
    loginUserId() {
      return this.userStore.uid || 0;
    },
    // 关注按钮样式
    followBtnClass() {
      if (parseInt(this.userInfo.is_mutual_follow) === 1) {
        return "mutual";
      } else if (this.isFollowing || parseInt(this.userInfo.is_follow) === 1) {
        return "active";
      } else {
        return "";
      }
    },
    // 关注按钮文本
    followBtnText() {
      if (parseInt(this.userInfo.is_mutual_follow) === 1) {
        return "互相关注";
      } else if (this.isFollowing || parseInt(this.userInfo.is_follow) === 1) {
        return "已关注";
      } else {
        return "＋关注";
      }
    }
  },
  async onLoad(options) {
    try {
      if (typeof common_vendor.index.showShareMenu === "function") {
        common_vendor.index.showShareMenu();
      }
    } catch (e) {
      common_vendor.index.__f__("warn", "at pages/user/details.vue:318", "showShareMenu not supported on this platform:", e);
    }
    await this.$onLaunched;
    common_vendor.index.__f__("log", "at pages/user/details.vue:324", "页面onLoad参数:", options);
    try {
      const userId = (options == null ? void 0 : options.id) || (options == null ? void 0 : options.user_id) || (options == null ? void 0 : options.uid) || 0;
      if (userId) {
        this.userId = parseInt(userId);
        if (isNaN(this.userId) || this.userId <= 0) {
          common_vendor.index.__f__("error", "at pages/user/details.vue:335", "用户ID无效:", userId);
          this.opTipsPopup("用户ID无效", true);
          return;
        }
        const loginUserId = this.loginUserId || common_vendor.index.getStorageSync("uid") || 0;
        const isSelfProfile = this.userId === parseInt(loginUserId);
        common_vendor.index.__f__("log", "at pages/user/details.vue:344", "加载用户ID:", this.userId, "是否自己:", isSelfProfile);
        if (isSelfProfile) {
          this.barList = ["笔记", "赞过"];
        } else {
          this.barList = ["笔记", "赞过"];
        }
        this.getUserInfo();
        this.loadDynamicList();
      } else {
        common_vendor.index.__f__("error", "at pages/user/details.vue:357", "缺少用户ID参数");
        this.opTipsPopup("用户状态异常或已注销！", true);
      }
    } catch (e) {
      common_vendor.index.__f__("error", "at pages/user/details.vue:361", "onLoad异常:", e);
      this.opTipsPopup("加载用户信息失败", true);
    }
  },
  onUnload() {
    this.clearCarouselTimer();
  },
  methods: {
    /**
     * 获取用户信息
     */
    getUserInfo() {
      common_vendor.index.__f__("log", "at pages/user/details.vue:374", "开始获取用户信息, userId:", this.userId);
      if (!this.userId || this.userId <= 0) {
        common_vendor.index.__f__("error", "at pages/user/details.vue:378", "获取用户信息失败: 用户ID无效", this.userId);
        this.opTipsPopup("用户ID无效", true);
        return;
      }
      common_vendor.index.showLoading({
        title: "加载中...",
        mask: true
      });
      api_social.getUserHomepage({
        user_id: this.userId
      }).then((res) => {
        common_vendor.index.__f__("log", "at pages/user/details.vue:393", "获取用户信息成功:", res);
        if (res.data) {
          this.userInfo = res.data;
          if (!this.userInfo.id && this.userInfo.uid) {
            this.userInfo.id = this.userInfo.uid;
          }
          if (!this.userInfo.name && this.userInfo.nickname) {
            this.userInfo.name = this.userInfo.nickname;
          }
          if (!this.userInfo.intro && this.userInfo.about_me) {
            this.userInfo.intro = this.userInfo.about_me;
          }
          this.userInfo.is_follow = parseInt(this.userInfo.is_follow || 0);
          this.userInfo.is_mutual_follow = parseInt(this.userInfo.is_mutual_follow || 0);
          this.isFollowing = this.userInfo.is_follow === 1;
          if (!this.userInfo.privacy) {
            this.userInfo.privacy = {
              like: 1,
              follow: 1
            };
          }
          if (res.data.visitors && Array.isArray(res.data.visitors)) {
            this.userInfo.visitors = res.data.visitors;
            common_vendor.index.__f__("log", "at pages/user/details.vue:431", "获取到访客记录:", res.data.visitors.length, "条");
          } else {
            this.userInfo.visitors = [];
          }
          if (res.data.vip !== void 0) {
            this.userInfo.vip = res.data.vip;
            this.userInfo.vip_id = res.data.vip_id || 0;
            this.userInfo.vip_icon = res.data.vip_icon || "";
            this.userInfo.vip_name = res.data.vip_name || "";
            this.userInfo.vip_status = res.data.vip_status || 2;
            this.userInfo.svip_open = res.data.svip_open || false;
            this.userInfo.is_ever_level = res.data.is_ever_level || 0;
            this.userInfo.is_money_level = res.data.is_money_level || 0;
            this.userInfo.overdue_time = res.data.overdue_time || 0;
            common_vendor.index.__f__("log", "at pages/user/details.vue:447", "获取到VIP信息:", {
              vip: this.userInfo.vip,
              vip_name: this.userInfo.vip_name,
              vip_status: this.userInfo.vip_status,
              svip_open: this.userInfo.svip_open
            });
          }
          common_vendor.index.__f__("log", "at pages/user/details.vue:455", "处理后的用户信息:", {
            id: this.userInfo.id,
            name: this.userInfo.name,
            is_follow: this.userInfo.is_follow,
            is_mutual_follow: this.userInfo.is_mutual_follow,
            isFollowing: this.isFollowing,
            privacy: this.userInfo.privacy,
            visitorsCount: this.userInfo.visitors.length,
            vip: this.userInfo.vip,
            vip_status: this.userInfo.vip_status
          });
          this.updateBackgroundImages();
          common_vendor.index.setNavigationBarTitle({
            title: this.userInfo.name || "用户详情"
          });
        } else {
          common_vendor.index.__f__("error", "at pages/user/details.vue:475", "获取用户信息返回数据为空");
          this.opTipsPopup("获取用户信息失败", true);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/user/details.vue:479", "获取用户信息失败", err);
        this.opTipsPopup("获取用户信息失败: " + (err.msg || "网络错误"), true);
      }).finally(() => {
        common_vendor.index.hideLoading();
      });
    },
    /**
     * 加载用户动态列表
     */
    loadDynamicList() {
      if (this.isLoading || this.loadStatus === "noMore")
        return;
      this.isLoading = true;
      this.loadStatus = "loading";
      const isLikedTab = this.barIdx === 1;
      const isOwnProfile = this.userId === this.loginUserId;
      let apiCall;
      if (isLikedTab) {
        common_vendor.index.__f__("log", "at pages/user/details.vue:503", "加载点赞动态列表 - 用户ID:", this.userId);
        apiCall = api_social.getLikeDynamicList(this.userId, {
          page: this.page,
          limit: this.limit
        });
      } else {
        common_vendor.index.__f__("log", "at pages/user/details.vue:510", "加载用户动态列表, userId:", this.userId, "barIdx:", this.barIdx);
        apiCall = api_social.getOtherUserDynamicList(this.userId, {
          page: this.page,
          limit: this.limit
        });
      }
      apiCall.then((res) => {
        common_vendor.index.__f__("log", "at pages/user/details.vue:518", "动态列表接口返回:", res);
        const list = res.data.list || [];
        if (this.page === 1) {
          this.list = list;
          this.isEmpty = list.length === 0;
        } else {
          this.list = [...this.list, ...list];
        }
        this.loadStatus = list.length < this.limit ? "noMore" : "more";
        this.page++;
        if (this.page === 2 && list.length > 0) {
          const hasMediaContent = list.some(
            (item) => item.type === 2 || item.type === 3 || item.images && item.images.length > 0
          );
          this.isWaterfall = hasMediaContent;
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/user/details.vue:541", "获取动态列表失败", err);
        this.loadStatus = "more";
        if (isLikedTab && !isOwnProfile) {
          if (err.code === 403 || err.status === 403) {
            this.opTipsPopup("该用户已将点赞设为私密");
          } else {
            common_vendor.index.showToast({
              title: "加载失败，请重试",
              icon: "none"
            });
          }
        } else {
          common_vendor.index.showToast({
            title: "加载失败，请重试",
            icon: "none"
          });
        }
      }).finally(() => {
        this.isLoading = false;
        common_vendor.index.stopPullDownRefresh();
      });
    },
    /**
     * 处理关注/取消关注
     */
    handleFollow() {
      if (!this.isLogin) {
        common_vendor.index.navigateTo({
          url: "/pages/login/index"
        });
        return;
      }
      if (this.isProcessing)
        return;
      this.isProcessing = true;
      const targetUserId = parseInt(this.userId);
      if (!targetUserId || targetUserId <= 0) {
        common_vendor.index.showToast({
          title: "获取用户ID失败",
          icon: "none"
        });
        common_vendor.index.__f__("error", "at pages/user/details.vue:591", "关注操作失败: 无效的用户ID", targetUserId);
        this.isProcessing = false;
        return;
      }
      const isMutual = parseInt(this.userInfo.is_mutual_follow) === 1;
      const isFollowed = parseInt(this.userInfo.is_follow) === 1 || isMutual;
      common_vendor.index.__f__("log", "at pages/user/details.vue:600", "当前关注状态:", { isFollowed, isMutual, targetUserId });
      this.$set(this.userInfo, "is_follow", isFollowed ? 0 : 1);
      if (isFollowed) {
        this.$set(this.userInfo, "is_mutual_follow", 0);
      }
      this.isFollowing = !isFollowed;
      common_vendor.index.showToast({
        title: isFollowed ? "取消关注中..." : "关注中...",
        icon: "none",
        duration: 500
      });
      const params = {
        follow_uid: targetUserId,
        // 修改为follow_uid，与card-gg.vue保持一致
        is_follow: isFollowed ? 0 : 1
      };
      common_vendor.index.__f__("log", "at pages/user/details.vue:625", "发送关注请求参数:", JSON.stringify(params));
      api_social.followUser(params).then((res) => {
        common_vendor.index.__f__("log", "at pages/user/details.vue:628", "关注接口返回:", res);
        if (res.status === 200) {
          if (res.data && res.data.is_mutual !== void 0) {
            const isMutual2 = parseInt(res.data.is_mutual) === 1;
            this.$set(this.userInfo, "is_mutual_follow", isMutual2 ? 1 : 0);
            common_vendor.index.__f__("log", "at pages/user/details.vue:637", "更新互相关注状态:", this.userInfo.is_mutual_follow);
          }
          if (res.data && res.data.fans_count !== void 0) {
            this.$set(this.userInfo, "fans_count", res.data.fans_count);
          } else {
            const currentFansCount = parseInt(this.userInfo.fans_count) || 0;
            this.$set(this.userInfo, "fans_count", isFollowed ? Math.max(0, currentFansCount - 1) : currentFansCount + 1);
          }
          common_vendor.index.showToast({
            title: isFollowed ? "已取消关注" : "关注成功",
            icon: "none"
          });
        } else {
          this.userInfo.is_follow = isFollowed ? 1 : 0;
          if (isMutual) {
            this.userInfo.is_mutual_follow = isMutual ? 1 : 0;
          }
          this.isFollowing = isFollowed;
          common_vendor.index.showToast({
            title: res.msg || "操作失败，请重试",
            icon: "none"
          });
        }
      }).catch((err) => {
        this.userInfo.is_follow = isFollowed ? 1 : 0;
        if (isMutual) {
          this.userInfo.is_mutual_follow = isMutual ? 1 : 0;
        }
        this.isFollowing = isFollowed;
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
        common_vendor.index.__f__("error", "at pages/user/details.vue:678", "关注操作异常:", err);
      }).finally(() => {
        this.isProcessing = false;
      });
    },
    /**
     * 处理点赞回调
     */
    likeClick(e) {
      const { id, isLike } = e;
      common_vendor.index.__f__("log", "at pages/user/details.vue:690", "点赞状态变更", id, isLike);
    },
    /**
     * 处理关注回调
     */
    followBack(e) {
      const { idx, uid, is_follow, is_mutual } = e;
      common_vendor.index.__f__("log", "at pages/user/details.vue:698", "关注状态回调", { idx, uid, is_follow, is_mutual });
      if (this.list[idx]) {
        this.$set(this.list[idx], "is_follow", is_follow);
        this.$set(this.list[idx], "is_mutual_follow", is_mutual);
        if (this.list[idx].user_info) {
          this.$set(this.list[idx].user_info, "is_follow", is_follow);
          this.$set(this.list[idx].user_info, "is_mutual_follow", is_mutual);
        }
      }
    },
    /**
     * 下拉刷新
     */
    onPullDownRefresh() {
      common_vendor.index.__f__("log", "at pages/user/details.vue:717", "下拉刷新");
      this.page = 1;
      this.list = [];
      this.isEmpty = false;
      this.loadStatus = "loading";
      Promise.all([
        this.getUserInfo(),
        this.loadDynamicList()
      ]).finally(() => {
        common_vendor.index.stopPullDownRefresh();
      });
    },
    /**
     * 触底加载更多
     */
    onReachBottom() {
      common_vendor.index.__f__("log", "at pages/user/details.vue:738", "触底加载更多, 当前页:", this.page, "加载状态:", this.loadStatus);
      if (this.loadStatus === "more" && !this.isLoading) {
        this.loadDynamicList();
      }
    },
    barClick(e) {
      if (this.isThrottling)
        return;
      const newBarIdx = parseInt(e.currentTarget.dataset.idx);
      if (newBarIdx === this.barIdx)
        return;
      this.isThrottling = true;
      this.barIdx = newBarIdx;
      common_vendor.index.__f__("log", "at pages/user/details.vue:757", "切换标签:", this.barIdx, "用户ID:", this.userId, "登录用户ID:", this.loginUserId);
      const isOtherUserLikedTab = this.barIdx === 1 && this.userId !== this.loginUserId;
      if (isOtherUserLikedTab && this.userInfo.privacy && this.userInfo.privacy.like === 0) {
        common_vendor.index.__f__("log", "at pages/user/details.vue:762", "该用户的点赞内容不可见");
        this.isEmpty = true;
        this.isThrottling = false;
        return;
      }
      common_vendor.index.showLoading({
        title: "加载中...",
        mask: true
      });
      this.page = 1;
      this.list = [];
      this.isEmpty = false;
      this.loadStatus = "loading";
      this.isWaterfall = false;
      this.loadDynamicList();
      setTimeout(() => {
        this.isThrottling = false;
        common_vendor.index.hideLoading();
      }, 300);
    },
    followClick() {
      this.handleFollow();
    },
    toFollow(e) {
      let type = e.currentTarget.dataset.type;
      if (this.userInfo.privacy.follow == 0) {
        let msg = "由于该用户隐私设置，关注列表不可见";
        if (type == 1) {
          msg = "由于该用户隐私设置，粉丝列表不可见";
        }
        return this.opTipsPopup(msg);
      }
      common_vendor.index.navigateTo({
        url: "/pages/center/follow?type=" + type + "&id=" + this.userInfo.id + "&name=" + this.userInfo.name
      });
    },
    likePopupClick(show) {
      if (!show) {
        this.$refs.likePopup.close();
      }
      if (show) {
        this.$refs.likePopup.open();
      }
    },
    navigateToFun(e) {
      common_vendor.index.navigateTo({
        url: "/pages/" + e.currentTarget.dataset.url
      });
    },
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "/pages/index/index"
        });
      }
    },
    opTipsPopup(msg, back) {
      let that = this;
      that.tipsTitle = msg;
      that.$refs.tipsPopup.open();
      setTimeout(function() {
        that.$refs.tipsPopup.close();
        if (back) {
          that.navBack();
        }
      }, 2e3);
    },
    navigationBarColor(color) {
      common_vendor.index.setNavigationBarColor({
        frontColor: color,
        backgroundColor: "#ffffff",
        animation: {
          duration: 400,
          timingFunc: "easeIn"
        }
      });
    },
    switchBackground(index) {
      this.currentBgIndex = index;
      this.startCarousel();
    },
    updateBackgroundImages() {
      try {
        common_vendor.index.__f__("log", "at pages/user/details.vue:860", "处理用户背景图片数据:", this.userInfo.home_background);
        this.backgroundImages = [];
        if (this.userInfo.home_background) {
          let bgImages = [];
          if (typeof this.userInfo.home_background === "string") {
            try {
              if (this.userInfo.home_background.trim() === "" || this.userInfo.home_background.trim() === "[]") {
                bgImages = [];
              } else {
                bgImages = JSON.parse(this.userInfo.home_background);
                common_vendor.index.__f__("log", "at pages/user/details.vue:877", "解析背景图片JSON成功:", bgImages);
              }
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/user/details.vue:880", "解析背景图片JSON失败:", error);
              if (this.userInfo.home_background.trim().startsWith("http")) {
                bgImages = [{ url: this.userInfo.home_background }];
              }
            }
          } else if (Array.isArray(this.userInfo.home_background)) {
            bgImages = this.userInfo.home_background;
          }
          this.backgroundImages = bgImages.filter((item) => item && item.url && item.url.trim() !== "");
        }
        if (this.backgroundImages.length === 0) {
          const avatarUrl = this.userInfo.avatar || "/static/img/default-bg.png";
          this.backgroundImages = [{ url: avatarUrl }];
        }
        common_vendor.index.__f__("log", "at pages/user/details.vue:902", "处理后的背景图片数组:", this.backgroundImages);
        this.currentBgIndex = 0;
        if (this.backgroundImages.length > 1) {
          this.startCarousel();
        } else {
          this.clearCarouselTimer();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/user/details.vue:914", "处理背景图片数据异常:", error);
        this.backgroundImages = [{ url: this.userInfo.avatar || "/static/img/default-bg.png" }];
        this.currentBgIndex = 0;
        this.clearCarouselTimer();
      }
    },
    // 启动背景轮播
    startCarousel() {
      this.clearCarouselTimer();
      if (this.backgroundImages.length > 1) {
        this.carouselTimer = setInterval(() => {
          this.nextBackground();
        }, 4e3);
      }
    },
    // 清理轮播定时器
    clearCarouselTimer() {
      if (this.carouselTimer) {
        clearInterval(this.carouselTimer);
        this.carouselTimer = null;
      }
    },
    // 下一张背景
    nextBackground() {
      if (this.backgroundImages.length > 0) {
        this.currentBgIndex = (this.currentBgIndex + 1) % this.backgroundImages.length;
      }
    },
    /**
     * 重试加载
     */
    retryLoad() {
      this.hasError = false;
      this.errorMessage = "";
      this.page = 1;
      this.list = [];
      this.isEmpty = false;
      this.loadStatus = "loading";
      this.getUserInfo();
      this.loadDynamicList();
    },
    /**
     * 设置错误状态
     */
    setError(message) {
      this.hasError = true;
      this.errorMessage = message;
      this.loadStatus = "more";
    },
    /**
     * 清除错误状态
     */
    clearError() {
      this.hasError = false;
      this.errorMessage = "";
    }
  },
  onPageScroll(e) {
    let frontColor = "#ffffff";
    let ratio = (e.scrollTop > 180 ? 180 : e.scrollTop) / 180;
    if (ratio >= 1) {
      frontColor = "#000000";
    }
    this.navbarTrans = ratio;
    this.navigationBarColor(frontColor);
  },
  onShareAppMessage: function() {
    return {
      title: this.userInfo.name + " 的个人名片",
      imageUrl: this.userInfo.avatar,
      path: "/pages/user/details?id=" + this.userInfo.id
    };
  },
  onShareTimeline() {
    return {
      title: this.userInfo.name + " 的个人名片",
      imageUrl: this.userInfo.avatar,
      query: "id=" + this.userInfo.id
    };
  }
};
if (!Array) {
  const _component_lazy_image = common_vendor.resolveComponent("lazy-image");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _component_emptyPage = common_vendor.resolveComponent("emptyPage");
  const _component_waterfall = common_vendor.resolveComponent("waterfall");
  const _component_card_gg = common_vendor.resolveComponent("card-gg");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_lazy_image + _easycom_uni_load_more2 + _component_emptyPage + _component_waterfall + _component_card_gg + _easycom_uni_popup2)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.n($data.navbarTrans != 1 ? "xwb" : ""),
    b: $data.navbarTrans == 1 ? "/static/img/z.png" : "/static/img/z1.png",
    c: $data.titleBarHeight + "px",
    d: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    e: $data.navbarTrans == 1
  }, $data.navbarTrans == 1 ? {
    f: common_vendor.t($data.userInfo.name)
  } : {}, {
    g: $data.statusBarHeight + "px",
    h: "rgba(255, 255, 255," + $data.navbarTrans + ")",
    i: $data.backgroundImages.length > 0
  }, $data.backgroundImages.length > 0 ? common_vendor.e({
    j: common_vendor.f($data.backgroundImages, (img, index, i0) => {
      return {
        a: "2311e8f0-0-" + i0,
        b: common_vendor.p({
          src: img.url,
          mode: "aspectFill"
        }),
        c: index,
        d: index === $data.currentBgIndex ? 1 : ""
      };
    }),
    k: $data.backgroundImages.length > 1
  }, $data.backgroundImages.length > 1 ? {
    l: common_vendor.f($data.backgroundImages, (item, index, i0) => {
      return {
        a: index,
        b: index === $data.currentBgIndex ? 1 : "",
        c: common_vendor.o(($event) => $options.switchBackground(index), index)
      };
    })
  } : {}) : {
    m: common_vendor.p({
      src: $data.userInfo.avatar || "/static/img/avatar.png",
      mode: "aspectFill"
    })
  }, {
    n: common_vendor.p({
      src: $data.userInfo.avatar
    }),
    o: common_vendor.t($options.followBtnText),
    p: common_vendor.n($options.followBtnClass),
    q: common_vendor.o((...args) => $options.followClick && $options.followClick(...args)),
    r: common_vendor.t($data.userInfo.name),
    s: $data.userInfo.intro
  }, $data.userInfo.intro ? {
    t: common_vendor.t($data.userInfo.intro)
  } : {}, {
    v: $data.userInfo.gender != void 0 || $data.userInfo.sex != void 0
  }, $data.userInfo.gender != void 0 || $data.userInfo.sex != void 0 ? {
    w: $data.userInfo.gender == 1 || $data.userInfo.sex == 1 ? "/static/img/nan.png" : "/static/img/nv.png"
  } : {}, {
    x: $data.userInfo.constellation_label
  }, $data.userInfo.constellation_label ? {
    y: common_vendor.t($data.userInfo.constellation_label)
  } : $data.userInfo.age && $data.userInfo.age != "暂不展示" ? {
    A: common_vendor.t($data.userInfo.age)
  } : {}, {
    z: $data.userInfo.age && $data.userInfo.age != "暂不展示",
    B: common_vendor.t($data.userInfo.province || "未知"),
    C: common_vendor.t($data.userInfo.follow_count),
    D: common_vendor.o((...args) => $options.toFollow && $options.toFollow(...args)),
    E: common_vendor.t($data.userInfo.fans_count),
    F: common_vendor.o((...args) => $options.toFollow && $options.toFollow(...args)),
    G: common_vendor.t($data.userInfo.like_count_str),
    H: common_vendor.o(($event) => $options.likePopupClick(true)),
    I: $data.statusBarHeight + $data.titleBarHeight + "px",
    J: $data.userInfo.circle.length
  }, $data.userInfo.circle.length ? {
    K: common_vendor.t($data.userInfo.name),
    L: common_vendor.f($data.userInfo.circle, (item, index, i0) => {
      return {
        a: item.avatar,
        b: common_vendor.t(item.name),
        c: index,
        d: "note/circle?id=" + item.id,
        e: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), index)
      };
    })
  } : {}, {
    M: common_vendor.f($data.barList, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index == $data.barIdx ? "#000" : "#999",
        c: index == $data.barIdx ? "28rpx" : "26rpx",
        d: index == $data.barIdx ? 1 : 0,
        e: index,
        f: common_vendor.o((...args) => $options.barClick && $options.barClick(...args), index),
        g: index
      };
    }),
    N: $data.statusBarHeight + $data.titleBarHeight - 1 + "px",
    O: $data.loadStatus == "loading" && !$data.isThrottling
  }, $data.loadStatus == "loading" && !$data.isThrottling ? {
    P: common_vendor.p({
      status: $data.loadStatus
    })
  } : {}, {
    Q: $data.hasError
  }, $data.hasError ? {
    R: common_vendor.o($options.retryLoad),
    S: common_vendor.p({
      title: "加载失败",
      description: $data.errorMessage || "网络连接异常，请稍后重试",
      image: "/static/img/empty.png",
      buttonText: "重新加载"
    })
  } : $data.isEmpty && !$data.hasError ? {
    U: common_vendor.p({
      title: $data.barIdx == 0 ? "暂无笔记内容" : "暂无喜欢的内容",
      description: $data.userInfo.name + ($data.barIdx == 0 ? " 还没有发布过" : " 还没有点赞"),
      image: "/static/img/empty.png"
    })
  } : $data.barIdx == 1 && $data.userInfo.privacy.like == 0 ? {
    W: common_vendor.p({
      title: "点赞内容不可见",
      description: "该用户已将点赞设为私密",
      image: "/static/img/empty.png"
    })
  } : $data.list.length > 0 ? common_vendor.e({
    Y: $data.isWaterfall
  }, $data.isWaterfall ? {
    Z: common_vendor.p({
      note: $data.list,
      page: $data.page
    })
  } : {
    aa: common_vendor.f($data.list, (item, index, i0) => {
      return {
        a: common_vendor.o($options.likeClick, index),
        b: common_vendor.o($options.followBack, index),
        c: "2311e8f0-8-" + i0,
        d: common_vendor.p({
          item,
          idx: index
        }),
        e: index
      };
    })
  }, {
    ab: $data.loadStatus !== "loading"
  }, $data.loadStatus !== "loading" ? {
    ac: common_vendor.p({
      status: $data.loadStatus
    })
  } : {}, {
    ad: common_vendor.n($data.isWaterfall ? "dynamic-box" : "")
  }) : {}, {
    T: $data.isEmpty && !$data.hasError,
    V: $data.barIdx == 1 && $data.userInfo.privacy.like == 0,
    X: $data.list.length > 0,
    ae: common_assets._imports_0$9,
    af: common_vendor.t($data.userInfo.name),
    ag: common_vendor.t($data.userInfo.like_count),
    ah: common_vendor.o(($event) => $options.likePopupClick(false)),
    ai: common_vendor.sr("likePopup", "2311e8f0-10"),
    aj: common_vendor.t($data.tipsTitle),
    ak: common_vendor.sr("tipsPopup", "2311e8f0-11"),
    al: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 7;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/details.js.map
