{"version": 3, "file": "kefu.js", "sources": ["api/kefu.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport request from \"@/utils/request.js\";\n\n/**\n * 客服登录\n * @param data object 用户账号密码\n */\nexport function kefuLogin(data) {\n\treturn request.post(\"login\", data, {\n\t\tnoAuth: true,\n\t\tkefu: true\n\t});\n}\n\n/**\n * 获取左侧客服聊天用户列表\n * @constructor\n */\nexport function record(data) {\n\treturn request.get(\"user/record\", data, {\n\t\tnoAuth: true,\n\t\tkefu: true\n\t});\n}\n\n/**\n * 客服话术\n * @constructor\n */\nexport function speeChcraft(data) {\n\treturn request.get(\"service/speechcraft\", data, {\n\t\tnoAuth: true,\n\t\tkefu: true\n\t});\n}\n\n/**\n * 客服转接列表\n * @constructor\n */\nexport function transferList(data) {\n\treturn request.get(\"service/transfer_list\", data, {\n\t\tnoAuth: true,\n\t\tkefu: true\n\t});\n}\n\n/**\n * 商品购买记录\n * @constructor\n */\nexport function productCart(id, data) {\n\treturn request.get(\"product/cart/\" + id, data, {\n\t\tnoAuth: true,\n\t\tkefu: true\n\t});\n}\n\n/**\n * 热销商品\n * @constructor\n */\nexport function productHot(id, data) {\n\treturn request.get(\"product/hot/\" + id, data, {\n\t\tnoAuth: true,\n\t\tkefu: true\n\t});\n}\n\n/**\n * 商品足记\n * @constructor\n */\nexport function productVisit(id, data) {\n\treturn request.get(\"product/visit/\" + id, data, {\n\t\tnoAuth: true,\n\t\tkefu: true\n\t});\n}\n\n/**\n * 客服用户聊天列表\n * @constructor\n */\nexport function serviceList(data) {\n\treturn request.get(\"service/list\", data, {\n\t\tnoAuth: true,\n\t\tkefu: true\n\t});\n}\n\n/**\n * 客服转接\n * @constructor\n */\nexport function serviceTransfer(data) {\n\treturn request.post(\"service/transfer\", data, {\n\t\tnoAuth: true,\n\t\tkefu: true\n\t});\n}\n\n/**\n * 客服详细信息\n * @constructor\n */\nexport function serviceInfo(data) {\n\treturn request.get(\"service/info\", data, {\n\t\tnoAuth: true,\n\t\tkefu: true\n\t});\n}\n\n/**\n * 客服反馈头部信息\n * @constructor\n */\nexport function serviceFeedBack() {\n\treturn request.get(\"user/service/feedback\");\n}\n\n/**\n * 客服反馈\n * @constructor\n */\nexport function feedBackPost(data) {\n\treturn request.post(\"user/service/feedback\", data);\n}\n\n/**\n * 检测登录code\n * @constructor\n */\nexport function codeStauts(data) {\n\treturn request.get(\"user/code\", data);\n}\n/**\n * 获取客服端口\n * @constructor\n */\nexport function getWorkermanUrl(data) {\n\treturn request.get('get_workerman_url', {}, {\n\t\tnoAuth: true\n\t})\n}\n\n/**\n * 客服扫码登录code\n * @constructor\n */\nexport function kefuScanLogin(data) {\n\treturn request.post(\"user/code\", data);\n}\n"], "names": ["request"], "mappings": ";;AAqJO,SAAS,gBAAgB,MAAM;AACrC,SAAOA,sBAAQ,IAAI,qBAAqB,IAAI;AAAA,IAC3C,QAAQ;AAAA,EACV,CAAE;AACF;;"}