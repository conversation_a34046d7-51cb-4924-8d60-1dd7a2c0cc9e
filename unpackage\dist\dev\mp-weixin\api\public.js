"use strict";
const utils_request = require("../utils/request.js");
require("../common/vendor.js");
function getLogo() {
  return utils_request.request.get("wechat/get_logo", {}, {
    noAuth: true
  });
}
function wechatAuthLogin(data) {
  return utils_request.request.get("v2/wechat/auth_login", data, {
    noAuth: true
  });
}
function authType(data) {
  return utils_request.request.get("v2/routine/auth_type", data, {
    noAuth: true
  });
}
function authLogin(data) {
  return utils_request.request.get("v2/routine/auth_login", data, {
    noAuth: true
  });
}
function routineBindingPhone(data) {
  return utils_request.request.post("v2/routine/auth_binding_phone", data, {
    noAuth: true
  });
}
function phoneLogin(data) {
  return utils_request.request.post("v2/routine/phone_login", data, {
    noAuth: true
  });
}
function routineLogin(data) {
  return utils_request.request.get("v2/wechat/routine_auth", data, {
    noAuth: true
  });
}
function basicConfig(name) {
  return utils_request.request.get(`basic_config`, {}, {
    noAuth: true
  });
}
function remoteRegister(data) {
  return utils_request.request.get(`remote_register`, data, {
    noAuth: true
  });
}
exports.authLogin = authLogin;
exports.authType = authType;
exports.basicConfig = basicConfig;
exports.getLogo = getLogo;
exports.phoneLogin = phoneLogin;
exports.remoteRegister = remoteRegister;
exports.routineBindingPhone = routineBindingPhone;
exports.routineLogin = routineLogin;
exports.wechatAuthLogin = wechatAuthLogin;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/public.js.map
