"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const common_assets = require("../../common/assets.js");
const navbar = () => "../../components/navbar/navbar.js";
const uniPopup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
getApp();
const _sfc_main = {
  components: {
    navbar,
    uniPopup
  },
  data() {
    const date = /* @__PURE__ */ new Date();
    const currentYear = date.getFullYear();
    const years = Array.from({ length: 100 }, (_, i) => currentYear - 99 + i);
    const months = Array.from({ length: 12 }, (_, i) => i + 1);
    const days = Array.from({ length: 31 }, (_, i) => i + 1);
    const defaultYearIndex = years.findIndex((year) => year === currentYear - 25);
    return {
      userInfo: {
        avatar: "",
        nickname: "",
        about_me: "",
        sex: 0,
        birthday: "",
        constellation: null,
        school: "",
        hometown: "",
        occupation: "",
        residence_lat: 0,
        residence_lng: 0,
        residence_name: "",
        interest_tags: []
        // 标签数组
      },
      // 照片相关
      backgroundImages: [],
      photoTypes: ["生活照", "旅行照", "才艺照", "回忆照", "美食宠物照"],
      // 生日选择器
      years,
      months,
      days,
      birthdayPickerValue: [defaultYearIndex, 0, 0],
      // 优化的标签状态管理
      tagState: {
        selectedTags: [],
        // 当前选中的标签
        myTags: [],
        // 我的标签数据
        categories: [],
        // 标签分类数据
        currentCategoryIndex: 0,
        // 当前分类索引
        isLoading: false
        // 加载状态
      },
      // 优化的UI状态管理
      uiState: {
        canvasStatus: false,
        canvasWidth: 0,
        canvasHeight: 0,
        tempGender: void 0,
        // 临时存储性别选择
        tipsTitle: ""
        // 提示标题
      },
      // 防抖和性能优化
      debounceTimers: {
        save: null,
        upload: null
      }
    };
  },
  computed: {
    // 格式化生日显示
    formattedBirthday() {
      if (!this.userInfo.birthday)
        return "";
      if (typeof this.userInfo.birthday === "number" || !isNaN(this.userInfo.birthday)) {
        const timestamp = parseInt(this.userInfo.birthday);
        const date = new Date(timestamp * 1e3);
        return this.formatDate(date);
      }
      return this.userInfo.birthday;
    },
    // 获取当前分类的标签
    currentCategoryTags() {
      const currentCategory = this.tagState.categories[this.tagState.currentCategoryIndex];
      return (currentCategory == null ? void 0 : currentCategory.tags) || [];
    }
  },
  onLoad() {
    this.initializeData();
  },
  onShow() {
    this.checkUserChange();
  },
  methods: {
    // 检查用户是否切换
    async checkUserChange() {
      try {
        const currentUserId = this.$store.state.app.uid;
        const currentToken = this.$store.state.app.token;
        if (!currentToken || !currentUserId) {
          common_vendor.index.__f__("log", "at pages/center/means.vue:588", "用户未登录，跳转到登录页");
          common_vendor.index.navigateTo({
            url: "/pages/users/login/index"
          });
          return;
        }
        if (!this.userInfo.uid || currentUserId !== this.userInfo.uid) {
          common_vendor.index.__f__("log", "at pages/center/means.vue:597", "检测到用户切换或数据为空，重新加载数据");
          common_vendor.index.__f__("log", "at pages/center/means.vue:598", "当前用户ID:", currentUserId, "页面用户ID:", this.userInfo.uid);
          await this.refreshUserData();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:602", "检查用户切换失败:", error);
      }
    },
    // 刷新用户数据
    async refreshUserData() {
      try {
        common_vendor.index.showLoading({
          title: "刷新数据...",
          mask: true
        });
        await Promise.all([
          this.getUserInfo(),
          this.loadMyTags()
        ]);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:621", "刷新用户数据失败:", error);
        common_vendor.index.showToast({
          title: "刷新失败，请重试",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 统一初始化数据
    async initializeData() {
      try {
        common_vendor.index.showLoading({
          title: "加载中...",
          mask: true
        });
        await Promise.all([
          this.getUserInfo(),
          this.loadTagCategories(),
          this.loadMyTags(),
          // 添加加载我的标签
          this.getUserLocation()
        ]);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:649", "初始化数据失败:", error);
        common_vendor.index.showToast({
          title: "加载失败，请重试",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 获取用户信息
    async getUserInfo() {
      try {
        const res = await api_social.getUserSocialInfo();
        if (res.code === 200 && res.data) {
          common_vendor.index.__f__("log", "at pages/center/means.vue:664", "获取用户信息成功:", res.data);
          this.userInfo = res.data;
          if (res.data.interest_tags) {
            this.tagState.selectedTags = [...res.data.interest_tags];
          } else {
            this.tagState.selectedTags = [];
          }
          this.backgroundImages = res.data.background_images || [];
          common_vendor.index.setStorageSync("USER_INFO", res.data);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:683", "获取用户信息失败:", error);
        throw error;
      }
    },
    // 优化的标签加载方法
    async loadTagCategories() {
      if (this.tagState.isLoading)
        return;
      this.tagState.isLoading = true;
      try {
        const res = await api_social.getTagsWithCategories();
        if (res.code === 200 && res.data) {
          this.processNewTagsData(res.data);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:699", "加载标签分类失败:", error);
        throw error;
      } finally {
        this.tagState.isLoading = false;
      }
    },
    // 防抖处理方法
    debounce(func, delay = 300, timerKey = "default") {
      if (this.debounceTimers[timerKey]) {
        clearTimeout(this.debounceTimers[timerKey]);
      }
      this.debounceTimers[timerKey] = setTimeout(() => {
        func.call(this);
        this.debounceTimers[timerKey] = null;
      }, delay);
    },
    // 统一的错误处理方法
    handleError(error, context = "操作") {
      common_vendor.index.__f__("error", "at pages/center/means.vue:720", `${context}失败:`, error);
      let message = `${context}失败，请稍后重试`;
      if (error.message) {
        message = error.message;
      } else if (error.msg) {
        message = error.msg;
      }
      common_vendor.index.showToast({
        title: message,
        icon: "none",
        duration: 2e3
      });
    },
    // 优化的照片处理方法
    hasPhotoOfType(type) {
      return this.backgroundImages.some((img) => img.type === type);
    },
    getPhotoByType(type) {
      const photo = this.backgroundImages.find((img) => img.type === type);
      return photo ? photo.url : "";
    },
    getPhotoObject(type) {
      return this.backgroundImages.find((img) => img.type === type) || null;
    },
    getPhotoIndex(type) {
      return this.backgroundImages.findIndex((img) => img.type === type);
    },
    // 处理标签数据
    processNewTagsData(data) {
      try {
        const tagsArray = Array.isArray(data) ? data : data.list || data.data || [];
        this.newTagCategories = tagsArray.map((category) => {
          const tags = this.extractTags(category);
          const originalTags = this.getOriginalTags(category);
          return {
            id: category.id || 0,
            name: category.name || "未分类",
            tags: tags || [],
            originalTags: originalTags || []
          };
        }).filter((category) => category.tags && category.tags.length > 0);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:771", "处理标签数据失败:", error);
        this.newTagCategories = [];
      }
    },
    // 提取标签名称
    extractTags(category) {
      if (Array.isArray(category.tags)) {
        return category.tags.map((tag) => typeof tag === "string" ? tag : tag.name);
      }
      if (Array.isArray(category.tag_list)) {
        return category.tag_list.map((tag) => typeof tag === "string" ? tag : tag.name);
      }
      if (Array.isArray(category.tagList)) {
        return category.tagList.map((tag) => typeof tag === "string" ? tag : tag.name);
      }
      return category.name ? [category.name] : [];
    },
    // 获取原始标签数据
    getOriginalTags(category) {
      if (Array.isArray(category.tags))
        return category.tags;
      if (Array.isArray(category.tag_list))
        return category.tag_list;
      if (Array.isArray(category.tagList))
        return category.tagList;
      return category.name ? [category] : [];
    },
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 获取用户位置信息
    getUserLocation() {
      const cachedLatitude = common_vendor.index.getStorageSync("residence_lat");
      const cachedLongitude = common_vendor.index.getStorageSync("residence_lng");
      if (cachedLatitude && cachedLongitude) {
        this.userInfo.residence_lat = parseFloat(cachedLatitude);
        this.userInfo.residence_lng = parseFloat(cachedLongitude);
        common_vendor.index.__f__("log", "at pages/center/means.vue:815", "个人资料页：从缓存获取到用户位置:", this.userInfo.residence_lat, this.userInfo.residence_lng);
        const cachedAddress = common_vendor.index.getStorageSync("residence_name");
        if (cachedAddress) {
          this.userInfo.residence_name = cachedAddress;
          this.userInfo.province = cachedAddress;
          common_vendor.index.__f__("log", "at pages/center/means.vue:822", "从缓存获取到地址:", cachedAddress);
        } else {
          this.getAddressFromLocation(this.userInfo.residence_lat, this.userInfo.residence_lng);
        }
        return;
      }
      common_vendor.index.__f__("log", "at pages/center/means.vue:831", "个人资料页：开始获取用户位置信息");
      common_vendor.index.getLocation({
        type: "wgs84",
        success: (res) => {
          this.userInfo.residence_lat = res.latitude;
          this.userInfo.residence_lng = res.longitude;
          common_vendor.index.setStorageSync("residence_lat", res.latitude);
          common_vendor.index.setStorageSync("residence_lng", res.longitude);
          common_vendor.index.__f__("log", "at pages/center/means.vue:865", "个人资料页：uni.getLocation获取位置成功:", res.latitude, res.longitude);
          this.getAddressFromLocation(res.latitude, res.longitude);
        },
        fail: (err) => {
          common_vendor.index.__f__("log", "at pages/center/means.vue:871", "个人资料页：获取位置失败，使用默认位置:", err);
          this.setDefaultLocation();
        }
      });
    },
    // 设置默认位置（广州）
    setDefaultLocation() {
      this.userInfo.residence_lat = 23.12908;
      this.userInfo.residence_lng = 113.26436;
      this.userInfo.residence_name = "广东省广州市";
      this.userInfo.province = "广东省广州市";
      common_vendor.index.setStorageSync("residence_lat", this.userInfo.residence_lat);
      common_vendor.index.setStorageSync("residence_lng", this.userInfo.residence_lng);
      common_vendor.index.setStorageSync("residence_name", this.userInfo.residence_name);
      common_vendor.index.__f__("log", "at pages/center/means.vue:890", "个人资料页：使用默认位置（广州）:", this.userInfo.residence_lat, this.userInfo.residence_lng);
      common_vendor.index.__f__("log", "at pages/center/means.vue:891", "设置默认地址:", this.userInfo.residence_name);
    },
    // 强制刷新位置信息
    refreshLocation() {
      common_vendor.index.__f__("log", "at pages/center/means.vue:896", "个人资料页：强制刷新位置信息");
      common_vendor.index.removeStorageSync("residence_lat");
      common_vendor.index.removeStorageSync("residence_lng");
      common_vendor.index.removeStorageSync("residence_name");
      this.getUserLocation();
    },
    // 根据经纬度获取地址信息（逆地理编码）
    getAddressFromLocation(latitude, longitude) {
      common_vendor.index.__f__("log", "at pages/center/means.vue:907", "开始逆地理编码，经纬度:", latitude, longitude);
      common_vendor.index.request({
        url: "https://apis.map.qq.com/ws/geocoder/v1/",
        data: {
          location: `${latitude},${longitude}`,
          key: "F7LBZ-NLU6D-6524Z-PK6ZQ-D47AJ-KRB2I",
          // 腾讯地图API key
          get_poi: 0
        },
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/center/means.vue:918", "逆地理编码响应:", res.data);
          if (res.data.status === 0 && res.data.result) {
            const result = res.data.result;
            const address = result.address;
            const province = result.ad_info.province;
            const city = result.ad_info.city;
            const district = result.ad_info.district;
            let fullAddress = "";
            if (province && city) {
              if (province === city) {
                fullAddress = province + (district || "");
              } else {
                fullAddress = province + city + (district || "");
              }
            } else {
              fullAddress = address || "未知位置";
            }
            this.userInfo.residence_name = fullAddress;
            this.userInfo.province = fullAddress;
            common_vendor.index.setStorageSync("residence_name", fullAddress);
            common_vendor.index.__f__("log", "at pages/center/means.vue:948", "逆地理编码成功，地址:", fullAddress);
            this.opTipsPopup("位置获取成功: " + fullAddress);
          } else {
            common_vendor.index.__f__("error", "at pages/center/means.vue:951", "逆地理编码失败:", res.data);
            this.setFallbackAddress();
          }
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/center/means.vue:956", "逆地理编码请求失败:", err);
          this.setFallbackAddress();
        }
      });
    },
    // 设置备用地址
    setFallbackAddress() {
      const defaultAddress = "位置获取中...";
      this.userInfo.residence_name = defaultAddress;
      this.userInfo.province = defaultAddress;
      common_vendor.index.__f__("log", "at pages/center/means.vue:968", "设置备用地址:", defaultAddress);
    },
    // 同步位置信息到userInfo
    syncLocationInfo() {
      const cachedLatitude = common_vendor.index.getStorageSync("residence_lat");
      const cachedLongitude = common_vendor.index.getStorageSync("residence_lng");
      const cachedAddress = common_vendor.index.getStorageSync("residence_name");
      if (cachedLatitude && cachedLongitude) {
        if (!this.userInfo.residence_lat || !this.userInfo.residence_lng) {
          this.userInfo.residence_lat = parseFloat(cachedLatitude);
          this.userInfo.residence_lng = parseFloat(cachedLongitude);
          common_vendor.index.__f__("log", "at pages/center/means.vue:983", "同步位置信息到userInfo:", this.userInfo.residence_lat, this.userInfo.residence_lng);
        }
        if (cachedAddress && !this.userInfo.residence_name) {
          this.userInfo.residence_name = cachedAddress;
          this.userInfo.province = cachedAddress;
          common_vendor.index.__f__("log", "at pages/center/means.vue:990", "同步地址信息到userInfo:", cachedAddress);
        }
      } else {
        this.getUserLocation();
      }
    },
    // 统一的缓存解析方法
    getUserInfoFromCache() {
      let userInfo = common_vendor.index.getStorageSync("USER_INFO") || {};
      if (typeof userInfo === "string") {
        try {
          userInfo = JSON.parse(userInfo);
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/center/means.vue:1007", "解析USER_INFO缓存失败:", e);
          userInfo = {};
        }
      }
      return userInfo;
    },
    // 处理背景图片数据
    handleBackgroundImages() {
      if (this.userInfo.home_background) {
        try {
          if (typeof this.userInfo.home_background === "string") {
            this.backgroundImages = JSON.parse(this.userInfo.home_background);
          } else if (Array.isArray(this.userInfo.home_background)) {
            this.backgroundImages = this.userInfo.home_background;
          }
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/center/means.vue:1026", "解析背景图片数据失败", e);
          this.backgroundImages = [];
        }
      } else {
        this.backgroundImages = [];
      }
    },
    // 检查是否有指定类型的照片
    hasPhotoOfType(type) {
      return this.backgroundImages.some((item) => item.type === type);
    },
    // 获取指定类型的照片URL
    getPhotoByType(type) {
      const photo = this.backgroundImages.find((item) => item.type === type);
      return photo ? photo.url : "";
    },
    // 获取指定类型的照片对象
    getPhotoObject(type) {
      return this.backgroundImages.find((item) => item.type === type) || null;
    },
    // 获取指定类型的照片索引
    getPhotoIndex(type) {
      return this.backgroundImages.findIndex((item) => item.type === type);
    },
    // 根据类型添加照片
    addPhotoByType(type) {
      let that = this;
      if (!type) {
        const photoTypes = ["生活照", "旅行照", "才艺照", "回忆照", "美食宠物照"];
        const existingTypes = this.backgroundImages.map((item) => item.type);
        const availableTypes = photoTypes.filter((t) => !existingTypes.includes(t));
        if (availableTypes.length === 0) {
          this.opTipsPopup("已经添加了所有类型的照片");
          return;
        }
        common_vendor.index.showActionSheet({
          itemList: availableTypes,
          success: (res) => {
            const selectedType = availableTypes[res.tapIndex];
            that.chooseAndUploadPhoto(selectedType);
          }
        });
        return;
      }
      that.chooseAndUploadPhoto(type);
    },
    // 选择并上传照片
    chooseAndUploadPhoto(type) {
      let that = this;
      that.canvasStatus = true;
      that.$util.uploadImageChange("upload/image", (res) => {
        that.backgroundImages = that.backgroundImages.filter((item) => item.type !== type);
        that.backgroundImages.push({
          type,
          url: res.data.url
        });
        that.canvasStatus = false;
        that.opTipsPopup("上传成功");
      }, (res) => {
        that.canvasStatus = false;
        that.opTipsPopup("上传失败");
      }, (res) => {
        that.canvasWidth = res.w;
        that.canvasHeight = res.h;
      });
    },
    // 更换头像
    changeProfilePhoto() {
      let that = this;
      this.canvasStatus = true;
      that.$util.uploadImageChange("upload/image", (res) => {
        that.canvasStatus = false;
        that.userInfo.avatar = res.data.url;
        that.opTipsPopup("头像已选择，点击保存按钮生效");
      }, (res) => {
        that.canvasStatus = false;
        that.opTipsPopup("头像选择取消");
      }, (res) => {
        that.canvasWidth = res.w;
        that.canvasHeight = res.h;
      });
    },
    // 微信头像获取（小程序专用）
    onChooseAvatar(e) {
      const that = this;
      const { avatarUrl } = e.detail;
      this.$util.uploadImgs("upload/image", avatarUrl, (res) => {
        that.userInfo.avatar = res.data.url;
        that.opTipsPopup("头像已选择，点击保存按钮生效");
      }, (err) => {
        common_vendor.index.__f__("log", "at pages/center/means.vue:1140", err);
        this.opTipsPopup("头像上传失败");
      });
    },
    nameBlur() {
    },
    introBlur() {
    },
    userUpInfo(type = 0) {
      let that = this;
      that.userInfo.home_background = JSON.stringify(that.backgroundImages);
      if (api.default && api.default.api && api.default.api.editUserInfoUrl) {
        request(api.default.api.editUserInfoUrl, {
          type,
          name: that.userInfo.name,
          intro: that.userInfo.intro,
          avatar: that.userInfo.avatar,
          gender: that.userInfo.gender,
          age: that.userInfo.age,
          // 新增字段
          school: that.userInfo.school,
          hometown: that.userInfo.hometown,
          occupation: that.userInfo.occupation,
          height: that.userInfo.height,
          weight: that.userInfo.weight,
          birthday: that.userInfo.birthday,
          home_background: that.userInfo.home_background
        }, "POST").then(function(res) {
          if (res.code == 200) {
            let userInfo = that.getUserInfoFromCache();
            userInfo = { ...userInfo, ...that.userInfo };
            common_vendor.index.setStorageSync("USER_INFO", userInfo);
            that.originalUserInfo = JSON.parse(JSON.stringify(that.userInfo));
          }
          that.opTipsPopup(res.msg);
        });
      } else {
        setTimeout(() => {
          let userInfo = that.getUserInfoFromCache();
          userInfo = {
            ...userInfo,
            name: that.userInfo.name,
            intro: that.userInfo.intro,
            avatar: that.userInfo.avatar,
            gender: that.userInfo.gender,
            age: that.userInfo.age,
            // 新增字段
            school: that.userInfo.school,
            hometown: that.userInfo.hometown,
            profession: that.userInfo.profession,
            height: that.userInfo.height,
            weight: that.userInfo.weight,
            birthday: that.userInfo.birthday,
            home_background: that.userInfo.home_background
          };
          common_vendor.index.setStorageSync("USER_INFO", userInfo);
          that.originalUserInfo = JSON.parse(JSON.stringify(userInfo));
          const messages = [
            "个人信息更新成功",
            "昵称修改成功",
            "简介修改成功",
            "头像更新成功",
            "性别设置成功",
            "年龄设置成功"
          ];
          that.opTipsPopup(messages[type] || messages[0]);
        }, 300);
      }
    },
    // 保存所有修改
    saveAllChanges() {
      const that = this;
      common_vendor.index.showLoading({
        title: "保存中...",
        mask: true
      });
      const homeBackground = typeof that.backgroundImages === "string" ? that.backgroundImages : JSON.stringify(that.backgroundImages);
      const submitData = {
        // 用户基本信息
        avatar: that.userInfo.avatar,
        nickname: that.userInfo.nickname,
        birthday: that.userInfo.birthday,
        // 社交信息
        sex: that.userInfo.sex,
        about_me: that.userInfo.about_me,
        school: that.userInfo.school,
        hometown: that.userInfo.hometown,
        occupation: that.userInfo.occupation,
        height: that.userInfo.height,
        weight: that.userInfo.weight,
        home_background: homeBackground,
        constellation: that.userInfo.constellation,
        // 位置信息 - 提交residence字段
        residence_lat: that.userInfo.residence_lat || 0,
        residence_lng: that.userInfo.residence_lng || 0,
        residence_name: that.userInfo.residence_name || ""
      };
      common_vendor.index.__f__("log", "at pages/center/means.vue:1260", "提交的用户数据:", JSON.stringify(submitData));
      common_vendor.index.__f__("log", "at pages/center/means.vue:1261", "包含的位置信息:");
      common_vendor.index.__f__("log", "at pages/center/means.vue:1262", "- residence_lat:", submitData.residence_lat);
      common_vendor.index.__f__("log", "at pages/center/means.vue:1263", "- residence_lng:", submitData.residence_lng);
      common_vendor.index.__f__("log", "at pages/center/means.vue:1264", "- residence_name:", submitData.residence_name);
      api_social.updateUserSocialInfo(submitData).then((res) => {
        common_vendor.index.__f__("log", "at pages/center/means.vue:1268", "保存用户信息结果:", JSON.stringify(res));
        if (that.selectedTagIds && that.selectedTagIds.length > 0) {
          return api_social.updateUserTags(that.selectedTagIds).then((tagRes) => {
            common_vendor.index.__f__("log", "at pages/center/means.vue:1274", "标签更新结果:", JSON.stringify(tagRes));
            return { userResult: res, tagResult: tagRes };
          }).catch((tagErr) => {
            common_vendor.index.__f__("error", "at pages/center/means.vue:1277", "标签更新失败:", tagErr);
            return { userResult: res, tagError: tagErr };
          });
        } else {
          return { userResult: res };
        }
      }).then((results) => {
        var _a;
        common_vendor.index.hideLoading();
        if (results.userResult && (results.userResult.code == 200 || results.userResult.status == 200)) {
          if (results.tagResult && (results.tagResult.code != 200 && results.tagResult.status != 200)) {
            that.opTipsPopup("用户信息已保存，但标签更新失败");
          } else if (results.tagError) {
            that.opTipsPopup("用户信息已保存，但标签更新出错");
          } else {
            that.opTipsPopup("所有信息保存成功，位置信息已更新");
          }
          that.selectedTagIds = [];
          that.originalUserInfo = JSON.parse(JSON.stringify(that.userInfo));
          let cachedUserInfo = that.getUserInfoFromCache();
          cachedUserInfo = { ...cachedUserInfo, ...that.userInfo };
          common_vendor.index.setStorageSync("USER_INFO", cachedUserInfo);
          setTimeout(() => {
            that.refreshIpClick();
          }, 1e3);
        } else {
          that.opTipsPopup(((_a = results.userResult) == null ? void 0 : _a.msg) || "保存失败，请稍后重试");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/center/means.vue:1318", "保存失败:", err);
        that.opTipsPopup("保存失败: " + (typeof err === "string" ? err : "网络错误"));
      });
    },
    genderPopupClick(isOpen) {
      if (isOpen) {
        this.tempGender = this.userInfo.sex;
        this.$refs.genderPopup.open();
      } else {
        this.$refs.genderPopup.close();
      }
    },
    // 点击年龄项，临时选择
    ageItemClick(age) {
      this.tempAge = age;
    },
    // 确认选择年龄
    confirmAge() {
      if (this.tempAge) {
        this.userInfo.age = this.tempAge;
        this.tempAge = null;
      }
      this.$refs.agePopup.close();
    },
    // 年龄选择弹窗
    agePopupClick(isOpen) {
      const that = this;
      if (isOpen) {
        common_vendor.index.showLoading({
          title: "加载兴趣标签中...",
          mask: true
        });
        that.tagCategories = [];
        api_social.getTagsWithCategories().then(function(res) {
          common_vendor.index.__f__("log", "at pages/center/means.vue:1361", "兴趣标签接口原始响应:", JSON.stringify(res));
          const isSuccess = res.code == 200 || res.status == 200 || res.msg === "success";
          if (isSuccess && res.data) {
            if (Array.isArray(res.data) && res.data.length === 0) {
              common_vendor.index.hideLoading();
              that.opTipsPopup("暂无标签数据，请联系管理员添加标签");
              return;
            }
            that.processTagsData(res).then(() => {
              that.initializeSelectedTags();
              common_vendor.index.hideLoading();
              that.$refs.agePopup.open();
            }).catch((err) => {
              common_vendor.index.__f__("error", "at pages/center/means.vue:1383", "处理标签数据失败:", err);
              common_vendor.index.hideLoading();
              that.opTipsPopup("标签数据处理失败: " + (err.message || "未知错误"));
            });
          } else {
            common_vendor.index.__f__("error", "at pages/center/means.vue:1388", "加载标签失败:", res.msg || "未知错误");
            common_vendor.index.hideLoading();
            that.opTipsPopup("标签加载失败: " + (res.msg || "未知错误"));
          }
        }).catch((err) => {
          common_vendor.index.__f__("error", "at pages/center/means.vue:1393", "加载兴趣标签错误:", err);
          common_vendor.index.hideLoading();
          that.opTipsPopup("网络错误，请稍后重试");
        });
      } else {
        that.$refs.agePopup.close();
      }
    },
    // 处理标签数据的新方法，可以处理任何格式的响应
    processTagsData(response) {
      const that = this;
      return new Promise((resolve, reject) => {
        try {
          common_vendor.index.__f__("log", "at pages/center/means.vue:1407", "处理标签数据开始:", JSON.stringify(response));
          if (!response || typeof response !== "object") {
            common_vendor.index.__f__("error", "at pages/center/means.vue:1411", "响应数据不是对象");
            reject(new Error("响应数据不是对象"));
            return;
          }
          let data = response.data;
          if (!data || !Array.isArray(data)) {
            common_vendor.index.__f__("error", "at pages/center/means.vue:1421", "响应数据格式不正确，data不是数组");
            reject(new Error("响应数据格式不正确，data不是数组"));
            return;
          }
          if (data.length === 0) {
            common_vendor.index.__f__("error", "at pages/center/means.vue:1428", "API返回的标签数据为空数组");
            reject(new Error("API返回的标签数据为空数组"));
            return;
          }
          that.tagNameToIdMap = {};
          that.tagCategories = data.map((category) => {
            if (!category || typeof category !== "object") {
              common_vendor.index.__f__("error", "at pages/center/means.vue:1439", "分类数据无效:", category);
              return {
                name: "未知分类",
                id: 0,
                tags: [],
                tagsData: []
              };
            }
            const categoryTags = Array.isArray(category.tags) ? category.tags : [];
            common_vendor.index.__f__("log", "at pages/center/means.vue:1450", `分类[${category.name}]的原始标签数据:`, JSON.stringify(categoryTags));
            const tagNames = categoryTags.map((tag) => {
              if (tag && typeof tag === "object" && tag.name) {
                if (tag.id) {
                  that.tagNameToIdMap[tag.name] = tag.id;
                  common_vendor.index.__f__("log", "at pages/center/means.vue:1458", `添加API返回的标签映射: ${tag.name} => ${tag.id}`);
                }
                return tag.name;
              }
              common_vendor.index.__f__("warn", "at pages/center/means.vue:1462", "发现无效标签:", tag);
              return null;
            }).filter((name) => name !== null);
            common_vendor.index.__f__("log", "at pages/center/means.vue:1466", `分类[${category.name}]的标签名称:`, tagNames.join(", ") || "无标签");
            return {
              name: category.name || "未命名分类",
              id: category.id || 0,
              tags: tagNames,
              // 标签名称数组用于UI显示
              tagsData: categoryTags
              // 保存原始标签数据包含ID
            };
          });
          common_vendor.index.__f__("log", "at pages/center/means.vue:1476", "API返回的标签分类初步处理结果:", JSON.stringify(that.tagCategories));
          const allCategories = [...that.tagCategories];
          that.tagCategories = that.tagCategories.filter(
            (category) => category.tags && category.tags.length > 0
          );
          common_vendor.index.__f__("log", "at pages/center/means.vue:1484", "过滤后的标签分类结构:", JSON.stringify(that.tagCategories));
          common_vendor.index.__f__("log", "at pages/center/means.vue:1485", "API返回的标签ID映射:", that.tagNameToIdMap);
          if (that.tagCategories.length === 0) {
            common_vendor.index.__f__("warn", "at pages/center/means.vue:1488", "所有分类都没有标签，尝试使用原始分类");
            if (allCategories.length > 0) {
              that.tagCategories = allCategories;
              common_vendor.index.__f__("log", "at pages/center/means.vue:1492", "使用完整分类(含空标签分类):", JSON.stringify(that.tagCategories));
            } else {
              common_vendor.index.__f__("warn", "at pages/center/means.vue:1494", "API返回的原始分类也为空");
              reject(new Error("API返回的分类数据无效"));
              return;
            }
          }
          that.loadMyTags();
          common_vendor.index.__f__("log", "at pages/center/means.vue:1502", "API标签数据处理完成");
          resolve();
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/center/means.vue:1505", "处理API标签数据时出错:", error);
          reject(error);
        }
      });
    },
    // 初始化已选标签
    initializeSelectedTags() {
      if (this.userInfo.age) {
        this.selectedTags = this.userInfo.age.split(", ").filter((tag) => tag);
        common_vendor.index.__f__("log", "at pages/center/means.vue:1516", "从用户信息初始化已选标签:", this.selectedTags);
      } else {
        this.selectedTags = [];
      }
    },
    // 加载标签分类
    loadTagCategories() {
      const that = this;
      common_vendor.index.__f__("log", "at pages/center/means.vue:1525", "开始加载兴趣标签分类和列表");
      return new Promise((resolve, reject) => {
        api_social.getTagsWithCategories().then(function(res) {
          common_vendor.index.__f__("log", "at pages/center/means.vue:1531", "兴趣标签接口原始响应:", JSON.stringify(res));
          const isSuccess = res.code == 200 || res.status == 200 || res.msg === "success";
          if (isSuccess) {
            if (!res.data) {
              common_vendor.index.__f__("log", "at pages/center/means.vue:1539", "API响应成功但数据为空");
              that.opTipsPopup("标签数据为空");
              resolve([]);
              return;
            }
            if (Array.isArray(res.data) && res.data.length === 0) {
              common_vendor.index.__f__("log", "at pages/center/means.vue:1547", "API返回的标签数据为空数组");
              that.opTipsPopup("暂无标签数据");
              resolve([]);
              return;
            }
            that.processTagsData(res).then(resolve).catch((error) => {
              common_vendor.index.__f__("error", "at pages/center/means.vue:1557", "处理标签数据出错:", error);
              that.opTipsPopup("标签数据格式错误");
              resolve([]);
            });
          } else {
            common_vendor.index.__f__("error", "at pages/center/means.vue:1562", "加载标签分类和列表失败:", res.msg || "未知错误");
            that.opTipsPopup("标签数据加载失败: " + (res.msg || "未知错误"));
            reject(new Error(res.msg || "加载标签失败"));
          }
        }).catch((err) => {
          common_vendor.index.__f__("error", "at pages/center/means.vue:1567", "加载兴趣标签分类和列表错误:", err);
          that.opTipsPopup("网络错误，请稍后重试");
          reject(err);
        });
      });
    },
    // 加载标签列表
    loadTagsList() {
      const that = this;
      common_vendor.index.__f__("log", "at pages/center/means.vue:1577", "开始加载兴趣标签列表");
      that.tagNameToIdMap = {};
      api_social.getTagsWithCategories().then(function(res) {
        if (res.code == 200 && res.data) {
          that.processTagsData(res).then(() => {
            common_vendor.index.hideLoading();
            common_vendor.index.__f__("log", "at pages/center/means.vue:1588", "标签列表加载完成");
          }).catch((err) => {
            common_vendor.index.__f__("error", "at pages/center/means.vue:1591", "标签数据处理错误:", err);
            common_vendor.index.hideLoading();
            that.opTipsPopup("标签数据处理错误");
          });
        } else {
          common_vendor.index.__f__("error", "at pages/center/means.vue:1596", "获取标签列表失败:", res.msg);
          common_vendor.index.hideLoading();
          that.opTipsPopup("获取标签失败: " + (res.msg || "未知错误"));
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/center/means.vue:1601", "获取标签列表请求错误:", err);
        common_vendor.index.hideLoading();
        that.opTipsPopup("网络错误，请稍后重试");
      });
    },
    // 加载我的标签
    async loadMyTags() {
      try {
        const res = await api_social.getMyTags();
        common_vendor.index.__f__("log", "at pages/center/means.vue:1611", "获取我的标签返回数据:", JSON.stringify(res));
        if (res.status === 200 && res.data) {
          this.myTags = res.data;
          this.selectedTags = res.data.map((tag) => tag.name);
          this.selectedNewTags = [...this.selectedTags];
          this.userInfo.interest_tags = this.selectedTags;
          return res.data;
        }
        return [];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:1627", "加载我的标签失败:", error);
        return [];
      }
    },
    // 根据星座值获取星座名称
    getConstellationName(value) {
      const constellations = [
        "水瓶座",
        "双鱼座",
        "白羊座",
        "金牛座",
        "双子座",
        "巨蟹座",
        "狮子座",
        "处女座",
        "天秤座",
        "天蝎座",
        "射手座",
        "摩羯座"
      ];
      if (value !== null && value >= 0 && value < constellations.length) {
        return constellations[value];
      }
      return "未知";
    },
    // 表单提交
    formSubmit(e) {
      let that = this;
      let value = e.detail.value;
      if (!value.nickname) {
        return that.opTipsPopup("请输入昵称");
      }
      that.userInfo.nickname = value.nickname;
      that.saveAllChanges();
    },
    /**
     * 创建默认的标签数据并返回，但不设置到tagCategories
     * @param {Boolean} returnOnly - 是否仅返回数据而不设置到全局
     * @return {Array} 默认标签数据数组
     */
    // 已删除createDefaultTagData方法
    /**
     * 创建默认的标签数据
     * 当接口加载失败时使用此方法
     */
    // 已删除createDefaultTagsData方法
    // 生日相关方法
    birthdayPopupClick(isOpen) {
      const that = this;
      if (isOpen) {
        that.initBirthdayPicker();
        that.$refs.birthdayPopup.open();
      } else {
        that.$refs.birthdayPopup.close();
      }
    },
    // 初始化生日选择器
    initBirthdayPicker() {
      if (this.userInfo.birthday) {
        try {
          let year, month, day;
          if (typeof this.userInfo.birthday === "number" || typeof this.userInfo.birthday === "string" && !isNaN(this.userInfo.birthday) && !this.userInfo.birthday.includes("-")) {
            const timestamp = parseInt(this.userInfo.birthday);
            const date = new Date(timestamp * 1e3);
            year = date.getFullYear();
            month = date.getMonth() + 1;
            day = date.getDate();
          } else {
            const parts = this.userInfo.birthday.split("-");
            if (parts.length !== 3)
              return;
            year = parseInt(parts[0]);
            month = parseInt(parts[1]);
            day = parseInt(parts[2]);
          }
          const yearIndex = this.years.findIndex((y) => y === year);
          const monthIndex = this.months.findIndex((m) => m === month);
          const dayIndex = this.days.findIndex((d) => d === day);
          if (yearIndex !== -1 && monthIndex !== -1 && dayIndex !== -1) {
            this.birthdayPickerValue = [yearIndex, monthIndex, dayIndex];
            common_vendor.index.__f__("log", "at pages/center/means.vue:1722", "初始化生日选择器:", this.birthdayPickerValue, `(${year}-${month}-${day})`);
          }
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/center/means.vue:1725", "初始化生日选择器出错:", e);
        }
      }
    },
    birthdayPickerChange(e) {
      this.birthdayPickerValue = e.detail.value;
    },
    // 从生日字符串计算星座
    calculateConstellationFromBirthday() {
      if (!this.userInfo.birthday)
        return;
      try {
        let month, day;
        if (typeof this.userInfo.birthday === "number" || typeof this.userInfo.birthday === "string" && !isNaN(this.userInfo.birthday) && !this.userInfo.birthday.includes("-")) {
          const timestamp = parseInt(this.userInfo.birthday);
          const date = new Date(timestamp * 1e3);
          month = date.getMonth() + 1;
          day = date.getDate();
          common_vendor.index.__f__("log", "at pages/center/means.vue:1751", `从时间戳 ${this.userInfo.birthday} 解析出生日: ${month}月${day}日`);
        } else {
          const parts = this.userInfo.birthday.split("-");
          if (parts.length !== 3)
            return;
          month = parseInt(parts[1], 10);
          day = parseInt(parts[2], 10);
        }
        const constellation = this.calculateConstellation(month, day);
        this.userInfo.constellation = constellation.value;
        common_vendor.index.__f__("log", "at pages/center/means.vue:1765", "从生日计算星座:", constellation.name, "(值:", constellation.value, ")");
        if (this.originalUserInfo.constellation === null || this.originalUserInfo.constellation !== constellation.value) {
          common_vendor.index.__f__("log", "at pages/center/means.vue:1771", "更新星座信息到服务器");
          const submitData = {
            constellation: constellation.value
          };
          api_social.updateUserSocialInfo(submitData).then((res) => {
            common_vendor.index.__f__("log", "at pages/center/means.vue:1780", "星座更新结果:", JSON.stringify(res));
          }).catch((err) => {
            common_vendor.index.__f__("error", "at pages/center/means.vue:1782", "星座更新错误:", err);
          });
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:1786", "计算星座时出错:", e);
      }
    },
    // 计算星座
    calculateConstellation(month, day) {
      const constellationDates = [
        { name: "水瓶座", start: { month: 1, day: 20 }, end: { month: 2, day: 18 } },
        { name: "双鱼座", start: { month: 2, day: 19 }, end: { month: 3, day: 20 } },
        { name: "白羊座", start: { month: 3, day: 21 }, end: { month: 4, day: 19 } },
        { name: "金牛座", start: { month: 4, day: 20 }, end: { month: 5, day: 20 } },
        { name: "双子座", start: { month: 5, day: 21 }, end: { month: 6, day: 21 } },
        { name: "巨蟹座", start: { month: 6, day: 22 }, end: { month: 7, day: 22 } },
        { name: "狮子座", start: { month: 7, day: 23 }, end: { month: 8, day: 22 } },
        { name: "处女座", start: { month: 8, day: 23 }, end: { month: 9, day: 22 } },
        { name: "天秤座", start: { month: 9, day: 23 }, end: { month: 10, day: 23 } },
        { name: "天蝎座", start: { month: 10, day: 24 }, end: { month: 11, day: 22 } },
        { name: "射手座", start: { month: 11, day: 23 }, end: { month: 12, day: 21 } },
        { name: "摩羯座", start: { month: 12, day: 22 }, end: { month: 1, day: 19 } }
      ];
      for (let i = 0; i < constellationDates.length; i++) {
        const constellation = constellationDates[i];
        if (i === 11) {
          if (month === 12 && day >= constellation.start.day || month === 1 && day <= constellation.end.day) {
            return { name: constellation.name, value: i };
          }
        } else if (month === constellation.start.month && day >= constellation.start.day || month === constellation.end.month && day <= constellation.end.day) {
          return { name: constellation.name, value: i };
        }
      }
      return { name: "水瓶座", value: 0 };
    },
    // 确认生日选择
    confirmBirthday() {
      const that = this;
      const year = this.years[this.birthdayPickerValue[0]];
      const month = this.months[this.birthdayPickerValue[1]];
      const day = this.days[this.birthdayPickerValue[2]];
      const birthdayDate = new Date(year, month - 1, day);
      const birthdayTimestamp = Math.floor(birthdayDate.getTime() / 1e3);
      `${year}-${month.toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}`;
      const constellation = this.calculateConstellation(month, day);
      that.userInfo.birthday = birthdayTimestamp;
      that.userInfo.constellation = constellation.value;
      this.$refs.birthdayPopup.close();
    },
    // 绑定手机号
    bindMobileClick(e) {
      let that = this;
      if (e.detail.errMsg == "getPhoneNumber:ok") {
        common_vendor.index.showLoading({
          title: that.userInfo.phone ? "换绑授权中..." : "绑定授权中...",
          mask: true
        });
        that.$util.userBindingPhone(e.detail).then((res) => {
          common_vendor.index.hideLoading();
          if (res.code == 200) {
            that.userInfo.phone = res.data.phone || res.data.mobile;
            that.opTipsPopup(res.msg || "手机号绑定成功");
          } else {
            that.opTipsPopup(res.msg || "绑定失败");
          }
        }).catch((err) => {
          common_vendor.index.hideLoading();
          that.opTipsPopup("绑定失败: " + (typeof err === "string" ? err : "网络错误"));
        });
      }
    },
    // 刷新IP属地
    refreshIpClick() {
      let that = this;
      common_vendor.index.showLoading({
        title: "刷新中...",
        mask: true
      });
      const cachedLatitude = common_vendor.index.getStorageSync("residence_lat");
      const cachedLongitude = common_vendor.index.getStorageSync("residence_lng");
      if (cachedLatitude && cachedLongitude) {
        that.getAddressFromLocation(parseFloat(cachedLatitude), parseFloat(cachedLongitude));
        common_vendor.index.hideLoading();
        return;
      }
      that.getUserLocation();
      api_social.getUserSocialInfo().then(function(res) {
        common_vendor.index.hideLoading();
        if (res.code == 200) {
          const userData = res.data;
          common_vendor.index.setStorageSync("USER_INFO", userData);
          if (userData.residence_name) {
            that.userInfo.residence_name = userData.residence_name;
            that.userInfo.province = userData.residence_name;
          }
          if (userData.province && !userData.residence_name) {
            that.userInfo.province = userData.province;
          }
          that.originalUserInfo = JSON.parse(JSON.stringify(userData));
          that.opTipsPopup("IP属地刷新成功");
        } else {
          that.opTipsPopup(res.msg || "IP属地刷新失败");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        that.opTipsPopup("刷新失败: " + (typeof err === "string" ? err : "网络错误"));
      });
    },
    // 获取实名认证状态文本
    getAuthStatusText() {
      switch (this.userInfo.auth_status) {
        case 0:
          return "未认证";
        case 1:
          return "审核中";
        case 2:
          return "已认证";
        case 3:
          return "认证失败";
        default:
          return "未认证";
      }
    },
    // 跳转到实名认证页面
    goToRealAuth() {
      common_vendor.index.navigateTo({
        url: "/pages/setting/realname"
      });
    },
    // 显示提示弹窗
    opTipsPopup(msg, duration = 2e3) {
      let that = this;
      that.tipsTitle = msg;
      that.$refs.tipsPopup.open();
      setTimeout(function() {
        that.$refs.tipsPopup.close();
      }, duration);
    },
    // 点击性别项，临时选择
    genderItemClick(gender) {
      this.tempGender = gender;
    },
    // 确认选择性别
    confirmGender() {
      if (this.tempGender !== null) {
        this.userInfo.sex = this.tempGender;
        this.opTipsPopup("性别选择已更新，点击保存按钮生效");
      }
      this.$refs.genderPopup.close();
    },
    // 切换标签
    toggleTag(tag) {
      common_vendor.index.__f__("log", "at pages/center/means.vue:1980", "切换标签:", tag);
      common_vendor.index.__f__("log", "at pages/center/means.vue:1981", "当前已选择标签:", JSON.stringify(this.selectedTags));
      if (!tag) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:1984", "无效的标签名称");
        this.opTipsPopup("标签无效");
        return;
      }
      const tagId = this.getTagIdByName(tag);
      common_vendor.index.__f__("log", "at pages/center/means.vue:1991", "标签ID:", tagId);
      if (this.selectedTags.includes(tag)) {
        this.selectedTags = this.selectedTags.filter((t) => t !== tag);
        common_vendor.index.__f__("log", "at pages/center/means.vue:1995", "取消选择后的标签:", JSON.stringify(this.selectedTags));
        this.opTipsPopup(`已取消选择 ${tag}`);
      } else if (this.selectedTags.length < 5) {
        this.selectedTags.push(tag);
        common_vendor.index.__f__("log", "at pages/center/means.vue:1999", "选择后的标签:", JSON.stringify(this.selectedTags));
        this.opTipsPopup(`已选择 ${tag}`);
      } else {
        this.opTipsPopup("最多只能选择5个兴趣标签");
      }
    },
    // 切换标签分类
    switchCategory(index) {
      this.currentCategoryIndex = index;
    },
    // 滑动切换分类
    swiperChange(e) {
      this.currentCategoryIndex = e.detail.current;
    },
    // 确认选择标签
    confirmTags() {
      const that = this;
      if (that.selectedTags.length > 5) {
        that.opTipsPopup("最多只能选择5个兴趣标签");
        return;
      }
      that.userInfo.age = that.selectedTags.join(", ");
      const tagIds = [];
      const tagNames = [];
      for (const tagName of that.selectedTags) {
        const tagId = that.getTagIdByName(tagName);
        if (tagId) {
          tagIds.push(tagId);
          tagNames.push(tagName);
          common_vendor.index.__f__("log", "at pages/center/means.vue:2037", `成功添加标签 "${tagName}" 的ID: ${tagId}`);
        } else {
          common_vendor.index.__f__("warn", "at pages/center/means.vue:2039", `无法找到标签 "${tagName}" 的ID`);
        }
      }
      common_vendor.index.__f__("log", "at pages/center/means.vue:2043", "选中的标签:", tagNames);
      common_vendor.index.__f__("log", "at pages/center/means.vue:2044", "标签ID列表:", tagIds);
      if (tagIds.length === 0 && that.selectedTags.length > 0) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:2047", "无法获取任何标签ID，可能是数据结构问题");
        that.opTipsPopup("标签数据异常，请稍后重试");
        return;
      }
      common_vendor.index.showLoading({
        title: "保存兴趣标签中...",
        mask: true
      });
      api_social.updateUserTags(tagIds).then(function(res) {
        common_vendor.index.hideLoading();
        if (res.code == 200) {
          that.opTipsPopup("兴趣标签更新成功", 3e3);
          api_social.getUserSocialInfo().then(function(result) {
            if (result.code == 200) {
              common_vendor.index.__f__("log", "at pages/center/means.vue:2067", "刷新用户信息成功:", result.data);
              common_vendor.index.setStorageSync("USER_INFO", result.data);
              that.userInfo = result.data;
              that.originalUserInfo = JSON.parse(JSON.stringify(result.data));
              that.handleBackgroundImages();
            }
          });
        } else {
          common_vendor.index.__f__("error", "at pages/center/means.vue:2075", "标签更新失败:", res);
          that.opTipsPopup("标签更新失败: " + (res.msg || ""));
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/center/means.vue:2080", "标签更新错误:", err);
        that.opTipsPopup("标签更新失败: 网络错误");
      });
      that.$refs.agePopup.close();
    },
    // 根据标签名获取标签ID
    getTagIdByName(tagName) {
      if (!tagName) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:2090", "标签名为空");
        return null;
      }
      common_vendor.index.__f__("log", "at pages/center/means.vue:2094", "查找标签ID，标签名:", tagName);
      if (this.tagNameToIdMap && this.tagNameToIdMap[tagName]) {
        common_vendor.index.__f__("log", "at pages/center/means.vue:2098", "从映射中找到ID:", this.tagNameToIdMap[tagName]);
        return this.tagNameToIdMap[tagName];
      }
      for (const category of this.tagCategories) {
        if (category.tagsData && Array.isArray(category.tagsData)) {
          for (const tag of category.tagsData) {
            if (tag && tag.name === tagName) {
              this.tagNameToIdMap[tagName] = tag.id;
              common_vendor.index.__f__("log", "at pages/center/means.vue:2109", "在分类中找到ID:", tag.id);
              return tag.id;
            }
          }
        }
      }
      common_vendor.index.__f__("warn", "at pages/center/means.vue:2117", `未找到标签 "${tagName}" 的ID`);
      return null;
    },
    previewImage(url) {
      if (!url)
        return;
      common_vendor.index.previewImage({
        urls: [url],
        current: url
      });
    },
    // 显示照片长按菜单
    showPhotoMenu(photo, index) {
      if (!photo)
        return;
      const isAvatar = index === -1 || photo.type === "头像";
      const menuItems = isAvatar ? ["查看大图", "更换头像"] : ["查看大图", "删除", "设为头像"];
      common_vendor.index.showActionSheet({
        itemList: menuItems,
        success: (res) => {
          if (isAvatar) {
            switch (res.tapIndex) {
              case 0:
                this.previewImage(photo.url);
                break;
              case 1:
                this.changeProfilePhoto();
                break;
            }
          } else {
            switch (res.tapIndex) {
              case 0:
                this.previewImage(photo.url);
                break;
              case 1:
                common_vendor.index.showModal({
                  title: "确认删除",
                  content: "确定要删除这张照片吗？",
                  success: (res2) => {
                    if (res2.confirm) {
                      this.backgroundImages.splice(index, 1);
                      this.opTipsPopup("照片已删除");
                    }
                  }
                });
                break;
              case 2:
                common_vendor.index.showModal({
                  title: "设为头像",
                  content: "确定要将此照片设为头像吗？",
                  success: (res2) => {
                    if (res2.confirm) {
                      const oldAvatar = this.userInfo.avatar;
                      this.userInfo.avatar = photo.url;
                      if (oldAvatar && !oldAvatar.includes("/static/img/avatar.png")) {
                        this.backgroundImages[index].url = oldAvatar;
                      }
                      this.opTipsPopup("已设为头像，点击保存按钮生效");
                    }
                  }
                });
                break;
            }
          }
        }
      });
    },
    // 拖动相关变量
    dragStart(e) {
      this.dragStartIndex = parseInt(e.currentTarget.dataset.index);
      this.isDragging = true;
      this.startX = e.touches[0].clientX;
      this.startY = e.touches[0].clientY;
    },
    dragMove(e) {
      if (!this.isDragging)
        return;
      const moveX = e.touches[0].clientX - this.startX;
      const moveY = e.touches[0].clientY - this.startY;
      if (Math.abs(moveX) < 10 && Math.abs(moveY) < 10)
        return;
      this.isDragging = true;
      const touch = e.touches[0];
      const element = document.elementFromPoint(touch.clientX, touch.clientY);
      if (element) {
        let photoItem = element.closest(".photo-item");
        if (photoItem && photoItem.dataset && photoItem.dataset.index !== void 0) {
          const targetIndex = parseInt(photoItem.dataset.index);
          if (targetIndex !== this.dragStartIndex && targetIndex >= 0 && targetIndex < this.backgroundImages.length) {
            const temp = this.backgroundImages[this.dragStartIndex];
            this.backgroundImages.splice(this.dragStartIndex, 1);
            this.backgroundImages.splice(targetIndex, 0, temp);
            this.dragStartIndex = targetIndex;
          }
        }
      }
    },
    dragEnd(e) {
      this.isDragging = false;
    },
    newTagsPopupClick(isOpen) {
      if (isOpen) {
        common_vendor.index.showLoading({
          title: "加载标签中...",
          mask: true
        });
        api_social.getTagsWithCategories().then((res) => {
          common_vendor.index.__f__("log", "at pages/center/means.vue:2264", "获取标签数据响应:", JSON.stringify(res));
          const isSuccess = res.code == 200 || res.status == 200 || res.msg === "success";
          if (isSuccess && res.data) {
            if (Array.isArray(res.data) && res.data.length === 0) {
              common_vendor.index.hideLoading();
              this.opTipsPopup("暂无标签数据，请联系管理员添加标签");
              return;
            }
            common_vendor.index.__f__("log", "at pages/center/means.vue:2277", "处理从API获取的标签数据");
            try {
              if (Array.isArray(res.data)) {
                this.processNewTagsData(res.data);
              } else if (typeof res.data === "object") {
                const dataArray = res.data.list || res.data.data || Object.values(res.data);
                if (Array.isArray(dataArray) && dataArray.length > 0) {
                  this.processNewTagsData(dataArray);
                } else {
                  common_vendor.index.__f__("error", "at pages/center/means.vue:2287", "无法从对象中提取标签数组");
                  common_vendor.index.hideLoading();
                  this.opTipsPopup("标签数据格式错误");
                  return;
                }
              } else {
                common_vendor.index.__f__("error", "at pages/center/means.vue:2293", "未知的数据格式:", typeof res.data);
                common_vendor.index.hideLoading();
                this.opTipsPopup("标签数据格式不支持");
                return;
              }
            } catch (err) {
              common_vendor.index.__f__("error", "at pages/center/means.vue:2299", "处理标签数据时出错:", err);
              common_vendor.index.hideLoading();
              this.opTipsPopup("标签数据处理失败");
              return;
            }
            this.loadMyTagsOnly().catch((err) => {
              common_vendor.index.__f__("error", "at pages/center/means.vue:2308", "加载我的标签失败，但继续使用已加载的标签:", err);
            }).finally(() => {
              this.initializeNewSelectedTags();
              common_vendor.index.hideLoading();
              this.$refs.newTagsPopup.open();
            });
          } else {
            common_vendor.index.__f__("error", "at pages/center/means.vue:2316", "获取标签数据失败，状态码:", res.code || res.status);
            common_vendor.index.hideLoading();
            this.opTipsPopup("标签数据获取失败: " + (res.msg || "未知错误"));
          }
        }).catch((err) => {
          common_vendor.index.__f__("error", "at pages/center/means.vue:2321", "获取标签数据请求错误:", err);
          common_vendor.index.hideLoading();
          this.opTipsPopup("网络错误，请稍后重试");
        });
      } else {
        this.$refs.newTagsPopup.close();
      }
    },
    // 处理新标签数据
    processNewTagsData(data) {
      common_vendor.index.__f__("log", "at pages/center/means.vue:2332", "开始处理标签数据:", typeof data === "object" ? Array.isArray(data) ? "数组格式" : "对象格式" : typeof data);
      if (!Array.isArray(data)) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:2335", `标签数据不是数组格式`);
        if (data && typeof data === "object") {
          data = data.list || data.data || Object.values(data);
          if (!Array.isArray(data)) {
            common_vendor.index.__f__("error", "at pages/center/means.vue:2341", `无法将数据转换为数组`);
            return;
          }
        } else {
          common_vendor.index.__f__("error", "at pages/center/means.vue:2345", `数据格式无效，无法处理`);
          return;
        }
      }
      common_vendor.index.__f__("log", "at pages/center/means.vue:2350", `处理API数据数组，长度:`, data.length);
      this.newTagCategories = [];
      this.newTagCategories = data.map((category) => {
        if (category && category.name && category.id && !category.tags) {
          return {
            id: 0,
            name: "标签",
            tags: [category.name],
            originalTags: [category]
          };
        }
        let tags = [];
        if (Array.isArray(category.tags)) {
          tags = category.tags;
        } else if (category.tag_list && Array.isArray(category.tag_list)) {
          tags = category.tag_list;
        } else if (category.tagList && Array.isArray(category.tagList)) {
          tags = category.tagList;
        }
        common_vendor.index.__f__("log", "at pages/center/means.vue:2377", `API分类[${category.name}]的标签数量:`, tags.length);
        const tagNames = tags.map((tag) => {
          if (typeof tag === "string") {
            return tag;
          } else if (tag && tag.name) {
            return tag.name;
          }
          return null;
        }).filter((name) => name !== null);
        return {
          id: category.id || 0,
          name: category.name || "未命名分类",
          tags: tagNames,
          originalTags: tags
        };
      });
      this.newTagCategories = this.newTagCategories.filter(
        (category) => category.tags && category.tags.length > 0
      );
      common_vendor.index.__f__("log", "at pages/center/means.vue:2402", `处理后的API标签分类数量:`, this.newTagCategories.length);
      if (this.newTagCategories.length === 0) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:2404", `没有有效的API标签分类`);
        return;
      }
      this.initializeNewSelectedTags();
      common_vendor.index.__f__("log", "at pages/center/means.vue:2411", `API标签处理完成`);
    },
    // 初始化已选标签
    initializeNewSelectedTags() {
      const that = this;
      common_vendor.index.__f__("log", "at pages/center/means.vue:2418", "初始化已选标签开始...");
      common_vendor.index.__f__("log", "at pages/center/means.vue:2419", "现有标签ID:", that.selectedTagIds);
      common_vendor.index.__f__("log", "at pages/center/means.vue:2420", "现有已选标签名称:", that.selectedNewTags);
      if (that.selectedTagIds && that.selectedTagIds.length > 0) {
        common_vendor.index.__f__("log", "at pages/center/means.vue:2424", "根据标签ID初始化选中状态:", that.selectedTagIds);
        that.selectedNewTags = [];
        that.newTagCategories.forEach((category) => {
          if (category.originalTags && Array.isArray(category.originalTags)) {
            category.originalTags.forEach((tag) => {
              const tagId = tag.id || tag.tag_id;
              if (tagId && that.selectedTagIds.includes(tagId)) {
                if (tag.name) {
                  that.selectedNewTags.push(tag.name);
                  common_vendor.index.__f__("log", "at pages/center/means.vue:2438", `找到标签: ${tag.name}, ID: ${tagId}`);
                }
              }
            });
          }
        });
        common_vendor.index.__f__("log", "at pages/center/means.vue:2445", "根据ID找到的标签名称:", that.selectedNewTags);
        if (that.selectedNewTags.length === 0 && that.userInfo.age) {
          common_vendor.index.__f__("log", "at pages/center/means.vue:2449", "通过ID没有找到标签，尝试从userInfo.age获取");
          that.selectedNewTags = that.userInfo.age.split(", ").filter((tag) => tag);
          common_vendor.index.__f__("log", "at pages/center/means.vue:2451", "从userInfo.age获取的标签:", that.selectedNewTags);
        }
        if (that.selectedNewTags.length > 0) {
          that.userInfo.age = that.selectedNewTags.join(", ");
        }
      } else if (that.userInfo.age) {
        common_vendor.index.__f__("log", "at pages/center/means.vue:2461", "从userInfo.age初始化标签");
        that.selectedNewTags = that.userInfo.age.split(", ").filter((tag) => tag);
        common_vendor.index.__f__("log", "at pages/center/means.vue:2463", "从用户信息字符串初始化标签:", that.selectedNewTags);
        that.selectedTagIds = [];
        that.selectedNewTags.forEach((tagName) => {
          const tagId = that.getNewTagIdByName(tagName);
          if (tagId) {
            that.selectedTagIds.push(tagId);
            common_vendor.index.__f__("log", "at pages/center/means.vue:2471", `找到标签"${tagName}"的ID: ${tagId}`);
          } else {
            common_vendor.index.__f__("log", "at pages/center/means.vue:2473", `未找到标签"${tagName}"的ID`);
          }
        });
        common_vendor.index.__f__("log", "at pages/center/means.vue:2477", "根据名称找到的标签ID:", that.selectedTagIds);
      } else {
        common_vendor.index.__f__("log", "at pages/center/means.vue:2480", "没有已选标签信息");
        that.selectedNewTags = [];
        that.selectedTagIds = [];
      }
      common_vendor.index.__f__("log", "at pages/center/means.vue:2485", "初始化已选标签完成, 标签数量:", that.selectedNewTags.length);
    },
    switchNewCategory(index) {
      this.newCategoryIndex = index;
    },
    toggleNewTag(tag) {
      common_vendor.index.__f__("log", "at pages/center/means.vue:2493", "切换标签:", tag);
      common_vendor.index.__f__("log", "at pages/center/means.vue:2494", "当前已选择标签:", JSON.stringify(this.selectedNewTags));
      if (!tag) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:2497", "无效的标签名称");
        this.opTipsPopup("标签无效");
        return;
      }
      const tagId = this.getNewTagIdByName(tag);
      if (tagId) {
        common_vendor.index.__f__("log", "at pages/center/means.vue:2506", "找到标签ID:", tagId);
        if (this.selectedNewTags.includes(tag)) {
          this.selectedNewTags = this.selectedNewTags.filter((t) => t !== tag);
          this.selectedTagIds = this.selectedTagIds.filter((id) => id !== tagId);
          common_vendor.index.__f__("log", "at pages/center/means.vue:2515", "取消选择后的标签:", JSON.stringify(this.selectedNewTags));
          common_vendor.index.__f__("log", "at pages/center/means.vue:2516", "取消选择后的标签ID:", JSON.stringify(this.selectedTagIds));
          this.opTipsPopup(`已取消选择 ${tag}`);
        } else if (this.selectedNewTags.length < 5) {
          this.selectedNewTags.push(tag);
          this.selectedTagIds.push(tagId);
          common_vendor.index.__f__("log", "at pages/center/means.vue:2525", "选择后的标签:", JSON.stringify(this.selectedNewTags));
          common_vendor.index.__f__("log", "at pages/center/means.vue:2526", "选择后的标签ID:", JSON.stringify(this.selectedTagIds));
          this.opTipsPopup(`已选择 ${tag}`);
        } else {
          this.opTipsPopup("最多只能选择5个兴趣标签");
        }
      } else {
        common_vendor.index.__f__("error", "at pages/center/means.vue:2532", "标签在系统中不存在:", tag);
        this.opTipsPopup("该标签不存在或已被删除");
      }
    },
    confirmNewTags() {
      const that = this;
      if (that.selectedNewTags.length > 5) {
        that.opTipsPopup("最多只能选择5个兴趣标签");
        return;
      }
      common_vendor.index.showLoading({
        title: "保存中...",
        mask: true
      });
      const tagIds = that.selectedNewTags.map((tagName) => {
        for (const category of that.newTagCategories) {
          if (category.originalTags) {
            const tagObj = category.originalTags.find((t) => t.name === tagName);
            if (tagObj)
              return tagObj.id;
          }
        }
        return null;
      }).filter((id) => id !== null);
      api_social.updateUserTags(tagIds).then((res) => {
        if (res.code === 200) {
          that.userInfo.interest_tags = [...that.selectedNewTags];
          const cachedUserInfo = common_vendor.index.getStorageSync("USER_INFO");
          if (cachedUserInfo) {
            cachedUserInfo.interest_tags = [...that.selectedNewTags];
            common_vendor.index.setStorageSync("USER_INFO", cachedUserInfo);
          }
          common_vendor.index.$emit("updateUserTags", {
            tags: that.selectedNewTags,
            userId: that.userInfo.id
          });
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
          that.$refs.newTagsPopup.close();
        } else {
          throw new Error(res.msg || "保存失败");
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/center/means.vue:2591", "更新标签失败:", err);
        common_vendor.index.showToast({
          title: err.message || "保存失败，请重试",
          icon: "none"
        });
      }).finally(() => {
        common_vendor.index.hideLoading();
      });
    },
    // 获取新标签的ID
    getNewTagIdByName(tagName) {
      if (!tagName) {
        common_vendor.index.__f__("error", "at pages/center/means.vue:2604", "标签名为空");
        return null;
      }
      common_vendor.index.__f__("log", "at pages/center/means.vue:2608", "查找新标签ID，标签名:", tagName);
      for (const category of this.newTagCategories) {
        if (category.originalTags && Array.isArray(category.originalTags)) {
          for (const tag of category.originalTags) {
            if (tag && tag.name === tagName) {
              common_vendor.index.__f__("log", "at pages/center/means.vue:2616", "在新分类中找到ID:", tag.id);
              return tag.id;
            }
          }
        }
      }
      common_vendor.index.__f__("warn", "at pages/center/means.vue:2624", `未找到新标签 "${tagName}" 的ID`);
      return null;
    },
    newSwiperChange(e) {
      this.newCategoryIndex = e.detail.current;
    },
    // 获取用户信息
    getUserInfo() {
      const that = this;
      return new Promise((resolve, reject) => {
        api_social.getUserSocialInfo().then(function(res) {
          if (res.code == 200 || res.status == 200) {
            common_vendor.index.__f__("log", "at pages/center/means.vue:2639", "刷新用户信息成功:", JSON.stringify(res.data));
            const userData = res.data || res.result;
            if (userData) {
              common_vendor.index.setStorageSync("USER_INFO", userData);
              that.userInfo = userData;
              that.originalUserInfo = JSON.parse(JSON.stringify(userData));
              if (that.userInfo.birthday && that.userInfo.constellation === null) {
                that.calculateConstellationFromBirthday();
              }
              that.handleBackgroundImages();
              resolve(userData);
            }
          } else {
            common_vendor.index.__f__("error", "at pages/center/means.vue:2657", "获取用户信息失败:", res.msg || "未知错误");
            reject(new Error(res.msg || "获取用户信息失败"));
          }
        }).catch((err) => {
          common_vendor.index.__f__("error", "at pages/center/means.vue:2661", "获取用户信息错误:", err);
          reject(err);
        });
      });
    },
    // 不覆盖当前正在编辑的内容的刷新方法
    refreshUserInfoWithoutOverwrite() {
      const that = this;
      const currentAvatar = that.userInfo.avatar;
      api_social.getUserSocialInfo().then(function(res) {
        if (res.code == 200 || res.status == 200) {
          common_vendor.index.__f__("log", "at pages/center/means.vue:2677", "刷新用户信息成功:", JSON.stringify(res.data));
          const userData = res.data || res.result;
          if (userData) {
            userData.avatar = currentAvatar;
            common_vendor.index.setStorageSync("USER_INFO", userData);
            that.userInfo = {
              ...userData,
              avatar: currentAvatar
              // 确保头像不被覆盖
            };
            that.handleBackgroundImages();
          }
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/center/means.vue:2697", "刷新用户信息失败:", err);
      });
    },
    // 仅加载我的标签数据，不更新用户信息
    loadMyTagsOnly() {
      const that = this;
      common_vendor.index.__f__("log", "at pages/center/means.vue:2705", "开始加载我的标签数据...");
      return new Promise((resolve, reject) => {
        api_social.getMyTags().then(function(res) {
          common_vendor.index.__f__("log", "at pages/center/means.vue:2711", "获取我的标签响应:", JSON.stringify(res));
          if (!res || typeof res !== "object") {
            common_vendor.index.__f__("error", "at pages/center/means.vue:2714", "响应不是一个有效对象");
            reject(new Error("响应格式无效"));
            return;
          }
          if (res.code == 200 || res.status == 200) {
            let tagData = res.data || res.result;
            if (tagData) {
              if (!Array.isArray(tagData) && typeof tagData === "object") {
                tagData = tagData.list || tagData.data || Object.values(tagData);
              }
              if (Array.isArray(tagData) && tagData.length > 0) {
                common_vendor.index.__f__("log", "at pages/center/means.vue:2732", "成功获取我的标签，数量:", tagData.length);
                that.selectedTagIds = tagData.map((tag) => {
                  if (typeof tag === "object" && tag.id) {
                    return tag.id;
                  } else if (typeof tag === "number") {
                    return tag;
                  }
                  return null;
                }).filter((id) => id !== null);
                common_vendor.index.__f__("log", "at pages/center/means.vue:2744", "保存已选标签ID:", that.selectedTagIds);
                const tagNames = tagData.map((tag) => {
                  if (typeof tag === "string") {
                    return tag;
                  } else if (tag && tag.name) {
                    return tag.name;
                  }
                  return null;
                }).filter((name) => name !== null);
                that.selectedNewTags = tagNames;
                common_vendor.index.__f__("log", "at pages/center/means.vue:2758", "从API获取的已选标签:", tagNames);
                resolve(tagData);
              } else {
                common_vendor.index.__f__("log", "at pages/center/means.vue:2761", "API返回的标签数据为空或格式不是数组");
                that.selectedNewTags = [];
                that.selectedTagIds = [];
                resolve([]);
              }
            } else {
              common_vendor.index.__f__("log", "at pages/center/means.vue:2767", "API返回的标签数据为空");
              that.selectedNewTags = [];
              that.selectedTagIds = [];
              resolve([]);
            }
          } else {
            common_vendor.index.__f__("error", "at pages/center/means.vue:2773", "加载我的标签失败:", res.msg || "未知错误");
            reject(new Error(res.msg || "加载标签失败"));
          }
        }).catch((err) => {
          common_vendor.index.__f__("error", "at pages/center/means.vue:2777", "加载我的标签请求错误:", err);
          reject(err);
        });
      });
    },
    // 加载新标签数据
    loadNewTagsData() {
      const that = this;
      common_vendor.index.__f__("log", "at pages/center/means.vue:2785", "开始加载新标签数据");
      return new Promise((resolve, reject) => {
        api_social.getTagsWithCategories().then(function(res) {
          common_vendor.index.__f__("log", "at pages/center/means.vue:2791", "标签数据接口原始响应:", JSON.stringify(res));
          const isSuccess = res.code == 200 || res.status == 200 || res.msg === "success";
          if (isSuccess) {
            if (!res.data) {
              common_vendor.index.__f__("log", "at pages/center/means.vue:2799", "API响应成功但数据为空");
              that.opTipsPopup("标签数据为空");
              resolve([]);
              return;
            }
            if (Array.isArray(res.data) && res.data.length === 0) {
              common_vendor.index.__f__("log", "at pages/center/means.vue:2807", "API返回的标签数据为空数组");
              that.opTipsPopup("暂无标签数据");
              resolve([]);
              return;
            }
            try {
              if (Array.isArray(res.data)) {
                that.processNewTagsData(res.data);
                resolve(res.data);
              } else {
                common_vendor.index.__f__("log", "at pages/center/means.vue:2819", "标签数据格式不是数组，尝试处理对象格式");
                that.processNewTagsData(res.data);
                resolve(res.data);
              }
            } catch (err) {
              common_vendor.index.__f__("error", "at pages/center/means.vue:2824", "处理新标签数据时出错:", err);
              that.opTipsPopup("标签数据处理失败");
              resolve([]);
            }
          } else {
            common_vendor.index.__f__("error", "at pages/center/means.vue:2829", "加载新标签数据失败:", res.msg || "未知错误");
            that.opTipsPopup("标签数据加载失败: " + (res.msg || "未知错误"));
            reject(new Error(res.msg || "加载标签失败"));
          }
        }).catch((err) => {
          common_vendor.index.__f__("error", "at pages/center/means.vue:2834", "加载新标签数据请求错误:", err);
          that.opTipsPopup("网络错误，请稍后重试");
          reject(err);
        });
      });
    }
  }
};
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.userInfo.avatar ? $data.backgroundImages.length + 1 : $data.backgroundImages.length),
    b: common_vendor.o((...args) => $options.changeProfilePhoto && $options.changeProfilePhoto(...args)),
    c: $data.userInfo.avatar
  }, $data.userInfo.avatar ? {
    d: $data.userInfo.avatar
  } : {
    e: common_assets._imports_0$13
  }, {
    f: common_vendor.o(($event) => $data.userInfo.avatar ? $options.showPhotoMenu({
      type: "头像",
      url: $data.userInfo.avatar
    }, -1) : $options.changeProfilePhoto()),
    g: $options.hasPhotoOfType("生活照")
  }, $options.hasPhotoOfType("生活照") ? {
    h: $options.getPhotoByType("生活照")
  } : {
    i: common_assets._imports_1$16
  }, {
    j: common_vendor.o(($event) => $options.hasPhotoOfType("生活照") ? $options.showPhotoMenu($options.getPhotoObject("生活照"), $options.getPhotoIndex("生活照")) : $options.addPhotoByType("生活照")),
    k: $options.hasPhotoOfType("旅行照")
  }, $options.hasPhotoOfType("旅行照") ? {
    l: $options.getPhotoByType("旅行照")
  } : {
    m: common_assets._imports_2$11
  }, {
    n: common_vendor.o(($event) => $options.hasPhotoOfType("旅行照") ? $options.showPhotoMenu($options.getPhotoObject("旅行照"), $options.getPhotoIndex("旅行照")) : $options.addPhotoByType("旅行照")),
    o: $options.hasPhotoOfType("才艺照")
  }, $options.hasPhotoOfType("才艺照") ? {
    p: $options.getPhotoByType("才艺照")
  } : {
    q: common_assets._imports_3$12
  }, {
    r: common_vendor.o(($event) => $options.hasPhotoOfType("才艺照") ? $options.showPhotoMenu($options.getPhotoObject("才艺照"), $options.getPhotoIndex("才艺照")) : $options.addPhotoByType("才艺照")),
    s: $options.hasPhotoOfType("回忆照")
  }, $options.hasPhotoOfType("回忆照") ? {
    t: $options.getPhotoByType("回忆照")
  } : {
    v: common_assets._imports_4$3
  }, {
    w: common_vendor.o(($event) => $options.hasPhotoOfType("回忆照") ? $options.showPhotoMenu($options.getPhotoObject("回忆照"), $options.getPhotoIndex("回忆照")) : $options.addPhotoByType("回忆照")),
    x: $options.hasPhotoOfType("美食宠物照")
  }, $options.hasPhotoOfType("美食宠物照") ? {
    y: $options.getPhotoByType("美食宠物照")
  } : {
    z: common_assets._imports_5$5
  }, {
    A: common_vendor.o(($event) => $options.hasPhotoOfType("美食宠物照") ? $options.showPhotoMenu($options.getPhotoObject("美食宠物照"), $options.getPhotoIndex("美食宠物照")) : $options.addPhotoByType("美食宠物照")),
    B: common_vendor.o((...args) => $options.nameBlur && $options.nameBlur(...args)),
    C: $data.userInfo.nickname,
    D: common_vendor.o(($event) => $data.userInfo.nickname = $event.detail.value),
    E: $data.userInfo.sex === 1
  }, $data.userInfo.sex === 1 ? {} : $data.userInfo.sex === 2 ? {} : {}, {
    F: $data.userInfo.sex === 2,
    G: $data.userInfo.sex !== void 0 ? "#000" : "#999",
    H: common_assets._imports_1$4,
    I: common_vendor.o(($event) => $options.genderPopupClick(true)),
    J: common_vendor.o((...args) => $options.introBlur && $options.introBlur(...args)),
    K: $data.userInfo.about_me,
    L: common_vendor.o(($event) => $data.userInfo.about_me = $event.detail.value),
    M: $data.userInfo.interest_tags && $data.userInfo.interest_tags.length > 0
  }, $data.userInfo.interest_tags && $data.userInfo.interest_tags.length > 0 ? {
    N: common_vendor.t($data.userInfo.interest_tags.join("、"))
  } : {}, {
    O: $data.userInfo.interest_tags && $data.userInfo.interest_tags.length > 0 ? "#000" : "#999",
    P: common_assets._imports_1$4,
    Q: common_vendor.o(($event) => $options.newTagsPopupClick(true)),
    R: common_vendor.t($options.formattedBirthday || "点击选择"),
    S: $data.userInfo.birthday ? "#000" : "#999",
    T: common_assets._imports_1$4,
    U: common_vendor.o(($event) => $options.birthdayPopupClick(true)),
    V: common_vendor.t($data.userInfo.constellation !== null ? $options.getConstellationName($data.userInfo.constellation) : "选择生日后自动生成"),
    W: $data.userInfo.constellation !== null ? "#000" : "#999",
    X: $data.userInfo.school,
    Y: common_vendor.o(($event) => $data.userInfo.school = $event.detail.value),
    Z: $data.userInfo.hometown,
    aa: common_vendor.o(($event) => $data.userInfo.hometown = $event.detail.value),
    ab: $data.userInfo.occupation,
    ac: common_vendor.o(($event) => $data.userInfo.occupation = $event.detail.value),
    ad: $data.userInfo.height,
    ae: common_vendor.o(($event) => $data.userInfo.height = $event.detail.value),
    af: $data.userInfo.weight,
    ag: common_vendor.o(($event) => $data.userInfo.weight = $event.detail.value),
    ah: common_vendor.t($data.userInfo.phone || "未绑定"),
    ai: common_assets._imports_0$8,
    aj: common_vendor.t($data.userInfo.phone ? "换绑" : "绑定"),
    ak: common_vendor.o((...args) => $options.bindMobileClick && $options.bindMobileClick(...args)),
    al: common_vendor.t($options.getAuthStatusText()),
    am: common_assets._imports_8$2,
    an: common_vendor.t($data.userInfo.auth_status === 2 ? "完成" : "认证"),
    ao: common_vendor.o((...args) => $options.goToRealAuth && $options.goToRealAuth(...args)),
    ap: common_vendor.t($data.userInfo.residence_name || $data.userInfo.province || "未知"),
    aq: common_assets._imports_9$2,
    ar: common_vendor.o((...args) => $options.refreshIpClick && $options.refreshIpClick(...args)),
    as: common_vendor.o((...args) => $options.saveAllChanges && $options.saveAllChanges(...args)),
    at: common_assets._imports_0$4,
    av: common_vendor.o(($event) => $options.birthdayPopupClick(false)),
    aw: common_vendor.f($data.years, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index
      };
    }),
    ax: common_vendor.f($data.months, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index
      };
    }),
    ay: common_vendor.f($data.days, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index
      };
    }),
    az: $data.birthdayPickerValue,
    aA: common_vendor.o((...args) => $options.birthdayPickerChange && $options.birthdayPickerChange(...args)),
    aB: common_vendor.o((...args) => $options.confirmBirthday && $options.confirmBirthday(...args)),
    aC: common_vendor.sr("birthdayPopup", "2062139e-0"),
    aD: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    aE: common_vendor.t(_ctx.selectedTags.length),
    aF: common_assets._imports_0$4,
    aG: common_vendor.o(($event) => $options.agePopupClick(false)),
    aH: common_vendor.f(_ctx.tagCategories, (category, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(category.name),
        b: category.tags && category.tags.length > 0
      }, category.tags && category.tags.length > 0 ? {
        c: common_vendor.t(category.tags.length)
      } : {}, {
        d: index,
        e: common_vendor.n(_ctx.currentCategoryIndex === index ? "category-active" : ""),
        f: common_vendor.o(($event) => $options.switchCategory(index), index)
      });
    }),
    aI: common_vendor.f(_ctx.tagCategories, (category, index, i0) => {
      return common_vendor.e({
        a: !category.tags || category.tags.length === 0
      }, !category.tags || category.tags.length === 0 ? {} : {}, {
        b: common_vendor.f(category.tags, (item, itemIndex, i1) => {
          return {
            a: common_vendor.t(item),
            b: itemIndex,
            c: common_vendor.n(_ctx.selectedTags.includes(item) ? "tagactive" : ""),
            d: common_vendor.o(($event) => $options.toggleTag(item), itemIndex)
          };
        }),
        c: index
      });
    }),
    aJ: _ctx.currentCategoryIndex,
    aK: common_vendor.o((...args) => $options.swiperChange && $options.swiperChange(...args)),
    aL: common_vendor.o((...args) => $options.confirmTags && $options.confirmTags(...args)),
    aM: common_vendor.sr("agePopup", "2062139e-1"),
    aN: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    aO: common_assets._imports_0$4,
    aP: common_vendor.o(($event) => $options.genderPopupClick(false)),
    aQ: common_assets._imports_11$2,
    aR: common_vendor.o(($event) => $options.genderItemClick(1)),
    aS: common_vendor.n(_ctx.tempGender == 1 ? "active-1" : ""),
    aT: common_assets._imports_12$1,
    aU: common_vendor.o(($event) => $options.genderItemClick(2)),
    aV: common_vendor.n(_ctx.tempGender == 2 ? "active-2" : ""),
    aW: common_vendor.o(($event) => $options.genderItemClick(0)),
    aX: common_vendor.n(_ctx.tempGender == 0 ? "active-1" : ""),
    aY: common_vendor.o((...args) => $options.confirmGender && $options.confirmGender(...args)),
    aZ: common_vendor.sr("genderPopup", "2062139e-2"),
    ba: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    bb: common_vendor.t(_ctx.tipsTitle),
    bc: common_vendor.sr("tipsPopup", "2062139e-3"),
    bd: common_vendor.p({
      type: "center",
      ["mask-background-color"]: "rgba(0, 0, 0, 0.3)"
    }),
    be: _ctx.canvasStatus
  }, _ctx.canvasStatus ? {
    bf: _ctx.canvasWidth + "px",
    bg: _ctx.canvasHeight + "px"
  } : {}, {
    bh: common_vendor.t(_ctx.selectedNewTags.length),
    bi: common_assets._imports_0$4,
    bj: common_vendor.o(($event) => $options.newTagsPopupClick(false)),
    bk: common_vendor.f(_ctx.newTagCategories, (category, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(category.name),
        b: category.tags && category.tags.length > 0
      }, category.tags && category.tags.length > 0 ? {
        c: common_vendor.t(category.tags.length)
      } : {}, {
        d: index,
        e: common_vendor.n(_ctx.newCategoryIndex === index ? "category-active" : ""),
        f: common_vendor.o(($event) => $options.switchNewCategory(index), index)
      });
    }),
    bl: common_vendor.f(_ctx.newTagCategories, (category, index, i0) => {
      return common_vendor.e({
        a: !category.tags || category.tags.length === 0
      }, !category.tags || category.tags.length === 0 ? {} : {}, {
        b: common_vendor.f(category.tags, (item, itemIndex, i1) => {
          return {
            a: common_vendor.t(item),
            b: itemIndex,
            c: common_vendor.n(_ctx.selectedNewTags.includes(item) ? "tagactive" : ""),
            d: common_vendor.o(($event) => $options.toggleNewTag(item), itemIndex)
          };
        }),
        c: index
      });
    }),
    bm: _ctx.newCategoryIndex,
    bn: common_vendor.o((...args) => $options.newSwiperChange && $options.newSwiperChange(...args)),
    bo: common_vendor.o((...args) => $options.confirmNewTags && $options.confirmNewTags(...args)),
    bp: common_vendor.sr("newTagsPopup", "2062139e-4"),
    bq: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    br: common_vendor.o((...args) => $options.formSubmit && $options.formSubmit(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/center/means.js.map
