{"version": 3, "file": "cache.js", "sources": ["utils/cache.js"], "sourcesContent": ["// +----------------------------------------------------------------------\r\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\r\n// +----------------------------------------------------------------------\r\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\r\n// +----------------------------------------------------------------------\r\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\r\n// +----------------------------------------------------------------------\r\n// | Author: CRMEB Team <<EMAIL>>\r\n// +----------------------------------------------------------------------\r\n\r\nimport appConfig from '../config/app.js';\r\nconst { EXPIRE } = appConfig;\r\n\r\nclass Cache {\r\n\r\n\tconstructor(handler) {\r\n\t\tthis.cacheSetHandler = uni.setStorageSync;\r\n\t\tthis.cacheGetHandler = uni.getStorageSync;\r\n\t\tthis.cacheClearHandler = uni.removeStorageSync;\r\n\t\tthis.cacheExpire = 'UNI-APP-CRMEB:TAG';\r\n\t\tthis.clearOverdue();\r\n\t}\r\n\r\n\t/**\r\n\t * 获取当前时间戳\r\n\t */\r\n\ttime() {\r\n\t\treturn Math.round(new Date() / 1000);\r\n\t}\r\n\r\n\t/**\r\n\t * 字符串转时间戳\r\n\t * @param {Object} expiresTime\r\n\t */\r\n\tstrTotime(expiresTime) {\r\n\t\tlet expires_time = expiresTime.substring(0, 19);\r\n\t\texpires_time = expires_time.replace(/-/g, '/');\r\n\t\treturn Math.round(new Date(expires_time).getTime() / 1000);\r\n\t}\r\n\r\n\r\n\t/**\r\n\t * 设置过期时间缓存\r\n\t * @param {Object} key\r\n\t * @param {Object} expire\r\n\t */\r\n\tsetExpireCaheTag(key, expire) {\r\n\t\texpire = expire !== undefined ? expire : EXPIRE;\r\n\t\tif (typeof expire === 'number') {\r\n\t\t\tlet tag = this.cacheGetHandler(this.cacheExpire),\r\n\t\t\t\tnewTag = [],\r\n\t\t\t\tnewKeys = [];\r\n\t\t\tif (typeof tag === 'object' && tag.length) {\r\n\t\t\t\tnewTag = tag.map(item => {\r\n\t\t\t\t\tnewKeys.push(item.key);\r\n\t\t\t\t\tif (item.key === key) {\r\n\t\t\t\t\t\titem.expire = expire === 0 ? 0 : this.time() + expire;\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn item;\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\tif (!newKeys.length || newKeys.indexOf(key) === -1) {\r\n\t\t\t\tnewTag.push({\r\n\t\t\t\t\tkey: key,\r\n\t\t\t\t\texpire: expire === 0 ? 0 : this.time() + expire\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\tthis.cacheSetHandler(this.cacheExpire, newTag);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 缓存是否过期,过期自动删除\r\n\t * @param {Object} key\r\n\t * @param {Object} $bool true = 删除,false = 不删除\r\n\t */\r\n\tgetExpireCahe(key, $bool) {\r\n\t\ttry {\r\n\t\t\tlet tag = this.cacheGetHandler(this.cacheExpire),\r\n\t\t\t\ttime = 0,\r\n\t\t\t\tindex = false;\r\n\t\t\tif (typeof tag === 'object' && tag.length) {\r\n\t\t\t\ttag.map((item, i) => {\r\n\t\t\t\t\tif (item.key === key) {\r\n\t\t\t\t\t\ttime = item.expire\r\n\t\t\t\t\t\tindex = i\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\tif (time) {\r\n\t\t\t\t\tlet newTime = parseInt(time);\r\n\t\t\t\t\tif (time && time < this.time() && !Number.isNaN(newTime)) {\r\n\t\t\t\t\t\tif ($bool === undefined || $bool === true) {\r\n\t\t\t\t\t\t\tthis.cacheClearHandler(key);\r\n\t\t\t\t\t\t\tif (index !== false) {\r\n\t\t\t\t\t\t\t\ttag.splice(index, 1)\r\n\t\t\t\t\t\t\t\tthis.cacheSetHandler(this.cacheExpire, tag);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t} else\r\n\t\t\t\t\t\treturn true;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn !!this.cacheGetHandler(key);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn false;\r\n\t\t} catch (e) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 设置缓存\r\n\t * @param {Object} key\r\n\t * @param {Object} data\r\n\t */\r\n\tset(key, data, expire) {\r\n\t\tif (data === undefined) {\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\tif (typeof data === 'object')\r\n\t\t\tdata = JSON.stringify(data);\r\n\t\ttry {\r\n\t\t\tthis.setExpireCaheTag(key, expire);\r\n\t\t\treturn this.cacheSetHandler(key, data);\r\n\t\t} catch (e) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 检测缓存是否存在\r\n\t * @param {Object} key\r\n\t */\r\n\thas(checkwhethethecacheexists, isDel) {\r\n\t\tthis.clearOverdue();\r\n\t\treturn this.getExpireCahe(checkwhethethecacheexists, isDel);\r\n\t}\r\n\r\n\t/**\r\n\t * 获取缓存\r\n\t * @param {Object} key\r\n\t * @param {Object} $default\r\n\t * @param {Object} expire\r\n\t */\r\n\tget(key, $default, expire) {\r\n\t\tthis.clearOverdue();\r\n\t\ttry {\r\n\t\t\tlet isBe = this.getExpireCahe(key);\r\n\t\t\tlet data = this.cacheGetHandler(key);\r\n\t\t\tif (data && isBe) {\r\n\t\t\t\tif (typeof $default === 'boolean')\r\n\t\t\t\t\treturn JSON.parse(data);\r\n\t\t\t\telse\r\n\t\t\t\t\treturn data;\r\n\t\t\t} else {\r\n\t\t\t\tif (typeof $default === 'function') {\r\n\t\t\t\t\tlet value = $default();\r\n\t\t\t\t\tthis.set(key, value, expire);\r\n\t\t\t\t\treturn value;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.set(key, $default, expire);\r\n\t\t\t\t\treturn $default;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} catch (e) {\r\n\t\t\treturn null;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 删除缓存\r\n\t * @param {Object} key\r\n\t */\r\n\tclear(key) {\r\n\t\ttry {\r\n\t\t\tlet cahceValue = this.cacheGetHandler(this.cacheExpire),\r\n\t\t\t\tindex = false;\r\n\t\t\tif (cahceValue && typeof cahceValue === 'object' && cahceValue.length) {\r\n\t\t\t\tcahceValue.map((item, i) => {\r\n\t\t\t\t\tif (item.key === key) {\r\n\t\t\t\t\t\tindex = i;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t\tif (index !== false) {\r\n\t\t\t\t\tcahceValue.splice(index, 1);\r\n\t\t\t\t}\r\n\t\t\t\tthis.cacheSetHandler(this.cacheExpire, cahceValue);\r\n\t\t\t}\r\n\t\t\treturn this.cacheClearHandler(key);\r\n\t\t} catch (e) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 清除过期缓存\r\n\t */\r\n\tclearOverdue() {\r\n\t\tlet cahceValue = this.cacheGetHandler(this.cacheExpire),\r\n\t\t\ttime = this.time(),\r\n\t\t\tnewBeOverdueValue = [],\r\n\t\t\tnewTagValue = [];\r\n\r\n\t\tif (cahceValue && typeof cahceValue === 'object' && cahceValue.length) {\r\n\t\t\tcahceValue.map(item => {\r\n\t\t\t\tif (item) {\r\n\t\t\t\t\tif ((item.expire !== undefined && item.expire > time) || item.expire === 0) {\r\n\t\t\t\t\t\tnewTagValue.push(item);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tnewBeOverdueValue.push(item.key);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t\t//保存没有过期的缓存标签\r\n\t\tif (newTagValue.length !== cahceValue.length) {\r\n\t\t\tthis.cacheSetHandler(this.cacheExpire, newTagValue);\r\n\t\t}\r\n\t\t//删除过期缓存\r\n\t\tnewBeOverdueValue.forEach(k => {\r\n\t\t\tthis.cacheClearHandler(k);\r\n\t\t})\r\n\t}\r\n}\r\n\r\n\r\nexport default new Cache;\r\n"], "names": ["appConfig", "uni"], "mappings": ";;;AAWA,MAAM,EAAE,OAAQ,IAAGA;AAEnB,MAAM,MAAM;AAAA,EAEX,YAAY,SAAS;AACpB,SAAK,kBAAkBC,cAAG,MAAC;AAC3B,SAAK,kBAAkBA,cAAG,MAAC;AAC3B,SAAK,oBAAoBA,cAAG,MAAC;AAC7B,SAAK,cAAc;AACnB,SAAK,aAAY;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO;AACN,WAAO,KAAK,MAAM,oBAAI,KAAM,IAAG,GAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,UAAU,aAAa;AACtB,QAAI,eAAe,YAAY,UAAU,GAAG,EAAE;AAC9C,mBAAe,aAAa,QAAQ,MAAM,GAAG;AAC7C,WAAO,KAAK,MAAM,IAAI,KAAK,YAAY,EAAE,QAAO,IAAK,GAAI;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,iBAAiB,KAAK,QAAQ;AAC7B,aAAS,WAAW,SAAY,SAAS;AACzC,QAAI,OAAO,WAAW,UAAU;AAC/B,UAAI,MAAM,KAAK,gBAAgB,KAAK,WAAW,GAC9C,SAAS,CAAE,GACX,UAAU,CAAA;AACX,UAAI,OAAO,QAAQ,YAAY,IAAI,QAAQ;AAC1C,iBAAS,IAAI,IAAI,UAAQ;AACxB,kBAAQ,KAAK,KAAK,GAAG;AACrB,cAAI,KAAK,QAAQ,KAAK;AACrB,iBAAK,SAAS,WAAW,IAAI,IAAI,KAAK,KAAM,IAAG;AAAA,UAC/C;AACD,iBAAO;AAAA,QACZ,CAAK;AAAA,MACD;AACD,UAAI,CAAC,QAAQ,UAAU,QAAQ,QAAQ,GAAG,MAAM,IAAI;AACnD,eAAO,KAAK;AAAA,UACX;AAAA,UACA,QAAQ,WAAW,IAAI,IAAI,KAAK,KAAI,IAAK;AAAA,QAC9C,CAAK;AAAA,MACD;AACD,WAAK,gBAAgB,KAAK,aAAa,MAAM;AAAA,IAC7C;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,cAAc,KAAK,OAAO;AACzB,QAAI;AACH,UAAI,MAAM,KAAK,gBAAgB,KAAK,WAAW,GAC9C,OAAO,GACP,QAAQ;AACT,UAAI,OAAO,QAAQ,YAAY,IAAI,QAAQ;AAC1C,YAAI,IAAI,CAAC,MAAM,MAAM;AACpB,cAAI,KAAK,QAAQ,KAAK;AACrB,mBAAO,KAAK;AACZ,oBAAQ;AAAA,UACR;AAAA,QACN,CAAK;AACD,YAAI,MAAM;AACT,cAAI,UAAU,SAAS,IAAI;AAC3B,cAAI,QAAQ,OAAO,KAAK,KAAM,KAAI,CAAC,OAAO,MAAM,OAAO,GAAG;AACzD,gBAAI,UAAU,UAAa,UAAU,MAAM;AAC1C,mBAAK,kBAAkB,GAAG;AAC1B,kBAAI,UAAU,OAAO;AACpB,oBAAI,OAAO,OAAO,CAAC;AACnB,qBAAK,gBAAgB,KAAK,aAAa,GAAG;AAAA,cAC1C;AAAA,YACD;AACD,mBAAO;AAAA,UACP;AACA,mBAAO;AAAA,QACb,OAAW;AACN,iBAAO,CAAC,CAAC,KAAK,gBAAgB,GAAG;AAAA,QACjC;AAAA,MACD;AACD,aAAO;AAAA,IACP,SAAQ,GAAG;AACX,aAAO;AAAA,IACP;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,IAAI,KAAK,MAAM,QAAQ;AACtB,QAAI,SAAS,QAAW;AACvB,aAAO;AAAA,IACP;AACD,QAAI,OAAO,SAAS;AACnB,aAAO,KAAK,UAAU,IAAI;AAC3B,QAAI;AACH,WAAK,iBAAiB,KAAK,MAAM;AACjC,aAAO,KAAK,gBAAgB,KAAK,IAAI;AAAA,IACrC,SAAQ,GAAG;AACX,aAAO;AAAA,IACP;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,IAAI,2BAA2B,OAAO;AACrC,SAAK,aAAY;AACjB,WAAO,KAAK,cAAc,2BAA2B,KAAK;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,IAAI,KAAK,UAAU,QAAQ;AAC1B,SAAK,aAAY;AACjB,QAAI;AACH,UAAI,OAAO,KAAK,cAAc,GAAG;AACjC,UAAI,OAAO,KAAK,gBAAgB,GAAG;AACnC,UAAI,QAAQ,MAAM;AACjB,YAAI,OAAO,aAAa;AACvB,iBAAO,KAAK,MAAM,IAAI;AAAA;AAEtB,iBAAO;AAAA,MACZ,OAAU;AACN,YAAI,OAAO,aAAa,YAAY;AACnC,cAAI,QAAQ;AACZ,eAAK,IAAI,KAAK,OAAO,MAAM;AAC3B,iBAAO;AAAA,QACZ,OAAW;AACN,eAAK,IAAI,KAAK,UAAU,MAAM;AAC9B,iBAAO;AAAA,QACP;AAAA,MACD;AAAA,IACD,SAAQ,GAAG;AACX,aAAO;AAAA,IACP;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,KAAK;AACV,QAAI;AACH,UAAI,aAAa,KAAK,gBAAgB,KAAK,WAAW,GACrD,QAAQ;AACT,UAAI,cAAc,OAAO,eAAe,YAAY,WAAW,QAAQ;AACtE,mBAAW,IAAI,CAAC,MAAM,MAAM;AAC3B,cAAI,KAAK,QAAQ,KAAK;AACrB,oBAAQ;AAAA,UACR;AAAA,QACN,CAAK;AAED,YAAI,UAAU,OAAO;AACpB,qBAAW,OAAO,OAAO,CAAC;AAAA,QAC1B;AACD,aAAK,gBAAgB,KAAK,aAAa,UAAU;AAAA,MACjD;AACD,aAAO,KAAK,kBAAkB,GAAG;AAAA,IACjC,SAAQ,GAAG;AACX,aAAO;AAAA,IACP;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe;AACd,QAAI,aAAa,KAAK,gBAAgB,KAAK,WAAW,GACrD,OAAO,KAAK,KAAM,GAClB,oBAAoB,CAAE,GACtB,cAAc,CAAA;AAEf,QAAI,cAAc,OAAO,eAAe,YAAY,WAAW,QAAQ;AACtE,iBAAW,IAAI,UAAQ;AACtB,YAAI,MAAM;AACT,cAAK,KAAK,WAAW,UAAa,KAAK,SAAS,QAAS,KAAK,WAAW,GAAG;AAC3E,wBAAY,KAAK,IAAI;AAAA,UAC3B,OAAY;AACN,8BAAkB,KAAK,KAAK,GAAG;AAAA,UAC/B;AAAA,QACD;AAAA,MACL,CAAI;AAAA,IACD;AAED,QAAI,YAAY,WAAW,WAAW,QAAQ;AAC7C,WAAK,gBAAgB,KAAK,aAAa,WAAW;AAAA,IAClD;AAED,sBAAkB,QAAQ,OAAK;AAC9B,WAAK,kBAAkB,CAAC;AAAA,IAC3B,CAAG;AAAA,EACD;AACF;AAGA,MAAA,UAAe,IAAI;;"}