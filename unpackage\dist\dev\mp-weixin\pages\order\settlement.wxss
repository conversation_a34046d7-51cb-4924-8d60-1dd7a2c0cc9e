
page { background: #f8f8f8;
}
.container { padding-bottom: 240rpx;
}
.w100p30 {
  z-index: 1;
  margin: 20rpx 30rpx;
  width: calc(100% - 100rpx);
  padding: 30rpx 20rpx;
  border-radius: 30rpx;
  background: #fff;
  overflow: hidden;
  position: relative;
}
.title {
  width: 100%;
  color: #000;
  font-size: 26rpx;
  font-weight: 700;
}
.w100p30 .map-bg {
  position: absolute;
  z-index: -2;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}
.w100p30 .mask-bg {
  position: absolute;
  z-index: -1;
  right: -2rpx;
  bottom: -2rpx;
  width: calc(100% - 24rpx);
  height: calc(100% - 24rpx);
  border: 14rpx solid #fff;
  border-radius: 30rpx;
  background-image: linear-gradient(to right, #fff, rgba(255, 255, 255, .9), rgba(255, 255, 255, .6));
}
.w100p30 .adds-box {
  z-index: 1;
  width: 100%;
  height: 100%;
  justify-content: space-between;
}
.adds-box .adds-item {
  color: #000;
  font-size: 24rpx;
  font-weight: 700;
}
.adds-box .adds-item .txt {
  padding: 10rpx 0;
  font-size: 32rpx;
}
.adds-box .adds-add image {
  width: 28rpx;
  height: 28rpx;
  transform: rotate(-90deg);
}
.goods-item {
  padding-top: 30rpx;
  justify-content: space-between;
  animation: fadeIn .45s ease;
}
.goods-item image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 8rpx;
}
.goods-item .goods-info {
  width: calc(100% - 160rpx);
  height: 140rpx;
  font-weight: 700;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.goods-item .goods-info .t1 {
  color: #000;
  font-size: 26rpx;
}
.goods-item .goods-info .t2 {
  color: #999;
  font-size: 24rpx;
}
.goods-item .goods-info .goods-info-bom {
  margin-top: 25rpx;
  width: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}
.goods-info .goods-info-bom .sum {
  margin-right: 20rpx;
  color: #999;
  font-size: 22rpx;
}
.list-item {
  padding: 15rpx 0;
  justify-content: space-between;
  color: #000;
  font-size: 24rpx;
}
.list-item .list-right .zs {
  color: #999;
  margin-right: 10rpx;
}
.list-item .list-right .icon {
  width: 22rpx;
  height: 22rpx;
  transform: rotate(-90deg);
}
.list-item textarea {
  width: calc(100% - 40rpx);
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f8f8;
  font-size: 24rpx;
  font-weight: 700;
  min-height: 120rpx;
}
.list-item .list-item-xz {
  margin-top: 15rpx;
  color: #999;
  font-weight: 300;
  font-size: 24rpx;
}
.list-item .list-item-xz text {
  margin-left: 6rpx;
  color: #000;
  font-weight: 500;
  text-decoration: underline;
}
.footer {
  position: fixed;
  z-index: 99;
  bottom: 0;
  left: 0;
  width: calc(100% - 80rpx);
  padding: 30rpx 40rpx;
  flex-direction: column;
  background-color: #fff;
  padding-bottom: max(env(safe-area-inset-bottom), 20rpx);
  border-top: 2rpx solid #f5f5f5;
}
.footer .btn {
  width: 100%;
  height: 100rpx;
  justify-content: center;
  background: #000;
  border-radius: 50rpx;
}
.footer .btn image {
  width: 32rpx;
  height: 32rpx;
}
.footer .btn text {
  margin-left: 12rpx;
  color: #fff;
  font-size: 26rpx;
  font-weight: bolder;
}
.popup-box {
  width: calc(100% - 40rpx);
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 30rpx 30rpx 0 0;
  position: relative;
  overflow: hidden;
}
.popup-box .popup-top {
  width: calc(100% - 20rpx);
  padding: 10rpx;
  justify-content: space-between;
}
.popup-top .popup-title .t1 {
  font-size: 38rpx;
  font-weight: 700;
}
.popup-top .popup-title .t2 {
  color: #999;
  font-size: 20rpx;
  font-weight: 300;
}
.popup-top .popup-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #eee;
  justify-content: center;
  transform: rotate(45deg);
}
.popup-box .popup-scroll {
  padding-top: 20rpx;
  width: 100%;
  max-height: 50vh;
  overflow-y: scroll;
}
.popup-scroll .coupon {
  margin: 0 6rpx 30rpx;
  width: calc(100% - 20rpx);
  background: #fff;
  border-width: 4rpx;
  border-style: solid;
  border-radius: 8rpx;
  position: relative;
  overflow: hidden;
}
.coupon .coupon-bg {
  position: absolute;
  z-index: 1;
  right: -90rpx;
  bottom: -120rpx;
  width: 380rpx;
  height: 380rpx;
}
.coupon .coupon-sub {
  position: absolute;
  z-index: 9;
  top: 0;
  left: 0;
  border-radius: 8rpx 0;
  padding: 0rpx 12rpx;
  height: 36rpx;
  line-height: 36rpx;
  text-align: center;
  font-size: 20rpx;
  color: #fa5150;
  background: rgba(250, 81, 80, .125);
}
.coupon .coupon-item {
  z-index: 2;
  width: calc(100% - 80rpx);
  padding: 50rpx 40rpx 20rpx;
  border-bottom: 2rpx dashed #f8f8f8;
  position: relative;
}
.coupon-item .coupon-price {
  width: calc(100% - 200rpx);
  color: #000;
  font-size: 48rpx;
  font-weight: 700;
}
.coupon-item .coupon-intro {
  width: calc(100% - 200rpx);
  margin: 8rpx 0;
  color: #444;
  font-size: 24rpx;
}
.coupon .coupon-validity {
  width: calc(100% - 80rpx);
  padding: 20rpx 40rpx;
  color: #999;
  font-size: 20rpx;
}
.coupon .coupon-err {
  position: absolute;
  z-index: 10;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 28rpx;
  font-style: italic;
  font-weight: 700;
  justify-content: center;
  color: #ccc;
  background: rgba(255, 255, 255, .85);
}
.popup-box .popup-btn {
  margin: 40rpx 10rpx;
  width: calc(100% - 20rpx);
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 100rpx;
}
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}
.empty-box image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-box .e1 {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.empty-box .e2 {
  font-size: 24rpx;
  color: #999;
}
.tips-box {
  padding: 20rpx 30rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  justify-content: center;
}
.tips-box .tips-item {
  color: #fff;
  font-size: 28rpx;
  font-weight: 700;
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
