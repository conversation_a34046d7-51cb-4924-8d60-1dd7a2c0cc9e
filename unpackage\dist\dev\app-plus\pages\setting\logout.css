
.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.nav-bar[data-v-eaf4c2e5] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.nav-bar .navbar-item[data-v-eaf4c2e5] {
  width: 100%;
  display: flex;
  align-items: center;
}
.navbar-item .back-box[data-v-eaf4c2e5] {
  padding: 0 0.9375rem;
  display: flex;
  align-items: center;
}
.navbar-item .back-box uni-image[data-v-eaf4c2e5] {
  width: 1.0625rem;
  height: 1.0625rem;
}
.bfw[data-v-eaf4c2e5] {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,.8);
}
.bf8[data-v-eaf4c2e5] {
  background: #f8f8f8;
}


.container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
}
.title-box {
  font-size: 1.25rem;
  font-weight: 700;
}
/* 内容区域 - 使用原生滚动，性能更好 */
.content-wrapper {
  flex: 1;
  padding: 0 0.9375rem;
  box-sizing: border-box;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动流畅度 */
}
/* 加载状态 */
.loading-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.25rem 0;
  min-height: 6.25rem;
}
.loading-icon {
  width: 1.875rem;
  height: 1.875rem;
  margin-bottom: 0.625rem;
  border: 0.125rem solid #f3f3f3;
  border-top: 0.125rem solid #666;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text {
  font-size: 0.8125rem;
  color: #666;
}
/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  padding: 0.625rem 0;
  margin-bottom: 0.625rem;
}
.user-avatar {
  width: 2.375rem;
  height: 2.375rem;
  margin-right: 0.625rem;
  border-radius: 50%;
}
.user-name {
  font-size: 0.875rem;
  font-weight: 500;
}
/* 协议内容样式 */
.agreement-content {
  margin: 0.625rem 0 1.25rem;
  font-size: 0.8125rem;
  line-height: 1.6;
  color: #333;
}
/* 底部安全区域 */
.bottom-safe-area {
  height: 5.625rem;
  width: 100%;
}
.footer-box {
  position: fixed;
  z-index: 99;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  padding: 0.9375rem;
  box-sizing: border-box;
  padding-bottom: max(env(safe-area-inset-bottom), 0.9375rem);
}
.notice {
  color: #999;
  font-size: 0.75rem;
  text-align: center;
  margin-bottom: 0.625rem;
}
.btn-box {
  width: 100%;
  height: 3.125rem;
  line-height: 3.125rem;
  text-align: center;
  font-size: 0.75rem;
  color: #fff;
  font-weight: 700;
  background: #000;
  border-radius: 3.125rem;
}
.bfw {
  background: #fff;
}
.bUp {
  box-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);
}
.df {
  display: flex;
  align-items: center;
}
.tips-box {
  padding: 0.625rem 0.9375rem;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 0.375rem;
  justify-content: center;
}
.tips-item {
  color: #fff;
  font-size: 0.875rem;
  font-weight: 700;
}
