"use strict";
const common_vendor = require("../common/vendor.js");
const pinia = common_vendor.createPinia();
pinia.use(({ store }) => {
  const persistFields = {
    user: ["userInfo", "isLogin", "token", "settings"],
    app: ["theme", "systemInfo", "config"]
  };
  let saveTimer = null;
  const debouncedSave = (storeId, data) => {
    if (saveTimer)
      clearTimeout(saveTimer);
    saveTimer = setTimeout(() => {
      try {
        const fieldsToSave = persistFields[storeId] || [];
        const dataToSave = {};
        fieldsToSave.forEach((field) => {
          if (data[field] !== void 0) {
            dataToSave[field] = data[field];
          }
        });
        common_vendor.index.setStorageSync(`pinia_${storeId}`, JSON.stringify(dataToSave));
      } catch (error) {
        common_vendor.index.__f__("warn", "at stores/index.js:42", "Pinia持久化失败:", error);
        if (error.name === "QuotaExceededError") {
          try {
            common_vendor.index.removeStorageSync(`pinia_${storeId}`);
            common_vendor.index.__f__("log", "at stores/index.js:47", "已清理存储空间，请重新操作");
          } catch (e) {
            common_vendor.index.__f__("warn", "at stores/index.js:49", "清理存储失败:", e);
          }
        }
      }
    }, 1e3);
  };
  if (store.$id === "user" || store.$id === "app") {
    store.$subscribe((mutation, state) => {
      debouncedSave(store.$id, state);
    });
  }
  if (store.$id === "user" || store.$id === "app") {
    try {
      const savedState = common_vendor.index.getStorageSync(`pinia_${store.$id}`);
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        store.$patch(parsedState);
      }
    } catch (error) {
      common_vendor.index.__f__("warn", "at stores/index.js:72", "Pinia状态恢复失败:", error);
      try {
        common_vendor.index.removeStorageSync(`pinia_${store.$id}`);
      } catch (e) {
        common_vendor.index.__f__("warn", "at stores/index.js:77", "清理损坏数据失败:", e);
      }
    }
  }
});
exports.pinia = pinia;
//# sourceMappingURL=../../.sourcemap/mp-weixin/stores/index.js.map
