{"version": 3, "file": "card-gg.js", "sources": ["components/card-gg/card-gg.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy9jYXJkLWdnL2NhcmQtZ2cudnVl"], "sourcesContent": ["<template>\n  <view class=\"gg-box\" @tap=\"toPaths\">\n    <!-- 用户头像区域 -->\n    <view class=\"gg-avatar\" @tap.stop=\"toPaths\" data-type=\"1\">\n      <lazyImage :src=\"userAvatar\" br=\"50%\"></lazyImage>\n      <view v-if=\"displayState.isAuthenticated\" class=\"top df\">\n        <image src=\"/static/img/rz.png\" mode=\"aspectFit\" @error=\"onAuthIconError\"></image>\n      </view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view class=\"gg-item\">\n      <!-- 用户信息 -->\n      <view class=\"gg-item-user df\" style=\"width:100%\" @tap.stop=\"toPaths\" data-type=\"1\">\n        <view class=\"name-container df\">\n          <view class=\"name ohto\">{{userName}}</view>\n          <!-- VIP图标 -->\n          <view class=\"status-icon vip-icon\" v-if=\"userVipInfo.showVip\">\n            <image :src=\"userVipInfo.vipIcon\"></image>\n          </view>\n        </view>\n        <!-- <view class=\"tag df\">\n          <image :src=\"genderIcon\" style=\"width:100%;height:100%;object-fit:cover;\"></image>\n          <text v-if=\"displayState.showAge\">{{item.age}}</text>\n        </view> -->\n        <!-- 关注按钮 -->\n        <view :class=\"followButtonState.className\" @tap.stop=\"followUser\" v-if=\"followButtonState.show\">\n          {{followButtonState.text}}\n        </view>\n      </view>\n      \n      <!-- 时间和浏览信息 -->\n      <view class=\"gg-item-time\">{{timeDisplay}} · {{item.user_info.residence_name || ''}} ·  浏览 {{viewsCount}}</view>\n      \n      <!-- 文本内容 -->\n      <view :class=\"displayState.contentClass\" @tap.stop=\"toDetailsPage\">{{item.content}}</view>\n      <!-- 媒体内容 -->\n      <view v-if=\"mediaState.showMedia\" class=\"gg-item-file\">\n        <!-- 单图模式 -->\n        <view v-if=\"mediaState.isSingleImage\" :class=\"mediaState.singleImageClass\" @tap.stop=\"toVideoPage\">\n          <lazyImage :src=\"getImages[0]\"></lazyImage>\n        </view>\n\n        <!-- 多图模式 -->\n        <block v-if=\"mediaState.isMultiImage\">\n          <view v-for=\"(img, index) in getImages\" :key=\"index\" class=\"file-img\" @tap.stop=\"toVideoPage\">\n            <lazyImage :src=\"img\"></lazyImage>\n          </view>\n          <view v-if=\"mediaState.showImageCount\" class=\"file-count df\">\n            <image src=\"/static/img/i.png\"></image>\n            <text>{{getImagesCount}}</text>\n          </view>\n        </block>\n\n        <!-- 视频模式 -->\n        <block v-if=\"mediaState.isVideo\">\n          <view :class=\"mediaState.videoContainerClass\" @tap.stop=\"toVideoPage\">\n            <lazyImage :src=\"videoCoverSrc\"></lazyImage>\n          </view>\n          <view class=\"file-video df\" :style=\"mediaState.videoIconStyle\">\n            <image src=\"/static/img/v.png\"></image>\n          </view>\n        </block>\n\n        <!-- 音频模式 -->\n        <view v-if=\"mediaState.isAudio\" class=\"file-audio df\" @tap.stop=\"toVideoPage\">\n          <image class=\"audio-bg\" style=\"z-index:-2\" :src=\"audioCoverSrc\"></image>\n          <view class=\"audio-mb\"></view>\n          <view class=\"audio-left\">\n            <lazyImage :src=\"audioCoverSrc\"></lazyImage>\n            <image class=\"icon\" src=\"/static/img/yw.png\"></image>\n          </view>\n          <view style=\"width:calc(100% - 300rpx)\">\n            <view class=\"audio-t1\">{{audioInfo.title}}</view>\n            <view class=\"audio-t2 ohto\">{{audioInfo.intro}}</view>\n          </view>\n          <view class=\"audio-play\">去播放</view>\n        </view>\n      </view>\n\n      <!-- 关联信息 -->\n      <view v-if=\"displayState.showExtraInfo\" class=\"gg-item-g\">\n        <!-- 位置信息 -->\n        <view v-if=\"contentChecks.hasLocation\" class=\"g-item df\" @tap.stop=\"opLocationClick\">\n          <image class=\"g-item-icon\" style=\"width:30rpx;height:30rpx\" src=\"/static/img/wz.png\"></image>\n          <text style=\"padding:0 8rpx;color:#999\">{{locationName}}</text>\n        </view>\n        <!-- 圈子信息 -->\n        <view v-if=\"contentChecks.hasCircle\" class=\"g-item df\" style=\"border-radius:40rpx\" data-type=\"7\" @tap.stop=\"toPaths\">\n          <view class=\"g-item-img\" style=\"border-radius:50%\">\n            <image :src=\"circleInfo.avatar\" style=\"width:100%;height:100%;object-fit:cover;\"></image>\n          </view>\n          <text style=\"padding:0 8rpx 0 12rpx\">{{circleInfo.name}}</text>\n        </view>\n        <!-- 话题信息 -->\n        <view v-if=\"contentChecks.hasTopic\" class=\"g-item df\" style=\"border-radius:40rpx\" data-type=\"2\" @tap.stop=\"toPaths\">\n          <view class=\"g-item-img\" style=\"border-radius:50%\">\n            <image :src=\"(topicsList[0] && topicsList[0].icon) ? topicsList[0].icon : '/static/img/topic_icon.png'\" style=\"width:100%;height:100%;object-fit:cover;\"></image>\n          </view>\n          <text style=\"padding:0 8rpx 0 12rpx\">{{topicTitle(topicsList[0])}}</text>\n        </view>\n\n        <!-- 活动信息 -->\n        <view v-if=\"contentChecks.hasActivity\" class=\"g-item df\" data-type=\"6\" @tap.stop=\"toPaths\">\n          <view class=\"g-item-img\" style=\"width:50rpx\">\n            <lazyImage :src=\"activityImage\"></lazyImage>\n          </view>\n          <text style=\"padding:0 8rpx 0 12rpx\">{{activityName}}</text>\n        </view>\n\n        <!-- 商品信息 -->\n        <view v-if=\"contentChecks.hasProduct\" class=\"g-item df\" :data-id=\"productId\" data-type=\"3\" @tap.stop=\"toPaths\">\n            <view class=\"g-item-img\">\n            <lazyImage :src=\"productImage\"></lazyImage>\n          </view>\n          <text style=\"padding:0 8rpx 0 12rpx\">{{productName}}</text>\n        </view>\n      </view>\n      \n      <!-- 投票展示区 -->\n      <VoteComponent\n        v-if=\"item.vote_info\"\n        :voteInfo=\"item.vote_info\"\n        @vote-success=\"handleVoteSuccess\"\n      />\n\n      <!-- 评论预览 -->\n      <view v-if=\"contentChecks.hasLatestComments\" class=\"gg-item-comment\" @tap.stop=\"toPaths\" data-type=\"4\">\n        <view class=\"ohto2\">\n          <text style=\"color:#000\">{{commentInfo.latestUser}}：</text>{{commentInfo.latestContent}}\n        </view>\n      </view>\n      \n      <!-- 底部互动栏 -->\n      <view class=\"gg-item-unm df\">\n        <!-- 评论按钮 -->\n        <view class=\"unm-item\" @tap.stop=\"toPaths\" data-type=\"4\">\n          <image src=\"/static/img/pl.png\"></image>\n          <text>{{commentCount}} 评论</text>\n        </view>\n        \n        <!-- 点赞按钮 -->\n        <view class=\"unm-item\" @tap.stop=\"handleLike\">\n          <image v-if=\"isLiked\" class=\"hi\" src=\"/static/img/dz1.png\"></image>\n          <image v-else class=\"hi\" src=\"/static/img/dz.png\"></image>\n          <text :style=\"likeState.textStyle\">\n            {{likeState.countText}} 赞\n          </text>\n        </view>\n        \n        <!-- 分享按钮 -->\n        <view class=\"unm-item\" @tap.stop=\"toPaths\" data-type=\"5\" style=\"margin-left:auto\">\n          <image src=\"/static/img/fx.png\"></image>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport lazyImage from '@/components/lazyImage/lazyImage'\nimport VoteComponent from '@/components/vote-component/vote-component.vue'\nimport { likeDynamic, followUser } from '@/api/social.js'\n\n// 常量定义\nconst DYNAMIC_TYPES = {\n  TEXT: 1,\n  IMAGE: 2,\n  VIDEO: 3,\n  AUDIO: 4\n}\n\nconst NAVIGATION_TYPES = {\n  USER_PROFILE: 1,\n  TOPIC: 2,\n  PRODUCT: 3,\n  COMMENT: 4,\n  SHARE: 5,\n  ACTIVITY: 6,\n  CIRCLE: 7\n}\n\nconst DEBOUNCE_DELAYS = {\n  LIKE: 300,\n  FOLLOW: 500\n}\n\nexport default {\n  name: 'card-gg',\n  components: {\n    lazyImage,\n    VoteComponent\n  },\n  props: {\n    item: {\n      type: Object,\n      default: () => ({})\n    },\n    idx: {\n      type: Number,\n      default: 0\n    }\n  },\n  data() {\n    return {\n      isNoteVideo: (typeof getApp === 'function' && getApp() && getApp().globalData && getApp().globalData.isNoteVideo) || true,\n      userId: (uni.getStorageSync('userInfo') && uni.getStorageSync('userInfo').uid) || 0,\n      isLiked: false,\n      likeCount: 0,\n      isProcessing: false, // 防止重复点击\n    }\n  },\n  created() {\n    // 创建防抖函数实例\n    this.debouncedHandleLike = this.debounce(this.handleLikeAction, DEBOUNCE_DELAYS.LIKE);\n    this.debouncedFollowUser = this.debounce(this.followUserAction, DEBOUNCE_DELAYS.FOLLOW);\n\n    // 预加载关键图片\n    this.preloadImages();\n  },\n  mounted() {\n  },\n  computed: {\n    // 基础数据获取 - 统一处理多种数据结构\n    itemData() {\n      // 合并用户信息，优先使用user_info中的VIP信息\n      const userInfo = this.item.user_info || {};\n      const userBase = this.item.user || {};\n      const mergedUser = { ...userBase, ...userInfo };\n\n      return {\n        // 用户信息 - 合并user和user_info，确保VIP信息不丢失\n        user: mergedUser,\n        // 商品信息\n        product: this.item.goods_info || this.item.product_info || {},\n        // 活动信息\n        activity: this.item.activity || {},\n        // 话题信息\n        topics: this.item.topic_info || this.item.topics || [],\n        // 关注状态\n        followStatus: {\n          isFollow: this.item.is_follow === 1 || this.item.user_info?.is_follow === 1,\n          isMutual: this.item.is_mutual_follow === 1 || this.item.user_info?.is_mutual_follow === 1\n        }\n      };\n    },\n\n    // 性别图标\n    genderIcon() {\n      return this.item.gender == 0 ? '/static/img/nv.png' : '/static/img/nan.png';\n    },\n\n    // 视频封面\n    videoCoverSrc() {\n      return this.item.video_cover || this.item.video?.cover || '';\n    },\n\n    // 音频封面\n    audioCoverSrc() {\n      return this.item.audio_cover || this.item.audio?.cover || '/static/img/audio_cover.png';\n    },\n\n    // 用户头像\n    userAvatar() {\n      return this.itemData.user.avatar || this.item.avatar || '/static/img/avatar.png';\n    },\n\n    // 用户名\n    userName() {\n      return this.itemData.user.name || this.itemData.user.nickname || this.item.nickname || '用户';\n    },\n\n    // 用户VIP信息\n    userVipInfo() {\n      const user = this.itemData.user;\n\n      // 参考center.vue的VIP判断逻辑：userInfo.is_money_level> 0 && userInfo.svip_open\n      const isMoneyLevel = user.is_money_level > 0 || user.is_ever_level > 0; // 付费会员或永久会员\n      const svipOpen = user.svip_open > 0 || user.vip === true; // SVIP开启或VIP为true\n      const showVipByCenter = isMoneyLevel && svipOpen;\n\n      // 同时检查后端返回的VIP信息\n      const hasVip = user.vip || false;\n      const vipStatus = user.vip_status || 2;\n      const isValidVip = hasVip && (vipStatus === 1 || vipStatus === 3); // 永久会员或未到期会员\n\n      // 使用任一条件满足即显示VIP\n      const finalShowVip = showVipByCenter || isValidVip;\n      return {\n        showVip: finalShowVip,\n        vipIcon: '/static/img/svip.gif', // 与center.vue保持一致，使用固定图标\n        vipName: user.vip_name || '',\n        vipStatus: vipStatus\n      };\n    },\n\n    // 位置名称\n    locationName() {\n      return this.item.location_name || this.item.adds_name || '';\n    },\n\n    // 话题列表\n    topicsList() {\n      return this.itemData.topics;\n    },\n\n    // 商品信息\n    productId() {\n      return this.itemData.product.id || '';\n    },\n    productImage() {\n      return this.itemData.product.image || '';\n    },\n    productName() {\n      return this.itemData.product.store_name || '';\n    },\n\n    // 活动信息\n    activityImage() {\n      return this.item.activity_img || this.itemData.activity.img || '/static/img/activity.png';\n    },\n    activityName() {\n      return this.item.activity_name || this.itemData.activity.name || '活动详情';\n    },\n\n    // 获取图片数组\n    getImages() {\n      // 处理imgs字段\n      if (this.item.imgs?.length) {\n        return this.item.imgs.map(img => typeof img === 'string' ? img : img.url);\n      }\n      \n      // 处理images字段\n      if (this.item.images) {\n        if (Array.isArray(this.item.images)) {\n          return this.item.images;\n        }\n        \n        if (typeof this.item.images === 'string') {\n          try {\n            return JSON.parse(this.item.images);\n          } catch (e) {\n            return [this.item.images];\n          }\n        }\n      }\n      \n      // 处理单个image字段\n      if (this.item.image) {\n        return [this.item.image];\n      }\n      \n      return [];\n    },\n    \n    // 获取图片数量\n    getImagesCount() {\n      return this.getImages.length;\n    },\n    \n    // 内容检查 - 统一处理各种内容类型的存在性判断\n    contentChecks() {\n      return {\n        hasLocation: !!(this.item.location_name || this.item.adds_name),\n        hasTopic: this.itemData.topics.length > 0,\n        hasProduct: !!this.itemData.product.id,\n        hasActivity: !!(this.item.activity_id || this.itemData.activity.id),\n        hasCircle: this.circleInfo.id > 0,\n        hasLatestComments: this.item.latest_comments?.length > 0\n      };\n    },\n\n    // 圈子信息 - 统一处理圈子相关数据\n    circleInfo() {\n      const circleData = this.item.circle_info || {};\n      return {\n        id: parseInt(circleData.circle_id || this.item.circle_id || 0),\n        name: circleData.circle_name || this.item.circle_name || '',\n        avatar: circleData.circle_avatar || this.item.circle_avatar || '/static/img/qz1.png'\n      };\n    },\n\n    // 移除冗余的计算属性，直接在模板中使用 contentChecks 和 circleInfo\n    \n    // 获取视频宽高比\n    getVideoRatio() {\n      if (this.item.video_width && this.item.video_height) {\n        return this.item.video_width / this.item.video_height;\n      }\n      \n      if (this.item.video?.wide && this.item.video?.high) {\n        return this.item.video.wide / this.item.video.high;\n      }\n      \n      return 1;\n    },\n    \n    // 获取图片宽高比\n    getImageRatio() {\n      if (this.item.image_width && this.item.image_height) {\n        return this.item.image_width / this.item.image_height;\n      }\n      \n      if (this.getImages.length > 0 && this.item.imgs?.[0]?.wide && this.item.imgs?.[0]?.high) {\n        return this.item.imgs[0].wide / this.item.imgs[0].high;\n      }\n      \n      return 1;\n    },\n    \n\n    \n    // 关注按钮状态 - 统一处理关注相关逻辑\n    followButtonState() {\n      const { isFollow, isMutual } = this.itemData.followStatus;\n      const isFollowed = isFollow || isMutual;\n\n      return {\n        show: this.item.uid != this.userId,\n        isMutual,\n        isFollowed,\n        className: ['follow-btn', isMutual ? 'mutual' : (isFollowed ? 'active' : '')],\n        text: isMutual ? '互相关注' : (isFollowed ? '已关注' : '＋关注')\n      };\n    },\n\n    // 移除冗余的计算属性，直接在模板中使用 followButtonState\n    \n    // 媒体内容状态 - 统一处理媒体相关判断\n    mediaState() {\n      const type = this.item.type;\n      const imageCount = this.getImagesCount;\n      const videoRatio = this.getVideoRatio;\n      const imageRatio = this.getImageRatio;\n\n      return {\n        showMedia: type > DYNAMIC_TYPES.TEXT || (this.item.images?.length > 0),\n        isSingleImage: type === DYNAMIC_TYPES.IMAGE && imageCount === 1,\n        isMultiImage: type === DYNAMIC_TYPES.IMAGE && imageCount > 1,\n        isVideo: type === DYNAMIC_TYPES.VIDEO,\n        isAudio: type === DYNAMIC_TYPES.AUDIO,\n        showImageCount: imageCount > 3,\n        singleImageClass: ['file-h', imageRatio > 1 ? 'file-w' : ''],\n        videoContainerClass: ['file-h', videoRatio > 1 ? 'file-w' : ''],\n        videoIconStyle: { 'left': videoRatio > 1 ? '382rpx' : '282rpx' }\n      };\n    },\n\n    // 显示状态 - 统一处理显示相关判断\n    displayState() {\n      const checks = this.contentChecks;\n\n      // 多重检查认证状态，确保兼容不同数据结构\n      const authStatus = this.itemData.user.auth_status ||\n                        this.item.user_info?.auth_status ||\n                        this.item.user?.auth_status ||\n                        0;\n\n      const isAuthenticated = authStatus === 2;\n\n      return {\n        contentClass: ['gg-item-content', 'ohto2', (this.item.type === DYNAMIC_TYPES.TEXT && !this.item.images) ? 'wlc8' : ''],\n        showExtraInfo: checks.hasLocation || checks.hasTopic || checks.hasProduct || checks.hasCircle || this.item.activity_id,\n        showAge: this.item.age && this.item.age !== '暂不展示',\n        isAuthenticated: isAuthenticated\n      };\n    },\n\n    // 音频信息\n    audioInfo() {\n      return {\n        title: this.item.audio_title || this.item.audio?.name || '音频',\n        intro: this.item.audio?.intro || '点击播放音频'\n      };\n    },\n\n    // 移除冗余的计算属性，直接在模板中使用对应的状态对象\n    \n    // 评论相关信息\n    commentInfo() {\n      const latestComment = this.item.latest_comments?.[0];\n      return {\n        userName: this.item.comment?.user_name || this.item.comment?.user?.name || '',\n        latestUser: latestComment?.nickname || '匿名用户',\n        latestContent: latestComment?.content || '无内容'\n      };\n    },\n\n    // 点赞相关状态\n    likeState() {\n      const count = this.likeCount || '';\n      return {\n        textStyle: { 'color': this.isLiked ? '#FA5150' : '#999' },\n        countText: this.formatNumber(count, 10000, 'w')\n      };\n    },\n\n\n\n\n\n    // 保留常用的显示数据计算属性\n    viewsCount() { return this.item.browse || this.item.views || 0; },\n    timeDisplay() { return this.item.create_time_str || this.item.create_time || '刚刚'; },\n    commentCount() { return this.item.comment_count || this.item.comments || ''; }\n  },\n  watch: {\n    item: {\n      handler(newVal) {\n        if (newVal) {\n          const newIsLiked = newVal.is_like === 1;\n          const newLikeCount = parseInt(newVal.likes || 0);\n\n          // 只有当状态真正改变时才更新\n          if (this.isLiked !== newIsLiked || this.likeCount !== newLikeCount) {\n            this.isLiked = newIsLiked;\n            this.likeCount = newLikeCount;\n          }\n        }\n      },\n      immediate: true,\n      deep: true\n    },\n\n    // 监听 isLiked 变化，确保视图更新（添加防抖避免无限循环）\n    isLiked: {\n      handler(newVal, oldVal) {\n        // 只有当值真正改变时才更新视图，避免无限循环\n        if (newVal !== oldVal) {\n          this.$nextTick(() => {\n            this.$forceUpdate();\n          });\n        }\n      }\n    }\n  },\n  methods: {\n    // 防抖工具函数\n    debounce(func, wait) {\n      let timeout;\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout);\n          func.apply(this, args);\n        };\n        clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n      };\n    },\n\n    // 通用数字格式化方法\n    formatNumber(num, threshold = 10000, suffix = 'w') {\n      if (!num || num < threshold) return num.toString() || '';\n      return (num / threshold).toFixed(1) + suffix;\n    },\n\n    // 图片预加载优化\n    preloadImages() {\n      try {\n        // 预加载前3张图片\n        const imagesToPreload = this.getImages.slice(0, 3);\n        imagesToPreload.forEach(src => {\n          if (src) {\n            // 使用 uni.preloadPage 或简单的图片预加载\n            const img = new Image();\n            img.src = src;\n          }\n        });\n\n        // 预加载用户头像\n        if (this.userAvatar) {\n          const avatarImg = new Image();\n          avatarImg.src = this.userAvatar;\n        }\n      } catch (e) {\n      }\n    },\n\n    // 安全获取全局数据\n    getGlobalData(key, defaultValue) {\n      try {\n        if (typeof getApp !== 'function') return defaultValue;\n        const app = getApp();\n        if (!app || !app.globalData) return defaultValue;\n        return app.globalData[key] !== undefined ? app.globalData[key] : defaultValue;\n      } catch (e) {\n        return defaultValue;\n      }\n    },\n    \n    // 统一错误处理\n    handleError(error, defaultMessage = '操作失败') {\n      const message = error?.message || error?.msg || defaultMessage;\n      uni.showToast({\n        title: message,\n        icon: 'none',\n        duration: 2000\n      });\n    },\n\n    // 认证图标加载错误处理\n    onAuthIconError() {\n    },\n\n    // 获取话题标题\n    topicTitle(topic) {\n      if (!topic) return '';\n      return topic.title || topic.name || '';\n    },\n\n    // 获取笔记URL - 统一处理不同类型笔记的URL生成\n    getNoteUrl(params = '') {\n      const isMediaType = this.item.type == 2 || this.item.type == 3 || this.item.type == 4;\n      const basePage = isMediaType ? 'note/video' : 'note/details';\n      return `${basePage}?id=${this.item.id}${params}`;\n    },\n    \n    /**\n     * 处理点赞事件 - 防抖包装器\n     */\n    handleLike() {\n      this.debouncedHandleLike();\n    },\n\n    /**\n     * 实际的点赞处理逻辑\n     */\n    handleLikeAction() {\n      // 判断用户是否登录\n      if (!this.$store.getters.isLogin) {\n        uni.navigateTo({\n          url: '/pages/login/index'\n        });\n        return;\n      }\n      \n      // 防止重复点击\n      if (this.isProcessing) return;\n      this.isProcessing = true;\n      \n      const newLikeStatus = !this.isLiked;\n      const oldLikeCount = this.likeCount;\n      \n      // 先更新UI，提高用户体验\n      this.isLiked = newLikeStatus;\n      this.likeCount = newLikeStatus ? oldLikeCount + 1 : Math.max(0, oldLikeCount - 1);\n      \n      // 调用点赞API\n      likeDynamic({\n        id: this.item.id,\n        is_like: newLikeStatus ? 1 : 0\n      }).then(() => {\n        // 点赞成功，通知父组件\n        this.$emit('likeback', {\n          index: this.idx,\n          id: this.item.id,\n          isLike: newLikeStatus\n        });\n      }).catch((error) => {\n        // 点赞失败，恢复原状态\n        this.isLiked = !newLikeStatus;\n        this.likeCount = oldLikeCount;\n        this.handleError(error, '点赞失败，请重试');\n      }).finally(() => {\n        this.isProcessing = false;\n      });\n    },\n    \n    /**\n     * 处理不同类型的页面导航\n     * @param {Object} e - 点击事件对象\n     */\n    toPaths(e) {\n      // 防御性检查 - 确保item存在\n      if (!this.item || !this.item.id) return;\n      \n      // 从事件对象中获取导航类型\n      let type = e && e.currentTarget ? parseInt(e.currentTarget.dataset.type) : undefined;\n\n      // 根据内容类型决定跳转页面\n      let url;\n\n      // 根据不同的导航类型设置不同的URL\n      if (type === NAVIGATION_TYPES.USER_PROFILE) {\n        // 类型1: 导航到用户详情页\n        if (this.item.uid) {\n          url = 'user/details?id=' + this.item.uid;\n        }\n      } else if (type === NAVIGATION_TYPES.TOPIC) {\n        // 类型2: 话题详情页\n        if (this.topicsList && this.topicsList.length > 0) {\n          const topic = this.topicsList[0];\n          const topicId = topic && topic.id ? topic.id : '';\n          url = 'topic/details?id=' + topicId;\n        }\n      } else if (type === NAVIGATION_TYPES.CIRCLE) {\n        // 类型7: 圈子详情页\n        if (this.contentChecks.hasCircle) {\n          url = 'note/circle?id=' + this.circleInfo.id;\n        }\n      } else if (type === NAVIGATION_TYPES.PRODUCT) {\n        // 类型3: 导航到商品详情页，需要从事件中获取商品ID\n        let goodsId = e && e.currentTarget && e.currentTarget.dataset ? e.currentTarget.dataset.id : '';\n        if (goodsId) {\n          url = 'goods/details?id=' + goodsId;\n        }\n      } else if (type === NAVIGATION_TYPES.COMMENT) {\n        // 类型4: 导航到笔记详情并打开评论区\n        url = this.getNoteUrl('&comment=true');\n      } else if (type === NAVIGATION_TYPES.SHARE) {\n        // 类型5: 导航到笔记详情并打开分享弹窗\n        url = this.getNoteUrl('&share=true');\n      } else if (type === NAVIGATION_TYPES.ACTIVITY) {\n        // 类型6: 导航到活动详情页\n        const activityId = this.item.activity_id || this.itemData.activity.id;\n        if (activityId) {\n          url = 'activity/details?id=' + activityId;\n        }\n      } else {\n        // 默认导航 - 根据内容类型决定\n        url = this.getNoteUrl();\n      }\n      \n      // 安全地进行导航\n      if (url) {\n        try {\n          uni.navigateTo({\n            url: '/pages/' + url,\n            fail: (err) => {\n              console.error('页面导航失败:', err);\n              // 尝试使用switchTab作为备选方案\n              uni.switchTab({\n                url: '/pages/index/index',\n                fail: () => {\n                  console.error('无法导航到页面');\n                }\n              });\n            }\n          });\n        } catch (e) {\n          console.error('导航异常:', e);\n        }\n      }\n    },\n    \n    // 打开位置\n    opLocationClick() {\n      // 防御性检查\n      if (!this.item) return;\n      \n      // 获取纬度\n      let latitude = 0;\n      try {\n        if (this.item.latitude) {\n          latitude = parseFloat(this.item.latitude);\n        } else if (this.item.lat) {\n          latitude = parseFloat(this.item.lat);\n        }\n        \n        // 确保latitude是有效数字\n        if (isNaN(latitude)) latitude = 0;\n      } catch (e) {\n        console.error('解析纬度失败:', e);\n        latitude = 0;\n      }\n      \n      // 获取经度\n      let longitude = 0;\n      try {\n        if (this.item.longitude) {\n          longitude = parseFloat(this.item.longitude);\n        } else if (this.item.lng) {\n          longitude = parseFloat(this.item.lng);\n        }\n        \n        // 确保longitude是有效数字\n        if (isNaN(longitude)) longitude = 0;\n      } catch (e) {\n        console.error('解析经度失败:', e);\n        longitude = 0;\n      }\n      \n      // 如果经纬度都是0，则无法打开位置\n      if (latitude === 0 && longitude === 0) {\n        uni.showToast({\n          title: '位置信息不完整',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 安全地打开位置\n      try {\n        uni.openLocation({\n          latitude: latitude,\n          longitude: longitude,\n          name: this.locationName || '位置',\n          address: this.locationName || '位置',\n          fail: (err) => {\n            console.error('打开位置失败', err);\n            uni.showToast({\n              title: '无法打开位置信息',\n              icon: 'none'\n            });\n          }\n        });\n      } catch (e) {\n        console.error('打开位置异常:', e);\n        uni.showToast({\n          title: '无法打开位置信息',\n          icon: 'none'\n        });\n      }\n    },\n    \n    // 关注用户 - 防抖包装器\n    followUser() {\n      this.debouncedFollowUser();\n    },\n\n    // 实际的关注处理逻辑\n    followUserAction() {\n      // 防御性检查\n      if (!this.item || !this.item.uid) {\n        return;\n      }\n      \n      // 判断用户是否登录\n      if (!this.$store.getters.isLogin) {\n        uni.navigateTo({\n          url: '/pages/login/index'\n        });\n        return;\n      }\n      \n      // 防止重复点击\n      if (this.isProcessing) return;\n      this.isProcessing = true;\n      \n      // 获取目标用户ID，确保是整数类型\n      const targetUserId = parseInt(this.item.uid);\n      \n      // 验证用户ID\n      if (!targetUserId || targetUserId <= 0) {\n        uni.showToast({\n          title: \"获取用户ID失败\",\n          icon: \"none\"\n        });\n        this.isProcessing = false;\n        return;\n      }\n      \n      // 当前的关注状态 - 使用统一的状态管理\n      const { isFollow, isMutual } = this.itemData.followStatus;\n      const isFollowed = isFollow || isMutual;\n      \n      // 准备请求参数\n      const params = {\n        follow_uid: targetUserId, // 目标用户ID\n        is_follow: isFollowed ? 0 : 1 // 1:关注 0:取消关注\n      };\n      \n      // 先更新本地UI状态，提升用户体验\n      // Vue3兼容：直接赋值替代$set\n      if (isFollowed) {\n        // 取消关注\n        this.item.is_follow = 0;\n        this.item.is_mutual_follow = 0;\n\n        // 如果存在user_info，也需要更新\n        if (this.item.user_info) {\n          this.item.user_info.is_follow = 0;\n          this.item.user_info.is_mutual_follow = 0;\n        }\n      } else {\n        // 添加关注\n        this.item.is_follow = 1;\n        // 互相关注状态需要等待服务器返回\n\n        // 如果存在user_info，也需要更新\n        if (this.item.user_info) {\n          this.item.user_info.is_follow = 1;\n        }\n      }\n      \n      // 显示加载提示 - 使用较短的加载时间，增强用户体验\n      uni.showToast({\n        title: isFollowed ? \"取消关注中...\" : \"关注中...\",\n        icon: \"none\",\n        duration: 500\n      });\n      \n      // 调用关注/取消关注API\n      followUser(params).then(res => {\n        if (res.status === 200) {\n          // 处理互相关注状态\n          if (res.data && res.data.is_mutual !== undefined) {\n            const isMutual = res.data.is_mutual === 1;\n            \n            // Vue3兼容：直接赋值替代$set\n            this.item.is_mutual_follow = isMutual ? 1 : 0;\n\n            // 如果存在user_info，也需要更新\n            if (this.item.user_info) {\n              this.item.user_info.is_mutual_follow = isMutual ? 1 : 0;\n            }\n          }\n          \n          // 向父组件发送关注状态更新事件\n          this.$emit('followback', {\n            idx: this.idx,\n            uid: this.item.uid,\n            is_follow: isFollowed ? 0 : 1,\n            is_mutual: res.data?.is_mutual || 0\n          });\n          \n          // 显示操作结果提示\n          uni.showToast({\n            title: isFollowed ? \"已取消关注\" : \"关注成功\",\n            icon: \"none\",\n            duration: 1500\n          });\n        } else {\n          // 错误处理：恢复原始状态 - Vue3兼容：直接赋值替代$set\n          if (isFollowed) {\n            // 恢复为已关注状态\n            this.item.is_follow = 1;\n            if (isMutual) {\n              this.item.is_mutual_follow = 1;\n            }\n\n            // 如果存在user_info，也需要恢复\n            if (this.item.user_info) {\n              this.item.user_info.is_follow = 1;\n              if (isMutual) {\n                this.item.user_info.is_mutual_follow = 1;\n              }\n            }\n          } else {\n            // 恢复为未关注状态\n            this.item.is_follow = 0;\n            this.item.is_mutual_follow = 0;\n\n            // 如果存在user_info，也需要恢复\n            if (this.item.user_info) {\n              this.item.user_info.is_follow = 0;\n              this.item.user_info.is_mutual_follow = 0;\n            }\n          }\n          \n          // 显示错误提示\n          uni.showToast({\n            title: res.msg || \"操作失败，请重试\",\n            icon: \"none\"\n          });\n        }\n      }).catch((error) => {\n        // 错误处理：恢复原始状态 - Vue3兼容：直接赋值替代$set\n        if (isFollowed) {\n          // 恢复为已关注状态\n          this.item.is_follow = 1;\n          if (isMutual) {\n            this.item.is_mutual_follow = 1;\n          }\n\n          // 如果存在user_info，也需要恢复\n          if (this.item.user_info) {\n            this.item.user_info.is_follow = 1;\n            if (isMutual) {\n              this.item.user_info.is_mutual_follow = 1;\n            }\n          }\n        } else {\n          // 恢复为未关注状态\n          this.item.is_follow = 0;\n          this.item.is_mutual_follow = 0;\n\n          // 如果存在user_info，也需要恢复\n          if (this.item.user_info) {\n            this.item.user_info.is_follow = 0;\n            this.item.user_info.is_mutual_follow = 0;\n          }\n        }\n\n        // 使用统一错误处理\n        this.handleError(error, \"网络错误，请稍后重试\");\n      }).finally(() => {\n        this.isProcessing = false;\n      });\n    },\n    \n    // 跳转到视频页面\n    toVideoPage() {\n      this.navigateToPage(this.getNoteUrl());\n    },\n\n    // 跳转到详情页面\n    toDetailsPage() {\n      this.navigateToPage('note/details?id=' + this.item.id);\n    },\n\n    // 统一的页面导航方法\n    navigateToPage(url) {\n      // 防御性检查\n      if (!this.item || !this.item.id || !url) return;\n\n      try {\n        uni.navigateTo({\n          url: '/pages/' + url,\n          fail: (err) => {\n            console.error('页面导航失败:', err);\n            // 尝试使用switchTab作为备选方案\n            uni.switchTab({\n              url: '/pages/index/index',\n              fail: () => {\n                console.error('无法导航到页面');\n              }\n            });\n          }\n        });\n      } catch (e) {\n        console.error('导航异常:', e);\n      }\n    },\n\n    // 处理投票成功事件\n    handleVoteSuccess(data) {\n      // 更新本地数据\n      this.item.vote_info = data.voteInfo;\n\n      // 通知父组件\n      this.$emit('update', {\n        vote_info: data.voteInfo,\n        idx: this.idx\n      });\n    }\n  },\n  mounted() {\n    // 只有在类型未定义时才进行基本检查和设置\n    if (!this.item) return;\n    \n    if (this.item.type === undefined) {\n      if (this.item.video || this.item.video_url) {\n        this.$set(this.item, 'type', 3); // 视频类型\n      } else if (this.item.audio || this.item.audio_cover) {\n        this.$set(this.item, 'type', 4); // 音频类型\n      } else if (this.getImages && this.getImages.length > 0) {\n        this.$set(this.item, 'type', 2); // 图片类型\n      } else {\n        this.$set(this.item, 'type', 1); // 文本类型\n      }\n    }\n  },\n  beforeCreate() {\n    // 确保组件在初始化时item不为null\n    if (this.$options.propsData && !this.$options.propsData.item) {\n      this.$options.propsData.item = {};\n    }\n  },\n  errorCaptured(err, _, info) {\n    return false; // 阻止错误继续传播\n  }\n}\n</script>\n\n<style>\n.gg-box {\n  border-bottom: 1px solid #f8f8f8;\n  width: calc(100% - 60rpx);\n  padding: 30rpx;\n  display: flex;\n}\n\n.gg-box .gg-avatar {\n  width: 68rpx;\n  height: 68rpx;\n  border-radius: 50%;\n  border: 1px solid #f8f8f8;\n  position: relative;\n}\n\n.gg-avatar .top {\n  position: absolute;\n  right: -4rpx;\n  bottom: -4rpx;\n  width: 26rpx;\n  height: 26rpx;\n  border-radius: 50%;\n  justify-content: center;\n  background: #000;\n}\n\n.gg-avatar .top image {\n  width: 100%;\n  height: 100%;\n}\n\n.gg-box .gg-item {\n  width: calc(100% - 88rpx - 2px);\n  margin-left: 20rpx;\n}\n\n/* 用户名容器 */\n.gg-item .gg-item-user .name-container {\n  align-items: center;\n}\n\n.gg-item .gg-item-user .name {\n  color: #000;\n  font-size: 28rpx;\n  line-height: 34rpx;\n  font-weight: 700;\n}\n\n/* VIP图标样式 - 参考center.vue */\n.gg-item .gg-item-user .status-icon {\n  width: 70rpx;\n  height: 30rpx;\n  margin-right: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.gg-item .gg-item-user .vip-icon {\n  border-radius: 6rpx;\n  padding: 2rpx;\n}\n\n.gg-item .gg-item-user .vip-icon image {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n}\n\n.gg-item .gg-item-user .tag {\n  margin-left: 12rpx;\n  padding: 0 6rpx;\n  height: 34rpx;\n  border-radius: 4rpx;\n  background: #f5f5f5;\n}\n\n.gg-item-user .tag image {\n  margin: 0 3rpx;\n  width: 20rpx;\n  height: 20rpx;\n}\n\n.gg-item-user .tag text {\n  margin: 0 3rpx;\n  font-size: 18rpx;\n}\n\n.gg-item .gg-item-content {\n  margin-top: 12rpx;\n  font-size: 26rpx;\n  font-weight: 400;\n  line-height: 36rpx;\n  word-break: break-word;\n  white-space: pre-line;\n}\n\n.gg-item .gg-item-file {\n  margin-top: 20rpx;\n  display: flex;\n  position: relative;\n  z-index: 1;\n  flex-wrap: wrap;\n}\n\n.gg-item-file .file-h,\n.gg-item-file .file-w,\n.gg-item-file .file-img {\n  border-radius: 8rpx;\n  overflow: hidden;\n}\n\n.gg-item-file .file-h {\n  width: 320rpx;\n  height: 420rpx;\n}\n\n.gg-item-file .file-w {\n  width: 420rpx;\n  height: 320rpx;\n}\n\n.gg-item-file .file-img {\n  width: 196rpx;\n  height: 196rpx;\n  margin-right: 4rpx;\n  margin-bottom: 4rpx;\n}\n\n.gg-item-file .file-img:nth-child(3n) {\n  margin-right: 0rpx !important;\n}\n\n.gg-item-file .file-count {\n  position: absolute;\n  right: 20rpx;\n  bottom: 30rpx;\n  padding: 0 10rpx;\n  height: 40rpx;\n  color: #fff;\n  font-size: 20rpx;\n  font-weight: 700;\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 8rpx;\n}\n\n.gg-item-file .file-count image,\n.gg-item-file .file-video image {\n  width: 20rpx;\n  height: 20rpx;\n}\n\n.gg-item-file .file-count image {\n  margin-right: 10rpx;\n}\n\n.gg-item-file .file-video {\n  position: absolute;\n  top: 20rpx;\n  width: 48rpx;\n  height: 48rpx;\n  background: rgba(0, 0, 0, 0.6);\n  justify-content: center;\n  border-radius: 50%;\n}\n\n.gg-item-file .file-audio {\n  width: 100%;\n  height: 140rpx;\n  border-radius: 8rpx;\n  color: #fff;\n  position: relative;\n  overflow: hidden;\n}\n\n.file-audio .audio-left {\n  margin-right: 30rpx;\n  width: 140rpx;\n  height: 140rpx;\n  position: relative;\n}\n\n.file-audio .audio-left .icon {\n  position: absolute;\n  top: 45rpx;\n  right: 45rpx;\n  bottom: 45rpx;\n  left: 45rpx;\n  width: 50rpx;\n  height: 50rpx;\n}\n\n.file-audio .audio-bg,\n.file-audio .audio-mb {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n\n.file-audio .audio-mb {\n  z-index: -1;\n  -webkit-backdrop-filter: saturate(150%) blur(25px);\n  backdrop-filter: saturate(150%) blur(25px);\n  background: rgba(0, 0, 0, 0.5);\n}\n\n.file-audio .audio-t1 {\n  font-size: 26rpx;\n  font-weight: 700;\n}\n\n.file-audio .audio-t2 {\n  margin-top: 10rpx;\n  opacity: 0.8;\n  font-size: 20rpx;\n}\n\n.file-audio .audio-play {\n  margin: 0 30rpx;\n  width: 100rpx;\n  height: 60rpx;\n  line-height: 60rpx;\n  text-align: center;\n  font-size: 18rpx;\n  font-weight: 700;\n  background: rgba(255, 255, 255, 0.15);\n  border-radius: 60rpx;\n}\n\n.gg-item .gg-item-g {\n  margin-top: 10rpx;\n  width: 100%;\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.gg-item-g .g-item {\n  margin: 10rpx 10rpx 0 0;\n  padding: 8rpx;\n  font-size: 20rpx;\n  font-weight: 500;\n  border-radius: 8rpx;\n  background: #f8f8f8;\n}\n\n.g-item .g-item-img {\n  width: 40rpx;\n  height: 40rpx;\n  background: #f8f8f8;\n  border-radius: 4rpx;\n  overflow: hidden;\n}\n\n.gg-item .gg-item-time {\n  margin-top: 8rpx;\n  margin-bottom: 12rpx;\n  color: #999;\n  font-size: 20rpx;\n}\n\n.gg-item .gg-item-comment {\n  margin-top: 20rpx;\n  width: calc(100% - 40rpx);\n  padding: 20rpx;\n  color: #999;\n  font-size: 24rpx;\n  font-weight: 400;\n  word-break: break-word;\n  white-space: pre-line;\n  border-radius: 8rpx;\n  background: #f8f8f8;\n}\n\n.gg-item .gg-item-unm {\n  display: flex;\n  align-items: center;\n  width: 100%;\n}\n\n.gg-item-unm .unm-item {\n  margin-top: 30rpx;\n  display: flex;\n  align-items: center;\n}\n\n.gg-item-unm .unm-item image {\n  width: 44rpx;\n  height: 44rpx;\n}\n\n.gg-item-unm .unm-item text {\n  margin: 0 30rpx 0 6rpx;\n  color: #999;\n  font-size: 18rpx;\n  font-weight: 700;\n}\n\n.wlc8 {\n  -webkit-line-clamp: 8 !important;\n  line-clamp: 8 !important;\n}\n\n.df {\n  display: flex;\n  align-items: center;\n}\n\n.ohto {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.ohto2 {\n  word-break: break-all;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 4;\n  line-clamp: 4;\n  -webkit-box-orient: vertical;\n}\n\n/* 关注按钮样式 */\n.gg-item-user .follow-btn {\n  margin-left: auto;\n  padding: 0 20rpx;\n  height: 48rpx;\n  line-height: 48rpx;\n  border-radius: 8rpx;\n  font-size: 20rpx;\n  font-weight: 700;\n  color: #000;\n  background: #f8f8f8;\n  text-align: center;\n}\n\n.gg-item-user .follow-btn.active {\n  color: #999;\n  background: #f5f5f5;\n}\n\n.gg-item-user .follow-btn.mutual {\n  color: #576b95;\n  background: rgba(87, 107, 149, 0.1);\n}\n\n/* 移除原有 .topic-tag 样式，话题和圈子统一用 g-item/g-item-img/text 结构 */\n.topic-tag,\n.topic-tag text {\n  /* 移除样式 */\n  display: none !important;\n}\n\n/* 评论样式 */\n.gg-item .gg-item-comments {\n  margin-top: 20rpx;\n  width: calc(100% - 40rpx);\n  padding: 20rpx;\n  font-size: 24rpx;\n  border-radius: 8rpx;\n  background: #f8f8f8;\n}\n\n.gg-item-comments .comment-item {\n  margin-bottom: 15rpx;\n  padding-bottom: 15rpx;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\n}\n\n.gg-item-comments .comment-item:last-child {\n  margin-bottom: 0;\n  padding-bottom: 0;\n  border-bottom: none;\n}\n\n.gg-item-comments .comment-user {\n  font-size: 24rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 6rpx;\n}\n\n.gg-item-comments .comment-content {\n  font-size: 24rpx;\n  color: #666;\n  line-height: 32rpx;\n  word-break: break-all;\n}\n\n.gg-item-comments .comment-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 10rpx;\n}\n\n.gg-item-comments .comment-time {\n  font-size: 20rpx;\n  color: #999;\n}\n\n.gg-item-comments .comment-like {\n  display: flex;\n  align-items: center;\n}\n\n.gg-item-comments .comment-like image {\n  width: 28rpx;\n  height: 28rpx;\n  margin-right: 4rpx;\n}\n\n.gg-item-comments .comment-like text {\n  font-size: 20rpx;\n  color: #999;\n}\n\n.gg-item-comments .comment-like text.active {\n  color: #FA5150;\n}\n\n.gg-item-comments .more-comments {\n  margin-top: 15rpx;\n  text-align: center;\n  font-size: 22rpx;\n  color: #576b95;\n  padding: 10rpx 0;\n}\n\n\n\n</style> ", "import Component from 'D:/uniapp/vue3/components/card-gg/card-gg.vue'\nwx.createComponent(Component)"], "names": ["uni", "likeDynamic", "e", "followUser", "isMutual"], "mappings": ";;;;AAgKA,kBAAkB,MAAW;AAC7B,sBAAsB,MAAW;AAIjC,MAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACT;AAEA,MAAM,mBAAmB;AAAA,EACvB,cAAc;AAAA,EACd,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,OAAO;AAAA,EACP,UAAU;AAAA,EACV,QAAQ;AACV;AAEA,MAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,EACN,QAAQ;AACV;AAEA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,OAAO,CAAA;AAAA,IACjB;AAAA,IACD,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,aAAc,OAAO,WAAW,cAAc,YAAY,OAAQ,EAAC,cAAc,OAAM,EAAG,WAAW,eAAgB;AAAA,MACrH,QAASA,oBAAI,eAAe,UAAU,KAAKA,cAAG,MAAC,eAAe,UAAU,EAAE,OAAQ;AAAA,MAClF,SAAS;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA;AAAA,IAChB;AAAA,EACD;AAAA,EACD,UAAU;AAER,SAAK,sBAAsB,KAAK,SAAS,KAAK,kBAAkB,gBAAgB,IAAI;AACpF,SAAK,sBAAsB,KAAK,SAAS,KAAK,kBAAkB,gBAAgB,MAAM;AAGtF,SAAK,cAAa;AAAA,EACnB;AAAA,EACD,UAAU;AAAA,EACT;AAAA,EACD,UAAU;AAAA;AAAA,IAER,WAAW;;AAET,YAAM,WAAW,KAAK,KAAK,aAAa,CAAA;AACxC,YAAM,WAAW,KAAK,KAAK,QAAQ,CAAA;AACnC,YAAM,aAAa,EAAE,GAAG,UAAU,GAAG;AAErC,aAAO;AAAA;AAAA,QAEL,MAAM;AAAA;AAAA,QAEN,SAAS,KAAK,KAAK,cAAc,KAAK,KAAK,gBAAgB,CAAE;AAAA;AAAA,QAE7D,UAAU,KAAK,KAAK,YAAY,CAAE;AAAA;AAAA,QAElC,QAAQ,KAAK,KAAK,cAAc,KAAK,KAAK,UAAU,CAAE;AAAA;AAAA,QAEtD,cAAc;AAAA,UACZ,UAAU,KAAK,KAAK,cAAc,OAAK,UAAK,KAAK,cAAV,mBAAqB,eAAc;AAAA,UAC1E,UAAU,KAAK,KAAK,qBAAqB,OAAK,UAAK,KAAK,cAAV,mBAAqB,sBAAqB;AAAA,QAC1F;AAAA;IAEH;AAAA;AAAA,IAGD,aAAa;AACX,aAAO,KAAK,KAAK,UAAU,IAAI,uBAAuB;AAAA,IACvD;AAAA;AAAA,IAGD,gBAAgB;;AACd,aAAO,KAAK,KAAK,iBAAe,UAAK,KAAK,UAAV,mBAAiB,UAAS;AAAA,IAC3D;AAAA;AAAA,IAGD,gBAAgB;;AACd,aAAO,KAAK,KAAK,iBAAe,UAAK,KAAK,UAAV,mBAAiB,UAAS;AAAA,IAC3D;AAAA;AAAA,IAGD,aAAa;AACX,aAAO,KAAK,SAAS,KAAK,UAAU,KAAK,KAAK,UAAU;AAAA,IACzD;AAAA;AAAA,IAGD,WAAW;AACT,aAAO,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK,YAAY,KAAK,KAAK,YAAY;AAAA,IACxF;AAAA;AAAA,IAGD,cAAc;AACZ,YAAM,OAAO,KAAK,SAAS;AAG3B,YAAM,eAAe,KAAK,iBAAiB,KAAK,KAAK,gBAAgB;AACrE,YAAM,WAAW,KAAK,YAAY,KAAK,KAAK,QAAQ;AACpD,YAAM,kBAAkB,gBAAgB;AAGxC,YAAM,SAAS,KAAK,OAAO;AAC3B,YAAM,YAAY,KAAK,cAAc;AACrC,YAAM,aAAa,WAAW,cAAc,KAAK,cAAc;AAG/D,YAAM,eAAe,mBAAmB;AACxC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA;AAAA,QACT,SAAS,KAAK,YAAY;AAAA,QAC1B;AAAA;IAEH;AAAA;AAAA,IAGD,eAAe;AACb,aAAO,KAAK,KAAK,iBAAiB,KAAK,KAAK,aAAa;AAAA,IAC1D;AAAA;AAAA,IAGD,aAAa;AACX,aAAO,KAAK,SAAS;AAAA,IACtB;AAAA;AAAA,IAGD,YAAY;AACV,aAAO,KAAK,SAAS,QAAQ,MAAM;AAAA,IACpC;AAAA,IACD,eAAe;AACb,aAAO,KAAK,SAAS,QAAQ,SAAS;AAAA,IACvC;AAAA,IACD,cAAc;AACZ,aAAO,KAAK,SAAS,QAAQ,cAAc;AAAA,IAC5C;AAAA;AAAA,IAGD,gBAAgB;AACd,aAAO,KAAK,KAAK,gBAAgB,KAAK,SAAS,SAAS,OAAO;AAAA,IAChE;AAAA,IACD,eAAe;AACb,aAAO,KAAK,KAAK,iBAAiB,KAAK,SAAS,SAAS,QAAQ;AAAA,IAClE;AAAA;AAAA,IAGD,YAAY;;AAEV,WAAI,UAAK,KAAK,SAAV,mBAAgB,QAAQ;AAC1B,eAAO,KAAK,KAAK,KAAK,IAAI,SAAO,OAAO,QAAQ,WAAW,MAAM,IAAI,GAAG;AAAA,MAC1E;AAGA,UAAI,KAAK,KAAK,QAAQ;AACpB,YAAI,MAAM,QAAQ,KAAK,KAAK,MAAM,GAAG;AACnC,iBAAO,KAAK,KAAK;AAAA,QACnB;AAEA,YAAI,OAAO,KAAK,KAAK,WAAW,UAAU;AACxC,cAAI;AACF,mBAAO,KAAK,MAAM,KAAK,KAAK,MAAM;AAAA,UACpC,SAAS,GAAG;AACV,mBAAO,CAAC,KAAK,KAAK,MAAM;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAGA,UAAI,KAAK,KAAK,OAAO;AACnB,eAAO,CAAC,KAAK,KAAK,KAAK;AAAA,MACzB;AAEA,aAAO;IACR;AAAA;AAAA,IAGD,iBAAiB;AACf,aAAO,KAAK,UAAU;AAAA,IACvB;AAAA;AAAA,IAGD,gBAAgB;;AACd,aAAO;AAAA,QACL,aAAa,CAAC,EAAE,KAAK,KAAK,iBAAiB,KAAK,KAAK;AAAA,QACrD,UAAU,KAAK,SAAS,OAAO,SAAS;AAAA,QACxC,YAAY,CAAC,CAAC,KAAK,SAAS,QAAQ;AAAA,QACpC,aAAa,CAAC,EAAE,KAAK,KAAK,eAAe,KAAK,SAAS,SAAS;AAAA,QAChE,WAAW,KAAK,WAAW,KAAK;AAAA,QAChC,qBAAmB,UAAK,KAAK,oBAAV,mBAA2B,UAAS;AAAA;IAE1D;AAAA;AAAA,IAGD,aAAa;AACX,YAAM,aAAa,KAAK,KAAK,eAAe,CAAA;AAC5C,aAAO;AAAA,QACL,IAAI,SAAS,WAAW,aAAa,KAAK,KAAK,aAAa,CAAC;AAAA,QAC7D,MAAM,WAAW,eAAe,KAAK,KAAK,eAAe;AAAA,QACzD,QAAQ,WAAW,iBAAiB,KAAK,KAAK,iBAAiB;AAAA;IAElE;AAAA;AAAA;AAAA,IAKD,gBAAgB;;AACd,UAAI,KAAK,KAAK,eAAe,KAAK,KAAK,cAAc;AACnD,eAAO,KAAK,KAAK,cAAc,KAAK,KAAK;AAAA,MAC3C;AAEA,YAAI,UAAK,KAAK,UAAV,mBAAiB,WAAQ,UAAK,KAAK,UAAV,mBAAiB,OAAM;AAClD,eAAO,KAAK,KAAK,MAAM,OAAO,KAAK,KAAK,MAAM;AAAA,MAChD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,gBAAgB;;AACd,UAAI,KAAK,KAAK,eAAe,KAAK,KAAK,cAAc;AACnD,eAAO,KAAK,KAAK,cAAc,KAAK,KAAK;AAAA,MAC3C;AAEA,UAAI,KAAK,UAAU,SAAS,OAAK,gBAAK,KAAK,SAAV,mBAAiB,OAAjB,mBAAqB,WAAQ,gBAAK,KAAK,SAAV,mBAAiB,OAAjB,mBAAqB,OAAM;AACvF,eAAO,KAAK,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK,KAAK,KAAK,CAAC,EAAE;AAAA,MACpD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAKD,oBAAoB;AAClB,YAAM,EAAE,UAAU,SAAO,IAAM,KAAK,SAAS;AAC7C,YAAM,aAAa,YAAY;AAE/B,aAAO;AAAA,QACL,MAAM,KAAK,KAAK,OAAO,KAAK;AAAA,QAC5B;AAAA,QACA;AAAA,QACA,WAAW,CAAC,cAAc,WAAW,WAAY,aAAa,WAAW,EAAG;AAAA,QAC5E,MAAM,WAAW,SAAU,aAAa,QAAQ;AAAA;IAEnD;AAAA;AAAA;AAAA,IAKD,aAAa;;AACX,YAAM,OAAO,KAAK,KAAK;AACvB,YAAM,aAAa,KAAK;AACxB,YAAM,aAAa,KAAK;AACxB,YAAM,aAAa,KAAK;AAExB,aAAO;AAAA,QACL,WAAW,OAAO,cAAc,UAAS,UAAK,KAAK,WAAV,mBAAkB,UAAS;AAAA,QACpE,eAAe,SAAS,cAAc,SAAS,eAAe;AAAA,QAC9D,cAAc,SAAS,cAAc,SAAS,aAAa;AAAA,QAC3D,SAAS,SAAS,cAAc;AAAA,QAChC,SAAS,SAAS,cAAc;AAAA,QAChC,gBAAgB,aAAa;AAAA,QAC7B,kBAAkB,CAAC,UAAU,aAAa,IAAI,WAAW,EAAE;AAAA,QAC3D,qBAAqB,CAAC,UAAU,aAAa,IAAI,WAAW,EAAE;AAAA,QAC9D,gBAAgB,EAAE,QAAQ,aAAa,IAAI,WAAW,SAAS;AAAA;IAElE;AAAA;AAAA,IAGD,eAAe;;AACb,YAAM,SAAS,KAAK;AAGpB,YAAM,aAAa,KAAK,SAAS,KAAK,iBACpB,UAAK,KAAK,cAAV,mBAAqB,kBACrB,UAAK,KAAK,SAAV,mBAAgB,gBAChB;AAElB,YAAM,kBAAkB,eAAe;AAEvC,aAAO;AAAA,QACL,cAAc,CAAC,mBAAmB,SAAU,KAAK,KAAK,SAAS,cAAc,QAAQ,CAAC,KAAK,KAAK,SAAU,SAAS,EAAE;AAAA,QACrH,eAAe,OAAO,eAAe,OAAO,YAAY,OAAO,cAAc,OAAO,aAAa,KAAK,KAAK;AAAA,QAC3G,SAAS,KAAK,KAAK,OAAO,KAAK,KAAK,QAAQ;AAAA,QAC5C;AAAA;IAEH;AAAA;AAAA,IAGD,YAAY;;AACV,aAAO;AAAA,QACL,OAAO,KAAK,KAAK,iBAAe,UAAK,KAAK,UAAV,mBAAiB,SAAQ;AAAA,QACzD,SAAO,UAAK,KAAK,UAAV,mBAAiB,UAAS;AAAA;IAEpC;AAAA;AAAA;AAAA,IAKD,cAAc;;AACZ,YAAM,iBAAgB,UAAK,KAAK,oBAAV,mBAA4B;AAClD,aAAO;AAAA,QACL,YAAU,UAAK,KAAK,YAAV,mBAAmB,gBAAa,gBAAK,KAAK,YAAV,mBAAmB,SAAnB,mBAAyB,SAAQ;AAAA,QAC3E,aAAY,+CAAe,aAAY;AAAA,QACvC,gBAAe,+CAAe,YAAW;AAAA;IAE5C;AAAA;AAAA,IAGD,YAAY;AACV,YAAM,QAAQ,KAAK,aAAa;AAChC,aAAO;AAAA,QACL,WAAW,EAAE,SAAS,KAAK,UAAU,YAAY,OAAQ;AAAA,QACzD,WAAW,KAAK,aAAa,OAAO,KAAO,GAAG;AAAA;IAEjD;AAAA;AAAA,IAOD,aAAa;AAAE,aAAO,KAAK,KAAK,UAAU,KAAK,KAAK,SAAS;AAAA,IAAI;AAAA,IACjE,cAAc;AAAE,aAAO,KAAK,KAAK,mBAAmB,KAAK,KAAK,eAAe;AAAA,IAAO;AAAA,IACpF,eAAe;AAAE,aAAO,KAAK,KAAK,iBAAiB,KAAK,KAAK,YAAY;AAAA,IAAI;AAAA,EAC9E;AAAA,EACD,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,QAAQ,QAAQ;AACd,YAAI,QAAQ;AACV,gBAAM,aAAa,OAAO,YAAY;AACtC,gBAAM,eAAe,SAAS,OAAO,SAAS,CAAC;AAG/C,cAAI,KAAK,YAAY,cAAc,KAAK,cAAc,cAAc;AAClE,iBAAK,UAAU;AACf,iBAAK,YAAY;AAAA,UACnB;AAAA,QACF;AAAA,MACD;AAAA,MACD,WAAW;AAAA,MACX,MAAM;AAAA,IACP;AAAA;AAAA,IAGD,SAAS;AAAA,MACP,QAAQ,QAAQ,QAAQ;AAEtB,YAAI,WAAW,QAAQ;AACrB,eAAK,UAAU,MAAM;AACnB,iBAAK,aAAY;AAAA,UACnB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,SAAS,MAAM,MAAM;AACnB,UAAI;AACJ,aAAO,SAAS,oBAAoB,MAAM;AACxC,cAAM,QAAQ,MAAM;AAClB,uBAAa,OAAO;AACpB,eAAK,MAAM,MAAM,IAAI;AAAA;AAEvB,qBAAa,OAAO;AACpB,kBAAU,WAAW,OAAO,IAAI;AAAA;IAEnC;AAAA;AAAA,IAGD,aAAa,KAAK,YAAY,KAAO,SAAS,KAAK;AACjD,UAAI,CAAC,OAAO,MAAM;AAAW,eAAO,IAAI,SAAW,KAAG;AACtD,cAAQ,MAAM,WAAW,QAAQ,CAAC,IAAI;AAAA,IACvC;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI;AAEF,cAAM,kBAAkB,KAAK,UAAU,MAAM,GAAG,CAAC;AACjD,wBAAgB,QAAQ,SAAO;AAC7B,cAAI,KAAK;AAEP,kBAAM,MAAM,IAAI;AAChB,gBAAI,MAAM;AAAA,UACZ;AAAA,QACF,CAAC;AAGD,YAAI,KAAK,YAAY;AACnB,gBAAM,YAAY,IAAI;AACtB,oBAAU,MAAM,KAAK;AAAA,QACvB;AAAA,MACF,SAAS,GAAG;AAAA,MACZ;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,KAAK,cAAc;AAC/B,UAAI;AACF,YAAI,OAAO,WAAW;AAAY,iBAAO;AACzC,cAAM,MAAM;AACZ,YAAI,CAAC,OAAO,CAAC,IAAI;AAAY,iBAAO;AACpC,eAAO,IAAI,WAAW,GAAG,MAAM,SAAY,IAAI,WAAW,GAAG,IAAI;AAAA,MACnE,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,OAAO,iBAAiB,QAAQ;AAC1C,YAAM,WAAU,+BAAO,aAAW,+BAAO,QAAO;AAChDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB;AAAA,IACjB;AAAA;AAAA,IAGD,WAAW,OAAO;AAChB,UAAI,CAAC;AAAO,eAAO;AACnB,aAAO,MAAM,SAAS,MAAM,QAAQ;AAAA,IACrC;AAAA;AAAA,IAGD,WAAW,SAAS,IAAI;AACtB,YAAM,cAAc,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ;AACpF,YAAM,WAAW,cAAc,eAAe;AAC9C,aAAO,GAAG,QAAQ,OAAO,KAAK,KAAK,EAAE,GAAG,MAAM;AAAA,IAC/C;AAAA;AAAA;AAAA;AAAA,IAKD,aAAa;AACX,WAAK,oBAAmB;AAAA,IACzB;AAAA;AAAA;AAAA;AAAA,IAKD,mBAAmB;AAEjB,UAAI,CAAC,KAAK,OAAO,QAAQ,SAAS;AAChCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACP,CAAC;AACD;AAAA,MACF;AAGA,UAAI,KAAK;AAAc;AACvB,WAAK,eAAe;AAEpB,YAAM,gBAAgB,CAAC,KAAK;AAC5B,YAAM,eAAe,KAAK;AAG1B,WAAK,UAAU;AACf,WAAK,YAAY,gBAAgB,eAAe,IAAI,KAAK,IAAI,GAAG,eAAe,CAAC;AAGhFC,6BAAY;AAAA,QACV,IAAI,KAAK,KAAK;AAAA,QACd,SAAS,gBAAgB,IAAI;AAAA,OAC9B,EAAE,KAAK,MAAM;AAEZ,aAAK,MAAM,YAAY;AAAA,UACrB,OAAO,KAAK;AAAA,UACZ,IAAI,KAAK,KAAK;AAAA,UACd,QAAQ;AAAA,QACV,CAAC;AAAA,MACH,CAAC,EAAE,MAAM,CAAC,UAAU;AAElB,aAAK,UAAU,CAAC;AAChB,aAAK,YAAY;AACjB,aAAK,YAAY,OAAO,UAAU;AAAA,MACpC,CAAC,EAAE,QAAQ,MAAM;AACf,aAAK,eAAe;AAAA,MACtB,CAAC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,QAAQ,GAAG;AAET,UAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,KAAK;AAAI;AAGjC,UAAI,OAAO,KAAK,EAAE,gBAAgB,SAAS,EAAE,cAAc,QAAQ,IAAI,IAAI;AAG3E,UAAI;AAGJ,UAAI,SAAS,iBAAiB,cAAc;AAE1C,YAAI,KAAK,KAAK,KAAK;AACjB,gBAAM,qBAAqB,KAAK,KAAK;AAAA,QACvC;AAAA,MACF,WAAW,SAAS,iBAAiB,OAAO;AAE1C,YAAI,KAAK,cAAc,KAAK,WAAW,SAAS,GAAG;AACjD,gBAAM,QAAQ,KAAK,WAAW,CAAC;AAC/B,gBAAM,UAAU,SAAS,MAAM,KAAK,MAAM,KAAK;AAC/C,gBAAM,sBAAsB;AAAA,QAC9B;AAAA,MACF,WAAW,SAAS,iBAAiB,QAAQ;AAE3C,YAAI,KAAK,cAAc,WAAW;AAChC,gBAAM,oBAAoB,KAAK,WAAW;AAAA,QAC5C;AAAA,MACF,WAAW,SAAS,iBAAiB,SAAS;AAE5C,YAAI,UAAU,KAAK,EAAE,iBAAiB,EAAE,cAAc,UAAU,EAAE,cAAc,QAAQ,KAAK;AAC7F,YAAI,SAAS;AACX,gBAAM,sBAAsB;AAAA,QAC9B;AAAA,MACF,WAAW,SAAS,iBAAiB,SAAS;AAE5C,cAAM,KAAK,WAAW,eAAe;AAAA,MACvC,WAAW,SAAS,iBAAiB,OAAO;AAE1C,cAAM,KAAK,WAAW,aAAa;AAAA,MACrC,WAAW,SAAS,iBAAiB,UAAU;AAE7C,cAAM,aAAa,KAAK,KAAK,eAAe,KAAK,SAAS,SAAS;AACnE,YAAI,YAAY;AACd,gBAAM,yBAAyB;AAAA,QACjC;AAAA,aACK;AAEL,cAAM,KAAK;MACb;AAGA,UAAI,KAAK;AACP,YAAI;AACFD,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,YAAY;AAAA,YACjB,MAAM,CAAC,QAAQ;AACbA,4BAAA,MAAA,MAAA,SAAA,yCAAc,WAAW,GAAG;AAE5BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,KAAK;AAAA,gBACL,MAAM,MAAM;AACVA,gCAAAA,MAAA,MAAA,SAAA,yCAAc,SAAS;AAAA,gBACzB;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH,SAASE,IAAG;AACVF,wBAAA,MAAA,MAAA,SAAA,yCAAc,SAASE,EAAC;AAAA,QAC1B;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AAEhB,UAAI,CAAC,KAAK;AAAM;AAGhB,UAAI,WAAW;AACf,UAAI;AACF,YAAI,KAAK,KAAK,UAAU;AACtB,qBAAW,WAAW,KAAK,KAAK,QAAQ;AAAA,mBAC/B,KAAK,KAAK,KAAK;AACxB,qBAAW,WAAW,KAAK,KAAK,GAAG;AAAA,QACrC;AAGA,YAAI,MAAM,QAAQ;AAAG,qBAAW;AAAA,MAClC,SAAS,GAAG;AACVF,oFAAc,WAAW,CAAC;AAC1B,mBAAW;AAAA,MACb;AAGA,UAAI,YAAY;AAChB,UAAI;AACF,YAAI,KAAK,KAAK,WAAW;AACvB,sBAAY,WAAW,KAAK,KAAK,SAAS;AAAA,mBACjC,KAAK,KAAK,KAAK;AACxB,sBAAY,WAAW,KAAK,KAAK,GAAG;AAAA,QACtC;AAGA,YAAI,MAAM,SAAS;AAAG,sBAAY;AAAA,MACpC,SAAS,GAAG;AACVA,oFAAc,WAAW,CAAC;AAC1B,oBAAY;AAAA,MACd;AAGA,UAAI,aAAa,KAAK,cAAc,GAAG;AACrCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,UAAI;AACFA,sBAAAA,MAAI,aAAa;AAAA,UACf;AAAA,UACA;AAAA,UACA,MAAM,KAAK,gBAAgB;AAAA,UAC3B,SAAS,KAAK,gBAAgB;AAAA,UAC9B,MAAM,CAAC,QAAQ;AACbA,0BAAc,MAAA,MAAA,SAAA,yCAAA,UAAU,GAAG;AAC3BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH,SAAS,GAAG;AACVA,oFAAc,WAAW,CAAC;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,aAAa;AACX,WAAK,oBAAmB;AAAA,IACzB;AAAA;AAAA,IAGD,mBAAmB;AAEjB,UAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,KAAK,KAAK;AAChC;AAAA,MACF;AAGA,UAAI,CAAC,KAAK,OAAO,QAAQ,SAAS;AAChCA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK;AAAA,QACP,CAAC;AACD;AAAA,MACF;AAGA,UAAI,KAAK;AAAc;AACvB,WAAK,eAAe;AAGpB,YAAM,eAAe,SAAS,KAAK,KAAK,GAAG;AAG3C,UAAI,CAAC,gBAAgB,gBAAgB,GAAG;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD,aAAK,eAAe;AACpB;AAAA,MACF;AAGA,YAAM,EAAE,UAAU,SAAO,IAAM,KAAK,SAAS;AAC7C,YAAM,aAAa,YAAY;AAG/B,YAAM,SAAS;AAAA,QACb,YAAY;AAAA;AAAA,QACZ,WAAW,aAAa,IAAI;AAAA;AAAA;AAK9B,UAAI,YAAY;AAEd,aAAK,KAAK,YAAY;AACtB,aAAK,KAAK,mBAAmB;AAG7B,YAAI,KAAK,KAAK,WAAW;AACvB,eAAK,KAAK,UAAU,YAAY;AAChC,eAAK,KAAK,UAAU,mBAAmB;AAAA,QACzC;AAAA,aACK;AAEL,aAAK,KAAK,YAAY;AAItB,YAAI,KAAK,KAAK,WAAW;AACvB,eAAK,KAAK,UAAU,YAAY;AAAA,QAClC;AAAA,MACF;AAGAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,aAAa,aAAa;AAAA,QACjC,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAC;AAGDG,iBAAAA,WAAW,MAAM,EAAE,KAAK,SAAO;;AAC7B,YAAI,IAAI,WAAW,KAAK;AAEtB,cAAI,IAAI,QAAQ,IAAI,KAAK,cAAc,QAAW;AAChD,kBAAMC,YAAW,IAAI,KAAK,cAAc;AAGxC,iBAAK,KAAK,mBAAmBA,YAAW,IAAI;AAG5C,gBAAI,KAAK,KAAK,WAAW;AACvB,mBAAK,KAAK,UAAU,mBAAmBA,YAAW,IAAI;AAAA,YACxD;AAAA,UACF;AAGA,eAAK,MAAM,cAAc;AAAA,YACvB,KAAK,KAAK;AAAA,YACV,KAAK,KAAK,KAAK;AAAA,YACf,WAAW,aAAa,IAAI;AAAA,YAC5B,aAAW,SAAI,SAAJ,mBAAU,cAAa;AAAA,UACpC,CAAC;AAGDJ,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,aAAa,UAAU;AAAA,YAC9B,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAAA,eACI;AAEL,cAAI,YAAY;AAEd,iBAAK,KAAK,YAAY;AACtB,gBAAI,UAAU;AACZ,mBAAK,KAAK,mBAAmB;AAAA,YAC/B;AAGA,gBAAI,KAAK,KAAK,WAAW;AACvB,mBAAK,KAAK,UAAU,YAAY;AAChC,kBAAI,UAAU;AACZ,qBAAK,KAAK,UAAU,mBAAmB;AAAA,cACzC;AAAA,YACF;AAAA,iBACK;AAEL,iBAAK,KAAK,YAAY;AACtB,iBAAK,KAAK,mBAAmB;AAG7B,gBAAI,KAAK,KAAK,WAAW;AACvB,mBAAK,KAAK,UAAU,YAAY;AAChC,mBAAK,KAAK,UAAU,mBAAmB;AAAA,YACzC;AAAA,UACF;AAGAA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC,EAAE,MAAM,CAAC,UAAU;AAElB,YAAI,YAAY;AAEd,eAAK,KAAK,YAAY;AACtB,cAAI,UAAU;AACZ,iBAAK,KAAK,mBAAmB;AAAA,UAC/B;AAGA,cAAI,KAAK,KAAK,WAAW;AACvB,iBAAK,KAAK,UAAU,YAAY;AAChC,gBAAI,UAAU;AACZ,mBAAK,KAAK,UAAU,mBAAmB;AAAA,YACzC;AAAA,UACF;AAAA,eACK;AAEL,eAAK,KAAK,YAAY;AACtB,eAAK,KAAK,mBAAmB;AAG7B,cAAI,KAAK,KAAK,WAAW;AACvB,iBAAK,KAAK,UAAU,YAAY;AAChC,iBAAK,KAAK,UAAU,mBAAmB;AAAA,UACzC;AAAA,QACF;AAGA,aAAK,YAAY,OAAO,YAAY;AAAA,MACtC,CAAC,EAAE,QAAQ,MAAM;AACf,aAAK,eAAe;AAAA,MACtB,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AACZ,WAAK,eAAe,KAAK,WAAY,CAAA;AAAA,IACtC;AAAA;AAAA,IAGD,gBAAgB;AACd,WAAK,eAAe,qBAAqB,KAAK,KAAK,EAAE;AAAA,IACtD;AAAA;AAAA,IAGD,eAAe,KAAK;AAElB,UAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,KAAK,MAAM,CAAC;AAAK;AAEzC,UAAI;AACFA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,YAAY;AAAA,UACjB,MAAM,CAAC,QAAQ;AACbA,0BAAc,MAAA,MAAA,SAAA,0CAAA,WAAW,GAAG;AAE5BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,KAAK;AAAA,cACL,MAAM,MAAM;AACVA,8BAAAA,MAAA,MAAA,SAAA,0CAAc,SAAS;AAAA,cACzB;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH,SAAS,GAAG;AACVA,sBAAc,MAAA,MAAA,SAAA,0CAAA,SAAS,CAAC;AAAA,MAC1B;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,MAAM;AAEtB,WAAK,KAAK,YAAY,KAAK;AAG3B,WAAK,MAAM,UAAU;AAAA,QACnB,WAAW,KAAK;AAAA,QAChB,KAAK,KAAK;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACD;AAAA,EACD,UAAU;AAER,QAAI,CAAC,KAAK;AAAM;AAEhB,QAAI,KAAK,KAAK,SAAS,QAAW;AAChC,UAAI,KAAK,KAAK,SAAS,KAAK,KAAK,WAAW;AAC1C,aAAK,KAAK,KAAK,MAAM,QAAQ,CAAC;AAAA,iBACrB,KAAK,KAAK,SAAS,KAAK,KAAK,aAAa;AACnD,aAAK,KAAK,KAAK,MAAM,QAAQ,CAAC;AAAA,iBACrB,KAAK,aAAa,KAAK,UAAU,SAAS,GAAG;AACtD,aAAK,KAAK,KAAK,MAAM,QAAQ,CAAC;AAAA,aACzB;AACL,aAAK,KAAK,KAAK,MAAM,QAAQ,CAAC;AAAA,MAChC;AAAA,IACF;AAAA,EACD;AAAA,EACD,eAAe;AAEb,QAAI,KAAK,SAAS,aAAa,CAAC,KAAK,SAAS,UAAU,MAAM;AAC5D,WAAK,SAAS,UAAU,OAAO,CAAA;AAAA,IACjC;AAAA,EACD;AAAA,EACD,cAAc,KAAK,GAAG,MAAM;AAC1B,WAAO;AAAA,EACT;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACriCA,GAAG,gBAAgB,SAAS;"}