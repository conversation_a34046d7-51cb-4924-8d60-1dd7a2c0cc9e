<view class="container"><view class="nav-bar bfw" style="{{'padding-top:' + f}}"><view class="bar-box df" style="{{'height:' + d + ';' + ('width:' + '100%')}}"><view class="bar-back df" bindtap="{{b}}"><image src="{{a}}" style="width:34rpx;height:34rpx"></image></view><view class="bar-title ohto">{{c}}</view></view><view class="nav-box df"><view wx:for="{{e}}" wx:for-item="item" wx:key="e" class="nav-item df" bindtap="{{item.f}}" data-idx="{{item.g}}"><text style="{{'color:' + item.b + ';' + ('font-size:' + item.c)}}">{{item.a}}</text><view style="{{'opacity:' + item.d}}" class="nav-line"></view></view></view></view><view class="content-box" style="{{'margin-top:' + m}}"><view wx:if="{{g}}" class="loading-container"><view class="loading-indicator"></view></view><empty-page wx:if="{{h}}" u-i="127bb211-0" bind:__l="__l" u-p="{{i}}"/><block wx:else><view wx:for="{{j}}" wx:for-item="item" wx:key="i" class="list-box df" bindtap="{{item.j}}" data-id="{{item.k}}"><view class="list-item df"><view class="list-avatar"><lazy-image wx:if="{{item.b}}" u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}"></lazy-image></view><view class="item-content"><view class="name ohto">{{item.c}}</view><view class="intro ohto">{{item.d}}</view></view></view><view wx:if="{{k}}" data-idx="{{item.f}}" catchtap="{{item.g}}" class="{{item.h}}">{{item.e}}</view></view></block><view wx:if="{{l}}" style="text-align:center;padding:20rpx 0;color:#999;font-size:24rpx"> 没有更多数据了 </view></view></view>