
.container {
  min-height: 100vh;
  background: #f8f8f8;
}
.nav-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  background: #fff;
  border-bottom: 0.03125rem solid #f0f0f0;
}
.nav-back {
  padding: 0 0.9375rem;
  width: 1.0625rem;
  height: 100%;
}
.nav-title {
  font-size: 1rem;
  font-weight: 700;
  color: #333;
}
.topic-list {
  padding: 0.625rem;
}
.topic-item {
  background: #fff;
  border-radius: 0.5rem;
  padding: 0.9375rem;
  margin-bottom: 0.625rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 0.0625rem 0.25rem rgba(0,0,0,0.1);
}
.topic-content {
  flex: 1;
}
.topic-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.3125rem;
}
.topic-hash {
  font-size: 1rem;
  font-weight: bold;
  color: #007AFF;
  margin-right: 0.25rem;
}
.topic-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
}
.topic-stats {
  display: flex;
  align-items: center;
  margin-bottom: 0.3125rem;
}
.stat-text {
  font-size: 0.75rem;
  color: #999;
}
.stat-divider {
  margin: 0 0.25rem;
  font-size: 0.75rem;
  color: #999;
}
.topic-desc {
  font-size: 0.75rem;
  color: #666;
  line-height: 1.4;
}
.topic-arrow {
  width: 1rem;
  height: 1rem;
}
.topic-arrow uni-image {
  width: 100%;
  height: 100%;
}
.empty-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-box uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.9375rem;
}
.empty-text {
  font-size: 0.875rem;
  color: #999;
}
.loading-box {
  display: flex;
  justify-content: center;
  padding: 1.5625rem 0;
}
.loading-box uni-text {
  font-size: 0.875rem;
  color: #999;
}
.df {
  display: flex;
  align-items: center;
}
