{"version": 3, "file": "lazyImage.js", "sources": ["components/lazyImage/lazyImage.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy9sYXp5SW1hZ2UvbGF6eUltYWdlLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"lazy-image\" ref=\"imageWrapper\" :style=\"{'border-radius': br}\">\n    <image \n      :src=\"src || '/static/img/load.png'\" \n      :style=\"{'opacity': loaded ? 1 : 0, 'transition': 'opacity .3s ease-in-out'}\" \n      @load=\"onImageLoad\" \n      @error=\"onImageError\"\n      mode=\"aspectFill\"/>\n    <view v-if=\"error\" class=\"err\"></view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'lazyImage',\n  props: {\n    src: {\n      type: String,\n      default: '',\n      required: false\n    },\n    br: {\n      type: String,\n      default: undefined,\n      required: false\n    }\n  },\n  data() {\n    return {\n      loaded: false,\n      error: false\n    }\n  },\n  watch: {\n    src: {\n      handler(newVal, oldVal) {\n        if (newVal !== oldVal) {\n          this.resetLoadingState();\n        }\n      },\n      immediate: false\n    }\n  },\n  mounted() {\n    this.resetLoadingState();\n  },\n  methods: {\n    // 重置加载状态\n    resetLoadingState() {\n      if (!this.src) {\n        this.loaded = true;\n        this.error = false;\n      } else {\n        this.loaded = false;\n        this.error = false;\n      }\n    },\n    // 图片加载成功\n    onImageLoad() {\n      this.loaded = true;\n      this.error = false;\n    },\n    // 图片加载失败\n    onImageError() {\n      this.error = true;\n      this.loaded = true; // 即使出错也标记为已加载，显示错误占位图\n      console.log('Image load error:', this.src);\n    }\n  }\n}\n</script>\n\n<style scoped>\n.lazy-image{position:relative;width:100%;height:100%;overflow:hidden;background:#f8f8f8;}\n.lazy-image image{width:100%;height:100%;}\n.lazy-image .err{position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(248,248,248,0.7) url('/static/img/load.png') no-repeat center/50%;}\n</style> ", "import Component from 'D:/uniapp/vue3/components/lazyImage/lazyImage.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AAaA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,IACX;AAAA,IACD,IAAI;AAAA,MACF,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACL,KAAK;AAAA,MACH,QAAQ,QAAQ,QAAQ;AACtB,YAAI,WAAW,QAAQ;AACrB,eAAK,kBAAiB;AAAA,QACxB;AAAA,MACD;AAAA,MACD,WAAW;AAAA,IACb;AAAA,EACD;AAAA,EACD,UAAU;AACR,SAAK,kBAAiB;AAAA,EACvB;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,oBAAoB;AAClB,UAAI,CAAC,KAAK,KAAK;AACb,aAAK,SAAS;AACd,aAAK,QAAQ;AAAA,aACR;AACL,aAAK,SAAS;AACd,aAAK,QAAQ;AAAA,MACf;AAAA,IACD;AAAA;AAAA,IAED,cAAc;AACZ,WAAK,SAAS;AACd,WAAK,QAAQ;AAAA,IACd;AAAA;AAAA,IAED,eAAe;AACb,WAAK,QAAQ;AACb,WAAK,SAAS;AACdA,mFAAY,qBAAqB,KAAK,GAAG;AAAA,IAC3C;AAAA,EACF;AACF;;;;;;;;;;;;;ACpEA,GAAG,gBAAgB,SAAS;"}