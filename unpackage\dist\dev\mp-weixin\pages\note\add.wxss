
.container {
  padding-bottom: 140rpx; /* 为固定工具栏留出空间，减少空白 */
}

/* 导航栏样式 - 参考details.vue */
.nav-box{
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  width: 100%;
  background: #fff;
  box-sizing: border-box;
}
.nav-item{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.nav-item .nav-back{
  padding: 0 30rpx;
  width: 34rpx;
  height: 100%;
  border-radius: 50%;
}
.nav-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 700;
  text-align: center;
  max-width: 400rpx;
}
.nav-publish {
  padding: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 非小程序环境的默认内边距 */
.publish-btn {
  position: relative;
  padding: 0 32rpx;
  height: 64rpx;
  min-width: 96rpx;
  border-radius: 32rpx;
  background: #000;
  color: #fff;
  font-size: 28rpx;
  font-weight: 700;
  letter-spacing: 1rpx;
  text-align: center;
  line-height: 64rpx;
  border: none;
  outline: none;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.15),
    0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 发光效果 */
.publish-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 内部高光 */
.publish-btn::after {
  content: '';
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  right: 2rpx;
  height: 30rpx;
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 30rpx 30rpx 60rpx 60rpx;
  opacity: 0.6;
}

/* 点击效果 */
.publish-btn:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.2),
    0 1rpx 4rpx rgba(0, 0, 0, 0.15);
}

/* 禁用状态 */
.publish-btn-disabled {
  background: #666;
  color: #999;
  cursor: not-allowed;
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.1),
    0 1rpx 4rpx rgba(0, 0, 0, 0.05);
}
.publish-btn-disabled::before {
  display: none;
}
.publish-btn-disabled::after {
  opacity: 0.3;
}
.publish-btn-disabled:active {
  transform: none;
}

/* 小程序环境适配 */
.publish-btn {
  background: #000;
  border: 2rpx solid rgba(255, 255, 255, 0.1);
}
.publish-btn::before {
  background: linear-gradient(45deg,
    rgba(7, 193, 96, 0.1) 0%,
    rgba(0, 174, 66, 0.05) 50%,
    rgba(7, 193, 96, 0.1) 100%);
}


/* H5环境优化 */























/* APP环境优化 */














/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
.publish-btn {
    background: #1a1a1a;
    border: 1rpx solid rgba(255, 255, 255, 0.1);
    box-shadow:
      0 4rpx 16rpx rgba(0, 0, 0, 0.3),
      0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}
.publish-btn-disabled {
    background: #333;
    color: #666;
}
}
.content-box {
  margin: 30rpx;
  width: calc(100% - 60rpx);
  border-radius: 30rpx;
  position: relative;
}
.textarea-container {
  position: relative;
  width: 100%;
}

/* 透明的textarea用于输入 */
.textarea-input {
  width: calc(100% - 40rpx);
  padding: 20rpx;
  font-size: 28rpx;
  min-height: 280rpx;
  line-height: 1.6;
  color: transparent; /* 文字透明，只显示光标 */
  background: transparent;
  position: relative;
  z-index: 2;
}

/* 文本覆盖层 */
.text-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: calc(100% - 40rpx);
  padding: 20rpx;
  font-size: 28rpx;
  min-height: 280rpx;
  line-height: 1.6;
  pointer-events: none; /* 不阻挡textarea的点击 */
  z-index: 1;
}
.formatted-content {
  width: 100%;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  word-wrap: break-word;
  word-break: break-all;
}

/* 占位符文本样式 */
.placeholder-text {
  color: #999;
  font-size: 28rpx;
}

/* 普通文本样式 */
.normal-text {
  color: #333;
  font-size: 28rpx;
}

/* @用户文本样式 - 蓝色链接样式 */
.mention-text {
  color: #1890ff;
  font-size: 28rpx;
  font-weight: 500;
}

/* 兼容原有的textarea-item样式 */
.content-box .textarea-item {
  width: calc(100% - 40rpx);
  padding: 20rpx;
  font-size: 28rpx;
  min-height: 280rpx;
}

/* @用户标签样式 */
.mention-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  padding: 0 20rpx 10rpx;
  margin-top: 10rpx;
}
.mention-tag {
  display: flex;
  align-items: center;
  background: #f0f9ff;
  border: 1px solid #07c160;
  border-radius: 20rpx;
  padding: 8rpx 12rpx;
}
.mention-tag:active {
  background: #e6f7ff;
}
.mention-avatar {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  margin-right: 8rpx;
  flex-shrink: 0;
}
.mention-nickname {
  font-size: 24rpx;
  color: #07c160;
  font-weight: 500;
  margin-right: 8rpx;
}
.mention-delete {
  font-size: 28rpx;
  color: #999;
  font-weight: bold;
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
}
.content-box .scroll-box {
  margin: 20rpx 0;
  white-space: nowrap;
  width: 100%;
}
.scroll-box .scroll-item {
  display: flex;
  padding: 0 20rpx;
}
.scroll-item .del {
  position: absolute;
  z-index: 9;
  top: 8rpx;
  right: 8rpx;
  width: 38rpx;
  height: 38rpx;
  border-radius: 50%;
  justify-content: center;
  transform: rotate(45deg);
  border: 1px solid #fff;
  background: rgba(0, 0, 0, 0.3);
}
.scroll-item .del image {
  width: 18rpx;
  height: 18rpx;
}
.scroll-item .img-box {
  flex-shrink: 0;
  margin-right: 8rpx;
  width: 196rpx;
  height: 196rpx;
  border-radius: 8rpx;
  border: 1px solid #f8f8f8;
  justify-content: center;
  position: relative;
  overflow: hidden;
}
.scroll-item .icon {
  color: #999;
  font-size: 36rpx;
  font-weight: 700;
}
.scroll-item image,
.scroll-item video {
  width: 100%;
  height: 100%;
}
.scroll-item .sort {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 196rpx;
  height: 45rpx;
  background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.376));
  border-radius: 0 0 8rpx 8rpx;
}
.scroll-item .sort .sort-item {
  width: 50%;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 700;
}
.scroll-item .video-box {
  width: 100%;
  justify-content: space-between;
}
.video-box .video-item {
  width: calc(50% - 8rpx);
  height: 240rpx;
  border-radius: 8rpx;
  position: relative;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.video-box .video-item video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8rpx;
}
.video-box .video-item .del {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  z-index: 10;
}
.video-box .video-item .del image {
  width: 20rpx;
  height: 20rpx;
}
.scroll-item .audio-box,
.scroll-item .file-audio {
  width: 100%;
  height: 128rpx;
  border-radius: 8rpx;
}
.scroll-item .file-audio {
  color: #fff;
  position: relative;
  overflow: hidden;
}
.file-audio .audio-left {
  margin-right: 30rpx;
  width: 128rpx;
  height: 128rpx;
  position: relative;
}
.file-audio .audio-left .icon {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  bottom: 40rpx;
  left: 40rpx;
  width: 48rpx;
  height: 48rpx;
}
.file-audio .audio-bg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -2;
  width: 100%;
  height: 100%;
}
.file-audio .audio-mb {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  -webkit-backdrop-filter: saturate(150%) blur(25px);
  backdrop-filter: saturate(150%) blur(25px);
  background: rgba(0, 0, 0, 0.5);
}
.file-audio .audio-t1 {
  font-size: 26rpx;
  font-weight: 700;
}
.file-audio .audio-t2 {
  margin-top: 10rpx;
  opacity: 0.8;
  font-size: 20rpx;
}
.file-audio .aph {
  color: #fff;
}
.scroll-item .file-add {
  flex-direction: column;
  justify-content: center;
  background: #f8f8f8;
  border: 1px solid #f8f8f8;
  font-size: 18rpx;
  color: #000;
}
.scroll-item .file-add image {
  width: 20rpx;
  height: 20rpx;
  margin-bottom: 8rpx;
}
.content-box .content-item {
  width: calc(100% - 40rpx);
  margin: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 16rpx;
}

/* 内容区域的标签项样式调整 */
.content-item .tag-item {
  margin: 0; /* 重置margin，使用gap来控制间距 */
  flex-shrink: 0; /* 防止标签被压缩 */
}

/* 标签区域的标签项保持原有边距 */
.tags-box .tag-item {
  margin: 0 10rpx 10rpx;
}
.tags-box {
  width: calc(100% - 40rpx);
  padding: 0 20rpx;
  display: flex;
  flex-wrap: wrap;
}
.tag-item {
  margin: 0 10rpx 10rpx;
  height: 64rpx;
  border-radius: 16rpx;
  border: 4rpx solid #f5f5f5;
  transition: all 0.2s ease;
}
.tag-item:active {
  transform: scale(0.95);
  background-color: #f8f8f8;
}

/* 已选择状态 */
.tag-item.selected {
  border: 4rpx solid #f5f5f5;
  background-color: #fff;
}

/* 未选择状态 */
.tag-item.unselected {
  border: 4rpx dashed #e0e0e0;
  background-color: #fafafa;
}
.tag-item.unselected:active {
  border-color: #d0d0d0;
  background-color: #f0f0f0;
  transform: scale(0.95);
}
.tag-item .icon {
  margin: 8rpx;
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  transition: opacity 0.2s ease;
}
.tag-item text {
  font-size: 24rpx;
  font-weight: 700;
  margin: 0 16rpx 0 8rpx;
  transition: color 0.2s ease;
}
.tag-delete {
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8rpx;
}
.tag-delete text {
  font-size: 24rpx;
  color: #999;
  margin: 0;
}
.location-icon {
  padding: 12rpx;
  transition: opacity 0.2s ease;
}
.location-text {
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.content-hk .hk-item .del {
  color: #fa5150;
  font-size: 28rpx;
  font-weight: 700;
  transform: rotate(45deg);
}
.popup-box {
  width: calc(100% - 40rpx);
  padding: 20rpx;
  background: #fff;
  border-radius: 30rpx 30rpx 0 0;
  position: relative;
  overflow: hidden;
}
.popup-box .popup-top {
  width: calc(100% - 20rpx);
  padding: 10rpx;
  justify-content: space-between;
}
.popup-top .popup-title .t1 {
  font-size: 38rpx;
  font-weight: 700;
}
.popup-top .popup-title .t2 {
  color: #999;
  font-size: 20rpx;
  font-weight: 300;
}
.popup-top .popup-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f8f8;
  justify-content: center;
  transform: rotate(45deg);
}
.popup-box .popup-btn {
  margin: 40rpx 10rpx;
  width: calc(100% - 20rpx);
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 100rpx;
}
.popup-box .popup-search {
  margin: 30rpx 10rpx;
  width: calc(100% - 40rpx);
  padding: 0 10rpx;
  height: 80rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}
.popup-box .popup-search input {
  width: calc(100% - 130rpx);
  margin: 0 20rpx;
  font-size: 24rpx;
  font-weight: 700;
}
.popup-search .search-ph {
  color: #999;
}
.popup-search .search-btn {
  width: 90rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 20rpx;
  font-weight: 700;
  color: #000;
  background: #fff;
  border-radius: 8rpx;
}
.popup-box .popup-scroll {
  width: 100%;
  max-height: 50vh;
  overflow-y: scroll;
}
.popup-box .circle-box {
  display: flex;
  flex-wrap: wrap;
}
.circle-box .circle-item {
  margin: 10rpx;
  padding: 8rpx;
  color: #000;
  border-color: #f8f8f8;
  border-width: 4rpx;
  border-style: solid;
  border-radius: 50rpx;
}
.circle-box .active {
  border-color: #000 !important;
  background: #f8f8f8 !important;
}
.circle-box .circle-item image {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
}
.circle-box .circle-item text {
  margin: 0 16rpx;
  font-size: 26rpx;
  font-weight: 700;
}
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-box image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-box .e1 {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.empty-box .e2 {
  font-size: 24rpx;
  color: #999;
}
.tips-box {
  padding: 20rpx 30rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  justify-content: center;
}
.tips-box .tips-item {
  color: #fff;
  font-size: 28rpx;
  font-weight: 700;
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.media-toolbar-fixed {
  position: fixed;
  left: 0;
  width: 100%;
  background-color: #f8f8f8;
  border-top: 1px solid #e5e5e5;
  z-index: 99;
  box-shadow: 0 -2rpx 6rpx rgba(0, 0, 0, 0.05);
  transition: bottom 0.3s ease;
}

/* 键盘弹起时的样式调整 */
.media-toolbar-fixed.keyboard-active {
  padding-bottom: 0; /* 键盘弹起时移除底部安全区域 */
}

/* 非键盘状态时保持底部安全区域 */
.media-toolbar-fixed:not(.keyboard-active) {
  padding-bottom: env(safe-area-inset-bottom);
}
.toolbar-box {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  width: 100%;
  min-height: 88rpx;
  position: relative;
}
.toolbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12rpx 6rpx;
  border-radius: 12rpx;
  transition: all 0.2s;
  flex: 1;
  max-width: 100rpx;
  min-height: 70rpx;
}
.toolbar-item:active {
  background-color: #eaeaea;
  transform: scale(0.95);
}
.toolbar-item.active {
  background-color: #f0f0f0;
  border-radius: 12rpx;
}
.toolbar-item image {
  width: 44rpx;
  height: 44rpx;
}
.toolbar-item .vote {
  color: #ff9500;
  text-shadow: 0 1rpx 2rpx rgba(255, 149, 0, 0.3);
  font-size: 40rpx;
  font-weight: bold;
  background-color: rgba(255, 149, 0, 0.1);
  border-radius: 50%;
  width: 44rpx;
  height: 44rpx;
  line-height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.more-options-panel {
  width: 100%;
  background: #f8f8f8;
  border-radius: 0;
  max-height: 0;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
}
.more-options-panel.expanded {
  max-height: 200rpx;
  padding: 20rpx 0;
}
.more-options-row {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  margin-bottom: 0;
}
.more-options-row .toolbar-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 12rpx 6rpx;
  margin: 0 6rpx;
}
.more-options-row .toolbar-item image {
  width: 44rpx;
  height: 44rpx;
}
.toolbar-item.invisible {
  opacity: 0;
  pointer-events: none;
}

/* 话题列表样式 */
.topic-box {
  width: 100%;
  padding: 10rpx;
}
.topic-item {
  width: calc(100% - 40rpx);
  padding: 20rpx;
  border-bottom: 1px solid #f5f5f5;
}
.topic-tag {
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background: #f8f8f8;
  border-radius: 50%;
  font-size: 36rpx;
  font-weight: bold;
  margin-right: 20rpx;
}
.topic-content {
  flex: 1;
}
.topic-name {
  font-size: 28rpx;
  font-weight: 700;
  margin-bottom: 6rpx;
}
.topic-desc {
  font-size: 24rpx;
  color: #999;
}

/* 录音组件样式 */
.record-popup-box {
  padding-bottom: 40rpx;
}
.record-container {
  width: 100%;
  padding: 30rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.record-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  padding: 20rpx;
}

/* 投票弹窗样式 */
.vote-popup {
  padding-bottom: 30rpx;
}
.vote-title-input {
  margin: 20rpx 10rpx;
  width: calc(100% - 20rpx);
  border-bottom: 1px solid #f0f0f0;
}
.vote-input {
  width: 100%;
  height: 80rpx;
  font-size: 28rpx;
  font-weight: 500;
  padding: 0 10rpx;
}
.vote-options {
  margin: 20rpx 10rpx;
  width: calc(100% - 20rpx);
}
.vote-option-item {
  position: relative;
  margin-bottom: 20rpx;
  background-color: #f5f6fa;
  border-radius: 16rpx;
  padding: 0 20rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
}
.vote-option-input {
  flex: 1;
  height: 80rpx;
  font-size: 26rpx;
  color: #333;
}
.vote-option-delete {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.delete-circle {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #ff5252;
  display: flex;
  align-items: center;
  justify-content: center;
}
.delete-line {
  width: 20rpx;
  height: 4rpx;
  background-color: #fff;
}
.vote-add-option {
  margin: 30rpx 10rpx;
  width: calc(100% - 20rpx);
  height: 80rpx;
  border: 1px dashed #ddd;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}
.vote-add-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}
.vote-add-text {
  font-size: 26rpx;
}
.vote-bottom-btns {
  margin-top: 40rpx;
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
}
.vote-cancel-btn, .vote-confirm-btn {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 40rpx;
}
.vote-cancel-btn {
  color: #666;
  background-color: #f5f5f5;
}
.vote-confirm-btn {
  color: #fff;
  background-color: #000;
}

/* 投票显示样式 */
.vote-display {
  margin: 20rpx 0;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 16rpx;
  width: 100%;
}
.vote-display-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.vote-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}
.vote-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.vote-display-options {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.vote-display-option {
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #333;
  text-align: center;
}

/* 投票显示样式 */
.vote-box {
  width: 100%;
}
.vote-container {
  width: calc(100% - 40rpx);
  margin: 0 20rpx;
  background-color: #f5f5f5;
  border-radius: 16rpx;
  padding: 20rpx;
  position: relative;
}
.vote-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
  position: relative;
}
.vote-title-container {
  display: flex;
  align-items: center;
}
.vote-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}
.vote-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  text-align: left;
}
.vote-delete {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
}
.vote-options {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.vote-option {
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #333;
  text-align: center;
}
.toolbar-label {
  margin-top: 6rpx;
  font-size: 22rpx;
  color: #666;
  text-align: center;
  line-height: 1;
}
.empty-users, .loading-users {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
  color: #999;
  font-size: 26rpx;
}



/* @用户面板样式 - 横向滑动卡片式 */
.mention-user-panel {
  position: fixed;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1rpx solid #e5e5e5;
  z-index: 999;
  padding: 20rpx 0;
  /* 在工具栏上方，工具栏最小高度88rpx + 安全区域 */
  margin-bottom: calc(88rpx + env(safe-area-inset-bottom));
}

/* @用户面板中的关闭按钮定位 */
.mention-user-panel .popup-close {
  position: absolute;
  top: 10rpx;
  right: 20rpx;
  z-index: 1000;
}
.empty-mention-users,
.loading-mention-users {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 横向滚动容器 */
.mention-user-scroll {
  width: 100%;
  height: 120rpx;
  white-space: nowrap;
}

/* 横向用户列表容器 */
.mention-user-list-horizontal {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 120rpx;
  padding: 0 20rpx;
  gap: 20rpx;
}

/* 用户卡片样式 */
.mention-user-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 120rpx;
  flex-shrink: 0;
  position: relative;
  border-radius: 12rpx;
  padding: 8rpx;
}
.mention-user-card:active {
  background: #f5f5f5;
}

/* 用户头像 */
.mention-user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-bottom: 8rpx;
  flex-shrink: 0;
  border: 2rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 用户昵称 */
.mention-user-nickname {
  font-size: 22rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  max-width: 80rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
}


