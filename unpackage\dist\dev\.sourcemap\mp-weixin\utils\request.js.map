{"version": 3, "file": "request.js", "sources": ["utils/request.js"], "sourcesContent": ["// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport appConfig from '@/config/app.js';\nconst {\n\tHTTP_REQUEST_URL,\n\tHEADER,\n\tTOKENNAME,\n\tTIMEOUT\n} = appConfig;\nimport {\n\ttoLogin,\n\tcheckLogin\n} from '../libs/login';\nimport store from '../store';\nimport i18n from './lang.js';\n\n/**\n * 发送请求\n */\nfunction baseRequest(url, method, data, {\n\tnoAuth = false,\n\tnoVerify = false\n}) {\n\tlet Url = HTTP_REQUEST_URL,\n\t\theader = HEADER;\n\n\tif (!noAuth) {\n\t\t//登录过期自动登录\n\t\tif (!store.state.app.token && !checkLogin()) {\n\t\t\ttoLogin();\n\t\t\treturn Promise.reject({\n\t\t\t\tmsg: i18n.t(`未登录`)\n\t\t\t});\n\t\t}\n\t}\n\tif (store.state.app.token) header[TOKENNAME] = 'Bearer ' + store.state.app.token;\n\n\treturn new Promise((reslove, reject) => {\n\t\tif (uni.getStorageSync('locale')) {\n\t\t\theader['Cb-lang'] = uni.getStorageSync('locale')\n\t\t}\n\t\tuni.request({\n\t\t\turl: Url + '/api/' + url,\n\t\t\tmethod: method || 'GET',\n\t\t\theader: header,\n\t\t\tdata: data || {},\n\t\t\ttimeout: TIMEOUT,\n\t\t\tsuccess: (res) => {\n\t\t\t\tif (noVerify)\n\t\t\t\t\treslove(res.data, res);\n\t\t\t\telse if (res.data.status == 200)\n\t\t\t\t\treslove(res.data, res);\n\t\t\t\telse if ([110002, 110003, 110004].indexOf(res.data.status) !== -1) {\n\t\t\t\t\ttoLogin();\n\t\t\t\t\treject(res.data);\n\t\t\t\t} else if (res.data.status == 100103) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: i18n.t(`提示`),\n\t\t\t\t\t\tcontent: res.data.msg,\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tconfirmText: i18n.t(`我知道了`)\n\t\t\t\t\t});\n\t\t\t\t\treject(res.data);\n\t\t\t\t} else {\n\t\t\t\t\t// 统一错误格式，确保返回对象而不是字符串\n\t\t\t\t\tconst errorData = {\n\t\t\t\t\t\tstatus: res.data.status || 400,\n\t\t\t\t\t\tmsg: res.data.msg || i18n.t(`系统错误`),\n\t\t\t\t\t\tdata: res.data.data || null,\n\t\t\t\t\t\tcode: res.data.code || res.data.status || 400\n\t\t\t\t\t};\n\n\t\t\t\t\t// 调试信息\n\t\t\t\t\tconsole.log('API错误响应:', {\n\t\t\t\t\t\turl: url,\n\t\t\t\t\t\tstatus: res.data.status,\n\t\t\t\t\t\tmsg: res.data.msg,\n\t\t\t\t\t\terrorData: errorData\n\t\t\t\t\t});\n\n\t\t\t\t\treject(errorData);\n\t\t\t\t}\n\t\t\t},\n\t\t\tfail: (error) => {\n\t\t\t\t// 统一网络错误格式\n\t\t\t\tconst errorData = {\n\t\t\t\t\tstatus: 0,\n\t\t\t\t\tmsg: i18n.t(`请求失败`),\n\t\t\t\t\tcode: 'NETWORK_ERROR',\n\t\t\t\t\tdata: null,\n\t\t\t\t\terror: error\n\t\t\t\t};\n\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\treject(errorData);\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-PLUS\n\t\t\t\treject(errorData);\n\t\t\t\t// #endif\n\t\t\t}\n\t\t})\n\t});\n}\n\nconst request = {};\n\n['options', 'get', 'post', 'put', 'head', 'delete', 'trace', 'connect'].forEach((method) => {\n\trequest[method] = (api, data, opt) => baseRequest(api, method, data, opt || {})\n});\n\n\n\nexport default request;"], "names": ["appConfig", "store", "checkLogin", "<PERSON><PERSON><PERSON><PERSON>", "i18n", "uni"], "mappings": ";;;;;;AAWA,MAAM;AAAA,EACL;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,IAAIA;AAWJ,SAAS,YAAY,KAAK,QAAQ,MAAM;AAAA,EACvC,SAAS;AAAA,EACT,WAAW;AACZ,GAAG;AACF,MAAI,MAAM,kBACT,SAAS;AAEV,MAAI,CAAC,QAAQ;AAEZ,QAAI,CAACC,YAAK,MAAC,MAAM,IAAI,SAAS,CAACC,WAAAA,cAAc;AAC5CC,iBAAAA;AACA,aAAO,QAAQ,OAAO;AAAA,QACrB,KAAKC,WAAI,KAAC,EAAE,KAAK;AAAA,MACrB,CAAI;AAAA,IACD;AAAA,EACD;AACD,MAAIH,kBAAM,MAAM,IAAI;AAAO,WAAO,SAAS,IAAI,YAAYA,YAAK,MAAC,MAAM,IAAI;AAE3E,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,QAAII,cAAG,MAAC,eAAe,QAAQ,GAAG;AACjC,aAAO,SAAS,IAAIA,oBAAI,eAAe,QAAQ;AAAA,IAC/C;AACDA,kBAAAA,MAAI,QAAQ;AAAA,MACX,KAAK,MAAM,UAAU;AAAA,MACrB,QAAQ,UAAU;AAAA,MAClB;AAAA,MACA,MAAM,QAAQ,CAAE;AAAA,MAChB,SAAS;AAAA,MACT,SAAS,CAAC,QAAQ;AACjB,YAAI;AACH,kBAAQ,IAAI,MAAM,GAAG;AAAA,iBACb,IAAI,KAAK,UAAU;AAC3B,kBAAQ,IAAI,MAAM,GAAG;AAAA,iBACb,CAAC,QAAQ,QAAQ,MAAM,EAAE,QAAQ,IAAI,KAAK,MAAM,MAAM,IAAI;AAClEF,qBAAAA;AACA,iBAAO,IAAI,IAAI;AAAA,QACf,WAAU,IAAI,KAAK,UAAU,QAAQ;AACrCE,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAOD,WAAI,KAAC,EAAE,IAAI;AAAA,YAClB,SAAS,IAAI,KAAK;AAAA,YAClB,YAAY;AAAA,YACZ,aAAaA,WAAI,KAAC,EAAE,MAAM;AAAA,UAChC,CAAM;AACD,iBAAO,IAAI,IAAI;AAAA,QACpB,OAAW;AAEN,gBAAM,YAAY;AAAA,YACjB,QAAQ,IAAI,KAAK,UAAU;AAAA,YAC3B,KAAK,IAAI,KAAK,OAAOA,WAAAA,KAAK,EAAE,MAAM;AAAA,YAClC,MAAM,IAAI,KAAK,QAAQ;AAAA,YACvB,MAAM,IAAI,KAAK,QAAQ,IAAI,KAAK,UAAU;AAAA,UAChD;AAGKC,wBAAAA,MAAA,MAAA,OAAA,0BAAY,YAAY;AAAA,YACvB;AAAA,YACA,QAAQ,IAAI,KAAK;AAAA,YACjB,KAAK,IAAI,KAAK;AAAA,YACd;AAAA,UACN,CAAM;AAED,iBAAO,SAAS;AAAA,QAChB;AAAA,MACD;AAAA,MACD,MAAM,CAAC,UAAU;AAEhB,cAAM,YAAY;AAAA,UACjB,QAAQ;AAAA,UACR,KAAKD,WAAI,KAAC,EAAE,MAAM;AAAA,UAClB,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,QACL;AAMI,eAAO,SAAS;AAAA,MAEhB;AAAA,IACJ,CAAG;AAAA,EACH,CAAE;AACF;AAEK,MAAC,UAAU,CAAG;AAEnB,CAAC,WAAW,OAAO,QAAQ,OAAO,QAAQ,UAAU,SAAS,SAAS,EAAE,QAAQ,CAAC,WAAW;AAC3F,UAAQ,MAAM,IAAI,CAAC,KAAK,MAAM,QAAQ,YAAY,KAAK,QAAQ,MAAM,OAAO,CAAA,CAAE;AAC/E,CAAC;;"}