
.container.data-v-531fb3c5 {
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 导航栏 */
.nav-bar.data-v-531fb3c5 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 999;
}
.nav-back.data-v-531fb3c5 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-531fb3c5 {
  width: 32rpx;
  height: 32rpx;
}
.nav-title.data-v-531fb3c5 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.nav-right.data-v-531fb3c5 {
  width: 60rpx;
}

/* 表单容器 */
.form-container.data-v-531fb3c5 {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
}
.form-item.data-v-531fb3c5 {
  margin-bottom: 40rpx;
  position: relative;
}
.form-item.data-v-531fb3c5:last-child {
  margin-bottom: 0;
}
.form-label.data-v-531fb3c5 {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
  color: #333;
}

/* 上传区域 */
.upload-row.data-v-531fb3c5 {
  display: flex;
  gap: 30rpx;
}
.upload-item.data-v-531fb3c5 {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 头像区域占1份 */
.avatar-item.data-v-531fb3c5 {
  flex: 1;
}

/* 背景区域占3份 */
.background-item.data-v-531fb3c5 {
  flex: 3;
}
.upload-box.data-v-531fb3c5 {
  width: 100%;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f8f8f8;
  border: 2rpx dashed #ddd;
  transition: all 0.3s ease;
}
.upload-box.data-v-531fb3c5:active {
  transform: scale(0.98);
}
.avatar-box.data-v-531fb3c5 {
  border-radius: 20rpx;
}
.background-box.data-v-531fb3c5 {
  border-radius: 12rpx;
}
.upload-image.data-v-531fb3c5 {
  width: 100%;
  height: 100%;
}
.upload-placeholder.data-v-531fb3c5 {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-icon.data-v-531fb3c5 {
  font-size: 48rpx;
  font-weight: 300;
  color: #999;
  margin-bottom: 8rpx;
}
.upload-text.data-v-531fb3c5 {
  font-size: 20rpx;
  color: #999;
  text-align: center;
}
.upload-label.data-v-531fb3c5 {
  font-size: 24rpx;
  color: #666;
  margin-top: 16rpx;
  text-align: center;
}

/* 输入框 */
.form-input.data-v-531fb3c5 {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  background-color: #f8f8f8;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}
.form-input.data-v-531fb3c5:focus {
  background-color: #fff;
  border-color: #333;
}
.form-textarea.data-v-531fb3c5 {
  width: 100%;
  min-height: 160rpx;
  border-radius: 12rpx;
  background-color: #f8f8f8;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  line-height: 1.6;
}
.form-textarea.data-v-531fb3c5:focus {
  background-color: #fff;
  border-color: #333;
}
.form-count.data-v-531fb3c5 {
  position: absolute;
  right: 24rpx;
  bottom: 16rpx;
  font-size: 24rpx;
  color: #999;
}

/* 提交按钮 */
.btn-container.data-v-531fb3c5 {
  margin: 40rpx 20rpx;
}
.submit-btn.data-v-531fb3c5 {
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
  background: linear-gradient(135deg, #333 0%, #555 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 48rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}
.submit-btn.data-v-531fb3c5:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}
.submit-btn[disabled].data-v-531fb3c5 {
  background: #ccc;
  color: #999;
  box-shadow: none;
  transform: none;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
.upload-row.data-v-531fb3c5 {
    gap: 20rpx;
}
.upload-box.data-v-531fb3c5 {
    height: 140rpx;
}
.upload-icon.data-v-531fb3c5 {
    font-size: 40rpx;
}
.upload-text.data-v-531fb3c5 {
    font-size: 18rpx;
}
}
