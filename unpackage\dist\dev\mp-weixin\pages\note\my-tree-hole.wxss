
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.bar-box .bar-back {
  padding: 0 30rpx;
  width: 34rpx;
  height: 100%;
}
.bar-box .bar-title {
  max-width: 60%;
  font-size: 32rpx;
  font-weight: 700;
  flex: 1;
  text-align: center;
}
.nav-box {
  width: 100%;
  height: 80rpx;
}
.nav-box .nav-item {
  padding: 0 30rpx;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.nav-box .nav-item text {
  font-weight: 700;
  transition: all .3s ease-in-out;
}
.nav-box .nav-line {
  position: absolute;
  bottom: 12rpx;
  width: 18rpx;
  height: 6rpx;
  border-radius: 6rpx;
  background: #000;
  transition: opacity .3s ease-in-out;
}
.content-box {
  width: calc(100% - 60rpx);
  padding: 30rpx;
}
.notification-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.notification-icon {
  font-size: 32rpx;
}

/* 加载中状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60rpx;
  margin-bottom: 20rpx;
}
.loading-indicator {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.paper-list {
  width: 100%;
}

/* 纸条项 */
.paper-item {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 24rpx;
}
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  background: #f0f0f0;
}
.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.user-details {
  flex: 1;
}
.username {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.user-meta {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.gender-icon {
  color: #666;
  font-size: 24rpx;
}
.age {
  color: #666;
  font-size: 24rpx;
}
.type-tag {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.type-1 { background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
}
.type-2 { background: linear-gradient(135deg, #EC4899 0%, #BE185D 100%);
}
.type-3 { background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
}
.type-4 { background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
}
.type-icon,
.type-text {
  color: #fff;
  font-size: 24rpx;
  font-weight: 500;
}

/* 纸条内容 */
.paper-content {
  margin-bottom: 24rpx;
}
.content-text {
  color: #333;
  font-size: 28rpx;
  line-height: 1.6;
}

/* 状态信息 */
.paper-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.status-text {
  color: #999;
  font-size: 24rpx;
}
.time-text {
  color: #999;
  font-size: 24rpx;
}

/* 回应信息 */
.response-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  margin-bottom: 24rpx;
}
.response-text {
  color: #333;
  font-size: 28rpx;
}
.arrow-icon {
  color: #999;
  font-size: 32rpx;
}

/* 回应按钮 */
.reply-btn {
  width: 100%;
  height: 72rpx;
  background: #f8f9fa;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.reply-btn:active {
  background: #e9ecef;
  transform: scale(0.98);
}
.reply-text {
  color: #666;
  font-size: 28rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  color: #666;
  font-size: 28rpx;
}

/* 空状态 */
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-box image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-box .e1 {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.empty-box .e2 {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 30rpx;
}
.empty-btn {
  padding: 24rpx 48rpx;
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  border-radius: 48rpx;
  box-shadow: 0 4rpx 16rpx rgba(139, 92, 246, 0.3);
}
.empty-btn-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
}
.df {
  display: flex;
  align-items: center;
}
.bfw {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,.8);
}
.bfh {
  background: #000;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 700;
}
.tips-box {
  justify-content: center;
  width: 100%;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

