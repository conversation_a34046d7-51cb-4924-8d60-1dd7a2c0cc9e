
.pl-sty {
		color: #999999;
		font-size: 30rpx;
}

.product-window.on.data-v-f7f0d621 {
  transform: translate3d(0, 0, 0);
}
.mask.data-v-f7f0d621 {
  z-index: 99;
}
.product-window.data-v-f7f0d621 {
  position: fixed;
  bottom: 0;
  width: 100%;
  left: 0;
  background-color: #fff;
  z-index: 1000;
  border-radius: 20rpx 20rpx 0 0;
  transform: translate3d(0, 100%, 0);
  transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
  padding: 38rpx 40rpx;
  padding-bottom: 80rpx;
  padding-bottom: calc(80rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(80rpx + env(safe-area-inset-bottom));
}
.product-window .icon-guanbi.data-v-f7f0d621 {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  font-size: 24rpx;
  font-weight: bold;
  color: #999;
}
.product-window .mp-data.data-v-f7f0d621 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.product-window .mp-data .mp-name.data-v-f7f0d621 {
  font-size: 28rpx;
  font-weight: bold;
  color: #000000;
}
.product-window .mp-data image.data-v-f7f0d621 {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}
.product-window .trip-msg.data-v-f7f0d621 {
  padding-bottom: 32rpx;
  border-bottom: 1px solid #F5F5F5;
}
.product-window .trip-msg .title.data-v-f7f0d621 {
  font-size: 30rpx;
  font-weight: bold;
  color: #000;
  margin-bottom: 6rpx;
}
.product-window .trip-msg .trip.data-v-f7f0d621 {
  font-size: 26rpx;
  color: #777777;
}
.product-window .edit.data-v-f7f0d621 {
  border-bottom: 1px solid #F5F5F5;
}
.product-window .edit .avatar.data-v-f7f0d621 {
  border-bottom: 1px solid #F5F5F5;
}
.product-window .edit .nickname .input.data-v-f7f0d621 {
  width: 100%;
}
.product-window .edit .nickname input.data-v-f7f0d621 {
  height: 80rpx;
}
.product-window .edit .edit-box.data-v-f7f0d621 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 30rpx;
  padding: 22rpx 0;
}
.product-window .edit .edit-box .left.data-v-f7f0d621 {
  display: flex;
  align-items: center;
  flex: 1;
}
.product-window .edit .edit-box .left .head.data-v-f7f0d621 {
  color: rgba(0, 0, 0, 0.9);
  white-space: nowrap;
  margin-right: 60rpx;
}
.product-window .edit .edit-box .left button.data-v-f7f0d621 {
  flex: 1;
  display: flex;
  align-items: center;
}
.product-window .edit .edit-box image.data-v-f7f0d621 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 6rpx;
}
.product-window .edit .icon-xiangyou.data-v-f7f0d621 {
  color: #cfcfcf;
}
.product-window .bottom.data-v-f7f0d621 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.product-window .bottom .save.data-v-f7f0d621 {
  border: 1px solid #F5F5F5;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 368rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-top: 52rpx;
  background-color: #F5F5F5;
  color: #ccc;
  font-size: 30rpx;
  font-weight: bold;
}
.product-window .bottom .save.open.data-v-f7f0d621 {
  border: 1px solid #fff;
  background-color: #07C160;
  color: #fff;
}