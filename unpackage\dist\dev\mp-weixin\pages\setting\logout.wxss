
.container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
}
.title-box {
  font-size: 40rpx;
  font-weight: 700;
}
/* 内容区域 - 使用原生滚动，性能更好 */
.content-wrapper {
  flex: 1;
  padding: 0 30rpx;
  box-sizing: border-box;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动流畅度 */
}
/* 加载状态 */
.loading-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  min-height: 200rpx;
}
.loading-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #666;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text {
  font-size: 26rpx;
  color: #666;
}
/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}
.user-avatar {
  width: 76rpx;
  height: 76rpx;
  margin-right: 20rpx;
  border-radius: 50%;
}
.user-name {
  font-size: 28rpx;
  font-weight: 500;
}
/* 协议内容样式 */
.agreement-content {
  margin: 20rpx 0 40rpx;
  font-size: 26rpx;
  line-height: 1.6;
  color: #333;
}
/* 底部安全区域 */
.bottom-safe-area {
  height: 180rpx;
  width: 100%;
}
.footer-box {
  position: fixed;
  z-index: 99;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
  padding-bottom: max(env(safe-area-inset-bottom), 30rpx);
}
.notice {
  color: #999;
  font-size: 24rpx;
  text-align: center;
  margin-bottom: 20rpx;
}
.btn-box {
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 24rpx;
  color: #fff;
  font-weight: 700;
  background: #000;
  border-radius: 100rpx;
}
.bfw {
  background: #fff;
}
.bUp {
  box-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);
}
.df {
  display: flex;
  align-items: center;
}
.tips-box {
  padding: 20rpx 30rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  justify-content: center;
}
.tips-item {
  color: #fff;
  font-size: 28rpx;
  font-weight: 700;
}
