
.content-box {
  width: calc(100% - 60rpx);
  padding: 30rpx;
  background: #f8f9fa;
  min-height: 100vh;
}

/* 加载中状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60rpx;
  margin-bottom: 20rpx;
}
.loading-indicator {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}

/* 签到记录列表样式 */
.sign-list {
  width: 100%;
}
.month-group {
  margin-bottom: 40rpx;
}
.month-title {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
}
.sign-items {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}
.sign-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.sign-item:last-child {
  border-bottom: none;
}
.sign-info {
  flex: 1;
}
.sign-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  line-height: 1.4;
}
.sign-date {
  font-size: 26rpx;
  color: #999;
}
.sign-reward {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.reward-text {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}
.reward-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 底部加载状态 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
}
.loading-text,
.no-more-text,
.load-more-text {
  font-size: 28rpx;
  color: #999;
}
.load-more-text {
  color: #007aff;
}

/* 空状态 */
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.empty-box image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-box .e1 {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.empty-box .e2 {
  font-size: 24rpx;
  color: #999;
}
.df {
  display: flex;
  align-items: center;
}
.bfh {
  background: #000;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 700;
}
.tips-box {
  justify-content: center;
  width: 100%;
}
