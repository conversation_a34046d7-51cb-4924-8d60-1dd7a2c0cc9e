<view wx:if="{{a}}" class="comment-input-wrapper"><view class="comment-input-box"><block wx:if="{{r0}}"><editor id="commentEditor" class="comment-editor" placeholder="{{b}}" read-only="{{c}}" bindready="{{d}}" bindfocus="{{e}}" bindblur="{{f}}" bindinput="{{g}}" aria-label="评论输入框" role="textbox"></editor></block></view><view wx:if="{{h}}" class="selected-image-preview"><image class="selected-image" src="{{i}}" mode="aspectFill" bindtap="{{j}}"></image><view class="delete-image" bindtap="{{k}}">×</view></view><view class="comment-tools-bar"><view class="tools-left"><view class="tool-item" bindtap="{{m}}"><image src="{{l}}"></image></view><view class="tool-item" bindtap="{{o}}"><image src="{{n}}"></image></view><view class="tool-item" bindtap="{{q}}"><image src="{{p}}"></image></view></view><view class="{{s}}" catchtap="{{t}}" catchtap="{{v}}" catchtouchend="{{w}}">{{r}}</view></view><emoji-panel wx:if="{{A}}" bindselect="{{x}}" bindselectGif="{{y}}" binddelete="{{z}}" u-i="0f1cc78f-0" bind:__l="__l" u-p="{{A}}"></emoji-panel><uni-popup wx:if="{{E}}" class="r" u-s="{{['d']}}" u-r="atUserPopup" u-i="0f1cc78f-1" bind:__l="__l" u-p="{{E}}"><view class="at-user-popup"><view class="at-user-header"><text>选择要@的用户</text><view class="close-btn" bindtap="{{B}}">关闭</view></view><scroll-view scroll-y class="at-user-list"><view wx:for="{{C}}" wx:for-item="user" wx:key="c" class="at-user-item" bindtap="{{user.d}}"><image class="at-user-avatar" src="{{user.a}}"></image><text class="at-user-name">{{user.b}}</text></view></scroll-view></view></uni-popup></view>