"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const config_api = require("../../config/api.js");
const common_assets = require("../../common/assets.js");
const money = () => "../../components/money/money.js";
const app = getApp();
const _sfc_main = {
  components: {
    money
  },
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      appCard: app.globalData.isCard || false,
      list: [],
      listIdx: -1,
      quantity: 0,
      goodsAmount: "0.00",
      discountAmount: "0.00",
      orderAmount: "0.00",
      cardAmount: 0,
      allCheck: 0,
      checkCount: 0,
      sumCount: 0,
      listProductIdx: 0,
      productList: [{ stock: 1 }],
      productIdx: 0,
      goodsName: "",
      isEmpty: false,
      tipsTitle: "",
      mockData: {
        cartItems: [
          {
            id: 1,
            goods_id: 101,
            goods_name: "轻奢真皮小白鞋",
            goods_product_id: 1001,
            check: 1,
            quantity: 2,
            status: 1,
            status_str: "",
            is_quantity: false,
            product: {
              id: 1001,
              name: "白色 39码",
              img: "/static/img/avatar.png",
              price: "299.00",
              line_price: "399.00",
              stock: 10
            }
          },
          {
            id: 2,
            goods_id: 102,
            goods_name: "夏季薄款T恤",
            goods_product_id: 1002,
            check: 1,
            quantity: 1,
            status: 1,
            status_str: "",
            is_quantity: false,
            product: {
              id: 1002,
              name: "蓝色 L码",
              img: "/static/img/avatar.png",
              price: "89.00",
              line_price: "129.00",
              stock: 5
            }
          },
          {
            id: 3,
            goods_id: 103,
            goods_name: "轻便双肩包",
            goods_product_id: 1003,
            check: 0,
            quantity: 1,
            status: 1,
            status_str: "",
            is_quantity: false,
            product: {
              id: 1003,
              name: "黑色 标准款",
              img: "/static/img/avatar.png",
              price: "199.00",
              line_price: "259.00",
              stock: 8
            }
          },
          {
            id: 4,
            goods_id: 104,
            goods_name: "经典牛仔裤",
            goods_product_id: 1004,
            check: 0,
            quantity: 1,
            status: 2,
            status_str: "已售罄",
            is_quantity: false,
            product: {
              id: 1004,
              name: "深蓝 30码",
              img: "/static/img/avatar.png",
              price: "159.00",
              line_price: "199.00",
              stock: 0
            }
          }
        ],
        productVariants: {
          101: [
            {
              id: 1001,
              name: "白色 39码",
              img: "/static/img/avatar.png",
              price: "299.00",
              stock: 10
            },
            {
              id: 1005,
              name: "白色 40码",
              img: "/static/img/avatar.png",
              price: "299.00",
              stock: 8
            },
            {
              id: 1006,
              name: "黑色 39码",
              img: "/static/img/avatar.png",
              price: "319.00",
              stock: 5
            }
          ]
        },
        cards: [
          {
            card: {
              price: "30.00",
              name: "满300减30券"
            }
          }
        ]
      }
    };
  },
  onLoad() {
    this.goodsCartList();
  },
  onShow() {
    if (app.globalData.isCenterPage) {
      this.goodsCartList();
      app.globalData.isCenterPage = false;
    }
  },
  methods: {
    goodsCartList() {
      let that = this;
      that.isEmpty = false;
      if (config_api.api$1 && config_api.api$1.api && config_api.api$1.api.goodsCartListUrl) {
        utils_request.request(config_api.api$1.api.goodsCartListUrl).then(function(res) {
          if (res.code == 200 && res.data.length) {
            that.list = res.data;
            that.calculate();
          } else {
            that.isEmpty = true;
          }
        });
      } else {
        setTimeout(() => {
          if (that.mockData.cartItems.length) {
            that.list = JSON.parse(JSON.stringify(that.mockData.cartItems));
            that.calculate();
          } else {
            that.isEmpty = true;
          }
        }, 500);
      }
    },
    openProductClick(e) {
      let that = this;
      let index = this.getIndex(e.currentTarget.dataset.id);
      that.listIdx = index;
      that.goodsName = that.list[index].goods_name;
      common_vendor.index.showLoading({
        mask: true
      });
      if (config_api.api$1 && config_api.api$1.api && config_api.api$1.api.goodsProductUrl) {
        utils_request.request(config_api.api$1.api.goodsProductUrl, {
          id: that.list[index].goods_id
        }).then(function(res) {
          common_vendor.index.hideLoading();
          that.productList = res.data;
          for (let i in that.productList) {
            if (that.productList[i].id == that.list[index].goods_product_id) {
              that.listProductIdx = parseInt(i);
              that.productIdx = parseInt(i);
              break;
            }
          }
          that.$refs.goodsPopup.open();
        });
      } else {
        setTimeout(() => {
          common_vendor.index.hideLoading();
          const goodsId = that.list[index].goods_id;
          if (that.mockData.productVariants[goodsId]) {
            that.productList = that.mockData.productVariants[goodsId];
          } else {
            that.productList = [that.list[index].product];
          }
          for (let i in that.productList) {
            if (that.productList[i].id == that.list[index].goods_product_id) {
              that.listProductIdx = parseInt(i);
              that.productIdx = parseInt(i);
              break;
            }
          }
          that.$refs.goodsPopup.open();
        }, 300);
      }
    },
    cartProductClick() {
      let that = this;
      if (that.listProductIdx != that.productIdx) {
        let product = that.productList[that.productIdx];
        if (product.stock <= 0) {
          return that.opTipsPopup("该款式已售罄暂时无法购买！");
        }
        common_vendor.index.showLoading({
          mask: true
        });
        if (config_api.api$1 && config_api.api$1.api && config_api.api$1.api.goodsSaveCartUrl) {
          utils_request.request(config_api.api$1.api.goodsSaveCartUrl, {
            type: 4,
            id: that.list[that.listIdx].id,
            goods_product_id: product.id,
            quantity: that.list[that.listIdx].quantity
          }, "POST").then(function(res) {
            common_vendor.index.hideLoading();
            app.globalData.isCenterPage = true;
            that.goodsCartList();
            that.$refs.goodsPopup.close();
          });
        } else {
          setTimeout(() => {
            common_vendor.index.hideLoading();
            that.list[that.listIdx].product = product;
            that.list[that.listIdx].goods_product_id = product.id;
            app.globalData.isCenterPage = true;
            that.calculate();
            that.$refs.goodsPopup.close();
          }, 300);
        }
      } else {
        that.$refs.goodsPopup.close();
      }
    },
    cartCheck(e) {
      let that = this;
      let index = this.getIndex(e.currentTarget.dataset.id);
      let check = e.currentTarget.dataset.check;
      if (index >= 0 && that.list[index].status == 2) {
        return that.opTipsPopup("该款式已售罄暂时无法购买！");
      } else if (index >= 0 && that.list[index].status == 3) {
        return that.opTipsPopup("该款式已下架无法购买！");
      }
      if (config_api.api$1 && config_api.api$1.api && config_api.api$1.api.goodsSaveCartUrl) {
        utils_request.request(config_api.api$1.api.goodsSaveCartUrl, {
          type: index >= 0 ? 1 : 2,
          id: index >= 0 ? that.list[index].id : 0,
          check
        }, "POST").then(function(res) {
          if (index >= 0) {
            that.list[index].check = that.list[index].check == 1 ? 0 : 1;
          } else {
            that.allCheck = that.allCheck == 1 ? 0 : 1;
            for (let i in that.list) {
              if (that.list[i].status == 1) {
                that.list[i].check = that.allCheck;
              }
            }
          }
          that.calculate();
        });
      } else {
        setTimeout(() => {
          if (index >= 0) {
            that.list[index].check = that.list[index].check == 1 ? 0 : 1;
          } else {
            that.allCheck = that.allCheck == 1 ? 0 : 1;
            for (let i in that.list) {
              if (that.list[i].status == 1) {
                that.list[i].check = that.allCheck;
              }
            }
          }
          that.calculate();
        }, 200);
      }
    },
    quantityBtn(e) {
      let that = this;
      let index = this.getIndex(e.currentTarget.dataset.id);
      let type = e.currentTarget.dataset.type;
      let isNeedUpdate = false;
      if (that.list[index].status == 2) {
        return that.opTipsPopup("该款式已售罄暂时无法编辑数量！");
      } else if (that.list[index].status == 3) {
        return that.opTipsPopup("该款式已下架无法编辑数量！");
      } else if (type == 0 && parseInt(that.quantity) <= 1) {
        return that.opTipsPopup("就一件了，数量不能再少啦！");
      }
      if (parseInt(that.quantity) > parseInt(that.list[index].product.stock)) {
        isNeedUpdate = true;
        that.quantity = parseInt(that.list[index].product.stock);
        that.opTipsPopup("购买数量已达到最大库存上限！");
      } else if (that.list[index].status == 1 && that.quantity && that.quantity != 0) {
        isNeedUpdate = true;
        if (type == 0) {
          that.quantity = parseInt(that.quantity) - 1;
        }
        if (type == 1 && parseInt(that.quantity) < parseInt(that.list[index].product.stock)) {
          that.quantity = parseInt(that.quantity) + 1;
        }
      }
      if (isNeedUpdate) {
        if (config_api.api$1 && config_api.api$1.api && config_api.api$1.api.goodsSaveCartUrl) {
          utils_request.request(config_api.api$1.api.goodsSaveCartUrl, {
            type: 3,
            id: that.list[index].id,
            quantity: parseInt(that.quantity)
          }, "POST").then(function(res) {
            that.list[index].quantity = parseInt(that.quantity);
            that.calculate();
          });
        } else {
          setTimeout(() => {
            that.list[index].quantity = parseInt(that.quantity);
            that.calculate();
          }, 200);
        }
      }
    },
    cartDelClick() {
      let that = this;
      common_vendor.index.showLoading({
        mask: true
      });
      if (config_api.api$1 && config_api.api$1.api && config_api.api$1.api.goodsCartDelUrl) {
        utils_request.request(config_api.api$1.api.goodsCartDelUrl, {
          id: that.list[that.listIdx].id
        }, "POST").then(function(res) {
          common_vendor.index.hideLoading();
          app.globalData.isCenterPage = true;
          that.list.splice(that.listIdx, 1);
          if (that.list.length > 0) {
            that.calculate();
          } else {
            that.isEmpty = true;
          }
          that.$refs.morePopup.close();
        });
      } else {
        setTimeout(() => {
          common_vendor.index.hideLoading();
          app.globalData.isCenterPage = true;
          that.list.splice(that.listIdx, 1);
          if (that.list.length > 0) {
            that.calculate();
          } else {
            that.isEmpty = true;
          }
          that.$refs.morePopup.close();
        }, 300);
      }
    },
    calculate() {
      let that = this;
      let goodsAmount = 0;
      let discountAmount = 0;
      let orderAmount = 0;
      let checkCount = 0;
      let sumCount = 0;
      let invalidCount = 0;
      for (let item of that.list) {
        if (item.check && item.status == 1) {
          checkCount += 1;
          sumCount += parseInt(item.quantity);
          goodsAmount += parseFloat(item.product.line_price * item.quantity);
          discountAmount += parseFloat((item.product.line_price - item.product.price) * item.quantity);
          orderAmount += parseFloat(item.product.price * item.quantity);
        } else if (item.status == 2 || item.status == 3) {
          invalidCount += 1;
        }
      }
      that.goodsAmount = goodsAmount.toFixed(2);
      that.discountAmount = discountAmount.toFixed(2);
      that.orderAmount = orderAmount.toFixed(2);
      that.checkCount = checkCount;
      that.sumCount = sumCount;
      if (that.list.length == checkCount + invalidCount && checkCount != 0) {
        that.allCheck = 1;
      } else {
        that.allCheck = 0;
      }
      that.useCard();
    },
    useCard() {
      let that = this;
      if (config_api.api$1 && config_api.api$1.api && config_api.api$1.api.useCardUrl) {
        utils_request.request(config_api.api$1.api.useCardUrl, {
          product_id: 0,
          amount: that.orderAmount
        }, "POST").then(function(res) {
          if (res.code == 200 && res.data[0]) {
            that.cardAmount = res.data[0].card.price;
            let newOrderAmount = that.orderAmount - res.data[0].card.price;
            that.orderAmount = newOrderAmount.toFixed(2);
          }
        });
      } else {
        setTimeout(() => {
          if (parseFloat(that.orderAmount) >= 300) {
            that.cardAmount = that.mockData.cards[0].card.price;
            let newOrderAmount = parseFloat(that.orderAmount) - parseFloat(that.cardAmount);
            that.orderAmount = newOrderAmount.toFixed(2);
          } else {
            that.cardAmount = 0;
          }
        }, 200);
      }
    },
    openQuantity(e) {
      let index = this.getIndex(e.currentTarget.dataset.id);
      for (let i in this.list) {
        if (index != i) {
          this.list[i].is_quantity = false;
        }
      }
      this.quantity = this.list[index].quantity;
      this.list[index].is_quantity = true;
    },
    paramClick(e) {
      this.productIdx = e.currentTarget.dataset.idx;
    },
    openMore(e) {
      this.listIdx = this.getIndex(e.currentTarget.dataset.id);
      this.$refs.morePopup.open();
    },
    closePopupClick(type) {
      if (type == 1) {
        this.$refs.goodsPopup.close();
      } else if (type == 2) {
        this.$refs.morePopup.close();
      }
    },
    getIndex(id) {
      for (let i in this.list) {
        if (this.list[i].id == id) {
          return i;
        }
      }
      return -1;
    },
    imgParamTap(e) {
      let url = e.currentTarget.dataset.url;
      common_vendor.index.previewImage({
        current: url,
        urls: [url]
      });
    },
    navigateToFun(e) {
      let url = e.currentTarget.dataset.url;
      let pay = e.currentTarget.dataset.pay;
      if (pay && this.checkCount <= 0) {
        return this.opTipsPopup("没有可结算的商品！");
      }
      if (pay && this.orderAmount <= 0) {
        return this.opTipsPopup("结算金额不能小于0.01！");
      }
      common_vendor.index.navigateTo({
        url: "/pages/" + url
      });
    },
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "/pages/tabbar/shop"
        });
      }
    },
    opTipsPopup(msg) {
      let that = this;
      that.tipsTitle = msg;
      that.$refs.tipsPopup.open();
      setTimeout(function() {
        that.$refs.tipsPopup.close();
      }, 2e3);
    }
  }
};
if (!Array) {
  const _component_money = common_vendor.resolveComponent("money");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_money + _easycom_uni_popup2)();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: $data.titleBarHeight + "px",
    c: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    d: $data.statusBarHeight + "px",
    e: $data.isEmpty
  }, $data.isEmpty ? {
    f: common_assets._imports_1$5
  } : common_vendor.e({
    g: common_vendor.f($data.list, (item, index, i0) => {
      return common_vendor.e({
        a: item.check == 1 ? "/static/img/c1.png" : "/static/img/c.png",
        b: common_vendor.o((...args) => $options.cartCheck && $options.cartCheck(...args), index),
        c: item.check,
        d: item.id,
        e: item.product.img,
        f: item.status_str
      }, item.status_str ? {
        g: common_vendor.t(item.status_str)
      } : {}, {
        h: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), index),
        i: "goods/details?id=" + item.goods_id,
        j: common_vendor.t(item.goods_name),
        k: common_vendor.t(item.product.name),
        l: common_vendor.o((...args) => $options.openProductClick && $options.openProductClick(...args), index),
        m: item.id,
        n: common_vendor.o((...args) => $options.openMore && $options.openMore(...args), index),
        o: item.id,
        p: "6dc304c3-0-" + i0,
        q: common_vendor.p({
          price: item.product.price
        }),
        r: item.is_quantity
      }, item.is_quantity ? {
        s: $data.quantity > 1 ? "#000" : "#ccc",
        t: common_vendor.o((...args) => $options.quantityBtn && $options.quantityBtn(...args), index),
        v: item.id,
        w: common_vendor.o((...args) => $options.quantityBtn && $options.quantityBtn(...args), index),
        x: item.id,
        y: $data.quantity,
        z: common_vendor.o(($event) => $data.quantity = $event.detail.value, index),
        A: $data.quantity < item.product.stock ? "#000" : "#ccc",
        B: common_vendor.o((...args) => $options.quantityBtn && $options.quantityBtn(...args), index),
        C: item.id
      } : {
        D: common_vendor.t(item.quantity),
        E: common_vendor.o((...args) => $options.openQuantity && $options.openQuantity(...args), index),
        F: item.id
      }, {
        G: index
      });
    }),
    h: common_assets._imports_1$4,
    i: common_assets._imports_3$10,
    j: common_vendor.t($data.sumCount),
    k: common_vendor.p({
      price: $data.goodsAmount,
      qs: "28rpx",
      ts: "18rpx"
    }),
    l: common_vendor.p({
      price: $data.discountAmount,
      cor: "#999",
      qs: "28rpx",
      ts: "18rpx"
    }),
    m: $data.appCard
  }, $data.appCard ? {
    n: $data.cardAmount ? "#FA5150" : "#999",
    o: common_vendor.p({
      price: $data.cardAmount,
      cor: $data.cardAmount ? "#FA5150" : "#999",
      qs: "28rpx",
      ts: "18rpx"
    })
  } : {}, {
    p: common_vendor.p({
      price: 0,
      qs: "28rpx",
      ts: "18rpx"
    }),
    q: common_vendor.p({
      price: $data.orderAmount,
      qs: "28rpx",
      ts: "18rpx"
    })
  }), {
    r: $data.statusBarHeight + $data.titleBarHeight + "px",
    s: !$data.isEmpty
  }, !$data.isEmpty ? common_vendor.e({
    t: $data.allCheck == 1 ? "/static/img/c1.png" : "/static/img/c.png",
    v: common_vendor.o((...args) => $options.cartCheck && $options.cartCheck(...args)),
    w: $data.allCheck,
    x: $data.orderAmount > 0
  }, $data.orderAmount > 0 ? {
    y: common_vendor.t($data.orderAmount)
  } : {}, {
    z: common_vendor.n($data.checkCount > 0 && "act"),
    A: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  }) : {}, {
    B: common_vendor.t($data.goodsName),
    C: common_assets._imports_0$4,
    D: common_vendor.o(($event) => $options.closePopupClick(1)),
    E: common_vendor.f($data.productList, (item, index, i0) => {
      return {
        a: item.img,
        b: common_vendor.o((...args) => $options.imgParamTap && $options.imgParamTap(...args), index),
        c: item.img,
        d: common_vendor.t(item.name),
        e: common_vendor.t(item.price),
        f: index,
        g: $data.productIdx == index ? "#000" : "#f8f8f8",
        h: common_vendor.o((...args) => $options.paramClick && $options.paramClick(...args), index),
        i: index
      };
    }),
    F: common_assets._imports_1$14,
    G: common_vendor.t($data.productList[$data.productIdx].stock > 0 ? "确定" : "暂无库存"),
    H: common_vendor.n($data.productList[$data.productIdx].stock > 0 && "act"),
    I: common_vendor.o((...args) => $options.cartProductClick && $options.cartProductClick(...args)),
    J: common_vendor.sr("goodsPopup", "6dc304c3-6"),
    K: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    L: common_assets._imports_6$4,
    M: common_vendor.o((...args) => $options.cartDelClick && $options.cartDelClick(...args)),
    N: common_vendor.o(($event) => $options.closePopupClick(2)),
    O: common_vendor.sr("morePopup", "6dc304c3-7"),
    P: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    }),
    Q: common_vendor.t($data.tipsTitle),
    R: common_vendor.sr("tipsPopup", "6dc304c3-8"),
    S: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/goods/cart.js.map
