"use strict";
const common_vendor = require("../../../../common/vendor.js");
const uniTransition = () => "../../../uni-transition/components/uni-transition/uni-transition.js";
const _sfc_main = {
  name: "uniPopup",
  components: {
    uniTransition
  },
  emits: ["change", "maskClick"],
  props: {
    animation: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: "center"
    },
    isMaskClick: {
      type: Boolean,
      default: null
    },
    maskClick: {
      type: Boolean,
      default: null
    },
    backgroundColor: {
      type: String,
      default: "none"
    },
    safeArea: {
      type: Boolean,
      default: true
    },
    maskBackgroundColor: {
      type: String,
      default: "rgba(0, 0, 0, 0.4)"
    },
    borderRadius: {
      type: String,
      default: "0"
    }
  },
  watch: {
    type: {
      handler: function(type) {
        if (this.config[type]) {
          this[this.config[type]](true);
        }
      },
      immediate: true
    },
    isDesktop: {
      handler: function(newVal) {
        if (this.config[newVal]) {
          this[this.config[this.type]](true);
        }
      },
      immediate: true
    },
    maskClick: {
      handler: function(val) {
        this.mkclick = val;
      },
      immediate: true
    },
    isMaskClick: {
      handler: function(val) {
        this.mkclick = val;
      },
      immediate: true
    },
    showPopup(show) {
    }
  },
  data() {
    return {
      duration: 300,
      ani: [],
      showPopup: false,
      showTrans: false,
      popupWidth: 0,
      popupHeight: 0,
      config: {
        top: "top",
        bottom: "bottom",
        center: "center",
        left: "left",
        right: "right",
        message: "top",
        dialog: "center",
        share: "bottom"
      },
      maskClass: {
        position: "fixed",
        bottom: 0,
        top: 0,
        left: 0,
        right: 0,
        backgroundColor: "rgba(0, 0, 0, 0.4)"
      },
      transClass: {
        position: "fixed",
        left: 0,
        right: 0,
        backgroundColor: "transparent",
        borderRadius: "0"
      },
      maskShow: true,
      mkclick: true,
      popupstyle: "top"
    };
  },
  computed: {
    getStyles() {
      let styles = {
        backgroundColor: this.bg
      };
      styles = Object.assign(styles, {
        borderRadius: this.borderRadius
      });
      return styles;
    },
    isDesktop() {
      return this.popupWidth >= 500 && this.popupHeight >= 500;
    },
    bg() {
      if (this.backgroundColor === "" || this.backgroundColor === "none") {
        return "transparent";
      }
      return this.backgroundColor;
    }
  },
  mounted() {
    const info = common_vendor.index.getSystemInfoSync();
    this.popupWidth = info.windowWidth;
    this.popupHeight = info.windowHeight + (info.windowTop || 0);
    if (info.safeArea && this.safeArea) {
      this.safeAreaInsets = info.screenHeight - info.safeArea.bottom;
    } else {
      this.safeAreaInsets = 0;
    }
  },
  unmounted() {
    this.setH5Visible();
  },
  activated() {
    this.setH5Visible(!this.showPopup);
  },
  deactivated() {
    this.setH5Visible(true);
  },
  created() {
    if (this.isMaskClick === null && this.maskClick === null) {
      this.mkclick = true;
    } else {
      this.mkclick = this.isMaskClick !== null ? this.isMaskClick : this.maskClick;
    }
    if (this.animation) {
      this.duration = 300;
    } else {
      this.duration = 0;
    }
    this.messageChild = null;
    this.clearPropagation = false;
    this.maskClass.backgroundColor = this.maskBackgroundColor;
  },
  methods: {
    setH5Visible(visible = true) {
    },
    closeMask() {
      this.maskShow = false;
    },
    disableMask() {
      this.mkclick = false;
    },
    clear(e) {
      e.stopPropagation();
      this.clearPropagation = true;
    },
    open(type) {
      if (this.showPopup) {
        return;
      }
      let valid = ["top", "center", "bottom", "left", "right", "message", "dialog", "share"];
      if (type && valid.indexOf(type) !== -1) {
        this.type = type;
      }
      if (this.config[this.type]) {
        this[this.config[this.type]]();
        this.$emit("change", {
          show: true,
          type: this.type
        });
      } else {
        common_vendor.index.__f__("error", "at uni_modules/uni-popup/components/uni-popup/uni-popup.vue:213", "缺少类型：", this.type);
      }
    },
    close(type) {
      this.showTrans = false;
      this.$emit("change", {
        show: false,
        type: this.type
      });
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.showPopup = false;
      }, 300);
    },
    touchstart() {
      this.clearPropagation = false;
    },
    onTap() {
      if (this.clearPropagation) {
        this.clearPropagation = false;
        return;
      }
      this.$emit("maskClick");
      if (this.mkclick) {
        this.close();
      }
    },
    /**
     * 顶部弹出样式处理
     */
    top(type) {
      this.popupstyle = this.isDesktop ? "fixforpc-top" : "top";
      this.ani = ["slide-top"];
      this.transClass = {
        position: "fixed",
        left: 0,
        right: 0,
        backgroundColor: this.bg,
        borderRadius: this.borderRadius || "0"
      };
      if (!type) {
        this.showPopup = true;
        this.showTrans = true;
        this.$nextTick(() => {
          if (this.messageChild && this.type === "message") {
            this.messageChild.timerClose();
          }
        });
      }
    },
    /**
     * 底部弹出样式处理
     */
    bottom(type) {
      this.popupstyle = "bottom";
      this.ani = ["slide-bottom"];
      this.transClass = {
        position: "fixed",
        left: 0,
        right: 0,
        bottom: 0,
        paddingBottom: this.safeAreaInsets + "px",
        backgroundColor: this.bg,
        borderRadius: this.borderRadius || "0"
      };
      if (!type) {
        this.showPopup = true;
        this.showTrans = true;
      }
    },
    /**
     * 中间弹出样式处理
     */
    center(type) {
      this.popupstyle = "center";
      this.ani = ["fade"];
      this.transClass = {
        position: "fixed",
        display: "flex",
        flexDirection: "column",
        bottom: 0,
        left: 0,
        right: 0,
        top: 0,
        justifyContent: "center",
        alignItems: "center",
        borderRadius: this.borderRadius || "0"
      };
      if (!type) {
        this.showPopup = true;
        this.showTrans = true;
      }
    },
    left(type) {
      this.popupstyle = "left";
      this.ani = ["slide-left"];
      this.transClass = {
        position: "fixed",
        left: 0,
        bottom: 0,
        top: 0,
        backgroundColor: this.bg,
        borderRadius: this.borderRadius || "0",
        display: "flex",
        flexDirection: "column"
      };
      if (!type) {
        this.showPopup = true;
        this.showTrans = true;
      }
    },
    right(type) {
      this.popupstyle = "right";
      this.ani = ["slide-right"];
      this.transClass = {
        position: "fixed",
        bottom: 0,
        right: 0,
        top: 0,
        backgroundColor: this.bg,
        borderRadius: this.borderRadius || "0",
        display: "flex",
        flexDirection: "column"
      };
      if (!type) {
        this.showPopup = true;
        this.showTrans = true;
      }
    }
  }
};
if (!Array) {
  const _easycom_uni_transition2 = common_vendor.resolveComponent("uni-transition");
  _easycom_uni_transition2();
}
const _easycom_uni_transition = () => "../../../uni-transition/components/uni-transition/uni-transition.js";
if (!Math) {
  _easycom_uni_transition();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.showPopup
  }, $data.showPopup ? common_vendor.e({
    b: $data.maskShow
  }, $data.maskShow ? {
    c: common_vendor.o($options.onTap),
    d: common_vendor.p({
      name: "mask",
      ["mode-class"]: "fade",
      styles: $data.maskClass,
      duration: $data.duration,
      show: $data.showTrans
    })
  } : {}, {
    e: $data.showTrans
  }, $data.showTrans ? {
    f: common_vendor.s($options.getStyles),
    g: common_vendor.n($data.popupstyle),
    h: common_vendor.o((...args) => $options.clear && $options.clear(...args)),
    i: common_vendor.o($options.onTap),
    j: common_vendor.p({
      ["mode-class"]: $data.ani,
      name: "content",
      styles: $data.transClass,
      duration: $data.duration,
      show: $data.showTrans
    })
  } : {}, {
    k: common_vendor.o((...args) => $options.touchstart && $options.touchstart(...args)),
    l: common_vendor.n($data.popupstyle),
    m: common_vendor.n($options.isDesktop ? "fixforpc-z-index" : "")
  }) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/uni-popup/components/uni-popup/uni-popup.js.map
