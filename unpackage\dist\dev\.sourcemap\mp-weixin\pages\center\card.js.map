{"version": 3, "file": "card.js", "sources": ["pages/center/card.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2VudGVyL2NhcmQudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <view class=\"nav-bar bf8\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\r\n      <view class=\"bar-box df\" :style=\"{'height': titleBarHeight + 'px', 'width': '100%'}\">\r\n        <view class=\"bar-back df\" @tap=\"navBack\">\r\n          <image src=\"/static/img/back.png\" style=\"width:34rpx;height:34rpx\"></image>\r\n        </view>\r\n        <view class=\"bar-title ohto\">卡券</view>\r\n      </view>\r\n      <view class=\"nav-box df\">\r\n        <view \r\n          v-for=\"(item, index) in barList\" \r\n          :key=\"index\" \r\n          class=\"nav-item df\" \r\n          @tap=\"barClick\" \r\n          :data-idx=\"index\">\r\n          <text :style=\"{'color': index == barIdx ? '#000' : '#999', 'font-size': index == barIdx ? '28rpx' : '26rpx'}\">\r\n            {{ item }}\r\n          </text>\r\n          <view :style=\"{'opacity': index == barIdx ? 1 : 0}\" class=\"nav-line\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <view class=\"content\" :style=\"{'margin-top': 'calc(' + (statusBarHeight + titleBarHeight) + 'px + 90rpx)'}\">\r\n      <view v-if=\"isEmpty\" class=\"empty-box df\">\r\n        <image src=\"/static/img/empty.png\" mode=\"aspectFill\"/>\r\n        <view class=\"e1\">暂无卡券</view>\r\n        <view class=\"e2\">空空如也，等待探索</view>\r\n      </view>\r\n      <block v-else>\r\n        <view v-for=\"(item, index) in list\" :key=\"item.id\" class=\"coupon\">\r\n          <image class=\"coupon-bg\" src=\"/static/img/yhq.png\"></image>\r\n          <view class=\"coupon-item\">\r\n            <view class=\"corner-mark\">{{ item.subscript }}</view>\r\n            <view class=\"t1\">{{ item.price }} 元</view>\r\n            <view class=\"t2\">{{ item.intro }}</view>\r\n          </view>\r\n          <view v-if=\"barIdx == 0\" class=\"validity\">领取后 {{ item.validity }} 天内可用</view>\r\n          <view v-if=\"barIdx == 1\" class=\"validity\">有效期：{{ item.neck_create_time }} - {{ item.expire_time }}</view>\r\n          <view v-if=\"barIdx == 0\" class=\"coupon-btn\" @tap=\"goodsCardNeck\" :data-type=\"1\" :data-id=\"item.id\">立即领取</view>\r\n          <view v-if=\"item.is_usable\" class=\"coupon-err df\">已使用或已失效</view>\r\n        </view>\r\n      </block>\r\n      <uni-load-more :status=\"loadStatus\"></uni-load-more>\r\n    </view>\r\n    <view class=\"code-box df\" @tap=\"exchangeClick(true)\">\r\n      <image src=\"/static/img/kq.png\"></image>\r\n      <text>兑换卡券</text>\r\n    </view>\r\n    <uni-popup ref=\"exchangePopup\" type=\"bottom\" :safe-area=\"false\">\r\n      <view class=\"popup-box\">\r\n        <view class=\"popup-top df\">\r\n          <view class=\"popup-title\">\r\n            <view class=\"t1\">兑换卡券</view>\r\n            <view class=\"t2\">一张兑换码仅可使用一次，兑换成功后失效</view>\r\n          </view>\r\n          <view class=\"popup-close df\" @tap=\"exchangeClick(false)\">\r\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\r\n          </view>\r\n        </view>\r\n        <input class=\"exchange-input\" placeholder=\"请输入兑换码\" cursor-spacing=\"30px\" v-model=\"code\"/>\r\n        <view class=\"exchange-item\">\r\n          <view>1.使用兑换码请注意检查兑换码是否在有效期（客服告知或实体卡卡面为准）内，到期未使用则自动作废；</view>\r\n          <view>2.兑换码确认兑换后，系统会自动为您添加对应优惠券，过程不可逆；</view>\r\n        </view>\r\n        <view class=\"popup-btn\" @tap=\"goodsCardNeck\" :data-type=\"2\" :data-id=\"0\">确认兑换</view>\r\n      </view>\r\n    </uni-popup>\r\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\r\n      <view class=\"tips-box df\">\r\n        <view class=\"tips-item\">{{ tipsTitle }}</view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request.js'\r\nimport * as api from '@/config/api.js'\r\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\r\nimport uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'\r\n\r\nconst app = getApp();\r\n\r\nexport default {\r\n  components: {\r\n    uniLoadMore,\r\n    uniPopup\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\r\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\r\n      barList: [\"领券中心\", \"我的卡券\"],\r\n      barIdx: 0,\r\n      list: [],\r\n      page: 1,\r\n      isEmpty: false,\r\n      loadStatus: \"loading\",\r\n      code: \"\",\r\n      isThrottling: true,\r\n      isCode: false,\r\n      tipsTitle: \"\",\r\n      // Mock数据\r\n      mockCoupons: {\r\n        center: [\r\n          {\r\n            id: 1,\r\n            subscript: \"满减券\",\r\n            price: \"50\",\r\n            intro: \"满200元可用\",\r\n            validity: \"30\",\r\n            is_usable: false\r\n          },\r\n          {\r\n            id: 2,\r\n            subscript: \"折扣券\",\r\n            price: \"30\",\r\n            intro: \"全场通用\",\r\n            validity: \"15\",\r\n            is_usable: false\r\n          },\r\n          {\r\n            id: 3,\r\n            subscript: \"新人券\",\r\n            price: \"10\",\r\n            intro: \"新用户专享\",\r\n            validity: \"7\",\r\n            is_usable: false\r\n          }\r\n        ],\r\n        my: [\r\n          {\r\n            id: 4,\r\n            subscript: \"满减券\",\r\n            price: \"100\",\r\n            intro: \"满300元可用\",\r\n            neck_create_time: \"2023-01-01\",\r\n            expire_time: \"2023-12-31\",\r\n            is_usable: false\r\n          },\r\n          {\r\n            id: 5,\r\n            subscript: \"折扣券\",\r\n            price: \"20\",\r\n            intro: \"特定商品可用\",\r\n            neck_create_time: \"2023-01-15\",\r\n            expire_time: \"2023-06-30\",\r\n            is_usable: true\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    if (options.type) {\r\n      this.barIdx = parseInt(options.type);\r\n    }\r\n    this.goodsCardList();\r\n  },\r\n  methods: {\r\n    goodsCardList() {\r\n      let that = this;\r\n      \r\n      // 尝试使用真实API\r\n      if (api.default && api.default.api && api.default.api.goodsCardListUrl) {\r\n        request(api.default.api.goodsCardListUrl, {\r\n          type: that.barIdx,\r\n          page: that.page\r\n        }).then(function(res) {\r\n          that.isThrottling = true;\r\n          that.loadStatus = \"more\";\r\n          \r\n          if (res.data.data.length > 0) {\r\n            if (that.page == 1) {\r\n              that.list = res.data.data;\r\n            } else {\r\n              that.list = that.list.concat(res.data.data);\r\n            }\r\n            that.page = res.data.current_page;\r\n            that.isEmpty = false;\r\n          } else if (that.page == 1) {\r\n            that.isEmpty = true;\r\n          }\r\n        });\r\n      } else {\r\n        // 使用Mock数据\r\n        setTimeout(() => {\r\n          that.isThrottling = true;\r\n          that.loadStatus = \"more\";\r\n          \r\n          // 根据当前tab返回不同的数据\r\n          const mockData = that.barIdx == 0 ? that.mockCoupons.center : that.mockCoupons.my;\r\n          \r\n          // 模拟分页\r\n          const pageSize = 5;\r\n          const startIndex = (that.page - 1) * pageSize;\r\n          const endIndex = startIndex + pageSize;\r\n          const pageData = mockData.slice(startIndex, endIndex);\r\n          \r\n          if (pageData.length > 0) {\r\n            if (that.page == 1) {\r\n              that.list = pageData;\r\n            } else {\r\n              that.list = that.list.concat(pageData);\r\n            }\r\n            that.isEmpty = false;\r\n          } else if (that.page == 1) {\r\n            that.isEmpty = true;\r\n          }\r\n        }, 500);\r\n      }\r\n    },\r\n    \r\n    barClick(e) {\r\n      if (this.isThrottling) {\r\n        this.isThrottling = false;\r\n        this.barIdx = e.currentTarget.dataset.idx;\r\n        this.page = 1;\r\n        this.goodsCardList();\r\n      }\r\n    },\r\n    \r\n    goodsCardNeck(e) {\r\n      let that = this;\r\n      const dataset = e.currentTarget.dataset;\r\n      \r\n      // 验证兑换码\r\n      if (dataset.type == 2 && !that.code) {\r\n        return that.opTipsPopup(\"请输入兑换码！\");\r\n      }\r\n      \r\n      // 尝试使用真实API\r\n      if (api.default && api.default.api && api.default.api.goodsCardNeckUrl) {\r\n        request(api.default.api.goodsCardNeckUrl, {\r\n          id: dataset.id,\r\n          type: dataset.type,\r\n          code: that.code\r\n        }, \"POST\").then(function(res) {\r\n          that.opTipsPopup(res.msg);\r\n          if (res.code == 200) {\r\n            app.globalData.isCenterPage = true;\r\n            if (dataset.type == 2) {\r\n              that.barIdx = 1;\r\n            }\r\n            that.list = [];\r\n            that.page = 1;\r\n            that.goodsCardList();\r\n            that.$refs.exchangePopup.close();\r\n          }\r\n        });\r\n      } else {\r\n        // 使用Mock数据\r\n        setTimeout(() => {\r\n          if (dataset.type == 1) {\r\n            // 领取优惠券\r\n            const coupon = that.mockCoupons.center.find(item => item.id === dataset.id);\r\n            if (coupon) {\r\n              // 创建一个新的我的优惠券\r\n              const newCoupon = {\r\n                id: 100 + that.mockCoupons.my.length,\r\n                subscript: coupon.subscript,\r\n                price: coupon.price,\r\n                intro: coupon.intro,\r\n                    neck_create_time: new Date().toISOString().slice(0, 19).replace('T', ' '),\r\n    expire_time: new Date(Date.now() + coupon.validity * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),\r\n                is_usable: false\r\n              };\r\n              \r\n              that.mockCoupons.my.unshift(newCoupon);\r\n              that.opTipsPopup(\"领取成功\");\r\n            }\r\n          } else if (dataset.type == 2) {\r\n            // 兑换优惠券\r\n            // 验证兑换码格式（模拟）\r\n            const validCode = /^[A-Za-z0-9]{6,12}$/.test(that.code);\r\n            \r\n            if (validCode) {\r\n              // 创建一个新的兑换券\r\n              const newCoupon = {\r\n                id: 200 + that.mockCoupons.my.length,\r\n                subscript: \"兑换券\",\r\n                price: Math.floor(Math.random() * 100) + 10,\r\n                intro: \"兑换码\" + that.code + \"专享\",\r\n                    neck_create_time: new Date().toISOString().slice(0, 19).replace('T', ' '),\r\n    expire_time: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),\r\n                is_usable: false\r\n              };\r\n              \r\n              that.mockCoupons.my.unshift(newCoupon);\r\n              that.opTipsPopup(\"兑换成功\");\r\n              that.barIdx = 1;\r\n              that.list = [];\r\n              that.page = 1;\r\n              that.goodsCardList();\r\n              that.$refs.exchangePopup.close();\r\n              that.code = \"\";\r\n            } else {\r\n              that.opTipsPopup(\"兑换码无效\");\r\n            }\r\n          }\r\n        }, 500);\r\n      }\r\n    },\r\n    \r\n    exchangeClick(isOpen) {\r\n      if (isOpen) {\r\n        this.$refs.exchangePopup.open();\r\n        this.isCode = true;\r\n      } else {\r\n        this.$refs.exchangePopup.close();\r\n        this.isCode = false;\r\n      }\r\n    },\r\n    \r\n    opTipsPopup(msg) {\r\n      let that = this;\r\n      that.tipsTitle = msg;\r\n      that.$refs.tipsPopup.open();\r\n      setTimeout(function() {\r\n        that.$refs.tipsPopup.close();\r\n      }, 2000);\r\n    },\r\n    \r\n    navBack() {\r\n      if (getCurrentPages().length > 1) {\r\n        uni.navigateBack();\r\n      } else {\r\n        uni.switchTab({\r\n          url: \"/pages/tabbar/center\"\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 辅助函数：格式化日期为datetime格式\r\n    formatDate(date) {\r\n      // 直接返回ISO字符串格式化为datetime\r\n      return date.toISOString().slice(0, 19).replace('T', ' ');\r\n    }\r\n  },\r\n  onReachBottom() {\r\n    if (this.list.length) {\r\n      this.page = this.page + 1;\r\n      this.loadStatus = \"loading\";\r\n      this.goodsCardList();\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\npage {\r\n\tbackground: #f8f8f8;\r\n\tpadding-bottom: 100rpx;\r\n}\r\n\r\n.container {\r\n\twidth: 100%;\r\n}\r\n\r\n.nav-bar {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\tz-index: 99;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.bar-box .bar-back {\r\n\tpadding: 0 30rpx;\r\n\twidth: 34rpx;\r\n\theight: 100%;\r\n}\r\n\r\n.bar-box .bar-title {\r\n\tmax-width: 60%;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 700;\r\n}\r\n\r\n.nav-box {\r\n\twidth: 100%;\r\n\theight: 80rpx;\r\n}\r\n\r\n.nav-box .nav-item {\r\n\tpadding: 0 30rpx;\r\n\theight: 100%;\r\n\tflex-direction: column;\r\n\tjustify-content: center;\r\n\tposition: relative;\r\n}\r\n\r\n.nav-box .nav-item text {\r\n\tfont-weight: 700;\r\n\ttransition: all .3s ease-in-out;\r\n}\r\n\r\n.nav-box .nav-line {\r\n\tposition: absolute;\r\n\tbottom: 12rpx;\r\n\twidth: 18rpx;\r\n\theight: 6rpx;\r\n\tborder-radius: 6rpx;\r\n\tbackground: #000;\r\n\ttransition: opacity .3s ease-in-out;\r\n}\r\n\r\n.code-box {\r\n\tposition: fixed;\r\n\tz-index: 99;\r\n\tleft: 30rpx;\r\n\tbottom: max(env(safe-area-inset-bottom), 30rpx);\r\n\twidth: calc(100% - 60rpx);\r\n\theight: 100rpx;\r\n\tcolor: #fff;\r\n\tfont-size: 24rpx;\r\n\tfont-weight: 700;\r\n\tbackground: #000;\r\n\tborder-radius: 100rpx;\r\n\tjustify-content: center;\r\n}\r\n\r\n.code-box image {\r\n\tmargin-right: 8rpx;\r\n\twidth: 34rpx;\r\n\theight: 34rpx;\r\n}\r\n\r\n.content {\r\n\twidth: calc(100% - 60rpx);\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.coupon {\r\n\tmargin-bottom: 30rpx;\r\n\twidth: 100%;\r\n\tborder-radius: 8rpx;\r\n\tbackground: #fff;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.coupon .coupon-bg {\r\n\tposition: absolute;\r\n\tz-index: 1;\r\n\tright: -90rpx;\r\n\tbottom: -120rpx;\r\n\twidth: 380rpx;\r\n\theight: 380rpx;\r\n}\r\n\r\n.coupon .coupon-item {\r\n\tz-index: 2;\r\n\twidth: calc(100% - 80rpx);\r\n\tpadding: 50rpx 40rpx 20rpx;\r\n\tborder-bottom: 2rpx dashed #f8f8f8;\r\n\tposition: relative;\r\n}\r\n\r\n.coupon-item .corner-mark {\r\n\tposition: absolute;\r\n\tz-index: 9;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tborder-radius: 8rpx 0;\r\n\tpadding: 0rpx 12rpx;\r\n\theight: 36rpx;\r\n\tline-height: 36rpx;\r\n\ttext-align: center;\r\n\tfont-size: 20rpx;\r\n\tcolor: #fa5150;\r\n\tbackground: rgba(250, 81, 80, .125);\r\n}\r\n\r\n.coupon-item .t1 {\r\n\twidth: calc(100% - 200rpx);\r\n\tcolor: #000;\r\n\tfont-size: 48rpx;\r\n\tfont-weight: 700;\r\n}\r\n\r\n.coupon-item .t2 {\r\n\twidth: calc(100% - 200rpx);\r\n\tmargin: 8rpx 0;\r\n\tcolor: #444;\r\n\tfont-size: 24rpx;\r\n}\r\n\r\n.coupon .coupon-btn {\r\n\tposition: absolute;\r\n\tz-index: 9;\r\n\ttop: calc(50% - 30rpx);\r\n\tright: 30rpx;\r\n\twidth: 140rpx;\r\n\theight: 60rpx;\r\n\tline-height: 60rpx;\r\n\ttext-align: center;\r\n\tfont-size: 20rpx;\r\n\tfont-weight: 700;\r\n\tborder-radius: 50rpx;\r\n\tcolor: #fff;\r\n\tbackground: #000;\r\n}\r\n\r\n.coupon .validity {\r\n\twidth: calc(100% - 80rpx);\r\n\tpadding: 20rpx 40rpx;\r\n\tcolor: #999;\r\n\tfont-size: 20rpx;\r\n}\r\n\r\n.coupon .coupon-err {\r\n\tposition: absolute;\r\n\tz-index: 10;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tfont-size: 28rpx;\r\n\tfont-style: italic;\r\n\tfont-weight: 700;\r\n\tjustify-content: center;\r\n\tcolor: #ccc;\r\n\tbackground: rgba(255, 255, 255, .85);\r\n}\r\n\r\n.popup-box {\r\n\twidth: calc(100% - 40rpx);\r\n\tpadding: 20rpx;\r\n\tbackground: #fff;\r\n\tborder-radius: 30rpx 30rpx 0 0;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.popup-box .popup-top {\r\n\twidth: calc(100% - 20rpx);\r\n\tpadding: 10rpx;\r\n\tjustify-content: space-between;\r\n}\r\n\r\n.popup-top .popup-title .t1 {\r\n\tfont-size: 38rpx;\r\n\tfont-weight: 700;\r\n}\r\n\r\n.popup-top .popup-title .t2 {\r\n\tcolor: #999;\r\n\tfont-size: 20rpx;\r\n\tfont-weight: 300;\r\n}\r\n\r\n.popup-top .popup-close {\r\n\twidth: 48rpx;\r\n\theight: 48rpx;\r\n\tborder-radius: 50%;\r\n\tbackground: #f8f8f8;\r\n\tjustify-content: center;\r\n\ttransform: rotate(45deg);\r\n}\r\n\r\n.exchange-input {\r\n\tmargin: 30rpx 10rpx;\r\n\tpadding: 0 30rpx;\r\n\twidth: calc(100% - 80rpx);\r\n\theight: 100rpx;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 700;\r\n\ttext-align: center;\r\n\tborder: 4rpx solid #f5f5f5;\r\n\tborder-radius: 30rpx;\r\n}\r\n\r\n.exchange-item view {\r\n\tmargin: 10rpx;\r\n\twidth: calc(100% - 20rpx);\r\n\tcolor: #999;\r\n\tfont-size: 20rpx;\r\n}\r\n\r\n.popup-box .popup-btn {\r\n\tmargin: 40rpx 10rpx;\r\n\twidth: calc(100% - 20rpx);\r\n\theight: 100rpx;\r\n\tline-height: 100rpx;\r\n\ttext-align: center;\r\n\tfont-size: 24rpx;\r\n\tfont-weight: 700;\r\n\tcolor: #fff;\r\n\tbackground: #000;\r\n\tborder-radius: 100rpx;\r\n}\r\n\r\n.empty-box {\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 100rpx 0;\r\n}\r\n\r\n.empty-box image {\r\n\twidth: 200rpx;\r\n\theight: 200rpx;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.empty-box .e1 {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.empty-box .e2 {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.tips-box {\r\n\tpadding: 20rpx 30rpx;\r\n\tbackground: rgba(0, 0, 0, 0.6);\r\n\tborder-radius: 12rpx;\r\n\tjustify-content: center;\r\n}\r\n\r\n.tips-box .tips-item {\r\n\tcolor: #fff;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 700;\r\n}\r\n\r\n.df {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.ohto {\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.bf8 {\r\n\tbackground: #f8f8f8;\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/uniapp/vue3/pages/center/card.vue'\nwx.createPage(MiniProgramPage)"], "names": ["api.default", "request", "uni"], "mappings": ";;;;;AA+EA,MAAO,cAAa,MAAW;AAC/B,iBAAiB,MAAW;AAE5B,MAAM,MAAM,OAAM;AAElB,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB,KAAK,OAAO,MAAM,mBAAmB;AAAA,MACtD,gBAAgB,KAAK,OAAO,MAAM,kBAAkB;AAAA,MACpD,SAAS,CAAC,QAAQ,MAAM;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,CAAE;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,WAAW;AAAA;AAAA,MAEX,aAAa;AAAA,QACX,QAAQ;AAAA,UACN;AAAA,YACE,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,OAAO;AAAA,YACP,OAAO;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,UACZ;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,OAAO;AAAA,YACP,OAAO;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,UACZ;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,OAAO;AAAA,YACP,OAAO;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,UACb;AAAA,QACD;AAAA,QACD,IAAI;AAAA,UACF;AAAA,YACE,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,OAAO;AAAA,YACP,OAAO;AAAA,YACP,kBAAkB;AAAA,YAClB,aAAa;AAAA,YACb,WAAW;AAAA,UACZ;AAAA,UACD;AAAA,YACE,IAAI;AAAA,YACJ,WAAW;AAAA,YACX,OAAO;AAAA,YACP,OAAO;AAAA,YACP,kBAAkB;AAAA,YAClB,aAAa;AAAA,YACb,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AACd,QAAI,QAAQ,MAAM;AAChB,WAAK,SAAS,SAAS,QAAQ,IAAI;AAAA,IACrC;AACA,SAAK,cAAa;AAAA,EACnB;AAAA,EACD,SAAS;AAAA,IACP,gBAAgB;AACd,UAAI,OAAO;AAGX,UAAIA,WAAAA,SAAeA,WAAAA,MAAY,OAAOA,WAAAA,MAAY,IAAI,kBAAkB;AACtEC,8BAAQD,WAAAA,MAAY,IAAI,kBAAkB;AAAA,UACxC,MAAM,KAAK;AAAA,UACX,MAAM,KAAK;AAAA,QACb,CAAC,EAAE,KAAK,SAAS,KAAK;AACpB,eAAK,eAAe;AACpB,eAAK,aAAa;AAElB,cAAI,IAAI,KAAK,KAAK,SAAS,GAAG;AAC5B,gBAAI,KAAK,QAAQ,GAAG;AAClB,mBAAK,OAAO,IAAI,KAAK;AAAA,mBAChB;AACL,mBAAK,OAAO,KAAK,KAAK,OAAO,IAAI,KAAK,IAAI;AAAA,YAC5C;AACA,iBAAK,OAAO,IAAI,KAAK;AACrB,iBAAK,UAAU;AAAA,UACjB,WAAW,KAAK,QAAQ,GAAG;AACzB,iBAAK,UAAU;AAAA,UACjB;AAAA,QACF,CAAC;AAAA,aACI;AAEL,mBAAW,MAAM;AACf,eAAK,eAAe;AACpB,eAAK,aAAa;AAGlB,gBAAM,WAAW,KAAK,UAAU,IAAI,KAAK,YAAY,SAAS,KAAK,YAAY;AAG/E,gBAAM,WAAW;AACjB,gBAAM,cAAc,KAAK,OAAO,KAAK;AACrC,gBAAM,WAAW,aAAa;AAC9B,gBAAM,WAAW,SAAS,MAAM,YAAY,QAAQ;AAEpD,cAAI,SAAS,SAAS,GAAG;AACvB,gBAAI,KAAK,QAAQ,GAAG;AAClB,mBAAK,OAAO;AAAA,mBACP;AACL,mBAAK,OAAO,KAAK,KAAK,OAAO,QAAQ;AAAA,YACvC;AACA,iBAAK,UAAU;AAAA,UACjB,WAAW,KAAK,QAAQ,GAAG;AACzB,iBAAK,UAAU;AAAA,UACjB;AAAA,QACD,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA,IAED,SAAS,GAAG;AACV,UAAI,KAAK,cAAc;AACrB,aAAK,eAAe;AACpB,aAAK,SAAS,EAAE,cAAc,QAAQ;AACtC,aAAK,OAAO;AACZ,aAAK,cAAa;AAAA,MACpB;AAAA,IACD;AAAA,IAED,cAAc,GAAG;AACf,UAAI,OAAO;AACX,YAAM,UAAU,EAAE,cAAc;AAGhC,UAAI,QAAQ,QAAQ,KAAK,CAAC,KAAK,MAAM;AACnC,eAAO,KAAK,YAAY,SAAS;AAAA,MACnC;AAGA,UAAIA,WAAAA,SAAeA,WAAAA,MAAY,OAAOA,WAAAA,MAAY,IAAI,kBAAkB;AACtEC,8BAAQD,WAAAA,MAAY,IAAI,kBAAkB;AAAA,UACxC,IAAI,QAAQ;AAAA,UACZ,MAAM,QAAQ;AAAA,UACd,MAAM,KAAK;AAAA,QACZ,GAAE,MAAM,EAAE,KAAK,SAAS,KAAK;AAC5B,eAAK,YAAY,IAAI,GAAG;AACxB,cAAI,IAAI,QAAQ,KAAK;AACnB,gBAAI,WAAW,eAAe;AAC9B,gBAAI,QAAQ,QAAQ,GAAG;AACrB,mBAAK,SAAS;AAAA,YAChB;AACA,iBAAK,OAAO;AACZ,iBAAK,OAAO;AACZ,iBAAK,cAAa;AAClB,iBAAK,MAAM,cAAc;UAC3B;AAAA,QACF,CAAC;AAAA,aACI;AAEL,mBAAW,MAAM;AACf,cAAI,QAAQ,QAAQ,GAAG;AAErB,kBAAM,SAAS,KAAK,YAAY,OAAO,KAAK,UAAQ,KAAK,OAAO,QAAQ,EAAE;AAC1E,gBAAI,QAAQ;AAEV,oBAAM,YAAY;AAAA,gBAChB,IAAI,MAAM,KAAK,YAAY,GAAG;AAAA,gBAC9B,WAAW,OAAO;AAAA,gBAClB,OAAO,OAAO;AAAA,gBACd,OAAO,OAAO;AAAA,gBACV,mBAAkB,oBAAI,QAAO,cAAc,MAAM,GAAG,EAAE,EAAE,QAAQ,KAAK,GAAG;AAAA,gBACxF,aAAa,IAAI,KAAK,KAAK,IAAM,IAAE,OAAO,WAAW,KAAK,KAAK,KAAK,GAAI,EAAE,YAAa,EAAC,MAAM,GAAG,EAAE,EAAE,QAAQ,KAAK,GAAG;AAAA,gBACzG,WAAW;AAAA;AAGb,mBAAK,YAAY,GAAG,QAAQ,SAAS;AACrC,mBAAK,YAAY,MAAM;AAAA,YACzB;AAAA,qBACS,QAAQ,QAAQ,GAAG;AAG5B,kBAAM,YAAY,sBAAsB,KAAK,KAAK,IAAI;AAEtD,gBAAI,WAAW;AAEb,oBAAM,YAAY;AAAA,gBAChB,IAAI,MAAM,KAAK,YAAY,GAAG;AAAA,gBAC9B,WAAW;AAAA,gBACX,OAAO,KAAK,MAAM,KAAK,OAAS,IAAE,GAAG,IAAI;AAAA,gBACzC,OAAO,QAAQ,KAAK,OAAO;AAAA,gBACvB,mBAAkB,oBAAI,QAAO,cAAc,MAAM,GAAG,EAAE,EAAE,QAAQ,KAAK,GAAG;AAAA,gBACxF,aAAa,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,GAAI,EAAE,YAAW,EAAG,MAAM,GAAG,EAAE,EAAE,QAAQ,KAAK,GAAG;AAAA,gBAC5F,WAAW;AAAA;AAGb,mBAAK,YAAY,GAAG,QAAQ,SAAS;AACrC,mBAAK,YAAY,MAAM;AACvB,mBAAK,SAAS;AACd,mBAAK,OAAO;AACZ,mBAAK,OAAO;AACZ,mBAAK,cAAa;AAClB,mBAAK,MAAM,cAAc;AACzB,mBAAK,OAAO;AAAA,mBACP;AACL,mBAAK,YAAY,OAAO;AAAA,YAC1B;AAAA,UACF;AAAA,QACD,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA,IAED,cAAc,QAAQ;AACpB,UAAI,QAAQ;AACV,aAAK,MAAM,cAAc;AACzB,aAAK,SAAS;AAAA,aACT;AACL,aAAK,MAAM,cAAc;AACzB,aAAK,SAAS;AAAA,MAChB;AAAA,IACD;AAAA,IAED,YAAY,KAAK;AACf,UAAI,OAAO;AACX,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU;AACrB,iBAAW,WAAW;AACpB,aAAK,MAAM,UAAU;MACtB,GAAE,GAAI;AAAA,IACR;AAAA,IAED,UAAU;AACR,UAAI,gBAAe,EAAG,SAAS,GAAG;AAChCE,sBAAG,MAAC,aAAY;AAAA,aACX;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,QACP,CAAC;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,MAAM;AAEf,aAAO,KAAK,YAAa,EAAC,MAAM,GAAG,EAAE,EAAE,QAAQ,KAAK,GAAG;AAAA,IACzD;AAAA,EACD;AAAA,EACD,gBAAgB;AACd,QAAI,KAAK,KAAK,QAAQ;AACpB,WAAK,OAAO,KAAK,OAAO;AACxB,WAAK,aAAa;AAClB,WAAK,cAAa;AAAA,IACpB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1VA,GAAG,WAAW,eAAe;"}