"use strict";
const utils_request = require("../utils/request.js");
function getUserInfo() {
  return utils_request.request.get("user");
}
function loginH5(data) {
  return utils_request.request.post("login", data, {
    noAuth: true
  });
}
function loginMobile(data) {
  return utils_request.request.post("login/mobile", data, {
    noAuth: true
  });
}
function getCodeApi() {
  return utils_request.request.get("verify_code", {}, {
    noAuth: true
  });
}
function registerVerify(data) {
  return utils_request.request.post("register/verify", data, {
    noAuth: true
  });
}
function register(data) {
  return utils_request.request.post("register", data, {
    noAuth: true
  });
}
function getMenuList() {
  return utils_request.request.get("menu/user", {}, {
    noAuth: true
  });
}
function postSignUser(sign) {
  return utils_request.request.post("sign/user", sign);
}
function getSignConfig() {
  return utils_request.request.get("sign/config");
}
function getSignList(data) {
  return utils_request.request.get("sign/list", data);
}
function setSignIntegral() {
  return utils_request.request.post("sign/integral");
}
function getSignMonthList(data) {
  return utils_request.request.get("sign/month", data);
}
function getIntegralList(q) {
  return utils_request.request.get("integral/list", q);
}
function getAddressList(data) {
  return utils_request.request.get("address/list", data);
}
function setAddressDefault(id) {
  return utils_request.request.post("address/default/set", {
    id
  });
}
function editAddress(data) {
  return utils_request.request.post("address/edit", data);
}
function delAddress(id) {
  return utils_request.request.post("address/del", {
    id
  });
}
function getAddressDetail(id) {
  return utils_request.request.get("address/detail/" + id);
}
function userEdit(data) {
  return utils_request.request.post("user/edit", data);
}
function getLogout() {
  return utils_request.request.get("logout");
}
function setVisit(data) {
  return utils_request.request.post("user/set_visit", {
    ...data
  }, {
    noAuth: true
  });
}
function spread(puid) {
  return utils_request.request.post("user/spread", puid);
}
function phoneWxSilenceAuth(data) {
  return utils_request.request.post("v2/phone_wx_silence_auth", data, {
    noAuth: true
  });
}
function phoneSilenceAuth(data) {
  return utils_request.request.post("v2/phone_silence_auth", data, {
    noAuth: true
  });
}
function appleLogin(data) {
  return utils_request.request.post("apple_login", data, {
    noAuth: true
  });
}
function getUserAgreement(type) {
  return utils_request.request.get(`get_agreement/${type}`, {}, {
    noAuth: true
  });
}
function cancelUser() {
  return utils_request.request.get("user_cancel");
}
function getLangList() {
  return utils_request.request.get("get_lang_type_list", {}, {
    noAuth: true
  });
}
function getLangJson() {
  return utils_request.request.get("get_lang_json", {}, {
    noAuth: true
  });
}
function getLangVersion() {
  return utils_request.request.get("lang_version", {}, {
    noAuth: true
  });
}
function mpBindingPhone(data) {
  return utils_request.request.post("v2/routine/binding_phone", data);
}
function changeRemindStatus(status) {
  return utils_request.request.get(`sign/remind/${status}`, {}, {
    noAuth: true
  });
}
exports.appleLogin = appleLogin;
exports.cancelUser = cancelUser;
exports.changeRemindStatus = changeRemindStatus;
exports.delAddress = delAddress;
exports.editAddress = editAddress;
exports.getAddressDetail = getAddressDetail;
exports.getAddressList = getAddressList;
exports.getCodeApi = getCodeApi;
exports.getIntegralList = getIntegralList;
exports.getLangJson = getLangJson;
exports.getLangList = getLangList;
exports.getLangVersion = getLangVersion;
exports.getLogout = getLogout;
exports.getMenuList = getMenuList;
exports.getSignConfig = getSignConfig;
exports.getSignList = getSignList;
exports.getSignMonthList = getSignMonthList;
exports.getUserAgreement = getUserAgreement;
exports.getUserInfo = getUserInfo;
exports.loginH5 = loginH5;
exports.loginMobile = loginMobile;
exports.mpBindingPhone = mpBindingPhone;
exports.phoneSilenceAuth = phoneSilenceAuth;
exports.phoneWxSilenceAuth = phoneWxSilenceAuth;
exports.postSignUser = postSignUser;
exports.register = register;
exports.registerVerify = registerVerify;
exports.setAddressDefault = setAddressDefault;
exports.setSignIntegral = setSignIntegral;
exports.setVisit = setVisit;
exports.spread = spread;
exports.userEdit = userEdit;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/user.js.map
