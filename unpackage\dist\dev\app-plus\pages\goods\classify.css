
.uni-load-more[data-v-9245e42c] {
  display: flex;
  flex-direction: row;
  height: 1.875rem;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-9245e42c] {
  font-size: 0.75rem;
  margin-left: 0.5rem;
}
.uni-load-more__img[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
}
.uni-load-more__img--nvue[data-v-9245e42c] {
  color: #999;
}
.uni-load-more__img--android[data-v-9245e42c],
.uni-load-more__img--ios[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
}
.uni-load-more__img--android[data-v-9245e42c] {
  animation: loading-ios 1s 0s linear infinite;
}
.uni-load-more__img--ios-H5[data-v-9245e42c] {
  position: relative;
  animation: loading-ios-H5-9245e42c 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-9245e42c] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-9245e42c {
0% {
    transform: rotate(0);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
to {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-9245e42c] {
  position: relative;
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 0.125rem transparent;
  border-top: solid 0.125rem #999;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(1) {
  animation: loading-android-MP-1-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(2) {
  animation: loading-android-MP-2-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(3) {
  animation: loading-android-MP-3-9245e42c 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(90deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(180deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(270deg);
}
to {
    transform: rotate(360deg);
}
}


.lazy-image[data-v-337fa078]{position:relative;width:100%;height:100%;overflow:hidden;background:#f8f8f8;}
.lazy-image uni-image[data-v-337fa078]{width:100%;height:100%;}
.lazy-image .err[data-v-337fa078]{position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(248,248,248,0.7) url('../../static/img/load.png') no-repeat center/50%;}


.money-box[data-v-433ff6d7] {
  display: flex;
  align-items: flex-end;
  font-weight: bolder;
}
.money-box .sale[data-v-433ff6d7] {
  margin: 0 0.0625rem;
}
.money-box .unit[data-v-433ff6d7] {
  margin-bottom: 0.0625rem;
}


body {
  background: #f8f8f8;
}
.nav-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.nav-box .nav-back {
  padding: 0 0.9375rem;
}
.bg-mk, .bg-mk2, .bg-img {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.bg-mk {
  z-index: -1;
  background: linear-gradient(to left, rgba(255, 255, 255, .3), #fff);
}
.bg-mk2 {
  z-index: -1;
  background: linear-gradient(to bottom, rgba(255, 255, 255, .3), #fff, #fff);
}
.bg-img {
  z-index: -2;
}
.nav-box .classify-scroll {
  width: 100%;
  white-space: nowrap;
  transition: all .3s ease-in-out;
  overflow: hidden;
}
.classify-scroll .one-box {
  width: calc(100% - 0.9375rem);
  padding: 0 0.46875rem;
  height: 3.125rem;
}
.one-box .one-item {
  flex-shrink: 0;
  margin: 0 0.46875rem;
  padding: 0 0.9375rem;
  height: 2.125rem;
  font-size: 0.625rem;
  font-weight: 700;
  background: #f8f8f8;
  border-radius: 2.125rem;
  justify-content: center;
}
.one-box .active {
  color: #fff;
  background: #000;
}
.classify-scroll .two-box {
  width: 100%;
  height: 2.125rem;
}
.two-box .two-item {
  flex-shrink: 0;
  padding: 0 0.9375rem;
  height: 2.125rem;
  line-height: 2.125rem;
  font-size: 0.75rem;
  font-weight: 700;
  transition: color .3s ease-in-out;
}
.content-box {
  width: 100%;
  transition: all .3s ease-in-out;
}
.goods-box {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.goods-box .goods-item {
  width: calc(50% - 0.46875rem);
  margin: 0.3125rem 0 0 0.3125rem;
  background: #fff;
  border-radius: 0.25rem;
  overflow: hidden;
}
.goods-item .goods-img {
  width: 100%;
  padding-top: 100%;
  position: relative;
}
.goods-img .goods-img-item {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.goods-item .goods-name {
  width: calc(100% - 1.25rem);
  margin: 0.46875rem 0.625rem;
  font-size: 0.8125rem;
  line-height: 1.125rem;
  font-weight: 500;
}
.goods-item .goods-price {
  width: calc(100% - 0.9375rem);
  margin: 0 0.625rem 0.625rem;
  display: flex;
  align-items: flex-end;
}
.goods-price .price-h {
  margin-left: 0.46875rem;
  color: #999;
  font-size: 0.625rem;
  line-height: 0.625rem;
}
.goods-item .goods-tag {
  width: calc(100% - 0.9375rem);
  margin: 0 0.46875rem 0.46875rem;
  display: flex;
  flex-wrap: wrap;
}
.goods-tag .tag-item {
  margin: 0 0.15625rem 0.15625rem;
  height: 1.25rem;
  padding: 0 0.375rem;
  line-height: 1.25rem;
  font-size: 0.5625rem;
  font-weight: 500;
  background: #f8f8f8;
  border-radius: 0.25rem;
}

/* 空状态样式 */
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-box uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.9375rem;
}
.empty-box .e1 {
  font-size: 0.875rem;
  font-weight: bold;
  margin-bottom: 0.3125rem;
}
.empty-box .e2 {
  font-size: 0.75rem;
  color: #999;
}

/* 辅助类 */
.df {
  display: flex;
  align-items: center;
}
.ohto2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.heio {
  width: 100%;
  justify-content: center;
  transition: all .3s ease-in-out;
  overflow: hidden;
}
