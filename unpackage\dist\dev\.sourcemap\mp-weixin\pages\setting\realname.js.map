{"version": 3, "file": "realname.js", "sources": ["pages/setting/realname.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc2V0dGluZy9yZWFsbmFtZS52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"page-wrapper\">\r\n\t\t<!-- Scrollable content area -->\r\n\t\t<scroll-view scroll-y class=\"content-scroll\">\r\n\t\t\t<view class=\"container\">\r\n\t\t\t\t<view class=\"title-box\">\r\n\t\t\t\t\t<view>请填写真实身份信息</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"input-tips\" style=\"color:#FA5150\">\r\n\t\t\t\t\t注：因监管部门要求，社区类产品需实名后使用，绑定手机号即可发布动态、评论、参加活动等。认证信息将用于平台服务，与账号唯一绑定，无法解绑。我们会对信息进行严格保密。\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 表单区域 -->\r\n\t\t\t\t<view class=\"form-section\">\r\n\t\t\t\t\t<!-- 手机号显示 -->\r\n\t\t\t\t\t<view class=\"title-label\">手机号</view>\r\n\t\t\t\t\t<view class=\"input-box df\">\r\n\t\t\t\t\t\t<view>{{userInfo.phone || '未绑定'}}</view>\r\n\t\t\t\t\t\t<button class=\"input-btn df\" open-type=\"getPhoneNumber\" @getphonenumber=\"bindMobileClick\">\r\n\t\t\t\t\t\t\t<image src=\"/static/img/dh.png\"></image>\r\n\t\t\t\t\t\t\t<text>{{userInfo.phone ? '换绑' : '绑定'}}</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\t\t\t\r\n\t\t\t\t\t<view class=\"title-label\">证件类型</view>\r\n\t\t\t\t\t<view class=\"input-box\">\r\n\t\t\t\t\t\t<view class=\"value\">居民身份证</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"title-label\">真实姓名</view>\r\n\t\t\t\t\t<input \r\n\t\t\t\t\t\tclass=\"input-box\" \r\n\t\t\t\t\t\tv-model=\"real_name\" \r\n\t\t\t\t\t\t:disabled=\"isReadonly\"\r\n\t\t\t\t\t\tplaceholder=\"请填写真实姓名\" \r\n\t\t\t\t\t\tmaxlength=\"20\"\r\n\t\t\t\t\t\t@blur=\"nameBlur\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"title-label\">身份证号</view>\r\n\t\t\t\t\t<input \r\n\t\t\t\t\t\tclass=\"input-box\" \r\n\t\t\t\t\t\tv-model=\"id_card_number\" \r\n\t\t\t\t\t\t:disabled=\"isReadonly\"\r\n\t\t\t\t\t\tplaceholder=\"请填写身份证号码\" \r\n\t\t\t\t\t\tmaxlength=\"18\"\r\n\t\t\t\t\t\t@blur=\"idCardBlur\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 状态提示 -->\r\n\t\t\t\t\t<view v-if=\"auth_status === 3 && verify_remark\" class=\"fail-reason\">\r\n\t\t\t\t\t\t审核失败：{{verify_remark}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"auth_status === 2\" class=\"success-msg\">\r\n\t\t\t\t\t\t已认证，无法修改\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"auth_status === 1\" class=\"pending-msg\">\r\n\t\t\t\t\t\t已提交，等待审核\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 协议勾选 -->\r\n\t\t\t\t<view class=\"protocol-row\" v-if=\"auth_status !== 2\">\r\n\t\t\t\t\t<checkbox-group @change=\"onCheckboxChange\">\r\n\t\t\t\t\t\t<label class=\"df\">\r\n\t\t\t\t\t\t\t<checkbox :checked=\"checked ? true : false\" :disabled=\"isReadonly\" style=\"transform:scale(0.7)\"/>\r\n\t\t\t\t\t\t\t<text class=\"protocol-text\">我已阅读并同意 <text class=\"protocol-link\" @tap=\"openProtocol\">《实名认证服务协议》</text></text>\r\n\t\t\t\t\t\t</label>\r\n\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 撤销申请按钮 -->\r\n\t\t\t\t<view v-if=\"auth_status === 1\" class=\"cancel-section\">\r\n\t\t\t\t\t<view class=\"footer-item df\">\r\n\t\t\t\t\t\t<button class=\"btn cancel-btn\" @tap=\"onCancel\" :disabled=\"loading\">\r\n\t\t\t\t\t\t\t撤销申请\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 底部安全区域 - 为固定底部按钮留出空间 -->\r\n\t\t\t\t<view class=\"bottom-safe-area\"></view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t\t\r\n\t\t<!-- Fixed footer -->\r\n\t\t<view class=\"footer-box bfw bUp\">\r\n\t\t\t<view class=\"footer-item df\">\r\n\t\t\t\t<button class=\"btn\" :class=\"auth_status === 2 ? 'btn-gray' : 'bg2'\" @tap=\"onSubmit\" :disabled=\"loading || auth_status === 2\">\r\n\t\t\t\t\t{{loading ? '提交中...' : getButtonText()}}\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 提示弹窗 -->\r\n\t\t<uni-popup ref=\"tipsPopup\" type=\"center\" >\r\n\t\t\t<view class=\"tips-box df\">\r\n\t\t\t\t<view class=\"tips-item\">{{tipsTitle}}</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { getRealAuthInfo, submitRealAuth, cancelRealAuth, getUserSocialInfo } from '@/api/social.js'\r\nimport uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'\r\n\r\nexport default {\r\n\tcomponents: {\r\n\t\tuniPopup\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\treal_name: '',\r\n\t\t\tid_card_number: '',\r\n\t\t\tchecked: false,\r\n\t\t\tauth_status: 0, // 0未认证 1待审核 2已认证 3失败\r\n\t\t\tverify_remark: '',\r\n\t\t\tloading: false,\r\n\t\t\ttipsTitle: \"\",\r\n\t\t\tuserInfo: {\r\n\t\t\t\tphone: ''\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tisReadonly() {\r\n\t\t\treturn this.auth_status === 2 || this.auth_status === 1;\r\n\t\t},\r\n\t\tcanSubmit() {\r\n\t\t\t// 如果已认证，不能提交\r\n\t\t\tif (this.auth_status === 2) return false;\r\n\t\t\t\r\n\t\t\treturn this.real_name && this.id_card_number && !this.isReadonly && !this.loading;\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\t// 显示加载中\r\n\t\tuni.showLoading({\r\n\t\t\ttitle: '加载中...',\r\n\t\t\tmask: true\r\n\t\t});\r\n\t\t\r\n\t\t// 并行加载数据\r\n\t\tPromise.all([\r\n\t\t\tthis.loadAuthInfo(),\r\n\t\t\tthis.loadUserInfo()\r\n\t\t]).catch(err => {\r\n\t\t\tconsole.error('初始化数据失败:', err);\r\n\t\t\tthis.opTipsPopup('加载失败，请重试');\r\n\t\t}).finally(() => {\r\n\t\t\tuni.hideLoading();\r\n\t\t});\r\n\t},\r\n\tmethods: {\r\n\t\t// 加载认证信息\r\n\t\tloadAuthInfo() {\r\n\t\t\tconst that = this;\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tgetRealAuthInfo().then(res => {\r\n\t\t\t\t\t// 检查是否成功 - 增加多种成功状态的检查\r\n\t\t\t\t\tconst isSuccess = res.code === 200 || res.status === 200 || res.msg === 'success';\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (isSuccess) {\r\n\t\t\t\t\t\tif (res.data) {\r\n\t\t\t\t\t\t\tthat.auth_status = res.data.auth_status || 0;\r\n\t\t\t\t\t\t\tthat.real_name = res.data.real_name || '';\r\n\t\t\t\t\t\t\tthat.id_card_number = res.data.id_card_number || '';\r\n\t\t\t\t\t\t\tthat.verify_remark = res.data.auth_remark || res.data.verify_remark || '';\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tconsole.log('获取认证信息成功:', res.data);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tresolve(res.data || {});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('获取认证信息失败:', res.msg);\r\n\t\t\t\t\t\treject(new Error(res.msg || '获取认证信息失败'));\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tconsole.error('获取认证信息请求错误:', err);\r\n\t\t\t\t\treject(err);\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 加载用户信息\r\n\t\tloadUserInfo() {\r\n\t\t\tconst that = this;\r\n\t\t\t// 先尝试从store获取用户信息\r\n\t\t\tconst storeUserInfo = this.$store.state.userInfo;\r\n\t\t\tif (storeUserInfo && storeUserInfo.phone) {\r\n\t\t\t\tthis.userInfo.phone = storeUserInfo.phone;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 返回Promise以便与Promise.all一起使用\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t// 调用API获取用户社交信息\r\n\t\t\t\tgetUserSocialInfo().then(res => {\r\n\t\t\t\t\t// 检查是否成功 - 增加多种成功状态的检查\r\n\t\t\t\t\tconst isSuccess = res.code === 200 || res.status === 200 || res.msg === 'success';\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (isSuccess) {\r\n\t\t\t\t\t\tif (res.data) {\r\n\t\t\t\t\t\t\tconsole.log('获取用户信息成功:', res.data);\r\n\t\t\t\t\t\t\t// 更新用户信息\r\n\t\t\t\t\t\t\tthat.userInfo = {\r\n\t\t\t\t\t\t\t\t...that.userInfo,\r\n\t\t\t\t\t\t\t\t...res.data\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t// 保存到本地\r\n\t\t\t\t\t\t\tuni.setStorageSync('USER_INFO', res.data);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tresolve(res.data || {});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('获取用户信息失败:', res.msg);\r\n\t\t\t\t\t\treject(new Error(res.msg || '获取用户信息失败'));\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tconsole.error('获取用户信息请求错误:', err);\r\n\t\t\t\t\treject(err);\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 获取按钮文本\r\n\t\tgetButtonText() {\r\n\t\t\t// 如果手机号未绑定，显示绑定手机号\r\n\t\t\tif (!this.userInfo.phone) {\r\n\t\t\t\treturn '请先绑定手机号';\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 如果基本数据未填写，显示提示\r\n\t\t\tif (!this.real_name || !this.id_card_number) {\r\n\t\t\t\treturn '请填写认证信息';\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tswitch (this.auth_status) {\r\n\t\t\t\tcase 0: return '提交认证';\r\n\t\t\t\tcase 1: return '审核中';\r\n\t\t\t\tcase 2: return '已认证';\r\n\t\t\t\tcase 3: return '重新提交';\r\n\t\t\t\tdefault: return '提交认证';\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 姓名输入验证\r\n\t\tnameBlur() {\r\n\t\t\t// 只在姓名不为空且长度不足时才提示\r\n\t\t\tif (this.real_name && this.real_name.trim() && this.real_name.length < 2) {\r\n\t\t\t\tthis.opTipsPopup('姓名长度不能少于2个字符');\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 身份证号输入验证\r\n\t\tidCardBlur() {\r\n\t\t\tconsole.log('身份证号失去焦点验证:', this.id_card_number);\r\n\t\t\t// 只在身份证号不为空且格式不正确时才提示\r\n\t\t\tif (this.id_card_number && this.id_card_number.trim() && !this.validateIdCard(this.id_card_number)) {\r\n\t\t\t\tthis.opTipsPopup('身份证号格式不正确');\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 身份证号验证\r\n\t\tvalidateIdCard(idCard) {\r\n\t\t\t// 基本格式验证\r\n\t\t\tif (!/^\\d{17}[\\dXx]$/.test(idCard)) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 校验码验证\r\n\t\t\tconst factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];\r\n\t\t\tconst verify = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];\r\n\t\t\t\r\n\t\t\tlet sum = 0;\r\n\t\t\tfor (let i = 0; i < 17; i++) {\r\n\t\t\t\tsum += parseInt(idCard[i]) * factor[i];\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst check = verify[sum % 11];\r\n\t\t\treturn idCard[17].toUpperCase() === check;\r\n\t\t},\r\n\t\t\r\n\t\t// 提交认证\r\n\t\tonSubmit() {\r\n\t\t\t// 如果已认证，不允许提交\r\n\t\t\tif (this.auth_status === 2) {\r\n\t\t\t\tthis.opTipsPopup('您已完成认证，无需重复提交');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 检查手机号是否已绑定\r\n\t\t\tif (!this.userInfo.phone) {\r\n\t\t\t\tthis.opTipsPopup('请先绑定手机号后再进行实名认证');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 表单验证\r\n\t\t\tif (!this.real_name.trim()) {\r\n\t\t\t\tthis.opTipsPopup('请输入真实姓名');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (!this.id_card_number.trim()) {\r\n\t\t\t\tthis.opTipsPopup('请输入身份证号');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (!this.validateIdCard(this.id_card_number)) {\r\n\t\t\t\tthis.opTipsPopup('身份证号格式不正确');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (!this.checked && this.auth_status !== 2) {\r\n\t\t\t\tthis.opTipsPopup('请先同意实名认证服务协议');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 检查是否正在加载\r\n\t\t\tif (this.loading) return;\r\n\t\t\t\r\n\t\t\tconst that = this;\r\n\t\t\tthis.loading = true;\r\n\t\t\tsubmitRealAuth({ \r\n\t\t\t\treal_name: this.real_name.trim(), \r\n\t\t\t\tid_card_number: this.id_card_number.trim() \r\n\t\t\t}).then(res => {\r\n\t\t\t\t// 检查是否成功 - 增加多种成功状态的检查\r\n\t\t\t\tconst isSuccess = res.code === 200 || res.status === 200 || res.msg === 'success';\r\n\t\t\t\t\r\n\t\t\t\tif (isSuccess) {\r\n\t\t\t\t\tthat.opTipsPopup('提交成功，等待审核');\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 刷新认证信息和用户信息\r\n\t\t\t\t\tPromise.all([\r\n\t\t\t\t\t\tthat.loadAuthInfo(),\r\n\t\t\t\t\t\tthat.loadUserInfo()\r\n\t\t\t\t\t]).catch(err => {\r\n\t\t\t\t\t\tconsole.error('刷新数据失败:', err);\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.opTipsPopup(res.msg || '提交失败');\r\n\t\t\t\t}\r\n\t\t\t}).catch(err => {\r\n\t\t\t\tthat.opTipsPopup('提交失败: ' + (err && err.msg ? err.msg : '网络错误'));\r\n\t\t\t}).finally(() => {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 撤销申请\r\n\t\tonCancel() {\r\n\t\t\tconst that = this;\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '确认撤销',\r\n\t\t\t\tcontent: '确定要撤销认证申请吗？撤销后可以重新提交。',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tthat.loading = true;\r\n\t\t\t\t\t\tcancelRealAuth().then(res => {\r\n\t\t\t\t\t\t\t// 检查是否成功 - 增加多种成功状态的检查\r\n\t\t\t\t\t\t\tconst isSuccess = res.code === 200 || res.status === 200 || res.msg === 'success';\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tif (isSuccess) {\r\n\t\t\t\t\t\t\t\tthat.opTipsPopup('撤销成功');\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 刷新认证信息和用户信息\r\n\t\t\t\t\t\t\t\tPromise.all([\r\n\t\t\t\t\t\t\t\t\tthat.loadAuthInfo(),\r\n\t\t\t\t\t\t\t\t\tthat.loadUserInfo()\r\n\t\t\t\t\t\t\t\t]).catch(err => {\r\n\t\t\t\t\t\t\t\t\tconsole.error('刷新数据失败:', err);\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthat.opTipsPopup(res.msg || '撤销失败');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\t\tthat.opTipsPopup('撤销失败: ' + (err && err.msg ? err.msg : '网络错误'));\r\n\t\t\t\t\t\t}).finally(() => {\r\n\t\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 打开协议\r\n\t\topenProtocol() {\r\n\t\t\tuni.navigateTo({ \r\n\t\t\t\turl: '/pages/setting/xinxuan?type=5' \r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 复选框变化处理\r\n\t\tonCheckboxChange(e) {\r\n\t\t\tconsole.log('协议勾选变化:', !this.checked, '->', this.checked);\r\n\t\t\tthis.$set(this, 'checked', !this.checked);\r\n\t\t},\r\n\t\t\r\n\t\t// 显示提示弹窗\r\n\t\topTipsPopup(msg, duration = 2000) {\r\n\t\t\tthis.tipsTitle = msg;\r\n\t\t\tthis.$refs.tipsPopup.open();\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.$refs.tipsPopup.close();\r\n\t\t\t}, duration);\r\n\t\t},\r\n\t\t\r\n\t\t// 绑定手机号\r\n\t\tbindMobileClick(e) {\r\n\t\t\tlet that = this;\r\n\t\t\tif (e.detail.errMsg === 'getPhoneNumber:ok') {\r\n\t\t\t\t// 获取到手机号，调用后端接口绑定\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: that.userInfo.phone ? '换绑授权中...' : '绑定授权中...',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 使用实际的绑定手机号API\r\n\t\t\t\tthat.$util.userBindingPhone(e.detail).then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t// 检查是否成功 - 增加多种成功状态的检查\r\n\t\t\t\t\tconst isSuccess = res.code == 200 || res.status == 200 || res.msg === 'success';\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (isSuccess) {\r\n\t\t\t\t\t\t// 更新用户信息\r\n\t\t\t\t\t\tthat.userInfo.phone = res.data.phone || res.data.mobile;\r\n\t\t\t\t\t\t// 提示成功\r\n\t\t\t\t\t\tthat.opTipsPopup(res.msg || '手机号绑定成功');\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.opTipsPopup(res.msg || '绑定失败');\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.opTipsPopup('绑定失败: ' + (typeof err === 'string' ? err : '网络错误'));\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tthat.opTipsPopup('获取手机号失败');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.page-wrapper {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\theight: 100vh;\r\n\tbackground: #fff;\r\n\tposition: relative;\r\n}\r\n\r\n.content-scroll {\r\n\tflex: 1;\r\n\toverflow-y: auto;\r\n}\r\n\r\n.container {\r\n\twidth: calc(100% - 60rpx);\r\n\tpadding: 0 30rpx;\r\n\tbackground: #fff;\r\n}\r\n\r\n.title-box {\r\n\tpadding: 20rpx 0;\r\n\tfont-size: 40rpx;\r\n\tfont-weight: 700;\r\n}\r\n\r\n.title-box view:last-child {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tfont-weight: normal;\r\n\tmargin-top: 10rpx;\r\n}\r\n\r\n.desc {\r\n\tcolor: #888;\r\n\tfont-size: 24rpx;\r\n\tmargin-bottom: 30rpx;\r\n\tline-height: 1.4;\r\n}\r\n\r\n.form-section {\r\n\tbackground: #fff;\r\n\tborder-radius: 20rpx;\r\n\tbox-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);\r\n\tpadding: 20rpx 0 10rpx 0;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.title-label {\r\n\twidth: calc(100% - 48rpx);\r\n\tpadding: 30rpx 24rpx 12rpx;\r\n\tcolor: #999;\r\n\tfont-size: 24rpx;\r\n\tfont-weight: 700;\r\n}\r\n\r\n.input-box {\r\n\twidth: calc(100% - 68rpx);\r\n\tpadding: 0 30rpx;\r\n\theight: 90rpx;\r\n\tline-height: 90rpx;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 700;\r\n\tborder: 4rpx solid #f5f5f5;\r\n\tborder-radius: 24rpx;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.input-box[disabled] {\r\n\tbackground: #f8f8f8;\r\n\tcolor: #bbb;\r\n}\r\n\r\n.value {\r\n\tcolor: #333;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.fail-reason {\r\n\tcolor: #fa5150;\r\n\tfont-size: 24rpx;\r\n\tmargin: 10rpx 24rpx 20rpx;\r\n\tpadding: 20rpx;\r\n\tbackground: rgba(250, 81, 80, 0.1);\r\n\tborder-radius: 12rpx;\r\n}\r\n\r\n.success-msg {\r\n\tcolor: #4cd964;\r\n\tfont-size: 24rpx;\r\n\tmargin: 10rpx 24rpx 20rpx;\r\n\tpadding: 20rpx;\r\n\tbackground: rgba(76, 217, 100, 0.1);\r\n\tborder-radius: 12rpx;\r\n}\r\n\r\n.pending-msg {\r\n\tcolor: #faad14;\r\n\tfont-size: 24rpx;\r\n\tmargin: 10rpx 24rpx 20rpx;\r\n\tpadding: 20rpx;\r\n\tbackground: rgba(250, 173, 20, 0.1);\r\n\tborder-radius: 12rpx;\r\n}\r\n\r\n.protocol-row {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin: 20rpx 0;\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.protocol-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.protocol-link {\r\n\tcolor: #576b95;\r\n}\r\n\r\n/* 底部按钮样式 */\r\n.footer-box {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\tpadding-bottom: env(safe-area-inset-bottom);\r\n\tbackground: #fff;\r\n\tz-index: 99;\r\n}\r\n\r\n.footer-box .footer-item {\r\n\twidth: calc(100% - 60rpx);\r\n\tpadding: 20rpx 30rpx;\r\n\tjustify-content: center;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.footer-item .btn {\r\n\twidth: calc(100% - 30rpx);\r\n\theight: 90rpx;\r\n\tline-height: 90rpx;\r\n\ttext-align: center;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 700;\r\n\tborder-radius: 45rpx;\r\n}\r\n\r\n.bg2 {\r\n\tcolor: #fff;\r\n\tbackground: #000;\r\n}\r\n\r\n.bg2[disabled] {\r\n\tbackground: #ccc !important;\r\n\tcolor: #fff !important;\r\n\topacity: 0.6;\r\n}\r\n\r\n.btn-gray {\r\n\tbackground: #ccc !important;\r\n\tcolor: #fff !important;\r\n\topacity: 0.6;\r\n\tcursor: not-allowed;\r\n}\r\n\r\nbutton[disabled] {\r\n\tbackground: #ccc !important;\r\n\tcolor: #fff !important;\r\n\topacity: 0.6;\r\n}\r\n\r\n.cancel-section {\r\n\tmargin: 20rpx 0 40rpx;\r\n\twidth: 100%;\r\n}\r\n\r\n.cancel-btn {\r\n\tcolor: #666;\r\n\tbackground: #f5f5f5;\r\n\tborder: 2rpx solid #ddd;\r\n}\r\n\r\n.cancel-btn[disabled] {\r\n\tbackground: #f0f0f0;\r\n\tcolor: #ccc;\r\n}\r\n\r\n.bfw {\r\n\tbackground: #fff;\r\n}\r\n\r\n.bUp {\r\n\tbox-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* 提示弹窗样式 */\r\n.tips-box {\r\n\tpadding: 20rpx 30rpx;\r\n\tborder-radius: 12rpx;\r\n\tjustify-content: center;\r\n\tmargin-top: 40rpx;\r\n}\r\n\r\n.tips-box .tips-item {\r\n\tcolor: #fff;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 600;\r\n\ttext-align: center;\r\n}\r\n\r\n.df {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n/* 底部安全区域 */\r\n.bottom-safe-area {\r\n\theight: 150rpx;\r\n\twidth: 100%;\r\n\tmargin-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* 输入框样式优化 */\r\ninput {\r\n\tborder: none;\r\n\tbackground: transparent;\r\n\toutline: none;\r\n\twidth: 100%;\r\n}\r\n\r\ninput[disabled] {\r\n\tcolor: #bbb;\r\n}\r\n\r\n/* 复选框样式 */\r\ncheckbox {\r\n\ttransform: scale(0.8);\r\n}\r\n\r\n/* 手机号绑定按钮样式 */\r\n.input-btn {\r\n\twidth: 90rpx;\r\n\theight: 90rpx;\r\n\tfont-size: 24rpx;\r\n\tjustify-content: space-between;\r\n\tmargin: 0;\r\n\tpadding: 0;\r\n\tbackground: #fff;\r\n}\r\n\r\n.input-btn image {\r\n\tmargin-right: 8rpx;\r\n\twidth: 32rpx;\r\n\theight: 32rpx;\r\n}\r\n\r\n.input-tips {\r\n\tcolor: #999;\r\n\tfont-size: 24rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n/* Adjust the protocol row spacing */\r\n.protocol-row {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin: 20rpx 0;\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* Ensure the footer button has proper spacing */\r\n.footer-box .footer-item {\r\n\twidth: calc(100%);\r\n\tpadding: 20rpx 30rpx;\r\n\tjustify-content: center;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n/* Add box shadow to make the footer stand out */\r\n.bUp {\r\n\tbox-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/uniapp/vue3/pages/setting/realname.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getRealAuthInfo", "getUserSocialInfo", "submitRealAuth", "cancelRealAuth", "res"], "mappings": ";;;;AAuGA,iBAAiB,MAAW;AAE5B,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,aAAa;AAAA;AAAA,MACb,eAAe;AAAA,MACf,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,QACT,OAAO;AAAA,MACR;AAAA,IACD;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,aAAa;AACZ,aAAO,KAAK,gBAAgB,KAAK,KAAK,gBAAgB;AAAA,IACtD;AAAA,IACD,YAAY;AAEX,UAAI,KAAK,gBAAgB;AAAG,eAAO;AAEnC,aAAO,KAAK,aAAa,KAAK,kBAAkB,CAAC,KAAK,cAAc,CAAC,KAAK;AAAA,IAC3E;AAAA,EACA;AAAA,EACD,SAAS;AAERA,kBAAAA,MAAI,YAAY;AAAA,MACf,OAAO;AAAA,MACP,MAAM;AAAA,IACP,CAAC;AAGD,YAAQ,IAAI;AAAA,MACX,KAAK,aAAc;AAAA,MACnB,KAAK,aAAa;AAAA,IACnB,CAAC,EAAE,MAAM,SAAO;AACfA,oBAAc,MAAA,MAAA,SAAA,qCAAA,YAAY,GAAG;AAC7B,WAAK,YAAY,UAAU;AAAA,IAC5B,CAAC,EAAE,QAAQ,MAAM;AAChBA,oBAAG,MAAC,YAAW;AAAA,IAChB,CAAC;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAER,eAAe;AACd,YAAM,OAAO;AACb,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCC,mCAAiB,EAAC,KAAK,SAAO;AAE7B,gBAAM,YAAY,IAAI,SAAS,OAAO,IAAI,WAAW,OAAO,IAAI,QAAQ;AAExE,cAAI,WAAW;AACd,gBAAI,IAAI,MAAM;AACb,mBAAK,cAAc,IAAI,KAAK,eAAe;AAC3C,mBAAK,YAAY,IAAI,KAAK,aAAa;AACvC,mBAAK,iBAAiB,IAAI,KAAK,kBAAkB;AACjD,mBAAK,gBAAgB,IAAI,KAAK,eAAe,IAAI,KAAK,iBAAiB;AAEvED,4BAAY,MAAA,MAAA,OAAA,qCAAA,aAAa,IAAI,IAAI;AAAA,YAClC;AACA,oBAAQ,IAAI,QAAQ,CAAA,CAAE;AAAA,iBAChB;AACNA,0BAAA,MAAA,MAAA,SAAA,qCAAc,aAAa,IAAI,GAAG;AAClC,mBAAO,IAAI,MAAM,IAAI,OAAO,UAAU,CAAC;AAAA,UACxC;AAAA,QACD,CAAC,EAAE,MAAM,SAAO;AACfA,wBAAA,MAAA,MAAA,SAAA,qCAAc,eAAe,GAAG;AAChC,iBAAO,GAAG;AAAA,QACX,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACd,YAAM,OAAO;AAEb,YAAM,gBAAgB,KAAK,OAAO,MAAM;AACxC,UAAI,iBAAiB,cAAc,OAAO;AACzC,aAAK,SAAS,QAAQ,cAAc;AAAA,MACrC;AAGA,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEvCE,qCAAmB,EAAC,KAAK,SAAO;AAE/B,gBAAM,YAAY,IAAI,SAAS,OAAO,IAAI,WAAW,OAAO,IAAI,QAAQ;AAExE,cAAI,WAAW;AACd,gBAAI,IAAI,MAAM;AACbF,4BAAY,MAAA,MAAA,OAAA,qCAAA,aAAa,IAAI,IAAI;AAEjC,mBAAK,WAAW;AAAA,gBACf,GAAG,KAAK;AAAA,gBACR,GAAG,IAAI;AAAA;AAGRA,4BAAAA,MAAI,eAAe,aAAa,IAAI,IAAI;AAAA,YACzC;AACA,oBAAQ,IAAI,QAAQ,CAAA,CAAE;AAAA,iBAChB;AACNA,0BAAA,MAAA,MAAA,SAAA,qCAAc,aAAa,IAAI,GAAG;AAClC,mBAAO,IAAI,MAAM,IAAI,OAAO,UAAU,CAAC;AAAA,UACxC;AAAA,QACD,CAAC,EAAE,MAAM,SAAO;AACfA,wBAAA,MAAA,MAAA,SAAA,qCAAc,eAAe,GAAG;AAChC,iBAAO,GAAG;AAAA,QACX,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AAEf,UAAI,CAAC,KAAK,SAAS,OAAO;AACzB,eAAO;AAAA,MACR;AAGA,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,gBAAgB;AAC5C,eAAO;AAAA,MACR;AAEA,cAAQ,KAAK,aAAW;AAAA,QACvB,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf;AAAS,iBAAO;AAAA,MACjB;AAAA,IACA;AAAA;AAAA,IAGD,WAAW;AAEV,UAAI,KAAK,aAAa,KAAK,UAAU,KAAO,KAAG,KAAK,UAAU,SAAS,GAAG;AACzE,aAAK,YAAY,cAAc;AAAA,MAChC;AAAA,IACA;AAAA;AAAA,IAGD,aAAa;AACZA,oBAAA,MAAA,MAAA,OAAA,qCAAY,eAAe,KAAK,cAAc;AAE9C,UAAI,KAAK,kBAAkB,KAAK,eAAe,UAAU,CAAC,KAAK,eAAe,KAAK,cAAc,GAAG;AACnG,aAAK,YAAY,WAAW;AAAA,MAC7B;AAAA,IACA;AAAA;AAAA,IAGD,eAAe,QAAQ;AAEtB,UAAI,CAAC,iBAAiB,KAAK,MAAM,GAAG;AACnC,eAAO;AAAA,MACR;AAGA,YAAM,SAAS,CAAC,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;AACnE,YAAM,SAAS,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAErE,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,eAAO,SAAS,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC;AAAA,MACtC;AAEA,YAAM,QAAQ,OAAO,MAAM,EAAE;AAC7B,aAAO,OAAO,EAAE,EAAE,YAAW,MAAO;AAAA,IACpC;AAAA;AAAA,IAGD,WAAW;AAEV,UAAI,KAAK,gBAAgB,GAAG;AAC3B,aAAK,YAAY,eAAe;AAChC;AAAA,MACD;AAGA,UAAI,CAAC,KAAK,SAAS,OAAO;AACzB,aAAK,YAAY,iBAAiB;AAClC;AAAA,MACD;AAGA,UAAI,CAAC,KAAK,UAAU,QAAQ;AAC3B,aAAK,YAAY,SAAS;AAC1B;AAAA,MACD;AAEA,UAAI,CAAC,KAAK,eAAe,QAAQ;AAChC,aAAK,YAAY,SAAS;AAC1B;AAAA,MACD;AAEA,UAAI,CAAC,KAAK,eAAe,KAAK,cAAc,GAAG;AAC9C,aAAK,YAAY,WAAW;AAC5B;AAAA,MACD;AAEA,UAAI,CAAC,KAAK,WAAW,KAAK,gBAAgB,GAAG;AAC5C,aAAK,YAAY,cAAc;AAC/B;AAAA,MACD;AAGA,UAAI,KAAK;AAAS;AAElB,YAAM,OAAO;AACb,WAAK,UAAU;AACfG,gCAAe;AAAA,QACd,WAAW,KAAK,UAAU,KAAM;AAAA,QAChC,gBAAgB,KAAK,eAAe,KAAK;AAAA,OACzC,EAAE,KAAK,SAAO;AAEd,cAAM,YAAY,IAAI,SAAS,OAAO,IAAI,WAAW,OAAO,IAAI,QAAQ;AAExE,YAAI,WAAW;AACd,eAAK,YAAY,WAAW;AAG5B,kBAAQ,IAAI;AAAA,YACX,KAAK,aAAc;AAAA,YACnB,KAAK,aAAa;AAAA,UACnB,CAAC,EAAE,MAAM,SAAO;AACfH,0BAAc,MAAA,MAAA,SAAA,qCAAA,WAAW,GAAG;AAAA,UAC7B,CAAC;AAAA,eACK;AACN,eAAK,YAAY,IAAI,OAAO,MAAM;AAAA,QACnC;AAAA,MACD,CAAC,EAAE,MAAM,SAAO;AACf,aAAK,YAAY,YAAY,OAAO,IAAI,MAAM,IAAI,MAAM,OAAO;AAAA,MAChE,CAAC,EAAE,QAAQ,MAAM;AAChB,aAAK,UAAU;AAAA,MAChB,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW;AACV,YAAM,OAAO;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,UAAU;AACfI,sCAAgB,EAAC,KAAK,CAAAC,SAAO;AAE5B,oBAAM,YAAYA,KAAI,SAAS,OAAOA,KAAI,WAAW,OAAOA,KAAI,QAAQ;AAExE,kBAAI,WAAW;AACd,qBAAK,YAAY,MAAM;AAGvB,wBAAQ,IAAI;AAAA,kBACX,KAAK,aAAc;AAAA,kBACnB,KAAK,aAAa;AAAA,gBACnB,CAAC,EAAE,MAAM,SAAO;AACfL,gCAAc,MAAA,MAAA,SAAA,qCAAA,WAAW,GAAG;AAAA,gBAC7B,CAAC;AAAA,qBACK;AACN,qBAAK,YAAYK,KAAI,OAAO,MAAM;AAAA,cACnC;AAAA,YACD,CAAC,EAAE,MAAM,SAAO;AACf,mBAAK,YAAY,YAAY,OAAO,IAAI,MAAM,IAAI,MAAM,OAAO;AAAA,YAChE,CAAC,EAAE,QAAQ,MAAM;AAChB,mBAAK,UAAU;AAAA,YAChB,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACdL,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB,GAAG;AACnBA,oBAAAA,MAAY,MAAA,OAAA,qCAAA,WAAW,CAAC,KAAK,SAAS,MAAM,KAAK,OAAO;AACxD,WAAK,KAAK,MAAM,WAAW,CAAC,KAAK,OAAO;AAAA,IACxC;AAAA;AAAA,IAGD,YAAY,KAAK,WAAW,KAAM;AACjC,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU;AACrB,iBAAW,MAAM;AAChB,aAAK,MAAM,UAAU;MACrB,GAAE,QAAQ;AAAA,IACX;AAAA;AAAA,IAGD,gBAAgB,GAAG;AAClB,UAAI,OAAO;AACX,UAAI,EAAE,OAAO,WAAW,qBAAqB;AAE5CA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO,KAAK,SAAS,QAAQ,aAAa;AAAA,UAC1C,MAAM;AAAA,QACP,CAAC;AAGD,aAAK,MAAM,iBAAiB,EAAE,MAAM,EAAE,KAAK,SAAO;AACjDA,wBAAG,MAAC,YAAW;AAEf,gBAAM,YAAY,IAAI,QAAQ,OAAO,IAAI,UAAU,OAAO,IAAI,QAAQ;AAEtE,cAAI,WAAW;AAEd,iBAAK,SAAS,QAAQ,IAAI,KAAK,SAAS,IAAI,KAAK;AAEjD,iBAAK,YAAY,IAAI,OAAO,SAAS;AAAA,iBAC/B;AACN,iBAAK,YAAY,IAAI,OAAO,MAAM;AAAA,UACnC;AAAA,QACD,CAAC,EAAE,MAAM,SAAO;AACfA,wBAAG,MAAC,YAAW;AACf,eAAK,YAAY,YAAY,OAAO,QAAQ,WAAW,MAAM,OAAO;AAAA,QACrE,CAAC;AAAA,aACK;AACN,aAAK,YAAY,SAAS;AAAA,MAC3B;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpbA,GAAG,WAAW,eAAe;"}