<view class="container"><view class="nav-box" style="{{'padding-top:' + d}}"><view class="bar-box df" style="{{'height:' + c}}"><view class="nav-scroll-box"><scroll-view id="nav-scroll" scroll-x="true" show-scrollbar="false" class="nav-scroll" scroll-into-view="{{b}}" scroll-with-animation="true"><view class="nav-scroll-content"><view wx:for="{{a}}" wx:for-item="item" wx:key="c" class="{{['nav-item', item.d && 'active']}}" data-idx="{{item.e}}" id="{{item.f}}" bindtap="{{item.g}}"><text>{{item.a}}</text><view wx:if="{{item.b}}" class="active-line"></view></view></view></scroll-view></view></view></view><view class="content-box" style="{{'padding-top:' + U}}"><swiper class="content-swiper" current="{{R}}" bindchange="{{S}}" style="{{'height:' + T}}" duration="{{300}}" easing-function="{{'easeInOutCubic'}}" skip-hidden-item-layout="{{true}}" disable-touch="{{false}}"><swiper-item><scroll-view scroll-y="true" bindscrolltolower="{{m}}" bindscroll="{{n}}" class="content-scroll" show-scrollbar="{{true}}"><empty-page wx:if="{{e}}" bindbuttonClick="{{f}}" u-i="2ca76830-0" bind:__l="__l" u-p="{{g}}"/><empty-page wx:elif="{{h}}" u-i="2ca76830-1" bind:__l="__l" u-p="{{i}}"/><view wx:elif="{{j}}"><block wx:for="{{k}}" wx:for-item="item" wx:key="f"><card-gg wx:if="{{item.e}}" bindlikeback="{{item.a}}" bindfollowback="{{item.b}}" bindupdate="{{item.c}}" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"></card-gg></block><uni-load-more wx:if="{{l}}" u-i="2ca76830-3" bind:__l="__l" u-p="{{l}}"></uni-load-more></view></scroll-view></swiper-item><swiper-item><scroll-view scroll-y="true" bindscrolltolower="{{D}}" bindscroll="{{E}}" class="content-scroll" show-scrollbar="{{true}}"><scroll-view wx:if="{{o}}" scroll-x="true" class="scroll-box" style="height:246rpx"><view class="circle-box"><view wx:for="{{p}}" wx:for-item="item" wx:key="i" class="circle-item" data-url="{{item.j}}" bindtap="{{item.k}}"><view class="circle-item-top"><image src="{{item.a}}" mode="aspectFill"></image><view wx:if="{{item.b}}" class="circle-item-tag" style="background:url(/static/img/gf.png) 0% 0% / 100% 100%"></view><view wx:elif="{{item.c}}" class="circle-item-tag" style="background:url(/static/img/tj.png) 0% 0% / 100% 100%"></view></view><view class="circle-name ohto">{{item.d}}</view><view class="circle-tips"><text wx:if="{{item.e}}">{{item.f}}更新</text><text wx:elif="{{item.g}}">{{item.h}}新圈友</text><text wx:else>推荐的圈子</text></view></view><view class="circle-item" data-url="center/circle" bindtap="{{r}}"><view class="circle-item-top"><image class="icon" src="{{q}}"></image></view><view class="circle-name">更多圈子</view></view><view class="circle-item" style="width:10rpx"></view></view></scroll-view><view wx:if="{{s}}" class="empty-box df"><image src="{{t}}"/><view class="e1">暂无推荐内容</view><view class="e2">去发笔记，或许就上推荐了</view></view><view wx:elif="{{v}}" class="{{[C]}}"><waterfall wx:if="{{w}}" bindlikeback="{{x}}" bindfollowback="{{y}}" u-i="2ca76830-4" bind:__l="__l" u-p="{{z}}"></waterfall><block wx:else><block wx:for="{{A}}" wx:for-item="item" wx:key="f"><card-gg wx:if="{{item.e}}" bindlikeback="{{item.a}}" bindfollowback="{{item.b}}" bindrefresh="{{item.c}}" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"></card-gg></block></block><uni-load-more wx:if="{{B}}" u-i="2ca76830-6" bind:__l="__l" u-p="{{B}}"></uni-load-more></view></scroll-view></swiper-item><swiper-item><scroll-view scroll-y="true" bindscrolltolower="{{P}}" bindscroll="{{Q}}" class="content-scroll" show-scrollbar="{{true}}"><empty-page wx:if="{{F}}" u-i="2ca76830-7" bind:__l="__l" u-p="{{G}}"/><view wx:elif="{{H}}" class="{{[O]}}"><waterfall wx:if="{{I}}" bindlikeback="{{J}}" bindfollowback="{{K}}" u-i="2ca76830-8" bind:__l="__l" u-p="{{L}}"></waterfall><block wx:else><block wx:for="{{M}}" wx:for-item="item" wx:key="f"><card-gg wx:if="{{item.e}}" bindlikeback="{{item.a}}" bindfollowback="{{item.b}}" bindrefresh="{{item.c}}" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"></card-gg></block></block><uni-load-more wx:if="{{N}}" u-i="2ca76830-10" bind:__l="__l" u-p="{{N}}"></uni-load-more></view></scroll-view></swiper-item></swiper></view><tabbar wx:if="{{V}}" u-i="2ca76830-11" bind:__l="__l" u-p="{{V}}"></tabbar></view>