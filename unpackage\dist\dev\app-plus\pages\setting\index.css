
.nav-bar[data-v-eaf4c2e5] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.nav-bar .navbar-item[data-v-eaf4c2e5] {
  width: 100%;
  display: flex;
  align-items: center;
}
.navbar-item .back-box[data-v-eaf4c2e5] {
  padding: 0 0.9375rem;
  display: flex;
  align-items: center;
}
.navbar-item .back-box uni-image[data-v-eaf4c2e5] {
  width: 1.0625rem;
  height: 1.0625rem;
}
.bfw[data-v-eaf4c2e5] {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255,255,255,.8);
}
.bf8[data-v-eaf4c2e5] {
  background: #f8f8f8;
}

.popup-bg[data-v-dc4a3d83] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 23.4375rem;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 10000;
}
.popup-content[data-v-dc4a3d83] {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.popup-content-show[data-v-dc4a3d83] {
  animation: mymove-dc4a3d83 500ms;
  transform: scale(1);
}
@keyframes mymove-dc4a3d83 {
0% {
    transform: scale(0);
    /*开始为原始大小*/
}
100% {
    transform: scale(1);
}
}
.update-wrap[data-v-dc4a3d83] {
  width: 18.125rem;
  border-radius: 0.5625rem;
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  padding: 5.3125rem 0.9375rem 0;
}
.update-wrap .top-img[data-v-dc4a3d83] {
  position: absolute;
  left: 0;
  width: 100%;
  height: 8rem;
  top: -4rem;
}
.update-wrap .content[data-v-dc4a3d83] {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 1.25rem;
}
.update-wrap .content .title[data-v-dc4a3d83] {
  font-size: 1rem;
  font-weight: bold;
  color: #6526f3;
}
.update-wrap .content .title-sub[data-v-dc4a3d83] {
  text-align: center;
  font-size: 0.75rem;
  color: #666666;
  padding: 0.9375rem 0;
}
.update-wrap .content .btn[data-v-dc4a3d83] {
  width: 14.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 0.9375rem;
  height: 2.5rem;
  line-height: 2.5rem;
  border-radius: 100px;
  background-color: #6526f3;
  margin-top: 0.625rem;
}
.close-ioc[data-v-dc4a3d83] {
  width: 2.1875rem;
  height: 2.1875rem;
  margin-top: 0.9375rem;
}
.sche-wrap[data-v-dc4a3d83] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  padding: 0.3125rem 1.5625rem 0;
}
.sche-wrap .sche-wrap-text[data-v-dc4a3d83] {
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 0.625rem;
}
.sche-wrap .sche-bg[data-v-dc4a3d83] {
  position: relative;
  background-color: #cccccc;
  height: 0.9375rem;
  border-radius: 100px;
  width: 15rem;
  display: flex;
  align-items: center;
}
.sche-wrap .sche-bg .sche-bg-jindu[data-v-dc4a3d83] {
  position: absolute;
  left: 0;
  top: 0;
  height: 0.9375rem;
  min-width: 1.25rem;
  border-radius: 100px;
  background: url(../../assets/round.c38b48ab.png) #5775e7 center right 0.125rem no-repeat;
  background-size: 0.8125rem 0.8125rem;
}
.sche-wrap .down-text[data-v-dc4a3d83] {
  font-size: 0.75rem;
  color: #5674e5;
  margin-top: 0.5rem;
}

body {
  background: #f8f8f8;
  padding: 0 0.9375rem;
  box-sizing: border-box;
  overflow-x: hidden;
}
.title-box {
	padding: 0.625rem 0;
	font-size: 1.25rem;
	font-weight: bold;
}
.table {
	padding: 0.9375rem 0;
	color: #999;
	font-size: 0.75rem;
	font-weight: 500;
}
.list-box {
  width: 100%;
  border-radius: 0.75rem;
  overflow: hidden;
  box-sizing: border-box;
}
.list-box .list {
	margin: 0;
	padding: 0;
	width: 100%;
	background: #fff!important;
	border-radius: 0;
}
.list-box .list:last-child {
	border-bottom: none;
}
.list-box .list .icon {
	margin: 0 0.9375rem;
	width: 1.1875rem;
	height: 1.1875rem;
}
.list-box .list-item {
	width: calc(100% - 3.0625rem);
	padding: 0.9375rem 0.9375rem 0.9375rem 0;
	justify-content: space-between;
}
.list-box .list-item .title {
	font-size: 0.75rem;
	font-weight: 500;
	min-width: 3.75rem;
}
.list-box .list-item uni-image {
	width: 0.75rem;
	height: 0.75rem;
	transform: rotate(-90deg);
}

/* 按钮样式 */
.input-btn {
	background: transparent !important;
	border: none !important;
	color: #868686 !important;
	font-size: 0.75rem !important;
	text-align: right;
	padding: 0 !important;
	margin: 0 !important;
	line-height: normal !important;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}
.input-btn::after {
	border: none !important;
}

/* 数值显示样式 */
.list-box .list-item .value {
	color: #868686;
	font-size: 0.75rem;
	margin-right: 0.3125rem;
}

/* 退出登录按钮样式 */
.logout-section {
  margin-top: 0.9375rem;
}
.logout-btn {
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 21.5625rem;
  height: 2.8125rem;
  border-radius: 1.40625rem;
  box-shadow: 0 0.125rem 0.375rem rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}
.logout-btn:active {
  transform: translateY(0.0625rem);
  box-shadow: 0 0.0625rem 0.25rem rgba(255, 107, 107, 0.4);
}
.logout-btn::after {
  border: none;
}
.logout-icon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.3125rem;
  filter: brightness(0) invert(1);
}
.logout-text {
  font-size: 1rem;
  color: #fff;
  font-weight: 500;
}
.version {
	padding: 1.875rem 0;
	flex-direction: column;
	justify-content: center;
}
.version uni-image {
	margin-right: 0.3125rem;
	width: 0.625rem;
	height: 0.625rem;
}
.version uni-text {
	color: #999;
	font-size: 0.5625rem;
}
.df {
  display: flex;
  align-items: center;
}
.acea-row {
  display: flex;
  flex-direction: row;
}
.row-center-wrapper {
  justify-content: center;
}
.row-between-wrapper {
  justify-content: space-between;
}
.row-middle {
  align-items: center;
}
.bb1 {
  border-bottom: 1px solid #f8f8f8;
}

/* 图标样式 */
.iconfont {
	font-size: 0.75rem;
	color: #ccc;
}
.icon-xiangyou::before {
	content: '>';
}
.icon-suozi::before {
	content: '🔒';
}

/* 列表项间距调整 */
.list-box .list:not(:last-child) .list-item {
	border-bottom: 1px solid #f8f8f8;
}
.list-box .list:last-child .list-item {
	border-bottom: none;
}

/* 账号切换样式 */
.wrapper {
  margin: 0.3125rem 0;
  background-color: #fff;
  padding: 1.125rem 0.9375rem 0.40625rem 0.9375rem;
  border-radius: 0.5rem;
}
.wrapper .title {
  margin-bottom: 0.9375rem;
  font-size: 1rem;
  color: #282828;
  font-weight: bold;
}
.wrapper .wrapList .item {
  width: 21.5625rem;
  height: 5rem;
  background-color: #f8f8f8;
  border-radius: 0.625rem;
  margin-bottom: 0.6875rem;
  padding: 0 0.9375rem;
  position: relative;
  border: 0.0625rem solid #f8f8f8;
  box-sizing: border-box;
}
.wrapper .wrapList .item.on {
  border-color: var(--view-theme);
  border-radius: 0.625rem;
  background-color: #fff9f9;
}
.wrapper .wrapList .item .picTxt {
  width: 13.90625rem;
}
.wrapper .wrapList .item .picTxt .pictrue {
  width: 3rem;
  height: 3rem;
  position: relative;
}
.wrapper .wrapList .item .picTxt .pictrue uni-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.wrapper .wrapList .item .picTxt .text {
  width: 10.15625rem;
}
.wrapper .wrapList .item .picTxt .text .name {
  width: 100%;
  font-size: 0.9375rem;
  color: #282828;
}
.wrapper .wrapList .item .picTxt .text .phone {
  font-size: 0.75rem;
  color: #999;
  margin-top: 0.3125rem;
}
.wrapper .wrapList .item .bnt {
  font-size: 0.75rem;
  border-radius: 0.84375rem;
  width: 4.375rem;
  height: 1.6875rem;
  border: 0.0625rem solid var(--view-theme);
  color: var(--view-theme);
  background-color: #fff;
}
.wrapper .wrapList .item .currentBnt {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 0.8125rem;
  background-color: rgba(233, 51, 35, 0.1);
  width: 4.375rem;
  height: 1.5rem;
  border-radius: 0 0.625rem 0 0.625rem;
  color: var(--view-theme);
}

/* 输入框焦点样式 */
.list-box .list-item .input uni-input:focus {
	color: #333;
}

/* 禁用状态样式 */
.list-box .list-item .input uni-input[disabled] {
	color: #ccc;
}

/* 表单区域样式 */
uni-form {
	margin-bottom: 0.625rem;
}

/* 针对不同平台的样式适配 */
uni-button::after {
  border: none;
}


/* 语言切换选择器样式 */
.picker-content {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: #868686;
  font-size: 0.75rem;
}
.picker-text {
  margin-right: 0.3125rem;
}
.picker-content .iconfont {
  font-size: 0.75rem;
  color: #ccc;
}
