<view wx:if="{{a}}" class="emoji-panel"><scroll-view class="emoji-scroll" scroll-y enhanced show-scrollbar="{{false}}"><view class="emoji-container"><view wx:if="{{b}}" class="recent-section"><view class="section-title">最近使用</view><view class="recent-emojis"><view wx:for="{{c}}" wx:for-item="emoji" wx:key="c" class="emoji-item" catchtap="{{emoji.d}}" bindlongpress="{{emoji.e}}"><image class="emoji-image" src="{{emoji.a}}" lazy-load="{{true}}" mode="aspectFit" binderror="{{emoji.b}}"></image></view></view></view><view wx:if="{{d}}" class="section-divider"></view><view class="all-emojis"><view wx:for="{{e}}" wx:for-item="emoji" wx:key="c" class="emoji-item" catchtap="{{emoji.d}}" bindlongpress="{{emoji.e}}"><image class="emoji-image" src="{{emoji.a}}" lazy-load="{{true}}" mode="aspectFit" binderror="{{emoji.b}}"></image></view></view></view></scroll-view><view wx:if="{{f}}" class="emoji-preview" bindtap="{{l}}"><view class="preview-content" catchtap="{{k}}"><image class="preview-image" src="{{g}}" mode="aspectFit"></image><text class="preview-name">{{h}}</text><view class="preview-actions"><view class="preview-btn" bindtap="{{i}}">发送</view><view class="preview-btn secondary" bindtap="{{j}}">取消</view></view></view></view></view>