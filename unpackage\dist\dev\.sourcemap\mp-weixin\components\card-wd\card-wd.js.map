{"version": 3, "file": "card-wd.js", "sources": ["components/card-wd/card-wd.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovdW5pYXBwL3Z1ZTMvY29tcG9uZW50cy9jYXJkLXdkL2NhcmQtd2QudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"gg-box\" @tap=\"toPaths\">\r\n    <!-- 用户头像/日期 -->\r\n    <view v-if=\"bar == 1\" class=\"gg-avatar\" @tap.stop=\"toPaths\" data-type=\"1\">\r\n      <lazyImage v-if=\"item.avatar\" :src=\"item.avatar\" br=\"50%\"></lazyImage>\r\n    </view>\r\n    <view v-else class=\"gg-avatar rq\">{{getDay}}</view>\r\n    <view class=\"timeline-line\"></view>\r\n    <!-- 内容区域 -->\r\n\r\n    <view class=\"gg-item\">\r\n      <!-- 用户信息/日期 -->\r\n      <view v-if=\"bar == 1\" class=\"gg-item-name\">\r\n        <view class=\"ohto\">{{item.nickname || '用户'}}</view>\r\n        {{formatTime}} · {{item.province || '未知地区'}}\r\n      </view>\r\n      <view v-else class=\"gg-item-name\">\r\n        <text v-if=\"getStatusText\">{{getStatusText}}</text>\r\n        {{formatDate}} · {{item.province || '未知地区'}}\r\n      </view>\r\n      <view  class=\"cccc\">   \r\n      <!-- 笔记内容 -->\r\n      <view :class=\"['gg-item-content', 'ohto2', item.type == 1 ? 'wlc8' : '']\">{{item.content || '无内容'}}</view>\r\n      \r\n      <!-- 图片/视频/音频内容 -->\r\n\r\n      <view v-if=\"item.type > 0\" class=\"gg-item-file\">\r\n        <!-- 单图模式 -->\r\n        <view v-if=\"item.type == 2 && getImages.length == 1\" :class=\"getSingleImageClass\">\r\n          <lazyImage :src=\"getImages[0]\"></lazyImage>\r\n        </view>\r\n        \r\n        <!-- 多图模式 -->\r\n        <block v-if=\"item.type == 2 && getImages.length > 1\">\r\n          <view v-for=\"(img, index) in getImages.slice(0, 3)\" :key=\"index\" class=\"file-img\">\r\n            <lazyImage :src=\"img\"></lazyImage>\r\n          </view>\r\n          <view v-if=\"getImagesCount > 3\" class=\"file-count df\">\r\n            <image src=\"/static/img/i.png\"></image>\r\n            <text>{{getImagesCount}}</text>\r\n          </view>\r\n        </block>\r\n        \r\n        <!-- 视频模式 -->\r\n        <block v-if=\"item.type == 3 && item.video\">\r\n          <view class=\"file-h\">\r\n            <lazyImage :src=\"item.video_cover || getImages[0] || '/static/img/video_placeholder.png'\"></lazyImage>\r\n          </view>\r\n          <view class=\"file-video df\" style=\"left:382rpx;\">\r\n            <image src=\"/static/img/v.png\"></image>\r\n          </view>\r\n        </block>\r\n        \r\n        <!-- 音频模式 -->\r\n        <view v-if=\"item.type == 4 && item.audio\" class=\"file-audio df\">\r\n          <image class=\"audio-bg\" style=\"z-index:-2\" :src=\"item.audio_cover || '/static/img/audio_cover.png'\"></image>\r\n          <view class=\"audio-mb\"></view>\r\n          <view class=\"audio-left\">\r\n            <lazyImage :src=\"item.audio_cover || '/static/img/audio_cover.png'\"></lazyImage>\r\n            <image class=\"icon\" src=\"/static/img/yw.png\"></image>\r\n          </view>\r\n          <view style=\"width:calc(100% - 330rpx)\">\r\n            <view class=\"audio-t1\">{{item.audio_name || '音频'}}</view>\r\n            <view class=\"audio-t2 ohto\">{{item.audio_intro || '点击播放音频'}}</view>\r\n          </view>\r\n          <view class=\"audio-play\">去播放</view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 投票展示区 -->\r\n      <VoteComponent\r\n        v-if=\"item.vote_info\"\r\n        :voteInfo=\"item.vote_info\"\r\n        @vote-success=\"handleVoteSuccess\"\r\n      />\r\n\r\n      <!-- 关联信息 -->\r\n      <view v-if=\"hasLocation || item.topic_info || item.product_info\" class=\"gg-item-g\">\r\n        <!-- 位置信息 -->\r\n        <view v-if=\"hasLocation\" class=\"g-item df\" @tap.stop=\"opLocationClick\">\r\n          <image class=\"g-item-icon\" style=\"width:30rpx;height:30rpx\" src=\"/static/img/wz.png\"></image>\r\n          <text style=\"padding:0 8rpx;color:#999\">{{item.location_name}}</text>\r\n        </view>\r\n        \r\n        <!-- 话题信息 -->\r\n        <template v-if=\"hasTopic\">\r\n          <view v-for=\"(topic, index) in item.topic_info\" :key=\"'topic-'+index\" class=\"g-item df\" style=\"border-radius:40rpx\" data-type=\"2\" @tap.stop=\"toPaths\">\r\n            <view class=\"g-item-img\" style=\"border-radius:50%\">\r\n              <lazyImage :src=\"topic.image || '/static/img/topic.png'\" br=\"50%\"></lazyImage>\r\n            </view>\r\n            <text style=\"padding:0 8rpx 0 12rpx\">{{topic.title}}</text>\r\n          </view>\r\n        </template>\r\n        \r\n        <!-- 商品信息 -->\r\n        <view v-if=\"hasProduct\" class=\"g-item df\" :data-id=\"item.product_info.id\" data-type=\"3\" @tap.stop=\"toPaths\">\r\n          <view class=\"g-item-img\">\r\n            <lazyImage :src=\"item.product_info.image\"></lazyImage>\r\n          </view>\r\n          <text style=\"padding:0 8rpx 0 12rpx\">{{item.product_info.store_name}}</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 互动数据 -->\r\n      <view class=\"gg-item-unm df\">\r\n        <text class=\"gg-item-unm-txt\">评论 {{item.comments || 0}}</text>\r\n        <text class=\"gg-item-unm-txt\">赞 {{item.likes || 0}}</text>\r\n        <text class=\"gg-item-unm-txt\">浏览 {{item.views || 0}}</text>\r\n        <view v-if=\"bar == 2 || item.uid == currentUid\" @tap.stop=\"delNote\" class=\"gg-item-unm-txt\" style=\"font-weight:bold;color:#FA5150\">删除</view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</view>\r\n</template>\r\n\r\n<script>\r\nimport lazyImage from '@/components/lazyImage/lazyImage'\r\nimport VoteComponent from '@/components/vote-component/vote-component.vue'\r\n\r\nexport default {\r\n  name: 'card-wd',\r\n  components: {\r\n    lazyImage,\r\n    VoteComponent\r\n  },\r\n  props: {\r\n    item: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    idx: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    bar: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      currentUid: uni.getStorageSync('uid') || 0,\r\n    }\r\n  },\r\n  computed: {\r\n    // 获取日期数字\r\n    getDay() {\r\n      if (!this.item.create_time) return 'N/A';\r\n      \r\n      try {\r\n        const date = new Date(typeof this.item.create_time === 'string' ? \r\n                             this.item.create_time.replace(/-/g, '/') : \r\n                             this.item.create_time * 1000);\r\n        return date.getDate();\r\n      } catch(e) {\r\n        return 'N/A';\r\n      }\r\n    },\r\n    \r\n    // 获取状态文本\r\n    getStatusText() {\r\n      const statusMap = {\r\n        0: '待审核',\r\n        1: '已通过',\r\n        2: '未通过',\r\n        3: '草稿'\r\n      };\r\n      return statusMap[this.item.status] || '';\r\n    },\r\n    \r\n    // 格式化日期时间\r\n    formatTime() {\r\n      if (!this.item.create_time) return '';\r\n      \r\n      try {\r\n        // 处理可能的时间格式\r\n        let timestamp;\r\n        if (typeof this.item.create_time === 'string') {\r\n          timestamp = new Date(this.item.create_time.replace(/-/g, '/')).getTime();\r\n        } else if (typeof this.item.create_time === 'number') {\r\n          timestamp = this.item.create_time * 1000; // 秒转毫秒\r\n        } else {\r\n          return '';\r\n        }\r\n        \r\n        const now = new Date().getTime();\r\n        const diff = now - timestamp;\r\n        \r\n        // 小于1分钟\r\n        if (diff < 60 * 1000) {\r\n          return '刚刚';\r\n        }\r\n        // 小于1小时\r\n        else if (diff < 60 * 60 * 1000) {\r\n          return Math.floor(diff / (60 * 1000)) + '分钟前';\r\n        }\r\n        // 小于24小时\r\n        else if (diff < 24 * 60 * 60 * 1000) {\r\n          return Math.floor(diff / (60 * 60 * 1000)) + '小时前';\r\n        }\r\n        // 小于30天\r\n        else if (diff < 30 * 24 * 60 * 60 * 1000) {\r\n          return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前';\r\n        }\r\n        // 大于30天\r\n        else {\r\n          const date = new Date(timestamp);\r\n          return (date.getMonth() + 1) + '月' + date.getDate() + '日';\r\n        }\r\n      } catch(e) {\r\n        return '';\r\n      }\r\n    },\r\n    \r\n    // 格式化年月日\r\n    formatDate() {\r\n      if (!this.item.create_time) return '';\r\n      \r\n      try {\r\n        let timestamp;\r\n        if (typeof this.item.create_time === 'string') {\r\n          timestamp = new Date(this.item.create_time.replace(/-/g, '/')).getTime();\r\n        } else if (typeof this.item.create_time === 'number') {\r\n          timestamp = this.item.create_time * 1000;\r\n        } else {\r\n          return '';\r\n        }\r\n        \r\n        const date = new Date(timestamp);\r\n        return date.getFullYear() + '年' + (date.getMonth() + 1) + '月' + date.getDate() + '日';\r\n      } catch(e) {\r\n        return '';\r\n      }\r\n    },\r\n    \r\n    // 获取图片数组\r\n    getImages() {\r\n      if (!this.item.images) return [];\r\n      \r\n      if (typeof this.item.images === 'string') {\r\n        try {\r\n          return JSON.parse(this.item.images);\r\n        } catch (e) {\r\n          return [];\r\n        }\r\n      }\r\n      \r\n      return Array.isArray(this.item.images) ? this.item.images : [];\r\n    },\r\n    \r\n    // 获取图片数量\r\n    getImagesCount() {\r\n      return this.getImages.length;\r\n    },\r\n    \r\n    // 检查是否有位置\r\n    hasLocation() {\r\n      return this.item.location_name && this.item.location_name.length > 0;\r\n    },\r\n    \r\n    // 检查是否有话题\r\n    hasTopic() {\r\n      return this.item.topic_info && Array.isArray(this.item.topic_info) && this.item.topic_info.length > 0;\r\n    },\r\n    \r\n    // 检查是否有商品\r\n    hasProduct() {\r\n      return this.item.product_info && Object.keys(this.item.product_info).length > 0;\r\n    },\r\n    \r\n    // 获取图片宽高比（用于单图显示）\r\n    getImageRatio() {\r\n      if (this.item.image_width && this.item.image_height) {\r\n        return this.item.image_width / this.item.image_height;\r\n      }\r\n      \r\n      // 默认按竖图处理\r\n      return 0.8;\r\n    },\r\n    \r\n    // 单图容器类名\r\n    getSingleImageClass() {\r\n      return ['file-h', this.getImageRatio > 1.2 ? 'file-w' : ''];\r\n    }\r\n  },\r\n  methods: {\r\n    toPaths(e) {\r\n      let url = '';\r\n      \r\n      if (this.bar == 2) {\r\n        // 草稿箱，跳转到编辑页面\r\n        url = 'note/add?id=' + this.item.id;\r\n      } else {\r\n        let type = e && e.currentTarget ? e.currentTarget.dataset.type : 0;\r\n        \r\n        if (this.item.type == 2 || this.item.type == 3 || this.item.type == 4) {\r\n  // 图片、视频和音频类型，跳转到视频详情\r\n        url = 'note/video?id=' + this.item.id;\r\n      } else {\r\n  // 文本类型，跳转到普通详情页\r\n        url = 'note/details?id=' + this.item.id;\r\n        }\r\n        \r\n        if (type == 1) {\r\n          // 用户头像点击，跳转到用户详情\r\n          url = 'user/details?id=' + this.item.uid;\r\n        } else if (type == 2) {\r\n          // 话题点击，跳转到话题详情\r\n          const topicId = e.currentTarget.dataset.id || '';\r\n          url = 'topic/details?id=' + topicId;\r\n        } else if (type == 3) {\r\n          // 商品点击，跳转到商品详情\r\n          const goodsId = e.currentTarget.dataset.id || '';\r\n          url = 'goods/details?id=' + goodsId;\r\n        }\r\n      }\r\n      \r\n      uni.navigateTo({\r\n        url: '/pages/' + url\r\n      });\r\n    },\r\n    \r\n    delNote() {\r\n      this.$emit('delback', {\r\n        idx: this.idx\r\n      });\r\n    },\r\n    \r\n    opLocationClick() {\r\n      if (!this.item.latitude || !this.item.longitude) return;\r\n\r\n      uni.openLocation({\r\n        latitude: parseFloat(this.item.latitude),\r\n        longitude: parseFloat(this.item.longitude),\r\n        name: this.item.location_name\r\n      });\r\n    },\r\n\r\n    // 处理投票成功事件\r\n    handleVoteSuccess(data) {\r\n      // 更新本地数据\r\n      this.item.vote_info = data.voteInfo;\r\n\r\n      // 通知父组件\r\n      this.$emit('update', {\r\n        vote_info: data.voteInfo,\r\n        idx: this.idx\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.gg-box {\r\n  position: relative;\r\n  border-bottom: 1px solid #f8f8f8;\r\n  width: calc(100% - 60rpx);\r\n  padding: 30rpx;\r\n  display: flex;\r\n}\r\n\r\n.gg-box .gg-avatar {\r\n  position: relative;\r\n  z-index: 1;\r\n  width: 68rpx;\r\n  height: 68rpx;\r\n  border-radius: 50%;\r\n  border: 1px solid #f8f8f8;\r\n  overflow: hidden;\r\n}\r\n\r\n.gg-box .rq {\r\n  line-height: 68rpx;\r\n  font-size: 26rpx;\r\n  font-weight: bolder;\r\n  text-align: center;\r\n  color: #fff;\r\n  background: #000;\r\n  border: 1px solid #000;\r\n}\r\n\r\n.gg-box .gg-item {\r\n  width: calc(100% - 88rpx - 2px);\r\n  margin-left: 20rpx;\r\n}\r\n.timeline-line {\r\n  position: absolute;\r\n  left: 58rpx;\r\n  top: 120rpx; \r\n  bottom: 30rpx;\r\n  width: 2rpx;\r\n  background-color: #e5e5e5; \r\n  z-index: 0;\r\n}\r\n\r\n.timeline-line::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: -4rpx;\r\n  top: -4rpx;\r\n  width: 10rpx;\r\n  height: 10rpx;\r\n  border-radius: 50%;\r\n  background-color: #e5e5e5; \r\n}\r\n\r\n.gg-box:last-child .timeline-line {\r\n  display: none;\r\n}\r\n\r\n.gg-item .gg-item-name {\r\n  color: #999;\r\n  font-size: 22rpx;\r\n  line-height: 22rpx;\r\n  font-weight: 300;\r\n  display: flex;\r\n  align-items: flex-end;\r\n}\r\n\r\n.gg-item .gg-item-name text {\r\n  margin-right: 20rpx;\r\n  color: #fa5150;\r\n  font-weight: 700;\r\n}\r\n\r\n.gg-item .gg-item-name view {\r\n  max-width: 320rpx;\r\n  margin-right: 20rpx;\r\n  color: #000;\r\n  font-size: 28rpx;\r\n  line-height: 28rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n.gg-item .gg-item-content {\r\n  margin-top: 15rpx;\r\n  font-size: 26rpx;\r\n  font-weight: 400;\r\n  line-height: 36rpx;\r\n  word-break: break-word;\r\n  white-space: pre-line;\r\n}\r\n\r\n.gg-item .gg-item-file {\r\n  margin-top: 20rpx;\r\n  display: flex;\r\n  position: relative;\r\n  z-index: 1;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.gg-item-file .file-h,\r\n.gg-item-file .file-w,\r\n.gg-item-file .file-img {\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.gg-item-file .file-h {\r\n  width: 320rpx;\r\n  height: 420rpx;\r\n}\r\n\r\n.gg-item-file .file-w {\r\n  width: 420rpx;\r\n  height: 320rpx;\r\n}\r\n\r\n.gg-item-file .file-img {\r\n  width: 196rpx;\r\n  height: 196rpx;\r\n  margin-right: 4rpx;\r\n  margin-bottom: 4rpx;\r\n}\r\n\r\n.gg-item-file .file-img:nth-child(3n) {\r\n  margin-right: 0rpx !important;\r\n}\r\n\r\n.gg-item-file .file-count {\r\n  position: absolute;\r\n  right: 20rpx;\r\n  bottom: 30rpx;\r\n  padding: 0 10rpx;\r\n  height: 40rpx;\r\n  color: #fff;\r\n  font-size: 20rpx;\r\n  font-weight: 700;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  -webkit-backdrop-filter: blur(15px);\r\n  backdrop-filter: blur(15px);\r\n  border-radius: 8rpx;\r\n}\r\n\r\n.gg-item-file .file-count image,\r\n.gg-item-file .file-video image {\r\n  width: 20rpx;\r\n  height: 20rpx;\r\n}\r\n\r\n.gg-item-file .file-count image {\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.gg-item-file .file-video {\r\n  position: absolute;\r\n  top: 20rpx;\r\n  width: 48rpx;\r\n  height: 48rpx;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  justify-content: center;\r\n  border-radius: 50%;\r\n}\r\n\r\n.gg-item-file .file-audio {\r\n  width: 100%;\r\n  height: 140rpx;\r\n  border-radius: 8rpx;\r\n  color: #fff;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-audio .audio-left {\r\n  margin-right: 30rpx;\r\n  width: 140rpx;\r\n  height: 140rpx;\r\n  position: relative;\r\n}\r\n\r\n.file-audio .audio-left .icon {\r\n  position: absolute;\r\n  top: 45rpx;\r\n  right: 45rpx;\r\n  bottom: 45rpx;\r\n  left: 45rpx;\r\n  width: 50rpx;\r\n  height: 50rpx;\r\n}\r\n\r\n.file-audio .audio-bg,\r\n.file-audio .audio-mb {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.file-audio .audio-mb {\r\n  z-index: -1;\r\n  -webkit-backdrop-filter: saturate(150%) blur(25px);\r\n  backdrop-filter: saturate(150%) blur(25px);\r\n  background: rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.file-audio .audio-t1 {\r\n  font-size: 26rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n.file-audio .audio-t2 {\r\n  margin-top: 10rpx;\r\n  opacity: 0.8;\r\n  font-size: 20rpx;\r\n}\r\n\r\n.file-audio .audio-play {\r\n  margin: 0 30rpx;\r\n  width: 100rpx;\r\n  height: 60rpx;\r\n  line-height: 60rpx;\r\n  text-align: center;\r\n  font-size: 18rpx;\r\n  font-weight: 700;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  border-radius: 60rpx;\r\n}\r\n\r\n.gg-item .gg-item-g {\r\n  margin-top: 10rpx;\r\n  width: 100%;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.gg-item-g .g-item {\r\n  margin: 10rpx 10rpx 0 0;\r\n  padding: 8rpx;\r\n  font-size: 20rpx;\r\n  font-weight: 500;\r\n  border-radius: 8rpx;\r\n  background: #f8f8f8;\r\n}\r\n\r\n.g-item .g-item-icon {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n}\r\n\r\n.g-item .g-item-img {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  background: #f8f8f8;\r\n  border-radius: 4rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.gg-item-unm .gg-item-unm-txt {\r\n  margin: 20rpx 20rpx 0 0;\r\n  color: #999;\r\n  font-size: 20rpx;\r\n}\r\n\r\n.wlc8 {\r\n  -webkit-line-clamp: 8 !important;\r\n}\r\n\r\n.df {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ohto {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.ohto2 {\r\n  word-break: break-all;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 4;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n</style> ", "import Component from 'D:/uniapp/vue3/components/card-wd/card-wd.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;;AAoHA,kBAAkB,MAAW;AAC7B,sBAAsB,MAAW;AAEjC,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,IACD,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,KAAK;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,YAAYA,cAAG,MAAC,eAAe,KAAK,KAAK;AAAA,IAC3C;AAAA,EACD;AAAA,EACD,UAAU;AAAA;AAAA,IAER,SAAS;AACP,UAAI,CAAC,KAAK,KAAK;AAAa,eAAO;AAEnC,UAAI;AACF,cAAM,OAAO,IAAI,KAAK,OAAO,KAAK,KAAK,gBAAgB,WAClC,KAAK,KAAK,YAAY,QAAQ,MAAM,GAAG,IACvC,KAAK,KAAK,cAAc,GAAI;AACjD,eAAO,KAAK;MACd,SAAQ,GAAG;AACT,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACd,YAAM,YAAY;AAAA,QAChB,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA;AAEL,aAAO,UAAU,KAAK,KAAK,MAAM,KAAK;AAAA,IACvC;AAAA;AAAA,IAGD,aAAa;AACX,UAAI,CAAC,KAAK,KAAK;AAAa,eAAO;AAEnC,UAAI;AAEF,YAAI;AACJ,YAAI,OAAO,KAAK,KAAK,gBAAgB,UAAU;AAC7C,sBAAY,IAAI,KAAK,KAAK,KAAK,YAAY,QAAQ,MAAM,GAAG,CAAC,EAAE,QAAO;AAAA,QACtE,WAAS,OAAO,KAAK,KAAK,gBAAgB,UAAU;AACpD,sBAAY,KAAK,KAAK,cAAc;AAAA,eAC/B;AACL,iBAAO;AAAA,QACT;AAEA,cAAM,OAAM,oBAAI,KAAM,GAAC,QAAO;AAC9B,cAAM,OAAO,MAAM;AAGnB,YAAI,OAAO,KAAK,KAAM;AACpB,iBAAO;AAAA,QACT,WAES,OAAO,KAAK,KAAK,KAAM;AAC9B,iBAAO,KAAK,MAAM,QAAQ,KAAK,IAAK,IAAI;AAAA,QAC1C,WAES,OAAO,KAAK,KAAK,KAAK,KAAM;AACnC,iBAAO,KAAK,MAAM,QAAQ,KAAK,KAAK,IAAK,IAAI;AAAA,QAC/C,WAES,OAAO,KAAK,KAAK,KAAK,KAAK,KAAM;AACxC,iBAAO,KAAK,MAAM,QAAQ,KAAK,KAAK,KAAK,IAAK,IAAI;AAAA,QACpD,OAEK;AACH,gBAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,iBAAQ,KAAK,aAAa,IAAK,MAAM,KAAK,QAAU,IAAE;AAAA,QACxD;AAAA,MACF,SAAQ,GAAG;AACT,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,aAAa;AACX,UAAI,CAAC,KAAK,KAAK;AAAa,eAAO;AAEnC,UAAI;AACF,YAAI;AACJ,YAAI,OAAO,KAAK,KAAK,gBAAgB,UAAU;AAC7C,sBAAY,IAAI,KAAK,KAAK,KAAK,YAAY,QAAQ,MAAM,GAAG,CAAC,EAAE,QAAO;AAAA,QACtE,WAAS,OAAO,KAAK,KAAK,gBAAgB,UAAU;AACpD,sBAAY,KAAK,KAAK,cAAc;AAAA,eAC/B;AACL,iBAAO;AAAA,QACT;AAEA,cAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,eAAO,KAAK,YAAW,IAAK,OAAO,KAAK,SAAQ,IAAK,KAAK,MAAM,KAAK,QAAO,IAAK;AAAA,MACnF,SAAQ,GAAG;AACT,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACV,UAAI,CAAC,KAAK,KAAK;AAAQ,eAAO,CAAA;AAE9B,UAAI,OAAO,KAAK,KAAK,WAAW,UAAU;AACxC,YAAI;AACF,iBAAO,KAAK,MAAM,KAAK,KAAK,MAAM;AAAA,QACpC,SAAS,GAAG;AACV,iBAAO;QACT;AAAA,MACF;AAEA,aAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,SAAS;IAC7D;AAAA;AAAA,IAGD,iBAAiB;AACf,aAAO,KAAK,UAAU;AAAA,IACvB;AAAA;AAAA,IAGD,cAAc;AACZ,aAAO,KAAK,KAAK,iBAAiB,KAAK,KAAK,cAAc,SAAS;AAAA,IACpE;AAAA;AAAA,IAGD,WAAW;AACT,aAAO,KAAK,KAAK,cAAc,MAAM,QAAQ,KAAK,KAAK,UAAU,KAAK,KAAK,KAAK,WAAW,SAAS;AAAA,IACrG;AAAA;AAAA,IAGD,aAAa;AACX,aAAO,KAAK,KAAK,gBAAgB,OAAO,KAAK,KAAK,KAAK,YAAY,EAAE,SAAS;AAAA,IAC/E;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,KAAK,KAAK,eAAe,KAAK,KAAK,cAAc;AACnD,eAAO,KAAK,KAAK,cAAc,KAAK,KAAK;AAAA,MAC3C;AAGA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,sBAAsB;AACpB,aAAO,CAAC,UAAU,KAAK,gBAAgB,MAAM,WAAW,EAAE;AAAA,IAC5D;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,QAAQ,GAAG;AACT,UAAI,MAAM;AAEV,UAAI,KAAK,OAAO,GAAG;AAEjB,cAAM,iBAAiB,KAAK,KAAK;AAAA,aAC5B;AACL,YAAI,OAAO,KAAK,EAAE,gBAAgB,EAAE,cAAc,QAAQ,OAAO;AAEjE,YAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ,GAAG;AAEvE,gBAAM,mBAAmB,KAAK,KAAK;AAAA,eAC9B;AAEL,gBAAM,qBAAqB,KAAK,KAAK;AAAA,QACrC;AAEA,YAAI,QAAQ,GAAG;AAEb,gBAAM,qBAAqB,KAAK,KAAK;AAAA,QACvC,WAAW,QAAQ,GAAG;AAEpB,gBAAM,UAAU,EAAE,cAAc,QAAQ,MAAM;AAC9C,gBAAM,sBAAsB;AAAA,QAC9B,WAAW,QAAQ,GAAG;AAEpB,gBAAM,UAAU,EAAE,cAAc,QAAQ,MAAM;AAC9C,gBAAM,sBAAsB;AAAA,QAC9B;AAAA,MACF;AAEAA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,YAAY;AAAA,MACnB,CAAC;AAAA,IACF;AAAA,IAED,UAAU;AACR,WAAK,MAAM,WAAW;AAAA,QACpB,KAAK,KAAK;AAAA,MACZ,CAAC;AAAA,IACF;AAAA,IAED,kBAAkB;AAChB,UAAI,CAAC,KAAK,KAAK,YAAY,CAAC,KAAK,KAAK;AAAW;AAEjDA,oBAAAA,MAAI,aAAa;AAAA,QACf,UAAU,WAAW,KAAK,KAAK,QAAQ;AAAA,QACvC,WAAW,WAAW,KAAK,KAAK,SAAS;AAAA,QACzC,MAAM,KAAK,KAAK;AAAA,MAClB,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,MAAM;AAEtB,WAAK,KAAK,YAAY,KAAK;AAG3B,WAAK,MAAM,UAAU;AAAA,QACnB,WAAW,KAAK;AAAA,QAChB,KAAK,KAAK;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7VA,GAAG,gBAAgB,SAAS;"}