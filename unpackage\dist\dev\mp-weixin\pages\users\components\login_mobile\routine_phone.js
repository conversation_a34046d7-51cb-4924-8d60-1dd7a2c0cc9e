"use strict";
const common_vendor = require("../../../../common/vendor.js");
const libs_routine = require("../../../../libs/routine.js");
const api_user = require("../../../../api/user.js");
const api_social = require("../../../../api/social.js");
const api_public = require("../../../../api/public.js");
const app = getApp();
const _sfc_main = {
  name: "routine_phone",
  props: {
    isPhoneBox: {
      type: Boolean,
      default: false
    },
    logoUrl: {
      type: String,
      default: ""
    },
    authKey: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      keyCode: "",
      account: "",
      codeNum: "",
      isStatus: false
    };
  },
  mounted() {
  },
  methods: {
    // 小程序获取手机号码
    getphonenumber(e) {
      common_vendor.index.showLoading({
        title: this.$t(`正在登录中`)
      });
      libs_routine.Routine.getCode().then((code) => {
        this.getUserPhoneNumber(e.detail.encryptedData, e.detail.iv, code);
      }).catch((error) => {
        common_vendor.index.hideLoading();
      });
    },
    // 小程序获取手机号码回调
    getUserPhoneNumber(encryptedData, iv, code) {
      api_public.routineBindingPhone({
        encryptedData,
        iv,
        code,
        spread_spid: app.globalData.spid,
        spread_code: app.globalData.code,
        key: this.authKey
      }).then((res) => {
        let time = res.data.expires_time - this.$Cache.time();
        this.$store.commit("LOGIN", {
          token: res.data.token,
          time
        });
        this.$emit("loginSuccess", {
          isStatus: true,
          new_user: res.data.userInfo.new_user
        });
      }).catch((res) => {
        common_vendor.index.hideLoading();
      });
    },
    /**
     * 获取个人用户信息
     */
    getUserInfo: function() {
      let that = this;
      api_user.getUserInfo().then((res) => {
        that.userInfo = res.data;
        that.$store.commit("SETUID", res.data.uid);
        api_social.getUserSocialInfo().then((socialRes) => {
          common_vendor.index.hideLoading();
          if (socialRes.data) {
            that.$store.commit("UPDATE_USERINFO", socialRes.data);
          }
          that.isStatus = true;
          this.close(res.data.new_user || 0);
        }).catch(() => {
          common_vendor.index.hideLoading();
          that.isStatus = true;
          this.close(res.data.new_user || 0);
        });
      });
    },
    close(new_user) {
      this.$emit("close", {
        isStatus: this.isStatus,
        new_user
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.isPhoneBox
  }, $props.isPhoneBox ? {
    b: $props.logoUrl,
    c: common_vendor.t(_ctx.$t(`获取授权`)),
    d: common_vendor.t(_ctx.$t(`获取手机号授权`)),
    e: common_vendor.t(_ctx.$t(`获取手机号`)),
    f: common_vendor.o((...args) => $options.getphonenumber && $options.getphonenumber(...args)),
    g: _ctx.isUp ? 1 : ""
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pages/users/components/login_mobile/routine_phone.js.map
