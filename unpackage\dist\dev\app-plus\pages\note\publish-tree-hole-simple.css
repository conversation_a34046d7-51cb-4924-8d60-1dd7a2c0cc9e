
.container[data-v-22b8a2a2] {
  min-height: 100vh;
  background-color: #f8f8f8;
}
.nav-bar[data-v-22b8a2a2] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 2.75rem;
  padding: 0 0.9375rem;
  background-color: #fff;
  border-bottom: 0.03125rem solid #f0f0f0;
}
.nav-back[data-v-22b8a2a2] {
  width: 1.875rem;
  height: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.125rem;
}
.nav-title[data-v-22b8a2a2] {
  font-size: 1rem;
  font-weight: bold;
}
.nav-submit[data-v-22b8a2a2] {
  padding: 0.375rem 0.75rem;
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  border-radius: 0.625rem;
  color: white;
  font-size: 0.875rem;
}
.form-container[data-v-22b8a2a2] {
  padding: 0.9375rem;
}
.form-item[data-v-22b8a2a2] {
  background: #fff;
  border-radius: 0.5rem;
  padding: 0.9375rem;
  margin-bottom: 0.9375rem;
}
.form-label[data-v-22b8a2a2] {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 0.625rem;
}
.type-list[data-v-22b8a2a2] {
  display: flex;
  gap: 0.625rem;
  flex-wrap: wrap;
}
.type-item[data-v-22b8a2a2] {
  flex: 1;
  min-width: 4.375rem;
  padding: 0.625rem;
  background: #f8f9fa;
  border-radius: 0.375rem;
  text-align: center;
  border: 0.0625rem solid transparent;
}
.type-item.active[data-v-22b8a2a2] {
  background: #e6f0ff;
  border-color: #3B82F6;
}
.type-icon[data-v-22b8a2a2] {
  display: block;
  font-size: 1rem;
  margin-bottom: 0.25rem;
}
.type-name[data-v-22b8a2a2] {
  font-size: 0.75rem;
  color: #666;
}
.form-textarea[data-v-22b8a2a2] {
  width: 100%;
  min-height: 6.25rem;
  padding: 0.625rem;
  background: #f8f9fa;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.6;
}
.form-count[data-v-22b8a2a2] {
  text-align: right;
  font-size: 0.75rem;
  color: #999;
  margin-top: 0.3125rem;
}
.voice-section[data-v-22b8a2a2] {
  padding: 1.875rem;
  text-align: center;
  color: #999;
}
.setting-row[data-v-22b8a2a2] {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.setting-text[data-v-22b8a2a2] {
  font-size: 0.875rem;
}
.switch[data-v-22b8a2a2] {
  width: 2.5rem;
  height: 1.25rem;
  background: #e9ecef;
  border-radius: 0.625rem;
  position: relative;
  transition: all 0.3s ease;
}
.switch.active[data-v-22b8a2a2] {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
}
.switch-dot[data-v-22b8a2a2] {
  width: 1rem;
  height: 1rem;
  background: #fff;
  border-radius: 50%;
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  transition: all 0.3s ease;
}
.switch.active .switch-dot[data-v-22b8a2a2] {
  left: 1.375rem;
}

/* 调试信息样式 */
.debug-info[data-v-22b8a2a2] {
  background: #fff3cd;
  border: 0.0625rem solid #ffeaa7;
  border-radius: 0.375rem;
  padding: 0.625rem;
  margin: 0.625rem;
}
.debug-title[data-v-22b8a2a2] {
  font-size: 0.875rem;
  font-weight: bold;
  color: #856404;
  margin-bottom: 0.5rem;
  text-align: center;
}
.debug-item[data-v-22b8a2a2] {
  font-size: 0.75rem;
  color: #856404;
  margin-bottom: 0.25rem;
  padding: 0.25rem;
  background: rgba(255, 234, 167, 0.3);
  border-radius: 0.1875rem;
  word-break: break-all;
}
