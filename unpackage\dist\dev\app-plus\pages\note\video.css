
.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.uni-load-more[data-v-9245e42c] {
  display: flex;
  flex-direction: row;
  height: 1.875rem;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-9245e42c] {
  font-size: 0.75rem;
  margin-left: 0.5rem;
}
.uni-load-more__img[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
}
.uni-load-more__img--nvue[data-v-9245e42c] {
  color: #999;
}
.uni-load-more__img--android[data-v-9245e42c],
.uni-load-more__img--ios[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
}
.uni-load-more__img--android[data-v-9245e42c] {
  animation: loading-ios 1s 0s linear infinite;
}
.uni-load-more__img--ios-H5[data-v-9245e42c] {
  position: relative;
  animation: loading-ios-H5-9245e42c 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-9245e42c] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-9245e42c {
0% {
    transform: rotate(0);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
to {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-9245e42c] {
  position: relative;
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 0.125rem transparent;
  border-top: solid 0.125rem #999;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(1) {
  animation: loading-android-MP-1-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(2) {
  animation: loading-android-MP-2-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(3) {
  animation: loading-android-MP-3-9245e42c 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(90deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(180deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(270deg);
}
to {
    transform: rotate(360deg);
}
}


.lazy-image[data-v-337fa078]{position:relative;width:100%;height:100%;overflow:hidden;background:#f8f8f8;}
.lazy-image uni-image[data-v-337fa078]{width:100%;height:100%;}
.lazy-image .err[data-v-337fa078]{position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(248,248,248,0.7) url('../../static/img/load.png') no-repeat center/50%;}


.play[data-v-0efbaf85] {
  height: 100%;
  justify-content: space-between;
}
.play .play-chunk1[data-v-0efbaf85],
.play .play-chunk2[data-v-0efbaf85],
.play .play-chunk3[data-v-0efbaf85] {
  width: calc(33.33% - 0.125rem);
  border-radius: 50px;
  animation: wave-0efbaf85 1s linear infinite;
}
.play .play-chunk1[data-v-0efbaf85] {
  animation-delay: 0s;
}
.play .play-chunk2[data-v-0efbaf85] {
  animation-delay: 0.33s;
}
.play .play-chunk3[data-v-0efbaf85] {
  animation-delay: 0.66s;
}
@keyframes wave-0efbaf85 {
0% {
    height: 20%;
}
50% {
    height: 100%;
}
to {
    height: 20%;
}
}
.df[data-v-0efbaf85] {
  display: flex;
  align-items: center;
}


.share-container[data-v-fd563daa] {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
}
.share-mask[data-v-fd563daa] {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
}
.share-content[data-v-fd563daa] {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: #ffffff;
  border-top-left-radius: 0.625rem;
  border-top-right-radius: 0.625rem;
  box-shadow: 0 -0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.share-header[data-v-fd563daa] {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 2.8125rem;
  border-bottom: 0.03125rem solid #f2f2f2;
}
.share-title[data-v-fd563daa] {
  font-size: 1rem;
  color: #333;
  font-weight: 500;
}
.popup-close[data-v-fd563daa] {
  position: absolute;
  right: 0.9375rem;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: #f8f8f8;
  justify-content: center;
}

/* 分割线样式 */
.divider-line[data-v-fd563daa] {
  height: 1px;
  background-color: #eeeeee;
  width: 100%;
}

/* 人员列表样式 */
.person-scroll[data-v-fd563daa] {
  width: 100%;
  white-space: nowrap;
  padding: 0.9375rem 0;
  background-color: #ffffff;
}
.person-list[data-v-fd563daa] {
  width: 100%;
  display: flex;
  padding: 0 0.3125rem;
}
.person-item[data-v-fd563daa] {
  flex-shrink: 0;
}
.person-avatar[data-v-fd563daa] {
  margin: 0 0.625rem;
  width: 3.625rem;
  height: 3.625rem;
  border-radius: 50%;
  background: #f8f8f8;
  border: 0.0625rem solid #f5f5f5;
  position: relative;
}
.person-avatar uni-image[data-v-fd563daa] {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.person-online-dot[data-v-fd563daa] {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  border: 0.1875rem solid #fff;
  z-index: 10;
}
.person-name[data-v-fd563daa] {
  margin: 0.625rem 0 0.3125rem;
  width: 5rem;
  color: #000;
  font-weight: 500;
  font-size: 0.75rem;
  line-height: 0.75rem;
  text-align: center;
}
.person-tips[data-v-fd563daa] {
  width: 5rem;
  color: #999;
  font-size: 0.5625rem;
  line-height: 0.5625rem;
  font-weight: 300;
  text-align: center;
}
.ohto[data-v-fd563daa] {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 分享选项滚动样式 */
.share-scroll[data-v-fd563daa] {
  width: 100%;
  white-space: nowrap;
  padding: 0.9375rem 0;
  background-color: #ffffff;
}
.share-list[data-v-fd563daa] {
  display: flex;
  padding: 0 0.3125rem;
}
.share-item[data-v-fd563daa] {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 0.625rem;
  background: none;
  border: none;
  padding: 0;
  font-size: inherit;
  color: inherit;
}
.share-button[data-v-fd563daa] {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 0.625rem !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}
.share-button[data-v-fd563daa]::after {
  border: none !important;
}
.share-icon-bg[data-v-fd563daa] {
  width: 3.125rem;
  height: 3.125rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.3125rem;
}
.bottom-icon[data-v-fd563daa] {
  background-color: #f8f8f8 !important;
}
.delete-item[data-v-fd563daa] {
  background-color: #fff2f2 !important;
}
.share-icon[data-v-fd563daa] {
  width: 1.5625rem;
  height: 1.5625rem;
}
.share-text[data-v-fd563daa] {
  font-size: 0.75rem;
  color: #333;
  text-align: center;
  width: 3.75rem;
  margin-top: 0.25rem;
}
.delete-text[data-v-fd563daa] {
  color: #ff4757 !important;
}
.safe-bottom[data-v-fd563daa] {
  height: 1.0625rem;
  height: calc(1.0625rem + env(safe-area-inset-bottom));
}

/* 海报相关样式 */
.poster-popup[data-v-fd563daa] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.8);
}
.poster-popup .close[data-v-fd563daa] {
  width: 1.875rem;
  height: 1.875rem;
  margin-bottom: 0.625rem;
}
.poster-img[data-v-fd563daa] {
  max-width: 80%;
  max-height: 70%;
  border-radius: 0.3125rem;
}
.save-poster[data-v-fd563daa] {
  margin-top: 1.25rem;
  padding: 0.625rem 1.25rem;
  background-color: #007aff;
  color: white;
  border-radius: 1.5625rem;
  font-size: 0.875rem;
}
.keep[data-v-fd563daa] {
  margin-top: 1.25rem;
  color: white;
  font-size: 0.875rem;
  text-align: center;
}
.canvas[data-v-fd563daa] {
  position: fixed;
  top: -312.46875rem;
  left: -312.46875rem;
  width: 23.4375rem;
  height: 41.6875rem;
}
.mask[data-v-fd563daa] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 299;
  background-color: rgba(0, 0, 0, 0.6);
}

/* H5分享图片 */
.share-box[data-v-fd563daa] {
  z-index: 1000;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.share-box uni-image[data-v-fd563daa] {
  width: 100%;
  height: 100%;
}

.emoji-panel[data-v-71424074] {
  width: 100%;
  height: 12.5rem;
  background-color: #fff;
  border-top: 0.03125rem solid #f1f1f1;
  z-index: 999;
  position: relative;
}
.emoji-scroll[data-v-71424074] {
  height: 100%;
}
.emoji-container[data-v-71424074] {
  padding: 0.625rem;
}
.recent-section[data-v-71424074] {
  margin-bottom: 0.625rem;
}
.section-title[data-v-71424074] {
  font-size: 0.75rem;
  color: #999;
  margin-bottom: 0.46875rem;
  padding-left: 0.3125rem;
}
.recent-emojis[data-v-71424074] {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3125rem;
}
.section-divider[data-v-71424074] {
  height: 0.03125rem;
  background-color: #f1f1f1;
  margin: 0.625rem 0;
}
.all-emojis[data-v-71424074] {
  display: flex;
  flex-wrap: wrap;
  gap: 0.15625rem;
}
.emoji-item[data-v-71424074] {
  width: 2.1875rem;
  height: 2.1875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}
.emoji-item[data-v-71424074]:active {
  background-color: #f5f5f5;
}
.emoji-image[data-v-71424074] {
  width: 1.5625rem;
  height: 1.5625rem;
  object-fit: contain;
}

/* 预览弹窗样式 */
.emoji-preview[data-v-71424074] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn-71424074 0.3s ease;
}
.preview-content[data-v-71424074] {
  background-color: #fff;
  border-radius: 0.625rem;
  padding: 1.875rem 1.25rem 1.25rem;
  margin: 1.25rem;
  max-width: 15.625rem;
  text-align: center;
  animation: slideUp-71424074 0.3s ease;
}
.preview-image[data-v-71424074] {
  width: 3.75rem;
  height: 3.75rem;
  margin-bottom: 0.625rem;
}
.preview-name[data-v-71424074] {
  font-size: 1rem;
  color: #333;
  margin-bottom: 1.25rem;
}
.preview-actions[data-v-71424074] {
  display: flex;
  gap: 0.625rem;
  justify-content: center;
}

/* 小程序兼容：将SCSS嵌套语法改为普通CSS */
.preview-btn[data-v-71424074] {
  padding: 0.625rem 1.25rem;
  border-radius: 1.5625rem;
  font-size: 0.875rem;
  text-align: center;
  transition: all 0.2s ease;
}
.preview-btn[data-v-71424074]:not(.secondary) {
  background-color: #ff4d6a;
  color: #fff;
}
.preview-btn[data-v-71424074]:not(.secondary):active {
  background-color: #e6445e;
}
.preview-btn.secondary[data-v-71424074] {
  background-color: #f5f5f5;
  color: #666;
}
.preview-btn.secondary[data-v-71424074]:active {
  background-color: #e8e8e8;
}

/* 动画 */
@keyframes fadeIn-71424074 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes slideUp-71424074 {
from {
    opacity: 0;
    transform: translateY(1.5625rem);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
@media (prefers-color-scheme: dark) {
.emoji-panel[data-v-71424074] {
    background-color: #1a1a1a;
    border-top-color: #333;
}
.section-title[data-v-71424074] {
    color: #666;
}
.section-divider[data-v-71424074] {
    background-color: #333;
}
.emoji-item[data-v-71424074]:active {
    background-color: #333;
}
.preview-content[data-v-71424074] {
    background-color: #333;
}
.preview-name[data-v-71424074] {
    color: #fff;
}
}
.comment-input-wrapper[data-v-88f35a0c] {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 999;
  box-shadow: 0 -0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.comment-input-box[data-v-88f35a0c] {
  padding: 0.625rem;
  background: #f5f5f5;
  position: relative;
}

/* 编辑器样式 */
.comment-editor[data-v-88f35a0c] {
  width: 100%;
  min-height: 1.875rem;
  /* 减小最小高度 */
  max-height: 7.5rem;
  /* 减小最大高度 */
  font-size: 0.875rem;
  line-height: 1.125rem;
  padding: 0.3125rem 0.625rem;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 0.25rem;
}

/* 为编辑器中的表情图片添加样式 */
.emoji-img[data-v-88f35a0c] {
  vertical-align: middle;
  margin: 0 1px;
  display: inline-block;
  transform: translateY(-1px);
  line-height: 1;
  width: 18px !important;
  height: 18px !important;
}
.comment-tools-bar[data-v-88f35a0c] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.3125rem 0.625rem;
  border-top: 0.03125rem solid #f1f1f1;
}
.tools-left[data-v-88f35a0c] {
  display: flex;
  align-items: center;
}
.tool-item[data-v-88f35a0c] {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.3125rem;
}
.tool-item uni-image[data-v-88f35a0c] {
  width: 1.375rem;
  height: 1.375rem;
}
.send-btn[data-v-88f35a0c] {
  width: 3.75rem;
  height: 2rem;
  line-height: 2rem;
  text-align: center;
  background-color: #f5f5f5;
  color: #ccc;
  font-size: 0.875rem;
  border-radius: 1rem;
  cursor: pointer;
  position: relative;
  z-index: 10;
}
.send-btn.active[data-v-88f35a0c] {
  background-color: #ff4d6a;
  color: #fff;
  cursor: pointer;
}
.selected-image-preview[data-v-88f35a0c] {
  padding: 0.625rem;
  background: #f5f5f5;
  position: relative;
}
.selected-image[data-v-88f35a0c] {
  width: 5rem;
  height: 5rem;
  border-radius: 0.25rem;
  object-fit: cover;
}
.delete-image[data-v-88f35a0c] {
  position: absolute;
  top: 0.3125rem;
  right: 0.3125rem;
  width: 1.25rem;
  height: 1.25rem;
  line-height: 1.125rem;
  text-align: center;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  font-size: 1rem;
}
.at-user-popup[data-v-88f35a0c] {
  background-color: #fff;
  border-radius: 0.625rem 0.625rem 0 0;
  padding-bottom: env(safe-area-inset-bottom);
}
.at-user-header[data-v-88f35a0c] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem;
  border-bottom: 0.03125rem solid #f1f1f1;
}
.close-btn[data-v-88f35a0c] {
  color: #999;
  font-size: 0.875rem;
}
.at-user-list[data-v-88f35a0c] {
  max-height: 18.75rem;
}
.at-user-item[data-v-88f35a0c] {
  display: flex;
  align-items: center;
  padding: 0.625rem;
  border-bottom: 0.03125rem solid #f5f5f5;
}
.at-user-avatar[data-v-88f35a0c] {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  margin-right: 0.625rem;
}
.at-user-name[data-v-88f35a0c] {
  font-size: 0.875rem;
}

body{
  background:#000;
}
.nav-box{
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  box-sizing: border-box;
}
.nav-item .nav-back{
  padding: 0 0.9375rem;
  width: 1.0625rem;
  height: 100%;
  border-radius: 50%;
}
.video-box{
  width: 100%;
  transition: height .45s ease-in-out;
}
.video-box uni-video{
  width: 100%;
  height: 100%;
}
.content-box{
  position: fixed;
  z-index: 99;
  left: 0;
  right: 0;
  width: calc(100% - 1.875rem);
  margin: 0.625rem 0.9375rem;
  color: #fff;
}
.content-box .nav-user{
  width: 100%;
  justify-content: space-between;
}
.content-box .user-info{
  display: flex;
  align-items: center;
}
.content-box .nav-user .nav-user-avatar{
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  border: 0.0625rem solid rgba(255,255,255,.8);
}
.content-box .nav-user .nav-user-name{
  margin-left: 0.625rem;
  font-size: 0.8125rem;
  font-weight: 700;
  opacity: .8;
}
/* 关注按钮样式 */
.content-box .nav-user .follow-btn{
  padding: 0 0.625rem;
  height: 1.5rem;
  line-height: 1.5rem;
  font-size: 0.625rem;
  font-weight: 700;
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.25rem;
  text-align: center;
}
.content-box .nav-user .follow-btn.active{
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
}
.content-box .content-item{
  width: 100%;
  margin: 0.625rem 0;
  word-break: break-word;
}
.content-box .content-item uni-text{
  font-size: 0.875rem;
  font-weight: 400;
}
.content-box .content-tag{
  margin-bottom: 0.3125rem;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.content-tag .tag-item{
  margin: 0 0.3125rem 0.3125rem 0;
  padding: 0.25rem;
  height: 1.25rem;
  border-radius: 0.25rem;
  background: rgba(255, 255, 255, 0.1);
}
.content-tag .tag-item .icon{
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 0.125rem;
}
.content-tag .tag-item uni-text{
  font-size: 0.625rem;
  padding: 0 0.25rem 0 0.375rem;
}
.content-box .content-tips{
  margin-top: 0.25rem;
  opacity: .6;
  font-size: 0.625rem;
}
.comment-box{
  width: calc(100% - 1.875rem);
  padding: 0.9375rem;
  background: #fff;
  border-radius: 0.9375rem 0.9375rem 0 0;
}
.comment-box .comment-top{
  width: 100%;
  height: 1.75rem;
  justify-content: space-between;
}
.comment-top .top-title{
  font-size: 0.8125rem;
  font-weight: 700;
}
.comment-top .top-btn{
  width: 4.25rem;
  padding: 0.1875rem;
  background: #f8f8f8;
  border-radius: 0.25rem;
  position: relative;
}
.comment-top .top-btn .btn-item, .comment-top .top-btn .btn-active{
  width: 2.125rem;
  height: 1.375rem;
}
.comment-top .top-btn .btn-item{
  z-index: 2;
  line-height: 1.375rem;
  text-align: center;
  font-size: 0.625rem;
  font-weight: 500;
  transition: color .3s;
}
.comment-top .top-btn .btn-active{
  position: absolute;
  z-index: 1;
  background: #fff;
  border-radius: 0.125rem;
  transition: left .3s;
}
.comment-top .top-close{
  width: 1.5rem;
  height: 1.5rem;
  background: #f5f5f5;
  border-radius: 50%;
  transform: rotate(45deg);
  justify-content: center;
}
.comment-scroll{
  width: 100%;
  height: calc(70vh - 6.125rem);
}
.comment-box .comment-item{
  width: 100%;
  margin-top: 0.9375rem;
  justify-content: space-between;
  align-items: flex-start!important;
}
.comment-item .comment-avatar, .comment-item .comment-avatar-z{
  background: #f8f8f8;
  border: 1px solid #f5f5f5;
  border-radius: 50%;
  overflow: hidden;
}
.comment-item .comment-avatar{
  width: 2rem;
  height: 2rem;
}
.comment-item .comment-avatar-z{
  width: 1.375rem;
  height: 1.375rem;
}
.comment-item .comment-info{
  width: calc(100% - 2.75rem);
}
.unfold{
  padding: 0.625rem 2.125rem;
  color: #999;
  font-size: 0.625rem;
  font-weight: 700;
}
.comment-info .comment-info-top{
  font-size: 0.75rem;
  color: #999;
}
.comment-info .comment-info-top-z uni-view{
  max-width: 7.1875rem;
  font-size: 0.6875rem;
  color: #999;
}
.comment-info .comment-info-top-z uni-text{
  margin-right: 0.25rem;
  color: #333;
  font-size: 0.6875rem;
  font-weight: 500;
}
.comment-info .nn, .comment-info .zz, .comment-info .wo{
  margin-right: 0.25rem;
}
.comment-info .zz{
  color: #FA5150!important;
  font-weight: 700;
}
.comment-info .wo{
  color: #000!important;
  font-weight: 700;
}
.comment-info .db{
  color: #ccc!important;
}
.comment-info .comment-info-content{
  word-break: break-word;
  white-space: pre-line;
}
.comment-info .comment-info-content uni-text{
  color: #333;
  font-size: 0.8125rem;
  font-weight: 400;
}
.comment-info .comment-info-bottom{
  margin-top: 0.46875rem;
  color: #999;
  font-size: 0.625rem;
}
.comment-info .comment-info-bottom uni-view{
  margin-left: 0.9375rem;
  font-weight: 700;
}
.comment-box .no-more{
  width: 100%;
  height: 2.8125rem;
  line-height: 2.8125rem;
  text-align: center;
  color: #999;
  font-size: 0.625rem;
}
.comment-box .comment-btn{
  width: 100%;
  height: 2.5rem;
  background: #f8f8f8;
  border-radius: 1.25rem;
}
.comment-box .comment-btn uni-image{
  margin: 0 0.625rem 0 0.3125rem;
  width: 1.875rem;
  height: 1.875rem;
  border-radius: 50%;
}
.footer-box{
  position: fixed;
  z-index: 99;
  left: 0;
  right: 0;
  bottom: 0;
  width: calc(100% - 0.9375rem);
  padding: 0.625rem 0.46875rem;
  background: #000;
  padding-bottom: max(env(safe-area-inset-bottom), 0.625rem);
}
.footer-box .footer-item{
  width: 100%;
  height: 2.5rem;
  justify-content: space-between;
}
.footer-means{
  margin-left: 0.46875rem;
  padding: 0 0.9375rem;
  height: 2.5rem;
  font-size: 0.625rem;
  font-weight: 700;
  color: #fff;
  background: #181818;
  border-radius: 1.25rem;
}
.footer-means uni-image{
  margin-left: 0.3125rem;
  width: 0.625rem;
  height: 0.625rem;
}
.footer-comment{
  margin-left: 0.625rem;
  padding: 0 0.9375rem;
  width: 6.25rem;
  height: 2.5rem;
  background: #181818;
  border-radius: 1.25rem;
}
.pl-str{
  line-height: 2.5rem;
  color: #999;
  font-size: 0.75rem;
  font-weight: 400;
  word-break: break-word;
  white-space: pre-line;
  display: block;
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}
.footer-item .footer-icon{
  padding: 0.5rem 0.46875rem;
  display: flex;
}
.footer-item .footer-icon uni-image{
  width: 1.5rem;
  height: 1.5rem;
}
.footer-item .footer-icon uni-text{
  margin-left: 0.25rem;
  color: #999;
  font-size: 0.5625rem;
  font-weight: 700;
}
.popup-comment-mask{
  position: fixed;
  z-index: 99998;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
}
.popup-comment{
  position: fixed;
  z-index: 99999;
  left: 0;
  width: calc(100% - 1.875rem);
  padding: 0.9375rem;
  background: #fff;
  border-top: 1px solid #f8f8f8;
  display: flex;
  align-items: flex-end;
}
.popup-comment .send{
  margin: 0 0 0.46875rem 0.9375rem;
  width: 1.5rem;
  height: 1.5rem;
}
.popup-comment .comment-textarea{
  width: calc(100% - 3.0625rem);
  padding: 0.3125rem 0.625rem;
  background: #f8f8f8;
  border-radius: 0.9375rem;
}
.popup-comment .comment-textarea uni-textarea{
  width: 100%;
  line-height: 1rem;
  min-height: 3rem;
  max-height: 10rem;
  font-size: 0.8125rem;
}
.popup-comment .comment-icon{
  width: calc(100% - 0.625rem);
  padding: 0.9375rem 0.3125rem;
}
.empty-box{
  width: 100%;
  padding: 3.125rem 0;
  flex-direction: column;
}
.empty-box uni-image{
  width: 9.375rem;
  height: 9.375rem;
  margin-bottom: 0.9375rem;
}
.empty-box .e1{
  font-size: 0.9375rem;
  font-weight: 700;
}
.empty-box .e2{
  margin-top: 0.3125rem;
  color: #999;
  font-size: 0.8125rem;
}
.tips-box{
  justify-content: center;
  width: 100%;
}
.tips-box .tips-item{
  background: #000;
  color: #fff;
  padding: 0.625rem 1.25rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 700;
}
.df{
  display: flex;
  align-items: center;
}
.ohto{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2{
  display: -webkit-box;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.xwb{
  filter: invert(1);
}

/* 单图样式 */
.image-box {
  width: 100%;
  background: #000;
  transition: height .45s ease-in-out;
  justify-content: center;
  align-items: center;
}
.image-box .full-image {
  width: 100%;
  height: 100%;
}

/* 多图样式 */
.multi-image-box {
  width: 100%;
  background: #000;
  transition: height .45s ease-in-out;
}
.multi-image-box .image-swiper {
  width: 100%;
  height: 100%;
}
.multi-image-box .swiper-image {
  width: 100%;
  height: 100%;
}
.multi-image-box .image-counter {
  position: absolute;
  right: 0.625rem;
  bottom: 0.625rem;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 0.125rem 0.625rem;
  border-radius: 0.9375rem;
  font-size: 0.625rem;
}

/* 内容区域调整 */
.nav-counter{
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 0.75rem;
  padding: 0.1875rem 0.625rem;
  border-radius: 0.9375rem;
}

/* 音频播放器样式 */
.audio-player-container {
  position: relative;
  width: 100%;
  background: #000;
  transition: height .45s ease-in-out;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

/* 背景模糊层 */
.audio-bg-blur {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.audio-bg-blur uni-image {
  width: 100%;
  height: 100%;
}
.audio-bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  -webkit-backdrop-filter: saturate(150%) blur(25px);
  backdrop-filter: saturate(150%) blur(25px);
  background: rgba(0, 0, 0, 0.5);
}

/* 音频播放内容 */
.audio-player-content {
  position: relative;
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.25rem;
  width: 100%;
  max-width: 21.875rem;
}

/* 碟机外圈 */
.vinyl-outer {
  position: relative;
  width: 12.5rem;
  height: 12.5rem;
  border: 0.09375rem solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1.25rem;
}

/* 外圈刻槽 */
.vinyl-groove {
  position: absolute;
  width: 10.9375rem;
  height: 10.9375rem;
  border: 0.0625rem solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border-style: dashed;
}
.vinyl-groove-2 {
  position: absolute;
  width: 10rem;
  height: 10rem;
  border: 0.03125rem solid rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  border-style: dotted;
}
.vinyl-groove-3 {
  position: absolute;
  width: 9.0625rem;
  height: 9.0625rem;
  border: 0.03125rem solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

/* 内圈唱片 */
.vinyl-inner {
  position: relative;
  width: 8.125rem;
  height: 8.125rem;
  background: rgba(20, 20, 20, 0.8);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: inset 0 0 0.9375rem rgba(0, 0, 0, 0.5);
}
.vinyl-center {
  position: relative;
  width: 6.25rem;
  height: 6.25rem;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 专辑封面 */
.album-cover {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.3);
}
.album-cover uni-image {
  width: 100%;
  height: 100%;
}

/* 中心圆点 */
.vinyl-dot {
  position: absolute;
  width: 0.5rem;
  height: 0.5rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  z-index: 2;
}

/* 音频信息 */
.audio-info {
  text-align: center;
  margin-bottom: 1.25rem;
  width: 100%;
}
.audio-title {
  font-size: 1rem;
  color: #fff;
  font-weight: 600;
  margin-bottom: 0.3125rem;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.audio-artist {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

/* 播放控制 */
.audio-controls {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.audio-play-btn {
  width: 3.125rem;
  height: 3.125rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(0.3125rem);
          backdrop-filter: blur(0.3125rem);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.9375rem;
  border: 0.0625rem solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}
.audio-play-btn:active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(0.95);
}
.audio-play-btn uni-image {
  width: 1.5625rem;
  height: 1.5625rem;
}

/* 进度条容器 */
.audio-progress-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.audio-time-start,
.audio-time-end {
  font-size: 0.625rem;
  color: rgba(255, 255, 255, 0.7);
  min-width: 2.5rem;
}
.audio-progress {
  flex: 1;
  margin: 0 0.625rem;
}

/* 旋转动画 */
.rotating {
  animation: rotate 10s linear infinite;
}
.rotating-slow {
  animation: rotate 30s linear infinite;
}
@keyframes rotate {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 0.625rem;
}
.loading-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.3125rem;
}
.comment-info-bottom .delete-btn{
  margin-left: 0.75rem;
  font-size: 0.75rem;
  color: #999;
}

/* 评论点赞按钮样式 */
.comment-info .user-info-left {
  display: flex;
  align-items: center;
}
.comment-info .like-icon {
  display: flex;
  align-items: center;
  margin-left: auto;
}
.comment-info .like-icon uni-image {
  width: 1rem;
  height: 1rem;
  margin-right: 0.125rem;
}
.comment-info .like-icon uni-text {
  font-size: 0.625rem;
  color: #999;
}

/* 评论图片样式 */
.comment-image {
  margin-top: 0.3125rem;
  max-width: 9.375rem;
  max-height: 12.5rem;
  border-radius: 0.25rem;
}
.reply-comment-image {
  max-width: 6.25rem;
  max-height: 9.375rem;
}

/* 已删除评论样式 */
.deleted-comment {
  color: #ccc;
  font-style: italic;
  font-size: 0.75rem;
}

/* 评论富文本样式 */
.comment-rich-text {
  word-break: break-word;
  white-space: pre-wrap;
}
.reply-rich-text {
  font-size: 0.75rem;
}

/* 系统消息样式 */
.system-message {
  color: #999;
  font-style: italic;
  font-size: 0.75rem;
  background-color: #f8f8f8;
  padding: 0.1875rem 0.375rem;
  border-radius: 0.1875rem;
}

/* 表情预览弹窗 */
.emoji-preview-popup {
  position: fixed;
  z-index: 999;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
}
.emoji-preview-image {
  width: 60%;
  height: auto;
  max-width: 12.5rem;
  max-height: 12.5rem;
}
.emoji-img-inline {
  display: inline-block;
  width: 1rem !important;
  height: 1rem !important;
  vertical-align: middle;
  margin: 0 0.125rem;
}
