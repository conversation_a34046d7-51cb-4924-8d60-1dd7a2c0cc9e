"use strict";
const common_vendor = require("../../../../../common/vendor.js");
const pages_users_components_verify_utils_ase = require("../utils/ase.js");
const pages_users_components_verify_utils_util = require("../utils/util.js");
const api_api = require("../../../../../api/api.js");
const _sfc_main = {
  name: "VerifySlide",
  props: {
    captchaType: {
      type: String,
      default: "blockPuzzle"
    },
    type: {
      type: String,
      default: "1"
    },
    // 弹出式pop，固定fixed
    mode: {
      type: String,
      default: "fixed"
    },
    vSpace: {
      type: Number,
      default: 5
    },
    explain: {
      type: String,
      default: "向右滑动完成验证"
    },
    imgSize: {
      type: Object,
      default() {
        return {
          width: "310px",
          height: "155px"
        };
      }
    },
    blockSize: {
      type: Object,
      default() {
        return {
          width: "50px",
          height: "50px"
        };
      }
    },
    barSize: {
      type: Object,
      default() {
        return {
          width: "310px",
          height: "40px"
        };
      }
    },
    defaultImg: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      secretKey: "",
      // 后端返回的加密秘钥 字段
      passFalg: "",
      // 是否通过的标识
      backImgBase: "",
      // 验证码背景图片
      blockBackImgBase: "",
      // 验证滑块的背景图片
      backToken: "",
      // 后端返回的唯一token值
      startMoveTime: "",
      // 移动开始的时间
      endMovetime: "",
      // 移动结束的时间
      tipsBackColor: "",
      // 提示词的背景颜色
      tipWords: "",
      text: "",
      finishText: "",
      setSize: {
        imgHeight: 0,
        imgWidth: 0,
        barHeight: 0,
        barWidth: 0
      },
      top: 0,
      left: 0,
      moveBlockLeft: void 0,
      leftBarWidth: void 0,
      // 移动中样式
      moveBlockBackgroundColor: void 0,
      leftBarBorderColor: "#ddd",
      iconColor: void 0,
      iconClass: "icon-right",
      status: false,
      // 鼠标状态
      isEnd: false,
      // 是够验证完成
      showRefresh: true,
      transitionLeft: "",
      transitionWidth: ""
    };
  },
  computed: {
    barArea() {
      return this.$el.querySelector(".verify-bar-area");
    },
    resetSize() {
      return pages_users_components_verify_utils_util.resetSize;
    }
  },
  watch: {
    // type变化则全面刷新
    type: {
      immediate: true,
      handler() {
        this.init();
      }
    }
  },
  mounted() {
    this.$el.onselectstart = function() {
      return false;
    };
  },
  methods: {
    init() {
      this.text = this.explain;
      this.getPictrue();
      this.$nextTick(() => {
        const setSize = this.resetSize(this);
        for (const key in setSize) {
          this.$set(this.setSize, key, setSize[key]);
        }
        this.$parent.$emit("ready", this);
      });
      var _this = this;
      window.removeEventListener("touchmove", function(e) {
        _this.move(e);
      });
      window.removeEventListener("mousemove", function(e) {
        _this.move(e);
      });
      window.removeEventListener("touchend", function() {
        _this.end();
      });
      window.removeEventListener("mouseup", function() {
        _this.end();
      });
      window.addEventListener("touchmove", function(e) {
        _this.move(e);
      });
      window.addEventListener("mousemove", function(e) {
        _this.move(e);
      });
      window.addEventListener("touchend", function() {
        _this.end();
      });
      window.addEventListener("mouseup", function() {
        _this.end();
      });
    },
    // 鼠标按下
    start: function(e) {
      e.preventDefault();
      e = e || window.event;
      if (!e.touches.length) {
        var x = e.clientX;
      } else {
        var x = e.touches[0].pageX;
      }
      this.startLeft = Math.floor(
        x - this.barArea.getBoundingClientRect().left
      );
      this.startMoveTime = +/* @__PURE__ */ new Date();
      if (this.isEnd == false) {
        this.text = "";
        this.moveBlockBackgroundColor = "#337ab7";
        this.leftBarBorderColor = "#337AB7";
        this.iconColor = "#fff";
        e.stopPropagation();
        this.status = true;
      }
    },
    // 鼠标移动
    move: function(e) {
      e = e || window.event;
      if (this.status && this.isEnd == false) {
        if (!e.touches) {
          var x = e.clientX;
        } else {
          var x = e.touches[0].pageX;
        }
        var bar_area_left = this.barArea.getBoundingClientRect().left;
        var move_block_left = x - bar_area_left;
        if (move_block_left >= this.barArea.offsetWidth - parseInt(parseInt(this.blockSize.width) / 2) - 2) {
          move_block_left = this.barArea.offsetWidth - parseInt(parseInt(this.blockSize.width) / 2) - 2;
        }
        if (move_block_left <= 0) {
          move_block_left = parseInt(parseInt(this.blockSize.width) / 2);
        }
        this.moveBlockLeft = move_block_left - this.startLeft + "px";
        this.leftBarWidth = move_block_left - this.startLeft + "px";
      }
    },
    // 鼠标松开
    end: function() {
      this.endMovetime = +/* @__PURE__ */ new Date();
      if (this.status && this.isEnd == false) {
        var moveLeftDistance = parseInt(
          (this.moveBlockLeft || "").replace("px", "")
        );
        moveLeftDistance = moveLeftDistance * 310 / parseInt(this.setSize.imgWidth);
        var captchaVerification = this.secretKey ? pages_users_components_verify_utils_ase.aesEncrypt(this.backToken + "---" + JSON.stringify({
          x: moveLeftDistance,
          y: 5
        }), this.secretKey) : this.backToken + "---" + JSON.stringify({
          x: moveLeftDistance,
          y: 5
        });
        const data = {
          captchaType: this.captchaType,
          pointJson: this.secretKey ? pages_users_components_verify_utils_ase.aesEncrypt(
            JSON.stringify({ x: moveLeftDistance, y: 5 }),
            this.secretKey
          ) : JSON.stringify({ x: moveLeftDistance, y: 5 }),
          token: this.backToken
        };
        api_api.ajcaptchaCheck(data).then((result) => {
          result.data;
          this.moveBlockBackgroundColor = "#5cb85c";
          this.leftBarBorderColor = "#5cb85c";
          this.iconColor = "#fff";
          this.iconClass = "icon-check";
          this.showRefresh = true;
          this.isEnd = true;
          setTimeout(() => {
            if (this.mode == "pop") {
              this.$parent.clickShow = false;
            }
            this.refresh();
          }, 1500);
          this.passFalg = true;
          this.tipWords = `${((this.endMovetime - this.startMoveTime) / 1e3).toFixed(2)}s验证成功`;
          setTimeout(() => {
            this.tipWords = "";
            this.$emit("success", {
              captchaVerification
            });
          }, 1e3);
        }).catch((res) => {
          this.moveBlockBackgroundColor = "#d9534f";
          this.leftBarBorderColor = "#d9534f";
          this.iconColor = "#fff";
          this.iconClass = "icon-close";
          this.passFalg = false;
          setTimeout(() => {
            this.refresh();
          }, 1e3);
          this.$parent.$emit("error", this);
          this.tipWords = "验证失败";
          setTimeout(() => {
            this.tipWords = "";
          }, 1e3);
        });
        this.status = false;
      }
    },
    refresh: function() {
      this.showRefresh = true;
      this.finishText = "";
      this.transitionLeft = "left .3s";
      this.moveBlockLeft = 0;
      this.leftBarWidth = void 0;
      this.transitionWidth = "width .3s";
      this.leftBarBorderColor = "#ddd";
      this.moveBlockBackgroundColor = "#fff";
      this.iconColor = "#000";
      this.iconClass = "icon-right";
      this.isEnd = false;
      this.getPictrue();
      setTimeout(() => {
        this.transitionWidth = "";
        this.transitionLeft = "";
        this.text = this.explain;
      }, 300);
    },
    // 请求背景图片和验证图片
    getPictrue() {
      let data = {
        captchaType: this.captchaType,
        clientUid: common_vendor.index.getStorageSync("slider"),
        ts: Date.now()
        // 现在的时间戳
      };
      api_api.getAjcaptcha(data).then((result) => {
        let res = result.data;
        this.backImgBase = res.originalImageBase64;
        this.blockBackImgBase = res.jigsawImageBase64;
        this.backToken = res.token;
        this.secretKey = res.secretKey;
      }).catch(() => {
        this.backImgBase = null;
        this.blockBackImgBase = null;
      });
    }
  }
};
if (!Array) {
  const _component_transition = common_vendor.resolveComponent("transition");
  _component_transition();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.type === "2"
  }, $props.type === "2" ? common_vendor.e({
    b: $data.backImgBase ? "data:image/png;base64," + $data.backImgBase : $props.defaultImg,
    c: $data.showRefresh,
    d: common_vendor.o((...args) => $options.refresh && $options.refresh(...args)),
    e: $data.tipWords
  }, $data.tipWords ? {
    f: common_vendor.t($data.tipWords),
    g: common_vendor.n($data.passFalg ? "suc-bg" : "err-bg")
  } : {}, {
    h: common_vendor.p({
      name: "tips"
    }),
    i: $data.setSize.imgWidth,
    j: $data.setSize.imgHeight,
    k: parseInt($data.setSize.imgHeight) + $props.vSpace + "px"
  }) : {}, {
    l: $data.backImgBase
  }, $data.backImgBase ? common_vendor.e({
    m: common_vendor.t($data.text),
    n: common_vendor.t($data.finishText),
    o: common_vendor.n($data.iconClass),
    p: $data.iconColor,
    q: $props.type === "2"
  }, $props.type === "2" ? {
    r: "data:image/png;base64," + $data.blockBackImgBase,
    s: Math.floor(parseInt($data.setSize.imgWidth) * 47 / 310) + "px",
    t: $data.setSize.imgHeight,
    v: "-" + (parseInt($data.setSize.imgHeight) + $props.vSpace) + "px",
    w: $data.setSize.imgWidth + " " + $data.setSize.imgHeight
  } : {}, {
    x: $props.barSize.height,
    y: $props.barSize.height,
    z: $data.moveBlockBackgroundColor,
    A: $data.moveBlockLeft,
    B: $data.transitionLeft,
    C: common_vendor.o((...args) => $options.start && $options.start(...args)),
    D: common_vendor.o((...args) => $options.start && $options.start(...args)),
    E: $data.leftBarWidth !== void 0 ? $data.leftBarWidth : $props.barSize.height,
    F: $props.barSize.height,
    G: $data.leftBarBorderColor,
    H: $data.transitionWidth,
    I: $data.setSize.imgWidth,
    J: $props.barSize.height,
    K: $props.barSize.height
  }) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-778a217d"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/pages/users/components/verify/verifySlider/verifySliderPc.js.map
