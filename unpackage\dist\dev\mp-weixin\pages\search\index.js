"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const config_api = require("../../config/api.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const cardGg = () => "../../components/card-gg/card-gg.js";
const money = () => "../../components/money/money.js";
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const waterfall = () => "../../components/waterfall/waterfall.js";
const emptyPage = () => "../../components/emptyPage/emptyPage.js";
const app = getApp();
const _sfc_main = {
  components: {
    lazyImage,
    cardGg,
    money,
    uniLoadMore,
    waterfall,
    emptyPage
  },
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      kw: "",
      barList: ["笔记", "商品", "圈子", "用户"],
      barIdx: 0,
      isThrottling: true,
      list: [],
      page: 1,
      isEmpty: false,
      loadStatus: "more",
      tipsTitle: "",
      isWaterfall: false,
      // 模拟数据 - 笔记
      mockNotes: [
        {
          id: 1001,
          type: 1,
          user_id: 2001,
          avatar: "/static/img/avatar.png",
          nickname: "旅行达人",
          is_follow: false,
          is_like: false,
          content: "今天去了一个超美的地方，分享给大家！这里的风景真的太美了，空气清新，环境宜人。",
          imgs: [
            { url: "/static/img/note1.jpg", wide: 800, high: 1200 },
            { url: "/static/img/note2.jpg", wide: 800, high: 1200 }
          ],
          adds_name: "北京市朝阳区",
          comment_count: 28,
          like_count: 156,
          browse_count: 1024,
          create_time_str: "2小时前"
        },
        {
          id: 1002,
          type: 2,
          user_id: 2002,
          avatar: "/static/img/avatar.png",
          nickname: "美食家",
          is_follow: true,
          is_like: true,
          content: "发现了一家超赞的餐厅，味道真的一级棒！强烈推荐给大家！",
          video: {
            url: "https://example.com/video.mp4",
            cover: "/static/img/video-cover.jpg",
            wide: 540,
            high: 960
          },
          adds_name: "上海市黄浦区",
          comment_count: 42,
          like_count: 267,
          browse_count: 2345,
          create_time_str: "昨天"
        },
        {
          id: 1003,
          type: 1,
          user_id: 2003,
          avatar: "/static/img/avatar.png",
          nickname: "时尚博主",
          is_follow: false,
          is_like: false,
          content: "分享一套今日穿搭，简约而不简单，希望大家喜欢~",
          imgs: [
            { url: "/static/img/note3.jpg", wide: 800, high: 1200 },
            { url: "/static/img/note4.jpg", wide: 800, high: 1200 },
            { url: "/static/img/note5.jpg", wide: 800, high: 1200 }
          ],
          adds_name: "广州市天河区",
          comment_count: 36,
          like_count: 198,
          browse_count: 1876,
          create_time_str: "3天前"
        }
      ],
      // 模拟数据 - 商品
      mockGoods: [
        {
          id: 2001,
          name: "2023夏季新款连衣裙",
          imgs: ["/static/img/product1.png"],
          product: {
            price: "199.00",
            line_price: "299.00"
          },
          buy: 128,
          cart: 45,
          browse: 1560,
          tags: ["新品", "热卖"]
        },
        {
          id: 2002,
          name: "轻薄透气运动鞋",
          imgs: ["/static/img/product2.png"],
          product: {
            price: "299.00",
            line_price: "399.00"
          },
          buy: 89,
          cart: 32,
          browse: 980,
          tags: ["限时特价"]
        },
        {
          id: 2003,
          name: "时尚斜挎小包",
          imgs: ["/static/img/product3.png"],
          product: {
            price: "159.00",
            line_price: "259.00"
          },
          buy: 56,
          cart: 28,
          browse: 756,
          tags: ["爆款"]
        }
      ],
      // 模拟数据 - 圈子
      mockCircles: [
        {
          id: 3001,
          avatar: "/static/img/circle1.png",
          name: "时尚穿搭圈",
          intro: "分享最新穿搭灵感，让你时刻保持时尚",
          is_hot: true,
          is_new: false,
          user_count: 12560,
          dynamic_count: 3567,
          user: [
            "/static/img/avatar.png",
            "/static/img/avatar.png",
            "/static/img/avatar.png"
          ]
        },
        {
          id: 3002,
          avatar: "/static/img/circle2.png",
          name: "美食爱好者",
          intro: "发现城市隐藏的美食宝藏，享受舌尖上的美味",
          is_hot: false,
          is_new: true,
          user_count: 8934,
          dynamic_count: 2456,
          user: [
            "/static/img/avatar.png",
            "/static/img/avatar.png",
            "/static/img/avatar.png"
          ]
        },
        {
          id: 3003,
          avatar: "/static/img/circle3.png",
          name: "旅行探索家",
          intro: "一起去探索世界的每个角落，记录旅途中的精彩",
          is_hot: true,
          is_new: false,
          user_count: 10238,
          dynamic_count: 2987,
          user: [
            "/static/img/avatar.png",
            "/static/img/avatar.png",
            "/static/img/avatar.png"
          ]
        }
      ],
      // 模拟数据 - 用户
      mockUsers: [
        {
          id: 4001,
          avatar: "/static/img/avatar.png",
          name: "时尚达人",
          dynamic: 156,
          fans: 3456,
          gender: 0,
          // 0-女 1-男 2-未知
          age: 28,
          province: "北京"
        },
        {
          id: 4002,
          avatar: "/static/img/avatar.png",
          name: "美食博主",
          dynamic: 234,
          fans: 5678,
          gender: 1,
          age: 32,
          province: "上海"
        },
        {
          id: 4003,
          avatar: "/static/img/avatar.png",
          name: "旅行摄影师",
          dynamic: 198,
          fans: 4567,
          gender: 1,
          age: 30,
          province: "广州"
        }
      ],
      // 模拟数据 - 活动
      mockActivities: [
        {
          id: 5001,
          img: "/static/img/activity1.png",
          status_str: "进行中",
          name: "夏日时尚穿搭分享会",
          activity_time: "2023-07-15 14:00-17:00",
          adds_name: "北京市朝阳区时尚广场B座",
          is_join: false,
          user_count: 156,
          avatar_list: [
            "/static/img/avatar.png",
            "/static/img/avatar.png",
            "/static/img/avatar.png"
          ],
          browse: 2345
        },
        {
          id: 5002,
          img: "/static/img/activity2.png",
          status_str: "即将开始",
          name: "美食品鉴会",
          activity_time: "2023-07-20 18:00-21:00",
          adds_name: "上海市黄浦区美食中心",
          is_join: true,
          user_count: 128,
          avatar_list: [
            "/static/img/avatar.png",
            "/static/img/avatar.png",
            "/static/img/avatar.png"
          ],
          browse: 1987
        },
        {
          id: 5003,
          img: "/static/img/activity3.png",
          status_str: "已结束",
          name: "城市摄影大赛",
          activity_time: "2023-07-01 09:00-17:00",
          adds_name: "广州市天河区摄影艺术中心",
          is_join: false,
          user_count: 0,
          browse: 3456
        }
      ]
    };
  },
  onLoad(options) {
    var _a;
    if (options.bar) {
      this.barIdx = options.bar;
    }
    if ((_a = app.globalData) == null ? void 0 : _a.isActivity) {
      this.barList.push("活动");
    }
  },
  methods: {
    // 搜索点击
    searchClick() {
      if (!this.kw) {
        return this.opTipsPopup("请输入搜索关键词");
      }
      this.isThrottling = false;
      this.list = [];
      this.page = 1;
      this.userSearch();
    },
    // 执行搜索
    userSearch() {
      var _a;
      let that = this;
      that.loadStatus = "loading";
      that.isEmpty = false;
      let searchType = that.barIdx;
      if (that.barIdx > 3 && that.barList[that.barIdx] == "活动") {
        searchType = "activity";
      }
      that.isWaterfall = ((_a = app.globalData) == null ? void 0 : _a.isWaterfall) || false;
      if (config_api.api) {
        let searchUrl = that.isWaterfall ? config_api.api.waterfallSearchUrl : config_api.api.searchUrl;
        if (searchUrl) {
          utils_request.request(searchUrl, {
            kw: that.kw,
            page: that.page,
            type: searchType
          }).then(function(res) {
            that.isThrottling = true;
            that.loadStatus = "more";
            if (res.data.data.length > 0) {
              if (that.page == 1) {
                that.list = res.data.data;
              } else {
                that.list = that.list.concat(res.data.data);
              }
              that.page = res.data.current_page;
            } else if (that.page == 1) {
              that.isEmpty = true;
            }
          });
        } else {
          that.useMockData();
        }
      } else {
        that.useMockData();
      }
    },
    // 使用模拟数据
    useMockData() {
      let that = this;
      setTimeout(() => {
        that.isThrottling = true;
        that.loadStatus = "more";
        let mockData = [];
        if (that.barIdx == 0) {
          mockData = that.mockNotes;
        } else if (that.barIdx == 1) {
          mockData = that.mockGoods;
        } else if (that.barIdx == 2) {
          mockData = that.mockCircles;
        } else if (that.barIdx == 3) {
          mockData = that.mockUsers;
        } else if (that.barIdx > 3 || that.barList[that.barIdx] == "活动") {
          mockData = that.mockActivities;
        }
        if (that.kw) {
          let keyword = that.kw.toLowerCase();
          mockData = mockData.filter((item) => {
            if (that.barIdx == 0) {
              return item.content.toLowerCase().includes(keyword) || item.nickname.toLowerCase().includes(keyword);
            } else if (that.barIdx == 1) {
              return item.name.toLowerCase().includes(keyword) || item.tags && item.tags.some((tag) => tag.toLowerCase().includes(keyword));
            } else if (that.barIdx == 2) {
              return item.name.toLowerCase().includes(keyword) || item.intro.toLowerCase().includes(keyword);
            } else if (that.barIdx == 3) {
              return item.name.toLowerCase().includes(keyword) || item.province.toLowerCase().includes(keyword);
            } else {
              return item.name.toLowerCase().includes(keyword) || item.adds_name.toLowerCase().includes(keyword);
            }
          });
        }
        if (mockData.length > 0) {
          if (that.page == 1) {
            that.list = mockData;
          } else {
            that.list = that.list.concat(mockData);
          }
        } else if (that.page == 1) {
          that.isEmpty = true;
        }
      }, 500);
    },
    // 标签切换
    barClick(e) {
      if (!this.kw) {
        return this.opTipsPopup("请输入搜索关键词");
      }
      if (this.isThrottling) {
        this.isThrottling = false;
        this.barIdx = e.currentTarget.dataset.idx;
        this.list = [];
        this.page = 1;
        this.userSearch();
      }
    },
    // 跳转到详情
    toNavigate(e) {
      let id = e.currentTarget.dataset.id;
      let url = "/pages/note/details?id=" + id;
      if (this.barIdx == 1) {
        url = "/pages/goods/details?id=" + id;
      } else if (this.barIdx == 2) {
        url = "/pages/note/circle?id=" + id;
      } else if (this.barIdx == 3) {
        url = "/pages/user/details?id=" + id;
      } else if (this.barIdx > 3 && this.barList[this.barIdx] == "活动") {
        url = "/pages/activity/details?id=" + id;
      }
      common_vendor.index.navigateTo({
        url
      });
    },
    // 点赞回调
    likeClick(e) {
      this.list[e.idx].is_like = e.is_like;
      this.list[e.idx].like_count = e.like_count;
    },
    // 提示弹窗
    opTipsPopup(msg) {
      let that = this;
      that.tipsTitle = msg;
      that.$refs.tipsPopup.open();
      setTimeout(function() {
        that.$refs.tipsPopup.close();
      }, 2e3);
    },
    // 返回上一页
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "/pages/index/index"
        });
      }
    },
    // 刷新列表
    fetchList() {
      this.isThrottling = false;
      this.list = [];
      this.page = 1;
      this.userSearch();
    },
    onCardUpdate({ vote_info, idx }) {
      if (this.list[idx]) {
        this.$set(this.list[idx], "vote_info", vote_info);
      }
    }
  },
  onReachBottom() {
    if (this.list.length) {
      this.page = this.page + 1;
      this.userSearch(false);
    }
  }
};
if (!Array) {
  const _component_emptyPage = common_vendor.resolveComponent("emptyPage");
  const _component_waterfall = common_vendor.resolveComponent("waterfall");
  const _component_card_gg = common_vendor.resolveComponent("card-gg");
  const _component_lazy_image = common_vendor.resolveComponent("lazy-image");
  const _component_money = common_vendor.resolveComponent("money");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_emptyPage + _component_waterfall + _component_card_gg + _component_lazy_image + _component_money + _easycom_uni_load_more2 + _easycom_uni_popup2)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    c: $data.titleBarHeight + "px",
    d: common_vendor.o((...args) => $options.searchClick && $options.searchClick(...args)),
    e: $data.kw,
    f: common_vendor.o(($event) => $data.kw = $event.detail.value),
    g: common_vendor.o((...args) => $options.searchClick && $options.searchClick(...args)),
    h: common_vendor.f($data.barList, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index == $data.barIdx ? "#000" : "#999",
        c: index == $data.barIdx ? "28rpx" : "26rpx",
        d: index == $data.barIdx ? 1 : 0,
        e: index,
        f: common_vendor.o((...args) => $options.barClick && $options.barClick(...args), index),
        g: index
      };
    }),
    i: $data.statusBarHeight + "px",
    j: $data.isEmpty
  }, $data.isEmpty ? {
    k: common_vendor.p({
      title: "没有找到相应内容",
      description: "换个词或切换搜索类型试试",
      image: "/static/img/empty.png"
    })
  } : common_vendor.e({
    l: $data.barIdx == 0
  }, $data.barIdx == 0 ? common_vendor.e({
    m: $data.isWaterfall
  }, $data.isWaterfall ? {
    n: common_vendor.p({
      note: $data.list,
      page: $data.page
    })
  } : {
    o: common_vendor.f($data.list, (item, index, i0) => {
      return {
        a: index,
        b: common_vendor.o($options.likeClick, index),
        c: common_vendor.o($options.onCardUpdate, index),
        d: "2d4a7a96-2-" + i0,
        e: common_vendor.p({
          item,
          idx: index
        })
      };
    })
  }, {
    p: common_vendor.n($data.isWaterfall && "dynamic-box")
  }) : {
    q: common_vendor.f($data.list, (item, index, i0) => {
      return common_vendor.e($data.barIdx == 1 ? {
        a: "2d4a7a96-3-" + i0,
        b: common_vendor.p({
          src: item.imgs[0]
        }),
        c: common_vendor.t(item.name),
        d: "2d4a7a96-4-" + i0,
        e: common_vendor.p({
          type: 1,
          price: item.product.price
        }),
        f: common_vendor.t(item.product.line_price),
        g: common_vendor.t(item.buy ? item.buy + "人已买" : item.cart + item.browse + "人想买"),
        h: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        })
      } : {}, $data.barIdx == 2 ? common_vendor.e({
        i: "2d4a7a96-5-" + i0,
        j: common_vendor.p({
          src: item.avatar,
          br: "168rpx"
        }),
        k: item.is_hot
      }, item.is_hot ? {} : item.is_new ? {} : {}, {
        l: item.is_new,
        m: common_vendor.t(item.name),
        n: common_vendor.t(item.intro),
        o: item.user_count > 0
      }, item.user_count > 0 ? {
        p: common_vendor.f(item.user, (img, imgIndex, i1) => {
          return {
            a: img,
            b: imgIndex
          };
        }),
        q: common_vendor.t(item.user_count),
        r: common_vendor.t(item.dynamic_count)
      } : {}) : {}, $data.barIdx == 3 ? common_vendor.e({
        s: "2d4a7a96-6-" + i0,
        t: common_vendor.p({
          src: item.avatar
        }),
        v: common_vendor.t(item.name),
        w: common_vendor.t(item.dynamic),
        x: common_vendor.t(item.fans),
        y: item.gender != 2
      }, item.gender != 2 ? common_vendor.e({
        z: item.gender == 0 ? "/static/img/nv.png" : "/static/img/nan.png",
        A: item.age && item.age != "暂不展示"
      }, item.age && item.age != "暂不展示" ? {
        B: common_vendor.t(item.age)
      } : {}) : {}, {
        C: common_vendor.t(item.province)
      }) : {}, $data.barIdx >= 4 || $data.barList[$data.barIdx] == "活动" ? common_vendor.e({
        D: "2d4a7a96-7-" + i0,
        E: common_vendor.p({
          src: item.img
        }),
        F: common_vendor.t(item.status_str || "加载中"),
        G: common_vendor.t(item.name || "活动名称加载中"),
        H: item.id,
        I: common_assets._imports_1$10,
        J: common_vendor.t(item.activity_time || "活动时间加载中"),
        K: item.id,
        L: common_assets._imports_2$8,
        M: common_vendor.t(item.adds_name || "活动地址加载中"),
        N: item.id,
        O: item.user_count
      }, item.user_count ? {
        P: common_vendor.f(item.avatar_list, (img, imgIndex, i1) => {
          return {
            a: img,
            b: imgIndex
          };
        }),
        Q: common_vendor.t(item.user_count)
      } : {
        R: common_vendor.t(item.browse)
      }, {
        S: common_vendor.t(item.is_join ? "查看详情" : "立即参加"),
        T: common_assets._imports_3$9
      }) : {}, {
        U: index,
        V: item.id,
        W: common_vendor.o((...args) => $options.toNavigate && $options.toNavigate(...args), index)
      });
    }),
    r: $data.barIdx == 1,
    s: $data.barIdx == 2,
    t: $data.barIdx == 3,
    v: $data.barIdx >= 4 || $data.barList[$data.barIdx] == "活动",
    w: common_vendor.n($data.barIdx == 1 && "goods-item")
  }), {
    x: common_vendor.p({
      status: $data.loadStatus
    }),
    y: common_vendor.n($data.barIdx == 1 && "goods"),
    z: "calc(" + ($data.statusBarHeight + $data.titleBarHeight) + "px + 200rpx)",
    A: common_vendor.t($data.tipsTitle),
    B: common_vendor.sr("tipsPopup", "2d4a7a96-9"),
    C: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/search/index.js.map
